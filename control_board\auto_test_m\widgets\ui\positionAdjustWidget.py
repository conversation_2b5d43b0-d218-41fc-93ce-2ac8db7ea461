# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'positionAdjustWidget.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(1600, 900)
        Form.setMinimumSize(QtCore.QSize(1600, 900))
        self.verticalLayout = QtWidgets.QVBoxLayout(Form)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.groupBox_2 = QtWidgets.QGroupBox(Form)
        self.groupBox_2.setTitle("")
        self.groupBox_2.setObjectName("groupBox_2")
        self.formLayout = QtWidgets.QFormLayout(self.groupBox_2)
        self.formLayout.setObjectName("formLayout")
        self.label_6 = QtWidgets.QLabel(self.groupBox_2)
        self.label_6.setObjectName("label_6")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_6)
        self.label_7 = QtWidgets.QLabel(self.groupBox_2)
        self.label_7.setObjectName("label_7")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.label_7)
        self.label = QtWidgets.QLabel(self.groupBox_2)
        self.label.setObjectName("label")
        self.formLayout.setWidget(1, QtWidgets.QFormLayout.LabelRole, self.label)
        self.lineEdit_x = QtWidgets.QLineEdit(self.groupBox_2)
        self.lineEdit_x.setReadOnly(True)
        self.lineEdit_x.setObjectName("lineEdit_x")
        self.formLayout.setWidget(1, QtWidgets.QFormLayout.FieldRole, self.lineEdit_x)
        self.label_2 = QtWidgets.QLabel(self.groupBox_2)
        self.label_2.setObjectName("label_2")
        self.formLayout.setWidget(2, QtWidgets.QFormLayout.LabelRole, self.label_2)
        self.lineEdit_y = QtWidgets.QLineEdit(self.groupBox_2)
        self.lineEdit_y.setReadOnly(True)
        self.lineEdit_y.setObjectName("lineEdit_y")
        self.formLayout.setWidget(2, QtWidgets.QFormLayout.FieldRole, self.lineEdit_y)
        self.label_3 = QtWidgets.QLabel(self.groupBox_2)
        self.label_3.setObjectName("label_3")
        self.formLayout.setWidget(3, QtWidgets.QFormLayout.LabelRole, self.label_3)
        self.lineEdit_z1 = QtWidgets.QLineEdit(self.groupBox_2)
        self.lineEdit_z1.setReadOnly(True)
        self.lineEdit_z1.setObjectName("lineEdit_z1")
        self.formLayout.setWidget(3, QtWidgets.QFormLayout.FieldRole, self.lineEdit_z1)
        self.label_4 = QtWidgets.QLabel(self.groupBox_2)
        self.label_4.setObjectName("label_4")
        self.formLayout.setWidget(4, QtWidgets.QFormLayout.LabelRole, self.label_4)
        self.lineEdit_z2 = QtWidgets.QLineEdit(self.groupBox_2)
        self.lineEdit_z2.setReadOnly(True)
        self.lineEdit_z2.setObjectName("lineEdit_z2")
        self.formLayout.setWidget(4, QtWidgets.QFormLayout.FieldRole, self.lineEdit_z2)
        self.label_5 = QtWidgets.QLabel(self.groupBox_2)
        self.label_5.setObjectName("label_5")
        self.formLayout.setWidget(5, QtWidgets.QFormLayout.LabelRole, self.label_5)
        self.lineEdit_r = QtWidgets.QLineEdit(self.groupBox_2)
        self.lineEdit_r.setReadOnly(True)
        self.lineEdit_r.setObjectName("lineEdit_r")
        self.formLayout.setWidget(5, QtWidgets.QFormLayout.FieldRole, self.lineEdit_r)
        self.horizontalLayout.addWidget(self.groupBox_2)
        self.groupBox_3 = QtWidgets.QGroupBox(Form)
        self.groupBox_3.setTitle("")
        self.groupBox_3.setObjectName("groupBox_3")
        self.formLayout_2 = QtWidgets.QFormLayout(self.groupBox_3)
        self.formLayout_2.setObjectName("formLayout_2")
        self.label_13 = QtWidgets.QLabel(self.groupBox_3)
        self.label_13.setObjectName("label_13")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_13)
        self.label_14 = QtWidgets.QLabel(self.groupBox_3)
        self.label_14.setObjectName("label_14")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.label_14)
        self.label_8 = QtWidgets.QLabel(self.groupBox_3)
        self.label_8.setObjectName("label_8")
        self.formLayout_2.setWidget(1, QtWidgets.QFormLayout.LabelRole, self.label_8)
        self.lineEdit_x_dest = QtWidgets.QLineEdit(self.groupBox_3)
        self.lineEdit_x_dest.setReadOnly(False)
        self.lineEdit_x_dest.setObjectName("lineEdit_x_dest")
        self.formLayout_2.setWidget(1, QtWidgets.QFormLayout.FieldRole, self.lineEdit_x_dest)
        self.label_9 = QtWidgets.QLabel(self.groupBox_3)
        self.label_9.setObjectName("label_9")
        self.formLayout_2.setWidget(2, QtWidgets.QFormLayout.LabelRole, self.label_9)
        self.lineEdit_y_dest = QtWidgets.QLineEdit(self.groupBox_3)
        self.lineEdit_y_dest.setReadOnly(False)
        self.lineEdit_y_dest.setObjectName("lineEdit_y_dest")
        self.formLayout_2.setWidget(2, QtWidgets.QFormLayout.FieldRole, self.lineEdit_y_dest)
        self.label_10 = QtWidgets.QLabel(self.groupBox_3)
        self.label_10.setObjectName("label_10")
        self.formLayout_2.setWidget(3, QtWidgets.QFormLayout.LabelRole, self.label_10)
        self.lineEdit_z1_dest = QtWidgets.QLineEdit(self.groupBox_3)
        self.lineEdit_z1_dest.setReadOnly(False)
        self.lineEdit_z1_dest.setObjectName("lineEdit_z1_dest")
        self.formLayout_2.setWidget(3, QtWidgets.QFormLayout.FieldRole, self.lineEdit_z1_dest)
        self.label_11 = QtWidgets.QLabel(self.groupBox_3)
        self.label_11.setObjectName("label_11")
        self.formLayout_2.setWidget(4, QtWidgets.QFormLayout.LabelRole, self.label_11)
        self.lineEdit_z2_dest = QtWidgets.QLineEdit(self.groupBox_3)
        self.lineEdit_z2_dest.setReadOnly(False)
        self.lineEdit_z2_dest.setObjectName("lineEdit_z2_dest")
        self.formLayout_2.setWidget(4, QtWidgets.QFormLayout.FieldRole, self.lineEdit_z2_dest)
        self.label_12 = QtWidgets.QLabel(self.groupBox_3)
        self.label_12.setObjectName("label_12")
        self.formLayout_2.setWidget(5, QtWidgets.QFormLayout.LabelRole, self.label_12)
        self.lineEdit_r_dest = QtWidgets.QLineEdit(self.groupBox_3)
        self.lineEdit_r_dest.setReadOnly(False)
        self.lineEdit_r_dest.setObjectName("lineEdit_r_dest")
        self.formLayout_2.setWidget(5, QtWidgets.QFormLayout.FieldRole, self.lineEdit_r_dest)
        self.horizontalLayout.addWidget(self.groupBox_3)
        self.verticalLayout.addLayout(self.horizontalLayout)
        self.groupBox = QtWidgets.QGroupBox(Form)
        self.groupBox.setTitle("")
        self.groupBox.setObjectName("groupBox")
        self.pushButton_x1 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_x1.setGeometry(QtCore.QRect(230, 140, 100, 45))
        self.pushButton_x1.setMinimumSize(QtCore.QSize(100, 45))
        self.pushButton_x1.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButton_x1.setObjectName("pushButton_x1")
        self.pushButton_x0 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_x0.setGeometry(QtCore.QRect(30, 140, 100, 45))
        self.pushButton_x0.setMinimumSize(QtCore.QSize(100, 45))
        self.pushButton_x0.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButton_x0.setObjectName("pushButton_x0")
        self.pushButton_y1 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y1.setGeometry(QtCore.QRect(130, 200, 100, 45))
        self.pushButton_y1.setMinimumSize(QtCore.QSize(100, 45))
        self.pushButton_y1.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButton_y1.setObjectName("pushButton_y1")
        self.pushButton_y0 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y0.setGeometry(QtCore.QRect(130, 80, 100, 45))
        self.pushButton_y0.setMinimumSize(QtCore.QSize(100, 45))
        self.pushButton_y0.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButton_y0.setObjectName("pushButton_y0")
        self.pushButton_z11 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_z11.setGeometry(QtCore.QRect(420, 80, 100, 45))
        self.pushButton_z11.setMinimumSize(QtCore.QSize(100, 45))
        self.pushButton_z11.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButton_z11.setObjectName("pushButton_z11")
        self.pushButton_z10 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_z10.setGeometry(QtCore.QRect(420, 200, 100, 45))
        self.pushButton_z10.setMinimumSize(QtCore.QSize(100, 45))
        self.pushButton_z10.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButton_z10.setObjectName("pushButton_z10")
        self.pushButton_z21 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_z21.setGeometry(QtCore.QRect(630, 80, 100, 45))
        self.pushButton_z21.setMinimumSize(QtCore.QSize(100, 45))
        self.pushButton_z21.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButton_z21.setObjectName("pushButton_z21")
        self.pushButton_z20 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_z20.setGeometry(QtCore.QRect(630, 200, 100, 45))
        self.pushButton_z20.setMinimumSize(QtCore.QSize(100, 45))
        self.pushButton_z20.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButton_z20.setObjectName("pushButton_z20")
        self.pushButton_r1 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_r1.setGeometry(QtCore.QRect(830, 80, 100, 45))
        self.pushButton_r1.setMinimumSize(QtCore.QSize(100, 45))
        self.pushButton_r1.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButton_r1.setObjectName("pushButton_r1")
        self.pushButton_r0 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_r0.setGeometry(QtCore.QRect(830, 200, 100, 45))
        self.pushButton_r0.setMinimumSize(QtCore.QSize(100, 45))
        self.pushButton_r0.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButton_r0.setObjectName("pushButton_r0")
        self.pushButton_home = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_home.setGeometry(QtCore.QRect(1250, 80, 100, 45))
        self.pushButton_home.setMinimumSize(QtCore.QSize(100, 45))
        self.pushButton_home.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButton_home.setObjectName("pushButton_home")
        self.pushButton_stop = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_stop.setGeometry(QtCore.QRect(1350, 200, 200, 45))
        self.pushButton_stop.setMinimumSize(QtCore.QSize(200, 45))
        self.pushButton_stop.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButton_stop.setObjectName("pushButton_stop")
        self.pushButton_start = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_start.setGeometry(QtCore.QRect(1050, 200, 200, 45))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton_start.sizePolicy().hasHeightForWidth())
        self.pushButton_start.setSizePolicy(sizePolicy)
        self.pushButton_start.setMinimumSize(QtCore.QSize(200, 45))
        self.pushButton_start.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButton_start.setObjectName("pushButton_start")
        self.verticalLayout.addWidget(self.groupBox)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.label_6.setText(_translate("Form", "轴"))
        self.label_7.setText(_translate("Form", "实际位置（purse）"))
        self.label.setText(_translate("Form", "X"))
        self.label_2.setText(_translate("Form", "Y"))
        self.label_3.setText(_translate("Form", "Z1"))
        self.label_4.setText(_translate("Form", "Z2"))
        self.label_5.setText(_translate("Form", "R"))
        self.label_13.setText(_translate("Form", "轴"))
        self.label_14.setText(_translate("Form", "目标位置（purse）"))
        self.label_8.setText(_translate("Form", "X"))
        self.label_9.setText(_translate("Form", "Y"))
        self.label_10.setText(_translate("Form", "Z1"))
        self.label_11.setText(_translate("Form", "Z2"))
        self.label_12.setText(_translate("Form", "R"))
        self.pushButton_x1.setText(_translate("Form", "X+"))
        self.pushButton_x0.setText(_translate("Form", "X-"))
        self.pushButton_y1.setText(_translate("Form", "Y+"))
        self.pushButton_y0.setText(_translate("Form", "Y-"))
        self.pushButton_z11.setText(_translate("Form", "Z1+"))
        self.pushButton_z10.setText(_translate("Form", "Z1-"))
        self.pushButton_z21.setText(_translate("Form", "Z2+"))
        self.pushButton_z20.setText(_translate("Form", "Z2-"))
        self.pushButton_r1.setText(_translate("Form", "R+"))
        self.pushButton_r0.setText(_translate("Form", "R-"))
        self.pushButton_home.setText(_translate("Form", "回零"))
        self.pushButton_stop.setText(_translate("Form", "停止"))
        self.pushButton_start.setText(_translate("Form", "绝对定位开始"))
