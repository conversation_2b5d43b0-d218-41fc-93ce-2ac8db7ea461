"""
电源相关命令处理器
"""
import logging
import operator
from typing import Dict, Any
from case.command_handlers.base_handler import BaseCommandHandler

logger = logging.getLogger(__name__)


class PowerCommandHandler(BaseCommandHandler):
    """电源命令处理器"""
    
    def get_supported_commands(self) -> set:
        """返回支持的电源相关命令"""
        return {
            "SwitchPowerFixed", "SwitchPowerRandom", "ReadAEQTASK", "SwitchVoltage",
            "SwitchStepVoltage", "ReadWorkCurrent", "ReadPeriodWorkCurrent",
            "ExecuteLoopPowerOnOff", "SetOscilloscopeDelayStop", "MonitorMultiChannelWorkCurrent"
        }
    
    def execute(self, command: str, step: Dict[str, Any]) -> None:
        """执行电源相关命令"""
        params = self._extract_step_params(step)
        case_number = params['case_number']
        data = params['data']
        
        try:
            if command == "SwitchPowerFixed":
                self._handle_switch_power_fixed(case_number, command, data)
                
            elif command == "SwitchPowerRandom":
                self._handle_switch_power_random(case_number, command, data)
                
            elif command == "ReadAEQTASK":
                self._handle_read_aeq_task(case_number, command, data)
                
            elif command == "SwitchVoltage":
                self._handle_switch_voltage(case_number, command, data)
                
            elif command == "SwitchStepVoltage":
                self._handle_switch_step_voltage(case_number, command, data)
                
            elif command == "ReadWorkCurrent":
                self._handle_read_work_current(case_number, command, data)
                
            elif command == "ReadPeriodWorkCurrent":
                self._handle_read_period_work_current(case_number, command, data)
                
            elif command == "ExecuteLoopPowerOnOff":
                self._handle_execute_loop_power_on_off(case_number, command, data)
                
            elif command == "SetOscilloscopeDelayStop":
                self._handle_set_oscilloscope_delay_stop(case_number, command, data)
                
            elif command == "MonitorMultiChannelWorkCurrent":
                self._handle_monitor_multi_channel_work_current(case_number, command, data)
                
            else:
                logger.warning(f"未处理的电源命令: {command}")
                self._emit_failure(case_number, command, f"未支持的命令: {command}")
                
        except Exception as e:
            logger.error(f"电源命令执行失败 {command}: {e}")
            self._emit_failure(case_number, command, f"命令执行异常: {str(e)}")
    
    def _handle_switch_power_fixed(self, case_number: str, command: str, data: str):
        """处理固定电源切换"""
        from power.tools.ETM3020PCControl import etm_3020pc_control
        
        power_time = data.split(" ")
        if len(power_time) >= 2:
            power_on = self._safe_int(power_time[0])
            power_off = self._safe_int(power_time[1])
            etm_3020pc_control.power_switch_fixed(power_on=power_on, power_off=power_off)
            self._emit_success(case_number, command)
        else:
            self._emit_failure(case_number, command, "参数错误")
    
    def _handle_switch_power_random(self, case_number: str, command: str, data: str):
        """处理随机电源切换"""
        from power.tools.ETM3020PCControl import etm_3020pc_control
        
        power_time = data.split(" ")
        if len(power_time) >= 4:
            power_on_min = self._safe_int(power_time[0])
            power_on_max = self._safe_int(power_time[1])
            power_off_min = self._safe_int(power_time[2])
            power_off_max = self._safe_int(power_time[3])
            etm_3020pc_control.power_switch_random(
                power_on_min=power_on_min,
                power_on_max=power_on_max,
                power_off_min=power_off_min,
                power_off_max=power_off_max
            )
            self._emit_success(case_number, command)
        else:
            self._emit_failure(case_number, command, "参数错误")
    
    def _handle_read_aeq_task(self, case_number: str, command: str, data: str):
        """处理读取AEQ任务"""
        from adb.AdbConnectDevice import adb_connect_device
        
        try:
            params = self._parse_data_params(data, 4)
            times = self._safe_int(params[0])
            interval_up = self._safe_int(params[1])
            interval_down = self._safe_int(params[2])
            volt = self._safe_int(params[3])  # 修改为int类型
            
            channel = 1
            if len(params) > 4:
                channel = self._safe_int(params[4])
            
            actual_result = adb_connect_device.read_aeq_task(times, interval_up, interval_down, volt, channel)
            
            if actual_result.startswith('success'):
                result = "PASS"
            else:
                result = "NG"
            
            self._emit_success(case_number, command, actual_result) if result == "PASS" else self._emit_failure(case_number, command, actual_result)
            
        except Exception as e:
            logger.error(f"ReadAEQTASK exception: {e}")
            self._emit_failure(case_number, command, "NG")
    
    def _handle_switch_voltage(self, case_number: str, command: str, data: str):
        """处理电压切换"""
        from utils.PowerManager import power_manager
        
        params = self._parse_data_params(data, 1)
        volt = self._safe_float(params[0])
        
        channel = 1
        if len(params) > 2:
            channel = self._safe_int(params[2])
        
        power_status, set_status = power_manager.set_volt(volt, channel)
        
        if power_status:
            if isinstance(set_status, tuple):
                if set_status[0]:
                    self._emit_success(case_number, command)
                else:
                    self._emit_failure(case_number, command, set_status[1])
            else:
                self._emit_failure(case_number, command, str(set_status))
        else:
            self._emit_failure(case_number, command, "电源未连接")
    
    def _handle_switch_step_voltage(self, case_number: str, command: str, data: str):
        """处理步进电压切换"""
        from power.tools.it_m3200_control import it_m3200_control
        from power.tools.ETM3020PCControl import etm_3020pc_control
        from power.tools.etm_mu3_control import etm_mu3_control
        
        if "," not in data:
            self._emit_failure(case_number, command, "参数格式错误")
            return
        
        params = self._parse_data_params(data, 3)
        start_volt = self._safe_float(params[0])
        end_volt = self._safe_float(params[1])
        power_channel = self._safe_int(params[2])
        
        interval = 0.5
        step_volt = 0.5
        if len(params) > 3:
            interval = self._safe_float(params[3]) / 1000
        if len(params) > 4:
            step_volt = self._safe_float(params[4])
        
        if it_m3200_control.is_connect():
            status = it_m3200_control.set_step_volt(start_volt, end_volt, interval, step_volt)
            if status:
                self._emit_success(case_number, command)
            else:
                self._emit_failure(case_number, command, "电压设置失败")
        elif etm_3020pc_control.is_open():
            status = etm_3020pc_control.set_step_voltage(start_volt, end_volt, interval, step_volt)
            if status:
                self._emit_success(case_number, command)
            else:
                self._emit_failure(case_number, command, "电压设置失败")
        elif etm_mu3_control.is_open():
            status = etm_mu3_control.set_step_voltage(power_channel, start_volt, end_volt, interval, step_volt)
            if status:
                self._emit_success(case_number, command)
            else:
                self._emit_failure(case_number, command, "电压设置失败")
        else:
            self._emit_failure(case_number, command, "电源未连接")
    
    def _handle_read_work_current(self, case_number: str, command: str, data: str):
        """处理读取工作电流"""
        from power.tools.it_m3200_control import it_m3200_control
        from power.tools.ETM3020PCControl import etm_3020pc_control
        from power.tools.etm_mu3_control import etm_mu3_control
        
        if "," not in data:
            self._emit_failure(case_number, command, "参数格式错误")
            return
        
        params = self._parse_data_params(data, 3)
        min_current = self._safe_float(params[0])
        max_current = self._safe_float(params[1])
        power_type = params[2]
        
        if operator.eq("IT-M3200", power_type):
            work_current = it_m3200_control.read_work_current()
            work_current = round(work_current, 3)
            if min_current < work_current < max_current:
                self._emit_success(case_number, command, str(work_current))
            else:
                self._emit_failure(case_number, command, str(work_current))
                
        elif operator.eq("TOMMENS", power_type):
            work_current = 0.0
            if etm_3020pc_control.is_open():
                status, work_current = etm_3020pc_control.read_work_current()
                if not status:
                    self._emit_failure(case_number, command, "电流读取失败")
                    return
            elif etm_mu3_control.is_open():
                channel = self._safe_int(params[3]) if len(params) > 3 else 1
                status, work_current = etm_mu3_control.read_work_current(channel=channel)
                if not status:
                    self._emit_failure(case_number, command, "电流读取失败")
                    return
            
            work_current = round(work_current, 3)
            logger.info(f"工作电流值: {work_current}")
            
            if min_current <= work_current <= max_current:
                self._emit_success(case_number, command, str(work_current))
            else:
                self._emit_failure(case_number, command, str(work_current))
    
    def _handle_read_period_work_current(self, case_number: str, command: str, data: str):
        """处理读取周期工作电流"""
        from power.tools.it_m3200_control import it_m3200_control
        from power.tools.ETM3020PCControl import etm_3020pc_control
        from power.tools.etm_mu3_control import etm_mu3_control
        
        if "," not in data:
            self._emit_failure(case_number, command, "参数格式错误")
            return
        
        params = self._parse_data_params(data, 4)
        read_interval = self._safe_float(params[0])
        read_time = self._safe_float(params[1])
        min_current = self._safe_float(params[2])
        max_current = self._safe_float(params[3])
        
        if it_m3200_control.is_connect():
            status, error_work_current = it_m3200_control.read_period_work_current(
                read_interval, read_time, min_current, max_current)
            if status:
                logger.info("周期工作电流读取正常")
                self._emit_success(case_number, command)
            else:
                logger.info(f"周期工作电流读取异常，异常工作电流值：{error_work_current}")
                self._emit_failure(case_number, command, str(error_work_current))
                
        elif etm_3020pc_control.is_open():
            status, error_work_current = etm_3020pc_control.read_period_work_current(
                read_interval, read_time, min_current, max_current)
            if status:
                logger.info("周期工作电流读取正常")
                self._emit_success(case_number, command)
            else:
                logger.info(f"周期工作电流读取异常，异常工作电流值：{error_work_current}")
                self._emit_failure(case_number, command, str(error_work_current))
                
        elif etm_mu3_control.is_open():
            channel = self._safe_int(params[4]) if len(params) > 4 else 1
            status, error_work_current = etm_mu3_control.read_period_work_current(
                read_interval, read_time, min_current, max_current, channel)
            if status:
                logger.info("周期工作电流读取正常")
                self._emit_success(case_number, command)
            else:
                logger.info(f"周期工作电流读取异常，异常工作电流值：{error_work_current}")
                self._emit_failure(case_number, command, str(error_work_current))
    
    def _handle_execute_loop_power_on_off(self, case_number: str, command: str, data: str):
        """处理循环上下电"""
        from utils.LoopPowerOnOffManager import loop_power_on_off_manager
        
        params = self._parse_data_params(data, 7)
        power_on_volt = self._safe_float(params[0])
        power_on_delay_min = self._safe_float(params[1])
        power_on_delay_max = self._safe_float(params[2])
        power_off_volt = self._safe_float(params[3])
        power_off_delay_min = self._safe_float(params[4])
        power_off_delay_max = self._safe_float(params[5])
        execute_times = self._safe_int(params[6])
        
        channel = 1
        if len(params) > 7:
            channel = self._safe_int(params[7])
        
        loop_power_on_off_manager.execute_loop(
            case_number, command, power_on_volt, power_on_delay_min,
            power_on_delay_max, power_off_volt, power_off_delay_min,
            power_off_delay_max, execute_times, channel)
    
    def _handle_set_oscilloscope_delay_stop(self, case_number: str, command: str, data: str):
        """处理设置示波器延时停止"""
        from oscilloscope.OscilloscopeManager import oscilloscope_manager
        
        delay_time = self._safe_float(data)
        oscilloscope_manager.set_delay_stop_time(delay_time)
        self._emit_success(case_number, command, f"示波器设置延时停止时长：{delay_time}秒")
    
    def _handle_monitor_multi_channel_work_current(self, case_number: str, command: str, data: str):
        """处理多通道工作电流监控"""
        from power.manager.WorkCurrentMonitorManager import work_current_monitor_manager
        
        work_current_monitor_manager.handle_monitor_multi_channel_work_current(case_number, command, data)
