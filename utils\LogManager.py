# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/7/8 17:40
@Desc   : 日志管理模块
"""
from adb.AdbConnectDevice import adb_connect_device
from common.LogUtils import logger
from utils.SignalsManager import signals_manager


class LogManager:

    def __init__(self):
        super().__init__()

    @staticmethod
    def parse_mcu_log(case_number, command, mark):
        logger.info(f"parse_mcu_log case_number={case_number}, command={command}, mark={mark}")
        # 未检测到mcu日志，默认NG
        if len(adb_connect_device.mcu_msgs) == 0:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "未检测到MCU日志，默认NG")
            return
        for msg in adb_connect_device.mcu_msgs:
            if msg.__contains__(mark):
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", msg)
                return
        signals_manager.step_execute_finish.emit(case_number, command, "NG", f"MCU日志不存在mark={mark}的信息")

    @staticmethod
    def parse_soc_log(case_number, command, mark):
        logger.info(f"parse_soc_log case_number={case_number}, command={command}, mark={mark}")
        for msg in adb_connect_device.soc_msgs:
            if msg.__contains__(mark):
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", msg)
                return
        signals_manager.step_execute_finish.emit(case_number, command, "NG", f"SOC日志不存在mark={mark}的信息")

    @staticmethod
    def parse_os_log(case_number, command, mark):
        logger.info(f"parse_os_log case_number={case_number}, command={command}, mark={mark}")
        for msg in adb_connect_device.os_msgs:
            if msg.__contains__(mark):
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", msg)
                return
        signals_manager.step_execute_finish.emit(case_number, command, "NG", f"OS日志不存在mark={mark}的信息")

    @staticmethod
    def parse_vds_app_log(case_number, command, mark):
        logger.info(f"parse_vds_app_log case_number={case_number}, command={command}, mark={mark}")
        for msg in adb_connect_device.vds_app_msgs:
            if msg.__contains__(mark):
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", msg)
                return
        signals_manager.step_execute_finish.emit(case_number, command, "NG", f"VDS APP日志不存在mark={mark}的信息")


log_manager: LogManager = LogManager()
