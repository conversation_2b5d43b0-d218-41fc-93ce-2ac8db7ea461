
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QTabWidget

from .jogWidget import JogWidget
from .axisStatusWidget import AxisStatusWidget
from .trapWidget import TrapWidget
from .homeWidget import HomeWidget
from .ioWidget import IOWidget
from .positionWidget import PositionWidget


class MotionControlCardWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent=parent)

        self.setWindowTitle("运动控制板卡")
        self.resize(900, 700)

        layout = QVBoxLayout(self)

        tab = QTabWidget()

        tab.addTab(PositionWidget(), "位置调试")
        tab.addTab(HomeWidget(), "回零")
        tab.addTab(JogWidget(), "Jog运动")
        tab.addTab(TrapWidget(), "点位运动")
        tab.addTab(IOWidget(), "IO")

        axis_status_widget = AxisStatusWidget()

        layout.addWidget(tab)
        layout.addWidget(axis_status_widget)
