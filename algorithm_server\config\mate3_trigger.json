{
    "trigger":
    [
        {
            "name":"调试触发",
            "type":"tcp_server",
            "run":"TCP调试流程",
            "param":
            {
                "server":"调试tcp"
            }
        },
        //控制卡触发
        {
            "name":"开始点胶标定上升沿",
            "type":"modbus_master",
            "run":"开始点胶标定上升沿流程",
            "enabled":true,
            "param":
            {
                "master":"点胶控制卡",
                "offset":"20",
                "type":"short",
                "source":"other->1"  
            }  
        },
        {
            "name":"开始点胶上升沿",
            "type":"modbus_master",
            "run":"开始点胶上升沿流程",
            "enabled":true,
            "param":
            {
                "master":"点胶控制卡",
                "offset":"35",
                "type":"short",
                "source":"other->1"  
            }
            
        },
        //左PLC触发
        {
            "name":"左相机复位上升沿",
            "type":"s7_client",
            "run":"左请求复位上升沿流程",
            "enabled":true,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"2",
                "type":"short",
                "source":"other->1"
            }
        },
        {
            "name":"左相机复位下降沿",
            "type":"s7_client",
            "run":"左请求复位下降沿流程",
            "enabled":false,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"2",
                "type":"short",
                "source":"0"
            }
        },
        {
            "name":"左收到相机复位上升沿",
            "type":"s7_client",
            "run":"左收到复位上升沿流程",
            "enabled":false,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"4",
                "type":"short",
                "source":"1"
            }
        },
        {
            "name":"左请求开始上升沿",
            "type":"s7_client",
            "run":"左请求开始上升沿流程",
            "enabled":true,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"6",
                "type":"short",
                "source":"other->1"
            }
        },
        {
            "name":"左请求开始下降沿",
            "type":"s7_client",
            "run":"左请求开始下降沿流程",
            "enabled":false,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"6",
                "type":"short",
                "source":"0"
            }
        },
        {
            "name":"左收到开始上升沿",
            "type":"s7_client",
            "run":"左收到开始上升沿流程",
            "enabled":false,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"8",
                "type":"short",
                "source":"1"
            }
        },
        {
            "name":"左请求拍屏幕上升沿",
            "type":"s7_client",
            "run":"左请求拍屏幕上升沿流程",
            "enabled":true,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"14",
                "type":"short",
                "source":"other->1"
            }
        },
        {
            "name":"左请求拍屏幕下降沿",
            "type":"s7_client",
            "run":"左请求拍屏幕下降沿流程",
            "enabled":false,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"14",
                "type":"short",
                "source":"0"
            }
        },
        {
            "name":"左收到拍屏幕上升沿",
            "type":"s7_client",
            "run":"左收到拍屏幕上升沿流程",
            "enabled":false,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"16",
                "type":"short",
                "source":"1"
            }
        },
        {
            "name":"左请求对位上升沿",
            "type":"s7_client",
            "run":"左请求对位上升沿流程",
            "enabled":true,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"18",
                "type":"short",
                "source":"other->1"
            }
        },
        {
            "name":"左请求对位下降沿",
            "type":"s7_client",
            "run":"左请求对位下降沿流程",
            "enabled":false,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"18",
                "type":"short",
                "source":"0"
            }
        },
        {
            "name":"左收到对位上升沿",
            "type":"s7_client",
            "run":"左收到对位上升沿流程",
            "enabled":false,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"20",
                "type":"short",
                "source":"1"
            }
        },
        {
            "name":"左请求间隙拍照上升沿",
            "type":"s7_client",
            "run":"左请求间隙拍照上升沿流程",
            "enabled":true,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"22",
                "type":"short",
                "source":"other->1"
            }
        },
        {
            "name":"左请求间隙拍照下降沿",
            "type":"s7_client",
            "run":"左请求间隙拍照下降沿流程",
            "enabled":false,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"22",
                "type":"short",
                "source":"0"
            }
        },
        {
            "name":"左收到间隙拍照上升沿",
            "type":"s7_client",
            "run":"左收到间隙拍照上升沿流程",
            "enabled":false,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"24",
                "type":"short",
                "source":"1"
            }
        },
        {
            "name":"左请求角度检测上升沿",
            "type":"s7_client",
            "run":"左请求角度检测上升沿流程",
            "enabled":true,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"26",
                "type":"short",
                "source":"other->1"
            }
        },
        {
            "name":"左请求角度检测下降沿",
            "type":"s7_client",
            "run":"左请求角度检测下降沿流程",
            "enabled":false,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"26",
                "type":"short",
                "source":"0"
            }
        },
        {
            "name":"左收到角度检测上升沿",
            "type":"s7_client",
            "run":"左收到角度检测上升沿流程",
            "enabled":false,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"28",
                "type":"short",
                "source":"1"
            }
        },
        {
            "name":"左请求点胶拍照上升沿",
            "type":"s7_client",
            "run":"左请求点胶拍照上升沿流程",
            "enabled":true,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"34",
                "type":"short",
                "source":"other->1"
            }
        },
        {
            "name":"左请求点胶拍照下降沿",
            "type":"s7_client",
            "run":"左请求点胶拍照下降沿流程",
            "enabled":false,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"34",
                "type":"short",
                "source":"0"
            }
        },
        {
            "name":"左收到点胶拍照上升沿",
            "type":"s7_client",
            "run":"左收到点胶拍照上升沿流程",
            "enabled":false,
            "param":
            {
                "client":"左PLC",
                "db":"520",
                "offset":"28",
                "type":"short",
                "source":"1"
            }
        },
        //右PLC触发
        {
            "name":"右相机复位上升沿",
            "type":"s7_client",
            "run":"右请求复位上升沿流程",
            "enabled":true,
            "param":
            {
                "client":"右PLC",
                "db":"520",
                "offset":"2",
                "type":"short",
                "source":"other->1"
            }
        },
        {
            "name":"右相机复位下降沿",
            "type":"s7_client",
            "run":"右请求复位下降沿流程",
            "enabled":false,
            "param":
            {
                "client":"右PLC",
                "db":"520",
                "offset":"2",
                "type":"short",
                "source":"0"
            }
        },
        {
            "name":"右收到相机复位上升沿",
            "type":"s7_client",
            "run":"右收到复位上升沿流程",
            "enabled":false,
            "param":
            {
                "client":"右PLC",
                "db":"520",
                "offset":"4",
                "type":"short",
                "source":"1"
            }
        },
        {
            "name":"右请求开始上升沿",
            "type":"s7_client",
            "run":"右请求开始上升沿流程",
            "enabled":true,
            "param":
            {
                "client":"右PLC",
                "db":"520",
                "offset":"6",
                "type":"short",
                "source":"other->1"
            }
        },
        {
            "name":"右请求开始下降沿",
            "type":"s7_client",
            "run":"右请求开始下降沿流程",
            "enabled":false,
            "param":
            {
                "client":"右PLC",
                "db":"520",
                "offset":"6",
                "type":"short",
                "source":"0"
            }
        },
        {
            "name":"右收到开始上升沿",
            "type":"s7_client",
            "run":"右收到开始上升沿流程",
            "enabled":false,
            "param":
            {
                "client":"右PLC",
                "db":"520",
                "offset":"8",
                "type":"short",
                "source":"1"
            }
        },
        {
            "name":"右请求拍屏幕上升沿",
            "type":"s7_client",
            "run":"右请求拍屏幕上升沿流程",
            "enabled":true,
            "param":
            {
                "client":"右PLC",
                "db":"520",
                "offset":"14",
                "type":"short",
                "source":"other->1"
            }
        },
        {
            "name":"右请求拍屏幕下降沿",
            "type":"s7_client",
            "run":"右请求拍屏幕下降沿流程",
            "enabled":false,
            "param":
            {
                "client":"右PLC",
                "db":"520",
                "offset":"14",
                "type":"short",
                "source":"0"
            }
        },
        {
            "name":"右收到拍屏幕上升沿",
            "type":"s7_client",
            "run":"右收到拍屏幕上升沿流程",
            "enabled":false,
            "param":
            {
                "client":"右PLC",
                "db":"520",
                "offset":"16",
                "type":"short",
                "source":"1"
            }
        },
        {
            "name":"右请求对位上升沿",
            "type":"s7_client",
            "run":"右请求对位上升沿流程",
            "enabled":true,
            "param":
            {
                "client":"右PLC",
                "db":"520",
                "offset":"18",
                "type":"short",
                "source":"other->1"
            }
        },
        {
            "name":"右请求对位下降沿",
            "type":"s7_client",
            "run":"右请求对位下降沿流程",
            "enabled":false,
            "param":
            {
                "client":"右PLC",
                "db":"520",
                "offset":"18",
                "type":"short",
                "source":"0"
            }
        },
        {
            "name":"右收到对位上升沿",
            "type":"s7_client",
            "run":"右收到对位上升沿流程",
            "enabled":false,
            "param":
            {
                "client":"右PLC",
                "db":"520",
                "offset":"20",
                "type":"short",
                "source":"1"
            }
        },
        {
            "name":"右请求间隙拍照上升沿",
            "type":"s7_client",
            "run":"右请求间隙拍照上升沿流程",
            "enabled":true,
            "param":
            {
                "client":"右PLC",
                "db":"520",
                "offset":"22",
                "type":"short",
                "source":"other->1"
            }
        },
        {
            "name":"右请求间隙拍照下降沿",
            "type":"s7_client",
            "run":"右请求间隙拍照下降沿流程",
            "enabled":false,
            "param":
            {
                "client":"右PLC",
                "db":"520",
                "offset":"22",
                "type":"short",
                "source":"0"
            }
        },
        {
            "name":"右收到间隙拍照上升沿",
            "type":"s7_client",
            "run":"右收到间隙拍照上升沿流程",
            "enabled":false,
            "param":
            {
                "client":"右PLC",
                "db":"520",
                "offset":"24",
                "type":"short",
                "source":"1"
            }
        },
        {
            "name":"右请求角度检测上升沿",
            "type":"s7_client",
            "run":"右请求角度检测上升沿流程",
            "enabled":true,
            "param":
            {
                "client":"右PLC",
                "db":"520",
                "offset":"26",
                "type":"short",
                "source":"other->1"
            }
        },
        {
            "name":"右请求角度检测下降沿",
            "type":"s7_client",
            "run":"右请求角度检测下降沿流程",
            "enabled":false,
            "param":
            {
                "client":"右PLC",
                "db":"520",
                "offset":"26",
                "type":"short",
                "source":"0"
            }
        },
        {
            "name":"右收到角度检测上升沿",
            "type":"s7_client",
            "run":"右收到角度检测上升沿流程",
            "enabled":false,
            "param":
            {
                "client":"右PLC",
                "db":"520",
                "offset":"28",
                "type":"short",
                "source":"1"
            }
        }
    ]
}
