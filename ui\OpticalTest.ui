<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OpticalTestForm</class>
 <widget class="QWidget" name="OpticalTestForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1600</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1600</width>
    <height>900</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="leftMargin">
    <number>5</number>
   </property>
   <property name="topMargin">
    <number>5</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="OpticalTestWidget" native="true">
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout" stretch="0,0">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="3,2">
        <property name="leftMargin">
         <number>10</number>
        </property>
        <property name="topMargin">
         <number>20</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <layout class="QGridLayout" name="gridLayout" columnstretch="1,0,0,1,1,1">
          <property name="verticalSpacing">
           <number>6</number>
          </property>
          <item row="1" column="1">
           <widget class="QSpinBox" name="spinBox_minLight">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
           </widget>
          </item>
          <item row="0" column="4">
           <widget class="QLabel" name="label_2">
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="text">
             <string>采集通道</string>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QLabel" name="label_3">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string>-</string>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QLabel" name="label">
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="text">
             <string>测试场景</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_5">
            <property name="styleSheet">
             <string notr="true">padding:10; border-radius:5;</string>
            </property>
            <property name="text">
             <string>灰阶/亮度范围</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1" colspan="3">
           <widget class="QComboBox" name="comboBoxTest">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <item>
             <property name="text">
              <string>Gamma曲线测试</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>亮度曲线测试</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>对比度测试</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>色域测试</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>均一性测试</string>
             </property>
            </item>
           </widget>
          </item>
          <item row="1" column="5">
           <widget class="QSpinBox" name="spinBoxStep">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="maximum">
             <number>9999</number>
            </property>
           </widget>
          </item>
          <item row="1" column="4">
           <widget class="QLabel" name="label_7">
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="text">
             <string>灰阶/亮度步进</string>
            </property>
           </widget>
          </item>
          <item row="1" column="3">
           <widget class="QSpinBox" name="spinBox_maxLight">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="maximum">
             <number>10000</number>
            </property>
            <property name="value">
             <number>255</number>
            </property>
           </widget>
          </item>
          <item row="0" column="5">
           <widget class="QComboBox" name="comboBoxChannel">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <item>
             <property name="text">
              <string>DCI-P3 D65</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>NTSC</string>
             </property>
            </item>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,1,0,0,0">
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>15</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_3">
            <item>
             <widget class="QLabel" name="label_9">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">padding:5; border-radius:5;</string>
              </property>
              <property name="text">
               <string>通信控制方式</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_4">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">padding:5; border-radius:5;</string>
              </property>
              <property name="text">
               <string>CAN通信参数</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_5">
            <item>
             <widget class="QComboBox" name="comboBoxType">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <item>
               <property name="text">
                <string>ADB</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>SIMBOX</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>CAN</string>
               </property>
              </item>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="lineEdit">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_4">
            <property name="leftMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QPushButton" name="pushButtonStart">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">border-radius:5;</string>
              </property>
              <property name="text">
               <string>启动</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButtonStop">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">border-radius:5;</string>
              </property>
              <property name="text">
               <string>停止</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <property name="leftMargin">
         <number>15</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>15</number>
        </property>
        <property name="bottomMargin">
         <number>15</number>
        </property>
        <item>
         <widget class="QTableWidget" name="tableWidget">
          <property name="styleSheet">
           <string notr="true">border-radius:0; margin:0;</string>
          </property>
          <property name="autoScrollMargin">
           <number>10</number>
          </property>
          <attribute name="verticalHeaderVisible">
           <bool>false</bool>
          </attribute>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
