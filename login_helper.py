import subprocess
import time
import sys
import os
import pyautogui

pyautogui.PAUSE = 0.3

class LoginHelper:
    def __init__(self, username, password):
        self.username = username
        self.password = password

    def auto_login(self):
        # 启动ATEApp.py
        print("正在启动ATEApp...")
        
        # 获取ATEApp.py的路径
        current_dir = os.getcwd()
        ate_app_path = os.path.join(current_dir, "ATEApp.py")
        
        # 指定虚拟环境的Python路径
        python_path = "d:/3_PythonWorkspace/HWTreeATE/.venv/Scripts/python.exe"
        
        # 启动ATEApp
        process = subprocess.Popen([python_path, ate_app_path])
        
        # 等待登录窗口出现
        print("等待登录窗口出现...")
        time.sleep(5)  # 给应用程序一些启动时间

        # 点击密码输入框
        print("点击密码输入框...")
        #pyautogui.press('tab')  # 先按Tab键切换焦点
        time.sleep(1)  # 等待输入框切换
        pyautogui.click(x=1920, y=1160)  # 这里的坐标需要根据实际情况调整
        
        # 自动填充密码
        print(f"正在输入密码...")
        pyautogui.write(self.password)
        
        # 自动填充用户名
        print(f"正在输入用户名: {self.username}")

        pyautogui.click(x=1920, y=1010)  # 替换为实际的用户名输入框
        pyautogui.write(self.username)
        pyautogui.press('enter')
    

        
        # 点击登录按钮
        print("点击登录按钮...")
        pyautogui.press('enter')  # 或者可以使用tab再回车

        time.sleep(1)

        pyautogui.press('enter')  # 或者可以使用tab再回车
        
        print("登录过程完成！")


if __name__ == "__main__":
    # 从命令行参数获取用户名和密码

    username = "hengyiw"
    password = "hwtc@666"

    # 创建登录助手实例并执行登录
    login_helper = LoginHelper(username, password)
    login_helper.auto_login()