import ctypes
from ctypes import *
import time

# 加载DLL
DAQdll = WinDLL(r"D:\work\HWTreeATE\adb\dll\USB6103.dll")

# 打开设备
erro = DAQdll.OpenUsbV6103()
if erro != 0:
    print("打开设备失败")
    exit()
# 定义函数原型
DoSetV6103 = DAQdll.DoSetV6103
DoSetV6103.argtypes = [c_int, c_ubyte, c_ubyte]
DoSetV6103.restype = c_int

result = DoSetV6103(0, 3, 1)
if result == 0:
    print(f"成功设置设备 ")
else:
    print(f"设置失败，错误代码：{result}")


# 配置采集参数
dev = 0  # 设备号
chan =0  # 通道号
gain = 1  # 量程 (±10.24V)
rate = 10000 # 采样频率 (1kHz)

# 启动连续采集
erro = DAQdll.ADContinuConfigV6103(dev, chan, gain, rate)
if erro != 0:
    print("配置连续采集失败")
    DAQdll.CloseUsbV6103()
    exit()

try:
    while True:
        # 查询缓冲区数据量
        buffer_size = DAQdll.GetAdBuffSizeV6103(dev)
        if buffer_size > 0:
            # 准备读取数据的缓冲区
            data_buffer = (c_float * buffer_size)()
            # 读取数据
            read_count = DAQdll.ReadAdBuffV6103(dev, data_buffer, buffer_size)
            if read_count > 0:
                # 处理数据 (这里只是简单打印，您可以根据需要进行数据处理)
                print(f"读取到 {read_count} 个数据点")
                print(f"前5个数据点: {[data_buffer[i] for i in range(max(5, read_count))]}")
                # print(f"最后5个数据点: ",[data_buffer[i] for i in range(min(5, read_count))])

        # 短暂休眠，避免过度占用CPU
        time.sleep(0.01)

except KeyboardInterrupt:
    print("用户中断，停止采集")

finally:
    # 停止采集
    DAQdll.ADContinuStopV6103(dev)
    # 关闭设备
    DAQdll.CloseUsbV6103()

print("程序结束")