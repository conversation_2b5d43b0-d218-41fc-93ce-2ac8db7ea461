import datetime
import operator
import os, time
import threading

from common.LogUtils import logger, CACHE_PATH
from vision.CameraManager import camera_manager
from vision.FrequencyTest import frequency_test
from utils.LogReader import mcu_log_reader, logcat_reader, soc_log_reader
from utils.SignalsManager import signals_manager
from case.CaseManager import <PERSON><PERSON><PERSON><PERSON><PERSON>, case_manager, CaseStatus
from case.StepManager import step_manager
from utils.ProjectManager import project_manager


class ExecuteCaseManager(CaseReceiver):

    @staticmethod
    def start_case_monitor_video(case_number, case_start_time):
        logger.info(f"start_case_monitor_video case_number={case_number}, case_start_time={case_start_time}")
        case_start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
        file_date = f"{case_number}_{case_start_time}"
        monitor_video_path = os.path.join(CACHE_PATH, "MonitorVideo")
        if not os.path.exists(monitor_video_path):
            os.mkdir(monitor_video_path)
        save_path = os.path.join(monitor_video_path, file_date)
        if not os.path.exists(save_path):
            os.mkdir(save_path)

        threading.Thread(target=frequency_test.start_case_monitor_video,
                         args=(save_path, camera_manager.video_list_monitor)).start()

    def receive_case(self, case):
        from oscilloscope.OscilloscopeManager import oscilloscope_manager
        from logic_analyzer.LogicManager import logic_manager
        mcu_log_reader.clear_error_mark()
        soc_log_reader.clear_error_mark()
        logcat_reader.clear_error_mark()
        step_manager.reset_params()
        case_manager.set_current_case(case)
        case_manager.case_order += 1
        case_index = case_manager.cases.index(case)
        total_times = case.get("cycle")
        execute_times = case.get("execute_times")
        if case_index == 0:
            if execute_times == 1:
                case.update({"is_start": True})
            else:
                case.update({"is_start": False})
        if case_index == len(case_manager.cases) - 1:
            if execute_times == total_times:
                case.update({"is_end": True})
            else:
                case.update({"is_end": False})

        case_id = str(case.get("id"))
        case_number = str(case.get("number"))
        case_name = case.get("name")
        vision_revert = case.get("vision_revert")
        vision_algorithm = case.get("vision_algorithm")
        execute_mode = case.get("execute_mode")
        step_manager.set_vision_revert(case_number, vision_revert)
        step_manager.set_vision_algorithm(case_number, vision_algorithm)
        step_manager.set_execute_mode(case_number, execute_mode)
        case_manager.update_case_status(case_number, CaseStatus.TESTING)
        case_start_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        # 将测试用例中的step放在队列中依次执行
        step_manager.clear_steps()
        case_manager.update_start_time(case_start_time=case_start_time)
        if execute_times == 1:
            # 用例执行的第1次将用例状态修改为执行中
            signals_manager.update_case_status.emit(case_manager.case_index, case_number, "执行中")
        steps = case.get("test_steps", [])

        for i in range(len(steps)):
            step = steps[i]
            logger.info(f"receive_case step={step}")
            if operator.eq("CUSTOM_CMD", step.get("type")):
                params = step.get("params")
                step_manager.append_step({"total_times": 1,
                                          "current_times": 1,
                                          "step_index": i,
                                          "step_type": "CustomizeCMD",
                                          "case_id": case_id,
                                          "case_number": case_number,
                                          "case_name": case_name,
                                          "execute_mode": execute_mode,
                                          "CustomizeCMD": params.get("cmd"),
                                          "data": ",".join([str(i) for i in params.get("params")]),
                                          "expect": step.get("expectation", ""),
                                          "command": params.get("cmd"),
                                          "params": ",".join([str(i) for i in params.get("params")]),
                                          "status": "待执行"
                                          })
            elif operator.eq("I2C", step.get("type")):
                params = step.get("params")
                step_manager.append_step({"total_times": 1,
                                          "current_times": 1,
                                          "step_index": i,
                                          "step_type": step.get("type"),
                                          "case_id": case_id,
                                          "case_number": case_number,
                                          "case_name": case_name,
                                          "execute_mode": execute_mode,
                                          "min": params.get("min"),
                                          "max": params.get("max"),
                                          "option": params.get("option"),
                                          "expect": params.get("recv_msg"),
                                          "command": step.get("type"),
                                          "params": params.get("cmd"),
                                          "status": "待执行"
                                          })
            elif operator.eq("CAN", step.get("type")):
                params = step.get("params")
                step_manager.append_step({"total_times": 1,
                                          "current_times": 1,
                                          "step_index": i,
                                          "step_type": step.get("type"),
                                          "case_id": case_id,
                                          "case_number": case_number,
                                          "case_name": case_name,
                                          "execute_mode": execute_mode,
                                          "can_type": step.get("can_type", "zlg"),
                                          "msg_type": params.get("msg_type", "MSG_CUSTOM"),
                                          "cycle_period": params.get("period"),
                                          "data_expect": params.get("recv_msg", ""),
                                          "expect": params.get("recv_msg", ""),
                                          "id": params.get("send_id"),
                                          "msg": params.get("send_msg"),
                                          "recv_id": params.get("recv_id"),
                                          "uds": "0",
                                          "command": step.get("type"),
                                          "consecutive_frames": params.get("consecutive_frames"),
                                          "params": "%s: %s" % (params.get("send_id"), params.get("send_msg")),
                                          "status": "待执行",
                                          "channel": int(params.get("channel"))
                                          })
            elif operator.eq("LIN", step.get("type")):
                params = step.get("params")
                step_manager.append_step({"total_times": 1,
                                          "current_times": 1,
                                          "step_index": i,
                                          "step_type": step.get("type"),
                                          "case_id": case_id,
                                          "case_number": case_number,
                                          "case_name": case_name,
                                          "check_number_type": params.get("check_number_type"),
                                          "direction": params.get("Publisher"),
                                          "expect": step.get("expectation", ""),
                                          "id": step.get("id"),
                                          "msg": params.get("send_msg"),
                                          "period": str(params.get("period")),
                                          "period_time": str(params.get("period")),
                                          "command": step.get("type"),
                                          "params": "%s: %s" % (step.get("type"), params.get("send_msg")),
                                          "status": "待执行"
                                          })
            elif operator.eq("MANUAL", step.get("type")):
                step_manager.append_step({"total_times": 1,
                                          "current_times": 1,
                                          "step_index": i,
                                          "step_type": step.get("type"),
                                          "case_id": case_id,
                                          "case_number": case_number,
                                          "case_name": case_name,
                                          "execute_mode": execute_mode,
                                          "command": step.get("desc"),
                                          "expect": step.get("expectation")
                                          })

        logger.info(f"receive_case steps_size={len(step_manager.steps)}")

        signals_manager.init_steps.emit()
        step_manager.set_step()
        logcat_reader.stop()
        # 监控os日志
        logcet_thread = None
        if not logcat_reader.is_running:
            if logcet_thread is not None:
                try:
                    logcet_thread.join(timeout=1)
                except Exception as e:
                    logger.error(f"logcet_thread.join error: {e}")
            logcet_thread = threading.Thread(target=logcat_reader.start_logcat, args=("VideoSourceApp",))
            logcet_thread.start()
        else:
            logger.info(f"logcat_reader.is_running={logcat_reader.is_running}")

        # 开始用例监控视频
        self.start_case_monitor_video(case_number, case_start_time)
        # 开始测试时运行示波器数据采集
        if oscilloscope_manager.is_open():
            threading.Timer(interval=3, function=oscilloscope_manager.run_oscilloscope).start()
        # 开始测试时运行逻辑分析仪数据采集
        if logic_manager.is_open():
            threading.Timer(interval=3, function=logic_manager.start).start()
            # 模拟开始逻辑分析仪数据采集
            # threading.Timer(interval=2, function=logic_manager.start_simulate).start()

    def __init__(self):
        super().__init__()
        case_manager.register_receiver(self)
        self.register_case_type("CAN")
        self.register_case_type("LIN")
        self.register_case_type("I2C")
        self.register_case_type("CustomizeCMD")
        self.project_name = ""
        self.adb_actual_msg = ""
        self.root = None
        self.plan = None

    def start_execute_case(self, plan):
        logger.info(f"start_execute_case plan={plan}")
        if project_manager.project_info is not None and len(project_manager.project_info["configs"]) > 0:
            project_manager.set_phy_address_id(project_manager.project_info["configs"].get("phy_address_id"))
            project_manager.set_func_address_id(project_manager.project_info["configs"].get("func_address_id"))
            project_manager.set_response_id(project_manager.project_info["configs"].get("response_id"))
        project_manager.set_test_plan_name(plan.get("name", ""))
        project_manager.set_test_plan_id(plan.get("id", 0))
        project_manager.set_test_plan_project_name(plan.get("project_name", ""))
        project_manager.set_test_plan_project_number(plan.get("project_number", ""))
        project_manager.set_test_version(plan.get("m_version", ""))
        project_manager.set_relate_version(plan.get("product_version", []))
        project_manager.set_plan_status(plan.get("status", ""))
        signals_manager.update_test_status.emit(CaseStatus.TESTING)
        signals_manager.update_project_info.emit()
        case_manager.abnormal_stop = plan.get("abnormal_stop", False)
        case_manager.finish_notice = plan.get("finish_notice", False)
        case_manager.case_index = 0
        case_manager.set_case(execute_times=1)
        case_manager.status = CaseStatus.TESTING
        step_manager.status = True
        self.start_serial_reader()
        mcu_log_reader.clear_error_mark()
        soc_log_reader.clear_error_mark()
        logcat_reader.clear_error_mark()

    @staticmethod
    def start_serial_reader():
        logger.info("start_serial_reader")
        # 先停止当前运行的读取器
        mcu_log_reader.stop()
        soc_log_reader.stop()
        
        # 确保等待一小段时间让线程真正停止
        time.sleep(0.5)
        # 确认停止状态
        if mcu_log_reader.is_running:
            logger.warning("MCU reader failed to stop, forcing state reset")
            mcu_log_reader.is_running = False
            
        if soc_log_reader.is_running:
            logger.warning("SOC reader failed to stop, forcing state reset")
            soc_log_reader.is_running = False
        
        # 检查并启动新线程
        try:
            mcu_log_reader.stop_event.clear()
            mcu_thread = threading.Thread(
                target=mcu_log_reader.read_mcu_msg, 
                name="MCU_LOG_READER"
            )
            mcu_thread.daemon = True  # 设为守护线程，主线程退出时自动结束
            mcu_thread.start()
            # 验证线程启动
            time.sleep(0.1)
            if not mcu_log_reader.is_running:
                logger.error("MCU reader thread failed to start properly")
        except Exception as e:
            logger.error(f"Failed to start MCU reader thread: {e}")
        
        # SOC日志读取器
        try:
            soc_log_reader.stop_event.clear()
            soc_thread = threading.Thread(
                target=soc_log_reader.read_soc_msg, 
                name="SOC_LOG_READER"
            )
            soc_thread.daemon = True  # 设为守护线程，主线程退出时自动结束 
            soc_thread.start()
            # 验证线程启动
            time.sleep(0.1)
            if not soc_log_reader.is_running:
                logger.error("SOC reader thread failed to start properly")
        except Exception as e:
            logger.error(f"Failed to start SOC reader thread: {e}")
        logger.info(f"Serial readers status - MCU: {mcu_log_reader.is_running}, SOC: {soc_log_reader.is_running}")


execute_case_manager: ExecuteCaseManager = ExecuteCaseManager()
