import os
import logging
from logging.handlers import RotatingFileHandler

USER_DIR = os.path.expanduser('~')
LOG_DIR = os.path.join(USER_DIR, "auto_test_m", "logs")
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)


def get_logger(file_name, level=logging.INFO):
    if not file_name.endswith('.log'):
        file_name = file_name + '.log'
    path_name = LOG_DIR + os.sep + file_name

    log = logging.getLogger(file_name)

    log.setLevel(level)

    formatter = logging.Formatter(
        "%(asctime)s %(pathname)s %(filename)s %(funcName)s %(lineno)s \
      %(levelname)s - %(message)s", "%Y-%m-%d %H:%M:%S")

    if level == logging.DEBUG:
        log_file_handler = RotatingFileHandler(
            filename=path_name, maxBytes=1024 * 1024 * 10, backupCount=1, encoding="utf-8"
        )
    else:
        log_file_handler = RotatingFileHandler(
            filename=path_name, maxBytes=1024 * 1024 * 50, backupCount=10, encoding="utf-8"
        )
    log_file_handler.setFormatter(formatter)
    log.addHandler(log_file_handler)

    stream_handler = logging.StreamHandler()
    stream_handler.setFormatter(formatter)
    log.addHandler(stream_handler)

    return log
