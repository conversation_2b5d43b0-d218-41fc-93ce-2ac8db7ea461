import cv2
import numpy as np



Calibration = {
    "point1": np.array([[15.515244683576741, 362.11734563156546, -3566.602613374325], [368.9264668203946, -11.448373046374597, -292034.38739431207]]),
    "point3": np.array([[-385.24554, 27.3228447, 391385.87068], [32.496089, 370.731993, -358661.83039]]),
    "color_analyser": np.array( [[-378.1212, 30.75036, 410688.72707], [34.40887, 367.750293, -354186.222546]])
}

def pixel_to_physical(pixel_coords, M = np.array([[15.515244683576741, 362.11734563156546, -3566.602613374325], [368.9264668203946, -11.448373046374597, -292034.38739431207]])):
    """
    将像素坐标转换为物理坐标

    参数:
    pixel_coords: 像素坐标点列表，形如 [[x1,y1], [x2,y2], ...]
    M: 由cv2.estimateAffine2D()得到的仿射变换矩阵

    返回:
    physical_coords: 物理坐标点列表，形如 [[x1,y1], [x2,y2], ...]
    """
    # 确保输入是numpy数组
    pixel_coords = np.array(pixel_coords)

    homogeneous_coords = np.hstack([pixel_coords, np.ones((pixel_coords.shape[0], 1))])
    physical_coords = np.dot(homogeneous_coords, M.T)

    return physical_coords.tolist()
if __name__ == '__main__':
    # pixel_coords = [[982, 240]] #1
    # # 假设的仿射变换矩阵（这只是一个示例，实际使用时应该用cv2.estimateAffine2D的结果）
    # M = np.array([[15.515244683576741, 362.11734563156546, -3566.602613374325], [368.9264668203946, -11.448373046374597, -292034.38739431207]])
    # physical_coords = pixel_to_physical(pixel_coords, M)
    # print("像素坐标:")
    # print(pixel_coords)
    # print("\n物理坐标:")
    # print(physical_coords)


    pixel_coords = np.array([
        [1612   , 895],  # 1
        # [1295, 379],  # 2
        [1346, 925  ],  # 3
        # [1211, 254],  # 4
        # [1215, 381],
        # [1225, 508], #6
        [1604, 814  ], #7
        # [1141, 387], #8
        [1340, 843 ], #9
    ] ,dtype=np.float32)

    # 机械手坐标 (x, y, z) #R -24567.0 Z -61286.0
    # robot_coords = np.array([
    #     [151038.0, 64737.0, ],  # 2
    #     [202050.0, 62016.0, ],  # 3
    #     [148389.0, 32555.0, ],  # 5
    #     [200038.0, 31418.0, ],  # 6
    # ], dtype=np.float32)
    robot_coords = np.array([
        [-205176.0 ,25527.0 , ],  #1
        # [-109190.0 , 87889.0, ],  # 2
        [-101881.0,28005.0],  # 3
        # [-159218.0, 54326.0, ],  # 4
        #
        # [-108763.0,55647.0, ],  # 5
        # [-57067.0, 60783.0, ],  # 6
        [-204884.0  , -4067.0, ],  #    4
        # [-106546.0, 25666.0, ],  # 8
        [-101810.0, -2590.0, ],  # 6
    ], dtype=np.float32)

    M, inliers = cv2.estimateAffine2D(pixel_coords, robot_coords)
    print("矩阵:\n", M.tolist())
    print()

