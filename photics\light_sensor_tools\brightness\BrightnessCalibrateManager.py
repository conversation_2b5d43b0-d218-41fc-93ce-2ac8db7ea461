# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/6/21 21:22
@Desc   : 亮度标定管理模块
"""
import time

from common.LogUtils import logger
from utils.SignalsManager import signals_manager
from photics import photics_manager
from photics.light_sensor_tools import Utils
from photics.light_sensor_tools.light_sensor.ColorTempLightIntensityClient import color_temp_light_intensity_client
from case.VdsDetectManager import vds_detect_manager
from utils import int_to_high_low
from utils.ProjectManager import project_manager


class BrightnessCalibrateManager:

    def __init__(self):
        super().__init__()

    def calibrate(self, case_id, command, threshold):
        test_plan_project_number = project_manager.get_test_plan_project_number()
        logger.info(f"calibrate case_id={case_id}, command={command}, threshold={threshold}")
        if test_plan_project_number in ["ICSCN30"]:
            # 特殊亮度校准流程
            self.calibrate_special(case_id, command, threshold)
        else:
            # 通用亮度校准流程
            self.calibrate_general(case_id, command, threshold)

    def calibrate_special(self, case_id, command, threshold):
        # threshold为标定误差百分比值
        logger.info(f"calibrate_special case_id={case_id}, command={command}, threshold={threshold}")
        device_brightness = self.get_average_device_brightness()
        product_brightness = self.get_special_average_product_brightness()
        k = int(round(device_brightness / product_brightness, 3) * 1000)
        logger.info(f"calibrate_special device_brightness={device_brightness}, "
                    f"product_brightness={product_brightness}, k={k}")
        # 将校准系数写入MCU
        self.set_brightness_coefficient(k)
        time.sleep(3)
        device_intensity = self.get_light_intensity()
        product_intensity = self.get_product_brightness()
        ret = abs(product_intensity - device_intensity) < device_intensity * (threshold / 100)
        if ret:
            desc = f"亮度标定成功: 色度计照度值(5000Lux)={device_intensity}Lux, 产品照度值(5000Lux)={product_intensity}Lux"
            result = "PASS"
        else:
            desc = f"亮度标定失败: 色度计照度值(5000Lux)={device_intensity}Lux, 产品照度值(5000Lux)={product_intensity}Lux"
            result = "NG"
        signals_manager.step_execute_finish.emit(case_id, command, result, desc)

    def calibrate_general(self, case_id, command, threshold):
        from adb.AdbConnectDevice import adb_connect_device
        adb_connect_device.notify_brightness_calibrate_start()
        # threshold为标定误差百分比值
        logger.info(f"calibrate_general case_id={case_id}, command={command}, threshold={threshold}")
        device_brightness = self.get_average_device_brightness()
        product_brightness = self.get_general_average_product_brightness()
        coefficient = int(round(device_brightness / product_brightness, 3) * 1000)
        logger.info(f"calibrate_general device_brightness={device_brightness}, product_brightness={product_brightness},"
                    f"coefficient={coefficient}")
        # 将校准系数写入MCU
        adb_connect_device.write_brightness_coefficient(str(coefficient))
        time.sleep(3)
        device_intensity = self.get_light_intensity()
        if vds_detect_manager.resp_light_sensor == -1:
            return signals_manager.step_execute_finish.emit(case_id, command, "NG", "亮度校准失败: 产品亮度读取失败")

        product_intensity = vds_detect_manager.resp_light_sensor
        logger.info(f"calibrate_general device_intensity={device_intensity}, product_intensity={product_intensity}")
        ret = abs(product_intensity - device_intensity) < device_intensity * (threshold / 100)
        if ret:
            desc = f"亮度校准成功: 色度计照度值(5000Lux)={device_intensity}Lux, 产品照度值(5000Lux)={product_intensity}Lux"
            result = "PASS"
        else:
            desc = f"亮度校准失败: 色度计照度值(5000Lux)={device_intensity}Lux, 产品照度值(5000Lux)={product_intensity}Lux"
            result = "NG"
        signals_manager.step_execute_finish.emit(case_id, command, result, desc)
        adb_connect_device.notify_brightness_calibrate_finish()

    @staticmethod
    def set_brightness_coefficient(coefficient):
        logger.info(f"set_brightness_coefficient coefficient={coefficient}")
        # 亮度校准标识
        brightness_flag = bytearray([0xEE, 0x00, 0x05, 0x03])
        high, low = int_to_high_low(coefficient)
        data = bytearray([high, low])
        brightness_xor_data = Utils.add_xor_sum(data)
        brightness_actual_result = Utils.merge_and_add_xor_sum(brightness_flag, brightness_xor_data)
        brightness_hex_data = " ".join([f'0x{byte:02X}' for byte in brightness_actual_result])
        logger.info(f"set_color_calibrate_coefficient brightness_hex_data={brightness_hex_data}")
        # 写入亮度校准系数
        write_brightness = f"adb shell i2ctransfer -f -y 4 w8@0x2b {brightness_hex_data}"
        Utils.execute_adb_command(write_brightness)
        time.sleep(1)
        # 读取亮度校准系数
        read_brightness = "i2ctransfer  -f -y 4 w1@0x2b 0xA3 r6"
        Utils.execute_adb_command(read_brightness)

    @staticmethod
    def get_light_intensity():
        # 读取色温传感器的照度值
        light_intensity = color_temp_light_intensity_client.get_light_intensity()
        if light_intensity is None:
            light_intensity = -1

        return light_intensity

    @staticmethod
    def get_product_brightness():
        photics_manager.execute_adb_command("adb root")
        cmd = "adb shell cat /sys/bus/i2c/drivers/veml6046/10-0029/sensor_value"
        output = photics_manager.execute_adb_command(cmd)
        logger.warning(f"get_product_brightness output={output}")
        brightness = -1
        if len(output) > 0:
            if output[0].__contains__("\n"):
                brightness = float(output[0].replace("\n", "").strip())
                logger.warning(f"get_product_brightness brightness={brightness}")
            else:
                logger.warning(f"get_product_brightness output format is invalid")
        else:
            logger.warning(f"get_product_brightness output is null")

        return brightness

    @staticmethod
    def remove_min_max(arr):
        arr.sort()
        # 返回排序后的列表，去掉第一个元素（最小值）和最后一个元素（最大值）
        return arr[1:-1]

    def get_average_device_brightness(self, times=10):
        data = []
        for i in range(times):
            light_intensity = self.get_light_intensity()
            data.append(light_intensity)
            time.sleep(0.5)
        data = self.remove_min_max(data)
        average = self.calculate_average(data)
        average = round(average, 3)
        logger.info(f"get_average_device_brightness average={average}")
        return average

    def get_special_average_product_brightness(self, times=10):
        data = []
        for i in range(times):
            brightness = self.get_product_brightness()
            data.append(brightness)
            time.sleep(0.5)
        data = self.remove_min_max(data)
        average = self.calculate_average(data)
        average = round(average, 3)
        logger.info(f"get_special_average_product_brightness average={average}")
        return average

    def get_general_average_product_brightness(self, times=10):
        data = []
        for i in range(times):
            data.append(vds_detect_manager.resp_light_sensor)
            time.sleep(0.5)
        logger.info(f"get_general_average_product_brightness data={data}")
        data = self.remove_min_max(data)
        average = self.calculate_average(data)
        average = round(average, 3)
        logger.info(f"get_general_average_product_brightness average={average}")
        return average

    @staticmethod
    def calculate_average(numbers):
        if numbers:
            return sum(numbers) / len(numbers)
        else:
            return 0


brightness_calibrate_manager: BrightnessCalibrateManager = BrightnessCalibrateManager()
