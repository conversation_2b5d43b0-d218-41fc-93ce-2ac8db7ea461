# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'OpticalTest.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_OpticalTestForm(object):
    def setupUi(self, OpticalTestForm):
        OpticalTestForm.setObjectName("OpticalTestForm")
        OpticalTestForm.resize(1600, 900)
        OpticalTestForm.setMinimumSize(QtCore.QSize(1600, 900))
        OpticalTestForm.setStyleSheet("")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(OpticalTestForm)
        self.verticalLayout_2.setContentsMargins(5, 5, 0, 0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.OpticalTestWidget = QtWidgets.QWidget(OpticalTestForm)
        self.OpticalTestWidget.setStyleSheet("")
        self.OpticalTestWidget.setObjectName("OpticalTestWidget")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.OpticalTestWidget)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setContentsMargins(10, 20, -1, 0)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setVerticalSpacing(6)
        self.gridLayout.setObjectName("gridLayout")
        self.spinBox_minLight = QtWidgets.QSpinBox(self.OpticalTestWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinBox_minLight.sizePolicy().hasHeightForWidth())
        self.spinBox_minLight.setSizePolicy(sizePolicy)
        self.spinBox_minLight.setStyleSheet("")
        self.spinBox_minLight.setObjectName("spinBox_minLight")
        self.gridLayout.addWidget(self.spinBox_minLight, 1, 1, 1, 1)
        self.label_2 = QtWidgets.QLabel(self.OpticalTestWidget)
        self.label_2.setStyleSheet("")
        self.label_2.setObjectName("label_2")
        self.gridLayout.addWidget(self.label_2, 0, 4, 1, 1)
        self.label_3 = QtWidgets.QLabel(self.OpticalTestWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_3.sizePolicy().hasHeightForWidth())
        self.label_3.setSizePolicy(sizePolicy)
        self.label_3.setObjectName("label_3")
        self.gridLayout.addWidget(self.label_3, 1, 2, 1, 1)
        self.label = QtWidgets.QLabel(self.OpticalTestWidget)
        self.label.setStyleSheet("")
        self.label.setObjectName("label")
        self.gridLayout.addWidget(self.label, 0, 0, 1, 1)
        self.label_5 = QtWidgets.QLabel(self.OpticalTestWidget)
        self.label_5.setStyleSheet("padding:10; border-radius:5;")
        self.label_5.setObjectName("label_5")
        self.gridLayout.addWidget(self.label_5, 1, 0, 1, 1)
        self.comboBoxTest = QtWidgets.QComboBox(self.OpticalTestWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.comboBoxTest.sizePolicy().hasHeightForWidth())
        self.comboBoxTest.setSizePolicy(sizePolicy)
        self.comboBoxTest.setMinimumSize(QtCore.QSize(0, 45))
        self.comboBoxTest.setStyleSheet("")
        self.comboBoxTest.setObjectName("comboBoxTest")
        self.comboBoxTest.addItem("")
        self.comboBoxTest.addItem("")
        self.comboBoxTest.addItem("")
        self.comboBoxTest.addItem("")
        self.comboBoxTest.addItem("")
        self.gridLayout.addWidget(self.comboBoxTest, 0, 1, 1, 3)
        self.spinBoxStep = QtWidgets.QSpinBox(self.OpticalTestWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinBoxStep.sizePolicy().hasHeightForWidth())
        self.spinBoxStep.setSizePolicy(sizePolicy)
        self.spinBoxStep.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBoxStep.setStyleSheet("")
        self.spinBoxStep.setMaximum(9999)
        self.spinBoxStep.setObjectName("spinBoxStep")
        self.gridLayout.addWidget(self.spinBoxStep, 1, 5, 1, 1)
        self.label_7 = QtWidgets.QLabel(self.OpticalTestWidget)
        self.label_7.setStyleSheet("")
        self.label_7.setObjectName("label_7")
        self.gridLayout.addWidget(self.label_7, 1, 4, 1, 1)
        self.spinBox_maxLight = QtWidgets.QSpinBox(self.OpticalTestWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinBox_maxLight.sizePolicy().hasHeightForWidth())
        self.spinBox_maxLight.setSizePolicy(sizePolicy)
        self.spinBox_maxLight.setStyleSheet("")
        self.spinBox_maxLight.setMaximum(10000)
        self.spinBox_maxLight.setProperty("value", 255)
        self.spinBox_maxLight.setObjectName("spinBox_maxLight")
        self.gridLayout.addWidget(self.spinBox_maxLight, 1, 3, 1, 1)
        self.comboBoxChannel = QtWidgets.QComboBox(self.OpticalTestWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.comboBoxChannel.sizePolicy().hasHeightForWidth())
        self.comboBoxChannel.setSizePolicy(sizePolicy)
        self.comboBoxChannel.setMinimumSize(QtCore.QSize(0, 45))
        self.comboBoxChannel.setStyleSheet("")
        self.comboBoxChannel.setObjectName("comboBoxChannel")
        self.comboBoxChannel.addItem("")
        self.comboBoxChannel.addItem("")
        self.gridLayout.addWidget(self.comboBoxChannel, 0, 5, 1, 1)
        self.gridLayout.setColumnStretch(0, 1)
        self.gridLayout.setColumnStretch(3, 1)
        self.gridLayout.setColumnStretch(4, 1)
        self.gridLayout.setColumnStretch(5, 1)
        self.horizontalLayout_2.addLayout(self.gridLayout)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setContentsMargins(-1, 0, 15, 0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout()
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.label_9 = QtWidgets.QLabel(self.OpticalTestWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_9.sizePolicy().hasHeightForWidth())
        self.label_9.setSizePolicy(sizePolicy)
        self.label_9.setMinimumSize(QtCore.QSize(120, 45))
        self.label_9.setStyleSheet("padding:5; border-radius:5;")
        self.label_9.setObjectName("label_9")
        self.verticalLayout_3.addWidget(self.label_9)
        self.label_4 = QtWidgets.QLabel(self.OpticalTestWidget)
        self.label_4.setMinimumSize(QtCore.QSize(120, 45))
        self.label_4.setStyleSheet("padding:5; border-radius:5;")
        self.label_4.setObjectName("label_4")
        self.verticalLayout_3.addWidget(self.label_4)
        self.horizontalLayout.addLayout(self.verticalLayout_3)
        self.verticalLayout_5 = QtWidgets.QVBoxLayout()
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.comboBoxType = QtWidgets.QComboBox(self.OpticalTestWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.comboBoxType.sizePolicy().hasHeightForWidth())
        self.comboBoxType.setSizePolicy(sizePolicy)
        self.comboBoxType.setMinimumSize(QtCore.QSize(0, 45))
        self.comboBoxType.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.comboBoxType.setObjectName("comboBoxType")
        self.comboBoxType.addItem("")
        self.comboBoxType.addItem("")
        self.comboBoxType.addItem("")
        self.verticalLayout_5.addWidget(self.comboBoxType)
        self.lineEdit = QtWidgets.QLineEdit(self.OpticalTestWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.lineEdit.sizePolicy().hasHeightForWidth())
        self.lineEdit.setSizePolicy(sizePolicy)
        self.lineEdit.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit.setStyleSheet("")
        self.lineEdit.setObjectName("lineEdit")
        self.verticalLayout_5.addWidget(self.lineEdit)
        self.horizontalLayout.addLayout(self.verticalLayout_5)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.verticalLayout_4 = QtWidgets.QVBoxLayout()
        self.verticalLayout_4.setContentsMargins(0, -1, -1, -1)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.pushButtonStart = QtWidgets.QPushButton(self.OpticalTestWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButtonStart.sizePolicy().hasHeightForWidth())
        self.pushButtonStart.setSizePolicy(sizePolicy)
        self.pushButtonStart.setMinimumSize(QtCore.QSize(100, 45))
        self.pushButtonStart.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButtonStart.setStyleSheet("border-radius:5;")
        self.pushButtonStart.setObjectName("pushButtonStart")
        self.verticalLayout_4.addWidget(self.pushButtonStart)
        self.pushButtonStop = QtWidgets.QPushButton(self.OpticalTestWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButtonStop.sizePolicy().hasHeightForWidth())
        self.pushButtonStop.setSizePolicy(sizePolicy)
        self.pushButtonStop.setMinimumSize(QtCore.QSize(100, 45))
        self.pushButtonStop.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButtonStop.setStyleSheet("border-radius:5;")
        self.pushButtonStop.setObjectName("pushButtonStop")
        self.verticalLayout_4.addWidget(self.pushButtonStop)
        self.horizontalLayout.addLayout(self.verticalLayout_4)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.horizontalLayout.setStretch(0, 1)
        self.horizontalLayout.setStretch(1, 1)
        self.horizontalLayout_2.addLayout(self.horizontalLayout)
        self.horizontalLayout_2.setStretch(0, 3)
        self.horizontalLayout_2.setStretch(1, 2)
        self.verticalLayout.addLayout(self.horizontalLayout_2)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setContentsMargins(15, 0, 15, 15)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.tableWidget = QtWidgets.QTableWidget(self.OpticalTestWidget)
        self.tableWidget.setStyleSheet("border-radius:0; margin:0;")
        self.tableWidget.setAutoScrollMargin(10)
        self.tableWidget.setObjectName("tableWidget")
        self.tableWidget.setColumnCount(0)
        self.tableWidget.setRowCount(0)
        self.tableWidget.verticalHeader().setVisible(False)
        self.horizontalLayout_3.addWidget(self.tableWidget)
        self.verticalLayout.addLayout(self.horizontalLayout_3)
        self.verticalLayout_2.addWidget(self.OpticalTestWidget)

        self.retranslateUi(OpticalTestForm)
        QtCore.QMetaObject.connectSlotsByName(OpticalTestForm)

    def retranslateUi(self, OpticalTestForm):
        _translate = QtCore.QCoreApplication.translate
        OpticalTestForm.setWindowTitle(_translate("OpticalTestForm", "Form"))
        self.label_2.setText(_translate("OpticalTestForm", "采集通道"))
        self.label_3.setText(_translate("OpticalTestForm", "-"))
        self.label.setText(_translate("OpticalTestForm", "测试场景"))
        self.label_5.setText(_translate("OpticalTestForm", "灰阶/亮度范围"))
        self.comboBoxTest.setItemText(0, _translate("OpticalTestForm", "Gamma曲线测试"))
        self.comboBoxTest.setItemText(1, _translate("OpticalTestForm", "亮度曲线测试"))
        self.comboBoxTest.setItemText(2, _translate("OpticalTestForm", "对比度测试"))
        self.comboBoxTest.setItemText(3, _translate("OpticalTestForm", "色域测试"))
        self.comboBoxTest.setItemText(4, _translate("OpticalTestForm", "均一性测试"))
        self.label_7.setText(_translate("OpticalTestForm", "灰阶/亮度步进"))
        self.comboBoxChannel.setItemText(0, _translate("OpticalTestForm", "DCI-P3 D65"))
        self.comboBoxChannel.setItemText(1, _translate("OpticalTestForm", "NTSC"))
        self.label_9.setText(_translate("OpticalTestForm", "通信控制方式"))
        self.label_4.setText(_translate("OpticalTestForm", "CAN通信参数"))
        self.comboBoxType.setItemText(0, _translate("OpticalTestForm", "ADB"))
        self.comboBoxType.setItemText(1, _translate("OpticalTestForm", "SIMBOX"))
        self.comboBoxType.setItemText(2, _translate("OpticalTestForm", "CAN"))
        self.pushButtonStart.setText(_translate("OpticalTestForm", "启动"))
        self.pushButtonStop.setText(_translate("OpticalTestForm", "停止"))
