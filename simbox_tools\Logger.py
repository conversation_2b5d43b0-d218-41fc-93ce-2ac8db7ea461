"""
user: Created by jid on 2019/4/22.
email: <EMAIL>
description:日志文件
"""

import os
from datetime import datetime



class Logger(object):
    _logTag = "Logger"
    WORK_FOLDER = None
    ERROR_LOGGER_FOLDER = None
    LOGGER_FOLDER = None
    CACHE_FOLDER = None

    def __init__(self):
        self.WORK_FOLDER = os.path.join(os.path.expanduser('~'), "mate_nomi1.5")
        self.ERROR_LOGGER_FOLDER = os.path.join(self.WORK_FOLDER, "error_log")
        self.LOGGER_FOLDER = os.path.join(self.WORK_FOLDER, "log")
        self.CACHE_FOLDER = os.path.join(self.WORK_FOLDER, "cache")

    def create_log_filename(self, strftime_type):
        return '%s.txt' % datetime.now().strftime(strftime_type)

    def write_log(self, log, append):
        try:
            if not os.path.exists(self.LOGGER_FOLDER):
                os.makedirs(self.LOGGER_FOLDER)
            file_name = self.create_log_filename("%Y-%m-%d-%H-%M-%S")
            file_path = '%s\\%s' % (self.LOGGER_FOLDER, file_name)
            log = '%s : %s' % (datetime.now().strftime("%Y-%m-%d %H:%M:%S"), log)
            if append:
                mode = 'a'
            else:
                mode = 'w'
            with open(file=file_path, mode=mode, encoding="utf-8") as file:
                file.write(log + '\n')
        except Exception as e:
            print(str(e.args))

    def read_log(self, file_name):
        file_path = '%s//%s' % (self.LOGGER_FOLDER, file_name)
        with open(file=file_path, mode='r', encoding="utf-8") as file:
            log = file.read()
        return log

    @staticmethod
    def console(tag, message):
        log = '%s : %s : %s' % (datetime.now().strftime("%Y-%m-%d %H:%M:%S"), tag, message)
        print(log)
