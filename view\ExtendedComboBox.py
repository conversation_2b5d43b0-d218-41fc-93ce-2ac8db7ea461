import sys

from PyQt5.Qt import Qt, QCompleter, QSortFilterProxyModel
from PyQt5.QtWidgets import QComboBox, QApplication

from common.LogUtils import logger
# from res import custom_combobox_stylesheet


# 带搜索功能的下拉框
class ExtendedComboBox(QComboBox):

    def __init__(self, parent=None):
        super(ExtendedComboBox, self).__init__(parent)
        self.setEditable(True)

        # 添加筛选器模型来筛选匹配项
        self.pFilterModel = QSortFilterProxyModel(self)
        self.pFilterModel.setFilterCaseSensitivity(Qt.CaseInsensitive)  # 大小写不敏感
        self.pFilterModel.setSourceModel(self.model())

        # 添加一个使用筛选器模型的QCompleter
        self.completer = QCompleter(self.pFilterModel, self)

        # 始终显示所有(过滤后的)补全结果
        self.completer.setCompletionMode(QCompleter.UnfilteredPopupCompletion)
        self.completer.setCaseSensitivity(Qt.CaseInsensitive)  # 不区分大小写
        self.setCompleter(self.completer)

        # QComboBox编辑栏文本变化时对应的槽函数
        self.lineEdit().textEdited.connect(self.pFilterModel.setFilterFixedString)
        self.completer.activated.connect(self.on_completer_activated)

    # 当在QCompleter列表选中候，下拉框项目列表选择相应的子项目
    def on_completer_activated(self, text):
        logger.info(f"on_completer_activated text={text}")
        if text:
            index = self.findText(text)
            self.setCurrentIndex(index)
            # self.activated[str].emit(self.itemText(index))

    # 在模型更改时，更新过滤器和补全器的模型
    def setModel(self, model):
        super(ExtendedComboBox, self).setModel(model)
        self.pFilterModel.setSourceModel(model)
        self.completer.setModel(self.pFilterModel)

    # 在模型列更改时，更新过滤器和补全器的模型列
    def setModelColumn(self, column):
        self.completer.setCompletionColumn(column)
        self.pFilterModel.setFilterKeyColumn(column)
        super(ExtendedComboBox, self).setModelColumn(column)

    # 回应回车按钮事件
    def keyPressEvent(self, e):
        if e.key() == Qt.Key_Enter & e.key() == Qt.Key_Return:
            text = self.currentText()
            index = self.findText(text, Qt.MatchExactly | Qt.MatchCaseSensitive)
            self.setCurrentIndex(index)
            self.hidePopup()
            super(ExtendedComboBox, self).keyPressEvent(e)
        else:
            super(ExtendedComboBox, self).keyPressEvent(e)


def run():
    app = QApplication(sys.argv)
    win = ExtendedComboBox()
    win.setStyleSheet("""
        QComboBox QAbstractItemView {
            spacing: 5px; /* 设置垂直间距 */
        }
    """)
    l = ["1fscsd", "1aew", "2asd", "3ewqr", "3ewqc", "2wqpu", "1kjijhm", "4kjndw", "5ioijb", "6eolv", "11ofmsw"]
    win.addItems(l)
    win.show()
    sys.exit(app.exec_())


if __name__ == '__main__':
    run()
