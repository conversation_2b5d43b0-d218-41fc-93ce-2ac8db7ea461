# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/3/4 13:47
@Desc   : 
"""
from PyQt5.QtWidgets import QTableWidgetItem


class Message_tool():
    def __init__(self):
        pass

    def calculate_min_max_values(self,bit_length):
        min_value = 0
        max_value = (1 << bit_length) - 1
        return min_value, max_value

    @staticmethod
    def init_message(count):
        data = ['00'] * count
        return data

    def hex_to_bin(self,data:list):
        # 将十六进制字符串列表转换为整数列表
        int_list = [int(hex_value, 16) for hex_value in data]

        # 将整数列表转换为二进制字符串列表
        binary_strings = [format(integer, '08b') for integer in int_list]

        # 将二进制字符串列表连接为一个连续的二进制字符串
        continuous_binary = ''.join(binary_strings)

        return continuous_binary

    def update_message(self,data,start_bit,bit_width,value,tableWidget_signal,row):
        # print('bit_width=',bit_width)

        continuous_binary=self.hex_to_bin(data)

        min, max= self.calculate_min_max_values(bit_width)
        # print(max,min,value)
        print('value',value)
        value=int(value,16)
        print('value2', value)
        if min <= value <= max:
            value=format(value, f'0{bit_width}b')
        else:
            print('mmmmmmmmmmm')
            tableWidget_signal.setItem(row, 2, QTableWidgetItem(f"{max}"))
            k = float(tableWidget_signal.item(row, 5).text() if tableWidget_signal.item(row, 5) else 1)
            b = float(tableWidget_signal.item(row, 6).text() if tableWidget_signal.item(row, 6) else 0)
            tableWidget_signal.setItem(row, 1, QTableWidgetItem(f"0x{hex(abs(int((max - b) / k)))[2:].upper()}"))
            value=format(max, f'0{bit_width}b')

            # 生成N个字节，所有字节都为0
        # try:
        continuous_binary=continuous_binary[:start_bit]+ value +continuous_binary[start_bit+bit_width:]
        # 将二进制字符串列表切割成每8位一组
        binary_chunks = [continuous_binary[i:i + 8] for i in range(0, len(continuous_binary), 8)]

        # 将每个8位二进制字符串转换为十六进制字符串
        hex_list_restored = [format(int(chunk, 2), '02X') for chunk in binary_chunks]
        return {'code':200,'data':hex_list_restored}
        # except Exception as es:
        #     print('eeror=',es)
        #     return {'code':400,'data':''}

    def decimal_to_hex_split(self,X, w):
        hex_representation = hex(X)[2:].zfill(w * 2)  # 获取十进制数字 X 的16进制表示，确保总长度为 w*2
        split_hex = [hex_representation[i:i+2].upper()for i in range(0, len(hex_representation), 2)]
        return split_hex


message=Message_tool()
if __name__ == '__main__':
    data=['00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '20',
     '00', '00', '00', '00', '00', '00', '00', '00', '00', '00'
        , '00', '00', '00', '00', '1F', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00',
     '00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00', '00']
    start_bit = 106  # 起始位
    bit_width = 6  # 位宽
    value = 20  # 替换值
    data2=message.update_message(data,start_bit,bit_width,value)
    print(data)
    # ret=decimal_to_binary_with_padding(value,bit_width*8)
    # print(ret)

