from PyQt5.QtWidgets import QWidget

from tools.can_tool.DbcRecvWidget import DbcRecvWidget
from tools.can_tool.DbcSendWidget import DbcSendWidget
from common import is_windows_platform
from common.LogUtils import logger
from control_board.auto_test_m.widgets.AxisWidget import AxisWidget
from control_board.auto_test_m.widgets.PositionAdjustWidget import PositionAdjustWidget
from tools.dynamics.usb6013.AntiPinchView import AntiPinchWindow
from tools.endurance.widgets.EnduranceWidget import EnduranceWidget
from i2c_update.view.ViewI2cUpdate import ViewI2cUpdate
from mate.view.PushImgView import PushImgWindow
from tools.picture_tool.PictureWindow import PictureWindow
from power.view.ITM3200PowerWidget import ITM3200PowerWidget
from power.view.PowerView import TommensPowerWidget
from res import global_btn_stylesheet
from rotationanglemeasurement.view.StereoView import StereoWindow
from tools.tri_color_light_tool.widgets.triColorLight import TriColorLightWidget
from tools.write_sn_tool.view.view_control.WriteSnWindow import WriteSnWindow
from ui.UITestTools import Ui_TestTools
from utils.SignalsManager import signals_manager
from view.AutoPressWidget import AutoPressWidget
from view.ColorTempLightSensorTest import ColorTempLightSensorTest
from view.EnvTestWidget import EnvTestWidget
from view.OpticalTestWidget import OpticalTestWidget
from view.VideoTestWidget import VideoTestWidget
from view.canoeUpdate import canoeUpdateWindow
from view.ElevationAngleview import ElevationAngleView


class TestToolsWidget(QWidget, Ui_TestTools):

    def __init__(self, parent=None):
        super(TestToolsWidget, self).__init__(parent)
        self.setupUi(self)
        self.showMaximized()
        self.init_view()

    def init_view(self):
        self.mate_push_img_window = PushImgWindow()
        self.endurance_widget = EnduranceWidget()
        self.picture_tool_widget = PictureWindow()
        self.anti_pinch_window = AntiPinchWindow()
        self.dbc_send_widget = DbcSendWidget()
        self.dbc_recv_widget = DbcRecvWidget()
        self.auto_press_widget = AutoPressWidget()
        self.tri_color_light_widget = TriColorLightWidget()
        self.tommens_power_widget = TommensPowerWidget()
        self.it_m3200_power_widget = ITM3200PowerWidget()
        self.stereo_test_widget = StereoWindow()
        self.color_temp_light_sensor_test = ColorTempLightSensorTest()
        self.video_test_widget = VideoTestWidget()
        self.canoe_updater_widget = canoeUpdateWindow()
        self.elevation_widget = ElevationAngleView()
        self.write_sn_window = WriteSnWindow()

        self.mate_push_img_btn.clicked.connect(self.mate_push_img_window.show)
        self.mate_push_img_btn.setStyleSheet(global_btn_stylesheet)
        self.endurance_btn.clicked.connect(self.endurance_widget.show)
        self.endurance_btn.setStyleSheet(global_btn_stylesheet)
        self.picture_tool_btn.clicked.connect(self.picture_tool_widget.show)
        self.picture_tool_btn.setStyleSheet(global_btn_stylesheet)
        self.anti_pinch_btn.clicked.connect(self.anti_pinch_window.show)
        self.anti_pinch_btn.setStyleSheet(global_btn_stylesheet)
        self.io_control_btn.clicked.connect(self.start_io_control)
        self.io_control_btn.setStyleSheet(global_btn_stylesheet)
        self.position_control_btn.clicked.connect(self.start_position_control)
        self.position_control_btn.setStyleSheet(global_btn_stylesheet)
        self.dbc_send_btn.clicked.connect(self.dbc_send_widget.showMaximized)
        self.dbc_send_btn.setStyleSheet(global_btn_stylesheet)
        self.dbc_recv_btn.clicked.connect(self.dbc_recv_widget.showMaximized)
        self.dbc_recv_btn.setStyleSheet(global_btn_stylesheet)
        self.auto_press_btn.clicked.connect(self.auto_press_widget.show)
        self.auto_press_btn.setStyleSheet(global_btn_stylesheet)
        self.tri_color_light_btn.clicked.connect(self.tri_color_light_widget.show)
        self.tri_color_light_btn.setStyleSheet(global_btn_stylesheet)
        self.tommens_power_btn.clicked.connect(self.tommens_power_widget.show)
        self.tommens_power_btn.setStyleSheet(global_btn_stylesheet)
        self.it_m3200_power_btn.clicked.connect(self.it_m3200_power_widget.show)
        self.it_m3200_power_btn.setStyleSheet(global_btn_stylesheet)
        self.rotate_test_btn.clicked.connect(self.stereo_test_widget.showMaximized)
        self.rotate_test_btn.setStyleSheet(global_btn_stylesheet)
        self.optical_test_btn.clicked.connect(self.show_optical_test_widget)
        self.optical_test_btn.setStyleSheet(global_btn_stylesheet)
        self.light_sensor_test_btn.clicked.connect(self.color_temp_light_sensor_test.show)
        self.light_sensor_test_btn.setStyleSheet(global_btn_stylesheet)
        self.temp_rise_btn.clicked.connect(self.show_env_test_widget)
        self.temp_rise_btn.setStyleSheet(global_btn_stylesheet)
        self.video_test_btn.clicked.connect(self.video_test_widget.showMaximized)
        self.video_test_btn.setStyleSheet(global_btn_stylesheet)
        self.i2c_update_btn.clicked.connect(self.show_i2c_update_widget)
        self.i2c_update_btn.setStyleSheet(global_btn_stylesheet)
        self.elevation_angle_btn.clicked.connect(self.elevation_widget.show)
        self.elevation_angle_btn.setStyleSheet(global_btn_stylesheet)
        # 添加VDS升级按钮点击事件
        self.vds_upgrade_btn.clicked.connect(self.show_vds_upgrade_widget)
        self.vds_upgrade_btn.setStyleSheet(global_btn_stylesheet)
        # 添加LIN调试按钮点击事件
        self.lin_debug_btn.clicked.connect(self.show_lin_debug_widget)
        self.lin_debug_btn.setStyleSheet(global_btn_stylesheet)
        signals_manager.canoe_update_process.connect(self.update_canoe_update_process)
        self.write_sn_btn.clicked.connect(self.write_sn_window.show)
        self.write_sn_btn.setStyleSheet(global_btn_stylesheet)
        self.pushButton_2.setStyleSheet(global_btn_stylesheet)
        self.pushButton_3.setStyleSheet(global_btn_stylesheet)

    def show_lin_debug_widget(self):
        logger.info(f"show_lin_debug_widget")
        from view.LinDebugWidget import LinDebugWidget
        self.lin_debug_widget = LinDebugWidget()
        self.lin_debug_widget.showMaximized()

    # 添加VDS升级窗口显示函数
    def show_vds_upgrade_widget(self):
        logger.info(f"show_vds_upgrade_widget")
        from view.VdsUpgradePage import SoftwareUpgradePage
        self.vds_upgrade_window = SoftwareUpgradePage()
        self.vds_upgrade_window.show()

    def update_canoe_update_process(self, process):
        if process == "update_start":
            self.canoe_updater_widget.setWindowTitle("升级中...")
            self.canoe_updater_widget.on_update_started()
            self.canoe_updater_widget.show()
        elif process == "update_ok":
            self.canoe_updater_widget.hide()
        elif process == "update_ng":
            self.canoe_updater_widget.setWindowTitle("升级失败！！")
            self.canoe_updater_widget.on_update_failed()

    def show_i2c_update_widget(self):
        self.i2c_update_widget = ViewI2cUpdate()
        self.i2c_update_widget.show()

    def show_optical_test_widget(self):
        logger.info(f"show_optical_test_widget")
        self.optical_test_widget = OpticalTestWidget()
        self.optical_test_widget.show()

    def show_env_test_widget(self):
        logger.info(f"show_env_test_widget")
        self.env_test_widget = EnvTestWidget()
        self.env_test_widget.showMaximized()

    def start_io_control(self):
        logger.info(f"start_io_control")
        if is_windows_platform():
            self.io_control_widget = AxisWidget()
            self.io_control_widget.show()

    def start_position_control(self):
        logger.info(f"start_position_control")
        self.position_control_dlg = PositionAdjustWidget()
        self.position_control_dlg.show()
