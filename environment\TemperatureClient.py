import logging
import traceback

import serial
from modbus_tk import defines
from modbus_tk.modbus_rtu import RtuMaster

from common.LogUtils import logger


class TemperatureClient:

    def __init__(self):
        self.port = None
        self.baudrate = None
        self.bytesize = None
        self.parity = None
        self.stopbits = None
        self.slave_id = None
        self.timeout = None

        self._is_open = False
        self._master = None

    def open(self, port, baudrate=9600, bytesize=8, parity="N", stopbits=1, slave_id=1, timeout=2.0):
        self.port = port
        self.baudrate = baudrate
        self.bytesize = bytesize
        self.parity = parity
        self.stopbits = stopbits
        self.slave_id = slave_id
        self.timeout = timeout

        try:
            if not self._is_open:
                if self._master:
                    self._master.close()
                self._master = RtuMaster(
                    serial.Serial(port=self.port, baudrate=self.baudrate, bytesize=self.bytesize,
                                  parity=self.parity, stopbits=self.stopbits)
                )
                self._master.set_timeout(self.timeout)
                self._master.open()
                self._is_open = True
                return True
        except Exception as e:
            logger.error("open exception: %s", str(e.args))
            return False

    def close(self):
        if self._is_open:
            if self._master:
                self._master.close()
                self._master = None
                self._is_open = False
        return True

    def execute(self, function_code, starting_address, quantity_of_x=0, output_value=0,
                data_format="", expected_length=-1, write_starting_address_fc23=0):
        if not self._master:
            return None

        try:
            r = self._master.execute(
                self.slave_id, function_code, starting_address, quantity_of_x,
                output_value, data_format, expected_length, write_starting_address_fc23
            )
            if r is None:
                return None
            return r
        except Exception as e:
            print(traceback.format_exc())
            logger.error("execute exception: {}".format(str(e.args)))
            return None

    def read_coil_one(self, starting_address):
        r = self.execute(defines.READ_COILS, starting_address, 1)
        if r is None:
            return None
        return r[0]

    def read_coil_many(self, starting_address, quantity_of_x):
        r = self.execute(defines.READ_COILS, starting_address, quantity_of_x)
        return r

    def write_coil_one(self, starting_address, value):
        r = self.execute(defines.WRITE_SINGLE_COIL, starting_address, 1, value)
        return r

    def write_coil_many(self, starting_address, data, data_format=""):
        r = self.execute(defines.WRITE_MULTIPLE_COILS, starting_address=starting_address,
                         output_value=data, data_format=data_format)
        return r

    def read_hr_one(self, starting_address):
        """保持寄存器单个读取"""
        r = self.execute(defines.READ_HOLDING_REGISTERS, starting_address, 1)
        if r is None:
            return None
        return r[0]

    def read_hr_many(self, starting_address, quantity_of_x):
        """保持寄存器多个读取"""
        r = self.execute(defines.READ_HOLDING_REGISTERS, starting_address, quantity_of_x)
        return r

    def write_hr_one(self, starting_address, value):
        """保持寄存器单个写入"""
        r = self.execute(defines.WRITE_SINGLE_REGISTER, starting_address, 1, value)
        return r

    def write_hr_many(self, starting_address, data, data_format=""):
        """保持寄存器多个写入"""
        r = self.execute(defines.WRITE_MULTIPLE_REGISTERS, starting_address=starting_address,
                         output_value=data, data_format=data_format)
        return r

    def is_open(self):
        return self._is_open


temperature_client = TemperatureClient()

if __name__ == '__main__':
    temperature_client.open("COM24")
    for i in range(1, 16):
        print(temperature_client.read_hr_one(2 * i - 1))

