# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file './ROI.ui'
#
# Created by: PyQt5 UI code generator 5.15.4
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtWidgets

from view.RoiLabel import RoiLabel


class Ui_Dialog(object):
    def setupUi(self, Dialog):
        Dialog.setObjectName("Dialog")
        Dialog.resize(400, 300)
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(Dialog)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.labelRoi = RoiLabel(Dialog)
        self.labelRoi.setText("")
        self.labelRoi.setObjectName("labelRoi")
        self.verticalLayout_2.addWidget(self.labelRoi)
        # self.verticalLayout_2.addWidget(self.label)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.refresh_button = QtWidgets.QPushButton(Dialog)
        self.refresh_button.setObjectName("refresh_button")
        self.horizontalLayout_3.addWidget(self.refresh_button)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.enter_button = QtWidgets.QPushButton(Dialog)
        self.enter_button.setObjectName("enter_button")
        self.horizontalLayout_2.addWidget(self.enter_button)
        self.close_button = QtWidgets.QPushButton(Dialog)
        self.close_button.setObjectName("close_button")
        self.horizontalLayout_2.addWidget(self.close_button)
        self.horizontalLayout_3.addLayout(self.horizontalLayout_2)
        self.verticalLayout_2.addLayout(self.horizontalLayout_3)
        self.verticalLayout_2.setStretch(0, 7)
        self.verticalLayout_2.setStretch(1, 1)

        self.retranslateUi(Dialog)
        QtCore.QMetaObject.connectSlotsByName(Dialog)

    def retranslateUi(self, Dialog):
        _translate = QtCore.QCoreApplication.translate
        Dialog.setWindowTitle(_translate("Dialog", "Dialog"))
        self.refresh_button.setText(_translate("Dialog", "刷新"))
        self.enter_button.setText(_translate("Dialog", "确定"))
        self.close_button.setText(_translate("Dialog", "取消"))
