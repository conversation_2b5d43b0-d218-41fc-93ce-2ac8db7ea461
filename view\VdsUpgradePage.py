from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem,
                             QHeaderView, QPushButton, QApplication, QHBoxLayout,
                             QLabel, QSizePolicy)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QEvent, QObject  # 添加 QObject
from PyQt5.QtGui import QFont
import requests
import json
import sys
from common.LogUtils import logger
from utils.ProjectManager import project_manager
import os
import re
# import zipfile
import shutil
import subprocess
import threading
from common.view.MessageDialog import MessageDialog
from adb.AdbConnectDevice import adb_connect_device
from functools import partial  # 用于 upgrade_btn.clicked.connect


# WorkerSignals 类用于线程通信
class WorkerSignals(QObject):
    update_status = pyqtSignal(bool, str)
    show_message_dialog = pyqtSignal(str, str)
    operation_finished = pyqtSignal(int, bool)


# run_in_thread 装饰器
def run_in_thread(func):
    def wrapper(*args, **kwargs):
        thread = threading.Thread(target=func, args=args, kwargs=kwargs)
        thread.daemon = True
        thread.start()

    return wrapper


class SoftwareUpgradePage(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.Window)
        self.setWindowTitle("VDS升级")

        # self.extract_dir = None # 不再需要，因为不解压zip
        self.upgrade_type = None
        self._initial_layout_done = False

        # 从配置或自动检测ADB
        self.ADB_EXE_PATH = self._get_adb_path()
        if not self.ADB_EXE_PATH:
            logger.warning("未能找到adb.exe，APK安装功能可能受限。")
            # 可以在UI上提示用户配置ADB路径

        # 定义列索引常量
        self.PROGRAM_NAME_COLUMN_INDEX = 0
        self.VERSION_COLUMN_INDEX = 1
        self.PATH_COLUMN_INDEX = 2  # 现在存储基础分享链接
        self.DESC_COLUMN_INDEX = 3
        self.UPDATE_TIME_COLUMN_INDEX = 4
        self.ACTION_COLUMN_INDEX = 5

        # 信号
        self.signals = WorkerSignals()
        self.signals.update_status.connect(self.on_update_status)
        self.signals.show_message_dialog.connect(self.on_show_message_dialog)
        self.signals.operation_finished.connect(self.on_operation_finished_ui_updates)

        self.init_ui()
        self.init_status_label()
        self.resize(1600, 900)

    def _get_adb_path(self):
        # (TODO) 实现获取ADB路径的逻辑
        # 尝试从环境变量、已知SDK路径或用户配置中查找adb.exe
        # 这是一个占位符实现，您需要根据实际情况填充
        adb_exe = "adb.exe" if os.name == 'nt' else "adb"

        # 尝试从PATH环境变量查找
        path_adb = shutil.which(adb_exe)
        if path_adb:
            logger.info(f"在系统PATH中找到ADB: {path_adb}")
            return path_adb

        logger.warning("未能在PATH或标准SDK位置找到adb。请确保已安装并配置，或在程序中提供配置方式。")
        return None

    def init_ui(self):
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)

        # --- 表格创建与配置 ---
        self.table = QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels(['程序名称', '版本', '分享链接', '描述', '更新时间', '操作'])

        header_font = QFont()
        header_font.setBold(True)
        header_font.setPointSize(11)
        self.table.horizontalHeader().setFont(header_font)
        self.table.horizontalHeader().setSectionsMovable(False)
        self.table.horizontalHeader().setStretchLastSection(True)

        self.column_ratios = [0.12, 0.20, 0.25, 0.23, 0.1, 0.1]

        # 允许双击编辑，但具体哪些单元格可编辑由item flags决定
        self.table.setEditTriggers(QTableWidget.DoubleClicked | QTableWidget.SelectedClicked)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setAlternatingRowColors(True)

        self.table.verticalHeader().setSectionResizeMode(QHeaderView.ResizeToContents)
        self.table.verticalHeader().setMinimumSectionSize(45)
        self.table.verticalHeader().setDefaultSectionSize(50)
        self.table.verticalHeader().setVisible(False)

        main_layout.addWidget(self.table, 1)

        # --- 底部按钮布局 ---
        bottom_button_layout = QHBoxLayout()
        bottom_button_layout.setSpacing(10)

        self.refresh_btn = QPushButton('刷新列表')
        self.refresh_btn.setMinimumHeight(60)
        self.refresh_btn.setMinimumWidth(120)
        self.refresh_btn.setMaximumWidth(180)
        self.refresh_btn.clicked.connect(self.load_data)
        self.refresh_btn.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)

        bottom_button_layout.addStretch(1)
        bottom_button_layout.addWidget(self.refresh_btn)
        bottom_button_layout.addStretch(1)
        main_layout.addLayout(bottom_button_layout)

    def init_status_label(self):
        self.status_label = QLabel(self)
        self.status_label.setFixedSize(200, 40)
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.hide()

    def showEvent(self, event):
        super().showEvent(event)
        if not self._initial_layout_done:
            self.load_data()
            QApplication.processEvents()
            self.update_column_widths()
            self._initial_layout_done = True
        self.update_status_position()

    def resizeEvent(self, event):
        super().resizeEvent(event)
        self.update_status_position()
        if hasattr(self, 'table') and self.table.isVisible():
            self.update_column_widths()

    def update_status_position(self):
        if hasattr(self, 'status_label') and self.status_label:
            margin = 20
            x = self.width() - self.status_label.width() - margin
            y = self.height() - self.status_label.height() - margin
            self.status_label.move(x, y)
            self.status_label.raise_()

    # --- 信号对应的槽函数 ---
    def on_update_status(self, show, text):
        self.show_status(show, text)

    def on_show_message_dialog(self, title, message):
        # 确保 MessageDialog 是线程安全的，或者在这里创建它
        MessageDialog.show_auto_close_message(title, message)

    def on_operation_finished_ui_updates(self, row_index, success):
        self._enable_buttons_after_action(row_index)
        self.show_status(False)  # 确保最终隐藏状态提示
        if success:
            logger.info(f"操作成功完成 (行: {row_index})。")
        else:
            logger.warning(f"操作失败 (行: {row_index})。")

    def show_status(self, show=True, text="处理中..."):
        if not hasattr(self, 'status_label'): return
        if show:
            self.status_label.setText(text)
            self.update_status_position()
            self.status_label.show()
        else:
            self.status_label.hide()

    def load_data(self):
        self.refresh_btn.setEnabled(False)
        self.signals.update_status.emit(True, "正在加载列表...")  # 使用信号
        try:
            logger.debug("获取软件升级数据")
            url = 'http://10.1.1.131:9000/programs/versions'
            params = {'page': 1, 'pagesize': 50}
            headers = {'User-Agent': 'Mozilla/5.0',
                       'Authorization': f"Bearer {project_manager.get_access_token()}"}
            response = requests.get(url, params=params, timeout=10, headers=headers)
            response.raise_for_status()
            data = response.json()

            if data.get('err_code') != 0:
                msg = data.get('msg', '未知API错误')
                raise ValueError(f"API返回错误: {msg}")

            results = data.get('data', {}).get('results', [])
            valid_programs = [p for p in results if isinstance(p, dict) and
                              re.search(r'vds|app|os', p.get('program_name', ''), re.IGNORECASE)]

            self.table.setRowCount(0)
            self.table.setRowCount(len(valid_programs))

            for row, program_data in enumerate(valid_programs):
                # 设置哪些单元格可编辑
                name_item = QTableWidgetItem(str(program_data.get('program_name', 'N/A')))
                name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
                self.table.setItem(row, self.PROGRAM_NAME_COLUMN_INDEX, name_item)

                version_item = QTableWidgetItem(str(program_data.get('version', 'N/A')))
                self.table.setItem(row, self.VERSION_COLUMN_INDEX, version_item)

                path_item = QTableWidgetItem(str(program_data.get('path', 'N/A')))  # 现在是分享链接
                self.table.setItem(row, self.PATH_COLUMN_INDEX, path_item)

                desc_item = QTableWidgetItem(str(program_data.get('desc', 'N/A')))
                # 描述列保持可编辑 (默认flags包含ItemIsEditable)
                self.table.setItem(row, self.DESC_COLUMN_INDEX, desc_item)

                update_time_item = QTableWidgetItem(str(program_data.get('update_time', 'N/A')))
                update_time_item.setFlags(update_time_item.flags() & ~Qt.ItemIsEditable)
                self.table.setItem(row, self.UPDATE_TIME_COLUMN_INDEX, update_time_item)

                upgrade_btn = QPushButton('升级')
                upgrade_btn.setMinimumHeight(45)
                upgrade_btn.setMaximumWidth(100)
                upgrade_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
                upgrade_btn.clicked.connect(partial(self.upgrade_program, row))

                cell_widget_container = QWidget()
                cell_layout = QHBoxLayout(cell_widget_container)
                cell_layout.addWidget(upgrade_btn)
                cell_layout.setAlignment(Qt.AlignCenter)
                cell_layout.setContentsMargins(0, 0, 0, 0)
                self.table.setCellWidget(row, self.ACTION_COLUMN_INDEX, cell_widget_container)

            if not self._initial_layout_done:
                self.update_column_widths()

        except requests.exceptions.RequestException as e:
            self.signals.show_message_dialog.emit('网络错误', f'无法加载升级列表：{str(e)}')
        except json.JSONDecodeError:
            self.signals.show_message_dialog.emit('数据错误', '升级列表数据格式无效')
        except ValueError as e:
            self.signals.show_message_dialog.emit('API错误', str(e))
        except Exception as e:
            logger.error(f"获取软件升级数据时发生未知错误: {str(e)}", exc_info=True)
            self.signals.show_message_dialog.emit('未知错误', f'加载升级列表失败: {str(e)}')
        finally:
            self.signals.update_status.emit(False, "")
            self.refresh_btn.setEnabled(True)

    def upgrade_program(self, row):
        try:
            button_container = self.table.cellWidget(row, self.ACTION_COLUMN_INDEX)
            if button_container:
                upgrade_button = button_container.layout().itemAt(0).widget()
                if upgrade_button:
                    upgrade_button.setEnabled(False)
                    upgrade_button.setText("处理中")
            self.refresh_btn.setEnabled(False)
        except Exception as e:
            logger.warning(f"尝试禁用按钮时出错: {e}")

        try:
            program_name = self.table.item(row, self.PROGRAM_NAME_COLUMN_INDEX).text()
            version = self.table.item(row, self.VERSION_COLUMN_INDEX).text()
            base_share_link = self.table.item(row, self.PATH_COLUMN_INDEX).text()

            if not base_share_link or base_share_link == 'N/A' or not base_share_link.startswith("http"):
                self.signals.show_message_dialog.emit("错误", "无效的程序分享链接。")
                self.signals.operation_finished.emit(row, False)
                return

            # --- 构建新的下载链接 ---
            apk_filename_on_server = "VideoSourceApp.apk"  # 固定文件名
            if base_share_link.endswith('/'):  # 确保基础链接末尾没有斜杠
                base_share_link = base_share_link[:-1]
            actual_download_url = f"{base_share_link}/download?path=%2F&files={apk_filename_on_server}"
            logger.info(f"构建的下载链接: {actual_download_url}")

            dialog = MessageDialog('确认升级', f'确定要下载并安装 {program_name} (版本 {version}) 吗？')
            if dialog.exec_() != MessageDialog.Accepted:  # 确保 MessageDialog.Accepted 是正确的值
                self.signals.operation_finished.emit(row, False)
                return

            logger.info(f"开始下载并安装: {program_name} (版本 {version}) 从 {actual_download_url}")

            if not self.ADB_EXE_PATH:
                self.signals.show_message_dialog.emit("错误", "ADB路径未配置或无效。无法继续安装。")
                self.signals.operation_finished.emit(row, False)
                return

            # --- 本地保存的文件名和路径 ---
            # local_apk_filename = f"{program_name.replace(' ', '_')}_{version}_{apk_filename_on_server}"
            local_apk_filename = f"VideoSourceApp_{version}.apk"

            save_dir = os.path.join(os.getcwd(), "temp_apk_downloads")
            os.makedirs(save_dir, exist_ok=True)
            local_apk_save_path = os.path.join(save_dir, local_apk_filename)

            # --- 判断升级类型并执行相应操作 ---
            self.upgrade_type = None  # 重置
            if 'vds' in program_name.lower():
                self.upgrade_type = "apk_install"
                self.signals.update_status.emit(True, "准备下载APK...")
                self.download_and_execute_vds_script(actual_download_url, local_apk_save_path, row)
            elif 'os' in program_name.lower():
                self.upgrade_type = "os_upgrade"
                # TODO OS升级逻辑:
                logger.info(f"OS升级 ({program_name}) 流程未实现或待定。")
                self.signals.show_message_dialog.emit("提示", f"OS升级流程 ({program_name}) 尚未完全实现。")
                self.signals.operation_finished.emit(row, False)  # 暂时标记为失败
            else:
                self.signals.show_message_dialog.emit("错误", f"未知程序类型，无法确定升级方式: {program_name}")
                self.signals.operation_finished.emit(row, False)
                return

        except Exception as e:
            self.signals.update_status.emit(False, "")
            logger.error(f"处理升级程序 '{program_name}' 时发生错误: {str(e)}", exc_info=True)
            self.signals.show_message_dialog.emit('升级处理错误', f'升级失败：{str(e)}')
            self.signals.operation_finished.emit(row, False)

    def _enable_buttons_after_action(self, row_index):
        try:
            self.refresh_btn.setEnabled(True)
            button_container = self.table.cellWidget(row_index, self.ACTION_COLUMN_INDEX)
            if button_container:
                upgrade_button = button_container.layout().itemAt(0).widget()
                if upgrade_button:
                    upgrade_button.setEnabled(True)
                    upgrade_button.setText("升级")
        except Exception as e:
            logger.warning(f"重新启用按钮时出错: {e}")

    # --- APK 下载和安装逻辑 ---
    @run_in_thread
    def download_and_execute_vds_script(self, apk_url, local_apk_save_path, row_for_ui_update):
        success = False
        apk_downloaded_successfully = False

        try:
            # --- 1. 下载 VideoSourceApp.apk ---
            self.signals.update_status.emit(True, "下载VDS APK中...")
            response = requests.get(apk_url, stream=True, timeout=300)  # 设置下载超时
            response.raise_for_status()  # 如果HTTP请求返回错误状态码，则抛出异常
            with open(local_apk_save_path, 'wb') as f:
                total_length = response.headers.get('content-length')
                if total_length:  # 如果服务器提供文件大小，则显示下载进度
                    dl = 0
                    total_length = int(total_length)
                    for data in response.iter_content(chunk_size=8192 * 2):
                        dl += len(data)
                        f.write(data)
                        # 更新下载进度状态 (简单文本进度条)
                        self.signals.update_status.emit(True, f"下载中: {dl * 100 / total_length:.1f}%")
                else:  # 服务器未提供文件大小，直接写入
                    f.write(response.content)
            logger.info(f"VDS APK下载成功，保存至: {local_apk_save_path}")
            apk_downloaded_successfully = True

            # --- 2. 执行VDS升级脚本中的ADB命令 ---
            self.signals.update_status.emit(True, "执行VDS升级脚本...")

            # 直接构建每个ADB命令的参数列表
            commands_to_execute = [
                [self.ADB_EXE_PATH, 'root'],
                [self.ADB_EXE_PATH, 'remount'],
                [self.ADB_EXE_PATH, 'shell', 'rm', '-rf', '/system/media/video'],
                [self.ADB_EXE_PATH, 'shell', 'rm', '-rf', '/system/media/audio'],
                [self.ADB_EXE_PATH, 'shell', 'rm', '-rf', '/system/media/ic'],
                [self.ADB_EXE_PATH, 'shell', 'rm', '-rf', '/system/media/ics'],
                [self.ADB_EXE_PATH, 'shell', 'rm', '-rf', '/system/media/picture'],
                [self.ADB_EXE_PATH, 'push', local_apk_save_path, '/system/app/VideoSourceApp/'],
                [self.ADB_EXE_PATH, 'shell', 'sync'],
                [self.ADB_EXE_PATH, 'shell', 'reboot']
            ]

            for cmd_list in commands_to_execute:
                # 更新状态，显示当前执行的命令（取命令的第二个词作为代表）
                command_action = cmd_list[1]
                if command_action == "shell" and len(cmd_list) > 2:
                    command_action = f"shell {cmd_list[2]}"  # e.g., "shell rm"
                elif command_action == "push":
                    command_action = f"push {os.path.basename(cmd_list[2])}"  # e.g., "push VideoSourceApp.apk"

                logger.info(f"执行命令: {' '.join(cmd_list)}")
                self.signals.update_status.emit(True, f"执行: {command_action}...")

                result = subprocess.run(
                    cmd_list,
                    capture_output=True, text=True, shell=False,
                    timeout=120  # 为每个ADB命令设置超时 (可根据命令调整)
                )

                # 记录命令的输出
                stdout_stripped = result.stdout.strip()
                stderr_stripped = result.stderr.strip()
                if stdout_stripped: logger.debug(f"命令 STDOUT: {stdout_stripped}")
                if stderr_stripped: logger.warning(f"命令 STDERR: {stderr_stripped}")

                # 检查命令执行结果
                if result.returncode != 0:
                    # 特殊处理某些命令的非零返回码，这些不一定代表失败
                    if cmd_list[1] == "root" and (
                            "already running as root" in stdout_stripped or "restarting adbd as root" in stdout_stripped):
                        logger.info("设备已经是root或正在重启为root。")
                    elif cmd_list[1] == "remount" and ("remount succeeded" in (
                            stdout_stripped + stderr_stripped).lower() or result.returncode == 0):
                        logger.info("Remount成功。")
                    elif cmd_list[1] == "reboot":  # reboot命令发送即认为主要流程完成
                        logger.info("Reboot命令已发送。设备将重启。")
                        success = True  # 标记脚本主要部分成功
                        break  # 设备将重启，后续命令无法执行，跳出循环
                    else:  # 其他命令非零返回码视为错误
                        error_msg = stderr_stripped or stdout_stripped or f"未知错误 (命令: {command_action})"
                        raise Exception(f"命令 {' '.join(cmd_list)} 执行失败 (code {result.returncode}): {error_msg}")

            # 如果循环正常结束（即所有命令成功，或因reboot成功跳出）
            if not success and (not commands_to_execute or commands_to_execute[-1][1] != 'reboot'):
                # 如果不是因为reboot成功跳出，并且循环完成了所有命令
                success = True

            if success:  # 只有在明确标记脚本执行成功时才显示成功消息
                self.signals.show_message_dialog.emit('成功', 'VDS升级脚本执行完成，设备将重启。')


        except requests.exceptions.RequestException as e:
            logger.error(f"VDS APK下载网络错误: {str(e)}")
            self.signals.show_message_dialog.emit('下载错误', f"下载VDS APK失败: {str(e)}")
            success = False  # 确保标记为失败
        except FileNotFoundError as e:  # 通常是 self.ADB_EXE_PATH 无效
            logger.error(f"文件未找到错误 (可能是ADB路径问题): {str(e)}")
            self.signals.show_message_dialog.emit('配置错误', f"所需文件未找到: {str(e)}")
            success = False
        except subprocess.TimeoutExpired:
            current_cmd_str = ' '.join(cmd_list) if 'cmd_list' in locals() else "未知ADB命令"
            logger.error(f"ADB命令执行超时: {current_cmd_str}")
            self.signals.show_message_dialog.emit('执行超时', f"命令 '{current_cmd_str}' 执行超时。")
            success = False
        except Exception as e:  # 捕获其他所有在脚本执行或下载中发生的错误
            logger.error(f"执行VDS升级脚本时发生未知错误: {str(e)}", exc_info=True)
            self.signals.show_message_dialog.emit('未知错误', f"发生意外错误: {str(e)}")
            success = False
        finally:
            # 清理下载临时文件夹（如果已下载）
            if apk_downloaded_successfully and os.path.exists(local_apk_save_path):
                try:
                    import shutil
                    shutil.rmtree(os.path.dirname(local_apk_save_path))
                    logger.info(f"已清理下载的VDS APK: {local_apk_save_path}")
                except Exception as e_clean:
                    logger.error(f"清理VDS APK文件失败: {e_clean}")

            # 通过信号通知主线程操作完成及其状态
            self.signals.operation_finished.emit(row_for_ui_update, success)

    def update_column_widths(self):
        if not hasattr(self, 'table') or not self.table or self.table.horizontalHeader().count() == 0: return
        available_width = self.table.viewport().width()
        num_cols = self.table.columnCount()
        if available_width <= 0 or num_cols == 0 or not self.column_ratios: return
        columns_to_set_explicitly = num_cols - 1 if self.table.horizontalHeader().stretchLastSection() and num_cols > 0 else num_cols
        total_ratio_for_explicit_cols = sum(
            self.column_ratios[i] for i in range(min(columns_to_set_explicitly, len(self.column_ratios))))
        if total_ratio_for_explicit_cols <= 0: return
        for i in range(columns_to_set_explicitly):
            if i < len(self.column_ratios):
                column_width = int(available_width * self.column_ratios[i])
                self.table.setColumnWidth(i, max(column_width, 20))
        if not self.table.horizontalHeader().stretchLastSection() and num_cols > 0 and (num_cols - 1) < len(
                self.column_ratios):
            last_col_index = num_cols - 1
            column_width = int(available_width * self.column_ratios[last_col_index])
            self.table.setColumnWidth(last_col_index, max(column_width, 20))

    def customEvent(self, event: QEvent):
        if event.type() == _EnableButtonEvent.EVENT_TYPE_INT:  # 使用 EVENT_TYPE_INT
            if isinstance(event, _EnableButtonEvent):
                self.on_operation_finished_ui_updates(event.row_index, event.success)  # 直接调用UI更新槽
        else:
            super().customEvent(event)


# 自定义事件类 (确保 EVENT_TYPE_INT 定义在类中)
class _EnableButtonEvent(QEvent):
    EVENT_TYPE_INT = QEvent.User + 100  # 与 customEvent 中的比较一致

    def __init__(self, row_index, success=True):
        super().__init__(QEvent.Type(self.EVENT_TYPE_INT))
        self.row_index = row_index
        self.success = success


if __name__ == "__main__":
    app = QApplication(sys.argv)
    software_upgrade_page = SoftwareUpgradePage()
    software_upgrade_page.show()
    sys.exit(app.exec_())
