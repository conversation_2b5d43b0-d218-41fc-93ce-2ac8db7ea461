"""
视觉相关命令处理器
"""
import logging
import threading
import datetime
import os
import shutil
from typing import Dict, Any
from case.command_handlers.base_handler import BaseCommandHandler

logger = logging.getLogger(__name__)


class VisionCommandHandler(BaseCommandHandler):
    """视觉命令处理器"""
    
    def get_supported_commands(self) -> set:
        """返回支持的视觉相关命令"""
        return {
            "Mate3BeforeRotate", "Mate3AfterRotate", "StartRecord", "StopRecord",
            "StartCollectVisionBrightness", "DetectVisionBrightness"
        }
    
    def execute(self, command: str, step: Dict[str, Any]) -> None:
        """执行视觉相关命令"""
        params = self._extract_step_params(step)
        case_number = params['case_number']
        data = params['data']
        current_times = params['current_times']
        total_times = params['total_times']
        
        try:
            if command == "Mate3BeforeRotate":
                self._handle_mate3_before_rotate(case_number, command)
                
            elif command == "Mate3AfterRotate":
                self._handle_mate3_after_rotate(case_number, command)
                
            elif command == "StartRecord":
                self._handle_start_record(case_number, command)
                
            elif command == "StopRecord":
                self._handle_stop_record(case_number, command, data, current_times, total_times)
                
            elif command == "StartCollectVisionBrightness":
                self._handle_start_collect_vision_brightness(case_number, command)
                
            elif command == "DetectVisionBrightness":
                self._handle_detect_vision_brightness(case_number, command, data)
                
            else:
                logger.warning(f"未处理的视觉命令: {command}")
                self._emit_failure(case_number, command, f"未支持的命令: {command}")
                
        except Exception as e:
            logger.error(f"视觉命令执行失败 {command}: {e}")
            self._emit_failure(case_number, command, f"命令执行异常: {str(e)}")
    
    def _handle_mate3_before_rotate(self, case_number: str, command: str):
        """处理Mate3旋转前"""
        from utils.SignalsManager import signals_manager
        signals_manager.capture_image_signal.emit("before rotate")
        self._emit_success(case_number, command)
    
    def _handle_mate3_after_rotate(self, case_number: str, command: str):
        """处理Mate3旋转后"""
        from utils.SignalsManager import signals_manager
        signals_manager.set_rotate_test_param.emit(case_number, command)
        signals_manager.capture_image_signal.emit("after rotate")
    
    def _handle_start_record(self, case_number: str, command: str):
        """处理开始录制"""
        from case.CaseManager import case_manager
        from vision.CameraManager import camera_manager
        from vision.FrequencyTest import frequency_test
        from vision.CameraConfig import camera_config
        from common.AppConfig import app_config
        from utils.ProjectManager import project_manager
        from utils.SignalsManager import signals_manager
        
        if case_manager.demarcated:
            case_start_time = case_manager.get_case_start_time()
            if case_start_time:
                start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
                file_date = f"{case_number}_{start_time}"
                date = f"{datetime.datetime.now().strftime('%Y-%m-%d')}"
                base_path = os.path.join(app_config.vision_folder, project_manager.get_test_plan_project_number(), date)
                camera_config.set_resource_path(base_path)
                threading.Thread(target=frequency_test.start_record,
                               args=(camera_manager.video_list_function, file_date)).start()
                self._switch_vision_collect(True)
                self._emit_success(case_number, command)
            else:
                self._emit_failure(case_number, command, "NG: 无法获取用例开始时间")
        else:
            self._emit_failure(case_number, command, "NG: 视觉算法画面未标定")
    
    def _handle_stop_record(self, case_number: str, command: str, data: str, current_times: int, total_times: int):
        """处理停止录制"""
        from case.CaseManager import case_manager
        from vision.VisualDetectSignal import visual_detect_signal
        from vision.FrequencyTest import frequency_test
        
        # 获取视觉反转状态
        from case.StepManager import step_manager
        vision_revert = step_manager.vision_reverts.get(case_number, False)
        
        case_start_time = case_manager.get_case_start_time()
        if case_start_time:
            start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
            file_date = f"{case_number}_{start_time}"
            visual_detect_signal.function_test_clear_result.emit()
            
            if case_manager.demarcated:
                self._switch_vision_collect(False)
                params = self._parse_data_params(data, 3)
                test_mode = self._safe_int(params[0])
                threshold_flicker = self._safe_float(params[1])
                threshold_grainy = self._safe_float(params[2])
                
                threshold_black = 10
                if len(params) > 3:
                    threshold_black = self._safe_float(params[3])
                
                self._functionality_stop_record(file_date, current_times, total_times, threshold_flicker,
                                              threshold_grainy, threshold_black, test_mode)
                
                result_dict = frequency_test.get_functionality_result()
                logger.info(f"视觉检测结果: {result_dict}")
                
                if result_dict is None:
                    self._emit_success(case_number, command)
                    return
                
                if len(result_dict) == 0:
                    self._emit_failure(case_number, command, "视觉检测异常：未检测到结果")
                    return
                
                result = self._detect_functionality_result(vision_revert, result_dict)
                if result[0]:
                    self._emit_success(case_number, command)
                else:
                    self._emit_failure(case_number, command, f"视觉检测异常：{result[1]}")
                
                # 更新检测值
                from utils.SignalsManager import signals_manager
                signals_manager.update_grainy_screen_detect_value.emit(result[2])
                signals_manager.update_flicker_screen_detect_value.emit(result[3])
                signals_manager.update_black_screen_detect_value.emit(result[4])
                
                # 清理临时文件
                if frequency_test.current_files_path is not None:
                    shutil.rmtree(frequency_test.current_files_path)
                    logger.info(f"删除临时文件: {frequency_test.current_files_path}")
            else:
                self._emit_failure(case_number, command, "NG: 视觉算法画面未标定")
        else:
            self._emit_failure(case_number, command, "NG: 无法获取用例开始时间")
    
    def _handle_start_collect_vision_brightness(self, case_number: str, command: str):
        """处理开始采集视觉亮度"""
        from vision.VisualDetectSignal import visual_detect_signal
        visual_detect_signal.start_brightness_test.emit()
    
    def _handle_detect_vision_brightness(self, case_number: str, command: str, data: str):
        """处理检测视觉亮度"""
        from vision.FrequencyTest import frequency_test
        
        if "," in data:
            params = self._parse_data_params(data, 2)
            min_brightness = self._safe_int(params[0])
            max_brightness = self._safe_int(params[1])
            detect_brightness = frequency_test.get_black_screen_value()
            
            if min_brightness <= detect_brightness <= max_brightness:
                self._emit_success(case_number, command, str(detect_brightness))
            else:
                self._emit_failure(case_number, command, str(detect_brightness))
        else:
            self._emit_failure(case_number, command, "参数格式错误")
    
    @staticmethod
    def _switch_vision_collect(status: bool):
        """切换视觉采集状态"""
        from vision.VisualDetectSignal import visual_detect_signal
        logger.info(f"切换视觉采集状态: {status}")
        visual_detect_signal.start_collect.emit(status)
    
    def _functionality_stop_record(self, file_date: str, now_count: int, all_count: int, 
                                 threshold_flicker: float, threshold_grainy: float,
                                 threshold_black: float, test_mode: int):
        """功能性停止录制"""
        from vision.FrequencyTest import frequency_test
        from vision.CameraConfig import camera_config
        
        logger.info(f"功能性停止录制: file_date={file_date}, now_count={now_count}, all_count={all_count}, "
                   f"threshold_flicker={threshold_flicker}, threshold_grainy={threshold_grainy}, "
                   f"threshold_black={threshold_black}, test_mode={test_mode}")
        
        frequency_test.start_record_index = False
        frequency_test.all_count = all_count
        threading.Timer(interval=3, function=self._functionality_video_test,
                       args=(frequency_test.record_video_path, file_date, now_count, all_count, threshold_flicker,
                             threshold_grainy, threshold_black, test_mode, camera_config.get_base_path())).start()
    
    @staticmethod
    def _functionality_video_test(file_path: str, file_date: str, now_count: int, all_count: int,
                                threshold_flicker: float, threshold_grainy: float,
                                threshold_black: float, test_mode: int, base_path: str):
        """功能性视频测试"""
        from vision.VisionManager import vision_manager
        from vision.FrequencyTest import frequency_test
        
        if vision_manager.vision_calibrate_dialog is not None:
            threading.Thread(target=frequency_test.start_functionality_video_test,
                           args=([file_path, ], vision_manager.vision_calibrate_dialog, file_date, now_count,
                                 all_count, threshold_flicker, threshold_grainy, threshold_black, test_mode,
                                 base_path)).start()
    
    @staticmethod
    def _detect_functionality_result(vision_revert: bool, result_dict: dict):
        """检测功能性结果"""
        logger.info(f"检测功能性结果: vision_revert={vision_revert}, result_dict={result_dict}")
        
        if len(result_dict) == 0:
            return True, 0, [], [], []
        
        result = True
        err_desc = []
        grainy_screen_detect_value = []
        flicker_screen_detect_value = []
        black_screen_detect_value = []
        
        for key, value in result_dict.items():
            flicker_screen = value["flicker_screen"]
            grainy_screen = value["grainy_screen"]
            black_screen = value["black_screen"]
            flicker_screen_reason = value.get("flicker_screen_reason", "")
            grainy_screen_reason = value.get("grainy_screen_reason", "")
            black_screen_reason = value.get("black_screen_reason", "")
            grainy_screen_detect_value = value["grainy_screen_detect_value"]
            flicker_screen_detect_value = value["flicker_screen_detect_value"]
            black_screen_detect_value = value["black_screen_detect_value"]
            
            logger.info(f"检测结果详情: flicker_screen={flicker_screen}, grainy_screen={grainy_screen}, "
                       f"black_screen={black_screen}, grainy_screen_detect_value={grainy_screen_detect_value}, "
                       f"flicker_screen_detect_value={flicker_screen_detect_value}, "
                       f"black_screen_detect_value={black_screen_detect_value}")
            
            if vision_revert:
                if black_screen != 1:
                    result = False
                    err_desc.append("黑屏异常")
                if flicker_screen > 0:
                    result = False
                    err_desc.append("闪屏异常")
                if grainy_screen > 0:
                    result = False
                    err_desc.append("花屏异常")
            else:
                if flicker_screen > 0:
                    result = False
                    err_desc.append(f"闪屏异常【{flicker_screen_reason}】")
                if grainy_screen > 0:
                    result = False
                    err_desc.append(f"花屏异常【{grainy_screen_reason}】")
                if black_screen > 0:
                    result = False
                    err_desc.append(f"黑屏异常【{black_screen_reason}】")
        
        ret_err_desc = " & ".join(err_desc)
        return result, ret_err_desc, grainy_screen_detect_value, flicker_screen_detect_value, black_screen_detect_value
