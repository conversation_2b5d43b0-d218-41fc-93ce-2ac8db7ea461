import os

from common import is_windows_platform
from common.LogUtils import logger


class CameraConfig:

    def __init__(self):
        self.flicker_threshold = 0.1
        self.grainy_threshold = 1000.00
        if is_windows_platform():
            self.exposure = -5.0
        else:
            self.exposure = 45
        self.up_power_status = False
        self.error_stop = False
        self.adb_power_status = False
        self.base_path = ""
        self.ng_flicker_img_path = ""
        self.ng_grainy_img_path = ""
        self.ng_black_img_path = ""
        self.video_path = ""
        self.functionality_video_result = {}
        self.errorScreen_adbShell = ""
        self.errorScreenTest_blackScreen_adbShell = ""
        self.grainyScreen_adbShell = ""
        self.grainyScreenTest_blackScreen_adbShell = ""

    def set_resource_path(self, base_path):
        logger.info(f"set_resource_path base_path={base_path}")
        if not os.path.exists(base_path):
            os.makedirs(base_path)
        self.base_path = base_path
        self.ng_flicker_img_path = os.path.join(base_path, 'ng_flicker_img')
        self.ng_grainy_img_path = os.path.join(base_path, 'ng_grainy_img')
        self.ng_black_img_path = os.path.join(base_path, 'ng_black_img')
        self.video_path = os.path.join(base_path, 'monitor_video')

        if not os.path.exists(self.ng_flicker_img_path):
            os.makedirs(self.ng_flicker_img_path)

        if not os.path.exists(self.ng_grainy_img_path):
            os.makedirs(self.ng_grainy_img_path)

        if not os.path.exists(self.ng_black_img_path):
            os.makedirs(self.ng_black_img_path)

        if not os.path.exists(self.video_path):
            os.makedirs(self.video_path)

        logger.info(f"set_resource_path ng_flicker_img_path={self.ng_flicker_img_path}")
        logger.info(f"set_resource_path ng_grainy_img_path={self.ng_grainy_img_path}")
        logger.info(f"set_resource_path ng_black_img_path={self.ng_black_img_path}")

    def get_base_path(self):
        return self.base_path

    def get_ng_flicker_img_path(self):
        return self.ng_flicker_img_path

    def get_ng_grainy_img_path(self):
        return self.ng_grainy_img_path

    def get_ng_black_img_path(self):
        return self.ng_black_img_path

    def get_video_path(self):
        return self.video_path


camera_config: CameraConfig = CameraConfig()
threshold = 30
