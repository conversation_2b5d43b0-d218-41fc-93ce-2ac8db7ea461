import ctypes

from .Base import gts

if gts is not None:
    gts.GTN_Open.argtypes = [ctypes.c_short, ctypes.c_short]
    gts.GTN_Open.restype = ctypes.c_short


def GTN_Open(channel=5, param=1):
    return gts.GTN_Open(channel, param)


if gts is not None:
    gts.GTN_Close.argtypes = []
    gts.GTN_Close.restype = ctypes.c_short


def GTN_Close():
    return gts.GTN_Close()


if gts is not None:
    gts.GTN_Reset.argtypes = [ctypes.c_short]
    gts.GTN_Reset.restype = ctypes.c_short


def GTN_Reset(core):
    return gts.GTN_Reset(core)


if gts is not None:
    gts.GTN_ZeroPos.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_short]
    gts.GTN_ZeroPos.restype = ctypes.c_short


def GTN_ZeroPos(core, axis, count=1):
    return gts.GTN_ZeroPos(core, axis, count)

if gts is not None:
    gts.GTN_SynchAxisPos.argtypes = [ctypes.c_short, ctypes.c_long]
    gts.GTN_SynchAxisPos.restype = ctypes.c_short


def GTN_SynchAxisPos(core, mask):
    return gts.GTN_SynchAxisPos(core, mask)


if gts is not None:
    gts.GTN_SetAxisBand.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_long, ctypes.c_long]
    gts.GTN_SetAxisBand.restype = ctypes.c_short


def GTN_SetAxisBand(core, axis, band, time):
    return gts.GTN_SetAxisBand(core, axis, band, time)


if gts is not None:
    gts.GTN_AxisOn.argtypes = [ctypes.c_short, ctypes.c_short]
    gts.GTN_AxisOn.restype = ctypes.c_short


def GTN_AxisOn(core, axis):
    return gts.GTN_AxisOn(core, axis)

if gts is not None:
    gts.GTN_AxisOff.argtypes = [ctypes.c_short, ctypes.c_short]
    gts.GTN_AxisOff.restype = ctypes.c_short


def GTN_AxisOff(core, axis):
    return gts.GTN_AxisOff(core, axis)
