import struct
import time
import traceback

import serial
from modbus_tk import defines
from modbus_tk.modbus_rtu import RtuMaster

from common.LogUtils import logger


class TMMotorClient:
    def __init__(self):
        self.port = None
        self.baudrate = None
        self.bytesize = None
        self.parity = None
        self.stopbits = None
        self.slave_id = None
        self.timeout = None
        self._is_open = False
        self._master = None

    def is_open(self):
        return self._is_open

    def open(self, port, baudrate=9600, bytesize=8, parity="N", stopbits=1, slave_id=1, timeout=3.0):
        self.port = port
        self.baudrate = baudrate
        self.bytesize = bytesize
        self.parity = parity
        self.stopbits = stopbits
        self.slave_id = slave_id
        self.timeout = timeout

        if not self._is_open:
            try:
                if self._master:
                    self._master.close()
                self._master = RtuMaster(
                    serial.Serial(port=self.port, baudrate=self.baudrate, bytesize=self.bytesize,
                                  parity=self.parity, stopbits=self.stopbits)
                )
                self._master.set_timeout(self.timeout)
                self._master.open()
                self._is_open = True
            except Exception as e:
                logger.error(f"open exception: {str(e.args)}")
                print("open exception\n", traceback.format_exc())
                self._is_open = False
        return self._is_open

    def close(self):
        if self._master:
            try:
                self._master.close()
            except Exception as e:
                logger.error(f"close exception: {str(e.args)}")

        self._master = None
        self._is_open = False

        return True

    def execute(self, function_code, starting_address, quantity_of_x=0, output_value=0,
                data_format="", expected_length=-1, write_starting_address_fc23=0):
        if self._master is None:
            return None
        r = self._master.execute(
            self.slave_id, function_code, starting_address, quantity_of_x,
            output_value, data_format, expected_length, write_starting_address_fc23
        )
        return r

    def read_hr_one(self, starting_address):
        """保持寄存器单个读取"""
        r = self.execute(defines.READ_HOLDING_REGISTERS, starting_address, 1)
        if r is None:
            return None
        return r[0]

    def read_hr_many(self, starting_address, quantity_of_x):
        """保持寄存器多个读取"""
        r = self.execute(defines.READ_HOLDING_REGISTERS, starting_address, quantity_of_x)
        return r

    def write_hr_one(self, starting_address, value):
        """保持寄存器单个写入"""
        r = self.execute(defines.WRITE_SINGLE_REGISTER, starting_address, 1, value)
        return r

    def write_hr_many(self, starting_address, data, data_format=""):
        """保持寄存器多个写入"""
        r = self.execute(defines.WRITE_MULTIPLE_REGISTERS, starting_address=starting_address,
                         output_value=data, data_format=data_format)
        return r

    def get_position(self):
        l, h = self.execute(defines.READ_HOLDING_REGISTERS, 0x5000, 2)
        v = (h << 16) | l
        v = struct.unpack('i', struct.pack('I', v))[0]
        v = v / 5000.00
        return v

    def get_speed(self):
        l, h = self.execute(defines.READ_HOLDING_REGISTERS, 0x5002, 2)
        v = (h << 16) | l
        v = struct.unpack('i', struct.pack('I', v))[0]
        v = v / 5000.00
        return v

    def get_status(self):
        return self.read_hr_one(0x6041)

    def clear_error(self):
        self.write_hr_one(0x6040, 0x80)

    def move_to(self, position, speed=100, acc=2000, dec=2000):
        self.clear_error()

        # 设置模式
        self.write_hr_one(0x6060, 0x1)

        # 使能
        self.write_hr_one(0x6040, 0x006)
        self.write_hr_one(0x6040, 0x00F)

        # 设置位置
        position = int(position * 5000.00)
        v = struct.unpack('I', struct.pack('i', position))[0]
        h = (v >> 16) & 0xFFFF
        l = v & 0xFFFF
        self.write_hr_one(0x6101, l)
        self.write_hr_one(0x607A, h)

        # 设置速度
        speed = int(speed * 5000.00)
        h = (speed >> 16) & 0xFFFF
        l = speed & 0xFFFF
        self.write_hr_one(0x6102, l)
        self.write_hr_one(0x6081, h)

        # 加速度
        acc = int(acc * 5000.00)
        h = (acc >> 16) & 0xFFFF
        l = acc & 0xFFFF
        self.write_hr_one(0x6105, l)
        self.write_hr_one(0x6083, h)

        # 减速度
        dec = int(dec * 5000.00)
        h = (dec >> 16) & 0xFFFF
        l = dec & 0xFFFF
        self.write_hr_one(0x6106, l)
        self.write_hr_one(0x6084, h)

        # 开始
        self.write_hr_one(0x6040, 0x01F)

        # # 获取运动状态
        # r = self.read_hr_one(0x6041)
        #
        # # 结束
        # self.write_hr_one(0x6040, 0x10F)

    # 伸出
    def extend(self):
        self.move_to(-9)

    # 缩回
    def retract(self):
        self.move_to(0)

    def click(self, start, end, t=0.5):
        # print("start click")
        tm_motor_client.move_to(start)
        time.sleep(t)
        tm_motor_client.move_to(end)
        # print("end click")


tm_motor_client: TMMotorClient = TMMotorClient()

if __name__ == '__main__':
    tm_motor_client.open(port="COM9")

    tm_motor_client.move_to(0)
    for i in range(100):
        print(f"mun {i}")
        tm_motor_client.click(-9, 0,0.5)
        tm_motor_client.move_to(0,)

    # tm_motor_client.move_to(-9)

    # r = tm_motor_client.get_status()
    # print(r)
