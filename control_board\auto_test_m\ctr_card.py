import os
import time
import queue
import threading
import traceback

from PyQt5.QtCore import QObject, pyqtSignal

from .. import gg_gen as gen
from .axis import Axis
from .io import IO

from ..common.log import get_logger
from .constants import *

logger = get_logger("auto_test_m")

CORE = 1


class WorkCmd:
    def __init__(self, name):
        self.name = name


class CtrCard(QObject):
    axis_status_changed_signal = pyqtSignal(int, int)
    axis_prf_pos_changed_signal = pyqtSignal(int, float)
    axis_enc_pos_changed_signal = pyqtSignal(int, float)
    axis_e_enc_pos_changed_signal = pyqtSignal(int, int)

    def __init__(self, axis_count, io_count, parent=None):
        super().__init__(parent=parent)

        self.axis_count = axis_count
        self.io_count = io_count

        self.axis_d = {}
        for i in range(self.axis_count):
            index = i + 1
            self.axis_d[index] = Axis(CORE, index)
            self.axis_d[index].status_changed_signal.connect(self.axis_status_changed_signal)
            self.axis_d[index].axis_prf_pos_changed_signal.connect(self.axis_prf_pos_changed_signal)
            self.axis_d[index].axis_enc_pos_changed_signal.connect(self.axis_enc_pos_changed_signal)
            self.axis_d[index].axis_e_enc_pos_changed_signal.connect(self.axis_e_enc_pos_changed_signal)

        self.xy = 1

        self.io = IO(io_count)

        self._is_init = False
        self._status = True
        self._error_msg = ""

        self.home_result = 0
        self.m_axis_trap_move_result = 0
        self.crd_move_result = 0

        self.work_queue = queue.Queue()

        self.work_thread = threading.Thread(target=self._work, daemon=True)

    def setup_coordinate_system(self):
        # xy
        profile_list = [0, 0, 0, 0, 0, 0, 0, 0]
        profile_list[X - 1] = 1
        profile_list[Y - 1] = 2
        r = gen.GTN_SetCrdPrm(
            core=CORE,
            profile=self.xy,
            dimension=2,
            profile_list=profile_list,
            synVelMax=500,
            synAccMax=1,
            evenTime=50,
            setOriginFlag=1,
            originPos_list=[0] * 8
        )
        if r != 0:
            logger.error(f"GTN_SetCrdPrm({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_SetCrdPrm({r}) 失败"
        return r

    def init(self):
        if self._is_init:
            return

        r = gen.GTN_Open()
        if r != 0:
            logger.error(f"GTN_Open({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_Open({r}) 失败"
            return False

        r = gen.GTN_TerminateEcatComm(CORE)
        if r != 0:
            logger.error(f"GTN_TerminateEcatComm({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_TerminateEcatComm({r}) 失败"
            return False

        eni_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Gecat.xml")
        r = gen.GTN_InitEcatCommEx(CORE, eni_path)
        if r != 0:
            logger.error(f"GTN_InitEcatCommEx({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_InitEcatCommEx({r}) 失败"
            return False

        while True:
            r, status = gen.GTN_IsEcatReady(CORE)
            logger.info("GTN_IsEcatReady %s, %s", r, status)
            if r != 0:
                logger.error(f"GTN_IsEcatReady({r}) 失败")
                self._status = False
                self._error_msg = f"GTN_IsEcatReady({r}) 失败"
                return False
            if status == 1:
                break
            time.sleep(0.1)

        r = gen.GTN_StartEcatComm(CORE)
        if r != 0:
            logger.error(f"GTN_StartEcatComm({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_StartEcatComm({r}) 失败"
            return False

        r = gen.GTN_Reset(CORE)
        if r != 0:
            logger.error(f"GTN_Reset({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_Reset({r}) 失败"
            return False

        cfg_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "gtn_core1.cfg")
        r = gen.GTN_LoadConfig(CORE, cfg_path)
        if r != 0:
            logger.error(f"GTN_LoadConfig({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_LoadConfig({r}) 失败"
            return False

        r, mc, ioc = gen.GTN_GetEcatSlaves(CORE)
        if r != 0:
            logger.error(f"GTN_GetEcatSlaves({r}) 失败")
            self._status = False
            self._error_msg = f"GTN_GetEcatSlaves({r}) 失败"
            return False
        logger.info(f"axis count {mc}, io count: {ioc}")

        for index in self.axis_d:
            self.axis_d[index].init()

        self.io.init()
        if not self.io.status:
            logger.error(f"io初始化失败 {self.io.error_msg}")
            self._status = False
            self._error_msg = f"io初始化失败 {self.io.error_msg}"
            return False

        self.work_thread.start()

        self._is_init = True

    def stop(self):
        for i in self.axis_d:
            self.axis_d[i].stop()

    def home(self):
        self.home_result = 0

        cmd = WorkCmd("home")
        self.work_queue.put(cmd)

    def go_home(self):
        self.home()
        while self.home_result != 1:
            time.sleep(0.1)

    def _home(self):
        self.axis_d[1].home()
        self.axis_d[2].home()

        while True:
            if self.axis_d[1].homing_result == 1 and self.axis_d[2].homing_result == 1:
                break
            time.sleep(0.1)

        self.axis_d[3].home()
        self.axis_d[4].home()
        self.axis_d[5].home()

        while True:
            if (self.axis_d[5].homing_result == 1 and self.axis_d[3].homing_result == 1
                    and self.axis_d[4].homing_result == 1):
                break
            time.sleep(0.1)

        # 建立坐标系
        self.setup_coordinate_system()

        self.home_result = 1

    def axis_jog(self, index, direction, vel=0, acc=0, dec=0, smooth=0):
        self.axis_d[index].jog_move(direction, vel, acc, dec, smooth)

    def axis_home(self, index):
        self.axis_d[index].home()

    def axis_zero_pos(self, index):
        self.axis_d[index].zero_pos()

    def axis_stop(self, index):
        self.axis_d[index].stop()

    def axis_trap_move(self, index, pos, vel, acc=0.05, dec=0.05, velStart=0, smoothTime=25):
        self.axis_d[index].trap_move(pos, vel, acc, dec, velStart, smoothTime)

    def m_axis_trap_move(self, pos_d: dict, extra=None):
        """
        extra: 额外配置
        {
            1, 2, 3, 4: {
                "vel": 50, 点位运动的速度
                "acc": 0.05, 点位运动的加速度。正数，单位：pulse/ms2
                "dec": 0.05, 点位运动的减速度。正数，单位：pulse/ms2。未设置减速度时，默认减速度和加速度相同。
                "velStart": 0, 起跳速度。正数，单位：pulse/ms。默认值为 0。
                "smoothTime": 25 平滑时间。正整数，取值范围：[0, 50]，单位 ms。平滑时间的数值越大，加减速过程越平稳。
            },
            5: {
                "vel": 2,
                "acc": 0.05,
                "dec": 0.05,
                "velStart": 0,
                "smoothTime": 25
            },
        """
        self.m_axis_trap_move_result = 0

        cmd = WorkCmd("m_axis_trap_move")
        cmd.pos_d = pos_d
        cmd.extra = extra
        self.work_queue.put(cmd)

    def _m_axis_trap_move(self, pos_d: dict, extra=None):
        if 2 in pos_d:
            vel = 50
            acc = 0.05
            dec = 0.05
            velStart = 0
            smoothTime = 25
            if isinstance(extra, dict):
                conf = extra.get(2, {})
                vel = conf.get("vel", vel)
                acc = conf.get("acc", acc)
                dec = conf.get("dec", dec)
                velStart = conf.get("velStart", velStart)
                smoothTime = conf.get("smoothTime", smoothTime)
            self.axis_d[2].trap_move(0, vel, acc, dec, velStart, smoothTime)
        while True:
            if self.axis_d[2].trap_move_result == 1:
                break
            time.sleep(0.1)

        for index in pos_d:
            if index == 2:
                continue
            elif index == 5:
                vel = 2
                acc = 0.05
                dec = 0.05
                velStart = 0
                smoothTime = 25
                if isinstance(extra, dict):
                    conf = extra.get(index, {})
                    vel = conf.get("vel", vel)
                    acc = conf.get("acc", acc)
                    dec = conf.get("dec", dec)
                    velStart = conf.get("velStart", velStart)
                    smoothTime = conf.get("smoothTime", smoothTime)
                self.axis_d[index].trap_move(pos_d[index], vel, acc, dec, velStart, smoothTime)
            else:
                vel = 50
                acc = 0.05
                dec = 0.05
                velStart = 0
                smoothTime = 25
                if isinstance(extra, dict):
                    conf = extra.get(index, {})
                    vel = conf.get("vel", vel)
                    acc = conf.get("acc", acc)
                    dec = conf.get("dec", dec)
                    velStart = conf.get("velStart", velStart)
                    smoothTime = conf.get("smoothTime", smoothTime)
                self.axis_d[index].trap_move(pos_d[index], vel, acc, dec, velStart, smoothTime)
        while True:
            f = True
            for i in self.axis_d:
                if i == 2:
                    continue
                if not self.axis_d[i].trap_move_result == 1:
                    f = False
            if f:
                break
            time.sleep(0.1)

        if 2 in pos_d:
            vel = 50
            acc = 0.05
            dec = 0.05
            velStart = 0
            smoothTime = 25
            if isinstance(extra, dict):
                conf = extra.get(2, {})
                vel = conf.get("vel", vel)
                acc = conf.get("acc", acc)
                dec = conf.get("dec", dec)
                velStart = conf.get("velStart", velStart)
                smoothTime = conf.get("smoothTime", smoothTime)
            self.axis_d[2].trap_move(pos_d[2], vel, acc, dec, velStart, smoothTime)
        while True:
            if self.axis_d[2].trap_move_result == 1:
                break
            time.sleep(0.1)

        self.m_axis_trap_move_result = 1

    def crd_move(self, pos_list, extra=None):
        """
        extra: 额外配置
        {
            "synVel": 100, 插补段的目标合成速度。取值范围：(0, 32767)，单位：pulse/ms
            "synAcc": 0.1, 插补段的合成加速度。取值范围：(0, 32767)，单位：pulse/ms2
            "velEnd": 0 插补段的终点速度。取值范围：[0, 32767)，单位：pulse/ms。该值只有在没有使
                        用前瞻预处理功能时才有意义，否则该值无效。默认值为：0
        }
        """
        self.crd_move_result = 0

        cmd = WorkCmd("crd_move")
        cmd.pos_list = pos_list
        cmd.extra = extra
        self.work_queue.put(cmd)

    def _crd_move(self, pos_list, extra=None):
        r = gen.GTN_CrdClear(CORE, self.xy, 0)
        if r != 0:
            print(f"GTN_CrdClear({r}) 失败")
            return

        synVel = 100
        synAcc = 0.1
        velEnd = 0
        if isinstance(extra, dict):
            synVel = extra.get("synVel", synVel)
            synAcc = extra.get("synAcc", synAcc)
            velEnd = extra.get("velEnd", velEnd)

        for pos in pos_list:
            r = gen.GTN_LnXY(CORE, self.xy, pos[0], pos[1], synVel, synAcc, velEnd)
            if r != 0:
                print(f"GTN_LnXY({r}) 失败")
                return

        r = gen.GTN_CrdStart(CORE, 1 << (self.xy - 1), 0)
        if r != 0:
            print(f"GTN_CrdStart({r}) 失败")
            return

        while True:
            r, run, seg = gen.GTN_CrdStatus(CORE, self.xy)
            # print('r, run, seg', r, run, seg)
            if r != 0:
                print(f"GTN_CrdStatus({r}) 失败")
                return
            if run == 0:
                break
            time.sleep(0.1)

        self.crd_move_result = 1

    def _work(self):
        while True:
            try:
                cmd = self.work_queue.get()

                if cmd.name == "home":
                    self._home()

                if cmd.name == "m_axis_trap_move":
                    self._m_axis_trap_move(cmd.pos_d, cmd.extra)

                if cmd.name == "crd_move":
                    self._crd_move(cmd.pos_list, cmd.extra)

            except Exception:
                print(traceback.format_exc())

    def __del__(self):
        r = gen.GTN_TerminateEcatComm(1)
        if r != 0:
            print(f"GTN_TerminateEcatComm({r}) 失败")

        r = gen.GTN_Close()
        if r != 0:
            print(f"GTN_Close({r}) 失败")


ctr_card = CtrCard(axis_count=5, io_count=2)
