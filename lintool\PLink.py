import json
import json
import os
import sys
import threading
import traceback

from PyQt5 import QtWidgets, QtCore
from PyQt5.QtCore import Qt, QSize, pyqtSignal, QEvent, QRect
from PyQt5.QtGui import QIcon, QPixmap, QCursor
from PyQt5.QtWidgets import QApplication, QPushButton, QLabel, QAbstractItemView, QTableWidgetItem, QDialog, \
    QListWidget, QComboBox, QLineEdit, QMessageBox, QVBoxLayout, QHBoxLayout, QGroupBox, QFormLayout, \
    QDesktopWidget, QMenu, QWidget, QToolTip, QHeaderView, QCheckBox

from common.LogUtils import logger
from PlinView import PLinApiView

if not os.path.exists("./new_frame/"):
    os.makedirs("./new_frame/")
if os.path.exists('new_frame/frame.txt'):
    os.remove('new_frame/frame.txt')

if not os.path.exists('./log/'):
    os.makedirs("./log/")

if not os.path.exists('./images/'):
    os.makedirs("./images/")


class SI:
    line = 0
    receive_count = 0
    transmit_count = 0
    choice_model = "Slave"
    choice_bit = 19200
    length = 2
    comment = ''
    id_text = ''
    pid_text = ''
    checksum = 'Classic'
    direction = 'Disabled'
    row = 0
    data = ''
    count = 0
    edit_line = 0
    period = 20


class MyTableWidget(QtWidgets.QTableWidget):
    """
    悬浮提示事件
    """
    update_table_tooltip_signal = pyqtSignal(object)

    def __init__(self):
        super(MyTableWidget, self).__init__()
        self.tool_tip = ''
        self.init_row = 0
        self.init_col = 0
        self.mouse_x = 0
        self.mouse_y = 0
        self.title_row_height = 40

        self.ini_table()

    def ini_table(self):
        """
        执行信号绑定
        """
        self.update_table_tooltip_signal.connect(self.update_table_tooltip_slot)

    def install_eventFilter(self):
        """
        为TableWidget安装事件过滤器
        """
        self.installEventFilter(self)
        self.setMouseTracking(True)

    # 通过计算坐标确定当前位置所属单元格
    def update_table_tooltip_slot(self, posit):
        try:
            self.tool_tip = ""
            self.mouse_x = posit.x()
            self.mouse_y = posit.y()
            self.row_height = self.title_row_height  # 累计行高,初始值为列标题行高
            for r in range(self.rowCount()):
                current_row_height = self.rowHeight(r)
                self.col_width = 0  # 累计列宽
                print(self.row_height, self.mouse_y, self.row_height + current_row_height)
                if self.row_height <= self.mouse_y <= self.row_height + current_row_height:
                    for c in range(self.columnCount()):
                        current_col_width = self.columnWidth(c)
                        if self.col_width <= self.mouse_x <= self.col_width + current_col_width:
                            r += self.init_row
                            c += self.init_col
                            print("鼠标当前所在的行和列为:({},{})".format(r, c))
                            item = self.item(r, c)
                            if item is not None:
                                self.tool_tip = item.text()
                            else:
                                self.tool_tip = ""
                            return self.tool_tip
                        else:
                            self.col_width += current_col_width
                else:
                    if self.mouse_y < self.row_height:
                        break
                    else:
                        self.row_height += current_row_height
        except Exception as e:
            print(e, traceback.format_exc())

    def eventFilter(self, obj, event):
        """
        # 事件过滤器
        """
        try:
            if event.type() == QEvent.ToolTip:
                self.setCursor(Qt.ArrowCursor)
                print("当前鼠标位置为:", event.pos())
                self.update_table_tooltip_signal.emit(event.pos())
                # 设置提示气泡显示范围矩形框,当鼠标离开该区域则ToolTip消失
                rect = QRect(self.mouse_x, self.mouse_y, 30, 10)
                QApplication.processEvents()
                # 在指定位置展示ToolTip
                QToolTip.showText(QCursor.pos(), self.tool_tip, self, rect, 1500)

                """
                showText(QPoint, str, QWidget, QRect, int)
                #############参数详解###########
                #QPoint指定tooptip显示的绝对坐标,QCursor.pos()返回当前鼠标所在位置
                #str为设定的tooptip
                #QWidget为要展示tooltip的控件
                #QRect指定tooltip显示的矩形框范围,当鼠标移出该范围,tooltip隐藏,使用该参数必须指定Qwidget!
                #int用于指定tooltip显示的时长(毫秒)
                """
            return QWidget.eventFilter(self, obj, event)
        except Exception as e:
            print(e)
            traceback.print_exc()


class Data:
    """
    data confing
    """
    data1 = None
    data2 = None
    data3 = None
    data4 = None
    data5 = None
    data6 = None
    data7 = None
    data8 = None
    data_list = []
    data_text_dict = {
        "data1": "",
        "data2": "",
        "data3": "",
        "data4": "",
        "data5": "",
        "data6": "",
        "data7": "",
        "data8": ""
    }


class ReadMessage:
    """
    读取的plink值
    """
    data = {}


class BaseWindows(QtWidgets.QMainWindow):
    """
    QtWidgets 基类
    """
    _startPos = None
    _endPos = None
    _isTracking = False
    rightClicked = pyqtSignal()

    def __init__(self):
        self.base_widget = None
        self.base_layout = None
        self.main_widget = None
        self.effect_shadow = None
        super(BaseWindows, self).__init__()
        self.setup_ui()
        self.add_shadow()

    def setup_ui(self):
        """
        初始化
        """
        self.setAttribute(Qt.WA_TranslucentBackground)

        self.base_widget = QtWidgets.QWidget()
        self.base_widget.setObjectName('base_widget')
        self.base_layout = QtWidgets.QGridLayout()
        self.base_widget.setLayout(self.base_layout)
        self.base_widget.setAttribute(Qt.WA_TranslucentBackground)

        self.main_widget = QtWidgets.QWidget()
        self.base_layout.addWidget(self.main_widget)
        self.setCentralWidget(self.base_widget)  # 设置窗口主部件
        # 获取屏幕的尺寸信息
        screen = QDesktopWidget().screenGeometry()
        # print(screen)
        # 获取窗口的尺寸信息
        # size = self.geometry()
        # print(size)
        # 将窗口移动到指定位置在屏幕中间
        self.move((screen.width() - 1800) / 2, (screen.height() - 750) / 2)

    def add_shadow(self):
        # 添加阴影
        self.effect_shadow = QtWidgets.QGraphicsDropShadowEffect(self)
        self.effect_shadow.setOffset(2, 0)  # 偏移
        self.effect_shadow.setBlurRadius(10)  # 阴影半径
        self.effect_shadow.setColor(Qt.gray)  # 阴影颜色
        self.main_widget.setGraphicsEffect(self.effect_shadow)

    def new_button(self, title=None):
        # style = "*{border-radius: 4px;background-color: #c5d5e8; color: #333; font-size:24px}"

        button = QPushButton(title)
        button.setGraphicsEffect(self.effect_shadow)
        return button


class PlinkView(BaseWindows):
    trigger = pyqtSignal()
    trigger_count = pyqtSignal(int, int)

    def __init__(self):
        self.app = QApplication(sys.argv)
        # self.app.setStyle(QStyleFactory.create("Windows"))
        super(PlinkView, self).__init__()
        self.api = PLinApiView()
        self.window_title = "Plink-View"
        self.start_button = None
        self.pause_button = None
        self.stop_button = None
        self.con_button = None
        self.add_button = None
        self.send_btn = None
        self.check_btn = None
        self.hex_combo = None
        self.qlist = None
        self.combo = None
        self.rec = None
        self.btn = None
        self.length = None
        self.new_dialog = None
        self.id_input = None
        self.pid_input = None
        self.checksum = None
        self.direction = None
        self.new_frame_btn = None
        self.bit_rate_combo = None
        self.connect_dialog = None
        self.comment_input = None
        self.Model_Combo = None
        self.period_input = None
        self.disconnect_button = None
        self.receive_tableWidget = None
        self.transmit_tableWidget = None
        self.msg = None
        self.data = None
        self.start_status = False
        self.line = 0
        self.vbox = QVBoxLayout()  # 纵向布局
        self.hbox = QHBoxLayout()  # 横向布局
        self.connect_vbox = QVBoxLayout()
        self.connect_hbox = QHBoxLayout()
        self.window_vbox = QVBoxLayout()
        self.window_hbox = QHBoxLayout()
        self.groupBox = QGroupBox('Frame Definition')
        self.vbox.setContentsMargins(20, 20, 20, 20)
        self.timer = None
        self.connect_status = False

        # 信号关联
        self.trigger.connect(self.update_ui_label)
        self.trigger_count.connect(self.update_count)
        self.rightClicked.connect(lambda: print("按钮被右键单击了"))
        self.ui_alive = True
        self.edit = False
        self.check = False
        self.cyclic_scheduling = False
        self.row_num = 0
        self.init_ui()

    def init_ui(self):
        self.window_vbox = QVBoxLayout()
        self.window_hbox = QHBoxLayout()
        logger.info("start")
        self.setWindowTitle("Plink-View")
        self.setWindowIcon(
            QIcon(os.path.join(os.getcwd(), './images/Plink-View.ico')))
        self.setFixedSize(QSize(1800, 750))
        self.connect_button()
        self.disconnect()
        self.new_frame()
        self.send_button()
        self.receive_label()
        self.transmit_label()
        self.check_button()
        self.start()
        self.pause()
        self.stop()

        receive_label = QLabel(self)
        pix = QPixmap("./images/receive.ico")
        receive_label.setPixmap(pix)
        receive_hbox = QHBoxLayout()
        receive_hbox.addWidget(receive_label)
        receive_hbox.addWidget(self.receive_tableWidget)

        transmit_label = QLabel(self)
        pix = QPixmap("./images/Transmit.ico")
        transmit_label.setPixmap(pix)
        transmit_hbox = QHBoxLayout()
        transmit_hbox.addWidget(transmit_label)
        transmit_hbox.addWidget(self.transmit_tableWidget)

        self.window_hbox.addWidget(self.con_button, 1, Qt.AlignTop)
        self.con_button.setFixedHeight(30)
        self.window_hbox.addWidget(self.disconnect_button, 1, Qt.AlignTop)
        self.disconnect_button.setFixedHeight(30)
        self.window_hbox.addWidget(self.add_button, 1, Qt.AlignTop)
        self.add_button.setFixedHeight(30)
        self.window_hbox.addWidget(self.send_btn, 1, Qt.AlignTop)
        self.send_btn.setFixedHeight(30)
        self.window_hbox.addWidget(self.check_btn, 1, Qt.AlignTop)
        self.check_btn.setFixedHeight(30)

        self.window_hbox.addWidget(self.start_button, 1, Qt.AlignTop)
        self.start_button.setFixedHeight(30)
        self.window_hbox.addWidget(self.pause_button, 1, Qt.AlignTop)
        self.pause_button.setFixedHeight(30)
        self.window_hbox.addWidget(self.stop_button, 1, Qt.AlignTop)
        self.stop_button.setFixedHeight(30)

        self.window_vbox.addLayout(self.window_hbox)
        self.window_vbox.addLayout(receive_hbox)
        self.window_vbox.addLayout(transmit_hbox)

        self.con_button.clicked.connect(self.show_dialog)
        self.disconnect_button.clicked.connect(self.disconnect_to)
        self.add_button.clicked.connect(self.new_frame_dialog)
        self.send_btn.clicked.connect(self.send_message)
        self.check_btn.clicked.connect(self.check_value)
        self.start_button.clicked.connect(self.start_send)
        self.pause_button.clicked.connect(self.stop_send)
        self.stop_button.clicked.connect(self.stop_send)
        self.main_widget.setLayout(self.window_vbox)
        self.show()
        return self.app.exec_()

    def check_value(self):
        if self.check:
            logger.info("CRC 校验关闭")
            self.check = False
            QMessageBox.about(self, 'check', "校验关闭")
        else:
            logger.info("CRC 校验开启")
            self.check = True
            QMessageBox.about(self, 'check', "校验开启")

    def receive_label(self):
        self.receive_tableWidget, _translate, item = self.table_widget(10)

        item = self.receive_tableWidget.horizontalHeaderItem(7)
        item.setText(_translate("MainWindow", "Period"))
        item = self.receive_tableWidget.horizontalHeaderItem(8)
        item.setText(_translate("MainWindow", "Checksum"))
        item = self.receive_tableWidget.horizontalHeaderItem(9)
        item.setText(_translate("MainWindow", "Errors"))
        self.receive_tableWidget.setColumnWidth(0, 120)
        self.receive_tableWidget.setColumnWidth(1, 170)
        self.receive_tableWidget.setColumnWidth(2, 120)
        self.receive_tableWidget.setColumnWidth(3, 500)
        self.receive_tableWidget.setColumnWidth(4, 120)
        self.receive_tableWidget.setColumnWidth(5, 120)
        self.receive_tableWidget.setColumnWidth(6, 120)
        self.receive_tableWidget.setColumnWidth(7, 120)
        self.receive_tableWidget.setColumnWidth(8, 120)
        self.receive_tableWidget.setColumnWidth(9, 200)
        self.receive_tableWidget.setContextMenuPolicy(Qt.CustomContextMenu)  # 允许右键产生子菜单
        self.receive_tableWidget.customContextMenuRequested.connect(self.receive_generate_menu)  # 右键菜单

    def transmit_label(self):
        self.transmit_tableWidget, _translate, item = self.table_widget(10)

        item = self.transmit_tableWidget.horizontalHeaderItem(7)
        item.setText(_translate("MainWindow", "Errors"))
        item = self.transmit_tableWidget.horizontalHeaderItem(8)
        item.setText(_translate("MainWindow", "Trigger"))
        item = self.transmit_tableWidget.horizontalHeaderItem(9)
        item.setText(_translate("MainWindow", "Comment"))
        self.transmit_tableWidget.setColumnWidth(0, 120)
        self.transmit_tableWidget.setColumnWidth(1, 170)
        self.transmit_tableWidget.setColumnWidth(2, 120)
        self.transmit_tableWidget.setColumnWidth(3, 500)
        self.transmit_tableWidget.setColumnWidth(4, 120)
        self.transmit_tableWidget.setColumnWidth(5, 120)
        self.transmit_tableWidget.setColumnWidth(6, 120)
        self.transmit_tableWidget.setColumnWidth(7, 200)
        self.transmit_tableWidget.setColumnWidth(8, 120)
        self.transmit_tableWidget.setColumnWidth(9, 120)
        self.transmit_tableWidget.cellPressed.connect(self.get_pos_content)
        self.transmit_tableWidget.setContextMenuPolicy(Qt.CustomContextMenu)  # 允许右键产生子菜单
        self.transmit_tableWidget.customContextMenuRequested.connect(self.transmit_generate_menu)  # 右键菜单

    @staticmethod
    def table_widget(count):
        _translate = QtCore.QCoreApplication.translate
        tableWidget = MyTableWidget()
        tableWidget.install_eventFilter()
        # tableWidget = QtWidgets.QTableWidget()

        tableWidget.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)  # 列宽手动调节

        tableWidget.setEditTriggers(QAbstractItemView.NoEditTriggers)  # 不可编辑
        # tableWidget.move(right, top)
        tableWidget.setShowGrid(True)
        tableWidget.setObjectName("tableWidget")

        tableWidget.setColumnCount(count)

        tableWidget.setRowCount(SI.line)
        tableWidget.setFocusPolicy(Qt.NoFocus)
        tableWidget.setSelectionBehavior(QAbstractItemView.SelectRows)
        tableWidget.horizontalHeader().setVisible(True)
        tableWidget.horizontalHeader().setCascadingSectionResizes(False)
        for i in range(10):
            item = QtWidgets.QTableWidgetItem()
            item.setTextAlignment(QtCore.Qt.AlignCenter)
            tableWidget.setHorizontalHeaderItem(i, item)
        title_list = ["ID", "Symbol", "Length", "Data", "Count", "Direction", "CST"]
        for i in range(len(title_list)):
            item = tableWidget.horizontalHeaderItem(i)
            item.setText(_translate("MainWindow", title_list[i]))
        return tableWidget, _translate, item

    def connect_button(self):
        self.con_button = self.new_button('创建连接')

    def get_pos_content(self, row, col):
        try:
            SI.edit_line = row + 1
            if self.transmit_tableWidget.rowCount():
                SI.id_text = self.transmit_tableWidget.cellWidget(row, 0).findChild(
                    QLabel).text() if self.transmit_tableWidget.cellWidget(row, 0).findChild(QLabel) else ''
            SI.length = self.transmit_tableWidget.item(row, 2).text() if self.transmit_tableWidget.item(row, 2) else 2
            SI.data = self.transmit_tableWidget.item(row, 3).text() if self.transmit_tableWidget.item(row, 3) else ''
            SI.transmit_count = int(self.transmit_tableWidget.item(row, 4).text()) if \
                self.transmit_tableWidget.item(row, 4) else 0
            SI.direction = self.transmit_tableWidget.item(row, 5).text() if self.transmit_tableWidget.item(row, 5) \
                else 'Disabled'
            SI.checksum = self.transmit_tableWidget.item(row, 6).text() if self.transmit_tableWidget.item(row, 6) \
                else 'Classic'

        except Exception as e:
            logger.error(f"错误：{e}\n {traceback.format_exc()}")
            QMessageBox.warning(self, 'New frame', str(e))

    def show_dialog(self):
        """
        is Connect Dialog
        :return:
        """
        model = QLabel()
        model.setText('   Model:')

        bit_rate = QLabel()
        bit_rate.setText('Bit rate:')

        form = QFormLayout()
        self.connect_vbox = QVBoxLayout()
        self.connect_hbox = QHBoxLayout()
        # 创建QDialog对象
        try:
            self.connect_dialog = QDialog(self)

            self.connect_label()

            self.qlist = QListWidget(self.connect_dialog)
            self.qlist.setMinimumSize(400, 300)
            self.connect_vbox.addWidget(self.qlist)

            self.model_combo()
            self.bit_combo()
            self.connect_vbox.setSpacing(20)
            self.Model_Combo.setMinimumSize(30, 30)
            self.bit_rate_combo.setMinimumSize(30, 30)
            form.addRow(model, self.Model_Combo)
            form.addRow(bit_rate, self.bit_rate_combo)
            self.connect_vbox.addLayout(form, 1)

            # 创建按钮到新创建的dialog对象中
            self.btn = QPushButton('OK', self.connect_dialog)
            btn_cancel = QPushButton('Cancel', self.connect_dialog)
            btn_cancel.clicked.connect(self.connect_dialog_close)
            self.connect_hbox.addWidget(self.btn, 1, Qt.AlignBottom | Qt.AlignCenter)
            self.connect_hbox.addWidget(btn_cancel, 1, Qt.AlignBottom | Qt.AlignCenter)
            self.connect_vbox.addLayout(self.connect_hbox, 2)

            self.connect_dialog.setLayout(self.connect_vbox)

            # 移动按钮，设置dialog的标题
            self.connect_dialog.setWindowTitle("connect to...")
            # self.connect_dialog.resize(500, 400)
            item = self.api.display_available_connection()
            if item == 'No hardware found!':
                self.btn.setEnabled(False)
            self.qlist.addItem(item)

            self.btn.clicked.connect(self.connect_dialog_to)

            # 设置窗口的属性为ApplicationModal模态，用户只有关闭弹窗后，才能关闭主界面
            self.connect_dialog.setWindowModality(Qt.ApplicationModal)
            self.connect_dialog.exec_()
        except Exception as e:
            logger.error("错误： {} \n {}".format(str(e), traceback.format_exc()))
            QMessageBox.warning(self, 'CONNECT', str(e))

    def connect_dialog_close(self):
        self.connect_dialog.close()

    def connect_dialog_to(self, boolean=False):
        self.ui_alive = True
        text = self.api.connect(SI.choice_model, SI.choice_bit)
        if not boolean:
            # 启动线程定时器
            QMessageBox.about(self, 'CONNECT', text)
            self.con_button.setEnabled(False)
            self.connect_dialog_close()
        self.connect_status = True
        self.timer = threading.Timer(int(SI.period) / 1000, self.read_message)
        self.timer.start()

    def disconnect(self):
        """
        disconnect
        :return:
        """
        self.disconnect_button = self.new_button('断开连接')

    def disconnect_to(self, boolean=False):
        self.ui_alive = False
        self.start_status = False
        text = self.api.disconnect()
        self.con_button.setEnabled(True)
        self.cyclic_scheduling = False
        self.connect_status = False
        if not boolean:
            QMessageBox.about(self, 'DISCONNECT', text)

    def new_frame(self):
        self.add_button = self.new_button('创建数据')

    def send_button(self):
        self.send_btn = self.new_button('单次发送')

    def check_button(self):
        self.check_btn = self.new_button('CRC校验')

    def new_frame_dialog(self):
        """
        is New Frame Dialog
        :return:
        """
        self.vbox = QVBoxLayout()
        self.hbox = QHBoxLayout()
        self.groupBox = QGroupBox('Frame Definition')
        Data.data_list = []
        self.new_dialog = QDialog(self)
        self.new_dialog.setWindowTitle("New frame")
        self.new_frame_label()

        self.new_frame_btn = QPushButton('OK')
        self.new_frame_btn.setEnabled(False)
        new_frame_cancel = QPushButton('Cancel')

        self.hbox.addWidget(self.new_frame_btn, 2, Qt.AlignBottom | Qt.AlignCenter)
        self.hbox.addWidget(new_frame_cancel, 2, Qt.AlignBottom | Qt.AlignCenter)
        self.hbox.setContentsMargins(0, 20, 0, 0)
        self.vbox.addLayout(self.hbox, 1)
        self.new_dialog.setLayout(self.vbox)
        if not self.edit:
            SI.transmit_count = 0
            if SI.edit_line != 0:
                SI.edit_line += 1
            self.new_frame_btn.clicked.connect(self.add_frame)
        else:
            self.new_frame_btn.setEnabled(True)
            self.new_frame_btn.clicked.connect(self.update_frame)
        new_frame_cancel.clicked.connect(self.new_dialog_close)

        self.new_dialog.resize(400, 500)
        self.new_dialog.setWindowModality(Qt.ApplicationModal)
        self.new_dialog.exec_()

    def new_dialog_close(self):
        """
        dialog is close
        :return:
        """
        self.edit = False
        self.new_dialog.close()

    def update_frame(self):
        try:
            if SI.data:
                SI.data = ''
            if SI.id_text and SI.pid_text and SI.checksum and SI.direction and SI.length:
                widget_id = QWidget()
                check_box = QCheckBox()
                check_box.setCheckState(2)
                lable = QLabel()
                lable.setText(SI.id_text)
                hLayout = QHBoxLayout()
                hLayout.addWidget(check_box)
                hLayout.addWidget(lable)
                hLayout.setContentsMargins(5, 5, 5, 5)
                widget_id.setLayout(hLayout)
                self.transmit_tableWidget.setCellWidget(self.row_num, 0, widget_id)

                # newItem = QTableWidgetItem(SI.id_text)
                # self.transmit_tableWidget.setItem(self.row_num, 0, newItem)
                newItem = QTableWidgetItem(str(SI.length))
                self.transmit_tableWidget.setItem(self.row_num, 2, newItem)
                for i in Data.data_text_dict:
                    if Data.data_text_dict[i]:
                        if SI.data:
                            SI.data += ' ' + Data.data_text_dict[i]
                        else:
                            SI.data += Data.data_text_dict[i]
                newItem = QTableWidgetItem(SI.data)
                self.transmit_tableWidget.setItem(self.row_num, 3, newItem)
                newItem = QTableWidgetItem(str(SI.transmit_count))
                self.transmit_tableWidget.setItem(self.row_num, 4, newItem)
                newItem = QTableWidgetItem(SI.direction)
                self.transmit_tableWidget.setItem(self.row_num, 5, newItem)
                newItem = QTableWidgetItem(SI.checksum)
                self.transmit_tableWidget.setItem(self.row_num, 6, newItem)
                newItem = QTableWidgetItem('Manual')
                self.transmit_tableWidget.setItem(self.row_num, 8, newItem)
                newItem = QTableWidgetItem(SI.comment)
                self.transmit_tableWidget.setItem(self.row_num, 9, newItem)

                self.transmit_tableWidget.setSelectionBehavior(QAbstractItemView.SelectRows)
                self.transmit_tableWidget.setSelectionMode(QAbstractItemView.SingleSelection)

                self.write_to_json()
                self.data = self.read_frame()
        except Exception as e:
            logger.error("错误： {}\n{}".format(e, traceback.format_exc()))
            # print(e, traceback.format_exc())
        self.new_dialog_close()

    def add_frame(self):
        """
        Add a new frame
        :return:
        """
        try:
            if SI.data:
                SI.data = ''
            if SI.id_text and SI.pid_text and SI.checksum and SI.direction and SI.length:
                # 插入表格
                SI.line += 1
                row_count = self.transmit_tableWidget.rowCount()  # 返回当前行数(尾部)
                self.transmit_tableWidget.insertRow(row_count)  # 尾部插入一行

                widget_id = QWidget()
                check_box = QCheckBox()
                check_box.setCheckState(2)
                lable = QLabel()
                lable.setText(SI.id_text)
                hLayout = QHBoxLayout()
                hLayout.addWidget(check_box)
                hLayout.addWidget(lable)
                hLayout.setContentsMargins(5, 5, 5, 5)
                widget_id.setLayout(hLayout)
                self.transmit_tableWidget.setCellWidget(SI.line - 1, 0, widget_id)

                # newItem = QTableWidgetItem(SI.id_text)
                # self.transmit_tableWidget.setItem(SI.line - 1, 0, newItem)
                newItem = QTableWidgetItem(str(SI.length))
                self.transmit_tableWidget.setItem(SI.line - 1, 2, newItem)
                for i in list(Data.data_text_dict)[:int(SI.length)]:
                    if Data.data_text_dict[i]:
                        if SI.data:
                            SI.data += ' ' + Data.data_text_dict[i]
                        else:
                            SI.data += Data.data_text_dict[i]
                newItem = QTableWidgetItem(SI.data)
                self.transmit_tableWidget.setItem(SI.line - 1, 3, newItem)
                newItem = QTableWidgetItem(str(SI.transmit_count))
                self.transmit_tableWidget.setItem(SI.line - 1, 4, newItem)
                newItem = QTableWidgetItem(SI.direction)
                self.transmit_tableWidget.setItem(SI.line - 1, 5, newItem)
                newItem = QTableWidgetItem(SI.checksum)
                self.transmit_tableWidget.setItem(SI.line - 1, 6, newItem)
                newItem = QTableWidgetItem('Manual')
                self.transmit_tableWidget.setItem(SI.line - 1, 8, newItem)
                newItem = QTableWidgetItem(SI.comment)
                self.transmit_tableWidget.setItem(SI.line - 1, 9, newItem)

                self.transmit_tableWidget.setSelectionBehavior(QAbstractItemView.SelectRows)
                self.transmit_tableWidget.setSelectionMode(QAbstractItemView.SingleSelection)

                self.write_to_json()
            self.new_dialog_close()
        except Exception as e:
            logger.error(f"错误： {e}\n{traceback.format_exc()}")

    def write_to_json(self):
        if self.edit:
            with open('new_frame/frame.txt', encoding='utf-8') as f1:
                data = f1.readlines()
                if not data:
                    data.append(json.dumps({
                        SI.id_text: {
                            "data": SI.data,
                            "pid": SI.pid_text,
                            "checksum": SI.checksum,
                            "direction": SI.direction,
                            "length": SI.length,
                            "count": 0,
                            "period": SI.period,  # 默认20毫秒"
                            "checkBox": False,
                        }
                    }) + "\n")
                else:
                    data[self.row_num] = json.dumps({
                        SI.id_text: {
                            "data": SI.data,
                            "pid": SI.pid_text,
                            "checksum": SI.checksum,
                            "direction": SI.direction,
                            "length": SI.length,
                            "count": 0,
                            "period": SI.period,  # 默认20毫秒
                            "checkBox": False,
                        }
                    }) + "\n"
                logger.info("修改数据data： {}".format(data))
                with open('new_frame/frame.txt', encoding='utf-8', mode='w') as f2:
                    f2.writelines(data)
        else:
            with open('new_frame/frame.txt', 'a+', encoding='utf-8') as f:
                data = {
                    SI.id_text: {
                        "data": SI.data,
                        "pid": SI.pid_text,
                        "checksum": SI.checksum,
                        "direction": SI.direction,
                        "length": SI.length,
                        "count": 0,
                        "period": SI.period,  # 默认20毫秒
                        "checkBox": False,
                    }
                }
                logger.info("新增数据data： {}".format(data))
                f.write(json.dumps(data) + '\n')

    @staticmethod
    def comment_text(text):
        SI.comment = text

    @staticmethod
    def id_text(text):
        SI.id_text = text

    def pid_text(self, text):
        """
        is pid text
        :param text:
        :return:
        """
        SI.pid_text = text

    def period(self, text):
        try:
            SI.period = int(text)
            if SI.id_text and SI.pid_text and SI.period:
                self.new_frame_btn.setEnabled(True)
            # print(self.new_frame_btn)
        except Exception as e:
            print(e)
            QMessageBox.warning(self, '警告', "delay 必须是数字")

    def data_input(self):
        """
        is data input
        :return:
        """
        hbox = QHBoxLayout()
        Data.data1 = self.input_text()
        if self.check:
            Data.data1.setFocusPolicy(QtCore.Qt.NoFocus)
        Data.data1.setFixedSize(40, 40)
        Data.data1.textChanged.connect(self.data1)
        Data.data1.setText("00")
        Data.data_list.append(Data.data1)
        Data.data2 = self.input_text()
        Data.data2.setFixedSize(40, 40)
        Data.data2.textChanged.connect(self.data2)
        Data.data2.setText("00")
        Data.data_list.append(Data.data2)

        Data.data3 = self.input_text()
        Data.data3.setFixedSize(40, 40)
        Data.data_list.append(Data.data3)
        Data.data3.hide()
        Data.data3.textChanged.connect(self.data3)
        Data.data4 = self.input_text()
        Data.data4.setFixedSize(40, 40)
        Data.data4.textChanged.connect(self.data4)
        Data.data4.hide()
        Data.data_list.append(Data.data4)
        Data.data5 = self.input_text()
        Data.data5.setFixedSize(40, 40)
        Data.data5.textChanged.connect(self.data5)
        Data.data5.hide()
        Data.data_list.append(Data.data5)

        Data.data6 = self.input_text()
        Data.data6.setFixedSize(40, 40)
        Data.data6.textChanged.connect(self.data6)
        Data.data_list.append(Data.data6)
        Data.data6.hide()
        Data.data7 = self.input_text()
        Data.data7.setFixedSize(40, 40)
        Data.data7.textChanged.connect(self.data7)
        Data.data_list.append(Data.data7)
        Data.data7.hide()
        Data.data8 = self.input_text()
        Data.data8.setFixedSize(40, 40)
        Data.data8.textChanged.connect(self.data8)
        Data.data_list.append(Data.data8)
        Data.data8.hide()
        if self.edit:
            data = SI.data.split()
            for i in range(len(data)):
                Data.data_list[i].setText(data[i])
                Data.data_list[i].show()
        for i in Data.data_list:
            hbox.addWidget(i, Qt.AlignTop | Qt.AlignLeft)
        hbox.addStretch(0)
        hbox.setSpacing(10)
        hbox.setContentsMargins(0, 0, 0, 10)
        self.vbox.addLayout(hbox)
        self.new_dialog.setLayout(self.vbox)

    @staticmethod
    def data1(text):
        Data.data_text_dict['data1'] = text

    @staticmethod
    def data2(text):
        Data.data_text_dict['data2'] = text

    @staticmethod
    def data3(text):
        Data.data_text_dict['data3'] = text

    @staticmethod
    def data4(text):
        Data.data_text_dict['data4'] = text

    @staticmethod
    def data5(text):
        Data.data_text_dict['data5'] = text

    @staticmethod
    def data6(text):
        Data.data_text_dict['data6'] = text

    @staticmethod
    def data7(text):
        Data.data_text_dict['data7'] = text

    @staticmethod
    def data8(text):
        Data.data_text_dict['data8'] = text

    def new_frame_label(self):
        """
        add new frame label
        :return:
        """
        hbox2 = QHBoxLayout()
        ID_HEX = QLabel()
        ID_HEX.setText('ID_HEX:')
        ID_HEX.setContentsMargins(0, 0, 0, 10)
        self.vbox.addWidget(ID_HEX)
        self.new_dialog.setLayout(self.vbox)
        self.id_hex()

        data = QLabel()
        data.setContentsMargins(0, 10, 0, 10)
        data.setText('Data(1..8):')
        self.vbox.addWidget(data, 0)
        self.new_dialog.setLayout(self.vbox)
        self.data_input()

        comment = QLabel()
        comment.setText('Comment: ')
        comment.setContentsMargins(0, 0, 0, 10)
        self.vbox.addWidget(comment, 0, Qt.AlignTop)
        self.new_dialog.setLayout(self.vbox)

        self.comment_input = self.input_text()
        self.comment_input.textChanged.connect(self.comment_text)
        self.comment_input.setContentsMargins(0, 0, 0, 10)
        self.comment_input.setMinimumSize(100, 40)
        self.vbox.addWidget(self.comment_input)
        self.new_dialog.setLayout(self.vbox)

        ID = QLabel()
        ID.setText("           ID: ")
        ID.setContentsMargins(0, 0, 0, 30)
        PID = QLabel()
        PID.setText("          PID: ")
        PID.setContentsMargins(0, 0, 0, 30)
        period = QLabel()
        period.setText("       Delay: ")
        period.setContentsMargins(0, 0, 0, 30)
        checksum_type = QLabel()
        checksum_type.setText("Checksum Type: ")
        checksum_type.setContentsMargins(0, 0, 0, 30)
        direction = QLabel()
        direction.setText("    Direction: ")
        direction.setContentsMargins(0, 0, 0, 30)
        length = QLabel()
        length.setText("       Length: ")
        length.setContentsMargins(0, 0, 0, 30)

        self.checksum_combo()
        self.direction_combo()
        self.length_combo()
        self.id_input = self.input_text()
        self.id_input.textChanged.connect(self.id_text)
        self.pid_input = self.input_text()
        self.period_input = self.input_text()
        if self.edit:
            self.id_input.setText(SI.id_text)
            self.pid_input.setText(SI.pid_text)
            self.period_input.setText(str(SI.period))
        self.pid_input.textChanged.connect(self.pid_text)
        self.period_input.textChanged.connect(self.period)

        form = QFormLayout()
        form.setContentsMargins(100, 30, 20, 0)

        form.addRow(ID, self.id_input)
        form.addRow(PID, self.pid_input)
        form.addRow(period, self.period_input)
        form.addRow(checksum_type, self.checksum)
        form.addRow(direction, self.direction)
        form.addRow(length, self.length)
        hbox2.addLayout(form)
        self.groupBox.setLayout(hbox2)
        self.vbox.addWidget(self.groupBox)
        self.vbox.setSpacing(0)
        self.new_dialog.setLayout(self.vbox)

    def connect_label(self):
        """
        Connect button label
        :return:
        """

        hardware = QLabel()
        hardware.setText('Hardware:')
        self.connect_vbox.addWidget(hardware)

    def checksum_combo(self):
        """
        Is Checksum Combobox
        :return:
        """
        checksum = ["Classic", "Enhanced", "Automatic"]
        self.checksum = QComboBox()
        self.checksum.addItem("Classic")
        self.checksum.addItem("Enhanced")
        self.checksum.addItem("Automatic")
        if self.edit:
            self.checksum.setCurrentIndex(checksum.index(str(SI.checksum)))
            SI.checksum = self.checksum.currentText()
        else:
            SI.checksum = self.checksum.currentText()
        self.checksum.currentIndexChanged.connect(self.checksum_combo_index)

    def direction_combo(self):
        """
        is Direction Combobox
        :return:
        """
        direction = ["Disabled", "Publisher", "Subscriber", "Subscriber Automatic Length"]
        self.direction = QComboBox()
        for i in direction:
            self.direction.addItem(i)
        if self.edit:
            self.direction.setCurrentIndex(direction.index(str(SI.direction)))
            SI.direction = self.direction.currentText()
        else:
            SI.direction = self.direction.currentText()
        self.direction.currentIndexChanged.connect(self.direction_combo_index)

    def length_combo(self):
        """
        is Length Combobox
        :return:
        """
        length_data = ['1', '2', '3', '4', '5', '6', '7', '8']
        self.length = QComboBox()
        for i in length_data:
            self.length.addItem(i)
        if self.edit:
            self.length.setCurrentIndex(length_data.index(str(SI.length)))
            SI.length = self.length.currentText()
        else:
            self.length.setCurrentIndex(1)
            SI.length = self.length.currentText()
        self.length.currentIndexChanged.connect(self.length_combo_index)

    def length_combo_index(self, index):
        print("current item is {0}".format(self.length.currentText()), index)
        SI.length = self.length.currentText()
        self.show_hide()

    @staticmethod
    def show_hide():
        data_list_show = Data.data_list[:int(SI.length)]
        data_list_hide = Data.data_list[int(SI.length):]
        for data in data_list_show:
            data.setText("00")
            data.show()
        for data in data_list_hide:
            data.setText("")
            data.hide()

    def checksum_combo_index(self, index):
        print("current item is {0}".format(self.checksum.currentText()), index)
        SI.checksum = self.checksum.currentText()
        # SI.checksum = index

    def direction_combo_index(self, index):
        print("current item is {0}".format(self.direction.currentText()), index)
        SI.direction = self.direction.currentText()
        # SI.direction = index

    def model_combo_index(self, index):
        """
        Model Options
        :param index:
        :return:
        """
        print("current item is {0}".format(self.Model_Combo.currentText()), index)
        SI.choice_model = self.Model_Combo.currentText()

    def bit_rate_combo_index(self, text):
        """
        Model Options
        :param text:
        :return:
        """
        print("current item is {0}".format(self.bit_rate_combo.currentText()), text)
        SI.choice_bit = self.bit_rate_combo.currentText()

    def model_combo(self):
        """
        is Model Combobox
        :return:
        """
        self.Model_Combo = QComboBox(self.connect_dialog)
        self.Model_Combo.addItem("Slave")
        self.Model_Combo.addItem("Master")
        self.Model_Combo.move(140, 258)
        self.Model_Combo.resize(250, 20)
        self.Model_Combo.currentIndexChanged.connect(self.model_combo_index)

    def bit_combo(self):
        """
        is Bit rate combobox
        :return:
        """
        self.bit_rate_combo = QComboBox(self.connect_dialog)
        self.bit_rate_combo.setEditable(True)  # 可编辑下拉框
        self.bit_rate_combo.addItem("19200")
        self.bit_rate_combo.move(140, 298)
        self.bit_rate_combo.resize(250, 20)
        self.bit_rate_combo.currentTextChanged.connect(self.bit_rate_combo_index)

    def id_hex(self):
        """
        is ID(HEX) combobox
        :return:
        """
        self.hex_combo = QComboBox(self.new_dialog)
        self.hex_combo.setFixedHeight(30)
        self.vbox.addWidget(self.hex_combo)
        self.new_dialog.setLayout(self.vbox)
        self.read_frame_txt()

    def read_frame_txt(self):
        try:
            if os.path.exists('new_frame/frame.txt'):
                with open('new_frame/frame.txt', encoding='UTF-8') as f:
                    data = f.readlines()
                    for i in data:
                        for k in json.loads(i.split('\n')[0]):
                            self.hex_combo.addItem(k)
        except Exception as e:
            print(e)

    def input_text(self):
        """
        is text input
        :return:
        """
        text = QLineEdit(self.new_dialog)
        return text

    def update_ui_label(self):
        # self.receive_tableWidget
        self.line += 1
        if self.msg and isinstance(self.msg, tuple):
            ReadMessage.data.update({self.msg[0]: {
                "symbol": "",
                "length": self.msg[1],
                "data": self.msg[2],
                "period": str(40),
                "count": 0,
                "direction": self.msg[4],
                "cst": self.msg[5],
                "checksum": self.msg[6],
                "error": self.msg[7]
            }})
        if ReadMessage.data:
            row_count = self.receive_tableWidget.rowCount()  # 返回当前行数(尾部)
            if row_count < len(ReadMessage.data):
                self.receive_tableWidget.insertRow(row_count)  # 尾部插入一行
            i = 0
            for msg in ReadMessage.data:
                # print(ReadMessage.data[msg]["data"], msg)
                newItem = QTableWidgetItem(msg)
                self.receive_tableWidget.setItem(i, 0, newItem)
                newItem = QTableWidgetItem(ReadMessage.data[msg]["symbol"])
                self.receive_tableWidget.setItem(i, 1, newItem)
                newItem = QTableWidgetItem(str(ReadMessage.data[msg]["length"]))
                self.receive_tableWidget.setItem(i, 2, newItem)
                newItem = QTableWidgetItem(ReadMessage.data[msg]["data"])
                self.receive_tableWidget.setItem(i, 3, newItem)
                newItem = QTableWidgetItem(ReadMessage.data[msg]["period"])
                self.receive_tableWidget.setItem(i, 7, newItem)
                newItem = QTableWidgetItem(str(SI.receive_count))
                self.receive_tableWidget.setItem(i, 4, newItem)
                newItem = QTableWidgetItem(ReadMessage.data[msg]["direction"])
                self.receive_tableWidget.setItem(i, 5, newItem)
                newItem = QTableWidgetItem(ReadMessage.data[msg]["cst"])
                self.receive_tableWidget.setItem(i, 6, newItem)
                newItem = QTableWidgetItem(ReadMessage.data[msg]["checksum"])
                self.receive_tableWidget.setItem(i, 8, newItem)
                newItem = QTableWidgetItem(ReadMessage.data[msg]["error"])
                self.receive_tableWidget.setItem(i, 9, newItem)
                if i < len(ReadMessage.data):
                    i += 1

    def read_message(self):
        self.msg = self.api.read_message()
        logger.info("msg: {}".format(self.msg))
        if self.msg and 'Bus' in self.msg:
            pass
        else:
            if self.check and self.msg:
                SI.receive_count += 1
                ptr = self.msg[2].split()
                if ptr:
                    self.crc_high_first(ptr)
        self.trigger.emit()
        if self.ui_alive:
            # 重新设置定时器
            self.timer = threading.Timer(int(SI.period) / 1000, self.read_message)
            self.timer.daemon = True
            self.timer.start()

    def send_message(self):
        try:
            SI.transmit_count += 1
            if SI.count > 15:
                SI.count = 0
            if self.check:
                ptr = SI.data.split()[1:]
                value = hex(int(ptr.pop(0), 16) >> 4)[2:] + hex(SI.count)[2:]
                ptr.insert(0, value)
                SI.data = self.crc_checksum(ptr)
            message = self.api.write_message(SI.id_text[:2], SI.data, SI.direction, SI.checksum, SI.length)
            self.disconnect_to(boolean=True)
            if message.startswith("S"):
                QMessageBox.about(self, 'send message', message)
            else:
                QMessageBox.warning(self, 'send message', message)
            self.connect_dialog_to(boolean=True)
            newItem = QTableWidgetItem(str(SI.transmit_count))
            if SI.edit_line == 0:
                SI.edit_line += 1
            self.transmit_tableWidget.setItem(SI.edit_line - 1, 4, newItem)
            SI.count += 1
        except Exception as e:
            # print(e, traceback.format_exc())
            logger.error("错误：{}\n{}".format(e, traceback.format_exc()))

    def receive_generate_menu(self, pos):
        try:
            row_num = -1
            for i in self.receive_tableWidget.selectionModel().selection().indexes():
                row_num = i.row()
            menu = QMenu()
            item1 = menu.addAction(u"删除")
            action = menu.exec_(self.receive_tableWidget.mapToGlobal(pos))
            if action == item1:
                self.receive_tableWidget.removeRow(row_num)
            else:
                return
        except Exception as e:
            print(e)

    def transmit_generate_menu(self, pos):
        try:
            row_num = -1
            for i in self.transmit_tableWidget.selectionModel().selection().indexes():
                row_num = i.row()
            menu = QMenu()
            item1 = menu.addAction(u"删除")
            item2 = menu.addAction(u"编辑")
            action = menu.exec_(self.transmit_tableWidget.mapToGlobal(pos))

            if action == item1 and row_num >= 0:
                SI.line -= 1
                self.transmit_tableWidget.removeRow(row_num)
                if row_num == 0:
                    self.get_pos_content(row_num, 0)
                else:
                    self.get_pos_content(0, 0)
                if os.path.exists("new_frame/frame.txt"):
                    with open('new_frame/frame.txt', encoding='utf-8') as f1:
                        data = f1.readlines()
                        data.pop(self.row_num)
                        with open('new_frame/frame.txt', encoding='utf-8', mode='w') as f2:
                            f2.writelines(data)
            elif action == item2 and row_num >= 0:
                SI.edit_line = 0
                self.edit = True
                self.row_num = row_num
                self.get_pos_content(row_num, 0)
                if SI.id_text:
                    self.new_frame_dialog()
            else:
                return
        except Exception as e:
            print(e)

    @staticmethod
    def crc_high_first(ptr):
        initial = '0x01'
        cache = ptr
        if ptr:
            initial = ptr.pop(0)
        crc = 0x00
        for iptr in ptr:
            crc ^= int(iptr, 16)
            for index in range(8):
                if crc & 0x80:
                    crc = (crc << 1) ^ 0x1d
                else:
                    crc = (crc << 1)

            # print("crc===%02x" % crc)
        str_crc = '0x{:02x}'.format(crc & 0xff)
        # print(str_crc)
        if int(str_crc, 16) == int(initial, 16):
            return True
        else:
            logger.info("CRC校验错误： {}".format(cache))
            return False

    @staticmethod
    def crc_checksum(ptr):
        crc = 0x00
        for iptr in ptr:
            crc ^= int(iptr, 16)
            for index in range(8):
                if crc & 0x80:
                    crc = (crc << 1) ^ 0x1d
                else:
                    crc = (crc << 1)

        str_crc = '0x{:02x}'.format(crc & 0xff)
        ptr.insert(0, str_crc[2:])
        logger.info("原始数据：{}，发送的CRC数据： {}".format(ptr, " ".join(ptr)))
        return " ".join(ptr)

    def start(self):
        self.start_button = self.new_button("循环发送")

    def pause(self):
        self.pause_button = self.new_button("循环暂停")

    def stop(self):
        self.stop_button = self.new_button("循环停止")

    def start_send(self):
        self.cyclic_scheduling = True
        self.start_status = True
        self.start_send_message()

    def start_send_message(self):
        self.data = self.read_frame()
        if not self.data:
            QMessageBox.about(self, "警告", "请先添加数据")
        if self.data and self.start_status:
            self.start_button.setEnabled(False)
            T = threading.Thread(target=self.scheduling, args=())
            T.daemon = True
            T.start()

    def stop_send(self):
        self.start_button.setEnabled(True)
        self.cyclic_scheduling = False
        self.start_status = False

    # @staticmethod
    def read_frame(self):
        try:
            if os.path.exists('new_frame/frame.txt'):
                with open('new_frame/frame.txt', encoding='UTF-8') as f:
                    data = f.readlines()
                    data = [json.loads(i.split('\n')[0]) for i in data]
                    for row in range(self.transmit_tableWidget.rowCount()):
                        if self.transmit_tableWidget.cellWidget(row, 0).findChild(QCheckBox).isChecked():
                            for v in data:
                                if self.transmit_tableWidget.cellWidget(row, 0).findChild(QLabel).text() in v:
                                    v[self.transmit_tableWidget.cellWidget(row, 0).findChild(QLabel).text()][
                                        "checkBox"] = True

                    return data
        except Exception as e:
            logger.info(f"{e}\n{traceback.format_exc()}")

    def scheduling(self):
        try:
            for i in range(len(self.data)):
                for k, v in self.data[i].items():
                    if v["checkBox"]:
                        QApplication.processEvents()
                        t = threading.Timer(self.data[i][list(self.data[i])[0]]["period"] / 1000, self.cycle_to_send,
                                            args=(
                                                self.data[i], i))
                        t.start()
                        if self.cyclic_scheduling and self.start_status:
                            t = threading.Timer(self.data[i][list(self.data[i])[0]]["period"] / 1000, self.scheduling)
                            t.start()
        except Exception as e:
            print(e)

    def cycle_to_send(self, data, i):
        data[list(data)[0]]["count"] += 1
        self.trigger_count.emit(data[list(data)[0]]["count"], i)
        try:
            count = data[list(data)[0]]["count"] % 15
            if self.check:
                ptr = data[list(data)[0]]["data"].split()[1:]
                value = hex(int(ptr.pop(0), 16) >> 4)[2:] + hex(count)[2:]
                ptr.insert(0, value)
                send_data = self.crc_checksum(ptr)
            else:
                send_data = data[list(data)[0]]["data"].split()
            message = self.api.write_message(list(data)[0][:2], send_data, SI.direction, SI.checksum, SI.length)
            logger.info(f"SEND MESSAGE: {message}")
        except Exception as e:
            # print(e, traceback.format_exc())
            logger.error("错误：{}\n{}".format(e, traceback.format_exc()))

    def update_count(self, count, line):
        newItem = QTableWidgetItem(str(count))
        self.transmit_tableWidget.setItem(line, 4, newItem)


if __name__ == '__main__':
    PlinkView()
