# -*- mode: python ; coding: utf-8 -*-

import sys
block_cipher = None


a = Analysis(
    ['ATEApp.py'],
    pathex=[""],
    binaries=[],
    datas=[
    ('images', 'images'),
    ('res', 'res'),
    ('control_board', 'control_board'),
    ('checkpoints', 'checkpoints'),
    ('configs', 'configs'),
    ("qt_material", "qt_material"),
    ('orbbec', 'orbbec'),
    ('algorithm_server', 'algorithm_server'),
    ('adb/zlgcan', 'adb/zlgcan'),
    ('adb/pcan', 'adb/pcan'),
    ('adb/lin', 'adb/lin'),
    ('ffmpeg', 'ffmpeg'),
    ('external_program/KingstVISQTTool','external_program/KingstVISQTTool'),
    ('tools/endurance/configs', 'tools/endurance/configs'),
    ('external_program/KingstVISQTTool/KingstVIS/', 'external_program/KingstVISQTTool/KingstVIS/'),
    ('power/configs', 'power/configs'),
    ('tools/can_tool/devices/canmatrix', 'canmatrix'),
    ('tools/can_tool/devices/future', 'future'),
    ('tools/can_tool/devices/past', 'past'),
    ('adb/TSMaster/libTSCANAPI/', 'libTSCANAPI'),
    ('adb/dll', 'adb/dll'),
    ],
    hiddenimports=['canmatrix.formats.dbc'],
    hookspath=[],
    runtime_hooks=[],
    excludes=['importlib_resources',],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,

)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='ATEApp',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=True,
    icon='.\\res\\HWTC.ico'
)
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=['api-ms-win-crt-multibyte-l1-1-0.dll'],
    name='ATEApp',
)
