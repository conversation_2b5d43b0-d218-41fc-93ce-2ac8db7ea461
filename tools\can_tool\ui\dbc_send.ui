<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1600</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1600</width>
    <height>900</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1,1">
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="title">
      <string>DBC视图</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <widget class="QPushButton" name="choice_dbc_button">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>45</height>
           </size>
          </property>
          <property name="text">
           <string>选择文件</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="dbc_file_path">
          <property name="text">
           <string>文件地址：等待导入中....</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="load_dbc_button">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>45</height>
           </size>
          </property>
          <property name="text">
           <string>导入</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QTreeWidget" name="treeWidget">
        <column>
         <property name="text">
          <string>消息名</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>ID</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>DLC</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>注释</string>
         </property>
        </column>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_2">
     <property name="title">
      <string>CAN视图</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3" stretch="1,4,4">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1,1,3">
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <item>
           <widget class="QPushButton" name="delete_message">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="text">
             <string>删除</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="clear_message">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="text">
             <string>清空</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QGridLayout" name="gridLayout" columnminimumwidth="3,1,1">
          <item row="1" column="1">
           <widget class="QComboBox" name="send_type_box">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <item>
             <property name="text">
              <string>串型发送</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>并型发送</string>
             </property>
            </item>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QPushButton" name="list_send_message">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="text">
             <string>列表发送</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <widget class="QPushButton" name="stop_message">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="text">
             <string>停止</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QPushButton" name="send_message">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="text">
             <string>发送</string>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QComboBox" name="connect_type">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <item>
             <property name="text">
              <string>请选择通讯方式</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>PCAN</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>周立功</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>CANOE</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>LIN</string>
             </property>
            </item>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QTableWidget" name="tableWidget_Message">
        <column>
         <property name="text">
          <string>消息名</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>ID(Hex)</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>发送次数</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>间隔(ms)</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>状态</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>已发</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>DLC</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>B1</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>B2</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>B3</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>B4</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>B5</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>B6</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>B7</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>B8</string>
         </property>
        </column>
       </widget>
      </item>
      <item>
       <widget class="QTableWidget" name="tableWidget_signal">
        <column>
         <property name="text">
          <string>信号名</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>原始值(Hex)</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>实际值</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>值描述</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>单位</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>变化比例</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>变化偏移</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>起始位</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>位宽</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>注释</string>
         </property>
        </column>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
