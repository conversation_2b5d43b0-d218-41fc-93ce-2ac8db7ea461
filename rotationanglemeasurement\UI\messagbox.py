# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'UiMessagDialog.ui'
#
# Created by: PyQt5 UI code generator 5.15.0
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_DialogMessage(object):
    def setupUi(self, DialogMessage):
        DialogMessage.setObjectName("DialogMessage")
        DialogMessage.resize(437, 178)
        self.verticalLayout = QtWidgets.QVBoxLayout(DialogMessage)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.label_icon = QtWidgets.QLabel(DialogMessage)
        self.label_icon.setText("")
        self.label_icon.setScaledContents(False)
        self.label_icon.setObjectName("label_icon")
        self.horizontalLayout.addWidget(self.label_icon)
        spacerItem1 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.label = QtWidgets.QLabel(DialogMessage)
        self.label.setText("")
        self.label.setObjectName("label")
        self.horizontalLayout.addWidget(self.label)
        self.horizontalLayout.setStretch(3, 2)
        self.verticalLayout.addLayout(self.horizontalLayout)
        self.buttonBox = QtWidgets.QDialogButtonBox(DialogMessage)
        self.buttonBox.setOrientation(QtCore.Qt.Horizontal)
        self.buttonBox.setStandardButtons(QtWidgets.QDialogButtonBox.Cancel|QtWidgets.QDialogButtonBox.Ok)
        self.buttonBox.setObjectName("buttonBox")
        self.verticalLayout.addWidget(self.buttonBox)

        self.retranslateUi(DialogMessage)
        self.buttonBox.accepted.connect(DialogMessage.accept)
        self.buttonBox.rejected.connect(DialogMessage.reject)
        QtCore.QMetaObject.connectSlotsByName(DialogMessage)

    def retranslateUi(self, DialogMessage):
        _translate = QtCore.QCoreApplication.translate
        DialogMessage.setWindowTitle(_translate("DialogMessage", "Dialog"))

