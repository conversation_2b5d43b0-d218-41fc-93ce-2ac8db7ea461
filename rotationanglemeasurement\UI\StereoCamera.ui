<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Stereo</class>
 <widget class="QWidget" name="Stereo">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>830</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>60</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout" stretch="0">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>10</number>
   </property>
   <property name="topMargin">
    <number>5</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QSplitter" name="splitter">
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="handleWidth">
      <number>0</number>
     </property>
     <widget class="QDockWidget" name="dockWidget">
      <property name="styleSheet">
       <string notr="true"/>
      </property>
      <widget class="QWidget" name="dockWidgetContents_3">
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_6">
        <property name="leftMargin">
         <number>5</number>
        </property>
        <property name="topMargin">
         <number>5</number>
        </property>
        <property name="rightMargin">
         <number>5</number>
        </property>
        <property name="bottomMargin">
         <number>5</number>
        </property>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_5" stretch="0,0,0,0,0,0">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout">
            <property name="topMargin">
             <number>10</number>
            </property>
            <item>
             <spacer name="horizontalSpacer_6">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeType">
               <enum>QSizePolicy::Fixed</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="pushButtonConnectCamera">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>150</width>
                <height>60</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>连接相机</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="pushButtonDisconnectCamera">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>150</width>
                <height>60</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>断开相机</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_7">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeType">
               <enum>QSizePolicy::Fixed</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBoxCameraMatrix">
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="title">
             <string/>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_2">
             <property name="spacing">
              <number>6</number>
             </property>
             <property name="topMargin">
              <number>9</number>
             </property>
             <item>
              <layout class="QGridLayout" name="gridLayout">
               <property name="topMargin">
                <number>0</number>
               </property>
               <property name="verticalSpacing">
                <number>0</number>
               </property>
               <item row="2" column="1">
                <widget class="QLineEdit" name="lineEdit2Start">
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QLineEdit" name="lineEdit1End">
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                </widget>
               </item>
               <item row="3" column="1">
                <widget class="QLineEdit" name="lineEdit2End">
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                </widget>
               </item>
               <item row="4" column="1">
                <widget class="QLineEdit" name="lineEdit3Start">
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                </widget>
               </item>
               <item row="5" column="1">
                <widget class="QLineEdit" name="lineEdit3End">
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                </widget>
               </item>
               <item row="6" column="1">
                <widget class="QLineEdit" name="lineEdit4Start">
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QLineEdit" name="lineEdit1Start">
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                </widget>
               </item>
               <item row="7" column="1">
                <widget class="QLineEdit" name="lineEdit4End">
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                </widget>
               </item>
               <item row="0" column="2">
                <widget class="QCheckBox" name="checkBox1start">
                 <property name="text">
                  <string/>
                 </property>
                </widget>
               </item>
               <item row="1" column="2">
                <widget class="QCheckBox" name="checkBox1end">
                 <property name="text">
                  <string/>
                 </property>
                </widget>
               </item>
               <item row="2" column="2">
                <widget class="QCheckBox" name="checkBox2start">
                 <property name="text">
                  <string/>
                 </property>
                </widget>
               </item>
               <item row="3" column="2">
                <widget class="QCheckBox" name="checkBox2end">
                 <property name="text">
                  <string/>
                 </property>
                </widget>
               </item>
               <item row="4" column="2">
                <widget class="QCheckBox" name="checkBox3start">
                 <property name="text">
                  <string/>
                 </property>
                </widget>
               </item>
               <item row="5" column="2">
                <widget class="QCheckBox" name="checkBox3end">
                 <property name="text">
                  <string/>
                 </property>
                </widget>
               </item>
               <item row="6" column="2">
                <widget class="QCheckBox" name="checkBox4start">
                 <property name="text">
                  <string/>
                 </property>
                </widget>
               </item>
               <item row="7" column="2">
                <widget class="QCheckBox" name="checkBox4end">
                 <property name="text">
                  <string/>
                 </property>
                </widget>
               </item>
               <item row="0" column="0" rowspan="2">
                <widget class="QLabel" name="label">
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="text">
                  <string>双目1</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="0" rowspan="2">
                <widget class="QLabel" name="label_2">
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="text">
                  <string>双目2</string>
                 </property>
                </widget>
               </item>
               <item row="4" column="0" rowspan="2">
                <widget class="QLabel" name="label_3">
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="text">
                  <string>双目3</string>
                 </property>
                </widget>
               </item>
               <item row="6" column="0" rowspan="2">
                <widget class="QLabel" name="label_4">
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="text">
                  <string>双目4</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButtonTakePhotos">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>60</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="text">
             <string>拍照</string>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_4">
            <item>
             <widget class="QPushButton" name="pushButtonCoordinateTransformation">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>60</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>坐标系转换</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButtonCalc">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>60</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>计算坐标</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="checkBoxYAW">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>60</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>测量YAW角度</string>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="pushButtonCalcAngle">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>60</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>计算角度</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <widget class="QLabel" name="labelResult">
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBoxTest">
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="title">
             <string/>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_4">
             <property name="spacing">
              <number>6</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_3">
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_2">
                 <item>
                  <widget class="QLabel" name="label_5">
                   <property name="styleSheet">
                    <string notr="true"/>
                   </property>
                   <property name="text">
                    <string>角度</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QSpinBox" name="spinBoxAngle">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>45</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true"/>
                   </property>
                   <property name="minimum">
                    <number>-354</number>
                   </property>
                   <property name="maximum">
                    <number>354</number>
                   </property>
                   <property name="value">
                    <number>15</number>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_2">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                 <item>
                  <widget class="QComboBox" name="comboBoxRotateType">
                   <property name="minimumSize">
                    <size>
                     <width>150</width>
                     <height>45</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>11</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true"/>
                   </property>
                   <item>
                    <property name="text">
                     <string>yaw</string>
                    </property>
                   </item>
                   <item>
                    <property name="text">
                     <string>pitch</string>
                    </property>
                   </item>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_4">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                 <item>
                  <widget class="QPushButton" name="pushButtonAdd">
                   <property name="minimumSize">
                    <size>
                     <width>75</width>
                     <height>60</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>75</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true"/>
                   </property>
                   <property name="text">
                    <string>+</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QPushButton" name="pushButtonSub">
                   <property name="minimumSize">
                    <size>
                     <width>75</width>
                     <height>60</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>75</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true"/>
                   </property>
                   <property name="text">
                    <string>-</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <widget class="QListWidget" name="listWidgetTest">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="styleSheet">
                  <string notr="true"/>
                 </property>
                 <property name="autoScrollMargin">
                  <number>6</number>
                 </property>
                </widget>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_3">
                 <item>
                  <widget class="QPushButton" name="pushButtonSaveTest">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>60</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true"/>
                   </property>
                   <property name="text">
                    <string>保存用例</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_3">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                 <item>
                  <widget class="QPushButton" name="pushButtonExportData">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>60</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true"/>
                   </property>
                   <property name="text">
                    <string>导出数据</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_5">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                 <item>
                  <widget class="QPushButton" name="pushButtonClearResult">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>60</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true"/>
                   </property>
                   <property name="text">
                    <string>  清除  </string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </widget>
     <widget class="QWidget" name="widgetDevices" native="true"/>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
