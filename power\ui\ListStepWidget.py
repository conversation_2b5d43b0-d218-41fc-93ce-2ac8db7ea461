# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'ListStepWidget.ui'
#
# Created by: PyQt5 UI code generator 5.15.6
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(490, 145)
        self.verticalLayout = QtWidgets.QVBoxLayout(Form)
        self.verticalLayout.setObjectName("verticalLayout")
        self.formLayout = QtWidgets.QFormLayout()
        self.formLayout.setObjectName("formLayout")
        self.label_2 = QtWidgets.QLabel(Form)
        self.label_2.setObjectName("label_2")
        self.formLayout.setWidget(1, QtWidgets.QFormLayout.LabelRole, self.label_2)
        self.doubleSpinBox_volt = QtWidgets.QDoubleSpinBox(Form)
        self.doubleSpinBox_volt.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_volt.setKeyboardTracking(False)
        self.doubleSpinBox_volt.setDecimals(3)
        self.doubleSpinBox_volt.setMinimum(0.0)
        self.doubleSpinBox_volt.setMaximum(60.0)
        self.doubleSpinBox_volt.setProperty("value", 0.0)
        self.doubleSpinBox_volt.setObjectName("doubleSpinBox_volt")
        self.formLayout.setWidget(1, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_volt)
        self.label_3 = QtWidgets.QLabel(Form)
        self.label_3.setObjectName("label_3")
        self.formLayout.setWidget(2, QtWidgets.QFormLayout.LabelRole, self.label_3)
        self.doubleSpinBox_curr = QtWidgets.QDoubleSpinBox(Form)
        self.doubleSpinBox_curr.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_curr.setDecimals(3)
        self.doubleSpinBox_curr.setMinimum(0.0)
        self.doubleSpinBox_curr.setMaximum(60.0)
        self.doubleSpinBox_curr.setProperty("value", 0.0)
        self.doubleSpinBox_curr.setObjectName("doubleSpinBox_curr")
        self.formLayout.setWidget(2, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_curr)
        self.label_4 = QtWidgets.QLabel(Form)
        self.label_4.setObjectName("label_4")
        self.formLayout.setWidget(3, QtWidgets.QFormLayout.LabelRole, self.label_4)
        self.doubleSpinBox_slope = QtWidgets.QDoubleSpinBox(Form)
        self.doubleSpinBox_slope.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_slope.setDecimals(3)
        self.doubleSpinBox_slope.setMinimum(0.001)
        self.doubleSpinBox_slope.setMaximum(9999.99)
        self.doubleSpinBox_slope.setObjectName("doubleSpinBox_slope")
        self.formLayout.setWidget(3, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_slope)
        self.label_5 = QtWidgets.QLabel(Form)
        self.label_5.setObjectName("label_5")
        self.formLayout.setWidget(4, QtWidgets.QFormLayout.LabelRole, self.label_5)
        self.doubleSpinBox_time = QtWidgets.QDoubleSpinBox(Form)
        self.doubleSpinBox_time.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_time.setDecimals(3)
        self.doubleSpinBox_time.setMinimum(0.0)
        self.doubleSpinBox_time.setMaximum(9999.99)
        self.doubleSpinBox_time.setProperty("value", 0.0)
        self.doubleSpinBox_time.setObjectName("doubleSpinBox_time")
        self.formLayout.setWidget(4, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_time)
        self.pushButton_delete = QtWidgets.QPushButton(Form)
        self.pushButton_delete.setObjectName("pushButton_delete")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.SpanningRole, self.pushButton_delete)
        self.verticalLayout.addLayout(self.formLayout)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.label_2.setText(_translate("Form", "电压(V)："))
        self.label_3.setText(_translate("Form", "电流(A)："))
        self.label_4.setText(_translate("Form", "上升斜率(S)："))
        self.label_5.setText(_translate("Form", "运行持续时间(S)："))
        self.pushButton_delete.setText(_translate("Form", "删除"))
