from PyQt5.QtGui import QPixmap
from PyQt5.QtWidgets import QDialog

from rotationanglemeasurement.UI.messagbox import Ui_DialogMessage




class MessageDialog(Ui_DialogMessage,QDialog):
    def __init__(self,title,info):
        super(MessageDialog, self).__init__()
        self.setupUi(self)
        self.setWindowTitle(title)
        self.label_icon.setPixmap(QPixmap(":/images/information_piece_icon.png").scaled(50,50))
        self.label.setText(info)
        # self.label_icon.setScaledContents(True)

    @staticmethod
    def showMessage(title, info):
        dialog = MessageDialog(title, info)
        result = dialog.exec_()
        return result
# 使用
# result = MessageDialog.showMessage("警告", "这是一个警告这是一个警告这是一个警告这是一个警告这是一个警告这是一个警告这是一个警告")
# if result == QDialog.Accepted:
#     print("用户点击了确认")
# elif result == QDialog.Rejected:
#     print("用户点击了取消")