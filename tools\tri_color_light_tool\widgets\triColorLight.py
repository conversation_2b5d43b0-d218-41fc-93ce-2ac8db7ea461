from PyQt5.QtWidgets import QWidget
from PyQt5.QtCore import QTimer

from control_board.mcc_io_client import mcc_io_client
from .ui.triColorLight import Ui_Form
from ..tri_color_light import tri_color_light


class TriColorLightWidget(QWidget, Ui_Form):
    def __init__(self, parent=None):
        super().__init__(parent=parent)

        self.setupUi(self)

        self.setWindowTitle("三色灯工具")

        self.resize(900, 400)

        self.pushButton_red_open.clicked.connect(self.red_open)
        self.pushButton_red_close.clicked.connect(self.red_close)

        self.pushButton_green_open.clicked.connect(self.green_open)
        self.pushButton_green_close.clicked.connect(self.green_close)

        self.pushButton_yellow_open.clicked.connect(self.yellow_open)
        self.pushButton_yellow_close.clicked.connect(self.yellow_close)

        self.pushButton_buzzer_open.clicked.connect(self.buzzer_open)
        self.pushButton_buzzer_close.clicked.connect(self.buzzer_close)

        self.pushButton_all_close.clicked.connect(self.all_close)

        self.timer = QTimer(self)
        self.timer.timeout.connect(self.on_status_monitor)
        self.timer.start(1000)

    @staticmethod
    def red_open():
        if tri_color_light.is_open():
            tri_color_light.red_open()
        elif mcc_io_client.is_open():
            mcc_io_client.tri_color_red_open()

    @staticmethod
    def red_close():
        if tri_color_light.is_open():
            tri_color_light.red_close()
        elif mcc_io_client.is_open():
            mcc_io_client.tri_color_red_close()

    @staticmethod
    def green_open():
        if tri_color_light.is_open():
            tri_color_light.green_open()
        elif mcc_io_client.is_open():
            mcc_io_client.tri_color_green_open()

    @staticmethod
    def green_close():
        if tri_color_light.is_open():
            tri_color_light.green_close()
        elif mcc_io_client.is_open():
            mcc_io_client.tri_color_green_close()

    @staticmethod
    def yellow_open():
        if tri_color_light.is_open():
            tri_color_light.yellow_open()
        elif mcc_io_client.is_open():
            mcc_io_client.tri_color_yellow_open()

    @staticmethod
    def yellow_close():
        if tri_color_light.is_open():
            tri_color_light.yellow_close()
        elif mcc_io_client.is_open():
            mcc_io_client.tri_color_yellow_close()

    @staticmethod
    def buzzer_open():
        if tri_color_light.is_open():
            tri_color_light.buzzer_open()
        elif mcc_io_client.is_open():
            mcc_io_client.tri_color_buzzer_open()

    @staticmethod
    def buzzer_close():
        if tri_color_light.is_open():
            tri_color_light.buzzer_close()
        elif mcc_io_client.is_open():
            mcc_io_client.tri_color_buzzer_close()

    @staticmethod
    def all_close():
        if tri_color_light.is_open():
            tri_color_light.all_close()
        elif mcc_io_client.is_open():
            mcc_io_client.tri_color_red_close()
            mcc_io_client.tri_color_green_close()
            mcc_io_client.tri_color_yellow_close()
            mcc_io_client.tri_color_buzzer_close()

    def on_status_monitor(self):
        if tri_color_light.is_open() or mcc_io_client.is_open():
            text = "已连接"
        else:
            text = "已断开"
        if self.lineEdit_conn_status.text() != text:
            self.lineEdit_conn_status.setText(text)

