import multiprocessing
import os
import sys
import time

from PyQt5 import QtCore
from PyQt5.QtCore import QLockFile, QDir
from PyQt5.QtGui import QIcon, QFont, QFontDatabase
from PyQt5.QtWidgets import QApplication

from common.AppConfig import app_config
from common.LogUtils import start_log
from qt_material import apply_stylesheet
from utils.ScreenManager import screen_manager
from utils.SystemManager import system_manager
from view.LoginView import LoginView


def set_global_font(font_path, point_size=14):
    # 加载字体文件
    font_id = QFontDatabase.addApplicationFont(font_path)

    print(f"font_id id={font_id}")

    if font_id < 0:
        print("Error loading font file.")
        return

    # 获取字体系列名称
    font_family = QFontDatabase.applicationFontFamilies(font_id)[0]
    print(f"font_family id={font_family}")

    # 设置全局字体
    app.setFont(QFont(font_family, point_size))


if __name__ == '__main__':
    multiprocessing.freeze_support()
    file = QLockFile(f'{QDir.tempPath()}/ATEApp.lock')
    file.setStaleLockTime(0)
    if not file.tryLock():
        # 防止多开
        sys.exit(0)
    # 当程序发生异常时，将异常信息以html的格式保存到指定文件夹中
    # sys.excepthook = cgitb.Hook(display=0, logdir=app_config.exception_folder, context=10, format='html')
    app_config.load_global_config()
    filename = '%s.log' % (time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime(time.time())))
    formatter = '%(asctime)s - %(thread)d - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s'
    start_log()
    # test_upload_manager.start_message_center()
    system_manager.check_disk_free_usage()
    QApplication.setAttribute(QtCore.Qt.AA_Use96Dpi)
    # QtCore.QCoreApplication.setAttribute(QtCore.Qt.AA_EnableHighDpiScaling)
    app = QApplication(sys.argv)
    # 低分辨率下 界面整齐
    screen = app.primaryScreen()
    screen_resolution = screen.size()
    screen_width, screen_height = screen_resolution.width(), screen_resolution.height()
    screen_manager.screen_width = screen_width
    screen_manager.screen_height = screen_height
    # 设置全局主题样式
    apply_stylesheet(app, theme='dark_custom.xml')
    set_global_font("qt_material/fonts/notosans/NotoSansSC-Regular.ttf", 11)
    icon = QIcon(os.path.join(os.getcwd(), "./res/HWTC.ico"))
    app.setWindowIcon(icon)
    login_window = LoginView()
    login_window.show()
    app.exec_()
