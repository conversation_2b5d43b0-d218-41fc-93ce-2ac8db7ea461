import threading
import time

from simbox_tools.Logger import Logger

# from core.CommunicationManager import communication_manager


class DiagServer(threading.Thread):
    __log_tag = "DiagServer"
    __send_func = None
    __request_flag = False
    __response_flag = False
    __data = None

    def __init__(self, send_func):
        threading.Thread.__init__(self)
        self.__send_func = send_func

    def get_data(self):
        return self.__data

    def msg_protocol(self, msg):
        Logger.console(self.__log_tag, "msg_protocol -> %s" % msg)
        # communication_manager.recv_diag_msg_signal.emit(msg)
        if msg is not None:
            self.__data = msg
            self.__response_flag = True

    def request(self, req_data):
        self.__response_flag = False
        self.__request_flag = True
        self.__send_func(req_data)

    def wait_response(self, timeout=10, data=None):
        while True:
            if timeout > 0:
                timeout -= 1
                if self.__response_flag:
                    if (data is None) or (len(data) <= 0):
                        break
                    else:
                        recv_data = self.__data
                        if (recv_data is not None) and (len(recv_data) >= len(data)):
                            flag = False
                            for index in range(len(data)):
                                if data[index] != recv_data[index]:
                                    flag = True
                            if flag is False:
                                break
                        pass

            else:
                return False
            time.sleep(0.001)
        return True
