import time

import pyvisa

if __name__ == '__main__':
    rm = pyvisa.ResourceManager()
    res_list = rm.list_resources()
    print(f"res_list={res_list}")
    inst = rm.open_resource(res_list[0])
    inst.write_termination = "\n"
    inst.read_termination = "\n"
    inst.timeout = 5000

    idn = inst.query("*IDN?")
    print(f"idn={idn}")

    inst.write("*IDN?")
    print(f"IDN inst_read={inst.read()}")
    inst.write(":TIMebase:HOTKeys RUN")

    inst.write(":TIMebase:MAIN:SCALe 0.0002")
    inst.write(":TIMebase:MAIN:SCALe 0.001")

    time.sleep(6)

    inst.write(":MEASure:STATistic:ITEM FREQuency,CHANnel1")
    # 查询最低频率测量值
    inst.write(":MEASure:STATistic:ITEM? MINimum,FREQuency")  # 发送查询命令
    frequency = inst.read()  # 读取结果
    print(f"frequency={frequency}")
    # 设置波形数据存储深度
    inst.write(":ACQuire:MDEPth 100k")
    inst.write(":RUN")
    time.sleep(5)
    inst.write(":STOP")
    inst.write(":WAV:MODE RAW")
    inst.write(":WAV:FORM BYTE")
    # inst.write(":WAV:STAR 1")
    # inst.write(":WAVeform:STOP 100000")
    inst.write(":WAV:DATA?")
    ret = inst.read_bytes(100000)
    print(f"WAV_DATA inst_read={ret}")
    int_list = [int(byte) for byte in ret]
    print(len(int_list), int_list)
    rm.close()
