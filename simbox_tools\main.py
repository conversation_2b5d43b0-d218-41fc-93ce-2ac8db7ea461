import sys

import json
import os
import shutil
import threading
import time

import cv2
from PIL import Image
from PyQt5.QtCore import QObject

from simbox_tools.CommunicationManager import communication_manager
from simbox_tools.UiControlManager import uiControlManager
from simbox_tools.serial_communication.NomiDevice import NomiDevice
from utils.VideoTools import Video<PERSON>ombine

# from serial_communication.NomiDevice import NomiDevice

nomiDevice = NomiDevice()


def getDevice(com):
    print("comes!")
    nomiDevice = NomiDevice()
    nomiDevice.connect(com)
    # 校准
    pitch_start_position, yaw_start_position = uiControlManager.get_kinetic_start_position()
    pitch_real_position = communication_manager.get_pitch_real_position()
    yaw_real_position = communication_manager.get_yaw_real_position()
    pitch_offset = pitch_start_position - pitch_real_position
    yaw_offset = yaw_start_position - yaw_real_position
    communication_manager.set_request_calibrate_state(1)  # 校准
    time.sleep(20)

    nomiDevice.rotate_degree(pitch_offset=-20, yaw_offset=-40)  # 起始位置
    time.sleep(3)
    print("nomiDevice.rotate_degree_movement(pitch_offset=30, yaw_offset=40,pitch_rotate_time=400,)")
    # rotate_degree_movement(self, pitch_offset=0, yaw_offset=0,pitch_rotate_time=100,yaw_rotate_time =100,conv0=45,conv1=55)
    nomiDevice.rotate_degree_movement(pitch_offset=0, yaw_offset=40, pitch_rotate_time=400, )
    time.sleep(10)
    nomiDevice.rotate_degree_movement(pitch_offset=0, yaw_offset=40, pitch_rotate_time=400, )
    print("end!!!")
    for thread in threading.enumerate():
        print(thread.name, "is running!")
        if "MainThread" == thread.name:
            pass
        else:
            try:
                thread._stop()  # 使用私有方法来试图结束线程
            except Exception as e:
                print(f"Error stopping thread {thread.name}: {e}")

    # time.sleep(50)
    return nomiDevice


class SimboxControl(QObject):

    def __init__(self):
        super().__init__()
        self.nomiDevice = nomiDevice
        self.communication_manager = communication_manager
        self.video_combine = VideoCombine()
        self._is_connect = False

    def connect_nomi(self, port):
        self.calibration()
        self._is_connect = self.nomiDevice.connect(port)
        return self._is_connect

    def push_rotate2nomi(self, angle):

        self.nomiDevice.rotate_degree(pitch_offset=-20, yaw_offset=angle)  # 起始位置

    def get_nomi_device(self):
        return self.nomiDevice

    def get_communication_manager(self):
        return self.communication_manager

    @staticmethod
    def find_simbox_drive():
        # 获取所有的盘符
        drives = [drive + ":" for drive in "ABCDEFGHIJKLMNOPQRSTUVWXYZ"]
        # 遍历每个盘符，查看是否有simbox.txt文件
        for drive in drives:
            drive_path = drive + "\\"
            if os.path.exists(drive_path):
                files = os.listdir(drive_path)
                if "simbox.txt" in files:
                    return drive_path
        # 如果没有找到simbox.txt文件，返回None
        return None

    def copy_file2nomi(self, h264_file):
        path = os.path.join(os.getcwd(), "images")
        if not os.path.exists(os.path.join(path, "tmp")):
            os.mkdir(os.path.join(path, "tmp"))
        else:
            # 删除tmp下所有文件
            for file in os.listdir(os.path.join(path, "tmp")):
                # 判断file是文件夹还是文件
                if os.path.isdir(os.path.join(path, "tmp", file)):
                    # 删除文件夹
                    shutil.rmtree(os.path.join(path, "tmp", file))
                else:
                    if file != "images.h264":
                        os.remove(os.path.join(path, "tmp", file))
        # 将img/template_520ms 下的所有文件和文件夹拷贝到 tmp文件下
        for file in os.listdir(os.path.join(path, "template_520ms")):
            if os.path.isdir(os.path.join(path, "template_520ms", file)):
                shutil.copytree(os.path.join(path, "template_520ms", file), os.path.join(path, "tmp", file))
            else:
                shutil.copy(os.path.join(path, "template_520ms", file), os.path.join(path, "tmp", file))

        shutil.copy(h264_file, os.path.join(path, "tmp", "test.h264"))
        simbox_drive = self.find_simbox_drive()
        # 将tmp文件夹下的所有文件拷贝到simbox盘符下
        if simbox_drive is not None:
            print("copy 2 simbox memery", simbox_drive)
            # 将tmp文件夹下的所有文件拷贝到simbox盘符下
            # 获取源目录下所有文件和文件夹
            src_folder = os.path.join(path, "tmp")
            dest_folder = simbox_drive
            for item in os.listdir(src_folder):
                s = os.path.join(src_folder, item)
                d = os.path.join(dest_folder, item)
                if os.path.isdir(s):
                    if sys.version_info >= (3, 8):
                        shutil.copytree(s, d, dirs_exist_ok=True)
                    else:
                        if os.path.exists(d):
                            shutil.rmtree(d)
                        shutil.copytree(s, d)
                else:
                    if item != "images.h264":
                        shutil.copy2(s, d)

        else:
            print("没有找到simbox盘符")

    def push_img2nomi(self, img_path):
        """
        将图片推送到nomi
        :param img_path: 图片路径
        :return:
        """
        h264_file = self.video_combine.create_h264(img_path)
        # 复制
        self.copy_file2nomi(h264_file)
        # sleep 0.5
        time.sleep(0.5)
        # 运动
        self.nomiDevice.replay_animation()

    def crop_to_square(self, image):
        """
        裁切图像为1:1的比例。
        """
        h, w, _ = image.shape
        diff = abs(h - w)

        if h > w:
            top = diff // 2
            bottom = h - (diff - top)
            return image[top:bottom, :]
        else:
            left = diff // 2
            right = w - (diff - left)
            return image[:, left:right]

    def extract_frames(self, video_path, output_folder, frames_per_second=10):
        """
        从视频中每秒提取指定数量的帧。

        :param video_path: 视频文件的路径。
        :param output_folder: 存储提取帧的文件夹。
        :param frames_per_second: 每秒要提取的帧数，默认为10。
        """
        # 确保输出文件夹存在
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
        # else:
        #     #  删除文件夹中的所有文件
        #     for f in os.listdir(output_folder):
        #         os.remove(os.path.join(output_folder, f))
        if video_path.endswith(".jpg") or video_path.endswith(".png"):
            count = 50
            # 打开图片文件
            frame = cv2.imread(video_path)
            resized_frame = cv2.resize(frame, (480, 480))
            # 保存图片 resized_frame
            for i in range(count):
                frame_filename = os.path.join(output_folder, f"frame_{str(count).zfill(5)}.jpg")
                cv2.imwrite(frame_filename, resized_frame)
                # shutil.copy(video_path,os.path.join(output_folder,f"frame_{str(i).zfill(5)}.jpg"))
            return count
        # 打开视频文件
        cap = cv2.VideoCapture(video_path)

        if not cap.isOpened():
            print("Error: Cannot open video.")
            return

        # 获取视频的帧率和总帧数
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        # 计算每隔多少帧提取一次
        frame_gap = int(fps / frames_per_second)

        print(f"Total frames: {frame_count}, FPS: {fps}, Extracting every {frame_gap} frames.")

        count = 0
        while True:
            ret, frame = cap.read()
            # 如果读取失败，说明视频结束或出现了问题
            if not ret or count >= 300:
                break
            # 每隔一定帧数保存一帧
            if count % frame_gap == 0:
                square_frame = self.crop_to_square(frame)
                resized_frame = cv2.resize(square_frame, (480, 480))
                frame_filename = os.path.join(output_folder, f"frame_{str(count).zfill(5)}.jpg")
                cv2.imwrite(frame_filename, resized_frame)
                # print(f"Extracted frame {count}")
            count += 1
        # 释放视频文件
        cap.release()
        # print("Extraction complete.")
        return count

    def fix_nomi_video_data(self, pic_num,
                            pitch_type="delay",
                            yaw_type="delay",
                            yaw_moveRange=207,
                            pitch_moveRange=0,
                            loop=0,
                            IndIn=30, IndOut=50
                            ):
        simbox_drive = self.find_simbox_drive()
        indent = 4
        pitch_way = {
            "name": "goto1",
            "direction": "pitch",
            "type": pitch_type,
            "loop": loop,
            "time": 40 * pic_num,
            "moveRange": pitch_moveRange,
            "Ind.In": IndIn,
            "Ind.Out": IndOut
        }
        yaw_way = {
            "name": "delay-1",
            "direction": "yaw",
            # "type": "Delay",# 禁止
            "type": yaw_type,  # Go to 运动
            "loop": loop,
            "time": 40 * pic_num,
            "moveRange": yaw_moveRange,
            "Ind.In": IndIn,
            "Ind.Out": IndOut
        }
        simbox_data = {
            "name": "test",
            "playback": {
                "name": "test.h264",
                "minRangeMs": 0,
                "maxRangeMs": 40 * pic_num,
                "kinetics": {
                    "pitch": [
                        {
                            "name": "delay-1",
                            "minRangeMs": 0,
                            "maxRangeMs": 40 * pic_num,
                            "kineticSequence": "/pitch/goto1.json"
                        }
                    ],
                    "yaw": [
                        {
                            "name": "delay-1",
                            "minRangeMs": 0,
                            "maxRangeMs": 40 * pic_num,
                            "kineticSequence": "/yaw/delay-1.json"
                        }
                    ]
                }
            }
        }
        # 修改参数

        with open(os.path.join(simbox_drive, "simbox.json"), "w") as f:
            f.write(json.dumps(simbox_data, indent=indent))
        with open(os.path.join(simbox_drive, "pitch", "goto1.json"), "w") as f:
            f.write(json.dumps(pitch_way, indent=indent))
        with open(os.path.join(simbox_drive, "yaw", "delay-1.json"), "w") as f:
            f.write(json.dumps(yaw_way, indent=indent))

    def push_img_rotate2mate3(self, img_path="", yaw_angle=0, pitch_angle=0):
        output_folder = os.path.join(os.getcwd(), "images", "tmp")
        video_dir = os.path.join(output_folder, "video")
        if not os.path.exists(video_dir):
            os.mkdir(video_dir)
        else:
            # 删除video_dir下所有文件
            for file in os.listdir(video_dir):
                os.remove(os.path.join(video_dir, file))
        pic_num = self.extract_frames(img_path, video_dir, frames_per_second=10)
        h264_file = self.video_combine.create_h264_from_folder(video_dir, fps=10)
        # print("video h264 file is:", h264_file)
        self.copy_file2nomi(h264_file)

        self.fix_nomi_video_data(pic_num, pitch_type="Go to", yaw_type="Go to",
                                 yaw_moveRange=yaw_angle,
                                 pitch_moveRange=pitch_angle,
                                 )

        time.sleep(0.5)
        self.nomiDevice.replay_animation()

    def push_video2nomi(self, video_path):
        # 将传入的视屏文件切分为图片
        output_folder = os.path.join(os.getcwd(), "images", "tmp")
        video_dir = os.path.join(output_folder, "video")
        if not os.path.exists(video_dir):
            os.mkdir(video_dir)
        else:
            # 删除video_dir下所有文件
            for file in os.listdir(video_dir):
                os.remove(os.path.join(video_dir, file))
        pic_num = self.extract_frames(video_path, video_dir, frames_per_second=10)
        h264_file = self.video_combine.create_h264_from_folder(video_dir, fps=10)
        print("video h264 file is:", h264_file)
        self.copy_file2nomi(h264_file)

        self.fix_nomi_video_data(pic_num)

        time.sleep(0.5)
        self.nomiDevice.replay_animation()

    def push_wb2nomi(self):
        h264_file = os.path.join(os.getcwd(), "images", "wb_160s.h264")
        pic_num = 4000  # 160秒视屏
        self.copy_file2nomi(h264_file)

        self.fix_nomi_video_data(pic_num)

        time.sleep(0.5)
        self.nomiDevice.replay_animation()

    def set_background_color(self, r=0, g=0, b=0):
        # 根据给定的rgb值生成一个纯色的图片 480*480 大小
        img = Image.new('RGB', (480, 480), (r, g, b))
        img.save(os.path.join(os.getcwd(), "images", "tmp", "background.png"))
        # 复制
        self.push_img2nomi(os.path.join(os.getcwd(), "images", "tmp", "background.png"))
        # 运动
        # nomiDevice.replay_animation()

    def disconnect_nomi(self):
        self._is_connect = self.nomiDevice.on_close()
        return self._is_connect

    def calibration(self):
        self.communication_manager.set_request_calibrate_state(1)

    def is_connect(self):
        return self._is_connect


simbox_control: SimboxControl = SimboxControl()
