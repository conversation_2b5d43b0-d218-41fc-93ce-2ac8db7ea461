"""
模块管理器 - 实现按需加载不同模块的命令处理器
"""
import logging
from typing import Dict, Any, Optional, Set
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class ModuleHandler(ABC):
    """模块处理器基类"""
    
    def __init__(self):
        self._initialized = False
        self._resources_loaded = False
    
    @abstractmethod
    def get_supported_commands(self) -> Set[str]:
        """获取支持的命令列表"""
        pass
    
    @abstractmethod
    def _load_resources(self):
        """加载模块资源（懒加载）"""
        pass
    
    @abstractmethod
    def execute_command(self, command: str, step: Dict[str, Any]):
        """执行命令"""
        pass
    
    def _ensure_resources_loaded(self):
        """确保资源已加载"""
        if not self._resources_loaded:
            try:
                self._load_resources()
                self._resources_loaded = True
                logger.info(f"{self.__class__.__name__} 资源加载完成")
            except Exception as e:
                logger.error(f"{self.__class__.__name__} 资源加载失败: {e}")
                raise
    
    def execute(self, command: str, step: Dict[str, Any]):
        """执行命令（确保资源已加载）"""
        self._ensure_resources_loaded()
        return self.execute_command(command, step)


class ModuleManager:
    """模块管理器 - 按需加载模块"""
    
    def __init__(self):
        self._modules: Dict[str, ModuleHandler] = {}
        self._command_to_module: Dict[str, str] = {}
        self._module_configs = {
            'adb': {
                'class_path': 'case.modules.adb_module.AdbModule',
                'commands': {
                    'SendCANMsg', 'SendCycleCANMsg', 'SendLINMsg', 'SendCycleLINMsg',
                    'SwitchColor', 'SwitchBrightness', 'SwitchSleep', 'SwitchWakeup',
                    'TpcmTest', 'SwitchBackLight', 'ReadBackLightStatus',
                    'SwitchBistPattern', 'ReadBistPatternStatus', 'DisplayReboot',
                    'TconReset', 'ExecuteAdbCmd', 'SerialProtocolTest'
                }
            },
            'power': {
                'class_path': 'case.modules.power_module.PowerModule',
                'commands': {
                    'SwitchPower', 'SwitchVoltage', 'SwitchStepVoltage',
                    'ReadWorkCurrent', 'ReadPeriodWorkCurrent', 'ReadWorkVoltage',
                    'MonitorMultiChannelWorkCurrent', 'ExecuteLoopPowerOnOff'
                }
            },
            'vision': {
                'class_path': 'case.modules.vision_module.VisionModule',
                'commands': {
                    'StartRecord', 'StopRecord', 'StartCollectVisionBrightness',
                    'DetectVisionBrightness', 'RecordImageAlgorithm'
                }
            },
            'touch': {
                'class_path': 'case.modules.touch_module.TouchModule',
                'commands': {
                    'TouchStillTest', 'TouchMarkingTest', 'TouchPointsDetect',
                    'TouchRespTimes', 'TouchRespTime', 'TouchReportRate',
                    'ServoMotorPositioning', 'CheckerboardClick'
                }
            },
            'optical': {
                'class_path': 'case.modules.optical_module.OpticalModule',
                'commands': {
                    'PhoticsGammaCurve', 'PhoticsBrightnessCurve', 'PhoticsContrastRatio',
                    'PhoticsColourGamut', 'PhoticsUniformity', 'ReadNitBrightness',
                    'ReadColorCoordinates', 'CalibrateColorTemperature', 'TestColorTemperature',
                    'CalibrateBrightness', 'TestBrightness', 'M410Test--GAMMA_CURVE',
                    'M410Test--BRIGHTNESS_CURVE', 'M410Test--CONTRAST_RATIO',
                    'M410Test--COLOUR_GAMUT', 'M410Test--UNIFORMITY',
                    'M410--9PointsColor', 'M410--9PointsBrightness',
                    'M410--SinglePointBrightness', 'M410--FlickerTest',
                    'M410--ResponseTimeTest', 'SwitchColorAnalyzerChannel',
                    'LightSensorAutoTest', 'WhiteBalanceTest'
                }
            },
            'system': {
                'class_path': 'case.modules.system_module.SystemModule',
                'commands': {
                    'SetDelayTime', 'SetRandomDelayTime', 'UpdateFwByRandom',
                    'UpdateFwByStep', 'ExecuteBat', 'ExecuteBlockBat',
                    'StartAutoCyclePress', 'StopAutoCyclePress',
                    'StartCustomCyclePress', 'StopCustomCyclePress',
                    'SetRelayStatus', 'EnvTemperatureTest', 'DetectAngle',
                    'recordCanMsg', 'LinSender', 'LinStopSender',
                    'StartProcessMonitor', 'StopProcessMonitor',
                    'SetOscilloscopeDelayStop', 'MarkDtcNormalMsg'
                }
            },
            'can': {
                'class_path': 'case.modules.can_module.CanModule',
                'commands': {
                    'StopCycleCANMsg', 'ClearCycleCANMsg', 'SecurityLevel',
                    'SecurityLevelEnter', 'SetCanMsgDelayTime', 'CanProtocolStartApp',
                    'CanProtocolStopApp', 'CanProtocolAddTestID', 'CheckCanMsgThreading',
                    'PushExceptCanMsg', 'TMfingerClick'
                }
            },
            'log': {
                'class_path': 'case.modules.log_module.LogModule',
                'commands': {
                    'DetectMcuLog', 'DetectSocLog', 'DetectOsLog', 'DetectVdsAppLog',
                    'DetectMcuError', 'DetectSocError', 'DetectOsError', 'DetectVdsAppError'
                }
            },
            'misc': {
                'class_path': 'case.modules.misc_module.MiscModule',
                'commands': {
                    'I2cChecksumDetect', 'LightSensorCurve', 'TemperatureRise'
                }
            }
        }
        
        # 构建命令到模块的映射
        for module_name, config in self._module_configs.items():
            for command in config['commands']:
                self._command_to_module[command] = module_name
    
    def _load_module(self, module_name: str) -> Optional[ModuleHandler]:
        """按需加载模块"""
        if module_name in self._modules:
            return self._modules[module_name]
        
        config = self._module_configs.get(module_name)
        if not config:
            logger.error(f"未找到模块配置: {module_name}")
            return None
        
        try:
            # 动态导入模块类
            module_path, class_name = config['class_path'].rsplit('.', 1)
            module = __import__(module_path, fromlist=[class_name])
            module_class = getattr(module, class_name)
            
            # 创建模块实例
            module_instance = module_class()
            self._modules[module_name] = module_instance
            
            logger.info(f"模块 {module_name} 加载成功")
            return module_instance
            
        except Exception as e:
            logger.error(f"模块 {module_name} 加载失败: {e}")
            return None
    
    def has_command(self, command: str) -> bool:
        """检查是否支持指定命令"""
        return command in self._command_to_module
    
    def execute_command(self, command: str, step: Dict[str, Any]) -> bool:
        """执行命令"""
        module_name = self._command_to_module.get(command)
        if not module_name:
            logger.warning(f"未找到命令对应的模块: {command}")
            return False
        
        # 按需加载模块
        module = self._load_module(module_name)
        if not module:
            logger.error(f"模块加载失败: {module_name}")
            return False
        
        try:
            module.execute(command, step)
            return True
        except Exception as e:
            logger.error(f"命令执行失败 {command}: {e}")
            # 发送失败信号
            case_number = step.get("case_number", "")
            from utils.SignalsManager import signals_manager
            signals_manager.step_execute_finish.emit(case_number, command, "NG", f"命令执行异常: {str(e)}")
            return False
    
    def get_loaded_modules(self) -> Set[str]:
        """获取已加载的模块列表"""
        return set(self._modules.keys())
    
    def get_all_commands(self) -> Set[str]:
        """获取所有支持的命令"""
        return set(self._command_to_module.keys())


# 全局模块管理器实例
module_manager = ModuleManager()
