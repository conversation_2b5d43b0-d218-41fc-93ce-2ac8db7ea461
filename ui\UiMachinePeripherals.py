# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'UiMachinePeripherals.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_MachinePeripherals(object):
    def setupUi(self, MachinePeripherals):
        MachinePeripherals.setObjectName("MachinePeripherals")
        MachinePeripherals.resize(1200, 800)
        MachinePeripherals.setMinimumSize(QtCore.QSize(1200, 800))
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(MachinePeripherals)
        self.verticalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.frame_3 = QtWidgets.QFrame(MachinePeripherals)
        font = QtGui.QFont()
        font.setPointSize(12)
        self.frame_3.setFont(font)
        self.frame_3.setObjectName("frame_3")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.frame_3)
        self.verticalLayout_6.setContentsMargins(40, 0, 20, 0)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.frame_2 = QtWidgets.QFrame(self.frame_3)
        self.frame_2.setMinimumSize(QtCore.QSize(0, 35))
        self.frame_2.setMaximumSize(QtCore.QSize(16777215, 35))
        self.frame_2.setStyleSheet("border:none")
        self.frame_2.setObjectName("frame_2")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.frame_2)
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.label_6 = QtWidgets.QLabel(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_6.sizePolicy().hasHeightForWidth())
        self.label_6.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(False)
        font.setWeight(50)
        self.label_6.setFont(font)
        self.label_6.setStyleSheet("")
        self.label_6.setObjectName("label_6")
        self.horizontalLayout.addWidget(self.label_6)
        self.machine_number_label = QtWidgets.QLabel(self.frame_2)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(False)
        font.setWeight(50)
        self.machine_number_label.setFont(font)
        self.machine_number_label.setStyleSheet("")
        self.machine_number_label.setText("")
        self.machine_number_label.setObjectName("machine_number_label")
        self.horizontalLayout.addWidget(self.machine_number_label)
        self.label_4 = QtWidgets.QLabel(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_4.sizePolicy().hasHeightForWidth())
        self.label_4.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(False)
        font.setWeight(50)
        self.label_4.setFont(font)
        self.label_4.setStyleSheet("")
        self.label_4.setObjectName("label_4")
        self.horizontalLayout.addWidget(self.label_4)
        self.machine_name_label = QtWidgets.QLabel(self.frame_2)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(False)
        font.setWeight(50)
        self.machine_name_label.setFont(font)
        self.machine_name_label.setStyleSheet("")
        self.machine_name_label.setText("")
        self.machine_name_label.setObjectName("machine_name_label")
        self.horizontalLayout.addWidget(self.machine_name_label)
        self.label_2 = QtWidgets.QLabel(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_2.sizePolicy().hasHeightForWidth())
        self.label_2.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(False)
        font.setWeight(50)
        self.label_2.setFont(font)
        self.label_2.setStyleSheet("")
        self.label_2.setObjectName("label_2")
        self.horizontalLayout.addWidget(self.label_2)
        self.maintainer_label = QtWidgets.QLabel(self.frame_2)
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(False)
        font.setWeight(50)
        self.maintainer_label.setFont(font)
        self.maintainer_label.setStyleSheet("")
        self.maintainer_label.setText("")
        self.maintainer_label.setObjectName("maintainer_label")
        self.horizontalLayout.addWidget(self.maintainer_label)
        self.verticalLayout_6.addWidget(self.frame_2)
        self.verticalLayout_3.addWidget(self.frame_3)
        self.frame_4 = QtWidgets.QFrame(MachinePeripherals)
        self.frame_4.setObjectName("frame_4")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.frame_4)
        self.horizontalLayout_2.setContentsMargins(10, 10, 10, 10)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.frame = QtWidgets.QFrame(self.frame_4)
        self.frame.setMinimumSize(QtCore.QSize(300, 100))
        self.frame.setStyleSheet("")
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout_8 = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout_8.setContentsMargins(6, 6, 6, 6)
        self.verticalLayout_8.setSpacing(6)
        self.verticalLayout_8.setObjectName("verticalLayout_8")
        self.frameSerial = QtWidgets.QFrame(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frameSerial.sizePolicy().hasHeightForWidth())
        self.frameSerial.setSizePolicy(sizePolicy)
        self.frameSerial.setStyleSheet("")
        self.frameSerial.setObjectName("frameSerial")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.frameSerial)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_14.setContentsMargins(0, 0, -1, -1)
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        spacerItem = QtWidgets.QSpacerItem(10, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_14.addItem(spacerItem)
        self.tableWidgetSerial = QtWidgets.QTableWidget(self.frameSerial)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.tableWidgetSerial.sizePolicy().hasHeightForWidth())
        self.tableWidgetSerial.setSizePolicy(sizePolicy)
        self.tableWidgetSerial.setStyleSheet("")
        self.tableWidgetSerial.setAutoScrollMargin(16)
        self.tableWidgetSerial.setObjectName("tableWidgetSerial")
        self.tableWidgetSerial.setColumnCount(0)
        self.tableWidgetSerial.setRowCount(0)
        self.horizontalLayout_14.addWidget(self.tableWidgetSerial)
        self.verticalLayout.addLayout(self.horizontalLayout_14)
        self.verticalLayout_8.addWidget(self.frameSerial)
        self.frameCanLin = QtWidgets.QFrame(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frameCanLin.sizePolicy().hasHeightForWidth())
        self.frameCanLin.setSizePolicy(sizePolicy)
        self.frameCanLin.setStyleSheet("")
        self.frameCanLin.setObjectName("frameCanLin")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.frameCanLin)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.horizontalLayout_15 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_15.setContentsMargins(0, 0, -1, -1)
        self.horizontalLayout_15.setObjectName("horizontalLayout_15")
        spacerItem1 = QtWidgets.QSpacerItem(10, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_15.addItem(spacerItem1)
        self.tableWidgetCanlin = QtWidgets.QTableWidget(self.frameCanLin)
        self.tableWidgetCanlin.setStyleSheet("")
        self.tableWidgetCanlin.setObjectName("tableWidgetCanlin")
        self.tableWidgetCanlin.setColumnCount(0)
        self.tableWidgetCanlin.setRowCount(0)
        self.horizontalLayout_15.addWidget(self.tableWidgetCanlin)
        self.verticalLayout_4.addLayout(self.horizontalLayout_15)
        self.verticalLayout_8.addWidget(self.frameCanLin)
        self.frameCamera = QtWidgets.QFrame(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frameCamera.sizePolicy().hasHeightForWidth())
        self.frameCamera.setSizePolicy(sizePolicy)
        self.frameCamera.setStyleSheet("")
        self.frameCamera.setObjectName("frameCamera")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.frameCamera)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.horizontalLayout_16 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_16.setContentsMargins(0, 0, -1, -1)
        self.horizontalLayout_16.setObjectName("horizontalLayout_16")
        spacerItem2 = QtWidgets.QSpacerItem(10, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_16.addItem(spacerItem2)
        self.tableWidgetCamera = QtWidgets.QTableWidget(self.frameCamera)
        self.tableWidgetCamera.setStyleSheet("")
        self.tableWidgetCamera.setObjectName("tableWidgetCamera")
        self.tableWidgetCamera.setColumnCount(0)
        self.tableWidgetCamera.setRowCount(0)
        self.horizontalLayout_16.addWidget(self.tableWidgetCamera)
        self.verticalLayout_5.addLayout(self.horizontalLayout_16)
        self.verticalLayout_8.addWidget(self.frameCamera)
        self.frameNet = QtWidgets.QFrame(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frameNet.sizePolicy().hasHeightForWidth())
        self.frameNet.setSizePolicy(sizePolicy)
        self.frameNet.setStyleSheet("")
        self.frameNet.setObjectName("frameNet")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.frameNet)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.horizontalLayout_17 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_17.setContentsMargins(0, 0, -1, -1)
        self.horizontalLayout_17.setObjectName("horizontalLayout_17")
        spacerItem3 = QtWidgets.QSpacerItem(10, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_17.addItem(spacerItem3)
        self.tableWidgetNet = QtWidgets.QTableWidget(self.frameNet)
        self.tableWidgetNet.setStyleSheet("")
        self.tableWidgetNet.setObjectName("tableWidgetNet")
        self.tableWidgetNet.setColumnCount(0)
        self.tableWidgetNet.setRowCount(0)
        self.horizontalLayout_17.addWidget(self.tableWidgetNet)
        self.verticalLayout_2.addLayout(self.horizontalLayout_17)
        self.verticalLayout_8.addWidget(self.frameNet)
        self.verticalLayout_8.setStretch(0, 30)
        self.verticalLayout_8.setStretch(1, 13)
        self.verticalLayout_8.setStretch(2, 5)
        self.verticalLayout_8.setStretch(3, 11)
        self.horizontalLayout_2.addWidget(self.frame)
        self.frame_5 = QtWidgets.QFrame(self.frame_4)
        self.frame_5.setStyleSheet("")
        self.frame_5.setObjectName("frame_5")
        self.verticalLayout_7 = QtWidgets.QVBoxLayout(self.frame_5)
        self.verticalLayout_7.setContentsMargins(10, 10, 10, 10)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.frame_6 = QtWidgets.QFrame(self.frame_5)
        self.frame_6.setMinimumSize(QtCore.QSize(0, 60))
        self.frame_6.setObjectName("frame_6")
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout(self.frame_6)
        self.horizontalLayout_8.setContentsMargins(-1, 0, -1, -1)
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.light_source_1_label = QtWidgets.QLabel(self.frame_6)
        self.light_source_1_label.setText("")
        self.light_source_1_label.setObjectName("light_source_1_label")
        self.horizontalLayout_8.addWidget(self.light_source_1_label)
        self.label_7 = QtWidgets.QLabel(self.frame_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_7.sizePolicy().hasHeightForWidth())
        self.label_7.setSizePolicy(sizePolicy)
        self.label_7.setStyleSheet("")
        self.label_7.setObjectName("label_7")
        self.horizontalLayout_8.addWidget(self.label_7)
        self.light_source_1_slider = QtWidgets.QSlider(self.frame_6)
        self.light_source_1_slider.setMaximum(100)
        self.light_source_1_slider.setOrientation(QtCore.Qt.Horizontal)
        self.light_source_1_slider.setObjectName("light_source_1_slider")
        self.horizontalLayout_8.addWidget(self.light_source_1_slider)
        self.light_source_1_value_label = QtWidgets.QLabel(self.frame_6)
        self.light_source_1_value_label.setMinimumSize(QtCore.QSize(60, 0))
        self.light_source_1_value_label.setStyleSheet("")
        self.light_source_1_value_label.setAlignment(QtCore.Qt.AlignCenter)
        self.light_source_1_value_label.setObjectName("light_source_1_value_label")
        self.horizontalLayout_8.addWidget(self.light_source_1_value_label)
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_8.addItem(spacerItem4)
        self.light_source_2_label = QtWidgets.QLabel(self.frame_6)
        self.light_source_2_label.setText("")
        self.light_source_2_label.setObjectName("light_source_2_label")
        self.horizontalLayout_8.addWidget(self.light_source_2_label)
        self.label = QtWidgets.QLabel(self.frame_6)
        self.label.setStyleSheet("")
        self.label.setObjectName("label")
        self.horizontalLayout_8.addWidget(self.label)
        self.light_source_2_slider = QtWidgets.QSlider(self.frame_6)
        self.light_source_2_slider.setMaximum(100)
        self.light_source_2_slider.setOrientation(QtCore.Qt.Horizontal)
        self.light_source_2_slider.setObjectName("light_source_2_slider")
        self.horizontalLayout_8.addWidget(self.light_source_2_slider)
        self.light_source_2_value_label = QtWidgets.QLabel(self.frame_6)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.light_source_2_value_label.sizePolicy().hasHeightForWidth())
        self.light_source_2_value_label.setSizePolicy(sizePolicy)
        self.light_source_2_value_label.setMinimumSize(QtCore.QSize(60, 0))
        self.light_source_2_value_label.setStyleSheet("")
        self.light_source_2_value_label.setAlignment(QtCore.Qt.AlignCenter)
        self.light_source_2_value_label.setObjectName("light_source_2_value_label")
        self.horizontalLayout_8.addWidget(self.light_source_2_value_label)
        self.verticalLayout_7.addWidget(self.frame_6)
        spacerItem5 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_7.addItem(spacerItem5)
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_7.setSpacing(6)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        spacerItem6 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_7.addItem(spacerItem6)
        self.init_btn = QtWidgets.QPushButton(self.frame_5)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.init_btn.sizePolicy().hasHeightForWidth())
        self.init_btn.setSizePolicy(sizePolicy)
        self.init_btn.setMinimumSize(QtCore.QSize(0, 50))
        self.init_btn.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.init_btn.setFont(font)
        self.init_btn.setObjectName("init_btn")
        self.horizontalLayout_7.addWidget(self.init_btn)
        spacerItem7 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_7.addItem(spacerItem7)
        self.start_btn = QtWidgets.QPushButton(self.frame_5)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.start_btn.sizePolicy().hasHeightForWidth())
        self.start_btn.setSizePolicy(sizePolicy)
        self.start_btn.setMinimumSize(QtCore.QSize(0, 50))
        self.start_btn.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.start_btn.setFont(font)
        self.start_btn.setStyleSheet("")
        self.start_btn.setObjectName("start_btn")
        self.horizontalLayout_7.addWidget(self.start_btn)
        spacerItem8 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_7.addItem(spacerItem8)
        self.stop_btn = QtWidgets.QPushButton(self.frame_5)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.stop_btn.sizePolicy().hasHeightForWidth())
        self.stop_btn.setSizePolicy(sizePolicy)
        self.stop_btn.setMinimumSize(QtCore.QSize(0, 50))
        self.stop_btn.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.stop_btn.setFont(font)
        self.stop_btn.setObjectName("stop_btn")
        self.horizontalLayout_7.addWidget(self.stop_btn)
        spacerItem9 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_7.addItem(spacerItem9)
        self.reset_btn = QtWidgets.QPushButton(self.frame_5)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.reset_btn.sizePolicy().hasHeightForWidth())
        self.reset_btn.setSizePolicy(sizePolicy)
        self.reset_btn.setMinimumSize(QtCore.QSize(0, 50))
        self.reset_btn.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.reset_btn.setFont(font)
        self.reset_btn.setObjectName("reset_btn")
        self.horizontalLayout_7.addWidget(self.reset_btn)
        spacerItem10 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_7.addItem(spacerItem10)
        self.horizontalLayout_7.setStretch(0, 1)
        self.horizontalLayout_7.setStretch(1, 1)
        self.horizontalLayout_7.setStretch(2, 1)
        self.horizontalLayout_7.setStretch(3, 1)
        self.horizontalLayout_7.setStretch(4, 1)
        self.horizontalLayout_7.setStretch(5, 1)
        self.horizontalLayout_7.setStretch(6, 1)
        self.horizontalLayout_7.setStretch(7, 1)
        self.horizontalLayout_7.setStretch(8, 1)
        self.verticalLayout_7.addLayout(self.horizontalLayout_7)
        spacerItem11 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_7.addItem(spacerItem11)
        self.frame_7 = QtWidgets.QFrame(self.frame_5)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frame_7.sizePolicy().hasHeightForWidth())
        self.frame_7.setSizePolicy(sizePolicy)
        self.frame_7.setMinimumSize(QtCore.QSize(0, 50))
        self.frame_7.setObjectName("frame_7")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.frame_7)
        self.horizontalLayout_6.setContentsMargins(10, 10, 10, 10)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        spacerItem12 = QtWidgets.QSpacerItem(50, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_6.addItem(spacerItem12)
        self.label_10 = QtWidgets.QLabel(self.frame_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_10.sizePolicy().hasHeightForWidth())
        self.label_10.setSizePolicy(sizePolicy)
        self.label_10.setMinimumSize(QtCore.QSize(0, 0))
        self.label_10.setStyleSheet("")
        self.label_10.setObjectName("label_10")
        self.horizontalLayout_6.addWidget(self.label_10)
        self.exposure_lineedit = QtWidgets.QLineEdit(self.frame_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.exposure_lineedit.sizePolicy().hasHeightForWidth())
        self.exposure_lineedit.setSizePolicy(sizePolicy)
        self.exposure_lineedit.setMinimumSize(QtCore.QSize(0, 35))
        self.exposure_lineedit.setStyleSheet("border:none")
        self.exposure_lineedit.setObjectName("exposure_lineedit")
        self.horizontalLayout_6.addWidget(self.exposure_lineedit)
        spacerItem13 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_6.addItem(spacerItem13)
        self.horizontalLayout_6.setStretch(1, 2)
        self.horizontalLayout_6.setStretch(2, 6)
        self.verticalLayout_7.addWidget(self.frame_7)
        spacerItem14 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_7.addItem(spacerItem14)
        self.horizontalFrame = QtWidgets.QFrame(self.frame_5)
        self.horizontalFrame.setMinimumSize(QtCore.QSize(0, 50))
        self.horizontalFrame.setObjectName("horizontalFrame")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.horizontalFrame)
        self.horizontalLayout_5.setContentsMargins(10, 10, 10, 10)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        spacerItem15 = QtWidgets.QSpacerItem(50, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_5.addItem(spacerItem15)
        self.label_9 = QtWidgets.QLabel(self.horizontalFrame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_9.sizePolicy().hasHeightForWidth())
        self.label_9.setSizePolicy(sizePolicy)
        self.label_9.setStyleSheet("")
        self.label_9.setObjectName("label_9")
        self.horizontalLayout_5.addWidget(self.label_9)
        self.grainy_screen_detect_value_lineedit = QtWidgets.QLineEdit(self.horizontalFrame)
        self.grainy_screen_detect_value_lineedit.setMinimumSize(QtCore.QSize(0, 35))
        self.grainy_screen_detect_value_lineedit.setStyleSheet("border:none")
        self.grainy_screen_detect_value_lineedit.setObjectName("grainy_screen_detect_value_lineedit")
        self.horizontalLayout_5.addWidget(self.grainy_screen_detect_value_lineedit)
        spacerItem16 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_5.addItem(spacerItem16)
        self.horizontalLayout_5.setStretch(1, 2)
        self.horizontalLayout_5.setStretch(2, 6)
        self.verticalLayout_7.addWidget(self.horizontalFrame)
        spacerItem17 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_7.addItem(spacerItem17)
        self.horizontalFrame_2 = QtWidgets.QFrame(self.frame_5)
        self.horizontalFrame_2.setMinimumSize(QtCore.QSize(0, 50))
        self.horizontalFrame_2.setObjectName("horizontalFrame_2")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.horizontalFrame_2)
        self.horizontalLayout_11.setContentsMargins(10, 10, 10, 10)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        spacerItem18 = QtWidgets.QSpacerItem(50, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem18)
        self.label_12 = QtWidgets.QLabel(self.horizontalFrame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_12.sizePolicy().hasHeightForWidth())
        self.label_12.setSizePolicy(sizePolicy)
        self.label_12.setStyleSheet("")
        self.label_12.setObjectName("label_12")
        self.horizontalLayout_11.addWidget(self.label_12)
        self.flicker_screen_detect_value_lineedit = QtWidgets.QLineEdit(self.horizontalFrame_2)
        self.flicker_screen_detect_value_lineedit.setMinimumSize(QtCore.QSize(0, 35))
        self.flicker_screen_detect_value_lineedit.setStyleSheet("border:none")
        self.flicker_screen_detect_value_lineedit.setObjectName("flicker_screen_detect_value_lineedit")
        self.horizontalLayout_11.addWidget(self.flicker_screen_detect_value_lineedit)
        spacerItem19 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_11.addItem(spacerItem19)
        self.horizontalLayout_11.setStretch(1, 2)
        self.horizontalLayout_11.setStretch(2, 6)
        self.verticalLayout_7.addWidget(self.horizontalFrame_2)
        spacerItem20 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_7.addItem(spacerItem20)
        self.horizontalFrame_3 = QtWidgets.QFrame(self.frame_5)
        self.horizontalFrame_3.setMinimumSize(QtCore.QSize(0, 50))
        self.horizontalFrame_3.setObjectName("horizontalFrame_3")
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout(self.horizontalFrame_3)
        self.horizontalLayout_12.setContentsMargins(10, 10, 10, 10)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        spacerItem21 = QtWidgets.QSpacerItem(50, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_12.addItem(spacerItem21)
        self.label_22 = QtWidgets.QLabel(self.horizontalFrame_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_22.sizePolicy().hasHeightForWidth())
        self.label_22.setSizePolicy(sizePolicy)
        self.label_22.setStyleSheet("")
        self.label_22.setObjectName("label_22")
        self.horizontalLayout_12.addWidget(self.label_22)
        self.black_screen_detect_value_lineedit = QtWidgets.QLineEdit(self.horizontalFrame_3)
        self.black_screen_detect_value_lineedit.setMinimumSize(QtCore.QSize(0, 35))
        self.black_screen_detect_value_lineedit.setStyleSheet("border:none")
        self.black_screen_detect_value_lineedit.setObjectName("black_screen_detect_value_lineedit")
        self.horizontalLayout_12.addWidget(self.black_screen_detect_value_lineedit)
        spacerItem22 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_12.addItem(spacerItem22)
        self.horizontalLayout_12.setStretch(1, 2)
        self.horizontalLayout_12.setStretch(2, 6)
        self.verticalLayout_7.addWidget(self.horizontalFrame_3)
        spacerItem23 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_7.addItem(spacerItem23)
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        spacerItem24 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_13.addItem(spacerItem24)
        self.fresh_port_btn = QtWidgets.QPushButton(self.frame_5)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.fresh_port_btn.sizePolicy().hasHeightForWidth())
        self.fresh_port_btn.setSizePolicy(sizePolicy)
        self.fresh_port_btn.setMinimumSize(QtCore.QSize(0, 50))
        self.fresh_port_btn.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.fresh_port_btn.setFont(font)
        self.fresh_port_btn.setObjectName("fresh_port_btn")
        self.horizontalLayout_13.addWidget(self.fresh_port_btn)
        spacerItem25 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_13.addItem(spacerItem25)
        self.fresh_camera_btn = QtWidgets.QPushButton(self.frame_5)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.fresh_camera_btn.sizePolicy().hasHeightForWidth())
        self.fresh_camera_btn.setSizePolicy(sizePolicy)
        self.fresh_camera_btn.setMinimumSize(QtCore.QSize(0, 50))
        self.fresh_camera_btn.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.fresh_camera_btn.setFont(font)
        self.fresh_camera_btn.setObjectName("fresh_camera_btn")
        self.horizontalLayout_13.addWidget(self.fresh_camera_btn)
        spacerItem26 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_13.addItem(spacerItem26)
        self.save_camera_config_btn = QtWidgets.QPushButton(self.frame_5)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.save_camera_config_btn.sizePolicy().hasHeightForWidth())
        self.save_camera_config_btn.setSizePolicy(sizePolicy)
        self.save_camera_config_btn.setMinimumSize(QtCore.QSize(0, 50))
        self.save_camera_config_btn.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.save_camera_config_btn.setFont(font)
        self.save_camera_config_btn.setObjectName("save_camera_config_btn")
        self.horizontalLayout_13.addWidget(self.save_camera_config_btn)
        spacerItem27 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_13.addItem(spacerItem27)
        self.vision_calibrate_btn = QtWidgets.QPushButton(self.frame_5)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.vision_calibrate_btn.sizePolicy().hasHeightForWidth())
        self.vision_calibrate_btn.setSizePolicy(sizePolicy)
        self.vision_calibrate_btn.setMinimumSize(QtCore.QSize(0, 50))
        self.vision_calibrate_btn.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.vision_calibrate_btn.setFont(font)
        self.vision_calibrate_btn.setObjectName("vision_calibrate_btn")
        self.horizontalLayout_13.addWidget(self.vision_calibrate_btn)
        spacerItem28 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_13.addItem(spacerItem28)
        self.horizontalLayout_13.setStretch(0, 1)
        self.horizontalLayout_13.setStretch(1, 1)
        self.horizontalLayout_13.setStretch(2, 1)
        self.horizontalLayout_13.setStretch(3, 1)
        self.horizontalLayout_13.setStretch(4, 1)
        self.horizontalLayout_13.setStretch(5, 1)
        self.horizontalLayout_13.setStretch(6, 1)
        self.horizontalLayout_13.setStretch(7, 1)
        self.horizontalLayout_13.setStretch(8, 1)
        self.verticalLayout_7.addLayout(self.horizontalLayout_13)
        spacerItem29 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_7.addItem(spacerItem29)
        self.stylus_table_widget = QtWidgets.QTableWidget(self.frame_5)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.stylus_table_widget.sizePolicy().hasHeightForWidth())
        self.stylus_table_widget.setSizePolicy(sizePolicy)
        self.stylus_table_widget.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.stylus_table_widget.setObjectName("stylus_table_widget")
        self.stylus_table_widget.setColumnCount(2)
        self.stylus_table_widget.setRowCount(10)
        item = QtWidgets.QTableWidgetItem()
        self.stylus_table_widget.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.stylus_table_widget.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.stylus_table_widget.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.stylus_table_widget.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.stylus_table_widget.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.stylus_table_widget.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.stylus_table_widget.setVerticalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.stylus_table_widget.setVerticalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.stylus_table_widget.setVerticalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.stylus_table_widget.setVerticalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        self.stylus_table_widget.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.stylus_table_widget.setHorizontalHeaderItem(1, item)
        self.stylus_table_widget.horizontalHeader().setVisible(True)
        self.stylus_table_widget.verticalHeader().setVisible(False)
        self.verticalLayout_7.addWidget(self.stylus_table_widget)
        self.verticalLayout_7.setStretch(0, 1)
        self.verticalLayout_7.setStretch(4, 1)
        self.verticalLayout_7.setStretch(6, 1)
        self.verticalLayout_7.setStretch(8, 1)
        self.verticalLayout_7.setStretch(10, 1)
        self.verticalLayout_7.setStretch(14, 6)
        self.horizontalLayout_2.addWidget(self.frame_5)
        self.horizontalLayout_2.setStretch(0, 1)
        self.horizontalLayout_2.setStretch(1, 1)
        self.verticalLayout_3.addWidget(self.frame_4)
        self.verticalLayout_3.setStretch(0, 1)
        self.verticalLayout_3.setStretch(1, 20)

        self.retranslateUi(MachinePeripherals)
        QtCore.QMetaObject.connectSlotsByName(MachinePeripherals)

    def retranslateUi(self, MachinePeripherals):
        _translate = QtCore.QCoreApplication.translate
        MachinePeripherals.setWindowTitle(_translate("MachinePeripherals", "系统设置"))
        self.label_6.setText(_translate("MachinePeripherals", "机台编号："))
        self.label_4.setText(_translate("MachinePeripherals", "机台名称："))
        self.label_2.setText(_translate("MachinePeripherals", "维护人员："))
        self.label_7.setText(_translate("MachinePeripherals", "光源亮度1#"))
        self.light_source_1_value_label.setText(_translate("MachinePeripherals", "0%"))
        self.label.setText(_translate("MachinePeripherals", "光源亮度2#"))
        self.light_source_2_value_label.setText(_translate("MachinePeripherals", "0%"))
        self.init_btn.setText(_translate("MachinePeripherals", "初始化"))
        self.start_btn.setText(_translate("MachinePeripherals", "启动"))
        self.stop_btn.setText(_translate("MachinePeripherals", "停止"))
        self.reset_btn.setText(_translate("MachinePeripherals", "复位"))
        self.label_10.setText(_translate("MachinePeripherals", "视觉检测曝光值："))
        self.label_9.setText(_translate("MachinePeripherals", "花屏实际检测值："))
        self.label_12.setText(_translate("MachinePeripherals", "闪屏实际检测值："))
        self.label_22.setText(_translate("MachinePeripherals", "黑屏实际检测值："))
        self.fresh_port_btn.setText(_translate("MachinePeripherals", "刷新串口设备"))
        self.fresh_camera_btn.setText(_translate("MachinePeripherals", "刷新相机"))
        self.save_camera_config_btn.setText(_translate("MachinePeripherals", "保存相机设置"))
        self.vision_calibrate_btn.setText(_translate("MachinePeripherals", "视觉标定"))
        item = self.stylus_table_widget.verticalHeaderItem(0)
        item.setText(_translate("MachinePeripherals", "1"))
        item = self.stylus_table_widget.verticalHeaderItem(1)
        item.setText(_translate("MachinePeripherals", "2"))
        item = self.stylus_table_widget.verticalHeaderItem(2)
        item.setText(_translate("MachinePeripherals", "3"))
        item = self.stylus_table_widget.verticalHeaderItem(3)
        item.setText(_translate("MachinePeripherals", "4"))
        item = self.stylus_table_widget.verticalHeaderItem(4)
        item.setText(_translate("MachinePeripherals", "5"))
        item = self.stylus_table_widget.verticalHeaderItem(5)
        item.setText(_translate("MachinePeripherals", "6"))
        item = self.stylus_table_widget.verticalHeaderItem(6)
        item.setText(_translate("MachinePeripherals", "7"))
        item = self.stylus_table_widget.verticalHeaderItem(7)
        item.setText(_translate("MachinePeripherals", "8"))
        item = self.stylus_table_widget.verticalHeaderItem(8)
        item.setText(_translate("MachinePeripherals", "9"))
        item = self.stylus_table_widget.verticalHeaderItem(9)
        item.setText(_translate("MachinePeripherals", "10"))
        item = self.stylus_table_widget.horizontalHeaderItem(0)
        item.setText(_translate("MachinePeripherals", "触控笔编号"))
        item = self.stylus_table_widget.horizontalHeaderItem(1)
        item.setText(_translate("MachinePeripherals", "状态"))
