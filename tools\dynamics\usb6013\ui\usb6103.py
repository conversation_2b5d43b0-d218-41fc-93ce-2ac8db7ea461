# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'usb6103.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_FormUSB6103(object):
    def setupUi(self, FormUSB6103):
        FormUSB6103.setObjectName("FormUSB6103")
        FormUSB6103.resize(1600, 900)
        FormUSB6103.setMinimumSize(QtCore.QSize(1600, 900))
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(FormUSB6103)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.widget_menu = QtWidgets.QWidget(FormUSB6103)
        self.widget_menu.setStyleSheet("")
        self.widget_menu.setObjectName("widget_menu")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.widget_menu)
        self.verticalLayout_2.setContentsMargins(2, 2, 2, 2)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setObjectName("verticalLayout")
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setObjectName("gridLayout")
        self.label_3 = QtWidgets.QLabel(self.widget_menu)
        self.label_3.setAlignment(QtCore.Qt.AlignCenter)
        self.label_3.setObjectName("label_3")
        self.gridLayout.addWidget(self.label_3, 2, 0, 1, 1)
        self.pushButtonPeeling = QtWidgets.QPushButton(self.widget_menu)
        self.pushButtonPeeling.setObjectName("pushButtonPeeling")
        self.gridLayout.addWidget(self.pushButtonPeeling, 5, 0, 1, 1)
        self.spinBoxChannel = QtWidgets.QSpinBox(self.widget_menu)
        self.spinBoxChannel.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBoxChannel.setMaximum(7)
        self.spinBoxChannel.setObjectName("spinBoxChannel")
        self.gridLayout.addWidget(self.spinBoxChannel, 2, 1, 1, 1)
        self.doubleSpinBoxPeeling = QtWidgets.QDoubleSpinBox(self.widget_menu)
        self.doubleSpinBoxPeeling.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBoxPeeling.setDecimals(1)
        self.doubleSpinBoxPeeling.setMinimum(-99.0)
        self.doubleSpinBoxPeeling.setMaximum(99.0)
        self.doubleSpinBoxPeeling.setProperty("value", 0.0)
        self.doubleSpinBoxPeeling.setObjectName("doubleSpinBoxPeeling")
        self.gridLayout.addWidget(self.doubleSpinBoxPeeling, 5, 1, 1, 1)
        self.label_4 = QtWidgets.QLabel(self.widget_menu)
        self.label_4.setAlignment(QtCore.Qt.AlignCenter)
        self.label_4.setObjectName("label_4")
        self.gridLayout.addWidget(self.label_4, 3, 0, 1, 1)
        self.doubleSpinBoxProportion = QtWidgets.QDoubleSpinBox(self.widget_menu)
        self.doubleSpinBoxProportion.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBoxProportion.setDecimals(1)
        self.doubleSpinBoxProportion.setMaximum(9999.0)
        self.doubleSpinBoxProportion.setProperty("value", 130.0)
        self.doubleSpinBoxProportion.setObjectName("doubleSpinBoxProportion")
        self.gridLayout.addWidget(self.doubleSpinBoxProportion, 1, 1, 1, 1)
        self.label_2 = QtWidgets.QLabel(self.widget_menu)
        self.label_2.setAlignment(QtCore.Qt.AlignCenter)
        self.label_2.setObjectName("label_2")
        self.gridLayout.addWidget(self.label_2, 1, 0, 1, 1)
        self.spinBoxFrequency = QtWidgets.QSpinBox(self.widget_menu)
        self.spinBoxFrequency.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBoxFrequency.setMinimum(10)
        self.spinBoxFrequency.setMaximum(10000)
        self.spinBoxFrequency.setProperty("value", 1000)
        self.spinBoxFrequency.setObjectName("spinBoxFrequency")
        self.gridLayout.addWidget(self.spinBoxFrequency, 4, 1, 1, 1)
        self.labelStatus = QtWidgets.QLabel(self.widget_menu)
        self.labelStatus.setObjectName("labelStatus")
        self.gridLayout.addWidget(self.labelStatus, 0, 1, 1, 1)
        self.pushButtonConnect = QtWidgets.QPushButton(self.widget_menu)
        self.pushButtonConnect.setObjectName("pushButtonConnect")
        self.gridLayout.addWidget(self.pushButtonConnect, 0, 0, 1, 1)
        self.label = QtWidgets.QLabel(self.widget_menu)
        self.label.setAlignment(QtCore.Qt.AlignCenter)
        self.label.setObjectName("label")
        self.gridLayout.addWidget(self.label, 4, 0, 1, 1)
        self.doubleSpinBoxLossPower = QtWidgets.QDoubleSpinBox(self.widget_menu)
        self.doubleSpinBoxLossPower.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBoxLossPower.setDecimals(1)
        self.doubleSpinBoxLossPower.setMaximum(999.0)
        self.doubleSpinBoxLossPower.setProperty("value", 20.0)
        self.doubleSpinBoxLossPower.setObjectName("doubleSpinBoxLossPower")
        self.gridLayout.addWidget(self.doubleSpinBoxLossPower, 3, 1, 1, 1)
        self.verticalLayout.addLayout(self.gridLayout)
        self.lcdNumber = QtWidgets.QLCDNumber(self.widget_menu)
        self.lcdNumber.setMinimumSize(QtCore.QSize(0, 120))
        self.lcdNumber.setObjectName("lcdNumber")
        self.verticalLayout.addWidget(self.lcdNumber)
        self.pushButtonStart = QtWidgets.QPushButton(self.widget_menu)
        self.pushButtonStart.setObjectName("pushButtonStart")
        self.verticalLayout.addWidget(self.pushButtonStart)
        self.pushButtonEnd = QtWidgets.QPushButton(self.widget_menu)
        self.pushButtonEnd.setObjectName("pushButtonEnd")
        self.verticalLayout.addWidget(self.pushButtonEnd)
        self.pushButtonSaveImage = QtWidgets.QPushButton(self.widget_menu)
        self.pushButtonSaveImage.setObjectName("pushButtonSaveImage")
        self.verticalLayout.addWidget(self.pushButtonSaveImage)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem)
        self.verticalLayout_2.addLayout(self.verticalLayout)
        self.horizontalLayout.addWidget(self.widget_menu)
        self.widget_display = QtWidgets.QWidget(FormUSB6103)
        self.widget_display.setStyleSheet("background-color: rgb(47, 47, 47);")
        self.widget_display.setObjectName("widget_display")
        self.horizontalLayout.addWidget(self.widget_display)
        self.horizontalLayout.setStretch(1, 1)
        self.verticalLayout_3.addLayout(self.horizontalLayout)

        self.retranslateUi(FormUSB6103)
        QtCore.QMetaObject.connectSlotsByName(FormUSB6103)

    def retranslateUi(self, FormUSB6103):
        _translate = QtCore.QCoreApplication.translate
        FormUSB6103.setWindowTitle(_translate("FormUSB6103", "防夹测试"))
        self.label_3.setText(_translate("FormUSB6103", "通道"))
        self.pushButtonPeeling.setText(_translate("FormUSB6103", "去皮"))
        self.label_4.setText(_translate("FormUSB6103", "卸力值"))
        self.label_2.setText(_translate("FormUSB6103", "系数比例"))
        self.labelStatus.setText(_translate("FormUSB6103", "连接状态"))
        self.pushButtonConnect.setText(_translate("FormUSB6103", "连接设备"))
        self.label.setText(_translate("FormUSB6103", "采集频率(n/s)"))
        self.pushButtonStart.setText(_translate("FormUSB6103", "开始采集"))
        self.pushButtonEnd.setText(_translate("FormUSB6103", "结束采集"))
        self.pushButtonSaveImage.setText(_translate("FormUSB6103", "导出图片"))
