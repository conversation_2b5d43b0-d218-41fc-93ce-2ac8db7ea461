# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'VoltItemWidget.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtC<PERSON>, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(462, 93)
        self.verticalLayout = QtWidgets.QVBoxLayout(Form)
        self.verticalLayout.setObjectName("verticalLayout")
        self.formLayout = QtWidgets.QFormLayout()
        self.formLayout.setObjectName("formLayout")
        self.label = QtWidgets.QLabel(Form)
        self.label.setObjectName("label")
        self.formLayout.setWidget(1, QtWidgets.QFormLayout.LabelRole, self.label)
        self.label_2 = QtWidgets.QLabel(Form)
        self.label_2.setObjectName("label_2")
        self.formLayout.setWidget(2, QtWidgets.QFormLayout.LabelRole, self.label_2)
        self.doubleSpinBox_volt = QtWidgets.QDoubleSpinBox(Form)
        self.doubleSpinBox_volt.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_volt.setMaximum(9999.99)
        self.doubleSpinBox_volt.setObjectName("doubleSpinBox_volt")
        self.formLayout.setWidget(1, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_volt)
        self.doubleSpinBox_time = QtWidgets.QDoubleSpinBox(Form)
        self.doubleSpinBox_time.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_time.setMaximum(9999.99)
        self.doubleSpinBox_time.setObjectName("doubleSpinBox_time")
        self.formLayout.setWidget(2, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_time)
        self.pushButton_delete = QtWidgets.QPushButton(Form)
        self.pushButton_delete.setObjectName("pushButton_delete")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.SpanningRole, self.pushButton_delete)
        self.verticalLayout.addLayout(self.formLayout)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.label.setText(_translate("Form", "电压(V)："))
        self.label_2.setText(_translate("Form", "时间(S)："))
        self.pushButton_delete.setText(_translate("Form", "删除"))
