# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'UIProjectInfoDialog.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_ProjectDialog(object):
    def setupUi(self, ProjectDialog):
        ProjectDialog.setObjectName("ProjectDialog")
        ProjectDialog.resize(1200, 800)
        ProjectDialog.setMinimumSize(QtCore.QSize(1200, 800))
        self.gridLayout_2 = QtWidgets.QGridLayout(ProjectDialog)
        self.gridLayout_2.setContentsMargins(20, 20, 20, 20)
        self.gridLayout_2.setSpacing(6)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.frame = QtWidgets.QFrame(ProjectDialog)
        self.frame.setStyleSheet("")
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout.setContentsMargins(15, 15, 15, 15)
        self.verticalLayout.setObjectName("verticalLayout")
        self.tableWidget = QtWidgets.QTableWidget(self.frame)
        self.tableWidget.setStyleSheet("border:none; margin:0; color:#8e8e8e")
        self.tableWidget.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.tableWidget.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.tableWidget.setAutoScrollMargin(16)
        self.tableWidget.setRowCount(15)
        self.tableWidget.setObjectName("tableWidget")
        self.tableWidget.setColumnCount(6)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setVerticalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setVerticalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setVerticalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setVerticalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(5, item)
        self.tableWidget.horizontalHeader().setVisible(True)
        self.tableWidget.horizontalHeader().setCascadingSectionResizes(False)
        self.tableWidget.horizontalHeader().setHighlightSections(True)
        self.tableWidget.horizontalHeader().setStretchLastSection(False)
        self.tableWidget.verticalHeader().setVisible(False)
        self.tableWidget.verticalHeader().setCascadingSectionResizes(False)
        self.tableWidget.verticalHeader().setStretchLastSection(False)
        self.verticalLayout.addWidget(self.tableWidget)
        self.gridLayout_2.addWidget(self.frame, 1, 0, 1, 1)
        self.frame_2 = QtWidgets.QFrame(ProjectDialog)
        self.frame_2.setStyleSheet("border-radius:5")
        self.frame_2.setObjectName("frame_2")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.frame_2)
        self.horizontalLayout_9.setContentsMargins(15, 0, 15, 15)
        self.horizontalLayout_9.setSpacing(0)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.label = QtWidgets.QLabel(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.label.setFont(font)
        self.label.setStyleSheet("")
        self.label.setObjectName("label")
        self.horizontalLayout_9.addWidget(self.label)
        self.project_number_label = QtWidgets.QLabel(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.project_number_label.sizePolicy().hasHeightForWidth())
        self.project_number_label.setSizePolicy(sizePolicy)
        self.project_number_label.setMinimumSize(QtCore.QSize(100, 0))
        self.project_number_label.setMaximumSize(QtCore.QSize(16777215, 50))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.project_number_label.setFont(font)
        self.project_number_label.setStyleSheet("")
        self.project_number_label.setText("")
        self.project_number_label.setAlignment(QtCore.Qt.AlignCenter)
        self.project_number_label.setObjectName("project_number_label")
        self.horizontalLayout_9.addWidget(self.project_number_label)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_9.addItem(spacerItem)
        self.label_6 = QtWidgets.QLabel(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_6.sizePolicy().hasHeightForWidth())
        self.label_6.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.label_6.setFont(font)
        self.label_6.setStyleSheet("")
        self.label_6.setObjectName("label_6")
        self.horizontalLayout_9.addWidget(self.label_6)
        self.project_name_label = QtWidgets.QLabel(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.project_name_label.sizePolicy().hasHeightForWidth())
        self.project_name_label.setSizePolicy(sizePolicy)
        self.project_name_label.setMinimumSize(QtCore.QSize(100, 0))
        self.project_name_label.setMaximumSize(QtCore.QSize(16777215, 50))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.project_name_label.setFont(font)
        self.project_name_label.setStyleSheet("")
        self.project_name_label.setText("")
        self.project_name_label.setAlignment(QtCore.Qt.AlignCenter)
        self.project_name_label.setObjectName("project_name_label")
        self.horizontalLayout_9.addWidget(self.project_name_label)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_9.addItem(spacerItem1)
        self.label_4 = QtWidgets.QLabel(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_4.sizePolicy().hasHeightForWidth())
        self.label_4.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.label_4.setFont(font)
        self.label_4.setStyleSheet("")
        self.label_4.setObjectName("label_4")
        self.horizontalLayout_9.addWidget(self.label_4)
        self.workspace_label = QtWidgets.QLabel(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.workspace_label.sizePolicy().hasHeightForWidth())
        self.workspace_label.setSizePolicy(sizePolicy)
        self.workspace_label.setMinimumSize(QtCore.QSize(100, 0))
        self.workspace_label.setMaximumSize(QtCore.QSize(16777215, 50))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.workspace_label.setFont(font)
        self.workspace_label.setStyleSheet("")
        self.workspace_label.setText("")
        self.workspace_label.setAlignment(QtCore.Qt.AlignCenter)
        self.workspace_label.setObjectName("workspace_label")
        self.horizontalLayout_9.addWidget(self.workspace_label)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_9.addItem(spacerItem2)
        self.label_2 = QtWidgets.QLabel(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_2.sizePolicy().hasHeightForWidth())
        self.label_2.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.label_2.setFont(font)
        self.label_2.setStyleSheet("")
        self.label_2.setObjectName("label_2")
        self.horizontalLayout_9.addWidget(self.label_2)
        self.test_plan_label = QtWidgets.QLabel(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.test_plan_label.sizePolicy().hasHeightForWidth())
        self.test_plan_label.setSizePolicy(sizePolicy)
        self.test_plan_label.setMinimumSize(QtCore.QSize(100, 0))
        self.test_plan_label.setMaximumSize(QtCore.QSize(16777215, 50))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.test_plan_label.setFont(font)
        self.test_plan_label.setStyleSheet("")
        self.test_plan_label.setText("")
        self.test_plan_label.setAlignment(QtCore.Qt.AlignCenter)
        self.test_plan_label.setObjectName("test_plan_label")
        self.horizontalLayout_9.addWidget(self.test_plan_label)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_9.addItem(spacerItem3)
        self.label_3 = QtWidgets.QLabel(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_3.sizePolicy().hasHeightForWidth())
        self.label_3.setSizePolicy(sizePolicy)
        self.label_3.setMinimumSize(QtCore.QSize(0, 0))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.label_3.setFont(font)
        self.label_3.setStyleSheet("")
        self.label_3.setObjectName("label_3")
        self.horizontalLayout_9.addWidget(self.label_3)
        self.product_sw_label = QtWidgets.QLabel(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.product_sw_label.sizePolicy().hasHeightForWidth())
        self.product_sw_label.setSizePolicy(sizePolicy)
        self.product_sw_label.setMinimumSize(QtCore.QSize(100, 0))
        self.product_sw_label.setMaximumSize(QtCore.QSize(16777215, 50))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.product_sw_label.setFont(font)
        self.product_sw_label.setStyleSheet("")
        self.product_sw_label.setText("")
        self.product_sw_label.setAlignment(QtCore.Qt.AlignCenter)
        self.product_sw_label.setObjectName("product_sw_label")
        self.horizontalLayout_9.addWidget(self.product_sw_label)
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_9.addItem(spacerItem4)
        self.horizontalLayout_9.setStretch(0, 1)
        self.horizontalLayout_9.setStretch(1, 1)
        self.horizontalLayout_9.setStretch(3, 1)
        self.horizontalLayout_9.setStretch(4, 1)
        self.horizontalLayout_9.setStretch(6, 1)
        self.horizontalLayout_9.setStretch(7, 1)
        self.horizontalLayout_9.setStretch(9, 1)
        self.horizontalLayout_9.setStretch(10, 1)
        self.horizontalLayout_9.setStretch(12, 1)
        self.horizontalLayout_9.setStretch(13, 1)
        self.gridLayout_2.addWidget(self.frame_2, 0, 0, 1, 1)
        self.gridLayout_2.setRowMinimumHeight(0, 1)
        self.gridLayout_2.setRowMinimumHeight(1, 9)
        self.gridLayout_2.setRowStretch(0, 1)
        self.gridLayout_2.setRowStretch(1, 15)

        self.retranslateUi(ProjectDialog)
        QtCore.QMetaObject.connectSlotsByName(ProjectDialog)

    def retranslateUi(self, ProjectDialog):
        _translate = QtCore.QCoreApplication.translate
        ProjectDialog.setWindowTitle(_translate("ProjectDialog", "Dialog"))
        item = self.tableWidget.verticalHeaderItem(0)
        item.setText(_translate("ProjectDialog", "New Row"))
        item = self.tableWidget.verticalHeaderItem(1)
        item.setText(_translate("ProjectDialog", "New Row"))
        item = self.tableWidget.verticalHeaderItem(2)
        item.setText(_translate("ProjectDialog", "New Row"))
        item = self.tableWidget.verticalHeaderItem(3)
        item.setText(_translate("ProjectDialog", "New Row"))
        item = self.tableWidget.verticalHeaderItem(4)
        item.setText(_translate("ProjectDialog", "New Row"))
        item = self.tableWidget.verticalHeaderItem(5)
        item.setText(_translate("ProjectDialog", "New Row"))
        item = self.tableWidget.verticalHeaderItem(6)
        item.setText(_translate("ProjectDialog", "New Row"))
        item = self.tableWidget.verticalHeaderItem(7)
        item.setText(_translate("ProjectDialog", "New Row"))
        item = self.tableWidget.verticalHeaderItem(8)
        item.setText(_translate("ProjectDialog", "New Row"))
        item = self.tableWidget.verticalHeaderItem(9)
        item.setText(_translate("ProjectDialog", "New Row"))
        item = self.tableWidget.horizontalHeaderItem(0)
        item.setText(_translate("ProjectDialog", "编号"))
        item = self.tableWidget.horizontalHeaderItem(1)
        item.setText(_translate("ProjectDialog", "测试任务"))
        item = self.tableWidget.horizontalHeaderItem(2)
        item.setText(_translate("ProjectDialog", "测试机台"))
        item = self.tableWidget.horizontalHeaderItem(3)
        item.setText(_translate("ProjectDialog", "用例数"))
        item = self.tableWidget.horizontalHeaderItem(4)
        item.setText(_translate("ProjectDialog", "测试人员"))
        item = self.tableWidget.horizontalHeaderItem(5)
        item.setText(_translate("ProjectDialog", "消息推送人员"))
        self.label.setText(_translate("ProjectDialog", "项目编号："))
        self.label_6.setText(_translate("ProjectDialog", "项目名称："))
        self.label_4.setText(_translate("ProjectDialog", "工作目录："))
        self.label_2.setText(_translate("ProjectDialog", "测试计划："))
        self.label_3.setText(_translate("ProjectDialog", "测试版本："))
