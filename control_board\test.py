import sys
import time

from PyQt5.QtWidgets import QApplication

from control_board.auto_test_m.widgets.AxisWidget import AxisWidget
from control_board.auto_test_m.ctr_card import ctr_card
# from control_board.auto_test_m.station import Station

app = QApplication(sys.argv)

w = AxisWidget()
w.show()

# station = Station()
# station.start()

# ctr_card.init()

ctr_card.init()

ctr_card.home()
# time.sleep(3)

# for i in range(10):
#     ctr_card.crd_move([(89168, 14180)])
#     while True:
#         if ctr_card.crd_move_result:
#             break
#     time.sleep(3)
#     ctr_card.crd_move([(0, 0)])
#     while True:
#         if ctr_card.crd_move_result:
#             break


sys.exit(app.exec())
