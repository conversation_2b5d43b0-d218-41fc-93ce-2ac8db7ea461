#!/bin/bash

if [ -d "build" ]; then
	rm -rf build
fi
										
if [ -d "dist" ]; then
	rm -rf dist
fi

python -m venv venv

source venv/bin/activate && python -V && python -m pip install --upgrade pip -i https://mirrors.aliyun.com/pypi/simple/ && pip install -r linux_requirements.txt -i https://mirrors.aliyun.com/pypi/simple/ && pyinstaller $1.spec && cp -r dist/ATEApp/_internal/* dist/ATEApp/. && python Compress.py dist/$1 dist/Release $1.zip && deactivate