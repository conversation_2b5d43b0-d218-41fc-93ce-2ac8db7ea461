# import scipy.io
# mat = scipy.io.loadmat(r'C:\Users\<USER>\Desktop\matlab.mat')
# # 解析mat文件，并获取其中的数据
# # print(sorted(mat.keys()))
# # print(mat["None"])
# for key in mat.keys():
#     print("key:",key)
#     print("value:",mat[key])
# #     try:
# #         if mat[key].keys() == None:
# #             continue
# #     except:
# #         continue
# #     for  key2 in mat[key].keys():
# #         print("key2:",key2)
# #         print("value2:",mat[key][key2])
# #     print("----------------------------------------------------")
