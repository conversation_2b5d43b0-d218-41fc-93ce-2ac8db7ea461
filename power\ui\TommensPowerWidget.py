# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'TommensPowerWidget.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_FormPower(object):
    def setupUi(self, FormPower):
        FormPower.setObjectName("FormPower")
        FormPower.resize(1600, 900)
        FormPower.setMinimumSize(QtCore.QSize(1600, 900))
        self.verticalLayout = QtWidgets.QVBoxLayout(FormPower)
        self.verticalLayout.setObjectName("verticalLayout")
        self.tabWidget = QtWidgets.QTabWidget(FormPower)
        self.tabWidget.setObjectName("tabWidget")
        self.tab = QtWidgets.QWidget()
        self.tab.setObjectName("tab")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.tab)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout()
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.groupBox = QtWidgets.QGroupBox(self.tab)
        self.groupBox.setObjectName("groupBox")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.groupBox)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setObjectName("gridLayout")
        self.label_2 = QtWidgets.QLabel(self.groupBox)
        self.label_2.setObjectName("label_2")
        self.gridLayout.addWidget(self.label_2, 1, 0, 1, 2)
        self.label_11 = QtWidgets.QLabel(self.groupBox)
        self.label_11.setObjectName("label_11")
        self.gridLayout.addWidget(self.label_11, 4, 0, 1, 3)
        self.spinBoxVoltage = QtWidgets.QSpinBox(self.groupBox)
        self.spinBoxVoltage.setMinimumSize(QtCore.QSize(0, 50))
        self.spinBoxVoltage.setProperty("value", 13)
        self.spinBoxVoltage.setObjectName("spinBoxVoltage")
        self.gridLayout.addWidget(self.spinBoxVoltage, 0, 2, 1, 1)
        self.label_12 = QtWidgets.QLabel(self.groupBox)
        self.label_12.setObjectName("label_12")
        self.gridLayout.addWidget(self.label_12, 6, 0, 1, 3)
        self.spinBoxPowerOff = QtWidgets.QSpinBox(self.groupBox)
        self.spinBoxPowerOff.setMinimumSize(QtCore.QSize(0, 50))
        self.spinBoxPowerOff.setMaximum(9999)
        self.spinBoxPowerOff.setObjectName("spinBoxPowerOff")
        self.gridLayout.addWidget(self.spinBoxPowerOff, 2, 2, 1, 1)
        self.label_3 = QtWidgets.QLabel(self.groupBox)
        self.label_3.setObjectName("label_3")
        self.gridLayout.addWidget(self.label_3, 2, 0, 1, 2)
        self.label_9 = QtWidgets.QLabel(self.groupBox)
        self.label_9.setObjectName("label_9")
        self.gridLayout.addWidget(self.label_9, 5, 1, 1, 1)
        self.spinBoxPowerOn = QtWidgets.QSpinBox(self.groupBox)
        self.spinBoxPowerOn.setMinimumSize(QtCore.QSize(0, 50))
        self.spinBoxPowerOn.setMaximum(9999)
        self.spinBoxPowerOn.setObjectName("spinBoxPowerOn")
        self.gridLayout.addWidget(self.spinBoxPowerOn, 1, 2, 1, 1)
        self.checkBoxRandomTest = QtWidgets.QCheckBox(self.groupBox)
        self.checkBoxRandomTest.setObjectName("checkBoxRandomTest")
        self.gridLayout.addWidget(self.checkBoxRandomTest, 3, 2, 1, 1)
        self.label_10 = QtWidgets.QLabel(self.groupBox)
        self.label_10.setMinimumSize(QtCore.QSize(0, 45))
        self.label_10.setObjectName("label_10")
        self.gridLayout.addWidget(self.label_10, 3, 0, 1, 2)
        self.label_4 = QtWidgets.QLabel(self.groupBox)
        self.label_4.setObjectName("label_4")
        self.gridLayout.addWidget(self.label_4, 0, 0, 1, 2)
        self.doubleSpinBoxMaxOff = QtWidgets.QDoubleSpinBox(self.groupBox)
        self.doubleSpinBoxMaxOff.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBoxMaxOff.setDecimals(3)
        self.doubleSpinBoxMaxOff.setObjectName("doubleSpinBoxMaxOff")
        self.gridLayout.addWidget(self.doubleSpinBoxMaxOff, 7, 2, 1, 1)
        self.label_13 = QtWidgets.QLabel(self.groupBox)
        self.label_13.setObjectName("label_13")
        self.gridLayout.addWidget(self.label_13, 7, 1, 1, 1)
        self.doubleSpinBoxMaxOn = QtWidgets.QDoubleSpinBox(self.groupBox)
        self.doubleSpinBoxMaxOn.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBoxMaxOn.setDecimals(3)
        self.doubleSpinBoxMaxOn.setObjectName("doubleSpinBoxMaxOn")
        self.gridLayout.addWidget(self.doubleSpinBoxMaxOn, 5, 2, 1, 1)
        self.doubleSpinBoxMinOn = QtWidgets.QDoubleSpinBox(self.groupBox)
        self.doubleSpinBoxMinOn.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBoxMinOn.setDecimals(3)
        self.doubleSpinBoxMinOn.setObjectName("doubleSpinBoxMinOn")
        self.gridLayout.addWidget(self.doubleSpinBoxMinOn, 5, 0, 1, 1)
        self.doubleSpinBoxMinOff = QtWidgets.QDoubleSpinBox(self.groupBox)
        self.doubleSpinBoxMinOff.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBoxMinOff.setDecimals(3)
        self.doubleSpinBoxMinOff.setObjectName("doubleSpinBoxMinOff")
        self.gridLayout.addWidget(self.doubleSpinBoxMinOff, 7, 0, 1, 1)
        self.gridLayout.setColumnStretch(0, 1)
        self.gridLayout.setColumnStretch(1, 1)
        self.gridLayout.setColumnStretch(2, 1)
        self.verticalLayout_3.addLayout(self.gridLayout)
        self.horizontalLayout_3.addWidget(self.groupBox)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.verticalLayout_14 = QtWidgets.QVBoxLayout()
        self.verticalLayout_14.setContentsMargins(0, -1, -1, -1)
        self.verticalLayout_14.setObjectName("verticalLayout_14")
        self.pushButtonStrart = QtWidgets.QPushButton(self.tab)
        self.pushButtonStrart.setMinimumSize(QtCore.QSize(200, 100))
        self.pushButtonStrart.setObjectName("pushButtonStrart")
        self.verticalLayout_14.addWidget(self.pushButtonStrart)
        self.pushButtonEnd = QtWidgets.QPushButton(self.tab)
        self.pushButtonEnd.setMinimumSize(QtCore.QSize(200, 100))
        self.pushButtonEnd.setObjectName("pushButtonEnd")
        self.verticalLayout_14.addWidget(self.pushButtonEnd)
        self.horizontalLayout_2.addLayout(self.verticalLayout_14)
        self.horizontalLayout_3.addLayout(self.horizontalLayout_2)
        self.verticalLayout_4.addLayout(self.horizontalLayout_3)
        self.label_process = QtWidgets.QLabel(self.tab)
        self.label_process.setObjectName("label_process")
        self.verticalLayout_4.addWidget(self.label_process)
        self.verticalLayout_5.addLayout(self.verticalLayout_4)
        self.tabWidget.addTab(self.tab, "")
        self.tab_2 = QtWidgets.QWidget()
        self.tab_2.setObjectName("tab_2")
        self.verticalLayout_7 = QtWidgets.QVBoxLayout(self.tab_2)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout()
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.groupBox_2 = QtWidgets.QGroupBox(self.tab_2)
        self.groupBox_2.setObjectName("groupBox_2")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.groupBox_2)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.gridLayout_2 = QtWidgets.QGridLayout()
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.doubleSpinBoxV2 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBoxV2.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBoxV2.setProperty("value", 13.0)
        self.doubleSpinBoxV2.setObjectName("doubleSpinBoxV2")
        self.gridLayout_2.addWidget(self.doubleSpinBoxV2, 1, 1, 1, 1)
        self.label_5 = QtWidgets.QLabel(self.groupBox_2)
        self.label_5.setObjectName("label_5")
        self.gridLayout_2.addWidget(self.label_5, 1, 0, 1, 1)
        self.label = QtWidgets.QLabel(self.groupBox_2)
        self.label.setObjectName("label")
        self.gridLayout_2.addWidget(self.label, 0, 0, 1, 1)
        self.label_6 = QtWidgets.QLabel(self.groupBox_2)
        self.label_6.setObjectName("label_6")
        self.gridLayout_2.addWidget(self.label_6, 2, 0, 1, 1)
        self.label_7 = QtWidgets.QLabel(self.groupBox_2)
        self.label_7.setObjectName("label_7")
        self.gridLayout_2.addWidget(self.label_7, 3, 0, 1, 1)
        self.doubleSpinBoxV1 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBoxV1.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBoxV1.setProperty("value", 13.0)
        self.doubleSpinBoxV1.setObjectName("doubleSpinBoxV1")
        self.gridLayout_2.addWidget(self.doubleSpinBoxV1, 0, 1, 1, 1)
        self.spinBoxSwitchNum = QtWidgets.QSpinBox(self.groupBox_2)
        self.spinBoxSwitchNum.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBoxSwitchNum.setMaximum(99999)
        self.spinBoxSwitchNum.setProperty("value", 10)
        self.spinBoxSwitchNum.setObjectName("spinBoxSwitchNum")
        self.gridLayout_2.addWidget(self.spinBoxSwitchNum, 3, 1, 1, 1)
        self.doubleSpinBoxSwitchTime = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBoxSwitchTime.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBoxSwitchTime.setDecimals(3)
        self.doubleSpinBoxSwitchTime.setObjectName("doubleSpinBoxSwitchTime")
        self.gridLayout_2.addWidget(self.doubleSpinBoxSwitchTime, 2, 1, 1, 1)
        self.verticalLayout_2.addLayout(self.gridLayout_2)
        self.horizontalLayout_4.addWidget(self.groupBox_2)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.verticalLayout_13 = QtWidgets.QVBoxLayout()
        self.verticalLayout_13.setContentsMargins(0, -1, -1, -1)
        self.verticalLayout_13.setObjectName("verticalLayout_13")
        self.pushButtonStrartSwitchVoltage = QtWidgets.QPushButton(self.tab_2)
        self.pushButtonStrartSwitchVoltage.setMinimumSize(QtCore.QSize(200, 100))
        self.pushButtonStrartSwitchVoltage.setObjectName("pushButtonStrartSwitchVoltage")
        self.verticalLayout_13.addWidget(self.pushButtonStrartSwitchVoltage)
        self.pushButtonEndSwitchVoltage = QtWidgets.QPushButton(self.tab_2)
        self.pushButtonEndSwitchVoltage.setMinimumSize(QtCore.QSize(200, 100))
        self.pushButtonEndSwitchVoltage.setObjectName("pushButtonEndSwitchVoltage")
        self.verticalLayout_13.addWidget(self.pushButtonEndSwitchVoltage)
        self.horizontalLayout.addLayout(self.verticalLayout_13)
        self.horizontalLayout_4.addLayout(self.horizontalLayout)
        self.verticalLayout_6.addLayout(self.horizontalLayout_4)
        self.label_process_v_switch = QtWidgets.QLabel(self.tab_2)
        self.label_process_v_switch.setObjectName("label_process_v_switch")
        self.verticalLayout_6.addWidget(self.label_process_v_switch)
        self.verticalLayout_7.addLayout(self.verticalLayout_6)
        self.tabWidget.addTab(self.tab_2, "")
        self.tab_3 = QtWidgets.QWidget()
        self.tab_3.setObjectName("tab_3")
        self.verticalLayout_11 = QtWidgets.QVBoxLayout(self.tab_3)
        self.verticalLayout_11.setObjectName("verticalLayout_11")
        self.verticalLayout_10 = QtWidgets.QVBoxLayout()
        self.verticalLayout_10.setObjectName("verticalLayout_10")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.groupBox_3 = QtWidgets.QGroupBox(self.tab_3)
        self.groupBox_3.setObjectName("groupBox_3")
        self.verticalLayout_9 = QtWidgets.QVBoxLayout(self.groupBox_3)
        self.verticalLayout_9.setObjectName("verticalLayout_9")
        self.verticalLayout_8 = QtWidgets.QVBoxLayout()
        self.verticalLayout_8.setObjectName("verticalLayout_8")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_6.addItem(spacerItem)
        self.toolButtonAdd = QtWidgets.QToolButton(self.groupBox_3)
        self.toolButtonAdd.setObjectName("toolButtonAdd")
        self.horizontalLayout_6.addWidget(self.toolButtonAdd)
        self.toolButtonReduce = QtWidgets.QToolButton(self.groupBox_3)
        self.toolButtonReduce.setObjectName("toolButtonReduce")
        self.horizontalLayout_6.addWidget(self.toolButtonReduce)
        self.verticalLayout_8.addLayout(self.horizontalLayout_6)
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.label_8 = QtWidgets.QLabel(self.groupBox_3)
        self.label_8.setObjectName("label_8")
        self.horizontalLayout_8.addWidget(self.label_8)
        self.doubleSpinBoxRandomTime = QtWidgets.QDoubleSpinBox(self.groupBox_3)
        self.doubleSpinBoxRandomTime.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBoxRandomTime.setObjectName("doubleSpinBoxRandomTime")
        self.horizontalLayout_8.addWidget(self.doubleSpinBoxRandomTime)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_8.addItem(spacerItem1)
        self.verticalLayout_8.addLayout(self.horizontalLayout_8)
        self.widgetVoltage = QtWidgets.QWidget(self.groupBox_3)
        self.widgetVoltage.setObjectName("widgetVoltage")
        self.verticalLayout_8.addWidget(self.widgetVoltage)
        self.verticalLayout_8.setStretch(0, 1)
        self.verticalLayout_8.setStretch(1, 1)
        self.verticalLayout_8.setStretch(2, 99)
        self.verticalLayout_9.addLayout(self.verticalLayout_8)
        self.horizontalLayout_7.addWidget(self.groupBox_3)
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.verticalLayout_12 = QtWidgets.QVBoxLayout()
        self.verticalLayout_12.setContentsMargins(0, -1, -1, -1)
        self.verticalLayout_12.setObjectName("verticalLayout_12")
        self.pushButtonStartRandomVoltage = QtWidgets.QPushButton(self.tab_3)
        self.pushButtonStartRandomVoltage.setMinimumSize(QtCore.QSize(200, 100))
        self.pushButtonStartRandomVoltage.setObjectName("pushButtonStartRandomVoltage")
        self.verticalLayout_12.addWidget(self.pushButtonStartRandomVoltage)
        self.pushButtonEndRandomVoltage = QtWidgets.QPushButton(self.tab_3)
        self.pushButtonEndRandomVoltage.setMinimumSize(QtCore.QSize(200, 100))
        self.pushButtonEndRandomVoltage.setObjectName("pushButtonEndRandomVoltage")
        self.verticalLayout_12.addWidget(self.pushButtonEndRandomVoltage)
        self.horizontalLayout_5.addLayout(self.verticalLayout_12)
        self.horizontalLayout_7.addLayout(self.horizontalLayout_5)
        self.verticalLayout_10.addLayout(self.horizontalLayout_7)
        self.label_process_random_voltage = QtWidgets.QLabel(self.tab_3)
        self.label_process_random_voltage.setObjectName("label_process_random_voltage")
        self.verticalLayout_10.addWidget(self.label_process_random_voltage)
        self.verticalLayout_10.setStretch(0, 99)
        self.verticalLayout_10.setStretch(1, 1)
        self.verticalLayout_11.addLayout(self.verticalLayout_10)
        self.tabWidget.addTab(self.tab_3, "")
        self.verticalLayout.addWidget(self.tabWidget)

        self.retranslateUi(FormPower)
        self.tabWidget.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(FormPower)

    def retranslateUi(self, FormPower):
        _translate = QtCore.QCoreApplication.translate
        FormPower.setWindowTitle(_translate("FormPower", "Tommens程控电源"))
        self.groupBox.setTitle(_translate("FormPower", "配置"))
        self.label_2.setText(_translate("FormPower", "上电时间(s)"))
        self.label_11.setText(_translate("FormPower", "上电时间范围(s)"))
        self.label_12.setText(_translate("FormPower", "下电时间范围"))
        self.label_3.setText(_translate("FormPower", "下电时间(s)"))
        self.label_9.setText(_translate("FormPower", " ~"))
        self.checkBoxRandomTest.setText(_translate("FormPower", "是"))
        self.label_10.setText(_translate("FormPower", "时间随机"))
        self.label_4.setText(_translate("FormPower", "电压(V)"))
        self.label_13.setText(_translate("FormPower", " ~"))
        self.pushButtonStrart.setText(_translate("FormPower", "开始"))
        self.pushButtonEnd.setText(_translate("FormPower", "结束"))
        self.label_process.setText(_translate("FormPower", "执行进度"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab), _translate("FormPower", "上下电控制"))
        self.groupBox_2.setTitle(_translate("FormPower", "配置"))
        self.label_5.setText(_translate("FormPower", "电压2(V)"))
        self.label.setText(_translate("FormPower", "电压1(V)"))
        self.label_6.setText(_translate("FormPower", "切换时间(s)"))
        self.label_7.setText(_translate("FormPower", "循环次数"))
        self.pushButtonStrartSwitchVoltage.setText(_translate("FormPower", "开始"))
        self.pushButtonEndSwitchVoltage.setText(_translate("FormPower", "结束"))
        self.label_process_v_switch.setText(_translate("FormPower", "执行进度"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_2), _translate("FormPower", "高低压控制"))
        self.groupBox_3.setTitle(_translate("FormPower", "配置"))
        self.toolButtonAdd.setText(_translate("FormPower", "+"))
        self.toolButtonReduce.setText(_translate("FormPower", "-"))
        self.label_8.setText(_translate("FormPower", "间隔时间(s) "))
        self.pushButtonStartRandomVoltage.setText(_translate("FormPower", "开始"))
        self.pushButtonEndRandomVoltage.setText(_translate("FormPower", "结束"))
        self.label_process_random_voltage.setText(_translate("FormPower", "执行进度"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_3), _translate("FormPower", "随机电压"))
