# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'SingleAngle.ui'
#
# Created by: PyQt5 UI code generator 5.15.6
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_singleAngle(object):
    def setupUi(self, singleAngle):
        singleAngle.setObjectName("singleAngle")
        singleAngle.resize(182, 51)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(singleAngle)
        self.horizontalLayout_2.setContentsMargins(0, 2, 2, 2)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.label = QtWidgets.QLabel(singleAngle)
        self.label.setAlignment(QtCore.Qt.AlignCenter)
        self.label.setObjectName("label")
        self.horizontalLayout.addWidget(self.label)
        self.spinBox = QtWidgets.QSpinBox(singleAngle)
        self.spinBox.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox.setMinimum(-354)
        self.spinBox.setMaximum(354)
        self.spinBox.setObjectName("spinBox")
        self.horizontalLayout.addWidget(self.spinBox)
        self.label_2 = QtWidgets.QLabel(singleAngle)
        self.label_2.setObjectName("label_2")
        self.horizontalLayout.addWidget(self.label_2)
        self.lineEditDetect = QtWidgets.QLineEdit(singleAngle)
        self.lineEditDetect.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEditDetect.setMaximumSize(QtCore.QSize(100, 16777215))
        self.lineEditDetect.setObjectName("lineEditDetect")
        self.horizontalLayout.addWidget(self.lineEditDetect)
        self.horizontalLayout_2.addLayout(self.horizontalLayout)

        self.retranslateUi(singleAngle)
        QtCore.QMetaObject.connectSlotsByName(singleAngle)

    def retranslateUi(self, singleAngle):
        _translate = QtCore.QCoreApplication.translate
        singleAngle.setWindowTitle(_translate("singleAngle", "Form"))
        self.label.setText(_translate("singleAngle", "旋转"))
        self.label_2.setText(_translate("singleAngle", "检测角度"))
