# -*-coding:utf-8 -*-
"""
# Author     : jid
# Time       : 2025/3/15 13:00
# Description: 
"""
import operator
import os

from common.LogUtils import logger


class AdbManager:

    def __init__(self):
        super().__init__()

    @staticmethod
    def handle_execute_adb_cmd(case_number, command, data):
        from photics import photics_manager
        from utils.SignalsManager import signals_manager
        if data.__contains__(","):
            cmd = data.split(",")[0]
            expect = data.split(",")[1]
            status = photics_manager.get_adb_status()
            if status:
                if operator.eq("NA", expect):
                    photics_manager.execute_adb_command_assign_device("adb root")
                    status, msg = photics_manager.execute_adb_command_assign_device(cmd)
                    step_result = "PASS" if status else "NG"
                    signals_manager.step_execute_finish.emit(case_number, command, step_result, msg)
                else:
                    photics_manager.execute_adb_command("adb root")
                    output = photics_manager.execute_adb_command(cmd)
                    if len(output) > 0:
                        if output[0].__contains__(expect):
                            signals_manager.step_execute_finish.emit(case_number, command, "PASS", str(output))
                        else:
                            signals_manager.step_execute_finish.emit(case_number, command, "NG", str(output))
                    else:
                        signals_manager.step_execute_finish.emit(case_number, command, "NG", "output is null")
            else:
                if data.__contains__("adb connect"):
                    # 无线adb连接
                    photics_manager.execute_adb_command(data)
                    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
                else:
                    signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb is not connected")
        else:
            status = photics_manager.get_adb_status()
            if status:
                photics_manager.execute_adb_command("adb root")
                photics_manager.execute_adb_command(data)
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                if data.__contains__("adb connect"):
                    # 无线adb连接
                    photics_manager.execute_adb_command(data)
                    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
                else:
                    signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb is not connected")

    @staticmethod
    def clear_adb_process():
        try:
            os.system("taskkill /f /im adb.exe")
        except Exception as e:
            logger.error(f"clear_adb_process exception: {str(e.args)}")


adb_manager: AdbManager = AdbManager()
