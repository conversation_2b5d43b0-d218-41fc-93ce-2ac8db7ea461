"""
ADB相关命令处理器
"""
import logging
from typing import Dict, Any
from case.command_handlers.base_handler import Base<PERSON>ommandHandler

logger = logging.getLogger(__name__)


class AdbCommandHandler(BaseCommandHandler):
    """ADB命令处理器"""
    
    def get_supported_commands(self) -> set:
        """返回支持的ADB相关命令"""
        return {
            "ReadScreenTemp", "ReadPCBTemp", "ReadSoftwareVersion", "WriteHardwareVersion",
            "ReadHardwareVersion", "ReadInnerSoftwareVersion", "ReadInnerHardwareVersion",
            "WritePartNumber", "ReadPartNumber", "ReadLDVersion", "ReadTDDIVersion",
            "ReadTCONVersion", "ReadBootVersion", "ReadTpVersion", "ReadAssemblyVersion",
            "WriteHWSN", "ReadHWSN", "WritePSN", "ReadPS<PERSON>", "SwitchBrightness",
            "SwitchRandomBrightness", "ReadBrightness", "ReadLightSensor", "SwitchColor",
            "SwitchPattern", "ReadWorkVoltage", "SwitchSleep", "SwitchWakeup", "TpcmTest",
            "SwitchBackLight", "ReadBackLightStatus", "SwitchBistPattern", "ReadBistPatternStatus",
            "DisplayReboot", "TconReset", "I2cChecksumDetect", "SerialProtocolTest",
            "StartProcessMonitor", "StopProcessMonitor"
        }
    
    def execute(self, command: str, step: Dict[str, Any]) -> None:
        """执行ADB相关命令"""
        params = self._extract_step_params(step)
        case_number = params['case_number']
        expect = params['expect']
        data = params['data']
        
        # 导入必要的模块
        from adb.AdbConnectDevice import adb_connect_device
        from case.VdsDetectManager import vds_detect_manager
        
        try:
            if command == "ReadScreenTemp":
                adb_connect_device.read_screen_temp()
                vds_detect_manager.set_expect_screen_temp(case_number, command, expect)
                
            elif command == "ReadPCBTemp":
                adb_connect_device.read_pcb_temp()
                vds_detect_manager.set_expect_pcb_temp(case_number, command, expect)
                
            elif command == "ReadSoftwareVersion":
                if adb_connect_device.is_connect():
                    adb_connect_device.read_software_version()
                    vds_detect_manager.set_expect_software_version(case_number, command, expect)
                else:
                    self._emit_failure(case_number, command, "adb forward 未连接")
                    
            elif command == "WriteHardwareVersion":
                adb_connect_device.write_hardware_version(data)
                self._emit_success(case_number, command)
                
            elif command == "ReadHardwareVersion":
                adb_connect_device.read_hardware_version(data)
                vds_detect_manager.set_expect_hardware_version(case_number, command, expect)
                
            elif command == "ReadInnerSoftwareVersion":
                adb_connect_device.read_inner_software_version()
                vds_detect_manager.set_expect_inner_software_version(case_number, command, expect)
                
            elif command == "ReadInnerHardwareVersion":
                adb_connect_device.read_inner_hardware_version(data)
                vds_detect_manager.set_expect_inner_hardware_version(case_number, command, expect)
                
            elif command == "WritePartNumber":
                adb_connect_device.write_part_number(data)
                self._emit_success(case_number, command)
                
            elif command == "ReadPartNumber":
                adb_connect_device.read_part_number()
                vds_detect_manager.set_expect_part_number(case_number, command, expect)
                
            elif command == "ReadLDVersion":
                adb_connect_device.read_ld_version()
                vds_detect_manager.set_expect_ld_version(case_number, command, expect)
                
            elif command == "ReadTDDIVersion":
                adb_connect_device.read_tddi_version()
                vds_detect_manager.set_expect_tddi_version(case_number, command, expect)
                
            elif command == "ReadTCONVersion":
                adb_connect_device.read_tcon_version()
                vds_detect_manager.set_expect_tcon_version(case_number, command, expect)
                
            elif command == "ReadBootVersion":
                adb_connect_device.read_boot_version()
                vds_detect_manager.set_expect_boot_version(case_number, command, expect)
                
            elif command == "ReadTpVersion":
                adb_connect_device.read_tp_version()
                vds_detect_manager.set_expect_tp_version(case_number, command, expect)
                
            elif command == "ReadAssemblyVersion":
                adb_connect_device.read_assembly_version()
                vds_detect_manager.set_expect_assembly_version(case_number, command, expect)
                
            elif command == "WriteHWSN":
                adb_connect_device.write_hwsn(data)
                self._emit_success(case_number, command)
                
            elif command == "ReadHWSN":
                adb_connect_device.read_hwsn(data)
                vds_detect_manager.set_expect_tp_version(case_number, command, expect)
                
            elif command == "WritePSN":
                adb_connect_device.write_psn(data)
                self._emit_success(case_number, command)
                
            elif command == "ReadPSN":
                adb_connect_device.read_psn(data)
                vds_detect_manager.set_expect_tp_version(case_number, command, expect)
                
            elif command == "SwitchBrightness":
                params_list = self._parse_data_params(data, 2)
                mode = params_list[0]
                value = params_list[1]
                brightness = f"{mode}:{value}"
                adb_connect_device.switch_brightness(brightness)
                self._emit_success(case_number, command)
                
            elif command == "SwitchRandomBrightness":
                self._handle_switch_random_brightness(case_number, command, data)
                
            elif command == "ReadBrightness":
                adb_connect_device.read_brightness()
                vds_detect_manager.set_expect_brightness(case_number, command, expect)
                
            elif command == "ReadLightSensor":
                adb_connect_device.read_light_sensor()
                self._emit_success(case_number, command)
                
            elif command == "SwitchColor":
                adb_connect_device.switch_color(data)
                self._emit_success(case_number, command)
                
            elif command == "SwitchPattern":
                adb_connect_device.switch_pattern(data)
                self._emit_success(case_number, command)
                
            elif command == "ReadWorkVoltage":
                params_list = self._parse_data_params(data, 2)
                min_voltage = params_list[0]
                max_voltage = params_list[1]
                adb_connect_device.adb_forward_send_data(action="readWorkVoltage")
                vds_detect_manager.set_expect_work_voltage(case_number, command, min_voltage, max_voltage)
                
            elif command == "SwitchSleep":
                adb_connect_device.switch_sleep()
                self._emit_success(case_number, command)
                
            elif command == "SwitchWakeup":
                adb_connect_device.switch_wakeup()
                self._emit_success(case_number, command)
                
            elif command == "TpcmTest":
                adb_connect_device.test_tpcm()
                vds_detect_manager.set_expect_test_tpcm(case_number, command)
                
            elif command == "SwitchBackLight":
                if adb_connect_device.is_connect():
                    adb_connect_device.switch_back_light(data)
                    self._emit_success(case_number, command)
                else:
                    self._emit_failure(case_number, command, "adb forward 未连接")
                    
            elif command == "ReadBackLightStatus":
                if adb_connect_device.is_connect():
                    adb_connect_device.read_back_light_status()
                    vds_detect_manager.set_expect_back_light_status(case_number, command, expect)
                else:
                    self._emit_failure(case_number, command, "adb forward 未连接")
                    
            elif command == "SwitchBistPattern":
                if adb_connect_device.is_connect():
                    adb_connect_device.switch_bist_pattern(data)
                    self._emit_success(case_number, command)
                else:
                    self._emit_failure(case_number, command, "adb forward 未连接")
                    
            elif command == "ReadBistPatternStatus":
                if adb_connect_device.is_connect():
                    adb_connect_device.read_bist_pattern_status()
                    vds_detect_manager.set_expect_bist_pattern_status(case_number, command, expect)
                else:
                    self._emit_failure(case_number, command, "adb forward 未连接")
                    
            elif command == "DisplayReboot":
                if adb_connect_device.is_connect():
                    adb_connect_device.display_reboot()
                    self._emit_success(case_number, command)
                else:
                    self._emit_failure(case_number, command, "adb forward 未连接")
                    
            elif command == "TconReset":
                if adb_connect_device.is_connect():
                    adb_connect_device.tcon_reset()
                    self._emit_success(case_number, command)
                else:
                    self._emit_failure(case_number, command, "adb forward 未连接")
                    
            elif command == "I2cChecksumDetect":
                params_list = self._parse_data_params(data, 2)
                i2c_data = params_list[0]
                checksum = params_list[1]
                adb_connect_device.i2c_checksum_detect(i2c_data, checksum)
                self._emit_success(case_number, command)
                
            elif command == "SerialProtocolTest":
                params_list = self._parse_data_params(data, 2)
                send_data = params_list[0]
                receive_data = params_list[1]
                adb_connect_device.send_serial_protocol_data(send_data)
                if receive_data == "NA":
                    self._emit_success(case_number, command, "串口协议数据发送成功")
                else:
                    vds_detect_manager.set_expect_serial_protocol_data(case_number, command, receive_data)
                    
            elif command == "StartProcessMonitor":
                adb_connect_device.start_process_monitor()
                self._emit_success(case_number, command)
                
            elif command == "StopProcessMonitor":
                adb_connect_device.stop_process_monitor()
                self._emit_success(case_number, command, "")
                
            else:
                logger.warning(f"未处理的ADB命令: {command}")
                self._emit_failure(case_number, command, f"未支持的命令: {command}")
                
        except Exception as e:
            logger.error(f"ADB命令执行失败 {command}: {e}")
            self._emit_failure(case_number, command, f"命令执行异常: {str(e)}")
    
    def _handle_switch_random_brightness(self, case_number: str, command: str, data: str):
        """处理随机亮度切换"""
        import threading
        from adb.AdbConnectDevice import adb_connect_device
        
        params = self._parse_data_params(data)
        if len(params) >= 4:
            mode = params[0]
            start_value = self._safe_int(params[1])
            end_value = self._safe_int(params[2])
            duration = self._safe_float(params[3])
            
            # 启动线程执行随机亮度变化
            threading.Thread(
                target=adb_connect_device.execute_random_brightness,
                args=(case_number, command, mode, start_value, end_value, duration)
            ).start()
        else:
            self._emit_failure(case_number, command, "NG: 参数错误")
