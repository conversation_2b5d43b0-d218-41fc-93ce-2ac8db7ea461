import copy
from itertools import combinations

import cv2
from PyQt5.QtCore import QRectF, Qt
from PyQt5.QtGui import QPixmap, QPainter, QCursor, QImage
from PyQt5.QtWidgets import QWidget, QGraphicsView, QGraphicsPixmapItem, QGraphicsScene, QMenu, QAction
import numpy as np
from rotationanglemeasurement.UI import Ui_FormImage
from rotationanglemeasurement.utils.manager import managerQ


class ImageView(QWidget):
    def __init__(self, parent=None):
        super(ImageView, self).__init__(parent=parent)
        self.ui = Ui_FormImage()
        self.ui.setupUi(self)

        # 创建 QGraphicsView 和 QGraphicsScene
        self.scene = QGraphicsScene(self)
        self.view = GraphicsImageView(self)
        self.ui.verticalLayout.addWidget(self.view)
        self.view.setScene(self.scene)
        self.pixmapItem = QGraphicsPixmapItem()
        self.scene.addItem(self.pixmapItem)


        #
        # # Add QSpinBox for param1 and param2
        # self.param1SpinBox = QSpinBox(self)
        # self.param2SpinBox = QSpinBox(self)
        #
        # # Set ranges for spin boxes if needed, for example:
        # self.param1SpinBox.setRange(1, 500)
        # self.param2SpinBox.setRange(1, 500)
        # self.param1SpinBox.setValue(100)
        # self.param2SpinBox.setValue(25)
        #
        # # Add the spin boxes to the layout
        # self.ui.verticalLayout.addWidget(self.param1SpinBox)
        # self.ui.verticalLayout.addWidget(self.param2SpinBox)
        #
        # # Connect the valueChanged signals of the spin boxes to the bat function
        # self.param1SpinBox.valueChanged.connect(self.update_image)
        # self.param2SpinBox.valueChanged.connect(self.update_image)

    # def update_image(self, ):
    #     image = self.find_circle(self.path, self.param1SpinBox.value(), self.param2SpinBox.value())
    #
    #     # image = self.find_circle(path)
    #     # Convert the NumPy array to QImage
    #     height, width, channels = image.shape
    #     bytes_per_line = channels * width
    #     q_image = QImage(image.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
    #
    #     self.pixmapItem.setPixmap(QPixmap.fromImage(q_image))
    #     # pixmap = QPixmap(path)
    #     # self.pixmapItem.setPixmap(pixmap)
    #     # 设置场景大小与图片大小相同
    #     self.view.setSceneRect(QRectF(self.pixmapItem.boundingRect()))
    #     # self.view.setSceneRect(QRectF(pixmap.rect()))  # 设置场景大小与图片大小相同
    #     self.setWindowTitle(self.path)
    #     # self.sender_name = sender
    #     # self.view.camera_id = sender
    #     # self.view.clear_all_dots()
    def load_image(self,sender, path,position_id,dot_num=3):
        self.view.clear_all_dots()
        self.view.__class__.point_num = dot_num
        self.sender_name = sender
        self.view.camera_id = sender
        self.view.position_id = position_id
        self.path = path
        self.view.image_path = path
        image = self.detect_red_points(path)
        # image = cv2.imread(path)
        # image = self.find_circle(path)
        # Convert the NumPy array to QImage
        height, width, channels = image.shape
        bytes_per_line = channels * width
        q_image = QImage(image.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()

        self.pixmapItem.setPixmap(QPixmap.fromImage(q_image))
        # pixmap = QPixmap(path)
        # self.pixmapItem.setPixmap(pixmap)
        # 设置场景大小与图片大小相同
        self.view.setSceneRect(QRectF(self.pixmapItem.boundingRect()))
        # self.view.setSceneRect(QRectF(pixmap.rect()))  # 设置场景大小与图片大小相同
        self.setWindowTitle(path)

        # TODO 识别点数方法
    def find_final_points(self,image_gray, filtered_corners):
        ret_list = []
        for i in filtered_corners:
            x, y = i.ravel()
            max_intensity = 0
            center_x, center_y = x, y
            for dx in range(-5, 6):
                for dy in range(-5, 6):
                    new_x, new_y = x + dx, y + dy
                    if 0 <= new_x < image_gray.shape[1] and 0 <= new_y < image_gray.shape[0]:
                        intensity = image_gray[new_y, new_x]
                        if intensity > max_intensity:
                            max_intensity = intensity
                            center_x, center_y = new_x, new_y
            ret_list.append([center_x, center_y])
        return ret_list
    def min_enclosing_circle(self,points):
        """
        返回一个圆，该圆包含给定的所有点，并且具有最小的可能半径。
        请注意，这不是最优解，但对于小规模点集足够有效。
        """
        center, radius = cv2.minEnclosingCircle(points)
        return center, radius
    def filter_point(self,min_d,max_d,corners):
        filtered_corners=[]
        for i in corners:
            for j in corners:
                if i is not j:
                    dist = np.linalg.norm(i - j)
                    if int(dist)==0:
                        continue
                    if min_d <= dist <= max_d:
                        print("dist:", dist)
                        filtered_corners.append(i)
                        break
                    else:
                        pass
                        print("fail dist:", dist)
        return filtered_corners

    def find_brightness(self, edge_coords, gray):
        ret_list = []
        radius = 10  # 半径设置为 10
        for [y, x] in edge_coords:
            max_intensity = 0
            # 首先确定区域内的最亮点
            for dx in range(-radius, radius + 1):
                for dy in range(-radius, radius + 1):
                    if dx * dx + dy * dy <= radius * radius:  # 确保点在圆内
                        nx, ny = x + dx, y + dy
                        if 0 <= nx < gray.shape[1] and 0 <= ny < gray.shape[0]:
                            intensity = gray[ny, nx]
                            if intensity > max_intensity:
                                max_intensity = intensity

            # 确定亮度阈值
            threshold = 0.7 * max_intensity  # 举例定为最高亮度的 80%

            # 寻找区域内高于阈值的所有点
            coords_x, coords_y = [], []
            for dx in range(-radius, radius + 1):
                for dy in range(-radius, radius + 1):
                    if dx * dx + dy * dy <= radius * radius:
                        nx, ny = x + dx, y + dy
                        if 0 <= nx < gray.shape[1] and 0 <= ny < gray.shape[0] and gray[ny, nx] >= threshold:
                            coords_x.append(nx)
                            coords_y.append(ny)

            # 如果有符合的点，则计算它们的平均位置作为亮斑中心
            if coords_x and coords_y:
                spot_center = [int(np.mean(coords_x)), int(np.mean(coords_y))]
                ret_list.append(spot_center)
        return ret_list
    def detect_red_points(self,path):
        img = cv2.imread(path)
        # 将图像转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        image_gray_origin =copy.deepcopy(gray)

        # 使用 Canny 算法检测边缘
        edges = cv2.Canny(gray, 100, 200)
        # 查找边界
        edge_coordinates = np.argwhere(edges != 0)

        ret_list = []

        # 遍历所有边缘点
        for point in edge_coordinates:
            y, x = point

            # 只有当点与ret_list中所有点的距离都大于20时，才加入该点
            is_distinct = True
            for py, px in ret_list:
                if np.linalg.norm(np.array([y, x]) - np.array([py, px])) < 50:
                    is_distinct = False
                    break  # 如果发现一个小于20的距离，中断内层循环

            if is_distinct and x>managerQ.min_x and y>managerQ.min_y and x < managerQ.max_x and y< managerQ.max_y:
                ret_list.append([y, x])  # 加入点到结果列表

        # # 打印最终符合条件的点的坐标信息
        # for y, x in ret_list:5
        #     print(len(ret_list),f"Distinct Edge Point at: x={x}, y={y}")
        # if len(ret_list) >8:
        #     return img
        print("ret_list:",len(ret_list),ret_list)
        ret_list = self.find_brightness(ret_list,image_gray_origin)

        if len(ret_list) > 5:  # 6个点
            # 对滤波后的角点进行组合，选择五个点
            combos = list(combinations(ret_list, 5))
            # 初始化最小半径为正无穷
            min_radius = float('inf')
            best_combo = None
            # 对所有可能的组合进行迭代
            for combo in combos:
                # 计算当前组合的外接圆
                _, radius = self.min_enclosing_circle(np.array(combo))
                # 更新最小半径和最好的组合
                if radius < min_radius:
                    min_radius = radius
                    best_combo = combo
            ret_list = best_combo
        if len(ret_list) == 4 or len(ret_list) == 5:
            #
            ret_list =list(ret_list)
            ret_list.sort(key=lambda item: item[1], reverse=True)
            if managerQ.dot_num==4:
                point2 = copy.deepcopy(ret_list[-1])
                point4 = copy.deepcopy(ret_list[0])
                ret_list.sort(key=lambda item: item[0], reverse=True)
                point1 = copy.deepcopy(ret_list[-1])
                point3 = copy.deepcopy(ret_list[0])

                # 模拟点击
                self.view.simulate_mouse_click(x=int(point1[0]), y=int(point1[1]))
                self.view.simulate_mouse_click(x=int(point2[0]), y=int(point2[1]))
                self.view.simulate_mouse_click(x=int(point3[0]), y=int(point3[1]))
                self.view.simulate_mouse_click(x=int(point4[0]), y=int(point4[1]))


            elif managerQ.dot_num == 3:
            #
            # # 第三个点是ret_list[-2]
                point3 = copy.deepcopy(ret_list[0])
                ret_list.sort(key=lambda item: item[0], reverse=True)
                point2 = copy.deepcopy(ret_list[0])
                point1 = copy.deepcopy(ret_list[-1])
            # 模拟点击
                self.view.simulate_mouse_click(x=int(point1[0]), y=int(point1[1]))
                self.view.simulate_mouse_click(x=int(point2[0]), y=int(point2[1]))
                self.view.simulate_mouse_click(x=int(point3[0]), y=int(point3[1]))

                cv2.circle(img, (int(point1[0]), int(point1[1])), 3, (255, 0, 0))
                cv2.circle(img, (int(point2[0]), int(point2[1])), 3, (255, 0, 0))
                cv2.circle(img, (int(point3[0]), int(point3[1])), 3, (255, 0, 0))
                # 保存文件
                # t = int(time.time()*1000)
                # img_path = os.path.join(os.getcwd(),"img",f"{t}.jpg")
                # cv2.imwrite(img_path, img)

        return img

    def detect_red_points2(self,image):
        """
        识别出点坐标
        :param img:
        :return:
        """
        # origin = copy.deepcopy(image)
        image_gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        image_gray_origin =copy.deepcopy(image_gray)
        # 检测角点
        corners = cv2.goodFeaturesToTrack(image_gray, 6, 0.001, 100)
        # 转换为整数坐标
        corners = np.intp(corners)

        print(f"{self.view.camera_id} corners:",len(corners))
        if len(corners)<4:
            # 将图像转换为灰度
            core_width = 6
            kernel = np.ones((core_width, core_width), np.uint8)
            image_gray = cv2.morphologyEx(image_gray, cv2.MORPH_OPEN, kernel)
            corners = cv2.goodFeaturesToTrack(image_gray, 10, 0.001, 100)
            corners = np.intp(corners)
            print("{} 开运算 {} 之后 检测到 {} 点 ".format(self.view.camera_id,core_width,len(corners)))

        max_d = 300
        min_d = 100
        filtered_corners = self.filter_point(min_d,max_d,corners)
        filtered_corners = np.unique(filtered_corners, axis=0)
        # for i in filtered_corners:
        #     x, y = i.ravel()
        #     cv2.circle(image, (x, y), 3, (0, 255, 0))
        if len(filtered_corners) >5:# 6个点
            # 对滤波后的角点进行组合，选择五个点
            combos = list(combinations(filtered_corners, 5))
            # 初始化最小半径为正无穷
            min_radius = float('inf')
            best_combo = None
            # 对所有可能的组合进行迭代
            for combo in combos:
                # 计算当前组合的外接圆
                _, radius = self.min_enclosing_circle(np.array(combo))
                # 更新最小半径和最好的组合
                if radius < min_radius:
                    min_radius = radius
                    best_combo = combo
            filtered_corners = best_combo
            # # 处理找到的最小半径圆的点
            # for point in best_combo:
            #     x, y = point.ravel()
            #     # 绘制或者处理点（例如，发送点击事件）
            #     cv2.circle(image, (x, y), 3, (0, 0, 255), 2)
            #     print(f"处理点坐标: ({x}, {y})")
        if len(filtered_corners) == 4 or len(filtered_corners) == 5:
            ret_list = self.find_final_points(image_gray_origin, filtered_corners)
            ret_list.sort(key=lambda item: item[1], reverse=True)
            print(ret_list)
            # 第三个点是ret_list[-2]
            point3 =copy.deepcopy(ret_list[0])
            ret_list.sort(key=lambda item: item[0], reverse=True)
            point2 = copy.deepcopy(ret_list[0])
            point1 = copy.deepcopy(ret_list[-1])
            # 模拟点击
            self.view.simulate_mouse_click(x=int(point1[0]),y=int(point1[1]))
            self.view.simulate_mouse_click(x=int(point2[0]),y=int(point2[1]))
            self.view.simulate_mouse_click(x=int(point3[0]),y=int(point3[1]))



        elif len(filtered_corners) == 3:
            print("filtered_corners length is 3")
            # ret_list = self.find_final_points(image_gray_origin, filtered_corners)
            # ret_list.sort(key=lambda item: item[0], reverse=True)
            # point2 = copy.deepcopy(ret_list[0])
            # point1 = copy.deepcopy(ret_list[-1])
            # point3 = copy.deepcopy(ret_list[1])
            # self.view.simulate_mouse_click(x=int(point1[0]), y=int(point1[1]))
            # self.view.simulate_mouse_click(x=int(point2[0]), y=int(point2[1]))
            # self.view.simulate_mouse_click(x=int(point3[0]), y=int(point3[1]))
            # for i in filtered_corners:
            #     x, y = i.ravel()
            #     cv2.circle(image,(x,y),3,(0,255,0))




        return image

class GraphicsImageView(QGraphicsView):
    point_num = 3
    def __init__(self, parent=None):
        super(GraphicsImageView, self).__init__(parent)
        self.setRenderHints(QPainter.Antialiasing | QPainter.SmoothPixmapTransform)
        self.setDragMode(QGraphicsView.ScrollHandDrag)
        self.location_mode = False  # 新增一个属性来标记是否为定位模式
        self.red_dots = []  # 初始化红点列表
        self.coordinate_list = []
        self.image_path = None
        # self.point_num = 3
        self.camera_id = None



    def draw_red_dot(self, scene_pos):
        dot_radius = 3 # 设置红点的半径
        red_dot = self.scene().addEllipse(scene_pos.x() - dot_radius, scene_pos.y() - dot_radius, 2 * dot_radius,
                                          2 * dot_radius, brush=Qt.red)
        self.red_dots.append(red_dot)

    def wheelEvent(self, event):
        # 鼠标滚轮事件，放大或缩小图像
        zoomInFactor = 1.25
        zoomOutFactor = 1 / zoomInFactor
        # 设置放大或缩小
        if event.angleDelta().y() > 0:
            zoomFactor = zoomInFactor
        else:
            zoomFactor = zoomOutFactor
        self.scale(zoomFactor, zoomFactor)
    def handle_scene_pos(self,scene_pos):
        # tmp = int(managerQ.num_cameras/2)
        num = self.position_id # 第num组相机
        if len(self.coordinate_list) < self.point_num:
            left_map_x, left_map_y = managerQ.camera_config[str(num)]["left_map_x"], managerQ.camera_config[str(num)]["left_map_y"]
            right_map_x, right_map_y = managerQ.camera_config[str(num)]["right_map_x"], managerQ.camera_config[str(num)]["right_map_y"]
            # 需要校准坐标
            x = int(scene_pos.x())
            y = int(scene_pos.y())
            corrected_x= 0
            corrected_y= 0
            if "right_" in self.image_path:
            # if self.position_id >=tmp:# 右边相机 左边相机
                corrected_x = right_map_x[y][x]
                corrected_y = right_map_y[y][x]
                # right
            elif "left_" in self.image_path:
                corrected_x = left_map_x[y][x]
                corrected_y = left_map_y[y][x]
                # left
            # print("校准前:",(str(x), str(y)))
            # print("校准后:", (str(int(corrected_x)), str(int(corrected_y))))
            self.coordinate_list.append((str(int(x)), str(int(y))))
            # self.coordinate_list.append((str(int(corrected_x)), str(int(corrected_y))))
            self.draw_red_dot(scene_pos)  # 在点击的位置绘制红点
        self.exit_location_mode()  # 退出定位模式
        self.location_mode = False
        if len(self.coordinate_list) == self.point_num:
            # print("self.coordinate_list",self.coordinate_list)
            if "left_" in self.image_path:
                managerQ.red_dot_finish.emit(self.camera_id, self.coordinate_list,"left")
            elif "right_" in self.image_path:
                managerQ.red_dot_finish.emit(self.camera_id, self.coordinate_list, "right")

    def mousePressEvent(self, event):
        if event.button() == Qt.RightButton:
            self.show_context_menu(event)
        elif event.button() == Qt.LeftButton and self.location_mode:
            scene_pos = self.mapToScene(event.pos())
            # scene_pos = event.scenePos()

            self.handle_scene_pos(scene_pos)
            # 判断是否已经完成3个点
        super(GraphicsImageView, self).mousePressEvent(event)

    def simulate_mouse_click(self, x, y, button=Qt.LeftButton):
        class Scene_pos():
            def x(self):
                return x

            def y(self):
                return y

        scene_pos = Scene_pos()
        self.handle_scene_pos(scene_pos)


    def show_context_menu(self, event):
        context_menu = QMenu(self)

        locate_action = QAction("定位", self)
        clear_one_dot = QAction("删除一个点", self)
        clear_dot = QAction("清空", self)
        locate_action.triggered.connect(self.activate_location_mode)
        clear_one_dot.triggered.connect(self.clear_one_dot)
        clear_dot.triggered.connect(self.clear_all_dots)
        context_menu.addAction(locate_action)
        context_menu.addAction(clear_dot)
        context_menu.addAction(clear_one_dot)

        context_menu.exec_(QCursor.pos())

    def clear_one_dot(self):
        if self.red_dots:  # 防止列表为空的时候调用
            last_red_dot = self.red_dots.pop()  # 取出最后一个红点
            self.scene().removeItem(last_red_dot)  # 从场景中移除这个红点
            self.coordinate_list.pop()  # 删除对应的坐标
            self.viewport().update()  # 刷新视图
            self.update()  # 更新视图

    def clear_all_dots(self):
        for red_dot in self.red_dots:
            self.scene().removeItem(red_dot)  # 从场景中移除红点
        self.viewport().update()  # 刷新视图
        self.red_dots = []
        self.coordinate_list = []


    def activate_location_mode(self):
        self.location_mode = True
        self.setDragMode(QGraphicsView.NoDrag)  # 禁用拖动模式
        self.setCursor(Qt.CrossCursor)  # 将鼠标指针设置为十字光标

    def exit_location_mode(self):
        self.location_mode = False
        self.setDragMode(QGraphicsView.ScrollHandDrag)  # 重新启用拖动模式
        self.setCursor(Qt.ArrowCursor)  # 将鼠标指针设置回默认形状