import threading
import time
import logging
import traceback

from PyQt5.QtCore import QObject, pyqtSignal

from .RelayClient import relay_client

logger = logging.getLogger("")


class ThreadStopException(SystemExit):
    pass


class WorkerThread(threading.Thread):
    def __init__(self):
        super().__init__()

        self.daemon = True

        self._stop_flag = False

    def check_point(self):
        if self._stop_flag:
            raise ThreadStopException()

    def stop(self):
        self._stop_flag = True
        while self.is_alive():
            print("*****")
            time.sleep(0.1)


class Service1Worker(WorkerThread):
    def __init__(self, service):
        super().__init__()

        self.service = service

    def run(self):
        try:
            print("service1 started")
            self.service.power_started_signal.emit()
            self.service.process()
        except ThreadStopException:
            print("service1 stopped")
        except Exception:
            print("service1 工作线程崩溃！\n%s", traceback.format_exc())

        print("service1 finished")
        self.service.power_stopped_signal.emit()


class Service1(QObject):
    power_on_time_changed_signal = pyqtSignal(int)
    power_off_time_changed_signal = pyqtSignal(int)
    power_started_signal = pyqtSignal()
    power_stopped_signal = pyqtSignal()
    power_remain_count_changed_signal = pyqtSignal(int)

    def __init__(self):
        super().__init__(parent=None)

        self.worker = None

        self.on_time = None
        self.off_time = None
        self.count = None
        self.delay = None

    def process(self):
        if self.count == 0:
            while True:
                self.once_work()
        else:
            for i in range(self.count):
                self.once_work()
                self.power_remain_count_changed_signal.emit(self.count - i - 1)

    def once_work(self):
        num = self.delay.replace("Y","")
        relay_client.set_relay_status(int(num), True)
        # if self.delay == "Y1":
        #     client.set_relay1_status(True)
        # elif self.delay == "Y2":
        #     client.set_relay2_status(True)
        # elif self.delay == "Y3":
        #     client.set_relay3_status(True)
        # elif self.delay == "Y4":
        #     client.set_relay4_status(True)
        # elif self.delay == "Y5":
        #     client.set_relay5_status(True)
        # elif self.delay == "Y6":
        #     client.set_relay6_status(True)
        # elif self.delay == "Y7":
        #     client.set_relay7_status(True)
        # elif self.delay == "Y8":
        #     client.set_relay8_status(True)
        # else:
        #     return

        self.check_point()

        count = self.on_time
        while count > 0:
            time.sleep(0.1)
            count -= 0.1
            self.check_point()
            self.power_on_time_changed_signal.emit(
                int((self.on_time - count) / self.on_time * 100)
            )
        self.power_on_time_changed_signal.emit(100)
        num = self.delay.replace("Y", "")
        relay_client.set_relay_status(int(num), False)
        # if self.delay == "Y1":
        #     client.set_relay1_status(False)
        # elif self.delay == "Y2":
        #     client.set_relay2_status(False)
        # elif self.delay == "Y3":
        #     client.set_relay3_status(False)
        # elif self.delay == "Y4":
        #     client.set_relay4_status(False)
        # elif self.delay == "Y5":
        #     client.set_relay5_status(False)
        # elif self.delay == "Y6":
        #     client.set_relay6_status(False)
        # elif self.delay == "Y7":
        #     client.set_relay7_status(False)
        # elif self.delay == "Y8":
        #     client.set_relay8_status(False)
        # else:
        #     return

        self.check_point()

        count = self.off_time
        while count > 0:
            time.sleep(0.1)
            count -= 0.1

            self.check_point()
            self.power_off_time_changed_signal.emit(
                int((self.off_time - count) / self.off_time * 100)
            )
        self.power_off_time_changed_signal.emit(100)

        self.power_on_time_changed_signal.emit(0)
        self.power_off_time_changed_signal.emit(0)

    def check_point(self):
        self.worker.check_point()

    def start(self, on_time, off_time, count, delay):
        self.on_time = on_time
        self.off_time = off_time
        self.count = count
        self.delay = delay

        if self.worker:
            self.worker.stop()
        self.worker = Service1Worker(self)
        self.worker.start()

    def stop(self):
        if self.worker:
            self.worker.stop()
