import json
import os
import shutil
import string

from PyQt5.QtCore import pyqtSignal, QObject

from simbox_tools.Logger import Logger

# 通过校验MD5 判断B内的文件与A 不同
# from core import configData


class CommonMethod(QObject):
    __logTag = "CommonMethod"
    title_signal = pyqtSignal(object)
    progress_signal = pyqtSignal(object)

    def get_MD5(self, file_path):
        files_md5 = os.popen('md5 %s' % file_path).read().strip()
        file_md5 = files_md5.replace('MD5 (%s) = ' % file_path, '')
        return file_md5

    # 获取盘符路径
    def get_disklist(self):
        disk_list = []
        for c in string.ascii_uppercase:
            disk = c + ':/'
            if os.path.isdir(disk):
                disk_list.append(disk)
        return disk_list

    def get_h264_folder(self, sign_file):
        h264_folder = None
        disk_list = self.get_disklist()
        for disk in disk_list:
            file_path = disk + sign_file
            if os.path.exists(file_path):
                h264_folder = disk

        return h264_folder

    def copy_file_folder(self, path, out):
        Logger.console(self.__logTag, "copy_file_folder -> %s,%s" % (path, out))
        try:
            if len(os.listdir(path)) > 0:
                self.title_signal.emit('img resource importing')
            else:
                return

            m = 0
            for files in os.listdir(path):
                name = os.path.join(path, files)
                back_name = os.path.join(out, files)
                if os.path.isfile(name):
                    if os.path.isfile(back_name):
                        # if get_MD5(name) != get_MD5(back_name):
                        shutil.copy(name, back_name)
                    else:
                        shutil.copy(name, back_name)
                    m += 1
                    progress = int(m / len(os.listdir(path)) * 100)
                    self.progress_signal.emit(progress)
                else:
                    if not os.path.isdir(back_name):
                        os.makedirs(back_name)
                    self.copy_file_folder(name, back_name)
        except Exception as e:
            Logger.console(self.__logTag, "copy_file_folder -> %s" % e.args)

    def get_img_complete_path(self, pro_data):
        try:
            img_data = []
            img_path_data = []
            for i in pro_data['emoji']:
                if i['is_delete'] == 1:
                    continue
                img_sources_path = i['source']
                img_source_path = os.getcwd() + img_sources_path
                begin_path = i['crop_from']
                end_path = i['crop_to']
                reverse_state = i['is_reverse']
                for root, dirs, files in os.walk(img_source_path):
                    begin_index = files.index(begin_path)
                    end_index = files.index(end_path)
                    if reverse_state:
                        files = files[end_index:begin_index + 1][::-1]
                    else:
                        files = files[begin_index:end_index + 1]
                    for file in files:
                        complete_path = root + '/' + file
                        img_path_data.append(complete_path)
                    img_data.extend(img_path_data)
                    img_path_data = []
            return img_data
        except Exception as e:
            Logger.console(self.__logTag, "get_img_complete_path -> %s" % e.args)

    def export_folder(self, project_data, export_folder):
        try:
            folder_list = os.listdir(export_folder)
            for folder in folder_list:
                path = export_folder + '/' + folder
                if os.path.isdir(path):
                    shutil.rmtree(path)
            for emoji_data in project_data['emoji']:
                emoji_name = emoji_data['name']
                emoji_time = emoji_data['time']
                emoji_img_source = emoji_data['source']
                emoji_source = os.getcwd() + emoji_img_source
                emoji_crop_from = emoji_data['crop_from']
                emoji_crop_to = emoji_data['crop_to']
                is_reverse = emoji_data['is_reverse']
                img_list = os.listdir(emoji_source)
                save_img_list = []
                emoji_crop_from_index = img_list.index(emoji_crop_from)
                emoji_crop_to_index = img_list.index(emoji_crop_to)
                if is_reverse:
                    img_list = img_list[emoji_crop_to_index:emoji_crop_from_index + 1]
                else:
                    img_list = img_list[emoji_crop_from_index:emoji_crop_to_index + 1]
                export_image_path = export_folder + '/emoji/{}/'.format(emoji_name)
                for img in img_list:
                    if not os.path.exists(export_image_path):
                        os.makedirs(export_image_path)
                    shutil.copy(emoji_source + '/' + img, export_image_path + img)
                # [save_img_list.append(configData.getExportEmojiFolder() + emoji_name + '/' + img) for img in img_list]
                # data = {"name": emoji_name, "imageSequence": save_img_list, "time": emoji_time}
                # with open(export_folder + '/emoji/{}/{}.json'.format(emoji_name, emoji_name), 'w') as f:
                #     f.write(json.dumps(data, indent=4))
            for pitch_data in project_data['pitch']:
                pitch_name = pitch_data['name']
                pitch_time = pitch_data['motion']['duration']
                pitch_direction = 'pitch'
                pitch_move_range = pitch_data['motion']['move_range']
                pitch_In = pitch_data['motion']['in']
                pitch_Out = pitch_data['motion']['out']
                pitch_loop = pitch_data['motion']['loop']
                pitch_path = export_folder + '/pitch'
                if not os.path.exists(pitch_path):
                    os.makedirs(pitch_path)
                data = {"name": pitch_name, "direction": pitch_direction, "loop": pitch_loop, "time": pitch_time,
                        "moveRange": pitch_move_range, "Ind.In": pitch_In, "Ind.Out": pitch_Out}
                with open(pitch_path + '/{}.json'.format(pitch_name), 'w') as f:
                    f.write(json.dumps(data, indent=4))
            for yaw_data in project_data['yaw']:
                yaw_name = yaw_data['name']
                yaw_time = yaw_data['motion']['duration']
                yaw_direction = 'yaw'
                yaw_move_range = yaw_data['motion']['move_range']
                yaw_ind_in = yaw_data['motion']['in']
                yaw_ind_out = yaw_data['motion']['out']
                yaw_loop = yaw_data['motion']['loop']
                yaw_path = export_folder + '/yaw'
                if not os.path.exists(yaw_path):
                    os.makedirs(yaw_path)
                data = {"name": yaw_name, "direction": yaw_direction, "loop": yaw_loop, "time": yaw_time,
                        "moveRange": yaw_move_range, "Ind.In": yaw_ind_in, "Ind.Out": yaw_ind_out}
                with open(yaw_path + '/{}.json'.format(yaw_name), 'w') as f:
                    f.write(json.dumps(data, indent=4))
        except Exception as e:
            Logger.console(self.__logTag, "export_folder -> %s" % e.args)

    def export_pitch_folder(self, project_data, export_folder):
        try:
            self.title_signal.emit('kinetic importing')
            m = 0
            for pitch_data in project_data['pitch']:
                pitch_name = pitch_data['name']
                pitch_time = pitch_data['motion']['duration']
                pitch_direction = 'pitch'
                pitch_move_range = pitch_data['motion']['move_range'] + 50
                pitch_In = pitch_data['motion']['in']
                pitch_Out = pitch_data['motion']['out']
                pitch_loop = pitch_data['motion']['loop']
                pitch_path = export_folder + 'pitch'
                if not os.path.exists(pitch_path):
                    os.makedirs(pitch_path)
                data = {"name": pitch_name, "direction": pitch_direction, "type": pitch_data['motion']['type'],
                        "loop": pitch_loop, "time": pitch_time,
                        "moveRange": pitch_move_range, "Ind.In": pitch_In, "Ind.Out": pitch_Out}
                with open(pitch_path + '/{}.json'.format(pitch_name), 'w') as f:
                    f.write(json.dumps(data, indent=4))
                m += 1
                progress = int(m / len(project_data['pitch']) * 100)
                self.progress_signal.emit(progress)
            m = 0
            for yaw_data in project_data['yaw']:
                yaw_name = yaw_data['name']
                yaw_time = yaw_data['motion']['duration']
                yaw_direction = 'yaw'
                yaw_move_range = yaw_data['motion']['move_range'] + 110
                yaw_ind_in = yaw_data['motion']['in']
                yaw_ind_out = yaw_data['motion']['out']
                yaw_loop = yaw_data['motion']['loop']
                yaw_path = export_folder + 'yaw'
                if not os.path.exists(yaw_path):
                    os.makedirs(yaw_path)
                data = {"name": yaw_name, "direction": yaw_direction, "type": yaw_data['motion']['type'],
                        "loop": yaw_loop, "time": yaw_time,
                        "moveRange": yaw_move_range, "Ind.In": yaw_ind_in, "Ind.Out": yaw_ind_out}
                with open(yaw_path + '/{}.json'.format(yaw_name), 'w') as f:
                    f.write(json.dumps(data, indent=4))
                m += 1
                progress = int(m / len(project_data['yaw']) * 100)
                self.progress_signal.emit(progress)
        except Exception as e:
            Logger.console(self.__logTag, "export_pitch_folder -> %s" % e.args)

    def create_export_json(self, project_data, project_name):
        try:
            datas = []
            pit_data = []
            ya_data = []
            emoji_origin_time = 0
            pitch_sum_time = 0
            yaw_sum_time = 0
            pitch_origin_time = 0
            yaw_origin_time = 0
            n = 0
            m = 0
            for emoji_data in project_data['emoji']:
                emoji_name = emoji_data['name']
                emoji_time = emoji_data['time']
                emoji_time += emoji_origin_time
                datas.append({"name": emoji_name, "imageSequence": "{}.json".format(emoji_name),
                              "minRangeMs": emoji_origin_time, "maxRangeMs": emoji_time,
                              "kinetics": {"pitch": [], "yaw": []}})
                emoji_origin_time += emoji_data['time']
                for pitch_data in project_data['pitch']:
                    if 'is_used' in pitch_data:
                        if pitch_data['is_used']:
                            continue
                        else:
                            pitch_name = pitch_data['name']
                            if pitch_sum_time < emoji_time:
                                pit_data.append({"name": pitch_name, "minRangeMs": pitch_origin_time,
                                                 "maxRangeMs": pitch_sum_time,
                                                 "kineticSequence": "{}.json".format(pitch_name)})
                                pitch_origin_time = pitch_sum_time
                                pitch_data['is_used'] = True
                                continue
                    pitch_name = pitch_data['name']
                    pitch_time = int(pitch_data['motion']['duration'])
                    pitch_sum_time += pitch_time
                    if emoji_time < pitch_sum_time:
                        pit_data.append({"name": pitch_name, "minRangeMs": pitch_origin_time,
                                         "maxRangeMs": emoji_time, "kineticSequence": "{}.json".format(pitch_name)})
                        pitch_origin_time = emoji_time
                        pitch_data['is_used'] = False
                        datas[n]["kinetics"]['pitch'] = pit_data
                        n += 1
                        pit_data = []
                        break
                    elif emoji_time == pitch_sum_time:
                        pit_data.append({"name": pitch_name, "minRangeMs": pitch_origin_time,
                                         "maxRangeMs": emoji_time, "kineticSequence": "{}.json".format(pitch_name)})
                        pitch_origin_time = pitch_sum_time
                        pitch_data['is_used'] = True
                        datas[n]["kinetics"]['pitch'] = pit_data
                        n += 1
                        pit_data = []
                        break
                    else:
                        pit_data.append({"name": pitch_name, "minRangeMs": pitch_origin_time,
                                         "maxRangeMs": pitch_sum_time,
                                         "kineticSequence": "{}.json".format(pitch_name)})
                        pitch_origin_time = pitch_sum_time
                        pitch_data['is_used'] = True
                        datas[n]["kinetics"]['pitch'] = pit_data
                for yaw_data in project_data['yaw']:
                    if 'is_used' in yaw_data:
                        if yaw_data['is_used']:
                            continue
                        else:
                            yaw_name = yaw_data['name']
                            if yaw_sum_time < emoji_time:
                                ya_data.append({"name": yaw_name, "minRangeMs": yaw_origin_time,
                                                "maxRangeMs": yaw_sum_time,
                                                "kineticSequence": "{}.json".format(yaw_name)})
                                yaw_origin_time = yaw_sum_time
                                yaw_data['is_used'] = True
                                continue
                    yaw_name = yaw_data['name']
                    yaw_time = int(yaw_data['motion']['duration'])
                    yaw_sum_time += yaw_time
                    if emoji_time < yaw_sum_time:
                        ya_data.append({"name": yaw_name, "minRangeMs": yaw_origin_time,
                                        "maxRangeMs": emoji_time, "kineticSequence": "{}.json".format(yaw_name)})
                        yaw_origin_time = emoji_time
                        yaw_data['is_used'] = False
                        datas[m]["kinetics"]['yaw'] = ya_data
                        m += 1
                        ya_data = []
                        break
                    elif emoji_time == yaw_sum_time:
                        ya_data.append({"name": yaw_name, "minRangeMs": yaw_origin_time,
                                        "maxRangeMs": yaw_sum_time, "kineticSequence": "{}.json".format(yaw_name)})
                        yaw_origin_time = yaw_sum_time
                        yaw_data['is_used'] = True
                        datas[m]["kinetics"]['yaw'] = ya_data
                        m += 1
                        ya_data = []
                        break
                    else:
                        ya_data.append({"name": yaw_name, "minRangeMs": yaw_origin_time,
                                        "maxRangeMs": yaw_sum_time, "kineticSequence": "{}.json".format(yaw_name)})
                        yaw_origin_time = yaw_sum_time
                        yaw_data['is_used'] = True
                        datas[m]["kinetics"]['yaw'] = ya_data
            data = {"name": project_name, "playback": datas}
            return data
        except Exception as e:
            Logger.console(self.__logTag, "create_export_json -> %s" % e.args)

    def create_inner_export_json(self, emoji_time, project_data, project_name):
        try:
            datas = {"name": project_name + '.h264', "minRangeMs": 0, "maxRangeMs": emoji_time,
                     "kinetics": {"pitch": [], "yaw": []}}
            pitch_origin_time = 0
            self.title_signal.emit('kinetic json file importing')
            m = 0
            for pitch_data in project_data['pitch']:
                pitch_name = pitch_data['name']
                pitch_time = pitch_data['motion']['duration']
                pitch_time += pitch_origin_time
                pit_data = {"name": pitch_name, "minRangeMs": pitch_origin_time,
                            "maxRangeMs": pitch_time, "kineticSequence": "/pitch/{}.json".format(pitch_name)}
                pitch_origin_time = pitch_time
                datas['kinetics']['pitch'].append(pit_data)
                m += 1
                progress = int(m / len(project_data['pitch']) * 100)
                self.progress_signal.emit(progress)
            yaw_origin_time = 0
            m = 0
            for yaw_data in project_data['yaw']:
                yaw_name = yaw_data['name']
                yaw_time = yaw_data['motion']['duration']
                yaw_time += yaw_origin_time
                ya_data = {"name": yaw_name, "minRangeMs": yaw_origin_time,
                           "maxRangeMs": yaw_time, "kineticSequence": "/yaw/{}.json".format(yaw_name)}
                yaw_origin_time = yaw_time
                datas['kinetics']['yaw'].append(ya_data)
                m += 1
                progress = int(m / len(project_data['yaw']) * 100)
                self.progress_signal.emit(progress)
            data = {"name": project_name, "playback": datas}
            return data
        except Exception as e:
            Logger.console(self.__logTag, "create_inner_export_json -> %s" % e.args)

    def copy_img_project_folder(self, _directory, frame_list, img_list):
        add_list = []
        # emoji_img_folder = configData.project_json_file + 'emoji/'
        # [os.remove(os.path.join(emoji_img_folder, i)) for i in os.listdir(emoji_img_folder)]
        # if not os.path.exists(emoji_img_folder):
        #     os.makedirs(emoji_img_folder)
        # n = 0
        # for frame in frame_list:
        #     img_num = img_list[frame].split('_')[-1].split('.')[0]
        #     if n < 10:
        #         update_img_name = img_list[frame].replace(img_num, '0000{}'.format(n))
        #     elif 9 < n < 100:
        #         update_img_name = img_list[frame].replace(img_num, '000{}'.format(n))
        #     elif 99 < n < 1000:
        #         update_img_name = img_list[frame].replace(img_num, '00{}'.format(n))
        #     elif 999 < n < 10000:
        #         update_img_name = img_list[frame].replace(img_num, '0{}'.format(n))
        #     else:
        #         update_img_name = img_list[frame].replace(img_num, '{}'.format(n))
        #     add_list.append(update_img_name)
        #     shutil.copyfile(_directory + '/' + img_list[frame], emoji_img_folder + '{}'.format(update_img_name))
        #     n += 1
        return add_list

    @staticmethod
    def iterable_byte_to_char(data, separate=''):
        """
        将byte数组转化为hex
        :param data: 原始byte数组
        :param separate: 分隔符
        :return: 字符串
        """
        return separate.join([chr(item) for item in data])

    @staticmethod
    def iterable_int_to_hex(data, separate=''):
        """
        将int数组转化为hex
        :param data: 原始int数组
        :param separate: 分隔符
        :return: hex字符数组
        """
        return separate.join([('%X' % i).zfill(2) for i in data])

    @staticmethod
    def iterable_str_to_hex(data):
        """
        将str数组转化为byte数组
        :param data: 原始str数组
        :return: byte数组
        """
        return [int(i, 16) for i in data]

    def check_image(self, image_source):
        try:
            n = int(image_source[0].split('_')[-1].split('.')[0])
            for frame in range(len(image_source)):
                img_num = int(image_source[frame].split('_')[-1].split('.')[0])
                if n != img_num:
                    return True
                n += 1
            return False
        except Exception as e:
            Logger.console(self.__logTag, "check_image -> %s" % e.args)
            return True
