<?xml version="1.0" encoding="utf-8"?>
<!--This file was saved with a tool from Vector Informatik GmbH-->
<AUTOSAR xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_4-2-2.xsd" xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>VectorAutosarExplorerGeneratedObjects</SHORT-NAME>
      <AR-PACKAGES>
        <AR-PACKAGE>
          <SHORT-NAME>SYSTEM</SHORT-NAME>
          <ELEMENTS>
            <SYSTEM>
              <SHORT-NAME>System</SHORT-NAME>
              <FIBEX-ELEMENTS>
                <FIBEX-ELEMENT-REF-CONDITIONAL>
                  <FIBEX-ELEMENT-REF DEST="CAN-CLUSTER">/VectorAutosarExplorerGeneratedObjects/New_CanCluster/New_CanCluster</FIBEX-ELEMENT-REF>
                </FIBEX-ELEMENT-REF-CONDITIONAL>
                <FIBEX-ELEMENT-REF-CONDITIONAL>
                  <FIBEX-ELEMENT-REF DEST="ECU-INSTANCE">/VectorAutosarExplorerGeneratedObjects/ECU_INSTANCES/New_ECU</FIBEX-ELEMENT-REF>
                </FIBEX-ELEMENT-REF-CONDITIONAL>
                <FIBEX-ELEMENT-REF-CONDITIONAL>
                  <FIBEX-ELEMENT-REF DEST="CAN-FRAME">/VectorAutosarExplorerGeneratedObjects/FRAME/New_Frame</FIBEX-ELEMENT-REF>
                </FIBEX-ELEMENT-REF-CONDITIONAL>
                <FIBEX-ELEMENT-REF-CONDITIONAL>
                  <FIBEX-ELEMENT-REF DEST="I-SIGNAL">/VectorAutosarExplorerGeneratedObjects/I_SIGNALS/New_Frame_NewPDU_NewSignal</FIBEX-ELEMENT-REF>
                </FIBEX-ELEMENT-REF-CONDITIONAL>
                <FIBEX-ELEMENT-REF-CONDITIONAL>
                  <FIBEX-ELEMENT-REF DEST="I-SIGNAL">/VectorAutosarExplorerGeneratedObjects/I_SIGNALS/New_Frame_NewPDU_NewSignal_1</FIBEX-ELEMENT-REF>
                </FIBEX-ELEMENT-REF-CONDITIONAL>
                <FIBEX-ELEMENT-REF-CONDITIONAL>
                  <FIBEX-ELEMENT-REF DEST="I-SIGNAL">/VectorAutosarExplorerGeneratedObjects/I_SIGNALS/New_Frame_NewPDU_NewSignal_2</FIBEX-ELEMENT-REF>
                </FIBEX-ELEMENT-REF-CONDITIONAL>
                <FIBEX-ELEMENT-REF-CONDITIONAL>
                  <FIBEX-ELEMENT-REF DEST="I-SIGNAL">/VectorAutosarExplorerGeneratedObjects/I_SIGNALS/New_Frame_NewPDU_NewSignal_3</FIBEX-ELEMENT-REF>
                </FIBEX-ELEMENT-REF-CONDITIONAL>
                <FIBEX-ELEMENT-REF-CONDITIONAL>
                  <FIBEX-ELEMENT-REF DEST="I-SIGNAL">/VectorAutosarExplorerGeneratedObjects/I_SIGNALS/New_Frame_NewPDU_NewSignal_4</FIBEX-ELEMENT-REF>
                </FIBEX-ELEMENT-REF-CONDITIONAL>
                <FIBEX-ELEMENT-REF-CONDITIONAL>
                  <FIBEX-ELEMENT-REF DEST="I-SIGNAL">/VectorAutosarExplorerGeneratedObjects/I_SIGNALS/New_Frame_NewPDU_NewSignal_5</FIBEX-ELEMENT-REF>
                </FIBEX-ELEMENT-REF-CONDITIONAL>
                <FIBEX-ELEMENT-REF-CONDITIONAL>
                  <FIBEX-ELEMENT-REF DEST="I-SIGNAL-I-PDU">/VectorAutosarExplorerGeneratedObjects/PDUS/New_Frame_NewPDU</FIBEX-ELEMENT-REF>
                </FIBEX-ELEMENT-REF-CONDITIONAL>
                <FIBEX-ELEMENT-REF-CONDITIONAL>
                  <FIBEX-ELEMENT-REF DEST="I-SIGNAL-I-PDU-GROUP">/VectorAutosarExplorerGeneratedObjects/PDU_GROUP/PduGroup_3d1efacc48324fb6a458184295d222c5_Rx</FIBEX-ELEMENT-REF>
                </FIBEX-ELEMENT-REF-CONDITIONAL>
              </FIBEX-ELEMENTS>
            </SYSTEM>
          </ELEMENTS>
        </AR-PACKAGE>
        <AR-PACKAGE>
          <SHORT-NAME>New_CanCluster</SHORT-NAME>
          <ELEMENTS>
            <CAN-CLUSTER>
              <SHORT-NAME>New_CanCluster</SHORT-NAME>
              <CAN-CLUSTER-VARIANTS>
                <CAN-CLUSTER-CONDITIONAL>
                  <BAUDRATE>250000</BAUDRATE>
                  <PHYSICAL-CHANNELS>
                    <CAN-PHYSICAL-CHANNEL>
                      <SHORT-NAME>CANChannel</SHORT-NAME>
                      <COMM-CONNECTORS>
                        <COMMUNICATION-CONNECTOR-REF-CONDITIONAL>
                          <COMMUNICATION-CONNECTOR-REF DEST="CAN-COMMUNICATION-CONNECTOR">/VectorAutosarExplorerGeneratedObjects/ECU_INSTANCES/New_ECU/Connector_New_ECU_1c8171768535fd8f</COMMUNICATION-CONNECTOR-REF>
                        </COMMUNICATION-CONNECTOR-REF-CONDITIONAL>
                      </COMM-CONNECTORS>
                      <FRAME-TRIGGERINGS>
                        <CAN-FRAME-TRIGGERING>
                          <SHORT-NAME>NewFrameTriggering_da879414224fd9a2</SHORT-NAME>
                          <FRAME-PORT-REFS>
                            <FRAME-PORT-REF DEST="FRAME-PORT">/VectorAutosarExplorerGeneratedObjects/ECU_INSTANCES/New_ECU/Connector_New_ECU_1c8171768535fd8f/framePort_fee56426c0f5d2a3</FRAME-PORT-REF>
                          </FRAME-PORT-REFS>
                          <FRAME-REF DEST="CAN-FRAME">/VectorAutosarExplorerGeneratedObjects/FRAME/New_Frame</FRAME-REF>
                          <PDU-TRIGGERINGS>
                            <PDU-TRIGGERING-REF-CONDITIONAL>
                              <PDU-TRIGGERING-REF DEST="PDU-TRIGGERING">/VectorAutosarExplorerGeneratedObjects/New_CanCluster/New_CanCluster/CANChannel/NewPduTriggering_a1c914924e7a9908</PDU-TRIGGERING-REF>
                            </PDU-TRIGGERING-REF-CONDITIONAL>
                          </PDU-TRIGGERINGS>
                          <CAN-ADDRESSING-MODE>STANDARD</CAN-ADDRESSING-MODE>
                          <CAN-FRAME-TX-BEHAVIOR>CAN-FD</CAN-FRAME-TX-BEHAVIOR>
                          <IDENTIFIER>1</IDENTIFIER>
                        </CAN-FRAME-TRIGGERING>
                      </FRAME-TRIGGERINGS>
                      <I-SIGNAL-TRIGGERINGS>
                        <I-SIGNAL-TRIGGERING>
                          <SHORT-NAME>NewSignalTriggering_b62ee9d4c50e6c9a</SHORT-NAME>
                          <I-SIGNAL-PORT-REFS>
                            <I-SIGNAL-PORT-REF DEST="I-SIGNAL-PORT">/VectorAutosarExplorerGeneratedObjects/ECU_INSTANCES/New_ECU/Connector_New_ECU_1c8171768535fd8f/SP_cef6f2b8d73546568794359c9a2a4f5c_Rx</I-SIGNAL-PORT-REF>
                          </I-SIGNAL-PORT-REFS>
                          <I-SIGNAL-REF DEST="I-SIGNAL">/VectorAutosarExplorerGeneratedObjects/I_SIGNALS/New_Frame_NewPDU_NewSignal</I-SIGNAL-REF>
                        </I-SIGNAL-TRIGGERING>
                        <I-SIGNAL-TRIGGERING>
                          <SHORT-NAME>NewSignalTriggering_7cfa964bc203bb08</SHORT-NAME>
                          <I-SIGNAL-PORT-REFS>
                            <I-SIGNAL-PORT-REF DEST="I-SIGNAL-PORT">/VectorAutosarExplorerGeneratedObjects/ECU_INSTANCES/New_ECU/Connector_New_ECU_1c8171768535fd8f/SP_7a4fcad1cfba48098e94f0573468de09_Rx</I-SIGNAL-PORT-REF>
                          </I-SIGNAL-PORT-REFS>
                          <I-SIGNAL-REF DEST="I-SIGNAL">/VectorAutosarExplorerGeneratedObjects/I_SIGNALS/New_Frame_NewPDU_NewSignal_1</I-SIGNAL-REF>
                        </I-SIGNAL-TRIGGERING>
                        <I-SIGNAL-TRIGGERING>
                          <SHORT-NAME>NewSignalTriggering_88b663171f0a13df</SHORT-NAME>
                          <I-SIGNAL-PORT-REFS>
                            <I-SIGNAL-PORT-REF DEST="I-SIGNAL-PORT">/VectorAutosarExplorerGeneratedObjects/ECU_INSTANCES/New_ECU/Connector_New_ECU_1c8171768535fd8f/SP_e9dbba2d6e3b4114a534d69dc7282b13_Rx</I-SIGNAL-PORT-REF>
                          </I-SIGNAL-PORT-REFS>
                          <I-SIGNAL-REF DEST="I-SIGNAL">/VectorAutosarExplorerGeneratedObjects/I_SIGNALS/New_Frame_NewPDU_NewSignal_2</I-SIGNAL-REF>
                        </I-SIGNAL-TRIGGERING>
                        <I-SIGNAL-TRIGGERING>
                          <SHORT-NAME>NewSignalTriggering_c692530680bd7c7e</SHORT-NAME>
                          <I-SIGNAL-PORT-REFS>
                            <I-SIGNAL-PORT-REF DEST="I-SIGNAL-PORT">/VectorAutosarExplorerGeneratedObjects/ECU_INSTANCES/New_ECU/Connector_New_ECU_1c8171768535fd8f/SP_19f4ade4ae0242e883eea84e083b9108_Rx</I-SIGNAL-PORT-REF>
                          </I-SIGNAL-PORT-REFS>
                          <I-SIGNAL-REF DEST="I-SIGNAL">/VectorAutosarExplorerGeneratedObjects/I_SIGNALS/New_Frame_NewPDU_NewSignal_3</I-SIGNAL-REF>
                        </I-SIGNAL-TRIGGERING>
                        <I-SIGNAL-TRIGGERING>
                          <SHORT-NAME>NewSignalTriggering_69ccfba3cb53e752</SHORT-NAME>
                          <I-SIGNAL-PORT-REFS>
                            <I-SIGNAL-PORT-REF DEST="I-SIGNAL-PORT">/VectorAutosarExplorerGeneratedObjects/ECU_INSTANCES/New_ECU/Connector_New_ECU_1c8171768535fd8f/SP_ce5f248122604afdb1440f8a7ac1b295_Rx</I-SIGNAL-PORT-REF>
                          </I-SIGNAL-PORT-REFS>
                          <I-SIGNAL-REF DEST="I-SIGNAL">/VectorAutosarExplorerGeneratedObjects/I_SIGNALS/New_Frame_NewPDU_NewSignal_4</I-SIGNAL-REF>
                        </I-SIGNAL-TRIGGERING>
                        <I-SIGNAL-TRIGGERING>
                          <SHORT-NAME>NewSignalTriggering_b195eb23b91e65a9</SHORT-NAME>
                          <I-SIGNAL-PORT-REFS>
                            <I-SIGNAL-PORT-REF DEST="I-SIGNAL-PORT">/VectorAutosarExplorerGeneratedObjects/ECU_INSTANCES/New_ECU/Connector_New_ECU_1c8171768535fd8f/SP_8df6e862731f403c82dd1a92f9f19830_Rx</I-SIGNAL-PORT-REF>
                          </I-SIGNAL-PORT-REFS>
                          <I-SIGNAL-REF DEST="I-SIGNAL">/VectorAutosarExplorerGeneratedObjects/I_SIGNALS/New_Frame_NewPDU_NewSignal_5</I-SIGNAL-REF>
                        </I-SIGNAL-TRIGGERING>
                      </I-SIGNAL-TRIGGERINGS>
                      <PDU-TRIGGERINGS>
                        <PDU-TRIGGERING>
                          <SHORT-NAME>NewPduTriggering_a1c914924e7a9908</SHORT-NAME>
                          <I-PDU-PORT-REFS>
                            <I-PDU-PORT-REF DEST="I-PDU-PORT">/VectorAutosarExplorerGeneratedObjects/ECU_INSTANCES/New_ECU/Connector_New_ECU_1c8171768535fd8f/PP_d757904cffa04e799d91690b59226ace_Rx</I-PDU-PORT-REF>
                          </I-PDU-PORT-REFS>
                          <I-PDU-REF DEST="I-SIGNAL-I-PDU">/VectorAutosarExplorerGeneratedObjects/PDUS/New_Frame_NewPDU</I-PDU-REF>
                          <I-SIGNAL-TRIGGERINGS>
                            <I-SIGNAL-TRIGGERING-REF-CONDITIONAL>
                              <I-SIGNAL-TRIGGERING-REF DEST="I-SIGNAL-TRIGGERING">/VectorAutosarExplorerGeneratedObjects/New_CanCluster/New_CanCluster/CANChannel/NewSignalTriggering_b62ee9d4c50e6c9a</I-SIGNAL-TRIGGERING-REF>
                            </I-SIGNAL-TRIGGERING-REF-CONDITIONAL>
                            <I-SIGNAL-TRIGGERING-REF-CONDITIONAL>
                              <I-SIGNAL-TRIGGERING-REF DEST="I-SIGNAL-TRIGGERING">/VectorAutosarExplorerGeneratedObjects/New_CanCluster/New_CanCluster/CANChannel/NewSignalTriggering_7cfa964bc203bb08</I-SIGNAL-TRIGGERING-REF>
                            </I-SIGNAL-TRIGGERING-REF-CONDITIONAL>
                            <I-SIGNAL-TRIGGERING-REF-CONDITIONAL>
                              <I-SIGNAL-TRIGGERING-REF DEST="I-SIGNAL-TRIGGERING">/VectorAutosarExplorerGeneratedObjects/New_CanCluster/New_CanCluster/CANChannel/NewSignalTriggering_88b663171f0a13df</I-SIGNAL-TRIGGERING-REF>
                            </I-SIGNAL-TRIGGERING-REF-CONDITIONAL>
                            <I-SIGNAL-TRIGGERING-REF-CONDITIONAL>
                              <I-SIGNAL-TRIGGERING-REF DEST="I-SIGNAL-TRIGGERING">/VectorAutosarExplorerGeneratedObjects/New_CanCluster/New_CanCluster/CANChannel/NewSignalTriggering_c692530680bd7c7e</I-SIGNAL-TRIGGERING-REF>
                            </I-SIGNAL-TRIGGERING-REF-CONDITIONAL>
                            <I-SIGNAL-TRIGGERING-REF-CONDITIONAL>
                              <I-SIGNAL-TRIGGERING-REF DEST="I-SIGNAL-TRIGGERING">/VectorAutosarExplorerGeneratedObjects/New_CanCluster/New_CanCluster/CANChannel/NewSignalTriggering_69ccfba3cb53e752</I-SIGNAL-TRIGGERING-REF>
                            </I-SIGNAL-TRIGGERING-REF-CONDITIONAL>
                            <I-SIGNAL-TRIGGERING-REF-CONDITIONAL>
                              <I-SIGNAL-TRIGGERING-REF DEST="I-SIGNAL-TRIGGERING">/VectorAutosarExplorerGeneratedObjects/New_CanCluster/New_CanCluster/CANChannel/NewSignalTriggering_b195eb23b91e65a9</I-SIGNAL-TRIGGERING-REF>
                            </I-SIGNAL-TRIGGERING-REF-CONDITIONAL>
                          </I-SIGNAL-TRIGGERINGS>
                        </PDU-TRIGGERING>
                      </PDU-TRIGGERINGS>
                    </CAN-PHYSICAL-CHANNEL>
                  </PHYSICAL-CHANNELS>
                  <PROTOCOL-NAME>CAN</PROTOCOL-NAME>
                  <SPEED>250000</SPEED>
                </CAN-CLUSTER-CONDITIONAL>
              </CAN-CLUSTER-VARIANTS>
            </CAN-CLUSTER>
          </ELEMENTS>
        </AR-PACKAGE>
        <AR-PACKAGE>
          <SHORT-NAME>ECU_INSTANCES</SHORT-NAME>
          <ELEMENTS>
            <ECU-INSTANCE>
              <SHORT-NAME>New_ECU</SHORT-NAME>
              <ASSOCIATED-COM-I-PDU-GROUP-REFS>
                <ASSOCIATED-COM-I-PDU-GROUP-REF DEST="I-SIGNAL-I-PDU-GROUP">/VectorAutosarExplorerGeneratedObjects/PDU_GROUP/PduGroup_3d1efacc48324fb6a458184295d222c5_Rx</ASSOCIATED-COM-I-PDU-GROUP-REF>
              </ASSOCIATED-COM-I-PDU-GROUP-REFS>
              <COMM-CONTROLLERS>
                <CAN-COMMUNICATION-CONTROLLER>
                  <SHORT-NAME>Controller_New_ECU_26c2009afd6ed0f5</SHORT-NAME>
                  <CAN-COMMUNICATION-CONTROLLER-VARIANTS>
                    <CAN-COMMUNICATION-CONTROLLER-CONDITIONAL>
                      <CAN-CONTROLLER-ATTRIBUTES>
                        <CAN-CONTROLLER-CONFIGURATION>
                          <CAN-CONTROLLER-FD-ATTRIBUTES />
                        </CAN-CONTROLLER-CONFIGURATION>
                      </CAN-CONTROLLER-ATTRIBUTES>
                    </CAN-COMMUNICATION-CONTROLLER-CONDITIONAL>
                  </CAN-COMMUNICATION-CONTROLLER-VARIANTS>
                </CAN-COMMUNICATION-CONTROLLER>
              </COMM-CONTROLLERS>
              <CONNECTORS>
                <CAN-COMMUNICATION-CONNECTOR>
                  <SHORT-NAME>Connector_New_ECU_1c8171768535fd8f</SHORT-NAME>
                  <COMM-CONTROLLER-REF DEST="CAN-COMMUNICATION-CONTROLLER">/VectorAutosarExplorerGeneratedObjects/ECU_INSTANCES/New_ECU/Controller_New_ECU_26c2009afd6ed0f5</COMM-CONTROLLER-REF>
                  <ECU-COMM-PORT-INSTANCES>
                    <FRAME-PORT>
                      <SHORT-NAME>framePort_fee56426c0f5d2a3</SHORT-NAME>
                      <COMMUNICATION-DIRECTION>IN</COMMUNICATION-DIRECTION>
                    </FRAME-PORT>
                    <I-PDU-PORT>
                      <SHORT-NAME>PP_d757904cffa04e799d91690b59226ace_Rx</SHORT-NAME>
                      <COMMUNICATION-DIRECTION>IN</COMMUNICATION-DIRECTION>
                    </I-PDU-PORT>
                    <I-SIGNAL-PORT>
                      <SHORT-NAME>SP_cef6f2b8d73546568794359c9a2a4f5c_Rx</SHORT-NAME>
                      <COMMUNICATION-DIRECTION>IN</COMMUNICATION-DIRECTION>
                    </I-SIGNAL-PORT>
                    <I-SIGNAL-PORT>
                      <SHORT-NAME>SP_7a4fcad1cfba48098e94f0573468de09_Rx</SHORT-NAME>
                      <COMMUNICATION-DIRECTION>IN</COMMUNICATION-DIRECTION>
                    </I-SIGNAL-PORT>
                    <I-SIGNAL-PORT>
                      <SHORT-NAME>SP_e9dbba2d6e3b4114a534d69dc7282b13_Rx</SHORT-NAME>
                      <COMMUNICATION-DIRECTION>IN</COMMUNICATION-DIRECTION>
                    </I-SIGNAL-PORT>
                    <I-SIGNAL-PORT>
                      <SHORT-NAME>SP_19f4ade4ae0242e883eea84e083b9108_Rx</SHORT-NAME>
                      <COMMUNICATION-DIRECTION>IN</COMMUNICATION-DIRECTION>
                    </I-SIGNAL-PORT>
                    <I-SIGNAL-PORT>
                      <SHORT-NAME>SP_ce5f248122604afdb1440f8a7ac1b295_Rx</SHORT-NAME>
                      <COMMUNICATION-DIRECTION>IN</COMMUNICATION-DIRECTION>
                    </I-SIGNAL-PORT>
                    <I-SIGNAL-PORT>
                      <SHORT-NAME>SP_8df6e862731f403c82dd1a92f9f19830_Rx</SHORT-NAME>
                      <COMMUNICATION-DIRECTION>IN</COMMUNICATION-DIRECTION>
                    </I-SIGNAL-PORT>
                  </ECU-COMM-PORT-INSTANCES>
                </CAN-COMMUNICATION-CONNECTOR>
              </CONNECTORS>
              <DIAGNOSTIC-ADDRESS>0</DIAGNOSTIC-ADDRESS>
            </ECU-INSTANCE>
          </ELEMENTS>
        </AR-PACKAGE>
        <AR-PACKAGE>
          <SHORT-NAME>FRAME</SHORT-NAME>
          <ELEMENTS>
            <CAN-FRAME>
              <SHORT-NAME>New_Frame</SHORT-NAME>
              <FRAME-LENGTH>8</FRAME-LENGTH>
              <PDU-TO-FRAME-MAPPINGS>
                <PDU-TO-FRAME-MAPPING>
                  <SHORT-NAME>PduToFrameMapping_a48ffcd274baecd2</SHORT-NAME>
                  <PACKING-BYTE-ORDER>MOST-SIGNIFICANT-BYTE-LAST</PACKING-BYTE-ORDER>
                  <PDU-REF DEST="I-SIGNAL-I-PDU">/VectorAutosarExplorerGeneratedObjects/PDUS/New_Frame_NewPDU</PDU-REF>
                  <START-POSITION>0</START-POSITION>
                </PDU-TO-FRAME-MAPPING>
              </PDU-TO-FRAME-MAPPINGS>
            </CAN-FRAME>
          </ELEMENTS>
        </AR-PACKAGE>
        <AR-PACKAGE>
          <SHORT-NAME>PDUS</SHORT-NAME>
          <ELEMENTS>
            <I-SIGNAL-I-PDU>
              <SHORT-NAME>New_Frame_NewPDU</SHORT-NAME>
              <LENGTH>8</LENGTH>
              <I-SIGNAL-TO-PDU-MAPPINGS>
                <I-SIGNAL-TO-I-PDU-MAPPING>
                  <SHORT-NAME>SignalPduMapping_d12425b0cf87e337</SHORT-NAME>
                  <I-SIGNAL-REF DEST="I-SIGNAL">/VectorAutosarExplorerGeneratedObjects/I_SIGNALS/New_Frame_NewPDU_NewSignal</I-SIGNAL-REF>
                  <PACKING-BYTE-ORDER>MOST-SIGNIFICANT-BYTE-FIRST</PACKING-BYTE-ORDER>
                  <START-POSITION>0</START-POSITION>
                  <TRANSFER-PROPERTY>PENDING</TRANSFER-PROPERTY>
                </I-SIGNAL-TO-I-PDU-MAPPING>
                <I-SIGNAL-TO-I-PDU-MAPPING>
                  <SHORT-NAME>SignalPduMapping_cbe48419bd90be0c</SHORT-NAME>
                  <I-SIGNAL-REF DEST="I-SIGNAL">/VectorAutosarExplorerGeneratedObjects/I_SIGNALS/New_Frame_NewPDU_NewSignal_1</I-SIGNAL-REF>
                  <PACKING-BYTE-ORDER>MOST-SIGNIFICANT-BYTE-FIRST</PACKING-BYTE-ORDER>
                  <START-POSITION>1</START-POSITION>
                  <TRANSFER-PROPERTY>PENDING</TRANSFER-PROPERTY>
                </I-SIGNAL-TO-I-PDU-MAPPING>
                <I-SIGNAL-TO-I-PDU-MAPPING>
                  <SHORT-NAME>SignalPduMapping_37669ae8aab1d57d</SHORT-NAME>
                  <I-SIGNAL-REF DEST="I-SIGNAL">/VectorAutosarExplorerGeneratedObjects/I_SIGNALS/New_Frame_NewPDU_NewSignal_2</I-SIGNAL-REF>
                  <PACKING-BYTE-ORDER>MOST-SIGNIFICANT-BYTE-LAST</PACKING-BYTE-ORDER>
                  <START-POSITION>16</START-POSITION>
                </I-SIGNAL-TO-I-PDU-MAPPING>
                <I-SIGNAL-TO-I-PDU-MAPPING>
                  <SHORT-NAME>SignalPduMapping_7de6d326c0e389eb</SHORT-NAME>
                  <I-SIGNAL-REF DEST="I-SIGNAL">/VectorAutosarExplorerGeneratedObjects/I_SIGNALS/New_Frame_NewPDU_NewSignal_3</I-SIGNAL-REF>
                  <PACKING-BYTE-ORDER>MOST-SIGNIFICANT-BYTE-FIRST</PACKING-BYTE-ORDER>
                  <START-POSITION>32</START-POSITION>
                  <TRANSFER-PROPERTY>PENDING</TRANSFER-PROPERTY>
                </I-SIGNAL-TO-I-PDU-MAPPING>
                <I-SIGNAL-TO-I-PDU-MAPPING>
                  <SHORT-NAME>SignalPduMapping_32ed7fa6bba2303c</SHORT-NAME>
                  <I-SIGNAL-REF DEST="I-SIGNAL">/VectorAutosarExplorerGeneratedObjects/I_SIGNALS/New_Frame_NewPDU_NewSignal_4</I-SIGNAL-REF>
                  <PACKING-BYTE-ORDER>MOST-SIGNIFICANT-BYTE-FIRST</PACKING-BYTE-ORDER>
                  <START-POSITION>56</START-POSITION>
                  <TRANSFER-PROPERTY>PENDING</TRANSFER-PROPERTY>
                </I-SIGNAL-TO-I-PDU-MAPPING>
                <I-SIGNAL-TO-I-PDU-MAPPING>
                  <SHORT-NAME>SignalPduMapping_c205706ea96dfa5c</SHORT-NAME>
                  <I-SIGNAL-REF DEST="I-SIGNAL">/VectorAutosarExplorerGeneratedObjects/I_SIGNALS/New_Frame_NewPDU_NewSignal_5</I-SIGNAL-REF>
                  <PACKING-BYTE-ORDER>MOST-SIGNIFICANT-BYTE-FIRST</PACKING-BYTE-ORDER>
                  <START-POSITION>2</START-POSITION>
                  <TRANSFER-PROPERTY>PENDING</TRANSFER-PROPERTY>
                </I-SIGNAL-TO-I-PDU-MAPPING>
              </I-SIGNAL-TO-PDU-MAPPINGS>
            </I-SIGNAL-I-PDU>
          </ELEMENTS>
        </AR-PACKAGE>
        <AR-PACKAGE>
          <SHORT-NAME>I_SIGNALS</SHORT-NAME>
          <ELEMENTS>
            <I-SIGNAL>
              <SHORT-NAME>New_Frame_NewPDU_NewSignal</SHORT-NAME>
              <DATA-TYPE-POLICY>OVERRIDE</DATA-TYPE-POLICY>
              <LENGTH>8</LENGTH>
              <NETWORK-REPRESENTATION-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <BASE-TYPE-REF DEST="SW-BASE-TYPE">/VectorAutosarExplorerGeneratedObjects/BASE_TYPES/New_BaseType</BASE-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </NETWORK-REPRESENTATION-PROPS>
              <SYSTEM-SIGNAL-REF DEST="SYSTEM-SIGNAL">/VectorAutosarExplorerGeneratedObjects/SYSTEM_SIGNALS/New_Frame_NewPDU_NewSignal_d3ad4ded3d1f5a52</SYSTEM-SIGNAL-REF>
            </I-SIGNAL>
            <I-SIGNAL>
              <SHORT-NAME>New_Frame_NewPDU_NewSignal_1</SHORT-NAME>
              <DATA-TYPE-POLICY>OVERRIDE</DATA-TYPE-POLICY>
              <LENGTH>4</LENGTH>
              <NETWORK-REPRESENTATION-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <BASE-TYPE-REF DEST="SW-BASE-TYPE">/VectorAutosarExplorerGeneratedObjects/BASE_TYPES/New_BaseType</BASE-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </NETWORK-REPRESENTATION-PROPS>
              <SYSTEM-SIGNAL-REF DEST="SYSTEM-SIGNAL">/VectorAutosarExplorerGeneratedObjects/SYSTEM_SIGNALS/New_Frame_NewPDU_NewSignal_1_97dba33bf5006571</SYSTEM-SIGNAL-REF>
            </I-SIGNAL>
            <I-SIGNAL>
              <SHORT-NAME>New_Frame_NewPDU_NewSignal_2</SHORT-NAME>
              <DATA-TYPE-POLICY>OVERRIDE</DATA-TYPE-POLICY>
              <LENGTH>16</LENGTH>
              <NETWORK-REPRESENTATION-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <BASE-TYPE-REF DEST="SW-BASE-TYPE">/VectorAutosarExplorerGeneratedObjects/BASE_TYPES/New_BaseType</BASE-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </NETWORK-REPRESENTATION-PROPS>
              <SYSTEM-SIGNAL-REF DEST="SYSTEM-SIGNAL">/VectorAutosarExplorerGeneratedObjects/SYSTEM_SIGNALS/New_Frame_NewPDU_NewSignal_2_c4d5cde179c23521</SYSTEM-SIGNAL-REF>
            </I-SIGNAL>
            <I-SIGNAL>
              <SHORT-NAME>New_Frame_NewPDU_NewSignal_3</SHORT-NAME>
              <DATA-TYPE-POLICY>OVERRIDE</DATA-TYPE-POLICY>
              <LENGTH>12</LENGTH>
              <NETWORK-REPRESENTATION-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <BASE-TYPE-REF DEST="SW-BASE-TYPE">/VectorAutosarExplorerGeneratedObjects/BASE_TYPES/New_BaseType</BASE-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </NETWORK-REPRESENTATION-PROPS>
              <SYSTEM-SIGNAL-REF DEST="SYSTEM-SIGNAL">/VectorAutosarExplorerGeneratedObjects/SYSTEM_SIGNALS/New_Frame_NewPDU_NewSignal_3_545262b7a6cd1260</SYSTEM-SIGNAL-REF>
            </I-SIGNAL>
            <I-SIGNAL>
              <SHORT-NAME>New_Frame_NewPDU_NewSignal_4</SHORT-NAME>
              <DATA-TYPE-POLICY>OVERRIDE</DATA-TYPE-POLICY>
              <LENGTH>2</LENGTH>
              <NETWORK-REPRESENTATION-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <BASE-TYPE-REF DEST="SW-BASE-TYPE">/VectorAutosarExplorerGeneratedObjects/BASE_TYPES/New_BaseType</BASE-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </NETWORK-REPRESENTATION-PROPS>
              <SYSTEM-SIGNAL-REF DEST="SYSTEM-SIGNAL">/VectorAutosarExplorerGeneratedObjects/SYSTEM_SIGNALS/New_Frame_NewPDU_NewSignal_4_7c569e4203cfd6a3</SYSTEM-SIGNAL-REF>
            </I-SIGNAL>
            <I-SIGNAL>
              <SHORT-NAME>New_Frame_NewPDU_NewSignal_5</SHORT-NAME>
              <DATA-TYPE-POLICY>OVERRIDE</DATA-TYPE-POLICY>
              <LENGTH>1</LENGTH>
              <NETWORK-REPRESENTATION-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <BASE-TYPE-REF DEST="SW-BASE-TYPE">/VectorAutosarExplorerGeneratedObjects/BASE_TYPES/New_BaseType</BASE-TYPE-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </NETWORK-REPRESENTATION-PROPS>
              <SYSTEM-SIGNAL-REF DEST="SYSTEM-SIGNAL">/VectorAutosarExplorerGeneratedObjects/SYSTEM_SIGNALS/New_Frame_NewPDU_NewSignal_5_0fa1a7a828ef955d</SYSTEM-SIGNAL-REF>
            </I-SIGNAL>
          </ELEMENTS>
        </AR-PACKAGE>
        <AR-PACKAGE>
          <SHORT-NAME>BASE_TYPES</SHORT-NAME>
          <ELEMENTS>
            <SW-BASE-TYPE>
              <SHORT-NAME>New_BaseType</SHORT-NAME>
              <BASE-TYPE-SIZE>16</BASE-TYPE-SIZE>
              <BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
            </SW-BASE-TYPE>
          </ELEMENTS>
        </AR-PACKAGE>
        <AR-PACKAGE>
          <SHORT-NAME>COMPUMETHODS</SHORT-NAME>
          <ELEMENTS>
            <COMPU-METHOD>
              <SHORT-NAME>New_Frame_NewPDU_NewSignal_Encoding</SHORT-NAME>
              <CATEGORY>SCALE_LINEAR_AND_TEXTTABLE</CATEGORY>
              <COMPU-INTERNAL-TO-PHYS>
                <COMPU-SCALES>
                  <COMPU-SCALE>
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                    <COMPU-RATIONAL-COEFFS>
                      <COMPU-NUMERATOR>
                        <V>0</V>
                        <V>1</V>
                      </COMPU-NUMERATOR>
                      <COMPU-DENOMINATOR>
                        <V>0</V>
                        <V>1</V>
                      </COMPU-DENOMINATOR>
                    </COMPU-RATIONAL-COEFFS>
                  </COMPU-SCALE>
                </COMPU-SCALES>
              </COMPU-INTERNAL-TO-PHYS>
            </COMPU-METHOD>
            <COMPU-METHOD>
              <SHORT-NAME>New_Frame_NewPDU_NewSignal_1_Encoding</SHORT-NAME>
              <CATEGORY>SCALE_LINEAR_AND_TEXTTABLE</CATEGORY>
              <COMPU-INTERNAL-TO-PHYS>
                <COMPU-SCALES>
                  <COMPU-SCALE>
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>16</UPPER-LIMIT>
                    <COMPU-RATIONAL-COEFFS>
                      <COMPU-NUMERATOR>
                        <V>0</V>
                        <V>1</V>
                      </COMPU-NUMERATOR>
                      <COMPU-DENOMINATOR>
                        <V>0</V>
                        <V>1</V>
                      </COMPU-DENOMINATOR>
                    </COMPU-RATIONAL-COEFFS>
                  </COMPU-SCALE>
                </COMPU-SCALES>
              </COMPU-INTERNAL-TO-PHYS>
            </COMPU-METHOD>
            <COMPU-METHOD>
              <SHORT-NAME>New_Frame_NewPDU_NewSignal_2_Encoding</SHORT-NAME>
              <CATEGORY>SCALE_LINEAR_AND_TEXTTABLE</CATEGORY>
              <COMPU-INTERNAL-TO-PHYS>
                <COMPU-SCALES>
                  <COMPU-SCALE>
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>65534</UPPER-LIMIT>
                    <COMPU-RATIONAL-COEFFS>
                      <COMPU-NUMERATOR>
                        <V>0</V>
                        <V>0.125</V>
                      </COMPU-NUMERATOR>
                      <COMPU-DENOMINATOR>
                        <V>0</V>
                        <V>1</V>
                      </COMPU-DENOMINATOR>
                    </COMPU-RATIONAL-COEFFS>
                  </COMPU-SCALE>
                </COMPU-SCALES>
              </COMPU-INTERNAL-TO-PHYS>
            </COMPU-METHOD>
            <COMPU-METHOD>
              <SHORT-NAME>New_Frame_NewPDU_NewSignal_3_Encoding</SHORT-NAME>
              <CATEGORY>SCALE_LINEAR_AND_TEXTTABLE</CATEGORY>
              <COMPU-INTERNAL-TO-PHYS>
                <COMPU-SCALES>
                  <COMPU-SCALE>
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>4094</UPPER-LIMIT>
                    <COMPU-RATIONAL-COEFFS>
                      <COMPU-NUMERATOR>
                        <V>-400</V>
                        <V>0.5</V>
                      </COMPU-NUMERATOR>
                      <COMPU-DENOMINATOR>
                        <V>0</V>
                        <V>1</V>
                      </COMPU-DENOMINATOR>
                    </COMPU-RATIONAL-COEFFS>
                  </COMPU-SCALE>
                </COMPU-SCALES>
              </COMPU-INTERNAL-TO-PHYS>
            </COMPU-METHOD>
            <COMPU-METHOD>
              <SHORT-NAME>New_Frame_NewPDU_NewSignal_4_Encoding</SHORT-NAME>
              <CATEGORY>SCALE_LINEAR_AND_TEXTTABLE</CATEGORY>
              <COMPU-INTERNAL-TO-PHYS>
                <COMPU-SCALES>
                  <COMPU-SCALE>
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>0</UPPER-LIMIT>
                    <COMPU-RATIONAL-COEFFS>
                      <COMPU-NUMERATOR>
                        <V>0</V>
                        <V>1</V>
                      </COMPU-NUMERATOR>
                      <COMPU-DENOMINATOR>
                        <V>0</V>
                        <V>1</V>
                      </COMPU-DENOMINATOR>
                    </COMPU-RATIONAL-COEFFS>
                  </COMPU-SCALE>
                </COMPU-SCALES>
              </COMPU-INTERNAL-TO-PHYS>
            </COMPU-METHOD>
            <COMPU-METHOD>
              <SHORT-NAME>New_Frame_NewPDU_NewSignal_5_Encoding</SHORT-NAME>
              <CATEGORY>SCALE_LINEAR_AND_TEXTTABLE</CATEGORY>
              <COMPU-INTERNAL-TO-PHYS>
                <COMPU-SCALES>
                  <COMPU-SCALE>
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>20000</UPPER-LIMIT>
                    <COMPU-RATIONAL-COEFFS>
                      <COMPU-NUMERATOR>
                        <V>-20000</V>
                        <V>2</V>
                      </COMPU-NUMERATOR>
                      <COMPU-DENOMINATOR>
                        <V>0</V>
                        <V>1</V>
                      </COMPU-DENOMINATOR>
                    </COMPU-RATIONAL-COEFFS>
                  </COMPU-SCALE>
                </COMPU-SCALES>
              </COMPU-INTERNAL-TO-PHYS>
            </COMPU-METHOD>
          </ELEMENTS>
        </AR-PACKAGE>
        <AR-PACKAGE>
          <SHORT-NAME>SYSTEM_SIGNALS</SHORT-NAME>
          <ELEMENTS>
            <SYSTEM-SIGNAL>
              <SHORT-NAME>New_Frame_NewPDU_NewSignal_d3ad4ded3d1f5a52</SHORT-NAME>
              <PHYSICAL-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <COMPU-METHOD-REF DEST="COMPU-METHOD">/VectorAutosarExplorerGeneratedObjects/COMPUMETHODS/New_Frame_NewPDU_NewSignal_Encoding</COMPU-METHOD-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </PHYSICAL-PROPS>
            </SYSTEM-SIGNAL>
            <SYSTEM-SIGNAL>
              <SHORT-NAME>New_Frame_NewPDU_NewSignal_1_97dba33bf5006571</SHORT-NAME>
              <PHYSICAL-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <COMPU-METHOD-REF DEST="COMPU-METHOD">/VectorAutosarExplorerGeneratedObjects/COMPUMETHODS/New_Frame_NewPDU_NewSignal_1_Encoding</COMPU-METHOD-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </PHYSICAL-PROPS>
            </SYSTEM-SIGNAL>
            <SYSTEM-SIGNAL>
              <SHORT-NAME>New_Frame_NewPDU_NewSignal_2_c4d5cde179c23521</SHORT-NAME>
              <PHYSICAL-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <COMPU-METHOD-REF DEST="COMPU-METHOD">/VectorAutosarExplorerGeneratedObjects/COMPUMETHODS/New_Frame_NewPDU_NewSignal_2_Encoding</COMPU-METHOD-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </PHYSICAL-PROPS>
            </SYSTEM-SIGNAL>
            <SYSTEM-SIGNAL>
              <SHORT-NAME>New_Frame_NewPDU_NewSignal_3_545262b7a6cd1260</SHORT-NAME>
              <PHYSICAL-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <COMPU-METHOD-REF DEST="COMPU-METHOD">/VectorAutosarExplorerGeneratedObjects/COMPUMETHODS/New_Frame_NewPDU_NewSignal_3_Encoding</COMPU-METHOD-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </PHYSICAL-PROPS>
            </SYSTEM-SIGNAL>
            <SYSTEM-SIGNAL>
              <SHORT-NAME>New_Frame_NewPDU_NewSignal_4_7c569e4203cfd6a3</SHORT-NAME>
              <PHYSICAL-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <COMPU-METHOD-REF DEST="COMPU-METHOD">/VectorAutosarExplorerGeneratedObjects/COMPUMETHODS/New_Frame_NewPDU_NewSignal_4_Encoding</COMPU-METHOD-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </PHYSICAL-PROPS>
            </SYSTEM-SIGNAL>
            <SYSTEM-SIGNAL>
              <SHORT-NAME>New_Frame_NewPDU_NewSignal_5_0fa1a7a828ef955d</SHORT-NAME>
              <PHYSICAL-PROPS>
                <SW-DATA-DEF-PROPS-VARIANTS>
                  <SW-DATA-DEF-PROPS-CONDITIONAL>
                    <COMPU-METHOD-REF DEST="COMPU-METHOD">/VectorAutosarExplorerGeneratedObjects/COMPUMETHODS/New_Frame_NewPDU_NewSignal_5_Encoding</COMPU-METHOD-REF>
                  </SW-DATA-DEF-PROPS-CONDITIONAL>
                </SW-DATA-DEF-PROPS-VARIANTS>
              </PHYSICAL-PROPS>
            </SYSTEM-SIGNAL>
          </ELEMENTS>
        </AR-PACKAGE>
        <AR-PACKAGE>
          <SHORT-NAME>PDU_GROUP</SHORT-NAME>
          <ELEMENTS>
            <I-SIGNAL-I-PDU-GROUP>
              <SHORT-NAME>PduGroup_3d1efacc48324fb6a458184295d222c5_Rx</SHORT-NAME>
              <COMMUNICATION-DIRECTION>IN</COMMUNICATION-DIRECTION>
            </I-SIGNAL-I-PDU-GROUP>
          </ELEMENTS>
        </AR-PACKAGE>
      </AR-PACKAGES>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>