# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'UITestTools.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_TestTools(object):
    def setupUi(self, TestTools):
        TestTools.setObjectName("TestTools")
        TestTools.resize(1200, 800)
        TestTools.setMinimumSize(QtCore.QSize(1200, 800))
        self.gridLayout_2 = QtWidgets.QGridLayout(TestTools)
        self.gridLayout_2.setContentsMargins(20, 20, 20, 20)
        self.gridLayout_2.setSpacing(6)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.frame_2 = QtWidgets.QFrame(TestTools)
        self.frame_2.setStyleSheet("border-radius:5")
        self.frame_2.setObjectName("frame_2")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.frame_2)
        self.verticalLayout.setContentsMargins(15, 0, 15, 15)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.mate_push_img_btn = QtWidgets.QPushButton(self.frame_2)
        self.mate_push_img_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.mate_push_img_btn.setObjectName("mate_push_img_btn")
        self.horizontalLayout.addWidget(self.mate_push_img_btn)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.endurance_btn = QtWidgets.QPushButton(self.frame_2)
        self.endurance_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.endurance_btn.setObjectName("endurance_btn")
        self.horizontalLayout.addWidget(self.endurance_btn)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem2)
        self.picture_tool_btn = QtWidgets.QPushButton(self.frame_2)
        self.picture_tool_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.picture_tool_btn.setObjectName("picture_tool_btn")
        self.horizontalLayout.addWidget(self.picture_tool_btn)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem3)
        self.anti_pinch_btn = QtWidgets.QPushButton(self.frame_2)
        self.anti_pinch_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.anti_pinch_btn.setObjectName("anti_pinch_btn")
        self.horizontalLayout.addWidget(self.anti_pinch_btn)
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem4)
        self.io_control_btn = QtWidgets.QPushButton(self.frame_2)
        self.io_control_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.io_control_btn.setObjectName("io_control_btn")
        self.horizontalLayout.addWidget(self.io_control_btn)
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem5)
        self.position_control_btn = QtWidgets.QPushButton(self.frame_2)
        self.position_control_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.position_control_btn.setObjectName("position_control_btn")
        self.horizontalLayout.addWidget(self.position_control_btn)
        spacerItem6 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem6)
        self.horizontalLayout.setStretch(0, 1)
        self.horizontalLayout.setStretch(1, 1)
        self.horizontalLayout.setStretch(2, 1)
        self.horizontalLayout.setStretch(3, 1)
        self.horizontalLayout.setStretch(4, 1)
        self.horizontalLayout.setStretch(5, 1)
        self.horizontalLayout.setStretch(6, 1)
        self.horizontalLayout.setStretch(7, 1)
        self.horizontalLayout.setStretch(8, 1)
        self.horizontalLayout.setStretch(9, 1)
        self.horizontalLayout.setStretch(10, 1)
        self.horizontalLayout.setStretch(11, 1)
        self.horizontalLayout.setStretch(12, 1)
        self.verticalLayout.addLayout(self.horizontalLayout)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        spacerItem7 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem7)
        self.dbc_send_btn = QtWidgets.QPushButton(self.frame_2)
        self.dbc_send_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.dbc_send_btn.setObjectName("dbc_send_btn")
        self.horizontalLayout_2.addWidget(self.dbc_send_btn)
        spacerItem8 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem8)
        self.dbc_recv_btn = QtWidgets.QPushButton(self.frame_2)
        self.dbc_recv_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.dbc_recv_btn.setObjectName("dbc_recv_btn")
        self.horizontalLayout_2.addWidget(self.dbc_recv_btn)
        spacerItem9 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem9)
        self.auto_press_btn = QtWidgets.QPushButton(self.frame_2)
        self.auto_press_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.auto_press_btn.setObjectName("auto_press_btn")
        self.horizontalLayout_2.addWidget(self.auto_press_btn)
        spacerItem10 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem10)
        self.tri_color_light_btn = QtWidgets.QPushButton(self.frame_2)
        self.tri_color_light_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.tri_color_light_btn.setObjectName("tri_color_light_btn")
        self.horizontalLayout_2.addWidget(self.tri_color_light_btn)
        spacerItem11 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem11)
        self.tommens_power_btn = QtWidgets.QPushButton(self.frame_2)
        self.tommens_power_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.tommens_power_btn.setObjectName("tommens_power_btn")
        self.horizontalLayout_2.addWidget(self.tommens_power_btn)
        spacerItem12 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem12)
        self.it_m3200_power_btn = QtWidgets.QPushButton(self.frame_2)
        self.it_m3200_power_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.it_m3200_power_btn.setObjectName("it_m3200_power_btn")
        self.horizontalLayout_2.addWidget(self.it_m3200_power_btn)
        spacerItem13 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem13)
        self.horizontalLayout_2.setStretch(0, 1)
        self.horizontalLayout_2.setStretch(1, 1)
        self.horizontalLayout_2.setStretch(2, 1)
        self.horizontalLayout_2.setStretch(3, 1)
        self.horizontalLayout_2.setStretch(4, 1)
        self.horizontalLayout_2.setStretch(5, 1)
        self.horizontalLayout_2.setStretch(6, 1)
        self.horizontalLayout_2.setStretch(7, 1)
        self.horizontalLayout_2.setStretch(8, 1)
        self.horizontalLayout_2.setStretch(9, 1)
        self.horizontalLayout_2.setStretch(10, 1)
        self.horizontalLayout_2.setStretch(11, 1)
        self.horizontalLayout_2.setStretch(12, 1)
        self.verticalLayout.addLayout(self.horizontalLayout_2)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        spacerItem14 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem14)
        self.rotate_test_btn = QtWidgets.QPushButton(self.frame_2)
        self.rotate_test_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.rotate_test_btn.setObjectName("rotate_test_btn")
        self.horizontalLayout_3.addWidget(self.rotate_test_btn)
        spacerItem15 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem15)
        self.optical_test_btn = QtWidgets.QPushButton(self.frame_2)
        self.optical_test_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.optical_test_btn.setObjectName("optical_test_btn")
        self.horizontalLayout_3.addWidget(self.optical_test_btn)
        spacerItem16 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem16)
        self.light_sensor_test_btn = QtWidgets.QPushButton(self.frame_2)
        self.light_sensor_test_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.light_sensor_test_btn.setObjectName("light_sensor_test_btn")
        self.horizontalLayout_3.addWidget(self.light_sensor_test_btn)
        spacerItem17 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem17)
        self.temp_rise_btn = QtWidgets.QPushButton(self.frame_2)
        self.temp_rise_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.temp_rise_btn.setObjectName("temp_rise_btn")
        self.horizontalLayout_3.addWidget(self.temp_rise_btn)
        spacerItem18 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem18)
        self.video_test_btn = QtWidgets.QPushButton(self.frame_2)
        self.video_test_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.video_test_btn.setObjectName("video_test_btn")
        self.horizontalLayout_3.addWidget(self.video_test_btn)
        spacerItem19 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem19)
        self.i2c_update_btn = QtWidgets.QPushButton(self.frame_2)
        self.i2c_update_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.i2c_update_btn.setObjectName("i2c_update_btn")
        self.horizontalLayout_3.addWidget(self.i2c_update_btn)
        spacerItem20 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem20)
        self.horizontalLayout_3.setStretch(0, 1)
        self.horizontalLayout_3.setStretch(1, 1)
        self.horizontalLayout_3.setStretch(2, 1)
        self.horizontalLayout_3.setStretch(3, 1)
        self.horizontalLayout_3.setStretch(4, 1)
        self.horizontalLayout_3.setStretch(5, 1)
        self.horizontalLayout_3.setStretch(6, 1)
        self.horizontalLayout_3.setStretch(7, 1)
        self.horizontalLayout_3.setStretch(8, 1)
        self.horizontalLayout_3.setStretch(9, 1)
        self.horizontalLayout_3.setStretch(10, 1)
        self.horizontalLayout_3.setStretch(11, 1)
        self.horizontalLayout_3.setStretch(12, 1)
        self.verticalLayout.addLayout(self.horizontalLayout_3)
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setContentsMargins(0, 0, -1, -1)
        self.horizontalLayout_4.setSpacing(6)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        spacerItem21 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem21)
        self.elevation_angle_btn = QtWidgets.QPushButton(self.frame_2)
        self.elevation_angle_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.elevation_angle_btn.setObjectName("elevation_angle_btn")
        self.horizontalLayout_4.addWidget(self.elevation_angle_btn)
        spacerItem22 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem22)
        self.vds_upgrade_btn = QtWidgets.QPushButton(self.frame_2)
        self.vds_upgrade_btn.setEnabled(True)
        self.vds_upgrade_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.vds_upgrade_btn.setObjectName("vds_upgrade_btn")
        self.horizontalLayout_4.addWidget(self.vds_upgrade_btn)
        spacerItem23 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem23)
        self.lin_debug_btn = QtWidgets.QPushButton(self.frame_2)
        self.lin_debug_btn.setEnabled(True)
        self.lin_debug_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.lin_debug_btn.setObjectName("lin_debug_btn")
        self.horizontalLayout_4.addWidget(self.lin_debug_btn)
        spacerItem24 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem24)
        self.write_sn_btn = QtWidgets.QPushButton(self.frame_2)
        self.write_sn_btn.setMinimumSize(QtCore.QSize(0, 60))
        self.write_sn_btn.setObjectName("write_sn_btn")
        self.horizontalLayout_4.addWidget(self.write_sn_btn)
        spacerItem25 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem25)
        self.pushButton_2 = QtWidgets.QPushButton(self.frame_2)
        self.pushButton_2.setMinimumSize(QtCore.QSize(0, 60))
        self.pushButton_2.setObjectName("pushButton_2")
        self.horizontalLayout_4.addWidget(self.pushButton_2)
        spacerItem26 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem26)
        self.pushButton_3 = QtWidgets.QPushButton(self.frame_2)
        self.pushButton_3.setMinimumSize(QtCore.QSize(0, 60))
        self.pushButton_3.setObjectName("pushButton_3")
        self.horizontalLayout_4.addWidget(self.pushButton_3)
        spacerItem27 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem27)
        self.horizontalLayout_4.setStretch(0, 1)
        self.horizontalLayout_4.setStretch(1, 1)
        self.horizontalLayout_4.setStretch(2, 1)
        self.horizontalLayout_4.setStretch(3, 1)
        self.horizontalLayout_4.setStretch(4, 1)
        self.horizontalLayout_4.setStretch(5, 1)
        self.horizontalLayout_4.setStretch(6, 1)
        self.horizontalLayout_4.setStretch(7, 1)
        self.horizontalLayout_4.setStretch(8, 1)
        self.horizontalLayout_4.setStretch(9, 1)
        self.horizontalLayout_4.setStretch(10, 1)
        self.horizontalLayout_4.setStretch(11, 1)
        self.horizontalLayout_4.setStretch(12, 1)
        self.verticalLayout.addLayout(self.horizontalLayout_4)
        self.gridLayout_2.addWidget(self.frame_2, 0, 0, 1, 1)
        self.gridLayout_2.setRowStretch(0, 1)

        self.retranslateUi(TestTools)
        QtCore.QMetaObject.connectSlotsByName(TestTools)

    def retranslateUi(self, TestTools):
        _translate = QtCore.QCoreApplication.translate
        TestTools.setWindowTitle(_translate("TestTools", "Dialog"))
        self.mate_push_img_btn.setText(_translate("TestTools", "Mate推图工具"))
        self.endurance_btn.setText(_translate("TestTools", "继电器工具"))
        self.picture_tool_btn.setText(_translate("TestTools", "图片生成工具"))
        self.anti_pinch_btn.setText(_translate("TestTools", "防夹测试工具"))
        self.io_control_btn.setText(_translate("TestTools", "板卡运动控制工具"))
        self.position_control_btn.setText(_translate("TestTools", "板卡位置控制工具"))
        self.dbc_send_btn.setText(_translate("TestTools", "DBC发送工具"))
        self.dbc_recv_btn.setText(_translate("TestTools", "DBC接收工具"))
        self.auto_press_btn.setText(_translate("TestTools", "自动按键工具"))
        self.tri_color_light_btn.setText(_translate("TestTools", "三色灯工具"))
        self.tommens_power_btn.setText(_translate("TestTools", "Tommens程控电源"))
        self.it_m3200_power_btn.setText(_translate("TestTools", "IT-M3200程控电源"))
        self.rotate_test_btn.setText(_translate("TestTools", "角度测试工具"))
        self.optical_test_btn.setText(_translate("TestTools", "光学测试工具"))
        self.light_sensor_test_btn.setText(_translate("TestTools", "光感测试工具"))
        self.temp_rise_btn.setText(_translate("TestTools", "温升测试工具"))
        self.video_test_btn.setText(_translate("TestTools", "视频测试工具"))
        self.i2c_update_btn.setText(_translate("TestTools", "MCU升级"))
        self.elevation_angle_btn.setText(_translate("TestTools", "  仰角测量  "))
        self.vds_upgrade_btn.setText(_translate("TestTools", "   VDS升级   "))
        self.lin_debug_btn.setText(_translate("TestTools", "LIN调试工具"))
        self.write_sn_btn.setText(_translate("TestTools", "写号工具"))
        self.pushButton_2.setText(_translate("TestTools", "调试工具"))
        self.pushButton_3.setText(_translate("TestTools", "调试工具"))
