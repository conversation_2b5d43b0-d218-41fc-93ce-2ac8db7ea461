import struct

from simbox_tools.AppExcept import AppExcept
from simbox_tools.Logger import Logger



class BytesTool(object):
    __logTag = "BytesTool"
    __bytes = None
    __name = None

    def __init__(self, name="Default bytes"):
        self.__name = name
        self.__bytes = None

    def print(self):
        if self.__bytes is None:
            Logger.console(self.__logTag, "There is nothing to print!")
            return
        print("[%s(0x%02x)] : " % (self.__name, len(self.__bytes)), end="")
        for index in self.__bytes:
            print("0x%02x" % index, end=" ")
        print("")

    def log(self, file=None):
        string = "[%s(0x%02x)] :" % (self.__name, len(self.__bytes))
        for index in self.__bytes:
            string += " 0x%02x" % index
        if (file is None) or (file.write is None):
            print(string)
        else:
            file.write(string)

    def set(self, data):
        self.__bytes = data

    def get(self):
        return self.__bytes

    def get_length(self):
        if self.__bytes is None:
            return 0
        return len(self.__bytes)

    def get_string(self, size, format="utf-8"):
        return self.get_byte_list(size).decode(format)

    def append_string(self, string, format="utf-8"):
        data = string.encode(format)
        self.append_byte_list(data)

    def get_byte_list(self, size=None):
        if self.__bytes is None:
            raise AppExcept("The BytesTool object is None!")
        if size is None:
            size = len(self.__bytes)
        elif size > len(self.__bytes):
            raise AppExcept("The size of data is too big!")
        temp, self.__bytes = struct.unpack('%ds%ds' % (size, len(self.__bytes) - size), self.__bytes)
        return temp

    def append_byte_list(self, data, size=0):
        temp = None
        if (data is None) or (len(data) <= 0):
            if size > 0:
                temp = struct.pack("B", 0)
                for index in range(size - 1):
                    temp = struct.pack("%dsB" % len(temp), temp, 0)
        else:
            for index in data:
                if temp is None:
                    temp = struct.pack("B", index)
                else:
                    temp = struct.pack("%dsB" % len(temp), temp, index)
        if self.__bytes is None:
            self.__bytes = temp
        else:
            self.__bytes = struct.pack("%ds%ds" % (len(self.__bytes), len(temp)), self.__bytes, temp)

    def get_int(self, size, type=True):
        data = 0
        if self.__bytes is None:
            raise AppExcept("The BytesTool object is None!")
        if (size > 4) or (size > len(self.__bytes)):
            raise AppExcept("The size of data is too big!")
        if type is True:
            for index in range(size):
                temp, self.__bytes = struct.unpack('B%ds' % (len(self.__bytes) - 1), self.__bytes)
                data += (temp << (8 * index))
        else:
            for index in range(size):
                temp, self.__bytes = struct.unpack("B%ds" % (len(self.__bytes) - 1), self.__bytes)
                data <<= 8
                data += temp
        return data

    def append_int(self, data, size=1, type=True):
        if size > 4:
            raise AppExcept("The size of data is too big!")
        if type is True:
            temp = struct.pack("B", data & 0xff)
            data >>= 8
            for index in range(size - 1):
                temp = struct.pack("%dsB" % len(temp), temp, data & 0xff)
                data >>= 8
        else:
            temp = struct.pack("B", data & 0xff)
            data >>= 8
            for index in range(size - 1):
                temp = struct.pack("B%ds" % len(temp), data & 0xff, temp)
                data >>= 8

        if self.__bytes is None:
            self.__bytes = temp
        else:
            self.__bytes = struct.pack("%ds%ds" % (len(self.__bytes), len(temp)), self.__bytes, temp)

    def is_equal(self, obj):
        if obj is None:
            return False
        return self.__bytes == obj.__bytes


if __name__ == '__main__':
    print(int("22",16))
