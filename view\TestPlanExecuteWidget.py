import copy
import datetime
import operator
import threading
import traceback

import cv2
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QBrush, QColor, QFont
from PyQt5.QtWidgets import QWidget, QTableWidgetItem, QListView, QPushButton

from common.LogUtils import logger
from common.view.CaseDecisionDialog import CaseDecisionDialog
from case.CaseManager import case_manager, CaseStatus, CaseMode
from case.StepManager import step_manager
from fs_manager.FSManager import fs_manager
from res import bold_font, global_label_stylesheet
from ui.TestPlanExecute import Ui_TestPlanExecuteForm
from utils.ProjectManager import project_manager
from utils.ScreenManager import screen_manager
from utils.SignalsManager import signals_manager
from vision.VisualDetectSignal import visual_detect_signal
from common.view.MessageDialog import MessageDialog


class TestPlanExecuteWidget(QWidget, Ui_TestPlanExecuteForm):

    def __init__(self, parent=None):
        super(TestPlanExecuteWidget, self).__init__(parent)
        self.setupUi(self)
        self.init_view()
        self.monitor_video_label.setScaledContents(False)
        self.monitor_video_label.setText("No Video Signal")
        self.monitor_video_label.setStyleSheet(global_label_stylesheet)
        self.monitor_video_label.setFont(QFont('Arial', 50, weight=QFont.Bold))
        self.monitor_video_label.setAlignment(Qt.AlignCenter)
        self.cases_info_table_widget.horizontalHeader().setFixedHeight(40)
        self.cases_info_table_widget.verticalHeader().hide()
        self.steps_info_table_widget.horizontalHeader().setFixedHeight(40)
        self.steps_info_table_widget.verticalHeader().hide()
        self.cases_info_table_widget.setFocusPolicy(Qt.NoFocus)
        self.steps_info_table_widget.setFocusPolicy(Qt.NoFocus)
        signals_manager.update_case_status.connect(self.update_case_status)
        signals_manager.update_case_result.connect(self.update_case_result)
        signals_manager.update_case_pass_ng_times.connect(self.update_case_pass_ng_times)
        signals_manager.update_test_status.connect(self.update_test_status)
        signals_manager.update_project_info.connect(self.update_project_info)
        signals_manager.step_execute_process.connect(self.step_execute_process)
        signals_manager.operate_test_status.connect(self.operate_test_status)
        self.camera = None
        self.tableRowCount = 0
        self.is_execute_complete = False
        visual_detect_signal.video_change.connect(self.graphics_video)
        signals_manager.init_steps.connect(self.init_steps)
        signals_manager.update_step_status.connect(self.update_step_status)
        self.cases_info_table_widget.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.start_btn.clicked.connect(self.start_plan)
        self.start_btn.setFixedWidth(150)
        signals_manager.connect_camera.connect(
            lambda number, status: threading.Thread(target=self.set_camera, args=(number, status)).start())
        self.step_header = ["步骤名称", "步骤参数", "期望值", "测试值", "执行结果"]
        self.steps_info_table_widget.setColumnCount(len(self.step_header))
        self.steps_info_table_widget.setHorizontalHeaderLabels(self.step_header)
        signals_manager.clear_plan.connect(self.clear_plan)
        signals_manager.update_plan_detail.connect(self.init_plan)
        signals_manager.update_v2_plan.connect(self.update_v2_plan)
        signals_manager.update_v2_plan_detail.connect(self.init_v2_plan)
        signals_manager.update_case_execute_times.connect(self.update_case_execute_times)
        self.comboBox.setView(QListView())
        self.cases_info_table_widget.setAutoScroll(True)
        case_percent = {0: 0.08, 1: 0.2, 2: 0.25, 3: 0.1, 4: 0.1, 5: 0.08, 6: 0.1, 7: 0.1}
        self.cases_info_table_widget.set_column_percent(case_percent)
        self.steps_info_table_widget.setAutoScroll(True)
        self.steps_info_table_widget.set_column_percent({0: 0.2, 1: 0.2, 2: 0.2, 3: 0.25, 4: 0.149})
        self.cases_info_table_widget.horizontalHeader().setFont(bold_font)
        self.steps_info_table_widget.horizontalHeader().setFont(bold_font)
        self.monitor_video_label.setFixedSize(int(screen_manager.screen_width * 2 / 5),
                                              int(screen_manager.screen_height * 3 / 7))
        self.monitor_video_label.setScaledContents(True)
        self.refresh_plan_btn.clicked.connect(self.proxy_v2_get_plans)
        self.comboBox.currentIndexChanged.connect(self.current_plan_changed)
        signals_manager.test_state_signal.connect(self.state_handle)
        signals_manager.tips_fs_err_msg.connect(self.show_tips_msg)
        signals_manager.gather_logic_analyzer_data.connect(self.show_tips_msg)

    def init_view(self):
        self.label.setStyleSheet(global_label_stylesheet)
        self.version_info_label.setStyleSheet(global_label_stylesheet)
        self.label_3.setStyleSheet(global_label_stylesheet)
        self.case_pass_count_label.setStyleSheet(global_label_stylesheet)
        self.label_8.setStyleSheet(global_label_stylesheet)
        self.case_ng_count_label.setStyleSheet(global_label_stylesheet)
        self.label_6.setStyleSheet(global_label_stylesheet)
        self.case_decision_count_label.setStyleSheet(global_label_stylesheet)
        self.label_5.setStyleSheet(global_label_stylesheet)

        self.cases_info_table_widget.setStyleSheet(global_label_stylesheet)
        self.steps_info_table_widget.setStyleSheet(global_label_stylesheet)

        self.label_7.setStyleSheet(global_label_stylesheet)
        self.execute_case_label.setStyleSheet(global_label_stylesheet)
        self.label_2.setStyleSheet(global_label_stylesheet)
        self.pass_times_label.setStyleSheet(global_label_stylesheet)
        self.label_4.setStyleSheet(global_label_stylesheet)
        self.ng_times_label.setStyleSheet(global_label_stylesheet)

    @staticmethod
    def show_tips_msg(title, info):
        logger.info(f"show_tips_msg title={title}, info={info}")
        MessageDialog.show_auto_close_message(title, info, 3000)

    def state_handle(self, value):
        if value == "start_test" or value == "pause_test":
            self.start_btn.click()

    def current_plan_changed(self):
        logger.info(f"current_plan_changed")
        if len(self.comboBox) == 0:
            return
        plan = self.comboBox.currentData()
        # logger.info(f"current_plan_changed plan={plan}")
        fs_manager.proxy_get_v2_plan_detail(plan["id"])

    def proxy_get_plans(self):
        self.comboBox.clear()
        fs_manager.proxy_get_plans(project_manager.get_project_number())

    def proxy_v2_get_plans(self):
        if case_manager.status == CaseStatus.TESTING:
            return logger.warning(f"proxy_v2_get_plans 正在测试中，请勿刷新测试计划")
        self.comboBox.clear()
        fs_manager.proxy_v2_get_plans(project_manager.get_project_number())

    def update_case_execute_times(self, case_index, total_times, execute_times):
        logger.info(f"update_case_execute_times case_index={case_index}, total_times={total_times}, "
                    f"execute_times={execute_times}")
        self.cases_info_table_widget.item(case_index, 5).setText(str(total_times))
        self.cases_info_table_widget.item(case_index, 6).setText(str(execute_times))

    @staticmethod
    def decision_case_result(case_number):
        logger.info(f"decision_case_result case_number={case_number}, current_case={case_manager.current_case_number}")
        if case_manager.current_case_number != case_number:
            return logger.warning(f"decision_case_result case_manager.current_case_number != case_number")

        if case_manager.status == CaseStatus.FINISH:
            return logger.warning(f"decision_case_result 测试计划已执行结束，请勿重复提交测试结果")

        if case_manager.get_case_status(case_number) == CaseStatus.IDLE:
            return logger.warning(f"decision_case_result 测试用例未开始执行，请勿提交测试结果")

        def callback_result(result: int, step_actual):
            if result == 0x00:
                step = step_manager.get_current_step()
                if step is None:
                    return logger.warning(f"step_execute_finish current step is None")

                step_end_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                step.update({"end_time": step_end_time})
                step.update({"status": "已执行"})
                step.update({"step_result": "PASS"})
                step.update({"step_actual": step_actual})
                result = True
            elif result == 0x01:
                step = step_manager.get_current_step()
                if step is None:
                    return logger.warning(f"step_execute_finish current step is None")

                step_end_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                step.update({"end_time": step_end_time})
                step.update({"status": "已执行"})
                step.update({"step_result": "NG"})
                step.update({"step_actual": step_actual})
                result = False

            steps = copy.deepcopy(step_manager.steps)
            signals_manager.case_execute_finish.emit(case_number, "PASS" if result else "NG", False)
            threading.Thread(target=step_manager.post_manual_test_records, args=(case_number, result, steps)).start()

        case_decision_dialog = CaseDecisionDialog(title="提示", info="请输入实测值并判定测试结果")
        case_decision_dialog.setWindowFlags(Qt.WindowStaysOnTopHint)
        case_decision_dialog.set_callback(callback_result)
        case_decision_dialog.exec()

    def check_details(self, case_number):
        logger.info(f"check_details case_number={case_number}")

    def init_cases(self):
        logger.info("init_cases")
        if len(case_manager.cases) > 0:
            self.cases_info_table_widget.setRowCount(len(case_manager.cases))
            for i in range(len(case_manager.cases)):
                case_number = case_manager.cases[i].get("number", "")
                case_name = case_manager.cases[i].get("name", "")
                module = case_manager.cases[i].get("module_name", "")
                version = case_manager.cases[i].get("version_name", "")
                status = case_manager.cases[i].get("status_name", "")
                execute_mode = case_manager.cases[i].get("execute_mode", "")
                result = case_manager.cases[i].get("result", {})
                item = QTableWidgetItem(str(i + 1))
                item.setTextAlignment(Qt.AlignCenter)
                self.cases_info_table_widget.setItem(i, 0, item)
                item = QTableWidgetItem(case_number)
                item.setTextAlignment(Qt.AlignCenter)
                self.cases_info_table_widget.setItem(i, 1, item)
                item = QTableWidgetItem(case_name)
                self.cases_info_table_widget.setItem(i, 2, item)
                item = QTableWidgetItem(module)
                item.setTextAlignment(Qt.AlignCenter)
                self.cases_info_table_widget.setItem(i, 3, item)
                item = QTableWidgetItem(version)
                item.setTextAlignment(Qt.AlignCenter)
                self.cases_info_table_widget.setItem(i, 4, item)
                item = QTableWidgetItem("")
                item.setTextAlignment(Qt.AlignCenter)
                self.cases_info_table_widget.setItem(i, 5, item)
                item = QTableWidgetItem("")
                item.setTextAlignment(Qt.AlignCenter)
                self.cases_info_table_widget.setItem(i, 6, item)
                # logger.info(f"init_cases case_number={case_number}, case_name={case_name}, module={module}, "
                #             f"version={version}, status={status}")
                if execute_mode in ["AUTOMATED_EXECUTION", "SEMI_AUTOMATED_EXECUTION"]:
                    if len(result) == 0:
                        item = QTableWidgetItem("待执行")
                        item.setFont(bold_font)
                    else:
                        case_result = result["result"]
                        if case_result is None:
                            item = QTableWidgetItem("待判定")
                            item.setForeground(QBrush(QColor('#cf8e6d')))
                            item.setFont(bold_font)
                        elif case_result:
                            item = QTableWidgetItem("PASS")
                            item.setForeground(QBrush(QColor('green')))
                            item.setFont(bold_font)
                        else:
                            item = QTableWidgetItem("NG")
                            item.setForeground(QBrush(QColor('#f05d28')))
                            item.setFont(bold_font)

                    item.setTextAlignment(Qt.AlignCenter)
                    # item.setFlags(item.flags() & ~Qt.ItemIsEnabled)
                    self.cases_info_table_widget.setItem(i, 7, item)
                else:
                    if len(result) == 0:
                        btn = QPushButton("判定")
                        btn.setEnabled(True)
                        btn.setFont(bold_font)
                        btn.clicked.connect(lambda checked, number=case_number: self.decision_case_result(number))
                        self.cases_info_table_widget.setCellWidget(i, 7, btn)
                    else:
                        case_result = result["result"]
                        if case_result is None:
                            item = QTableWidgetItem("待判定")
                            item.setForeground(QBrush(QColor('#cf8e6d')))
                            item.setFont(bold_font)
                        elif case_result:
                            item = QTableWidgetItem("PASS")
                            item.setForeground(QBrush(QColor('green')))
                            item.setFont(bold_font)
                        else:
                            item = QTableWidgetItem("NG")
                            item.setForeground(QBrush(QColor('#f05d28')))
                            item.setFont(bold_font)

                        item.setTextAlignment(Qt.AlignCenter)
                        self.cases_info_table_widget.setItem(i, 7, item)

                self.cases_info_table_widget.setRowHeight(i, 50)

    def init_steps(self):
        logger.info("init_steps")
        try:
            self.steps_info_table_widget.setColumnCount(len(self.step_header))
            self.steps_info_table_widget.setRowCount(len(step_manager.step_status))
            for i in range(len(step_manager.step_status)):
                command = step_manager.step_status[i].get("command", "")
                params = step_manager.step_status[i].get("params", "")
                expect = step_manager.step_status[i].get("expect", "")
                actual = step_manager.step_status[i].get("actual", "")
                status = step_manager.step_status[i].get("status", "")
                logger.info(f"init_step_status command={command}, params={params}, expect={expect}, actual={actual}, "
                            f"status={status}")
                item = QTableWidgetItem(str(command))
                item.setTextAlignment(Qt.AlignCenter)
                self.steps_info_table_widget.setItem(i, 0, item)
                item = QTableWidgetItem(str(params))
                item.setTextAlignment(Qt.AlignCenter)
                self.steps_info_table_widget.setItem(i, 1, item)
                item = QTableWidgetItem(str(expect))
                item.setTextAlignment(Qt.AlignCenter)
                self.steps_info_table_widget.setItem(i, 2, item)
                item = QTableWidgetItem(str(actual))
                item.setTextAlignment(Qt.AlignCenter)
                self.steps_info_table_widget.setItem(i, 3, item)
                item = QTableWidgetItem(str(status))
                item.setTextAlignment(Qt.AlignCenter)
                item.setFont(bold_font)
                self.steps_info_table_widget.setItem(i, 4, item)

                self.steps_info_table_widget.setRowHeight(i, 50)
        except Exception as e:
            logger.error(f"init_step_status exception: {str(e.args)}")

    def update_step_status(self, command, status, actual):
        if case_manager.mode == CaseMode.FUNCTION:
            step_command = ""
            if step_manager.step_index < len(step_manager.step_status):
                step_command = step_manager.step_status[step_manager.step_index]["command"]
            # logger.info(f"update_step_status step_command={step_command}, command={command}, status={status}, "
            #             f"actual={actual}")
            if step_command == command:
                item = QTableWidgetItem(actual)
                item.setTextAlignment(Qt.AlignCenter)
                self.steps_info_table_widget.setItem(step_manager.step_index, 3, item)
                item = QTableWidgetItem(status)
                item.setTextAlignment(Qt.AlignCenter)
                if operator.eq("PASS", status):
                    item.setForeground(QBrush(QColor('green')))
                    item.setFont(bold_font)
                elif operator.eq("NG", status):
                    item.setForeground(QBrush(QColor('#f05d28')))
                    item.setFont(bold_font)
                elif operator.eq("待判定", status):
                    item.setForeground(QBrush(QColor('#cf8e6d')))
                    item.setFont(bold_font)
                elif operator.eq("执行中", status):
                    item.setForeground(QBrush(QColor('#cf8e6d')))
                    item.setFont(bold_font)
                self.steps_info_table_widget.setItem(step_manager.step_index, 4, item)

    def update_step_start_time(self, command, start_time):
        if case_manager.mode == CaseMode.FUNCTION:
            step_command = ""
            if step_manager.step_index < len(step_manager.step_status):
                step_command = step_manager.step_status[step_manager.step_index]["command"]
            logger.info(f"update_step_start_time step_command={step_command}, command={command}, "
                        f"start_time={start_time}")
            if step_command == command:
                item = QTableWidgetItem(start_time)
                item.setTextAlignment(Qt.AlignCenter)
                self.steps_info_table_widget.setItem(step_manager.step_index, 2, item)

    def update_step_end_time(self, command, end_time):
        if case_manager.mode == CaseMode.FUNCTION:
            step_command = ""
            if step_manager.step_index < len(step_manager.step_status):
                step_command = step_manager.step_status[step_manager.step_index]["command"]
            logger.info(f"update_step_end_time step_command={step_command}, command={command}, end_time={end_time}")
            if step_command == command:
                item = QTableWidgetItem(end_time)
                item.setTextAlignment(Qt.AlignCenter)
                self.steps_info_table_widget.setItem(step_manager.step_index, 3, item)

    def graphics_video(self, image):
        self.monitor_video_label.setPixmap(image)

    def set_camera(self, number, status):
        logger.info(f"set_camera number={number}, status={status}")
        if status:
            self.camera = cv2.VideoCapture(number)
            if not self.camera.isOpened():
                return
        else:
            self.camera = None

    def clear_plan(self):
        logger.info(f"clear_plan")
        self.comboBox.clear()

    def init_plan(self, project_name, project_number, plan_name, plan_id, plan_dic):
        logger.info(f"init_plan project_name={project_name}, project_number={project_number}, plan_name={plan_name}, "
                    f"plan_id={plan_id}")
        sub_plans = plan_dic.get('sub_plans', [])
        software_version = plan_dic.get('software_version', "")
        product_version = plan_dic.get('product_version', [])
        for plan in sub_plans:
            plan.update({"plan_id": plan_id})
            sub_plan_name = plan.get("name", "")
            sub_plan_id = plan.get("id", -1)
            exec_status = plan.get('exec_status', False)
            abnormal_stop = plan.get('abnormal_stop', False)
            logger.info(f"init_plan plan_id={plan_id}, sub_plan_name={sub_plan_name}, exec_status={exec_status}, "
                        f"abnormal_stop={abnormal_stop}")
            data = {"project_name": project_name, "project_number": project_number, "plan_name": plan_name,
                    "plan_id": plan_id, "sub_plan_name": sub_plan_name, "sub_plan_id": sub_plan_id,
                    "software_version": software_version, "product_version": product_version,
                    "abnormal_stop": abnormal_stop}
            self.comboBox.addItem(f"{sub_plan_name}", data)
            project_manager.machine_plans.append(plan)
            project_manager.project_plans.append(plan)

    def update_v2_plan(self, plans):
        logger.info(f"update_v2_plan plans={plans}")
        for plan in plans:
            plan_name = plan["name"]
            self.comboBox.addItem(f"{plan_name}", plan)
            project_manager.machine_plans.append(plan)

    def init_v2_plan(self, data):
        # logger.info(f"init_v2_plan data={data}")
        self.cases_info_table_widget.clearContents()
        self.steps_info_table_widget.clearContents()
        case_manager.append_plan_case(data["test_cases"])
        self.init_cases()

    def reset_case_status(self):
        try:
            for row in range(self.cases_info_table_widget.rowCount()):
                item = QTableWidgetItem("待执行")
                item.setFont(bold_font)
                self.cases_info_table_widget.setItem(row, 2, item)
                self.cases_info_table_widget.setRowHeight(row, 50)
        except Exception as e:
            logger.error("reset_case_status exception: {}".format(str(e.args)))

    def init_cases_execute_status(self):
        logger.info(f"init_cases_execute_status")
        if len(case_manager.cases) > 0:
            self.cases_info_table_widget.setRowCount(len(case_manager.cases))
            for i in range(len(case_manager.cases)):
                case_number = case_manager.cases[i].get("number", "")
                case_name = case_manager.cases[i].get("name", "")
                module = case_manager.cases[i].get("module_name", "")
                version = case_manager.cases[i].get("version_name", "")
                status = case_manager.cases[i].get("status_name", "")
                execute_mode = case_manager.cases[i].get("execute_mode", "")
                case_retest = case_manager.cases[i].get("isRetest", False)
                # result = case_manager.cases[i].get("result", {})
                # logger.info(f"init_cases case_number={case_number}, case_name={case_name}, module={module}, "
                #             f"version={version}, status={status}, result={result}, case_retest={case_retest}")
                item = QTableWidgetItem(str(i + 1))
                item.setTextAlignment(Qt.AlignCenter)
                self.cases_info_table_widget.setItem(i, 0, item)
                item = QTableWidgetItem(case_number)
                item.setTextAlignment(Qt.AlignCenter)
                self.cases_info_table_widget.setItem(i, 1, item)
                item = QTableWidgetItem(case_name)
                self.cases_info_table_widget.setItem(i, 2, item)
                item = QTableWidgetItem(module)
                item.setTextAlignment(Qt.AlignCenter)
                self.cases_info_table_widget.setItem(i, 3, item)
                item = QTableWidgetItem(version)
                item.setTextAlignment(Qt.AlignCenter)
                self.cases_info_table_widget.setItem(i, 4, item)
                item = QTableWidgetItem("")
                item.setTextAlignment(Qt.AlignCenter)
                self.cases_info_table_widget.setItem(i, 5, item)
                item = QTableWidgetItem("")
                item.setTextAlignment(Qt.AlignCenter)
                self.cases_info_table_widget.setItem(i, 6, item)
                logger.info(f"init_cases case_number={case_number}, case_name={case_name}, module={module}, "
                            f"version={version}, status={status}")
                if execute_mode in ["AUTOMATED_EXECUTION", "SEMI_AUTOMATED_EXECUTION"]:
                    if case_retest:
                        item = QTableWidgetItem("待执行")
                        item.setFont(bold_font)
                        item.setTextAlignment(Qt.AlignCenter)
                        self.cases_info_table_widget.setItem(i, 7, item)
                else:
                    if case_retest:
                        btn = QPushButton("判定")
                        btn.setEnabled(True)
                        btn.setFont(bold_font)
                        btn.clicked.connect(lambda checked, number=case_number: self.decision_case_result(number))
                        self.cases_info_table_widget.setCellWidget(i, 7, btn)

                self.cases_info_table_widget.setRowHeight(i, 50)

    def start_plan(self):
        logger.info(f"start_plan case_state={case_manager.status}")
        self.case_pass_count_label.setText("0")
        self.case_ng_count_label.setText("0")
        self.case_decision_count_label.setText("0")
        self.pass_times_label.setText("0")
        self.ng_times_label.setText("0")

        if len(project_manager.machine_plans) == 0:
            return logger.warning("start_plan 测试计划不能为空")

        if len(case_manager.cases) == 0:
            return logger.warning("start_plan 测试用例不能为空")

        try:
            if case_manager.status == CaseStatus.IDLE or case_manager.status == CaseStatus.FINISH:
                self.start_btn.setText("停止测试")
                self.init_cases_execute_status()
                plan = self.comboBox.currentData()
                threading.Thread(target=case_manager.start_execute_case, args=(plan,)).start()
                self.comboBox.setEnabled(False)
            else:
                self.stop_plan()
        except Exception as e:
            print(traceback.format_exc())
            logger.error("execute_plan_case exception: {}".format(str(e.args)))

    @staticmethod
    def stop_plan():
        logger.info("stop_plan")
        if case_manager.status == CaseStatus.TESTING:
            step_manager.stop_test_plan(case_manager.current_case_number)
        #停止电流监测
        case_manager.stop_work_current_updater()

    def update_case_status(self, case_index, case_number, case_status):
        logger.info(f"update_case_status case_index={case_index}, case_number={case_number}, case_status={case_status}")
        try:
            if case_manager.current_case is not None:
                if case_manager.current_case.get("execute_mode") in ["AUTOMATED_EXECUTION", "SEMI_AUTOMATED_EXECUTION"]:
                    item = self.cases_info_table_widget.item(case_index, 7)
                    item.setText(case_status)
                    if case_status.__eq__("PASS"):
                        item.setForeground(QBrush(QColor('green')))
                        item.setFont(bold_font)
                    elif case_status.__eq__("NG"):
                        item.setForeground(QBrush(QColor('#f05d28')))
                        item.setFont(bold_font)
                    elif case_status.__eq__("待判定"):
                        item.setForeground(QBrush(QColor('#cf8e6d')))
                        item.setFont(bold_font)
                    elif case_status.__eq__("执行中"):
                        item.setForeground(QBrush(QColor('#cf8e6d')))
                        item.setFont(bold_font)
                    self.execute_case_label.setText(case_number)
                else:
                    if case_status.__eq__("PASS"):
                        item = self.cases_info_table_widget.cellWidget(case_index, 8)
                        item.setText(case_status)
                        item.setEnabled(False)
                        item.setFont(bold_font)
                    elif case_status.__eq__("NG"):
                        item = self.cases_info_table_widget.cellWidget(case_index, 8)
                        item.setText(case_status)
                        item.setEnabled(False)
                        item.setFont(bold_font)
                    elif case_status.__eq__("待判定"):
                        item = self.cases_info_table_widget.cellWidget(case_index, 8)
                        item.setText(case_status)
                        item.setForeground(QBrush(QColor('#cf8e6d')))
                        item.setFont(bold_font)
                    self.execute_case_label.setText(case_number)
        except Exception as e:
            logger.error(f"update_case_status exception: {str(e.args)}")

    def update_case_result(self, case_number):
        case_pass_number, case_ng_number, case_decision_number = case_manager.get_case_pass_ng_number()
        logger.info(f"update_case_result case_number={case_number}, case_pass_number={case_pass_number}, "
                    f"case_ng_number={case_ng_number}, case_decision_number={case_decision_number}")
        self.case_pass_count_label.setText(str(case_pass_number))
        self.case_ng_count_label.setText(str(case_ng_number))
        self.case_decision_count_label.setText(str(case_decision_number))

    def update_case_pass_ng_times(self, case_number):
        pass_times, ng_times = case_manager.get_current_case_pass_ng_times(case_number)
        logger.info(f"update_case_pass_ng_times pass_times={pass_times}, ng_times={ng_times}")
        self.pass_times_label.setText(str(pass_times))
        self.ng_times_label.setText(str(ng_times))

    def update_test_status(self, state):
        logger.info(f"update_test_status state={state}, case_mode={case_manager.mode}")
        if case_manager.mode == CaseMode.FUNCTION:
            if state == CaseStatus.IDLE:
                logger.info("update_test_status 待测试")
            elif state == CaseStatus.TESTING:
                logger.info("update_test_status 测试中")
            elif state == CaseStatus.FINISH:
                logger.info("update_test_status 测试完成")
                self.start_btn.setText("启动测试")
                self.comboBox.setEnabled(True)

    def update_project_info(self):
        test_version = project_manager.get_test_version()
        relate_version = project_manager.get_relate_version()
        logger.info(f"update_project_info test_version={test_version}, relate_version={relate_version}")
        self.version_info_label.setText(f"测试版本：{test_version}")

    def step_execute_process(self, case_number, command, actual):
        logger.info(f"step_execute_process case_number={case_number}, command={command}, actual={actual}")
        self.update_step_status(command, "执行中", actual)

    def operate_test_status(self, status):
        logger.info(f"operate_test_status status={status}")
        if not status:
            self.stop_plan()
