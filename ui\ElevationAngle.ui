<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>elevationForm</class>
 <widget class="QWidget" name="elevationForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1600</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1600</width>
    <height>900</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="0,1">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <layout class="QVBoxLayout" name="verticalLayout">
         <item>
          <widget class="QLCDNumber" name="lcdNumber">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>200</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>9</pointsize>
            </font>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout">
           <item>
            <widget class="QPushButton" name="pushButtonStart">
             <property name="text">
              <string>测量</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="pushButtonStop">
             <property name="text">
              <string>停止</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <widget class="CustomPlotWidget" name="widget_display" native="true">
       <property name="styleSheet">
        <string notr="true">background-color: rgb(168, 168, 168);</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>CustomPlotWidget</class>
   <extends>QWidget</extends>
   <header location="global">ui.canvas.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
