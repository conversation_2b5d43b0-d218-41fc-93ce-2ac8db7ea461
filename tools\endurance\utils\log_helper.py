import os

import logging.config

USER_DIR = os.path.expanduser('~')
log_path = os.path.join(USER_DIR, "endurance", "logs")
if not os.path.exists(log_path):
    os.makedirs(log_path)


# log配置字典
LOGGING_DIC = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '[%(asctime)s][%(threadName)s:%(thread)d][task_id:%(name)s][%(filename)s:%(lineno)d][%(levelname)s][%(message)s]'
        },
        'simple': {
            'format': '%(asctime)s - %(levelname)s - %(name)s - %(filename)s:%(lineno)d - %(message)s'
        },
    },
    'filters': {

    },
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple'
        },
        'default': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',  # 保存到文件
            'formatter': 'simple',
            'filename': os.path.join(log_path, "common.log"),  # 日志文件
            'maxBytes': 1024 * 1024 * 100,
            'backupCount': 1000,
            'encoding': 'utf-8',
        },
        'error': {
            'level': 'WARNING',
            'class': 'logging.handlers.RotatingFileHandler',  # 保存到文件
            'formatter': 'simple',
            'filename': os.path.join(log_path, "error.log"),  # 日志文件
            'maxBytes': 1024 * 1024 * 100,  # 日志大小 5M
            'backupCount': 1000,
            'encoding': 'utf-8',
        },
        'widget': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',  # 保存到文件
            'formatter': 'simple',
            'filename': os.path.join(log_path, "widget.log"),  # 日志文件
            'maxBytes': 1024 * 1024 * 10,
            'backupCount': 1,
            'encoding': 'utf-8',
        },
    },
    'loggers': {
        'endurance': {
            'handlers': ["default", "error"],
            'level': 'DEBUG',
            'propagate': False,
        }
    },
}

logging.config.dictConfig(LOGGING_DIC)
