import traceback
from queue import Queue

import logging

import threading

import socket

import psutil
import time, os
from PyQt5.QtWidgets import QWidget
from win32com.client import *
from win32com.client.connect import *
from PyQt5.QtWidgets import QLabel, QApplication, QWidget
from PyQt5.QtGui import QMovie, QPixmap
from PyQt5.QtCore import Qt, QSize, QObject

from fs_manager.FSManager import fs_manager
from utils.ProjectManager import project_manager
from utils.SignalsManager import signals_manager


def DoEvents():
    pythoncom.PumpWaitingMessages()
    time.sleep(10)

def DoEventsUntil(cond):
    while not cond():
        DoEvents()



class CanoeSync(object):
    _instance = None
    _initialized = False  # 类变量，用于跟踪是否完成所有初始化
    _app_configured = False  # 类变量，用于跟踪是否完成配置加载

    """Wrapper class for CANoe Application object"""
    Started = False
    Stopped = False
    ConfigPath = ""
    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(CanoeSync, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        # 只在第一次初始化时执行
        if not CanoeSync._initialized:
            app = DispatchEx('CANoe.Application')
            app.Configuration.Modified = False
            ver = app.Version
            print('Loaded CANoe version ',
                  ver.major, '.',
                  ver.minor, '.',
                  ver.Build, '...', sep='')
            self.App = app
            self.Measurement = app.Measurement
            self.Running = lambda: self.Measurement.Running
            self.WaitForStart = lambda: DoEventsUntil(lambda: CanoeSync.Started)
            self.WaitForStop = lambda: DoEventsUntil(lambda: CanoeSync.Stopped)
            WithEvents(self.App.Measurement, CanoeMeasurementEvents)
            self.testResul_all = []  # 用于统计测试结果
            self.stop = False
            threading.Thread(target=self.start_server, args=("0.0.0.0", 8005,)).start()
            self.task_q = Queue()
            self.result_q = Queue()
            CanoeSync._initialized = True
            print("start canoe:", "==" * 100)
            cfg_path = get_project_extra_info()
            self._app_configured = False
            self.Load(cfg_path)
            self.run_test_modules()
            value = self.get_SysVar("pythonsys", "run_test")
            logging.info(f"before value if {value} ")
            self.set_SysVar("pythonsys", "run_test", 1)
            t = time.time()

            while time.time() - t < 10:
                value=self.get_SysVar("pythonsys", "run_test")
                if int(value)==1:
                    # self.task_q.put("11001")
                    self._app_configured = True
                    logging.info(f"value is {value} CANoe is ready")
                    break
                time.sleep(0.2)


    def set_app_configured(self,value):
        self._app_configured =value

    def is_alive(self):
        # 遍历所有进程
        for proc in psutil.process_iter(['pid', 'name']):
            # 如果进程名匹配
            if proc.info['name'] == "CANoe64.exe":
                return True
        return False
    def configure_app(self):
        """配置应用程序，确保配置正确执行"""
        if not self.is_alive():
            try:
                CanoeSync._initialized = False
                self.__init__()

            except Exception as e:
                print(f"Error configuring CANoe: {e}")
                self._app_configured = False
                raise
        else:
            pass
    def get_SysVar(self, ns_name, sysvar_name):
        # print(self.App)
        if (self.App != None):
            systemCAN = self.App.System.Namespaces
            sys_namespace = systemCAN(ns_name)
            sys_value = sys_namespace.Variables(sysvar_name)
            return sys_value.Value
        else:
            raise RuntimeError("CANoe is not open,unable to GetVariable")

    def set_SysVar(self, ns_name, sysvar_name, var):

        if (self.App != None):
            systemCAN = self.App.System.Namespaces
            sys_namespace = systemCAN(ns_name)
            sys_value = sys_namespace.Variables(sysvar_name)
            sys_value.Value = var
            print(sys_value.Value)
            # result = sys_value(sys_name)
            #
            # result = var
        else:
            raise RuntimeError("CANoe is not open,unable to GetVariable")


    def Start(self):
        if not self.Running():
            self.Measurement.Start()
            self.WaitForStart()

    def Stop(self):
        if self.Running():
            self.Measurement.Stop()
            self.WaitForStop()
        self.stop = True
        self._instance = None
        # 删除self.initialized 属性
        # del self.initialized
        # self.initialized = False

    def Load(self, cfgPath):
        # current dir must point to the script file
        cfg = os.path.join(os.curdir, cfgPath)
        cfg = os.path.abspath(cfg)
        print('Opening: ', cfg)
        self.ConfigPath = os.path.dirname(cfg)
        self.Configuration = self.App.Configuration
        self.App.Open(cfg)

    def LoadTestSetup(self, testsetup):
        self.TestSetup = self.App.Configuration.TestSetup
        print(self.ConfigPath)
        path = os.path.join(self.ConfigPath, testsetup)
        print("传入的tse路径：", path)
        # 如果目标 tse 已存在，直接读取，否则添加它,如果已经存在，直接add的话会报错
        tse_count = self.TestSetup.TestEnvironments.Count
        print("add前tse数量:",tse_count)
        _existTse = False
        for _index_tse in range(1, tse_count + 1):
            if self.TestSetup.TestEnvironments.Item(_index_tse).FullName == path:
                testenv = self.TestSetup.TestEnvironments.Item(_index_tse)
                _existTse = True
                break
        if _existTse == False:
            testenv = self.TestSetup.TestEnvironments.Add(path)

        print("add后tse数量:", self.TestSetup.TestEnvironments.Count)

        testenv = CastTo(testenv, "ITestEnvironment2")
         # TestModules property to access the test modules
        self.TestModules = []
        self.TraverseTestItem(testenv, lambda tm: self.TestModules.append(CanoeTestModule(tm)))

    def TraverseTestItem(self, parent, testf):
        for test in parent.TestModules:
            testf(test)
        for folder in parent.Folders:
            found = self.TraverseTestItem(folder, testf)

    def RunTestModules(self):
        """ starts all test modules and waits for all of them to finish"""
        # start all test modules

        for subtm in self.TestModules:
            subtm.Start()
            while not subtm.IsDone():
                DoEvents()

            for i in range(1, subtm.tm.Sequence.Count + 1):
                self.StatisticTesResult(subtm, subtm.tm.Sequence.Item(i))
        # 统计测试结果
        import pandas as pd
        df = pd.DataFrame(self.testResul_all,columns=['TestModule', 'TestGroup', 'TestCae','TestResult'])
        print(df)

    def StatisticTesResult(self, tm, tx):
        """
        Summary test case result
        :para tx: test group or case.
        :para tg: test group
        :para tc: test case
        """
        tg = CastTo(tx, "ITestGroup")
        try:
            _count = tg.Sequence.Count
        except Exception:  # test case
            tc = CastTo(tx, "ITestCase")
            self.testResul_all.append( [tm.Name, tg.Name, tc.Name, str(tc.Verdict)])
        else:  # test group
            for j in range(1, _count + 1):
                self.StatisticTesResult(tm, tg.Sequence.Item(j))

    def GetTestResult(self):
        """获取所有测试模块"""
        if self.result_q.empty():
            return None
        result = self.result_q.get()
        self.result_q.task_done()
        return result

    def SetTestModulesPath(self, log_path):
            for subtm in self.TestModules:
                report_name = subtm.tm.Report.Name + ".xml"
                fullname = os.path.join(log_path, report_name)
                report = subtm.tm.Report
                report.FullName = fullname
                print("report.FullName :", report.FullName)
    def SetLogging(self):
        """修改当前 logging 文件为默认（temp.blf）。"""
        for i in range(1, self.Configuration.OnlineSetup.LoggingCollection.Count + 1):
            print("**********",self.Configuration.OnlineSetup.LoggingCollection(i).FullName)
            filepath, tmpfilename = os.path.split(self.Configuration.OnlineSetup.LoggingCollection(i).FullName)
            _log_fullName = os.path.join(filepath, 'temp.asc')
            if os.path.exists(_log_fullName):
                os.remove(_log_fullName)
            try:
                self.Configuration.OnlineSetup.LoggingCollection(i).FullName = _log_fullName
            except Exception as e:
                print(e)
            else:
                self.logging = self.Configuration.OnlineSetup.LoggingCollection(i)
                self.temp_logName = self.logging.FullName
            print("**********", self.Configuration.OnlineSetup.LoggingCollection(i).FullName)
    def AddTest(self,module_id,test_id):
        print("AddTest:",module_id, test_id)
        modules = ['FLASH','UDS-TP','CANFD','AUTOSAR',"UDS-TP-BOOT"]
        # module_id = modules.index(module_id.upper())
        # test_id = test_id.rjust(3, '0')
        # task = f"01{module_id+1}{test_id}"
        task = f"[{module_id.upper()},{test_id}]"

        # task = "[1,2,1,2,3,4,5]"
        logging.info(f"task is :{task}", )
        self.task_q.put(task)
    def run_test_modules(self,):
        # self.Load(cfg_path)
        # logging.info("load cfg_path {} ".format(cfg_path))
        # self.LoadTestSetup(tse_path)
        # logging.info("load tse_path {} ".format(cfg_path))
        self.Start()
        logging.info("start ")
        from utils.SignalsManager import signals_manager
        # signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        # self.RunTestModules()
    def handle_client(self,client_socket, client_address):
        print(f"客户端 {client_address} 已连接。")
        try:
            while True:
                data = client_socket.recv(1024)  # 接收数据，最大接收量为 1024 字节

                if not data:
                    logging.info(f"客户端 {client_address} 已断开连接。")
                    continue  # 如果没有接收到数据，跳出循环，结束客户端服务
                logging.info(f"接收到来自 {client_address} 的数据: {data.decode()}")
                if "request task" in data.decode():
                    logging.info(f"request task,task queue size is :{self.task_q.qsize()}")
                    if not self.task_q.empty():
                        task = self.task_q.get()
                        logging.info(f"send task:{task}")
                        client_socket.sendall(f"{task}".encode())
                        self.task_q.task_done()
                    else:
                        client_socket.sendall(b"")# 没有任务
                elif "response" in data.decode():
                    result = data.decode().replace("response","").strip()
                    if "NG" in result.upper():
                        result = "NG"
                    else:
                        result="PASS"
                    self.result_q.put(result)
                elif "update_start" in data.decode():
                    signals_manager.canoe_update_process.emit("update_start")
                elif "update_ok" in data.decode():
                    signals_manager.canoe_update_process.emit("update_ok")
                elif "update_ng" in data.decode():
                    signals_manager.canoe_update_process.emit("update_ng")
                elif "tcp_conn ok" in data.decode():
                    logging.info(f"tcp_conn ok set pythonsys run_test 1")
                    self.set_SysVar("pythonsys", "run_test", 1)
                    #弹窗 升级失败
                elif "heart" in data.decode():
                    continue
                # client_socket.sendall(data)  # 返回接收到的相同数据，实现 echo 服务
        except Exception as e:
            logging.info(f"处理 {client_address} 时发生异常: {e}")
        finally:
            client_socket.close()  # 关闭连接

    def start_server(self,host, port):
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        server_socket.bind((host, port))  # 绑定地址和端口
        server_socket.listen()

        print(f"服务器启动，监听于 {host}:{port}")

        try:
            while not self.stop:
                client_socket, client_address = server_socket.accept()  # 接受客户端连接
                thread = threading.Thread(target=self.handle_client, args=(client_socket, client_address))
                thread.start()  # 开启新线程处理客户端连接
        except KeyboardInterrupt:
            print("服务器关闭")
        finally:
            server_socket.close()
    def handle_msg(self,msg,client_socket):
        pass

class CanoeTestModule:
    """Wrapper class for CANoe TestModule object"""
    def __init__(self, tm):
        self.tm = tm
        self.Events = DispatchWithEvents(tm, CanoeTestEvents)
        self.Name = tm.Name
        self.FullName = tm.FullName
        self.Path = tm.Path
        self.IsDone = lambda: self.Events.stopped
        self.Enabled = tm.Enabled

    def Start(self):
        if self.tm.Enabled:
            self.tm.Start()
            self.Events.WaitForStart()
    def WaitReportGenerate(self):
        if self.tm.Enabled:
            self.Events.WaitForReportGenerated()
class CanoeTestEvents:
    """Utility class to handle the test events"""
    def __init__(self):
        self.started = False
        self.stopped = False
        self.reportGenerated = False
        self.WaitForStart = lambda: DoEventsUntil(lambda: self.started)
        self.WaitForStop = lambda: DoEventsUntil(lambda: self.stopped)
        self.WaitForReportGenerated = lambda: DoEventsUntil(lambda: self.reportGenerated)

    def OnStart(self):
        self.started = True
        self.stopped = False
        self.reportGenerated = False
        print("<", self.Name, " started >")
    def OnStop(self, reason):
        self.started = False
        self.stopped = True
        print("<", self.Name, " stopped >")
    def OnReportGenerated(self, success, sourceFullName, generatedFullName):
        self.reportGenerated = True
        print("<test report generated>:", sourceFullName)
        # print(success)
        # print(generatedFullName)
        # print(sourceFullName)


class CanoeMeasurementEvents(object):
    """Handler for CANoe measurement events"""
    def OnStart(self):
        CanoeSync.Started = True
        CanoeSync.Stopped = False
        print("< measurement started >")

    def OnStop(self):
        CanoeSync.Started = False
        CanoeSync.Stopped = True
        print("< measurement stopped >")

def get_project_extra_info():
    project_number = project_manager.get_test_plan_project_number()
    try:
        project_extra = fs_manager.get_project_extra_info(project_number)
        logging.info(f"get_project_extra_info from {project_number}={project_extra}")
        cfg_path = project_extra["data"]["configs"]["canoe_cfg_path"]
        logging.info(f"cfg_path is {cfg_path}")
    except Exception:
        logging.info(f"cfg_path is None!{traceback.format_exc()}")
        cfg_path = None
    return cfg_path

def get_canoe_app(kill=False):
    """获取CanoeSync的单例，并确保初始化操作只执行一次"""
    from utils.ProjectManager import project_manager

    machine_number = project_manager.get_machine_number()

    if machine_number in ["HW-T-0019", "HW-T-0020"]:
        canoe_app = CanoeSync()

        CanoeSync.configure_app(canoe_app)
    else:
        logging.info(f"CanoeApp is not available on this machine {machine_number}")
        canoe_app = None

    return canoe_app

# canoe_app = get_canoe_app()

def main():
    Tester = CanoeSync()
    Tester.Load(r'D:\岚图\mate_Flash/Configuration1.cfg')
    Tester.LoadTestSetup(r'D:\岚图\mate_Flash/Test Environment.tse')
    #Tester.SetTestModulesPath(os.path.join(Tester.ConfigPath, r"TestReport"))
    #Tester.SetLogging()
    Tester.Start()

    # print(Tester.App)
    # number1=Tester.get_SysVar("pythonsys","numb1")
    # print(number1)
    # Tester.set_SysVar("pythonsys","numb1",9)
    # time.sleep(10)
    # number1 = Tester.get_SysVar("pythonsys", "numb1")
    # print(number1)
    Tester.RunTestModules()
    time.sleep(10)
    sys.exit()
    #Tester.Stop()


# CanoeApp = get_canoe_app()
# #
# if __name__ == "__main__":
#     main()