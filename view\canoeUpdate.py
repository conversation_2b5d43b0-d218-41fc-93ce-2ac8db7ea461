# import os
# import sys
#
# from PyQt5.QtWidgets import QLabel, QApplication, QWidget
# from PyQt5.QtGui import QMovie, QPixmap
# from PyQt5.QtCore import Qt, QSize, QObject
#
#
# class canoeUpdateWindow(QWidget):
#     def __init__(self):
#         super().__init__()
#
#         self.setWindowTitle("update")
#         self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.CustomizeWindowHint | Qt.WindowTitleHint)  # 置顶，隐藏关闭按钮等
#
#         # 使用QMovie加载GIF
#         self.movie_label = QLabel(self)
#         path = os.path.join(os.getcwd(),"res", "update.gif")
#         self.movie = QMovie(path)
#         self.movie_label.setMovie(self.movie)
#         self.movie.start()
#
#         # 获取GIF大小，并设置窗口大小
#         movie_size = self.movie.frameRect().size()
#         # 缩小一半的尺寸
#         new_width = movie_size.width() // 2
#         new_height = movie_size.height() // 2
#         new_size = QSize(new_width, new_height)
#         self.setFixedSize(new_size)
#
#         # 缩放GIF动画
#         self.movie_label.setScaledContents(True)
#         self.movie_label.setFixedSize(new_size)
#
import os
from PyQt5.QtWidgets import QLabel, QApplication, QWidget
from PyQt5.QtGui import QMovie, QPixmap
from PyQt5.QtCore import Qt, QSize, QObject

class canoeUpdateWindow(QWidget):
    def __init__(self):
        super().__init__()

        self.setWindowTitle("update")
        self.setWindowFlags(Qt.WindowStaysOnTopHint|Qt.FramelessWindowHint)  # 置顶，隐藏关闭按钮等

        # 使用QMovie加载GIF
        self.movie_label = QLabel(self)
        self.load_gif("update.gif")

    def load_gif(self, gif_name, loop=True): # Added loop parameter

        path = os.path.join(os.getcwd(), "res", gif_name)
        self.movie = QMovie(path)
        self.movie_label.setMovie(self.movie)


        if not loop:
            try:
                self.movie.finished.disconnect()  # Disconnect any existing connection
            except TypeError:
                pass
            self.movie.finished.connect(self.movie.stop) # Connect

        self.movie.start()

        movie_size = self.movie.frameRect().size()
        new_width = 200
        new_height = 200
        new_size = QSize(new_width, new_height)
        self.setFixedSize(new_size)

        self.movie_label.setScaledContents(True)
        self.movie_label.setFixedSize(new_size)

    def on_update_failed(self):
        self.setWindowFlags(Qt.WindowStaysOnTopHint)
        self.load_gif("failed.gif", loop=False) # Play only once
    def on_update_started(self):
        self.load_gif("update.gif")

# 示例用法
if __name__ == "__main__":
    import sys
    app = QApplication(sys.argv)
    window = canoeUpdateWindow()
    window.show()

    # 模拟升级失败
    window.on_update_failed()

    sys.exit(app.exec_())
