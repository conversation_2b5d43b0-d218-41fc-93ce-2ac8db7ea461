<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1600</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1600</width>
    <height>900</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2" stretch="2,1">
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="title">
      <string/>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <widget class="QTabWidget" name="tabWidget">
        <property name="currentIndex">
         <number>0</number>
        </property>
        <widget class="QWidget" name="tab_5">
         <attribute name="title">
          <string>上下电控制</string>
         </attribute>
         <layout class="QHBoxLayout" name="horizontalLayout_6">
          <item>
           <widget class="QGroupBox" name="groupBox_7">
            <property name="title">
             <string/>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_8">
             <item>
              <layout class="QFormLayout" name="formLayout_3">
               <item row="0" column="0">
                <widget class="QLabel" name="label_11">
                 <property name="text">
                  <string>电压(V)：</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QDoubleSpinBox" name="doubleSpinBox_1_volt">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="decimals">
                  <number>3</number>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QLabel" name="label_12">
                 <property name="text">
                  <string>上电时间(S)：</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QDoubleSpinBox" name="doubleSpinBox_1_on_time">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="maximum">
                  <double>9999.989999999999782</double>
                 </property>
                </widget>
               </item>
               <item row="2" column="0">
                <widget class="QLabel" name="label_13">
                 <property name="text">
                  <string>下电时间(S)：</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="1">
                <widget class="QDoubleSpinBox" name="doubleSpinBox_1_off_time">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="maximum">
                  <double>9999.989999999999782</double>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <widget class="QCheckBox" name="checkBox_1_random_time_enable">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>45</height>
                </size>
               </property>
               <property name="text">
                <string>是否使用随机时间</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_17">
               <property name="text">
                <string>上电时间范围(S)：</string>
               </property>
              </widget>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_4">
               <item>
                <widget class="QDoubleSpinBox" name="doubleSpinBox_1_on_time_s">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="maximum">
                  <double>9999.989999999999782</double>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="label_15">
                 <property name="text">
                  <string>~</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QDoubleSpinBox" name="doubleSpinBox_1_on_time_e">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="maximum">
                  <double>9999.989999999999782</double>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <widget class="QLabel" name="label_18">
               <property name="text">
                <string>下点时间范围(S)：</string>
               </property>
              </widget>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_5">
               <item>
                <widget class="QDoubleSpinBox" name="doubleSpinBox_1_off_time_s">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="maximum">
                  <double>9999.989999999999782</double>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="label_16">
                 <property name="text">
                  <string>~</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QDoubleSpinBox" name="doubleSpinBox_1_off_time_e">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="maximum">
                  <double>9999.989999999999782</double>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="verticalSpacer_3">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>66</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBox_8">
            <property name="title">
             <string/>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_9">
             <item>
              <widget class="QPushButton" name="pushButton_1_start">
               <property name="text">
                <string>开始</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_1_stop">
               <property name="text">
                <string>结束</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="verticalSpacer_4">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>197</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="tab_3">
         <attribute name="title">
          <string>随机电压</string>
         </attribute>
         <layout class="QHBoxLayout" name="horizontalLayout_9" stretch="1,1">
          <item>
           <widget class="QGroupBox" name="groupBox_11">
            <property name="title">
             <string/>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_12">
             <item>
              <widget class="QLabel" name="label_26">
               <property name="text">
                <string>电压范围(V)：</string>
               </property>
              </widget>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_8">
               <item>
                <widget class="QDoubleSpinBox" name="doubleSpinBox_3_volt_s">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="maximum">
                  <double>9999.989999999999782</double>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="label_27">
                 <property name="text">
                  <string>~</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QDoubleSpinBox" name="doubleSpinBox_3_volt_e">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="maximum">
                  <double>9999.989999999999782</double>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QFormLayout" name="formLayout_6">
               <item row="0" column="0">
                <widget class="QLabel" name="label_25">
                 <property name="text">
                  <string>间隔时间(S)：</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QDoubleSpinBox" name="doubleSpinBox_3_internal">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="maximum">
                  <double>9999.989999999999782</double>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <widget class="QCheckBox" name="checkBox_3_volt_list_enable">
               <property name="text">
                <string>启用电压列表</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QScrollArea" name="scrollArea_3_s">
               <property name="widgetResizable">
                <bool>true</bool>
               </property>
               <widget class="QWidget" name="scrollAreaWidgetContents_2">
                <property name="geometry">
                 <rect>
                  <x>0</x>
                  <y>0</y>
                  <width>744</width>
                  <height>362</height>
                 </rect>
                </property>
               </widget>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBox_12">
            <property name="title">
             <string/>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_13">
             <item>
              <widget class="QPushButton" name="pushButton_3_start">
               <property name="text">
                <string>开始</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_3_stop">
               <property name="text">
                <string>结束</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="verticalSpacer_7">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>197</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="tab_4">
         <attribute name="title">
          <string>高低压控制</string>
         </attribute>
         <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="1,1">
          <item>
           <widget class="QGroupBox" name="groupBox_9">
            <property name="title">
             <string/>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_10" stretch="1,1">
             <item>
              <layout class="QFormLayout" name="formLayout_4">
               <item row="0" column="0">
                <widget class="QLabel" name="label_19">
                 <property name="text">
                  <string>电压1(V)：</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QLabel" name="label_20">
                 <property name="text">
                  <string>电压2(V)：</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="0">
                <widget class="QLabel" name="label_21">
                 <property name="text">
                  <string>切换时间(S)：</string>
                 </property>
                </widget>
               </item>
               <item row="3" column="0">
                <widget class="QLabel" name="label_22">
                 <property name="text">
                  <string>循环次数：</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QDoubleSpinBox" name="doubleSpinBox_2_volt1">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="maximum">
                  <double>9999.989999999999782</double>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QDoubleSpinBox" name="doubleSpinBox_2_volt2">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="maximum">
                  <double>9999.989999999999782</double>
                 </property>
                </widget>
               </item>
               <item row="2" column="1">
                <widget class="QDoubleSpinBox" name="doubleSpinBox_2_interval">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="maximum">
                  <double>9999.989999999999782</double>
                 </property>
                </widget>
               </item>
               <item row="3" column="1">
                <widget class="QSpinBox" name="spinBox_2_repeat">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="maximum">
                  <number>9999</number>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="verticalSpacer_5">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>123</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBox_10">
            <property name="title">
             <string/>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_11" stretch="0,0,0,0">
             <item>
              <widget class="QPushButton" name="pushButton_2_start">
               <property name="text">
                <string>开始</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_2_stop">
               <property name="text">
                <string>结束</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="verticalSpacer_6">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>170</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <layout class="QFormLayout" name="formLayout_5">
               <item row="0" column="0">
                <widget class="QLabel" name="label_23">
                 <property name="text">
                  <string>剩余次数：</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QLineEdit" name="lineEdit_2_remain_repeat">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="tab_6">
         <attribute name="title">
          <string>步进电压控制</string>
         </attribute>
         <layout class="QHBoxLayout" name="horizontalLayout_10" stretch="1,1">
          <item>
           <widget class="QGroupBox" name="groupBox_13">
            <property name="title">
             <string/>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_14">
             <item>
              <layout class="QFormLayout" name="formLayout_7">
               <item row="0" column="0">
                <widget class="QLabel" name="label_14">
                 <property name="text">
                  <string>起始电压(V)：</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QDoubleSpinBox" name="doubleSpinBox_4_volt_s">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="decimals">
                  <number>3</number>
                 </property>
                 <property name="maximum">
                  <double>9999.989999999999782</double>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QLabel" name="label_24">
                 <property name="text">
                  <string>终点电压(V)：</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QDoubleSpinBox" name="doubleSpinBox_4_volt_e">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="decimals">
                  <number>3</number>
                 </property>
                 <property name="maximum">
                  <double>9999.989999999999782</double>
                 </property>
                </widget>
               </item>
               <item row="2" column="0">
                <widget class="QLabel" name="label_28">
                 <property name="text">
                  <string>步进值(V)：</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="1">
                <widget class="QDoubleSpinBox" name="doubleSpinBox_4_step">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="decimals">
                  <number>3</number>
                 </property>
                 <property name="maximum">
                  <double>9999.989999999999782</double>
                 </property>
                </widget>
               </item>
               <item row="3" column="0">
                <widget class="QLabel" name="label_29">
                 <property name="text">
                  <string>停留时间(S)：</string>
                 </property>
                </widget>
               </item>
               <item row="3" column="1">
                <widget class="QDoubleSpinBox" name="doubleSpinBox_4_time">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="decimals">
                  <number>3</number>
                 </property>
                 <property name="maximum">
                  <double>9999.989999999999782</double>
                 </property>
                </widget>
               </item>
               <item row="4" column="0">
                <widget class="QLabel" name="label_30">
                 <property name="text">
                  <string>循环次数：</string>
                 </property>
                </widget>
               </item>
               <item row="4" column="1">
                <widget class="QSpinBox" name="spinBox_4_repeat">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="maximum">
                  <number>9999999</number>
                 </property>
                </widget>
               </item>
               <item row="5" column="0">
                <widget class="QLabel" name="label_32">
                 <property name="text">
                  <string>模式：</string>
                 </property>
                </widget>
               </item>
               <item row="5" column="1">
                <widget class="QComboBox" name="comboBox_4_mode">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <item>
                  <property name="text">
                   <string>模式1</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>模式2</string>
                  </property>
                 </item>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="verticalSpacer_8">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>143</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBox_14">
            <property name="title">
             <string/>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_15">
             <item>
              <widget class="QPushButton" name="pushButton_4_start">
               <property name="text">
                <string>开始</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_4_stop">
               <property name="text">
                <string>结束</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="verticalSpacer_9">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>210</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <layout class="QFormLayout" name="formLayout_8">
               <item row="0" column="0">
                <widget class="QLabel" name="label_31">
                 <property name="text">
                  <string>剩余次数：</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QLineEdit" name="lineEdit_4_remain_repeat">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="tab_2">
         <attribute name="title">
          <string>Fixed模式</string>
         </attribute>
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <item>
           <widget class="QGroupBox" name="groupBox_5">
            <property name="title">
             <string/>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_6">
             <item>
              <layout class="QFormLayout" name="formLayout">
               <item row="2" column="0">
                <widget class="QLabel" name="label_8">
                 <property name="text">
                  <string>电流(A)：</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="1">
                <widget class="QDoubleSpinBox" name="doubleSpinBox_f_curr">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="decimals">
                  <number>3</number>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QLabel" name="label_9">
                 <property name="text">
                  <string>电压(V)：</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QDoubleSpinBox" name="doubleSpinBox_f_volt">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="decimals">
                  <number>3</number>
                 </property>
                </widget>
               </item>
               <item row="0" column="0">
                <widget class="QLabel" name="label_10">
                 <property name="text">
                  <string>输出模式：</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QComboBox" name="comboBox_f_mode">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <item>
                  <property name="text">
                   <string>VOLTage</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>CURRent</string>
                  </property>
                 </item>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBox_6">
            <property name="title">
             <string/>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_7">
             <item>
              <widget class="QPushButton" name="pushButton_f_exec">
               <property name="text">
                <string>执行</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="verticalSpacer_2">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>277</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_f_off">
               <property name="text">
                <string>停止输出</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_f_on">
               <property name="text">
                <string>开启输出</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="tab">
         <attribute name="title">
          <string>List模式</string>
         </attribute>
         <layout class="QHBoxLayout" name="horizontalLayout">
          <item>
           <widget class="QGroupBox" name="groupBox_2">
            <property name="title">
             <string/>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_3">
             <item>
              <layout class="QFormLayout" name="formLayout_2">
               <item row="0" column="0">
                <widget class="QLabel" name="label">
                 <property name="text">
                  <string>文件:</string>
                 </property>
                </widget>
               </item>
               <item row="3" column="0">
                <widget class="QLabel" name="label_2">
                 <property name="text">
                  <string>功能模式：</string>
                 </property>
                </widget>
               </item>
               <item row="4" column="0">
                <widget class="QLabel" name="label_3">
                 <property name="text">
                  <string>结束方式：</string>
                 </property>
                </widget>
               </item>
               <item row="5" column="0">
                <widget class="QLabel" name="label_4">
                 <property name="text">
                  <string>重复次数：</string>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QComboBox" name="comboBox_list_index">
                 <item>
                  <property name="text">
                   <string>1</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>2</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>3</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>4</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>5</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>6</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>7</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>8</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>9</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>10</string>
                  </property>
                 </item>
                </widget>
               </item>
               <item row="3" column="1">
                <widget class="QComboBox" name="comboBox_list_func">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <item>
                  <property name="text">
                   <string>VOLTage</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>CURRent</string>
                  </property>
                 </item>
                </widget>
               </item>
               <item row="4" column="1">
                <widget class="QComboBox" name="comboBox_list_term">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <item>
                  <property name="text">
                   <string>NORMal</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>LAST</string>
                  </property>
                 </item>
                </widget>
               </item>
               <item row="5" column="1">
                <widget class="QSpinBox" name="spinBox_list_repeat">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                 <property name="minimum">
                  <number>1</number>
                 </property>
                 <property name="maximum">
                  <number>65535</number>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QLabel" name="label_6">
                 <property name="text">
                  <string>电压(V)：</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="0">
                <widget class="QLabel" name="label_7">
                 <property name="text">
                  <string>电流(A)：</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QDoubleSpinBox" name="doubleSpinBox_volt">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                </widget>
               </item>
               <item row="2" column="1">
                <widget class="QDoubleSpinBox" name="doubleSpinBox_curr">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>45</height>
                  </size>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <widget class="QLabel" name="label_5">
               <property name="text">
                <string>步骤（1-100）：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QScrollArea" name="scrollArea">
               <property name="widgetResizable">
                <bool>true</bool>
               </property>
               <widget class="QWidget" name="scrollAreaWidgetContents">
                <property name="geometry">
                 <rect>
                  <x>0</x>
                  <y>0</y>
                  <width>1415</width>
                  <height>205</height>
                 </rect>
                </property>
               </widget>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBox_4">
            <property name="title">
             <string/>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_4">
             <item>
              <widget class="QPushButton" name="pushButton_save">
               <property name="text">
                <string>保存配置</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_save_exec">
               <property name="text">
                <string>保存并执行</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_exec">
               <property name="text">
                <string>不保存仅执行</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="verticalSpacer">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>161</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_trigger">
               <property name="text">
                <string>触发</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_pause">
               <property name="text">
                <string>暂停</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_pause_release">
               <property name="text">
                <string>解除暂停</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_stop">
               <property name="text">
                <string>停止</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_3">
     <property name="title">
      <string/>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_5">
      <item>
       <widget class="QPushButton" name="pushButton_connect_device">
        <property name="text">
         <string>连接设备</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPlainTextEdit" name="plainTextEdit"/>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton_clear">
          <property name="text">
           <string>清除</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
