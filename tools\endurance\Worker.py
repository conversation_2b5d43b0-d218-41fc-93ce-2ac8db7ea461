from enum import Enum

from PyQt5.QtCore import QThread


class WorkerState(Enum):

    IDLE = 0
    RUNNING = 1
    SUSPENDING = 2


class Worker:

    def __init__(self, interval=50):
        super(Worker, self).__init__()
        self.thread: QThread = None
        self.state = WorkerState.IDLE.value
        self.interval = interval

    def process(self):
        pass

    def finish(self):
        pass

    def run(self):
        while not self.is_idle():
            if self.is_running():
                self.process()
            self.thread.msleep(self.interval)

        self.finish()

    def start(self):
        """
        启动任务
        :return:
        """
        if self.is_idle():
            self.thread = WorkerThread(self)
            self.thread.start()
            self.state = WorkerState.RUNNING.value
        elif self.is_suspending():
            self.state = WorkerState.RUNNING.value

        print(f'Work State: {self.state}')

    def suspend(self):
        """
        暂停任务
        :return:
        """
        if self.is_running():
            self.state = WorkerState.SUSPENDING.value

    def stop(self):
        """
        停止任务
        :return:
        """
        if not self.is_idle():
            self.state = WorkerState.IDLE.value

    def is_running(self):
        return self.state == WorkerState.RUNNING.value

    def is_suspending(self):
        return self.state == WorkerState.SUSPENDING.value

    def is_idle(self):
        return self.state == WorkerState.IDLE.value


class WorkerThread(QThread):

    def __init__(self, worker: Worker):
        super().__init__()
        self.worker = worker

    def run(self) -> None:
        if self.worker is not None:
            self.worker.run()
