<config>
	<algo>
	<detect>
	        <angle>9.5</angle>
	        <dis>265</dis>


	       <b_debugflag>0</b_debugflag>
	       <TEMP_x>500</TEMP_x>
	       <TEMP_y>800</TEMP_y>
	       <TEMP_w>1000</TEMP_w>
	       <TEMP_h>1500</TEMP_h>

	        <check_x>1520</check_x>
	        <check_y>688</check_y>
	        <check_w>1600</check_w>
	        <check_h>1700</check_h>
                       <thresh_value>200</thresh_value>
                       <angle_begin1>0</angle_begin1>
                       <angle_end1>1</angle_end1>
                       <angle_begin2>0</angle_begin2>
                       <angle_end2>30</angle_end2>
                       <angle_step>3</angle_step>
                       <scale_begin>0.7</scale_begin>
                       <scale_end>1.3</scale_end>
                       <scale_step>0.1</scale_step>
                       <threshold>50</threshold>
                       <count>1</count>
	</detect>

	</algo>
</config>



