from threading import Lock

import serial

from common.LogUtils import logger


class LightSourceClient:

    def __init__(self):
        self.channel = None
        self.port = None
        self.baudrate = None
        self.bytesize = None
        self.parity = None
        self.stopbits = None
        self.timeout = None
        self._is_open = False
        self._serial = None
        self.channel = 1
        self.lock = Lock()

    def is_open(self):
        return self._is_open

    def open(self, port, baudrate=9600, channel=1, bytesize=8, parity="N", stopbits=1, timeout=1.0):
        logger.info(f'open port={port}')
        self.port = port
        self.baudrate = baudrate
        self.channel = channel
        self.bytesize = bytesize
        self.parity = parity
        self.stopbits = stopbits
        self.timeout = timeout

        try:
            if not self._is_open:
                if self._serial:
                    self._serial.close()
                self._serial = serial.Serial(
                    port=self.port, baudrate=self.baudrate, bytesize=self.bytesize, parity=self.parity,
                    stopbits=self.stopbits, timeout=self.timeout, write_timeout=self.timeout
                )
                self._is_open = True
        except Exception as e:
            logger.info(f"open exception: {str(e.args)}")
            self._is_open = False

        return self._is_open

    def close(self):
        if self._is_open:
            if self._serial:
                self._serial.close()
                self._serial = None
                self._is_open = False
        return True

    def execute(self, command, channel, data=0):
        logger.info(f"execute command={command}, channel={channel}, data={data}")
        if self._serial is None:
            return

        with self.lock:
            s = "#" + str(command) + str(channel) + "{:03x}".format(data)

            checksum = 0
            for c in s:
                checksum ^= ord(c)

            s += "{:02x}".format(checksum)
            s = s.encode()

            try:
                self._serial.flush()
                self._serial.write(s)

                if command in [1, 2, 3]:
                    r = self._serial.read(1)
                    if r == b"#":
                        return True
                    else:
                        return None
                elif command in [4]:
                    r = self._serial.read(8)
                    if len(r) < 8:
                        return None
                    d = r[3:6]
                    d = int(d, base=16)
                    return d
            except Exception as e:
                logger.error("execute exception: {}".format(str(e.args)))
                return None

    def set_light_intensity(self, channel=1, i=0):
        logger.info(f"set_light_intensity channel={channel}, i={i}")
        r = self.execute(command=3, channel=channel, data=i)
        return r

    def get_light_intensity(self, channel=1):
        r = self.execute(command=4, channel=channel)
        return r

    def set_channels_light_intensity(self, i):
        logger.info(f"set_channels_light_intensity i={i}")
        for channel in range(1, 5):
            r = self.execute(command=3, channel=channel, data=i)
            logger.info(f"set_channels_light_intensity r={r}")


light_source_client: LightSourceClient = LightSourceClient()
