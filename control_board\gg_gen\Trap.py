import ctypes

from .Base import gts


class TTrapPrm(ctypes.Structure):
    _fields_ = [("acc", ctypes.c_double), ("dec", ctypes.c_double), ("velStart", ctypes.c_double),
                ("smoothTime", ctypes.c_short)]


if gts is not None:
    gts.GTN_PrfTrap.argtypes = [ctypes.c_short, ctypes.c_short]
    gts.GTN_PrfTrap.restype = ctypes.c_short


def GTN_PrfTrap(core, profile):
    return gts.GTN_PrfTrap(core, profile)


if gts is not None:
    gts.GTN_SetTrapPrm.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.POINTER(TTrapPrm)]
    gts.GTN_SetTrapPrm.restype = ctypes.c_short


def GTN_SetTrapPrm(core, profile, acc=0, dec=0, velStart=0, smoothTime=0):
    prm = TTrapPrm(acc, dec, velStart, smoothTime)
    return gts.GTN_SetTrapPrm(core, profile, ctypes.byref(prm))


if gts is not None:
    gts.GTN_GetTrapPrm.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.POINTER(TTrapPrm)]
    gts.GTN_GetTrapPrm.restype = ctypes.c_short


def GTN_GetTrapPrm(core, profile):
    prm = TTrapPrm()
    r = gts.GTN_GetTrapPrm(core, profile, ctypes.byref(prm))
    return r, prm.acc, prm.dec, prm.velStart, prm.smmothTime


if gts is not None:
    gts.GTN_SetPos.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_long]
    gts.GTN_SetPos.restype = ctypes.c_short


def GTN_SetPos(core, profile, pos):
    r = gts.GTN_SetPos(core, profile, pos)
    return r


if gts is not None:
    gts.GTN_GetPos.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.POINTER(ctypes.c_long)]
    gts.GTN_GetPos.restype = ctypes.c_short


def GTN_GetPos(core, profile):
    pos = ctypes.c_long(0)
    r = gts.GTN_GetPos(core, profile, ctypes.byref(pos))
    return r, pos.value
