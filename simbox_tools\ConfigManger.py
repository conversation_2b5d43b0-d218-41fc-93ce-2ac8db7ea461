"""
user: Created by jid on 2019/4/22.
email: <EMAIL>
description:配置文件
"""
import json
import os

from PyQt5.QtGui import QColor
from configobj import ConfigObj

from simbox_tools.Logger import Logger

Left, Top, Right, Bottom, LeftTop, RightTop, LeftBottom, RightBottom = range(8)

WORK_FOLDER = os.path.join(os.path.expanduser('~'), "mate_nomi1.5")
CONFIG_FILE = os.path.join(WORK_FOLDER, "config.ini")
EXCEPTION_LOG_FOLDER = os.path.join(WORK_FOLDER, "AppException")
COMBINE_VIDEO_FOLDER = os.path.join(WORK_FOLDER, "CombineVideo")
JSON_FILE_FOLDER = os.path.join(WORK_FOLDER, "JsonFile")


class NomiConfig:
    MainWindow = 1
    SubWindow = 2
    MotorLowSpeedWarning = "The motor runs at low speed for a long time,this design is not recommend!"
    MotorValidParamsWarning = "Please enter the full valid parameters"
    SliderScaleWidth = 10
    DefaultBrightnessPercent = 0
    DefaultRGB = QColor(255, 255, 255)
    EditorTypeEmoji = "emoji"
    EditorTypePitch = "pitch"
    EditorTypeYaw = "yaw"
    DialogBtnSave = "save"
    DialogBtnUnsave = "unsave"
    DialogBtnConfirm = "confirm"
    DialogBtnCancel = "cancel"
    DialogBtnExit = "exit"
    TitleBarMin = "min"
    TitleBarMax = "max"
    TitleBarClose = "close"
    KineticMotionTypeList = ['Go', 'Go to', 'Cycle', 'Delay']
    KineticMotionTypeGo = "Go"
    KineticMotionTypeGoTo = "Go to"
    KineticMotionTypeCycle = "Cycle"
    KineticMotionTypeDelay = "Delay"
    KineticDefineTypeDuration = "duration"
    KineticDefineTypeSpeed = "speed"
    EmojiDefineTypeDuration = "duration"
    EmojiDefineTypeSpeed = "speed"
    KineticIndPrecision = 100
    LabelCommandAddElement = "Add element"
    LabelCommandEditElement = "Edit element"
    LabelCommandCreate = "CreateLabel"
    LabelCommandEdit = "EditLabel"
    LabelCommandAddBegin = "Add begin"
    LabelCommandAddEnd = "Add to end"
    LabelCommandAddInFront = "Add in front"
    LabelCommandAddBehind = "Add behind"
    KineticLoopFalse = 0
    KineticLoopTrue = 1
    CalibrateInvalid = 3
    CalibrateReserved = 2
    CalibrateActive = 1
    CalibrateNotActive = 0
    CalibrateNotActivated = 0
    CalibrateActivated = 1
    CANDisconnected = "Disconnected"
    CANConnecting = "Connecting"
    CANConnected = "Connected"
    AnimationInit = 0
    AnimationStart = 1
    AnimationPlaying = 2
    AnimationPausing = 3
    AnimationEnd = 4
    RunModeSingle = 0
    RunModeCycle = 1
    RunModeAtoB = 2
    CalibrateActionConnect = "Connect"
    CalibrateActionDisconnect = "Disconnect"
    CalibrateActionRefresh = "Refresh"
    CalibrateActionCalibrate = "Calibrate"
    KineticPitchId = 0x12
    KineticYawId = 0x11
    KineticRotateStateNotActivated = 0
    KineticRotateStateActivated = 1
    KineticRotateStateNotCompleted = 2
    PathNotExists = "PathNotExists"
    CalibrateNormal = 0
    CalibrateError = 1


class ReDoUnDo:
    ReDoType = "redo_type"
    ReDoData = "redo_data"
    UnDoType = "undo_type"
    UnDoData = "undo_data"

    class Type:
        TARGET_POSITION_CHANGED = "target_position_changed"
        MOVE_RANGE_CHANGED = "move_range_changed"
        DELAY_CHANGED = "delay_changed"
        DURATION_CHANGED = "duration_changed"
        SPEED_CHANGED = "speed_changed"
        IND_IN_CHANGED = "ind_in_changed"
        IND_OUT_CHANGED = "ind_out_changed"
        RENAME = "rename"
        REVERSE = "reverse"
        BRIGHTNESS = "brightness"

    class Key:
        RENAME_KINETIC_NAME = "kinetic_name"
        REVERSE_CROP_FROM = "crop_from"
        REVERSE_CROP_TO = "crop_to"
        RENAME_EMOJI_NAME = "emoji_name"


class StyleSheet:
    minBtnStyle = "QPushButton{border-image:url(images/min_normal.png)}" \
                  "QPushButton:hover{border-image:url(images/min_hover.png)}" \
                  "QPushButton:pressed{border-image:url(images/min_hover.png)}"
    maxBtnStyle = "QPushButton{border-image:url(images/max_normal.png)}" \
                  "QPushButton:hover{border-image:url(images/max_hover.png)}" \
                  "QPushButton:pressed{border-image:url(images/max_hover.png)}"
    closeBtnStyle = "QPushButton{border-image:url(images/close_normal.png)}" \
                    "QPushButton:hover{border-image:url(images/close_hover.png)}" \
                    "QPushButton:pressed{border-image:url(images/close_hover.png)}"
    titleBarStyle = "background-color:#565656"

    QMenuBarStyleSheet = "QMenuBar{color:#FFFFFF;font-size:25px}" \
                         "QMenuBar:item{padding:8px 30px}" \
                         "QMenuBar:item:selected {background:#6A6A6A;padding:10px}" \
                         "QMenuBar:item:disabled {color:#505050}"

    QMenuStyleSheet = "QMenu{color:white;background:#7F7F7F}\
         QMenu:item{font-size:15px;background:#7F7F7F;padding:10px 20px;}\
         QMenu:item:selected {background-color:#6A6A6A}\
         QMenu:item:disabled {color:#505050}"

    RQSliderStyleSheet = "QSlider:groove:horizontal {background:#C0C0C0;height:6px}" \
                         "QSlider:handle:horizontal {border-image:url(images/slider.png);width:25px;margin:-25px -1px}" \
                         "QSlider:sub-page:horizontal {background:#FF0000;height:6px}"

    GQSliderStyleSheet = "QSlider:groove:horizontal {background:#C0C0C0;height:6px}" \
                         "QSlider:handle:horizontal {border-image:url(images/slider.png);width:25px;margin:-25px -1px}" \
                         "QSlider:sub-page:horizontal {background:#00FF00;height:6px}"

    BQSliderStyleSheet = "QSlider:groove:horizontal {background:#C0C0C0;height:6px}" \
                         "QSlider:handle:horizontal {border-image:url(images/slider.png);width:25px;margin:-25px -1px}" \
                         "QSlider:sub-page:horizontal {background:#0000FF;height:6px}"

    BrightnessSliderStyleSheet = "QSlider:groove:horizontal {background:#C0C0C0;height:6px}" \
                                 "QSlider:handle:horizontal {border-image:url(images/slider.png);width:25px;margin:-25px -1px}" \
                                 "QSlider:sub-page:horizontal {background:#C0C0C0;height:6px}"

    EmojiSliderStyleSheet = "QSlider:handle:horizontal {border-image:url(images/max_normal.png);width:25px;padding:20px}"

    EmojiLabelStyleSheet = "background-color:#7F7F7F;color:#E1E1E1;border-radius:5px;border-width:2px;border-style" \
                           ":solid;border-color:#A9A9A9;font-size:25px "
    CoordinateLabelStyleSheet = "background-color:#00000000;color:#FFFFFF;font-family:微软雅黑;font-size:14px"
    CombineProgressStyleSheet = "font-family:微软雅黑;font-size:16px"


class AppConfig(object):
    _log_tag = "AppConfig"
    # 电机低速运行的总时间
    _low_speed_tolerance = 200
    _current_low_speed_tolerance = 0
    # 等间隔时间采样,单位ms
    _time_sample_step = 2

    # 底部电机(base motor)最小步长
    _base_motor_step = 0.0625
    # 底部电机最大转速，单位degree/ms
    _base_motor_max_velocity = 0.0625
    # 底部电机最小转速，单位degree/ms
    _base_motor_min_velocity = 0.0125

    # 头部电机(head motor)最小步长
    _head_motor_step = 0.125
    # 头部电机最大转速，单位degree/ms
    _head_motor_max_velocity = 0.125
    # 头部电机最小转速
    _head_motor_min_velocity = 0.025
    # mate真实输出帧率
    _output_fps = 25
    # mate校准后Pitch的角度值
    _calibrate_pitch_degree = 30
    # mate校准后Yaw的角度值
    _calibrate_yaw_degree = 0
    # mate校准超时时间(单位ms)
    _calibrate_timeout = 15000
    # mate电机pitch方向上的最小转动角度
    _kinetic_pitch_min_degree = 0
    # mate电机pitch方向上的最大转动角度
    _kinetic_pitch_max_degree = 45
    # mate电机yaw方向上的最小转动角度
    _kinetic_yaw_min_degree = -50
    # mate电机yaw方向上的最大转动角度
    _kinetic_yaw_max_degree = 50
    _project_file_path = None
    config = None

    def load_global_config(self):
        if not os.path.exists(WORK_FOLDER):
            os.makedirs(WORK_FOLDER)
        if not os.path.exists(CONFIG_FILE):
            self.init_config()
        if not os.path.exists(EXCEPTION_LOG_FOLDER):
            os.makedirs(EXCEPTION_LOG_FOLDER)
        if not os.path.exists(COMBINE_VIDEO_FOLDER):
            os.makedirs(COMBINE_VIDEO_FOLDER)
        if not os.path.exists(JSON_FILE_FOLDER):
            os.makedirs(JSON_FILE_FOLDER)

        # configData.create_project_folder()

    def init_config(self):
        Logger.console(self._log_tag, "init_config")
        self.config = ConfigObj(CONFIG_FILE, encoding='UTF8')
        self.config['config'] = {}
        self.config['config']['time_sample_step'] = self._time_sample_step
        self.config['config']['low_speed_tolerance'] = self._low_speed_tolerance
        self.config['config']['current_low_speed_tolerance'] = self._current_low_speed_tolerance
        self.config['config']['base_motor_step'] = self._base_motor_step
        self.config['config']['base_motor_max_velocity'] = self._base_motor_max_velocity
        self.config['config']['base_motor_min_velocity'] = self._base_motor_min_velocity
        self.config['config']['head_motor_step'] = self._head_motor_step
        self.config['config']['head_motor_max_velocity'] = self._head_motor_max_velocity
        self.config['config']['head_motor_min_velocity'] = self._head_motor_min_velocity
        self.config['config']['output_fps'] = self._output_fps
        self.config['config']['calibrate_pitch_degree'] = self._calibrate_pitch_degree
        self.config['config']['calibrate_yaw_degree'] = self._calibrate_yaw_degree
        self.config['config']['calibrate_timeout'] = self._calibrate_timeout
        self.config['config']['kinetic_pitch_min_degree'] = self._kinetic_pitch_min_degree
        self.config['config']['kinetic_pitch_max_degree'] = self._kinetic_pitch_max_degree
        self.config['config']['kinetic_yaw_min_degree'] = self._kinetic_yaw_min_degree
        self.config['config']['kinetic_yaw_max_degree'] = self._kinetic_yaw_max_degree
        self.config.write()

    def update_current_low_speed_tolerance(self, tolerance):
        Logger.console(self._log_tag, "update_current_low_speed_tolerance -> %s" % tolerance)
        self.config['config']['current_low_speed_tolerance'] = tolerance
        self.config.write()

    def get_time_sample_step(self):
        self.config = ConfigObj(CONFIG_FILE, encoding='UTF8')
        if 'time_sample_step' in self.config['config']:
            self._time_sample_step = self.config['config']['time_sample_step']

        return self._time_sample_step

    def get_low_speed_tolerance(self):
        self.config = ConfigObj(CONFIG_FILE, encoding='UTF8')
        if 'tolerance' in self.config['config']:
            self._low_speed_tolerance = self.config['config']['low_speed_tolerance']

        return self._low_speed_tolerance

    def get_base_motor_step(self):
        self.config = ConfigObj(CONFIG_FILE, encoding='UTF8')
        if 'base_motor_step' in self.config['config']:
            self._base_motor_step = self.config['config']['base_motor_step']

        return self._base_motor_step

    def get_base_motor_max_velocity(self):
        self.config = ConfigObj(CONFIG_FILE, encoding='UTF8')
        if 'base_motor_max_velocity' in self.config['config']:
            self._base_motor_max_velocity = self.config['config']['base_motor_max_velocity']

        return self._base_motor_max_velocity

    def get_base_motor_min_velocity(self):
        self.config = ConfigObj(CONFIG_FILE, encoding='UTF8')
        if 'base_motor_min_velocity' in self.config['config']:
            self._base_motor_min_velocity = self.config['config']['base_motor_min_velocity']

        return self._base_motor_min_velocity

    def get_head_motor_step(self):
        self.config = ConfigObj(CONFIG_FILE, encoding='UTF8')
        if 'head_motor_step' in self.config['config']:
            self._head_motor_step = self.config['config']['head_motor_step']

        return self._head_motor_step

    def get_head_motor_max_velocity(self):
        self.config = ConfigObj(CONFIG_FILE, encoding='UTF8')
        if 'head_motor_max_velocity' in self.config['config']:
            self._head_motor_max_velocity = self.config['config']['head_motor_max_velocity']

        return self._head_motor_max_velocity

    def get_head_motor_min_velocity(self):
        self.config = ConfigObj(CONFIG_FILE, encoding='UTF8')
        if 'head_motor_min_velocity' in self.config['config']:
            self._head_motor_min_velocity = self.config['config']['head_motor_min_velocity']

        return self._head_motor_min_velocity

    def get_output_fps(self):
        self.config = ConfigObj(CONFIG_FILE, encoding='UTF8')
        if 'output_fps' in self.config['config']:
            self._output_fps = self.config['config']['output_fps']

        return self._output_fps

    def get_calibrate_pitch_degree(self):
        self.config = ConfigObj(CONFIG_FILE, encoding='UTF8')
        if 'calibrate_pitch_degree' in self.config['config']:
            self._calibrate_pitch_degree = self.config['config']['calibrate_pitch_degree']

        return self._calibrate_pitch_degree

    def get_calibrate_yaw_degree(self):
        self.config = ConfigObj(CONFIG_FILE, encoding='UTF8')
        if 'calibrate_yaw_degree' in self.config['config']:
            self._calibrate_yaw_degree = self.config['config']['calibrate_yaw_degree']

        return self._calibrate_yaw_degree

    def get_calibrate_timeout(self):
        self.config = ConfigObj(CONFIG_FILE, encoding='UTF8')
        if 'calibrate_timeout' in self.config['config']:
            self._calibrate_timeout = self.config['config']['calibrate_timeout']

        return self._calibrate_timeout

    def get_kinetic_pitch_min_degree(self):
        self.config = ConfigObj(CONFIG_FILE, encoding='UTF8')
        if 'kinetic_pitch_min_degree' in self.config['config']:
            self._kinetic_pitch_min_degree = self.config['config']['kinetic_pitch_min_degree']

        return self._kinetic_pitch_min_degree

    def get_kinetic_pitch_max_degree(self):
        self.config = ConfigObj(CONFIG_FILE, encoding='UTF8')
        if 'kinetic_pitch_max_degree' in self.config['config']:
            self._kinetic_pitch_max_degree = self.config['config']['kinetic_pitch_max_degree']

        return self._kinetic_pitch_max_degree

    def get_kinetic_yaw_min_degree(self):
        self.config = ConfigObj(CONFIG_FILE, encoding='UTF8')
        if 'kinetic_yaw_min_degree' in self.config['config']:
            self._kinetic_yaw_min_degree = self.config['config']['kinetic_yaw_min_degree']

        return self._kinetic_yaw_min_degree

    def get_kinetic_yaw_max_degree(self):
        self.config = ConfigObj(CONFIG_FILE, encoding='UTF8')
        if 'kinetic_yaw_max_degree' in self.config['config']:
            self._kinetic_yaw_max_degree = self.config['config']['kinetic_yaw_max_degree']

        return self._kinetic_yaw_max_degree

    def set_project_file_path(self, project_file_path):
        self._project_file_path = project_file_path

    def get_project_file_path(self):
        return self._project_file_path

    def get_project_file(self):
        project_file_path = self.get_project_file_path()
        if project_file_path is None:
            return

        with open(project_file_path, 'r', encoding='UTF-8') as f:
            project_data = json.load(f)

        return project_data


class EnglishInformation(object):
    main_information = 'Nomi'
    project_name = 'project name'
    input_project_name = 'Enter the project name'
    save_information = "You have a file that hasn't been saved yet,Whether to save?"
    warning = 'warning'
    confirm_exit = 'Confirm exit?'
    check_file_name = 'The project name already exists. Please re-enter it.'
    set_work_space = "You haven't set up the working directory yet. Do you need to set it now?"
    need_save_file = "Please enter the name of the file to be saved"
    work_space = 'Please select a workspace folder'
    select_folder = 'Please select a folder'
    export_name = "Please enter the name of the file you want to export"
    select_element = "Please select a element"
    paste_element = 'Different types of elements cannot be pasted'
    project_correct = 'The format of engineering documents is not correct.'
    frame_information = 'frames cannot be zero'
    export_success = 'Export success'
    is_equal = 'Emoji, pitch and yaw must be equal'
    value_type_incorrect = "The type of value you entered is incorrect. Please re-enter the correct number."
    value_incorrect = "The value you entered is incorrect. Please re-enter it."
    open_file_incorrect = "File content is incomplete, please complete images content"


appConfig = AppConfig()
