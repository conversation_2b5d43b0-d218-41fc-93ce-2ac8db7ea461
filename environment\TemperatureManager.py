import time

from adb.AdbConnectDevice import adb_connect_device
from case.VdsDetectManager import vds_detect_manager
from common.LogUtils import logger


class TemperatureManager:

    def __init__(self):
        super().__init__()

    @staticmethod
    def get_screen_temp(project_number, timeout=3):
        temp = 0
        if project_number in ["RESCN08", "ICSCN27"]:
            adb_connect_device.read_screen_temp()
            start_time = time.time()
            while time.time() - start_time < timeout:
                if vds_detect_manager.resp_screen_temp is not None:
                    temp = int(vds_detect_manager.resp_screen_temp)
                    vds_detect_manager.resp_screen_temp = None
                    break
            return temp
        elif project_number in ["ICSCN25"]:
            can_dict = {
                "id": "0x636",
                "msg": "03 22 39 04 00 00 00 00",
                "can_type": "",
                "cycle_period": "0",
                "uds": "",
                "recv_id": "",
                "expect": "",
            }
            from case.StepManager import step_manager
            msg = step_manager.set_message(can_dict)
            ret = adb_connect_device.can_send_and_read_msg(msg=msg, register_id="0x6b6", timeout=timeout)
            logger.info(f"get_screen_temp ret={ret}")
            if ret is not None and ret.__contains__(" "):
                # ICS3.0的屏幕温度为协议数据中的第7位数据，将16进制数据转换成10进制之后再减去40的偏移量就是实际的屏幕温度
                temp = ret.split(" ")[6]
                temp = int(f"0x{temp}", 16) - 40

        return temp


temperature_manager: TemperatureManager = TemperatureManager()
