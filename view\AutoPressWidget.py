from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QWidget

from touch.Worker import Worker
from ui.AutoPressDlg import Ui_Dialog


class AutoPressWidget(QDialog, Ui_Dialog):

    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setupUi(self)
        self.setWindowFlags(Qt.CustomizeWindowHint | Qt.WindowCloseButtonHint)
        self.setWindowFlags(Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)
        self.setWindowTitle("自动按键工具")
        self.doubleSpinBox_position.setValue(-9)
        self.doubleSpinBox_press.setValue(1)
        self.doubleSpinBox_up.setValue(1)
        self.progressBar.setValue(0)
        self.lineEdit_status.setText("停止中")
        self.lineEdit_press.setText("0")
        self.worker = Worker()
        self.repeat = 0
        self.pushButton_start.clicked.connect(self.on_start)
        self.pushButton_end.clicked.connect(self.on_stop)
        self.pushButton_pause.clicked.connect(self.on_pause)
        self.worker.progress_signal.connect(self.progressBar.setValue)
        self.worker.worker_status_signal.connect(self.on_worker_status_changed)
        self.worker.remain_cycles_signal.connect(self.on_remain_cycles_changed)

    def on_start(self):
        position = self.doubleSpinBox_position.value()
        press_time = self.doubleSpinBox_press.value()
        up_time = self.doubleSpinBox_up.value()
        repeat = int(self.spinBox_repeat.value())
        self.repeat = repeat

        self.worker.start(position, press_time, up_time, repeat)

    def on_stop(self):
        self.worker.stop()

    def on_pause(self):
        self.worker.pause()
        self.on_worker_status_changed(2)

    def on_worker_status_changed(self, status):
        if status == 0:
            self.lineEdit_status.setText("停止中")
        elif status == 1:
            self.lineEdit_status.setText("工作中")
        elif status == 2:
            self.lineEdit_status.setText("暂停中")

    def on_remain_cycles_changed(self, press_counter):
        self.lineEdit_press.setText(str(press_counter))

    def update_press_counter(self, press_counter):
        self.lineEdit_press.setText(str(press_counter))
        self.progressBar.setValue(int(press_counter / self.repeat * 100))


