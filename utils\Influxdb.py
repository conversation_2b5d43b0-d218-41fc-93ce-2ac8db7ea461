import datetime
import json
from dateutil.parser import isoparse
from influxdb_client import Point
from influxdb_client.client import influxdb_client
from influxdb_client.client.write_api import SYNCHRONOUS

from common.LogUtils import logger


class InfluxDBClient:
    """
    InfluxDB客户端类，封装InfluxDB的连接和操作功能
    """

    def __init__(self, url, token, org, default_bucket=None):
        """
        初始化InfluxDB客户端

        Args:
            url (str): InfluxDB服务器URL
            token (str): 认证令牌
            org (str): 组织名称
            default_bucket (str, optional): 默认的存储桶名称
        """
        self.url = url
        self.token = token
        self.org = org
        self.default_bucket = default_bucket

        # 创建客户端连接
        self.client = influxdb_client.InfluxDBClient(url=url, token=token, org=org)

        # 初始化写入和查询API
        self.write_api = self.client.write_api(write_options=SYNCHRONOUS)
        self.query_api = self.client.query_api()

        logger.info(f"InfluxDB client initialized with URL: {url}, org: {org}")

    def write_data(self, table, tag, data, bucket=None):
        """
        写入数据到InfluxDB

        Args:
            table (str): 表名(measurement)
            tag (dict): 标签，格式为 {'key': key_name, 'value': tag_value}
            data (dict): 数据，格式为 {'key': field_name, 'value': field_value}
            bucket (str, optional): 桶名称，如不提供则使用默认桶

        Returns:
            bool: 写入成功返回True，否则返回False
        """
        try:
            # 使用默认桶或指定的桶
            target_bucket = bucket if bucket else self.default_bucket
            if not target_bucket:
                raise ValueError("Bucket name is required")

            # 创建数据点
            point = (
                Point(table)
                .tag(tag['key'], tag['value'])
                .field(data['key'], data['value'])
            )

            # 写入数据
            self.write_api.write(bucket=target_bucket, org=self.org, record=point)
            logger.debug(f"Data written to {target_bucket}.{table}")
            return True

        except Exception as e:
            logger.error(f"Failed to write data: {e}")
            return False

    def write_data_multi(self, table, tags, fields, bucket=None, timestamp=None):
        """
        写入带有多个标签和字段的数据

        Args:
            table (str): 表名(measurement)
            tags (dict): 多个标签键值对
            fields (dict): 多个字段键值对
            bucket (str, optional): 桶名称，如不提供则使用默认桶
            timestamp (datetime, optional): 时间戳，如不提供则使用当前时间

        Returns:
            bool: 写入成功返回True，否则返回False
        """
        try:
            # 使用默认桶或指定的桶
            target_bucket = bucket if bucket else self.default_bucket
            if not target_bucket:
                raise ValueError("Bucket name is required")
            
            # 创建数据点
            point = Point(table)

            # 添加所有标签
            for tag_key, tag_value in tags.items():
                point = point.tag(tag_key, tag_value)

            # 添加所有字段
            for field_key, field_value in fields.items():
                point = point.field(field_key, field_value)

            # 设置时间戳(可选)
            if timestamp:
                point = point.time(timestamp)

            # 写入数据
            self.write_api.write(bucket=target_bucket, org=self.org, record=point)
            logger.debug(f"Multi data written to {target_bucket}.{table}")
            return True

        except Exception as e:
            logger.error(f"Failed to write multi data: {e}")
            return False

    def query(self, bucket, measurement, device, field, start="-10m"):
        """
        查询函数，返回单个值

        Args:
            bucket (str): 桶名称
            measurement (str): 表名
            device (str): 设备名
            field (str): 字段名
            start (str): 起始时间范围，默认"-10m"(10分钟前)

        Returns:
            Any: 查询结果的值，如果出错则返回None
        """
        logger.info(f"Querying - bucket: {bucket}, measurement: {measurement}, device: {device}, field: {field}")
        try:
            result = self.query_api.query(
                f"""from(bucket:"{bucket}")
                        |> range(start: {start})
                        |> filter(fn: (r) => r["_measurement"] == "{measurement}" and r["_field"] == "{field}")
                        |> filter(fn: (r) => r["device"] == "{device}")
                    """)
            output = json.loads(result.to_json())
            return output[-1]["_value"] if output else None
        except Exception as e:
            logger.error(f"Query failed: {e}")
            return None

    def query_latest(self, bucket, measurement, device, field=None, start='-10m', stop=None):
        """
        查询最新数据

        Args:
            bucket (str): 桶名称
            measurement (str): 表名
            device (str): 设备名
            field (str, optional): 字段名，如果为None则查询所有字段
            start (str): 起始时间范围，默认"-10m"(10分钟前)
            stop (str, optional): 结束时间范围

        Returns:
            dict: 查询结果，格式为 {'device': device_name, 'data': [...]}
        """
        logger.debug(f"Query latest - bucket: {bucket}, measurement: {measurement}, device: {device}, field: {field}")
        try:
            # 构建查询语句
            query_sql = self._build_query_sql(bucket, measurement, device, field, start, stop, "last()")

            # 执行查询
            result = self.query_api.query(query_sql, org=self.org)
            output = json.loads(result.to_json())

            # 处理结果
            data = {'device': device, 'data': []}
            for item in output:
                data['data'].append({
                    item['_field']: item['_value'],
                    'time': self.time_format_conversion(item['_time'])
                })
            return data
        except Exception as e:
            logger.error(f"Query latest failed: {e}")
            return {'device': device, 'data': []}

    def query_first(self, bucket, measurement, device, field=None, start='-10m', stop=None):
        """
        查询时间范围内的第一条数据

        Args:
            bucket (str): 桶名称
            measurement (str): 表名
            device (str): 设备名
            field (str, optional): 字段名，如果为None则查询所有字段
            start (str): 起始时间范围，默认"-10m"(10分钟前)
            stop (str, optional): 结束时间范围

        Returns:
            dict: 查询结果，格式为 {'device': device_name, 'data': [...]}
        """
        logger.info(f"Query first - bucket: {bucket}, measurement: {measurement}, device: {device}, field: {field}")
        try:
            # 构建查询语句
            query_sql = self._build_query_sql(bucket, measurement, device, field, start, stop, "first()")

            # 执行查询
            result = self.query_api.query(query_sql, org=self.org)
            output = json.loads(result.to_json())

            # 处理结果
            data = {'device': device, 'data': []}
            for item in output:
                data['data'].append({
                    item['_field']: item['_value'],
                    'time': self.time_format_conversion(item['_time'])
                })
            return data
        except Exception as e:
            logger.error(f"Query first failed: {e}")
            return {'device': device, 'data': []}

    def query_duration(self, bucket, measurement, device, field=None, start='-10m', stop=None):
        """
        按照时间段查询数据

        Args:
            bucket (str): 桶名称
            measurement (str): 表名
            device (str): 设备名
            field (str, optional): 字段名，如果为None则查询所有字段
            start (str): 起始时间范围，默认"-10m"(10分钟前)
            stop (str, optional): 结束时间范围

        Returns:
            dict: 查询结果，格式为 {'device': device_name, 'data': {field1: [{value, time}, ...], ...}}
        """
        logger.info(f"Query duration - bucket: {bucket}, measurement: {measurement}, device: {device}, field: {field}")
        try:
            # 构建查询语句
            query_sql = self._build_query_sql(bucket, measurement, device, field, start, stop)

            # 执行查询
            result = self.query_api.query(query_sql, org=self.org)
            output = json.loads(result.to_json())

            # 处理结果
            data = {'device': device, 'data': {}}
            for item in output:
                _field = item['_field']
                if _field not in data['data']:
                    data['data'][_field] = [{
                        'value': item['_value'],
                        'time': self.time_format_conversion(item['_time'])
                    }]
                else:
                    data['data'][_field].append({
                        'value': item['_value'],
                        'time': self.time_format_conversion(item['_time'])
                    })
            return data
        except Exception as e:
            logger.error(f"Query duration failed: {e}")
            return {'device': device, 'data': {}}

    def _build_query_sql(self, bucket, measurement, device, field=None, start='-10m', stop=None, operation=None):
        """
        构建Flux查询语句

        Args:
            bucket (str): 桶名称
            measurement (str): 表名
            device (str): 设备名
            field (str, optional): 字段名，如果为None则查询所有字段
            start (str): 起始时间范围
            stop (str, optional): 结束时间范围
            operation (str, optional): 操作，如"first()"或"last()"

        Returns:
            str: Flux查询语句
        """
        # 构建range部分
        range_part = f"|> range(start: {start})" if stop is None else f"|> range(start: {start}, stop: {stop})"

        # 构建field过滤部分
        field_filter = f' and r._field == "{field}"' if field is not None else ""

        # 构建操作部分
        operation_part = f"|> {operation}" if operation else ""

        # 使用提供的bucket或者硬编码的"iot"
        actual_bucket = bucket if field is not None else "iot"

        # 完整查询语句
        query_sql = f"""
            from(bucket: "{actual_bucket}")
                {range_part}
                |> filter(fn: (r) => r._measurement == "{measurement}"{field_filter})
                |> filter(fn: (r) => r.device == "{device}")
                {operation_part}
        """

        return query_sql

    @staticmethod
    def time_format_conversion(data):
        """
        时间格式转换，将ISO时间字符串转为本地时间字符串

        Args:
            data (str): ISO格式时间字符串

        Returns:
            str: 格式化后的本地时间字符串 (YYYY-MM-DD HH:MM:SS)
        """
        # 将字符串转换为 datetime 对象
        utc_time = isoparse(data)
        # 将 UTC 时间转换为 'Asia/Shanghai' 时区的本地时间
        local_time = utc_time.astimezone(datetime.timezone(datetime.timedelta(hours=8)))
        # 将本地时间转换为字符串
        formatted_time = local_time.strftime("%Y-%m-%d %H:%M:%S")
        return formatted_time

    def close(self):
        """关闭InfluxDB客户端连接"""
        if self.client:
            self.client.close()
            logger.info("InfluxDB client connection closed")

influx_client = InfluxDBClient(
        url="http://10.1.1.23:8088",
        token="cd4tYB0mt_YmLXyGoWh-Ll4KaxSjsHwQ-TeblI-mm7hms-kvTm7MzGaTbRUOOYsYwiLpoSZxSolBsGQ1r7ulMg==",
        org="hwtc",
        default_bucket="AutoTest"
    )
# 使用示例
# if __name__ == "__main__":
    # 创建InfluxDB客户端实例

    #
    # # 写入单个数据点示例
    # influx_client.write_data(
    #     table="temperature",
    #     tag={"key": "device", "value": "sensor1"},
    #     data={"key": "value", "value": 23.5}
    # )
    #
    # # 写入多个标签和字段的数据点示例
    # influx_client.write_data_multi(
    #     table="environment",
    #     tags={"device": "sensor1", "location": "room1"},
    #     fields={"temperature": 23.5, "humidity": 45.2}
    # )
    #
    # # 查询最新数据示例
    # result = influx_client.query_latest(
    #     bucket="AutoTest",
    #     measurement="temperature",
    #     device="sensor1",
    #     field="value"
    # )
    # print(result)
    #
    # # 不要忘记关闭连接
    # influx_client.close()