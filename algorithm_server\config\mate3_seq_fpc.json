{
    "sequence":
    [
        //排线检测
        {
            "name":"左排线检测流程",
            "command":
            [
            	{
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"."
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取字符串长度",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取字符串长度结果",
                        "string":"获取字符串长度"
                    }
                },
                {
                    "name":"判断获取字符串长度结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"读取排线图片",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"排线拍照",
                    "type":"get_camera_frame",
                    "in":
                    {
                        "camera":"左拍排线相机",
                        "exposure":"250000",
                        "gain":"8.0"
                    },
                    "out":
                    {
                        "result":"排线拍照结果",
                        "image":"排线拍照图片"
                    }
                },
                {
                    "name":"跳转获取时间",
                    "type":"jump",
                    "in":
                    {
                        "tag":"获取当前时间"
                    }
                },
                {
                    "name":"读取排线图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "image":"排线拍照图片",
                        "result":"排线拍照结果"
                    }
                },
                {
                    "name":"获取当前时间",
                    "type":"get_date_time",
                    "out":
                    {
                        "result":"获取当前时间结果",
                        "date_time":"当前时间"  
                    }
                    
                },
                {
                    "name":"生成图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"fpc_\",\"@当前时间\",\"_\",\"$左SN码\",\".jpg\"]"
                    },
                    "out":
                    {
                        "result":"生成图片名称结果",
                        "combine":"图片名称"
                    }
                },
                {
                    "name":"保存排线图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@排线拍照图片",
                        "path":"$左图片文件夹",
                        "name":"@图片名称"
                    },
                    "out":
                    {
                        "result":"保存图片结果",
                        "full_name":"图片路径"
                    }
                },
                {
                    "name":"发送排线检测结果",
                    "type":"send_s7_client",
                    "in":
                    {
                    	"wait":"排线拍照结果",
                        "client":"左PLC",
                        "db":"520",
                        "offset":"166",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"右排线检测流程",
            "command":
            [
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"."
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取字符串长度",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取字符串长度结果",
                        "string":"获取字符串长度"
                    }
                },
                {
                    "name":"判断获取字符串长度结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"读取排线图片",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"排线拍照",
                    "type":"get_camera_frame",
                    "in":
                    {
                        "camera":"右拍排线相机",
                        "exposure":"300000",
                        "gain":"8.0"
                    },
                    "out":
                    {
                        "result":"排线拍照结果",
                        "image":"排线拍照图片"
                    }
                },
                {
                    "name":"跳转获取时间",
                    "type":"jump",
                    "in":
                    {
                        "tag":"获取当前时间"
                    }
                },
                {
                    "name":"读取排线图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "image":"排线拍照图片",
                        "result":"排线拍照结果"
                    }
                },
                {
                    "name":"获取当前时间",
                    "type":"get_date_time",
                    "out":
                    {
                        "result":"获取当前时间结果",
                        "date_time":"当前时间"  
                    }
                    
                },
                {
                    "name":"生成图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"fpc_\",\"@当前时间\",\"_\",\"$右SN码\",\".jpg\"]"
                    },
                    "out":
                    {
                        "result":"生成图片名称结果",
                        "combine":"图片名称"
                    }
                },
                {
                    "name":"保存排线图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@排线拍照图片",
                        "path":"$右图片文件夹",
                        "name":"@图片名称"
                    },
                    "out":
                    {
                        "result":"保存图片结果",
                        "full_name":"图片路径"
                    }
                },
                {
                    "name":"发送排线检测结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"166",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
         //防水胶带检测
         {
            "name":"左防水胶带检测流程",
            "command":
            [
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"."
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取字符串长度",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取字符串长度结果",
                        "string":"获取字符串长度"
                    }
                },
                {
                    "name":"判断获取字符串长度结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"读取防水胶带图片",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"防水胶带拍照",
                    "type":"get_camera_frame",
                    "in":
                    {
                        "camera":"左拍排线相机",
                        "exposure":"250000",
                        "gain":"8.0"
                    },
                    "out":
                    {
                        "result":"防水胶带拍照结果",
                        "image":"防水胶带拍照图片"
                    }
                },
                {
                    "name":"跳转获取时间",
                    "type":"jump",
                    "in":
                    {
                        "tag":"获取当前时间"
                    }
                },
                {
                    "name":"读取防水胶带图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "image":"防水胶带拍照图片",
                        "result":"防水胶带拍照结果"
                    }
                },
                {
                    "name":"获取当前时间",
                    "type":"get_date_time",
                    "out":
                    {
                        "result":"获取当前时间结果",
                        "date_time":"当前时间"  
                    }
                    
                },
                {
                    "name":"生成图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"tape_\",\"@当前时间\",\"_\",\"$左SN码\",\".jpg\"]"
                    },
                    "out":
                    {
                        "result":"生成图片名称结果",
                        "combine":"图片名称"
                    }
                },
                {
                    "name":"保存防水胶带图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@防水胶带拍照图片",
                        "path":"$左图片文件夹",
                        "name":"@图片名称"
                    },
                    "out":
                    {
                        "result":"保存图片结果",
                        "full_name":"图片路径"
                    }
                },
                {
                    "name":"发送防水胶带检测结果",
                    "type":"send_s7_client",
                    "in":
                    {
                    "wait":"防水胶带拍照结果",
                        "client":"左PLC",
                        "db":"520",
                        "offset":"186",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"右防水胶带检测流程",
            "command":
            [
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"."
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取字符串长度",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取字符串长度结果",
                        "string":"获取字符串长度"
                    }
                },
                {
                    "name":"判断获取字符串长度结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"读取防水胶带图片",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"防水胶带拍照",
                    "type":"get_camera_frame",
                    "in":
                    {
                        "camera":"右拍排线相机",
                        "exposure":"300000",
                        "gain":"8.0"
                    },
                    "out":
                    {
                        "result":"防水胶带拍照结果",
                        "image":"防水胶带拍照图片"
                    }
                },
                {
                    "name":"跳转获取时间",
                    "type":"jump",
                    "in":
                    {
                        "tag":"获取当前时间"
                    }
                },
                {
                    "name":"读取防水胶带图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "image":"防水胶带拍照图片",
                        "result":"防水胶带拍照结果"
                    }
                },
                {
                    "name":"获取当前时间",
                    "type":"get_date_time",
                    "out":
                    {
                        "result":"获取当前时间结果",
                        "date_time":"当前时间"  
                    }
                    
                },
                {
                    "name":"生成图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"tape_\",\"@当前时间\",\"_\",\"$右SN码\",\".jpg\"]"
                    },
                    "out":
                    {
                        "result":"生成图片名称结果",
                        "combine":"图片名称"
                    }
                },
                {
                    "name":"保存防水胶带图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@防水胶带拍照图片",
                        "path":"$右图片文件夹",
                        "name":"@图片名称"
                    },
                    "out":
                    {
                        "result":"保存图片结果",
                        "full_name":"图片路径"
                    }
                },
                {
                    "name":"发送防水胶带检测结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"186",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        }
    ]
}
