import time
import threading
import sys

from PyQt5.QtWidgets import QApplication

from control_board.auto_test_m.station import Station

app = QApplication(sys.argv)

station = Station()
station.start()


class Work(threading.Thread):
    def run(self) -> None:
        while True:
            while True:
                print("station.test_triggered_flag ", station.test_triggered_flag)
                if station.test_triggered_flag == 1:
                    station.test_triggered_flag = 0
                    break
                time.sleep(0.5)

            station.x = 52960
            station.y = -1549
            station.z1 = 0
            station.z2 = -63628
            station.r = 0
            station.p_adjust_result_flag = 0
            station.p_adjust_triggered_flag = 1
            while True:
                print("station.p_adjust_result_flag ", station.p_adjust_result_flag)
                if station.p_adjust_result_flag == 1:
                    station.p_adjust_result_flag = 0
                    break
                time.sleep(0.5)

            time.sleep(5)

            station.x = 135054
            station.y = -1549
            station.z1 = 0
            station.z2 = -63628
            station.r = 0
            station.p_adjust_result_flag = 0
            station.p_adjust_triggered_flag = 1
            while True:
                print("station.p_adjust_result_flag ", station.p_adjust_result_flag)
                if station.p_adjust_result_flag == 1:
                    station.p_adjust_result_flag = 0
                    break
                time.sleep(0.5)

            time.sleep(5)

            station.x = -1390
            station.y = -1549
            station.z1 = 0
            station.z2 = -63628
            station.r = 2419
            station.p_adjust_result_flag = 0
            station.p_adjust_triggered_flag = 1
            while True:
                print("station.p_adjust_result_flag ", station.p_adjust_result_flag)
                if station.p_adjust_result_flag == 1:
                    station.p_adjust_result_flag = 0
                    break
                time.sleep(0.5)

            time.sleep(5)

            station.test_result_flag = 1


Work().start()

sys.exit(app.exec())
