import logging
import os
from datetime import datetime

from common.AppConfig import app_config

handler = logging.StreamHandler()
message_format = '%(asctime)s - %(levelname)s: %(message)s'
logging.basicConfig(level=logging.INFO, handlers=[handler], format=message_format)
can_logger = logging.getLogger('CanLog')
mcu_logger = logging.getLogger('McuLog')
os_logger = logging.getLogger('OsLog')
vds_app_logger = logging.getLogger('VdsAppLog')
oscilloscope_logger = logging.getLogger('OscilloscopeLog')
CACHE_PATH = os.path.join(app_config.log_folder, datetime.now().strftime("%Y-%m-%d"))
if not os.path.exists(CACHE_PATH):
    os.makedirs(CACHE_PATH)
