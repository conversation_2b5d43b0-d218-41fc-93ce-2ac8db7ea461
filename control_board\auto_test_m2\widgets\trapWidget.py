from PyQt5.QtWidgets import QWidget

from .ui.trapWidget import Ui_Form
from ..motion_control_card import mcc


class TrapWidget(QWidget, Ui_Form):
    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setupUi(self)

        self.setWindowTitle("点位运动")
        self.resize(900, 700)

        for i in mcc.axis_d:
            getattr(self, f"pushButton_trap_start_{i}").clicked.connect(self.on_trap_start)

    def on_trap_start(self):
        sender = self.sender().objectName()
        index = int(sender.split("_")[-1])

        pos = getattr(self, f"spinBox_trap_step_{index}").value()
        if getattr(self, f"comboBox_trap_mode_{index}").currentIndex() == 1:
            pos += int(mcc.axis_d[index].status.axis_enc_pos)
        vel = getattr(self, f"doubleSpinBox_trap_vel_{index}").value()
        acc = getattr(self, f"doubleSpinBox_trap_acc_{index}").value()
        dec = getattr(self, f"doubleSpinBox_trap_dec_{index}").value()
        vel_start = getattr(self, f"doubleSpinBox_trap_vel_start_{index}").value()
        smooth_time = getattr(self, f"spinBox_trap_smooth_time_{index}").value()

        mcc.axis_d[index].trap_move(pos=pos, vel=vel, acc=acc, dec=dec, velStart=vel_start, smoothTime=smooth_time)