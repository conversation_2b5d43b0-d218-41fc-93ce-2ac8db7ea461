# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/3/15 15:16
@Desc   : 
"""
from can import Message
from typing import Optional
class HW_Message(Message):
    def __init__(self,timestamp: float = 0.0,
        arbitration_id: int = 0,
        is_extended_id: bool = True,
        is_remote_frame: bool = False,
        is_error_frame: bool = False,
        channel= None,
        dlc: Optional[int] = None,
        data = None,
        is_fd: bool = False,
        is_rx: bool = True,
        bitrate_switch: bool = False,
        error_state_indicator: bool = False,
        check: bool = False,
        count: int = 0
                 ):
        super().__init__(timestamp,arbitration_id,is_extended_id,is_remote_frame,is_error_frame,channel,dlc,data,is_fd,is_rx,bitrate_switch,error_state_indicator,check)
        self.count=count