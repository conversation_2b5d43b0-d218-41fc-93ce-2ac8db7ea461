"""
基础命令处理器类
"""
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any

logger = logging.getLogger(__name__)


class BaseCommandHandler(ABC):
    """基础命令处理器抽象类"""
    
    def __init__(self):
        self.supported_commands = set()
    
    @abstractmethod
    def get_supported_commands(self) -> set:
        """返回支持的命令列表"""
        pass
    
    @abstractmethod
    def execute(self, command: str, step: Dict[str, Any]) -> None:
        """执行命令"""
        pass
    
    def can_handle(self, command: str) -> bool:
        """检查是否可以处理指定命令"""
        return command in self.get_supported_commands()
    
    def _extract_step_params(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """提取步骤参数"""
        return {
            'total_times': step.get("total_times"),
            'current_times': step.get("current_times"),
            'case_number': step.get("case_number"),
            'case_name': step.get("case_name"),
            'expect': step.get("expect"),
            'customize_cmd': step.get("CustomizeCMD"),
            'data': step.get("data"),
            'command': step.get("command")
        }
    
    def _emit_success(self, case_number: str, command: str, result: str = "PASS"):
        """发送成功信号"""
        from utils.SignalsManager import signals_manager
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", result)
    
    def _emit_failure(self, case_number: str, command: str, error_msg: str = "NG"):
        """发送失败信号"""
        from utils.SignalsManager import signals_manager
        signals_manager.step_execute_finish.emit(case_number, command, "NG", error_msg)
    
    def _parse_data_params(self, data: str, expected_count: int = None) -> list:
        """解析数据参数"""
        if not data:
            return []
        
        params = data.split(",")
        if expected_count and len(params) < expected_count:
            raise ValueError(f"参数数量不足，期望 {expected_count} 个，实际 {len(params)} 个")
        
        return params
    
    def _safe_float(self, value: str, default: float = 0.0) -> float:
        """安全转换为浮点数"""
        try:
            return float(value)
        except (ValueError, TypeError):
            logger.warning(f"无法转换为浮点数: {value}, 使用默认值: {default}")
            return default
    
    def _safe_int(self, value: str, default: int = 0) -> int:
        """安全转换为整数"""
        try:
            return int(value)
        except (ValueError, TypeError):
            logger.warning(f"无法转换为整数: {value}, 使用默认值: {default}")
            return default
