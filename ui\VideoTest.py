# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'VideoTest.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_video_cap(object):
    def setupUi(self, video_cap):
        video_cap.setObjectName("video_cap")
        video_cap.resize(2000, 900)
        video_cap.setMinimumSize(QtCore.QSize(1600, 900))
        video_cap.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(11)
        video_cap.setFont(font)
        video_cap.setStyleSheet("")
        self.verticalLayout_9 = QtWidgets.QVBoxLayout(video_cap)
        self.verticalLayout_9.setContentsMargins(15, 15, 10, 10)
        self.verticalLayout_9.setSpacing(10)
        self.verticalLayout_9.setObjectName("verticalLayout_9")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setContentsMargins(0, 0, -1, -1)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setContentsMargins(-1, 0, -1, 0)
        self.horizontalLayout_4.setSpacing(6)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem)
        self.label = QtWidgets.QLabel(video_cap)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setMinimumSize(QtCore.QSize(0, 45))
        font = QtGui.QFont()
        font.setPointSize(11)
        self.label.setFont(font)
        self.label.setStyleSheet("")
        self.label.setObjectName("label")
        self.horizontalLayout_4.addWidget(self.label)
        self.load_video = QtWidgets.QPushButton(video_cap)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.load_video.sizePolicy().hasHeightForWidth())
        self.load_video.setSizePolicy(sizePolicy)
        self.load_video.setMinimumSize(QtCore.QSize(0, 45))
        self.load_video.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(False)
        font.setWeight(50)
        self.load_video.setFont(font)
        self.load_video.setStyleSheet("")
        self.load_video.setObjectName("load_video")
        self.horizontalLayout_4.addWidget(self.load_video)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem1)
        self.label_2 = QtWidgets.QLabel(video_cap)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_2.sizePolicy().hasHeightForWidth())
        self.label_2.setSizePolicy(sizePolicy)
        self.label_2.setMinimumSize(QtCore.QSize(0, 45))
        font = QtGui.QFont()
        font.setPointSize(11)
        self.label_2.setFont(font)
        self.label_2.setStyleSheet("")
        self.label_2.setObjectName("label_2")
        self.horizontalLayout_4.addWidget(self.label_2)
        self.start_video_test_button = QtWidgets.QPushButton(video_cap)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.start_video_test_button.sizePolicy().hasHeightForWidth())
        self.start_video_test_button.setSizePolicy(sizePolicy)
        self.start_video_test_button.setMinimumSize(QtCore.QSize(0, 45))
        self.start_video_test_button.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(11)
        font.setBold(False)
        font.setWeight(50)
        self.start_video_test_button.setFont(font)
        self.start_video_test_button.setStyleSheet("")
        self.start_video_test_button.setObjectName("start_video_test_button")
        self.horizontalLayout_4.addWidget(self.start_video_test_button)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem2)
        self.label_4 = QtWidgets.QLabel(video_cap)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_4.sizePolicy().hasHeightForWidth())
        self.label_4.setSizePolicy(sizePolicy)
        self.label_4.setMinimumSize(QtCore.QSize(0, 45))
        font = QtGui.QFont()
        font.setPointSize(11)
        self.label_4.setFont(font)
        self.label_4.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.label_4.setAlignment(QtCore.Qt.AlignCenter)
        self.label_4.setObjectName("label_4")
        self.horizontalLayout_4.addWidget(self.label_4)
        self.test_status_label = QtWidgets.QLabel(video_cap)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.test_status_label.sizePolicy().hasHeightForWidth())
        self.test_status_label.setSizePolicy(sizePolicy)
        self.test_status_label.setMinimumSize(QtCore.QSize(0, 45))
        self.test_status_label.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(11)
        self.test_status_label.setFont(font)
        self.test_status_label.setStyleSheet("")
        self.test_status_label.setAlignment(QtCore.Qt.AlignCenter)
        self.test_status_label.setObjectName("test_status_label")
        self.horizontalLayout_4.addWidget(self.test_status_label)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem3)
        self.label_14 = QtWidgets.QLabel(video_cap)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_14.sizePolicy().hasHeightForWidth())
        self.label_14.setSizePolicy(sizePolicy)
        self.label_14.setMinimumSize(QtCore.QSize(0, 45))
        self.label_14.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(11)
        self.label_14.setFont(font)
        self.label_14.setObjectName("label_14")
        self.horizontalLayout_4.addWidget(self.label_14)
        self.scene_change = QtWidgets.QComboBox(video_cap)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.scene_change.sizePolicy().hasHeightForWidth())
        self.scene_change.setSizePolicy(sizePolicy)
        self.scene_change.setMinimumSize(QtCore.QSize(0, 45))
        self.scene_change.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(11)
        self.scene_change.setFont(font)
        self.scene_change.setStyleSheet("")
        self.scene_change.setObjectName("scene_change")
        self.horizontalLayout_4.addWidget(self.scene_change)
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem4)
        self.label_3 = QtWidgets.QLabel(video_cap)
        font = QtGui.QFont()
        font.setPointSize(11)
        self.label_3.setFont(font)
        self.label_3.setObjectName("label_3")
        self.horizontalLayout_4.addWidget(self.label_3)
        self.flicker_threshold_lineedit = QtWidgets.QLineEdit(video_cap)
        self.flicker_threshold_lineedit.setMinimumSize(QtCore.QSize(0, 45))
        font = QtGui.QFont()
        font.setPointSize(11)
        self.flicker_threshold_lineedit.setFont(font)
        self.flicker_threshold_lineedit.setObjectName("flicker_threshold_lineedit")
        self.horizontalLayout_4.addWidget(self.flicker_threshold_lineedit)
        self.label_6 = QtWidgets.QLabel(video_cap)
        font = QtGui.QFont()
        font.setPointSize(11)
        self.label_6.setFont(font)
        self.label_6.setObjectName("label_6")
        self.horizontalLayout_4.addWidget(self.label_6)
        self.grainy_threshold_lineedit = QtWidgets.QLineEdit(video_cap)
        self.grainy_threshold_lineedit.setMinimumSize(QtCore.QSize(0, 45))
        font = QtGui.QFont()
        font.setPointSize(11)
        self.grainy_threshold_lineedit.setFont(font)
        self.grainy_threshold_lineedit.setObjectName("grainy_threshold_lineedit")
        self.horizontalLayout_4.addWidget(self.grainy_threshold_lineedit)
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem5)
        self.horizontalLayout_4.setStretch(0, 1)
        self.horizontalLayout_4.setStretch(1, 1)
        self.horizontalLayout_4.setStretch(2, 1)
        self.horizontalLayout_4.setStretch(3, 1)
        self.horizontalLayout_4.setStretch(4, 1)
        self.horizontalLayout_4.setStretch(5, 1)
        self.horizontalLayout_4.setStretch(6, 1)
        self.horizontalLayout_4.setStretch(7, 1)
        self.horizontalLayout_4.setStretch(8, 1)
        self.horizontalLayout_4.setStretch(9, 1)
        self.horizontalLayout_4.setStretch(10, 1)
        self.horizontalLayout_4.setStretch(11, 1)
        self.horizontalLayout_4.setStretch(12, 1)
        self.horizontalLayout_4.setStretch(13, 1)
        self.horizontalLayout_4.setStretch(14, 1)
        self.horizontalLayout_4.setStretch(15, 1)
        self.horizontalLayout_4.setStretch(16, 1)
        self.horizontalLayout_4.setStretch(17, 1)
        self.horizontalLayout.addLayout(self.horizontalLayout_4)
        self.verticalLayout_9.addLayout(self.horizontalLayout)
        self.detect_value_textedit = QtWidgets.QTextEdit(video_cap)
        self.detect_value_textedit.setObjectName("detect_value_textedit")
        self.verticalLayout_9.addWidget(self.detect_value_textedit)
        self.groupBox_2 = QtWidgets.QGroupBox(video_cap)
        self.groupBox_2.setTitle("")
        self.groupBox_2.setObjectName("groupBox_2")
        self.horizontalLayout_11 = QtWidgets.QHBoxLayout(self.groupBox_2)
        self.horizontalLayout_11.setObjectName("horizontalLayout_11")
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setObjectName("gridLayout")
        self.graphicsView = QtWidgets.QGraphicsView(self.groupBox_2)
        self.graphicsView.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.graphicsView.setObjectName("graphicsView")
        self.gridLayout.addWidget(self.graphicsView, 0, 0, 1, 1)
        self.gridLayout.setColumnStretch(0, 1)
        self.horizontalLayout_11.addLayout(self.gridLayout)
        self.verticalLayout_9.addWidget(self.groupBox_2)
        self.verticalLayout_9.setStretch(1, 1)
        self.verticalLayout_9.setStretch(2, 5)

        self.retranslateUi(video_cap)
        QtCore.QMetaObject.connectSlotsByName(video_cap)

    def retranslateUi(self, video_cap):
        _translate = QtCore.QCoreApplication.translate
        video_cap.setWindowTitle(_translate("video_cap", "Form"))
        self.label.setText(_translate("video_cap", "导入视频:"))
        self.load_video.setText(_translate("video_cap", "导入"))
        self.label_2.setText(_translate("video_cap", "开始测试:"))
        self.start_video_test_button.setText(_translate("video_cap", "视频缺陷测试"))
        self.label_4.setText(_translate("video_cap", "测试状态:"))
        self.test_status_label.setText(_translate("video_cap", "待测试"))
        self.label_14.setText(_translate("video_cap", "场景选择:"))
        self.label_3.setText(_translate("video_cap", "闪屏阈值："))
        self.flicker_threshold_lineedit.setText(_translate("video_cap", "0.1"))
        self.label_6.setText(_translate("video_cap", "花屏阈值："))
        self.grainy_threshold_lineedit.setText(_translate("video_cap", "1000"))
