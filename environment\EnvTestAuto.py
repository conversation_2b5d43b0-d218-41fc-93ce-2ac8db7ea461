import json
import os
import threading
import time

from PyQt5.QtCore import QObject
from modbus_tk import defines

from common.LogUtils import logger
from environment.TemperatureManager import temperature_manager
from utils.ProjectManager import project_manager
from utils.SignalsManager import signals_manager
from environment.TemperatureClient import temperature_client


class EnvTestAuto(QObject):

    def __init__(self, parent=None):
        super(EnvTestAuto, self).__init__(parent)
        signals_manager.temp_data_update.connect(self.update_table)
        self.is_stop = True
        self.suspend_flag = False
        self.thread = None
        self.results = []
        self.test_result = None
        self.device = "TP1000"
        self.case_number = ""
        self.command = ""
        self.total_test_time = 0
        self.frequency = 0
        self.channel_list = []
        signals_manager.start_env_temperature_rise_signal.connect(self.start_env_temperature_rise)

    def set_parameter(self, device, frequency, total_test_time, channels):
        self.device = device
        self.channel_list = channels
        self.total_test_time = int(total_test_time)
        self.frequency = int(frequency)

    def init(self):
        self.is_stop = False
        self.suspend_flag = False
        self.thread = None
        self.results = []
        self.test_result = None

    def update_table(self, result):
        self.results.append(result)

    def start(self):
        logger.info("start")
        self.init()
        total_time = self.total_test_time * 60
        frequency = self.frequency
        if total_time <= 0 or frequency <= 0:
            # return MessageDialog.show_message("错误", "测试时间或者采集频率不能为0")
            logger.info("测试时间或者采集频率不能为0")
            return

        def callback_result(result: int):
            if result == 0x01:
                self.start_env_temperature_rise()

        callback_result(0x01)

    def start_env_temperature_rise(self, case_number="", command=""):
        logger.info(f"start_env_temperature_rise case_number={case_number}, command={command}")
        self.is_stop = False
        self.thread = threading.Thread(target=self.get_data)
        self.thread.start()

    def stop(self):
        self.is_stop = True
        head = ["时间"] + ["产品屏幕温度"] + [f"通道{i}" for i in self.channel_list]
        result = self.get_table_result()
        signals_manager.temperature_test_finished.emit(head, result)

    def get_data(self):
        total_time = self.total_test_time * 60
        frequency = self.frequency
        while not self.is_stop:
            if not self.suspend_flag:
                t = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())) + " "
                device = self.device
                result = [t]
                temp = temperature_manager.get_screen_temp(project_number=project_manager.get_test_plan_project_number())
                logger.info(f"get_data temp={temp}")
                result.append(str(temp))
                for num in self.channel_list:
                    if device == "TP1000" or device == "TP710A":
                        tem = temperature_client.execute(defines.READ_HOLDING_REGISTERS,
                                                         starting_address=(int(num) - 1) * 2,
                                                         quantity_of_x=2,
                                                         data_format=">f")[0]
                        tem = str(round(tem, 2))
                        result.append(tem)
                    elif device == "TP700":
                        tem = str(temperature_client.read_hr_one(2 * int(num) - 1))
                        tem = tem[0:2] + "." + tem[2:]
                        result.append(tem)

                signals_manager.temp_data_update.emit(result)
                time.sleep(frequency)
                total_time = total_time - frequency
                if total_time <= 0:
                    break
            else:
                time.sleep(0.2)

        head = ["时间"] + ["产品屏幕温度"] + [f"通道{i}" for i in self.channel_list]
        result = self.get_table_result()
        signals_manager.temperature_test_finished.emit(head, result)
        self.save_table_result(head, result)
        signals_manager.step_execute_finish.emit(self.case_number, self.command, "待判定", "待判定")

    def save_table_result(self, head, result):
        item = {"test_function": "EnvTemperatureTest", "header": head, "content": result}
        self.test_result = item

        from common.LogUtils import CACHE_PATH
        project_number = project_manager.get_test_plan_project_number()
        env_test_results_path = os.path.join(CACHE_PATH, "EnvTest")
        if not os.path.exists(env_test_results_path):
            os.mkdir(env_test_results_path)
        # 获取当前的时间
        time_str = time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime())
        path = os.path.join(env_test_results_path, project_number + f"_{time_str}EnvTemperatureTest.json")
        with open(path, "w") as f:
            data = json.dumps(item, indent=2)
            f.write(data)

    def get_table_result(self):
        return self.results


env_test_auto = EnvTestAuto()
