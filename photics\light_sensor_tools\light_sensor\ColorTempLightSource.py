# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/6/13 16:01
@Desc   : 色温照度光源
"""
import time

import serial

from common.LogUtils import logger
from utils import int_to_low_high


class ColorTempLightSource:

    def __init__(self):
        self.port = None
        self.baudrate = None
        self.bytesize = None
        self.parity = None
        self.stopbits = None
        self.timeout = None
        self._is_open = False
        self._serial = None
        self.color_temp = 0
        self.light_intensity = 5000
        self.max_light_intensity = 5000

    def open(self, port, baudrate=9600, bytesize=8, parity="N", stopbits=1, timeout=1.0):
        logger.info('open port=%s', port)
        self.port = port
        self.baudrate = baudrate
        self.bytesize = bytesize
        self.parity = parity
        self.stopbits = stopbits
        self.timeout = timeout

        try:
            if not self._is_open:
                if self._serial:
                    self._serial.close()
                self._serial = serial.Serial(
                    port=self.port, baudrate=self.baudrate, bytesize=self.bytesize, parity=self.parity,
                    stopbits=self.stopbits, timeout=self.timeout, write_timeout=self.timeout
                )
                self._is_open = True
        except Exception as e:
            logger.error(f"open exception: {str(e.args)}")
            self._is_open = False

        return self._is_open

    def close(self):
        if self._is_open:
            if self._serial:
                self._serial.close()
                self._serial = None
                self._is_open = False
        return True

    def set_color_temp(self, color_temp=6500):
        """
        设置光源色温值
        @param color_temp: 色温值 默认6500
        """
        logger.info(f"set_color_temp color_temp={color_temp}")
        low, high = int_to_low_high(color_temp)
        data = [0x55, 0xAA, 0x01, low, high, 0x00, 0x00]
        crc = ~(0x55 + 0xAA + 0x01 + low + high + 0x00 + 0x00) & 0xFF
        data.append(crc)
        logger.info(f"set_color_temp data={data}")
        if self._serial is not None:
            self._serial.flush()
            self._serial.write(bytes(data))

            self.color_temp = color_temp

    def set_light_intensity(self, light_intensity=50):
        """
        设置光源照度值
        @param light_intensity: 光源照度值 默认50lux
        """
        logger.info(f"set_light_intensity light_intensity={light_intensity}")
        low, high = int_to_low_high(light_intensity)
        data = [0x55, 0xAA, 0x02, low, high, 0x00, 0x00]
        crc = ~(0x55 + 0xAA + 0x02 + low + high + 0x00 + 0x00) & 0xFF
        data.append(crc)
        logger.info(f"set_light_intensity data={data}")
        if self._serial is not None:
            self._serial.flush()
            self._serial.write(bytes(data))

            self.light_intensity = light_intensity
            logger.info(f"set_light_intensity light_intensity={light_intensity}")

    def set_first_infrared_grade(self, grade=3000):
        """
        设置第一组红外等级
        @param grade: 红外等级 默认3000
        """
        logger.info(f"set_light_intensity grade={grade}")
        low, high = int_to_low_high(grade)
        data = [0x55, 0xAA, 0x05, low, high, 0x00, 0x00]
        crc = ~(0x55 + 0xAA + 0x05 + low + high + 0x00 + 0x00) & 0xFF
        data.append(crc)
        logger.info(f"set_first_infrared_grade data={data}")
        if self._serial is not None:
            self._serial.flush()
            self._serial.write(bytes(data))
            # 设置第一组红外等级的回复
            data = []
            lines = self._serial.readlines()
            logger.info(f"set_first_infrared_grade lines={lines}")
            if len(lines) > 0:
                line = lines[0]
                logger.info(f"set_first_infrared_grade line={line}")
                data = list(bytearray(line))
            logger.info(f"set_first_infrared_grade data={data}")

    def set_first_infrared_switch(self):
        """
        设置第一组红外开关，发送一次打开，再发送一次关闭
        """
        logger.info(f"set_first_infrared_switch open={open}")
        data = [0x55, 0xAA, 0x06, 0x00, 0x00, 0x00, 0x00]
        crc = ~(0x55 + 0xAA + 0x06 + 0x00 + 0x00 + 0x00 + 0x00) & 0xFF
        data.append(crc)
        logger.info(f"set_first_infrared_switch data={data}")
        if self._serial is not None:
            self._serial.flush()
            self._serial.write(bytes(data))
            # 设置第一组红外开关的回复
            data = []
            lines = self._serial.readlines()
            logger.info(f"set_first_infrared_switch lines={lines}")
            if len(lines) > 0:
                line = lines[0]
                logger.info(f"set_first_infrared_switch line={line}")
                data = list(bytearray(line))
            logger.info(f"set_first_infrared_switch data={data}")

    def get_first_infrared_switch_grade(self):
        """
        获取第一组红外的开关和等级
        """
        logger.info(f"get_first_infrared_switch_grade")
        data = [0x55, 0xAA, 0x07, 0x00, 0x00, 0x00, 0x00]
        crc = ~(0x55 + 0xAA + 0x07 + 0x00 + 0x00 + 0x00 + 0x00) & 0xFF
        data.append(crc)
        logger.info(f"get_first_infrared_switch_grade data={data}")
        if self._serial is not None:
            self._serial.flush()
            self._serial.write(bytes(data))
            # 获取第一组红外开关和等级
            data = []
            lines = self._serial.readlines()
            logger.info(f"get_first_infrared_switch_grade lines={lines}")
            if len(lines) > 0:
                line = lines[0]
                logger.info(f"get_first_infrared_switch_grade line={line}")
                data = list(bytearray(line))
            logger.info(f"get_first_infrared_switch_grade data={data}")

    def get_color_temperature_light_intensity(self):
        logger.info(f"get_color_temperature_light_intensity")
        req_data = [0x55, 0xAA, 0x00, 0x00, 0x00, 0x00, 0x00]
        crc = ~(0x55 + 0xAA + 0x00 + 0x00 + 0x00 + 0x00 + 0x00) & 0xFF
        req_data.append(crc)
        logger.info(f"get_color_temperature_light_intensity req_data={req_data}")
        if self._serial is not None:
            self._serial.flush()
            self._serial.write(bytes(req_data))

            line = self._serial.readlines()
            logger.info(f"get_color_temperature_light_intensity line={line}")

    def set_default_color_temp(self, retry_times=2):
        for i in range(retry_times):
            self.set_color_temp()
            time.sleep(0.5)

    def is_open(self):
        return self._is_open


color_temp_light_source: ColorTempLightSource = ColorTempLightSource()

if __name__ == '__main__':
    color_temp_light_source.open(port="COM15")
    color_temp_light_source.set_color_temp(color_temp=2800)
    # color_temp_light_source.set_light_intensity(light_intensity=1000)
    # color_temp_light_source.set_first_infrared_switch()
    # color_temp_light_source.set_first_infrared_grade(3500)
    # color_temp_light_source.get_first_infrared_switch_grade()
