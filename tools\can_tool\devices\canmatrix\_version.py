
# This file was generated by 'versioneer.py' (0.18) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2019-11-08T10:05:51+0100",
 "dirty": false,
 "error": null,
 "full-revisionid": "889c29520524553115f7a6dafecf8ac68b3eb871",
 "version": "0.9"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
