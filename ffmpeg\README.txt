This is a FFmpeg Win64 static build by <PERSON>.

Zeranoe's FFmpeg Builds Home Page: <http://ffmpeg.zeranoe.com/builds/>

FFmpeg version: 2015-08-03 git-5750d6c
  libavutil      54. 27.100 / 54. 27.100
  libavcodec     56. 46.100 / 56. 46.100
  libavformat    56. 40.100 / 56. 40.100
  libavdevice    56.  4.100 / 56.  4.100
  libavfilter     5. 19.100 /  5. 19.100
  libswscale      3.  1.101 /  3.  1.101
  libswresample   1.  2.100 /  1.  2.100
  libpostproc    53.  3.100 / 53.  3.100

This FFmpeg build was configured with:
  --enable-gpl
  --enable-version3
  --disable-w32threads
  --enable-avisynth
  --enable-bzlib
  --enable-fontconfig
  --enable-frei0r
  --enable-gnutls
  --enable-iconv
  --enable-libass
  --enable-libbluray
  --enable-libbs2b
  --enable-libcaca
  --enable-libdcadec
  --enable-libfreetype
  --enable-libgme
  --enable-libgsm
  --enable-libilbc
  --enable-libmodplug
  --enable-libmp3lame
  --enable-libopencore-amrnb
  --enable-libopencore-amrwb
  --enable-libopenjpeg
  --enable-libopus
  --enable-librtmp
  --enable-libschroedinger
  --enable-libsoxr
  --enable-libspeex
  --enable-libtheora
  --enable-libtwolame
  --enable-libvidstab
  --enable-libvo-aacenc
  --enable-libvo-amrwbenc
  --enable-libvorbis
  --enable-libvpx
  --enable-libwavpack
  --enable-libwebp
  --enable-libx264
  --enable-libx265
  --enable-libxavs
  --enable-libxvid
  --enable-lzma
  --enable-decklink
  --enable-zlib

This build was compiled with the following external libraries:
  bzip2 1.0.6 <http://bzip.org/>
  Fontconfig 2.11.94 <http://freedesktop.org/wiki/Software/fontconfig>
  Frei0r 20130909-git-10d8360 <http://frei0r.dyne.org/>
  GnuTLS 3.3.15 <http://gnutls.org/>
  libiconv 1.14 <http://gnu.org/software/libiconv/>
  libass 0.12.2 <http://code.google.com/p/libass/>
  libbluray 0.8.1 <http://videolan.org/developers/libbluray.html>
  libbs2b 3.1.0 <http://bs2b.sourceforge.net/>
  libcaca 0.99.beta18 <http://caca.zoy.org/wiki/libcaca>
  dcadec 20150506-git-98fb3b6 <https://github.com/foo86/dcadec>
  FreeType 2.5.5 <http://freetype.sourceforge.net/>
  Game Music Emu 0.6.0 <http://code.google.com/p/game-music-emu/>
  GSM 1.0.13-4 <http://packages.debian.org/source/squeeze/libgsm>
  iLBC 20141214-git-ef04ebe <https://github.com/dekkers/libilbc/>
  Modplug-XMMS ******* <http://modplug-xmms.sourceforge.net/>
  LAME 3.99.5 <http://lame.sourceforge.net/>
  OpenCORE AMR 0.1.3 <http://sourceforge.net/projects/opencore-amr/>
  OpenJPEG 1.5.2 <http://www.openjpeg.org/>
  Opus 1.1 <http://opus-codec.org/>
  RTMPDump 20140707-git-a1900c3 <http://rtmpdump.mplayerhq.hu/>
  Schroedinger 1.0.11 <http://diracvideo.org/>
  libsoxr 0.1.1 <http://sourceforge.net/projects/soxr/>
  Speex 1.2rc2 <http://speex.org/>
  Theora 1.1.1 <http://theora.org/>
  TwoLAME 0.3.13 <http://twolame.org/>
  vid.stab 0.98 <http://public.hronopik.de/vid.stab/>
  VisualOn AAC 0.1.3 <https://github.com/mstorsjo/vo-aacenc>
  VisualOn AMR-WB 0.1.2 <https://github.com/mstorsjo/vo-amrwbenc>
  Vorbis 1.3.5 <http://vorbis.com/>
  vpx 1.4.0 <http://webmproject.org/>
  WavPack 4.75.0 <http://wavpack.com/>
  WebP 0.4.3 <https://developers.google.com/speed/webp/>
  x264 20150725-git-73ae2d1 <http://videolan.org/developers/x264.html>
  x265 1.7 <http://x265.org/>
  XAVS svn-r55 <http://xavs.sourceforge.net/>
  Xvid 1.3.3 <http://xvid.org/>
  XZ Utils 5.2.1 <http://tukaani.org/xz>
  zlib 1.2.8 <http://zlib.net/>

The source code for this FFmpeg build can be found at: <http://ffmpeg.zeranoe.com/builds/source/>

This build was compiled on Debian 8.1 (64-bit): <http://www.debian.org/>

GCC 4.9.3 was used to compile this FFmpeg build: <http://gcc.gnu.org/>

This build was compiled using the MinGW-w64 toolchain: <http://mingw-w64.sourceforge.net/>

Licenses for each library can be found in the 'licenses' folder.
