"""
电源模块 - 处理电源相关命令
"""
import logging
import operator
from typing import Dict, Any, Set
from case.module_manager import ModuleHandler

logger = logging.getLogger(__name__)


class PowerModule(ModuleHandler):
    """电源模块处理器"""
    
    def __init__(self):
        super().__init__()
        # 延迟加载的资源
        self.it_m3200_control = None
        self.etm_3020pc_control = None
        self.etm_mu3_control = None
        self.signals_manager = None
        self.vds_detect_manager = None
        self.work_current_monitor_manager = None
        self.loop_power_on_off_manager = None
    
    def get_supported_commands(self) -> Set[str]:
        """获取支持的命令列表"""
        return {
            'SwitchPower', 'SwitchVoltage', 'SwitchStepVoltage',
            'ReadWorkCurrent', 'ReadPeriodWorkCurrent', 'ReadWorkVoltage',
            'MonitorMultiChannelWorkCurrent', 'ExecuteLoopPowerOnOff'
        }
    
    def _load_resources(self):
        """加载电源模块资源"""
        logger.info("正在加载电源模块资源...")
        
        # 导入电源相关模块
        from power.ItM3200Control import it_m3200_control
        from power.Etm3020pcControl import etm_3020pc_control
        from power.EtmMu3Control import etm_mu3_control
        from utils.SignalsManager import signals_manager
        from case.VdsDetectManager import vds_detect_manager
        from case.WorkCurrentMonitorManager import work_current_monitor_manager
        from case.LoopPowerOnOffManager import loop_power_on_off_manager
        
        self.it_m3200_control = it_m3200_control
        self.etm_3020pc_control = etm_3020pc_control
        self.etm_mu3_control = etm_mu3_control
        self.signals_manager = signals_manager
        self.vds_detect_manager = vds_detect_manager
        self.work_current_monitor_manager = work_current_monitor_manager
        self.loop_power_on_off_manager = loop_power_on_off_manager
        
        logger.info("电源模块资源加载完成")
    
    def execute_command(self, command: str, step: Dict[str, Any]):
        """执行电源命令"""
        case_number = step.get("case_number", "")
        data = step.get("params", "")
        
        if operator.eq("SwitchPower", command):
            self._handle_switch_power(case_number, command, data)
        elif operator.eq("SwitchVoltage", command):
            self._handle_switch_voltage(case_number, command, data)
        elif operator.eq("SwitchStepVoltage", command):
            self._handle_switch_step_voltage(case_number, command, data)
        elif operator.eq("ReadWorkCurrent", command):
            self._handle_read_work_current(case_number, command, data)
        elif operator.eq("ReadPeriodWorkCurrent", command):
            self._handle_read_period_work_current(case_number, command, data)
        elif operator.eq("ReadWorkVoltage", command):
            self._handle_read_work_voltage(case_number, command, data)
        elif operator.eq("MonitorMultiChannelWorkCurrent", command):
            self._handle_monitor_multi_channel_work_current(case_number, command, data)
        elif operator.eq("ExecuteLoopPowerOnOff", command):
            self._handle_execute_loop_power_on_off(case_number, command, data)
        else:
            logger.warning(f"电源模块不支持命令: {command}")
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", f"不支持的命令: {command}")
    
    def _handle_switch_power(self, case_number: str, command: str, data: str):
        """处理电源开关"""
        if data.__contains__(","):
            volt = float(data.split(",")[0])
            power_channel = int(data.split(",")[1])
        else:
            volt = float(data)
            power_channel = 1
        
        if self.it_m3200_control.is_connect():
            status = self.it_m3200_control.set_volt(volt)
            if status:
                self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "电压设置失败")
        elif self.etm_3020pc_control.is_open():
            status = self.etm_3020pc_control.set_voltage(volt)
            if status:
                self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "电压设置失败")
        elif self.etm_mu3_control.is_open():
            status = self.etm_mu3_control.set_voltage(power_channel, volt)
            if status:
                self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "电压设置失败")
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "电源未连接")
    
    def _handle_switch_voltage(self, case_number: str, command: str, data: str):
        """处理电压切换"""
        if data.__contains__(","):
            volt = float(data.split(",")[0])
            power_channel = int(data.split(",")[1])
        else:
            volt = float(data)
            power_channel = 1
        
        if self.it_m3200_control.is_connect():
            status = self.it_m3200_control.set_volt(volt)
            if status:
                self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "电压设置失败")
        elif self.etm_3020pc_control.is_open():
            status = self.etm_3020pc_control.set_voltage(volt)
            if status:
                self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "电压设置失败")
        elif self.etm_mu3_control.is_open():
            status = self.etm_mu3_control.set_voltage(power_channel, volt)
            if status:
                self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "电压设置失败")
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "电源未连接")
    
    def _handle_switch_step_voltage(self, case_number: str, command: str, data: str):
        """处理步进电压"""
        if data.__contains__(","):
            start_volt = float(data.split(",")[0])
            end_volt = float(data.split(",")[1])
            power_channel = int(data.split(",")[2])
            try:
                interval = float(data.split(",")[3]) / 1000
                step_volt = float(data.split(",")[4])
            except Exception as e:
                logger.info(f"execute_customize_cmd SwitchStepVoltage params error exception: {str(e.args)}")
                interval = 0.5
                step_volt = 0.5
        else:
            start_volt = 0.0
            end_volt = float(data)
            power_channel = 1
            interval = 0.5
            step_volt = 0.5
        
        if self.it_m3200_control.is_connect():
            status = self.it_m3200_control.set_step_volt(start_volt, end_volt, interval, step_volt)
            if status:
                self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "电压设置失败")
        elif self.etm_3020pc_control.is_open():
            status = self.etm_3020pc_control.set_step_voltage(start_volt, end_volt, interval, step_volt)
            if status:
                self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "电压设置失败")
        elif self.etm_mu3_control.is_open():
            status = self.etm_mu3_control.set_step_voltage(power_channel, start_volt, end_volt, interval, step_volt)
            if status:
                self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "电压设置失败")
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "电源未连接")
    
    def _handle_read_work_current(self, case_number: str, command: str, data: str):
        """处理读取工作电流"""
        min_current = float(data.split(",")[0])
        max_current = float(data.split(",")[1])
        power_type = data.split(",")[2]
        
        if operator.eq("IT-M3200", power_type):
            work_current = self.it_m3200_control.read_work_current()
            work_current = round(work_current, 3)
            if min_current < work_current < max_current:
                self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", str(work_current))
            else:
                self.signals_manager.step_execute_finish.emit(case_number, command, "NG", str(work_current))
        elif operator.eq("TOMMENS", power_type):
            work_current = 0.0
            if self.etm_3020pc_control.is_open():
                status, work_current = self.etm_3020pc_control.read_work_current()
                if not status:
                    return self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "电流读取失败")
            elif self.etm_mu3_control.is_open():
                channel = int(data.split(",")[3])
                status, work_current = self.etm_mu3_control.read_work_current(channel=channel)
                if not status:
                    return self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "电流读取失败")
            work_current = round(work_current, 3)
            logger.info(f"execute_customize_cmd case_number {case_number} work_current value is {work_current}")
            if min_current <= work_current <= max_current:
                self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", str(work_current))
            else:
                self.signals_manager.step_execute_finish.emit(case_number, command, "NG", str(work_current))
    
    def _handle_read_period_work_current(self, case_number: str, command: str, data: str):
        """处理读取周期工作电流"""
        if data.__contains__(","):
            read_interval = float(data.split(",")[0])
            read_time = float(data.split(",")[1])
            min_current = float(data.split(",")[2])
            max_current = float(data.split(",")[3])
            
            if self.it_m3200_control.is_connect():
                status, error_work_current = self.it_m3200_control.read_period_work_current(read_interval, read_time,
                                                                                       min_current, max_current)
                if status:
                    logger.info(f"execute_customize_cmd 周期工作电流读取正常")
                    self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
                else:
                    logger.info(f"execute_customize_cmd 周期工作电流读取异常，异常工作电流值：{error_work_current}")
                    self.signals_manager.step_execute_finish.emit(case_number, command, "NG", str(error_work_current))
            elif self.etm_3020pc_control.is_open():
                status, error_work_current = self.etm_3020pc_control.read_period_work_current(read_interval, read_time,
                                                                                         min_current, max_current)
                if status:
                    logger.info(f"execute_customize_cmd 周期工作电流读取正常")
                    self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
                else:
                    logger.info(f"execute_customize_cmd 周期工作电流读取异常，异常工作电流值：{error_work_current}")
                    self.signals_manager.step_execute_finish.emit(case_number, command, "NG", str(error_work_current))
            elif self.etm_mu3_control.is_open():
                channel = int(data.split(",")[4])
                status, error_work_current = self.etm_mu3_control.read_period_work_current(read_interval,
                                                                                      read_time, min_current,
                                                                                      max_current, channel)
                if status:
                    logger.info(f"execute_customize_cmd 周期工作电流读取正常")
                    self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
                else:
                    logger.info(f"execute_customize_cmd  周期工作电流读取异常，异常工作电流值：{error_work_current}")
                    self.signals_manager.step_execute_finish.emit(case_number, command, "NG", str(error_work_current))
    
    def _handle_read_work_voltage(self, case_number: str, command: str, data: str):
        """处理读取工作电压"""
        if data.__contains__(","):
            min_voltage = data.split(",")[0]
            max_voltage = data.split(",")[1]
            from adb.AdbConnectDevice import adb_connect_device
            adb_connect_device.adb_forward_send_data(action="readWorkVoltage")
            self.vds_detect_manager.set_expect_work_voltage(case_number, command, min_voltage, max_voltage)
    
    def _handle_monitor_multi_channel_work_current(self, case_number: str, command: str, data: str):
        """处理多通道工作电流监控"""
        self.work_current_monitor_manager.handle_monitor_multi_channel_work_current(case_number, command, data)
    
    def _handle_execute_loop_power_on_off(self, case_number: str, command: str, data: str):
        """处理循环电源开关"""
        power_on_volt = float(data.split(",")[0])
        power_on_delay_min = float(data.split(",")[1])
        power_on_delay_max = float(data.split(",")[2])
        power_off_volt = float(data.split(",")[3])
        power_off_delay_min = float(data.split(",")[4])
        power_off_delay_max = float(data.split(",")[5])
        execute_times = int(data.split(",")[6])
        try:
            channel = int(data.split(",")[7])
        except Exception as e:
            logger.error(f"execute_customize_cmd exception: {str(e.args)}")
            channel = 1
        self.loop_power_on_off_manager.execute_loop(case_number, command, power_on_volt, power_on_delay_min,
                                               power_on_delay_max, power_off_volt, power_off_delay_min,
                                               power_off_delay_max, execute_times, channel)
