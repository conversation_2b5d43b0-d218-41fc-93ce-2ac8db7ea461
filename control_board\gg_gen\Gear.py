import ctypes

from .Base import gts

if gts is not None:
    gts.GTN_PrfGear.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_short]
    gts.GTN_PrfGear.restype = ctypes.c_short


def GTN_PrfGear(core, profile, dir=0):
    return gts.GTN_PrfGear(core, profile, dir)


if gts is not None:
    gts.GTN_SetGearMaster.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_short, ctypes.c_short, ctypes.c_short]
    gts.GTN_SetGearMaster.restype = ctypes.c_short


def GTN_SetGearMaster(core, profile, masterIndex, masterType=2, masterItem=0):
    return gts.GTN_SetGearMaster(core, profile, masterIndex, masterType, masterItem)
