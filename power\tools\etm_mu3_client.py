import time
import traceback

import serial
from modbus_tk import defines
from modbus_tk.modbus_rtu import RtuMaster

from common.LogUtils import logger
from utils.SignalsManager import signals_manager


class ETMMU3Client:

    def __init__(self):
        self.device_name = None
        self.port = None
        self.baudrate = None
        self.bytesize = None
        self.parity = None
        self.stopbits = None
        self.slave_id = None
        self.timeout = None
        self._is_open = False
        self._master = None
        self._interrupt = False

    def set_interrupt(self, interrupt):
        self._interrupt = interrupt

    @property
    def is_open(self):
        return self._is_open

    def open(self, device_name, port, baudrate=9600, bytesize=8, parity="N", stopbits=1, slave_id=1, timeout=5.0):
        self.device_name = device_name
        self.port = port
        self.baudrate = baudrate
        self.bytesize = bytesize
        self.parity = parity
        self.stopbits = stopbits
        self.slave_id = slave_id
        self.timeout = timeout

        if not self._is_open:
            try:
                if self._master:
                    self._master.close()
                self._master = RtuMaster(
                    serial.Serial(port=self.port, baudrate=self.baudrate, bytesize=self.bytesize,
                                  parity=self.parity, stopbits=self.stopbits, timeout=2)
                )
                self._master.set_timeout(self.timeout)
                self._master.open()
                self._is_open = True
            except Exception as e:
                logger.error(f"open exception: {str(e.args)}")
                self._is_open = False

        signals_manager.update_device_status_signal.emit(device_name, self._is_open)
        return self._is_open

    def close(self):
        if self._master:
            try:
                self._master.close()
            except Exception as e:
                logger.error(f"close exception: {str(e.args)}")

        self._master = None
        self._is_open = False

        return True

    def execute(self, function_code, starting_address, quantity_of_x=0, output_value=0,
                data_format="", expected_length=-1, write_starting_address_fc23=0):
        if not self._master:
            return None

        # 最多尝试两次
        for attempt in range(3):
            try:
                r = self._master.execute(self.slave_id, function_code, starting_address, quantity_of_x, output_value,
                                       data_format, expected_length, write_starting_address_fc23)
                return r
            except Exception as e:
                logger.error(f"execute exception (attempt {attempt + 1}/3): {str(e.args)}")
                
                if not self._interrupt:     #初始1次 + 重试2次
                    self.close()
                    logger.info("attempt reconnect...")
                    time.sleep(0.5)
                    
                    # 尝试重新打开连接
                    if self.open(self.device_name, self.port, self.baudrate, self.bytesize, 
                               self.parity, self.stopbits, self.slave_id, self.timeout):
                        time.sleep(0.2)
                        continue
                    else:
                        logger.error("reconnect failed")
                else:
                    logger.error(f"retry interrupted {self._interrupt}")
                    break
                
        logger.error("reach max retry times")
        return None

    def read_hr_one(self, starting_address):
        """保持寄存器单个读取"""
        r = self.execute(defines.READ_HOLDING_REGISTERS, starting_address, 1)
        if r is None:
            return None
        return r[0]

    def read_hr_many(self, starting_address, quantity_of_x):
        """保持寄存器多个读取"""
        r = self.execute(defines.READ_HOLDING_REGISTERS, starting_address, quantity_of_x)
        return r

    def write_hr_one(self, starting_address, value):
        """保持寄存器单个写入"""
        r = self.execute(defines.WRITE_SINGLE_REGISTER, starting_address, 1, value)
        return r

    def write_hr_many(self, starting_address, data, data_format=""):
        """保持寄存器多个写入"""
        r = self.execute(defines.WRITE_MULTIPLE_REGISTERS, starting_address=starting_address,
                         output_value=data, data_format=data_format)
        return r

    def switch(self, flag):
        if flag:
            self.write_hr_one(0, 1)
        else:
            self.write_hr_one(0, 0)

    def get_switch_status(self):
        return self.read_hr_one(1)

    def switch_ch1(self, flag):
        if flag:
            self.write_hr_one(0x64, 1)
        else:
            self.write_hr_one(0x64, 0)

    def switch_ch2(self, flag):
        if flag:
            self.write_hr_one(0xc8, 1)
        else:
            self.write_hr_one(0xc8, 0)

    def switch_ch3(self, flag):
        if flag:
            self.write_hr_one(0x12c, 1)
        else:
            self.write_hr_one(0x12c, 0)

    def set_volt_ch1(self, volt):
        volt = int(volt * 100)
        return self.write_hr_one(0x6d, volt)

    def get_volt_ch1(self):
        r = self.read_hr_one(0x6d)
        r = r / 100.0
        return r

    def set_curr_ch1(self, curr):
        curr = int(curr * 1000)
        return self.write_hr_one(0x6e, curr)

    def get_curr_ch1(self):
        r = self.read_hr_one(0x6e)
        r = r / 1000.0
        return r

    def get_volt_p_ch1(self):
        r = self.read_hr_one(0x69)
        r = r / 100.0
        return r

    def get_curr_p_ch1(self):
        logger.debug(f"get_curr_p_ch1")
        r = self.read_hr_one(0x6a)
        if r is None:
            return None
        r = r / 1000.0
        return r

    def set_ovp_ch1(self, volt):
        volt = int(volt * 100)
        self.write_hr_one(0x6f, volt)

    def switch_ovp_ch1(self, flag):
        if flag:
            self.write_hr_one(0x70, 1)
        else:
            self.write_hr_one(0x70, 0)

    def set_ocp_ch1(self, curr):
        curr = int(curr * 1000)
        self.write_hr_one(0x71, curr)

    def switch_ocp_ch1(self, flag):
        if flag:
            self.write_hr_one(0x72, 1)
        else:
            self.write_hr_one(0x72, 0)

    def set_volt_ch2(self, volt):
        volt = int(volt * 100)
        return self.write_hr_one(0xd1, volt)

    def get_volt_ch2(self):
        r = self.read_hr_one(0xd1)
        r = r / 100.0
        return r

    def set_curr_ch2(self, curr):
        curr = int(curr * 1000)
        return self.write_hr_one(0xd2, curr)

    def get_curr_ch2(self):
        r = self.read_hr_one(0xd2)
        r = r / 1000.0
        return r

    def get_volt_p_ch2(self):
        r = self.read_hr_one(0xcd)
        r = r / 100.0
        return r

    def get_curr_p_ch2(self):
        r = self.read_hr_one(0xce)
        if r is None:
            return None
        r = r / 1000.0
        return r

    def set_volt_ch3(self, volt):
        volt = int(volt * 100)
        return self.write_hr_one(0x135, volt)

    def get_volt_ch3(self):
        r = self.read_hr_one(0x135)
        r = r / 100.0
        return r

    def set_curr_ch3(self, curr):
        curr = int(curr * 1000)
        return self.write_hr_one(0x136, curr)

    def get_curr_ch3(self):
        r = self.read_hr_one(0x136)
        r = r / 1000.0
        return r

    def get_volt_p_ch3(self):
        r = self.read_hr_one(0x131)
        r = r / 100.0
        return r

    def get_curr_p_ch3(self):
        r = self.read_hr_one(0x132)
        if r is None:
            return None
        r = r / 1000.0
        return r


client: ETMMU3Client = ETMMU3Client()

