# -*- coding: utf-8 -*-
"""
user:Created by jid on 2021/1/29
email:<EMAIL>
description:
"""
import operator
import os
import xml.etree.ElementTree as ET

from utils import get_workspace_disk_partition
from adb import xml_indent

class AppConfig:

    def __init__(self):
        super(AppConfig, self).__init__()
        disk_device = get_workspace_disk_partition()
        if disk_device is None:
            self.work_folder = os.path.join(os.path.expanduser('~'), 'HWTreeATE')
        else:
            self.work_folder = os.path.join(disk_device, 'HWTreeATE')
        self.exception_folder = os.path.join(self.work_folder, 'exception')
        self.config_folder = os.path.join(self.work_folder, 'config')
        self.log_folder = os.path.join(self.work_folder, 'log')
        self.vision_folder = os.path.join(self.work_folder, 'vision')
        self.system_config = os.path.join(self.config_folder, "SystemConfig.xml")

    def load_global_config(self):
        if not os.path.exists(self.work_folder):
            self.makedirs_safe(self.work_folder)
        if not os.path.exists(self.exception_folder):
            self.makedirs_safe(self.exception_folder)
        if not os.path.exists(self.config_folder):
            self.makedirs_safe(self.config_folder)
        if not os.path.exists(self.log_folder):
            self.makedirs_safe(self.log_folder)
        if not os.path.exists(self.vision_folder):
            self.makedirs_safe(self.vision_folder)

        self.init_system_config()

    def makedirs_safe(self, path):
        if not os.path.exists(path):
            try:
                os.makedirs(path)
            except Exception as e:
                from common.LogUtils import logger
                logger.error(f"make dirs error: {e}")
            

    def init_system_config(self):
        from common.LogUtils import logger
        from utils.ProjectManager import project_manager
        logger.info("init_system_config")
        try:
            if not os.path.exists(self.system_config):
                root = ET.Element('system')
                root.set("version", "1")
                logger.info(f"init_system_config machine_number={project_manager.get_machine_number()}")
                machine_config = ET.SubElement(root, 'component', attrib={'type': "machine_config"})
                machine_number = ET.SubElement(machine_config, 'machine_number')
                compress_config = ET.SubElement(root, 'component', attrib={'type': "compress_config"})
                video_compress = ET.SubElement(compress_config, 'video_compress')
                if operator.eq("", project_manager.get_machine_number()):
                    machine_number.text = "HW-T-0000"
                else:
                    machine_number.text = project_manager.get_machine_number()
                if operator.eq("", project_manager.get_video_compress()):
                    video_compress.text = "0"
                else:
                    video_compress.text = project_manager.get_video_compress()

                tree = ET.ElementTree(root)
                xml_indent(root, space="    ")
                tree.write(self.system_config, encoding='utf-8', xml_declaration=True)
        except Exception as e:
            logger.error("init_system_config exception: {}".format(str(e.args)))


app_config: AppConfig = AppConfig()
