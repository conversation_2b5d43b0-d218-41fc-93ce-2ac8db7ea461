import threading

import serial.tools.list_ports
from PyQt5.QtCore import QTimer, pyqtSignal
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QPushButton

from common.LogUtils import logger
from .ServiceWidget import ServiceWidget
from .ui.enduranceWidget import Ui_Form
from ..RelayClient import relay_client
from ..constants import PARITY_MAP, PARITY_MAP2, STOPBITS_MAP, STOPBITS_MAP2, CONF, update_conf


class EnduranceWidget(QWidget, Ui_Form):
    status_check = pyqtSignal(object)

    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setupUi(self)
        self.setWindowTitle("继电器工具")
        self.comboBox_baudrate.addItems(["4800", "9600", "14400", "19200", "38400", "56000", "57600", "115200"])
        self.comboBox_parity.addItems(list(PARITY_MAP.keys()))
        self.comboBox_bytesize.addItems(["5", "6", "7", "8"])
        self.comboBox_stopbits.addItems(list(STOPBITS_MAP.keys()))
        self.services = []
        self.pushButton_add = QPushButton("增加", self)
        self.service_layout = None
        self.timer1 = QTimer()
        self.timer1.timeout.connect(self.update_relays_status)
        # self.timer1.start(1000)
        self.timer2 = QTimer()
        self.timer2.singleShot(1000, self.get_available_ports)
        self.timer2.start()
        from functools import partial
        for channel in range(1, 17):
            getattr(self, f"pushButton_y{channel}_on").clicked.connect(
                partial(lambda channel, _: self.set_relay_status(channel, True), channel)
            )
            getattr(self, f"pushButton_y{channel}_off").clicked.connect(
                partial(lambda channel, _: self.set_relay_status(channel, False), channel)
            )

        self.pushButton_add.clicked.connect(self.add_service)
        self.pushButton_conf_update.clicked.connect(self.update_conf)
        self.pushButton_conf_save.clicked.connect(self.save_conf)
        self.pushButton_connect.clicked.connect(self.connect)
        self.update_conf()
        self.create_services()
        self.status_check.connect(self.update_status_label)

    def add_service(self):
        logger.info("add_service")
        if len(self.services) >= 8:
            return
        s = ServiceWidget(endurance_w=self)
        self.services.append(s)
        self.service_layout.insertWidget(len(self.services), s)

    def update_status_label(self, r):
        logger.debug("update_status_label r={}".format(r))
        if r is None:
            self.lineEdit_status.setText("设备未连接")
            for i in range(16):
                getattr(self, f"label_y{i + 1}_status").setStyleSheet("background:grey")
        else:
            self.lineEdit_status.setText("设备已连接")
            for i in range(16):
                y = r[i]
                if y:
                    getattr(self, f"label_y{i + 1}_status").setStyleSheet("background:green")
                else:
                    getattr(self, f"label_y{i + 1}_status").setStyleSheet("background:red")

    def connect(self):
        logger.info("connect")
        text = self.pushButton_connect.text()
        if text == "连接":
            port = self.comboBoxComPort.currentText()
            baudrate = self.comboBox_baudrate.currentText()
            baudrate = int(baudrate)
            bytesize = self.comboBox_bytesize.currentText()
            bytesize = int(bytesize)
            parity = self.comboBox_parity.currentText()
            parity = PARITY_MAP.get(parity)
            stopbits = self.comboBox_stopbits.currentText()
            stopbits = STOPBITS_MAP.get(stopbits)
            slave_id = self.spinBox_slave_id.value()

            status = relay_client.open(port=port, baudrate=baudrate, bytesize=bytesize, parity=parity,
                                       stopbits=stopbits, slave_id=slave_id)
            logger.info("connect status={}".format(status))
            if status:
                self.timer1.start(1000)
                self.pushButton_connect.setText("断开")
        else:
            status = relay_client.close()
            if status:
                self.pushButton_connect.setText("连接")

    def update_conf(self):
        logger.info("update_conf")
        baudrate = CONF.get("baudrate")
        bytesize = CONF.get("bytesize")
        parity = CONF.get("parity")
        stopbits = CONF.get("stopbits")
        slave_id = CONF.get("slave_id")
        self.get_available_ports()
        self.comboBox_baudrate.setCurrentText(str(baudrate))
        self.comboBox_parity.setCurrentText(PARITY_MAP2.get(parity))
        self.comboBox_bytesize.setCurrentText(str(bytesize))
        self.comboBox_stopbits.setCurrentText(STOPBITS_MAP2.get(stopbits))
        self.spinBox_slave_id.setValue(slave_id)

    def create_services(self):
        logger.info("create_services")
        widget = QWidget()
        self.service_layout = QVBoxLayout(widget)
        self.service_layout.addWidget(self.pushButton_add)
        s = ServiceWidget(endurance_w=self)
        self.services.append(s)
        self.service_layout.addWidget(s)
        self.service_layout.addStretch()
        self.scrollArea.setWidget(widget)

    def save_conf(self):
        logger.info("save_conf")
        port = self.comboBoxComPort.currentText()
        baudrate = self.comboBox_baudrate.currentText()
        baudrate = int(baudrate)
        bytesize = self.comboBox_bytesize.currentText()
        bytesize = int(bytesize)
        parity = self.comboBox_parity.currentText()
        parity = PARITY_MAP.get(parity)
        stopbits = self.comboBox_stopbits.currentText()
        stopbits = STOPBITS_MAP.get(stopbits)
        slave_id = self.spinBox_slave_id.value()

        update_conf({
            "port": port,
            "baudrate": baudrate,
            "bytesize": bytesize,
            "parity": parity,
            "stopbits": stopbits,
            "slave_id": slave_id,
        })

    def update_relays_status(self):
        logger.debug("update_relays_status")
        try:
            threading.Thread(target=self.get_relays_status).start()
        except Exception as e:
            logger.error(f"update_relays_status exception: {str(e.args)}")

    def get_relays_status(self):
        r = relay_client.get_relays_status()
        self.status_check.emit(r)

    @staticmethod
    def set_relay_status(channel, status):
        threading.Thread(target=relay_client.set_relay_status, args=(channel, status)).start()

    def on_y1_on(self):
        relay_client.set_relay1_status(True)

    def on_y2_on(self):
        relay_client.set_relay2_status(True)

    def on_y3_on(self):
        relay_client.set_relay3_status(True)

    def on_y4_on(self):
        relay_client.set_relay4_status(True)

    def on_y5_on(self):
        relay_client.set_relay5_status(True)

    def on_y6_on(self):
        relay_client.set_relay6_status(True)

    def on_y7_on(self):
        relay_client.set_relay7_status(True)

    def on_y8_on(self):
        relay_client.set_relay8_status(True)

    def on_y1_off(self):
        relay_client.set_relay1_status(False)

    def on_y2_off(self):
        relay_client.set_relay2_status(False)

    def on_y3_off(self):
        relay_client.set_relay3_status(False)

    def on_y4_off(self):
        relay_client.set_relay4_status(False)

    def on_y5_off(self):
        relay_client.set_relay5_status(False)

    def on_y6_off(self):
        relay_client.set_relay6_status(False)

    def on_y7_off(self):
        relay_client.set_relay7_status(False)

    def on_y8_off(self):
        relay_client.set_relay8_status(False)

    def get_available_ports(self):
        """
        读取串口号
        :return:
        """
        # 获取可用串口的端口号
        available_ports = serial.tools.list_ports.comports()
        self.comboBoxComPort.clear()
        # 打印可用串口的端口号
        port_list = []
        for port in available_ports:
            port_list.append(port.device)
        self.comboBoxComPort.addItems(port_list)
        return port_list
