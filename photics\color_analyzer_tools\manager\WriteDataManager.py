# -*- coding: utf-8 -*-
"""
user:Created by jid on 2021/3/22 15:07
email:<EMAIL>
description:
"""
from zlib import crc32

from common.LogUtils import logger
from photics.color_analyzer_tools.manager import MeasureType
from photics.color_analyzer_tools.manager.SignalCenter import signal_center


class WriteDataManager:

    def __init__(self):
        super(WriteDataManager, self).__init__()
        # self.data = [0x20, 0x00, 0xFF, 0x00, 0x00, 0x01, 0x00, 0x40, 0x17, 0x02, 0x50, 0x36, 0x0C, 0x10, 0xDD, 0x0F,
        #              0x41, 0x0A, 0x12, 0x01, 0x35, 0x14, 0xA1, 0x5C, 0x17, 0x01, 0x82, 0x19, 0x51, 0xA6, 0x1B, 0x71,
        #              0xC8, 0x1D, 0x81, 0xE8, 0x1F, 0x92, 0x08, 0x21, 0x72, 0x27, 0x23, 0x62, 0x45, 0x25, 0x32, 0x62,
        #              0x27, 0x02, 0x7E, 0x28, 0xC2, 0x99, 0x2A, 0x72, 0xB4, 0x2C, 0x12, 0xCE, 0x2D, 0xB2, 0xE8, 0x2F,
        #              0x53, 0x01, 0x30, 0xD3, 0x1A, 0x32, 0x63, 0x32, 0x33, 0xF3, 0x4A, 0x35, 0x73, 0x63, 0x36, 0xF3,
        #              0x7A, 0x38, 0x63, 0x93, 0x39, 0xE3, 0xAA, 0x3B, 0x63, 0xC1, 0x3C, 0xD3, 0xD7, 0x3E, 0x33, 0xEE,
        #              0x3F, 0x94, 0x05, 0x40, 0xF4, 0x1B, 0x42, 0x64, 0x31, 0x43, 0xC4, 0x46, 0x45, 0x24, 0x5C, 0x46,
        #              0x74, 0x72, 0x47, 0xC4, 0x86, 0x49, 0x24, 0x9C, 0x4A, 0x74, 0xB2, 0x4B, 0xD4, 0xC8, 0x4D, 0x44,
        #              0xDE, 0x4E, 0x94, 0xF5, 0x50, 0x15, 0x0C, 0x51, 0x65, 0x21, 0x52, 0xD5, 0x36, 0x54, 0x15, 0x4B,
        #              0x55, 0x65, 0x60, 0x56, 0xA5, 0x74, 0x57, 0xF5, 0x89, 0x59, 0x35, 0x9E, 0x5A, 0x85, 0xB3, 0x5B,
        #              0xD5, 0xC7, 0x5D, 0x25, 0xDC, 0x5E, 0x65, 0xEF, 0x5F, 0x96, 0x03, 0x60, 0xD6, 0x16, 0x61, 0xF6,
        #              0x2A, 0x63, 0x46, 0x3E, 0x64, 0x96, 0x53, 0x65, 0xE6, 0x67, 0x67, 0x26, 0x7C, 0x68, 0x76, 0x90,
        #              0x69, 0xB6, 0xA5, 0x6B, 0x06, 0xBA, 0x6C, 0x46, 0xCF, 0x6D, 0x96, 0xE3, 0x6E, 0xD6, 0xF8, 0x70,
        #              0x17, 0x0C, 0x71, 0x67, 0x20, 0x72, 0xA7, 0x34, 0x73, 0xE7, 0x48, 0x75, 0x27, 0x5B, 0x76, 0x57,
        #              0x6F, 0x77, 0x97, 0x82, 0x78, 0xC7, 0x96, 0x7A, 0x07, 0xAA, 0x7B, 0x47, 0xBD, 0x7C, 0x77, 0xD1,
        #              0x7D, 0xA7, 0xE5, 0x7E, 0xF7, 0xF8, 0x80, 0x28, 0x0D, 0x81, 0x88, 0x24, 0x82, 0xE8, 0x3A, 0x84,
        #              0x58, 0x50, 0x85, 0x21, 0x00, 0xFF, 0xB8, 0x67, 0x87, 0x28, 0x7D, 0x88, 0x78, 0x92, 0x89, 0xD8,
        #              0xA7, 0x8B, 0x18, 0xBC, 0x8C, 0x78, 0xD1, 0x8D, 0xC8, 0xE6, 0x8F, 0x18, 0xFC, 0x90, 0x69, 0x11,
        #              0x91, 0xD9, 0x27, 0x93, 0x29, 0x3C, 0x94, 0x79, 0x52, 0x95, 0xD9, 0x67, 0x97, 0x19, 0x7C, 0x98,
        #              0x69, 0x90, 0x99, 0xA9, 0xA5, 0x9A, 0xF9, 0xB9, 0x9C, 0x39, 0xCD, 0x9D, 0x89, 0xE2, 0x9E, 0xD9,
        #              0xF7, 0xA0, 0x2A, 0x0C, 0xA1, 0x7A, 0x22, 0xA2, 0xDA, 0x37, 0xA4, 0x2A, 0x4D, 0xA5, 0x7A, 0x63,
        #              0xA6, 0xDA, 0x78, 0xA8, 0x3A, 0x8E, 0xA9, 0x9A, 0xA4, 0xAA, 0xFA, 0xBA, 0xAC, 0x5A, 0xCF, 0xAD,
        #              0xBA, 0xE6, 0xAF, 0x0A, 0xFB, 0xB0, 0x6B, 0x11, 0xB1, 0xCB, 0x27, 0xB3, 0x2B, 0x3E, 0xB4, 0x9B,
        #              0x56, 0xB6, 0x2B, 0x6F, 0xB7, 0xAB, 0x87, 0xB9, 0x3B, 0xA0, 0xBA, 0xEB, 0xBB, 0xBC, 0x8B, 0xD6,
        #              0xBE, 0x4B, 0xF1, 0xBF, 0xEC, 0x14, 0x00, 0x00, 0x01, 0x00, 0x50, 0x1B, 0x02, 0xB0, 0x3E, 0x0D,
        #              0xC0, 0xFB, 0x11, 0x51, 0x2E, 0x14, 0x71, 0x5F, 0x17, 0x61, 0x8B, 0x1A, 0x11, 0xB5, 0x1C, 0xA1,
        #              0xDD, 0x1F, 0x12, 0x04, 0x21, 0x62, 0x29, 0x23, 0xB2, 0x4D, 0x25, 0xF2, 0x6F, 0x28, 0x02, 0x90,
        #              0x2A, 0x12, 0xB0, 0x2C, 0x02, 0xD0, 0x2D, 0xF2, 0xEF, 0x2F, 0xE3, 0x0D, 0x31, 0xC3, 0x2A, 0x33,
        #              0x93, 0x47, 0x35, 0x63, 0x64, 0x37, 0x33, 0x81, 0x38, 0xF3, 0x9D, 0x3A, 0xB3, 0xB9, 0x3C, 0x73,
        #              0xD5, 0x3E, 0x23, 0xF0, 0x3F, 0xE4, 0x0B, 0x41, 0x84, 0x25, 0x43, 0x34, 0x40, 0x44, 0xD4, 0x5A,
        #              0x46, 0x74, 0x74, 0x48, 0x14, 0x8D, 0x49, 0xA4, 0xA6, 0x4B, 0x34, 0xC1, 0x4C, 0xE4, 0xDC, 0x4E,
        #              0x94, 0xF7, 0x50, 0x45, 0x12, 0x51, 0xF5, 0x2C, 0x53, 0x95, 0x45, 0x55, 0x25, 0x5E, 0x56, 0xA5,
        #              0x77, 0x58, 0x35, 0x8F, 0x59, 0xC5, 0x22, 0x00, 0xFF, 0xA8, 0x5B, 0x45, 0xC0, 0x5C, 0xC5, 0xD8,
        #              0x5E, 0x45, 0xF1, 0x5F, 0xC6, 0x09, 0x61, 0x46, 0x20, 0x62, 0xC6, 0x39, 0x64, 0x46, 0x50, 0x65,
        #              0xD6, 0x68, 0x67, 0x46, 0x80, 0x68, 0xC6, 0x98, 0x6A, 0x56, 0xB1, 0x6B, 0xE6, 0xCB, 0x6D, 0x86,
        #              0xE4, 0x6F, 0x16, 0xFE, 0x70, 0xB7, 0x16, 0x72, 0x27, 0x2E, 0x73, 0x97, 0x44, 0x75, 0x07, 0x5B,
        #              0x76, 0x77, 0x72, 0x77, 0xD7, 0x89, 0x79, 0x57, 0xA1, 0x7A, 0xD7, 0xBA, 0x7C, 0x67, 0xD2, 0x7D,
        #              0xE7, 0xEA, 0x7F, 0x68, 0x02, 0x80, 0xF8, 0x1C, 0x82, 0x98, 0x36, 0x84, 0x38, 0x50, 0x85, 0xD8,
        #              0x69, 0x87, 0x68, 0x83, 0x89, 0x08, 0x9C, 0x8A, 0x98, 0xB6, 0x8C, 0x38, 0xCF, 0x8D, 0xC8, 0xE8,
        #              0x8F, 0x59, 0x02, 0x90, 0xF9, 0x1C, 0x92, 0x99, 0x35, 0x94, 0x29, 0x4F, 0x95, 0xC9, 0x68, 0x97,
        #              0x49, 0x7F, 0x98, 0xB9, 0x97, 0x9A, 0x39, 0xAF, 0x9B, 0xA9, 0xC6, 0x9D, 0x29, 0xDD, 0x9E, 0xA9,
        #              0xF7, 0xA0, 0x3A, 0x10, 0xA1, 0xDA, 0x29, 0xA3, 0x6A, 0x43, 0xA4, 0xFA, 0x5C, 0xA6, 0x9A, 0x75,
        #              0xA8, 0x2A, 0x8E, 0xA9, 0xAA, 0xA7, 0xAB, 0x3A, 0xC0, 0xAC, 0xDA, 0xD9, 0xAE, 0x5A, 0xF1, 0xAF,
        #              0xDB, 0x09, 0xB1, 0x5B, 0x21, 0xB2, 0xDB, 0x39, 0xB4, 0x5B, 0x52, 0xB6, 0x0B, 0x6D, 0xB7, 0x9B,
        #              0x87, 0xB9, 0x4B, 0xA1, 0xBA, 0xEB, 0xBC, 0xBC, 0x9B, 0xD6, 0xBE, 0x2B, 0xEF, 0xBF, 0xCC, 0x09,
        #              0xC1, 0x6C, 0x23, 0xC3, 0x0C, 0x3D, 0xC4, 0xBC, 0x59, 0xC6, 0x6C, 0x73, 0xC8, 0x1C, 0x8F, 0xC9,
        #              0xCC, 0xAA, 0xCB, 0x7C, 0xC4, 0xCD, 0x2C, 0xE0, 0xCE, 0xDC, 0xFB, 0xD0, 0x8D, 0x16, 0xD2, 0x3D,
        #              0x30, 0xD3, 0xED, 0x4B, 0xD5, 0x8D, 0x65, 0xD7, 0x3D, 0x80, 0xD8, 0xDD, 0x9A, 0xDA, 0x8D, 0xB6,
        #              0xDC, 0x4D, 0xD2, 0xDD, 0xFD, 0xED, 0xDF, 0xBE, 0x14, 0x23, 0x00, 0xFF, 0x00, 0x00, 0x01, 0x00,
        #              0x50, 0x1D, 0x02, 0xF0, 0x44, 0x0F, 0x11, 0x13, 0x12, 0xF1, 0x4A, 0x16, 0x51, 0x7F, 0x19, 0x91,
        #              0xB0, 0x1C, 0x81, 0xDE, 0x1F, 0x62, 0x0B, 0x22, 0x12, 0x37, 0x24, 0xB2, 0x60, 0x27, 0x42, 0x87,
        #              0x29, 0xB2, 0xAD, 0x2C, 0x02, 0xD2, 0x2E, 0x52, 0xF6, 0x30, 0x83, 0x1A, 0x32, 0xB3, 0x3C, 0x34,
        #              0xD3, 0x5E, 0x36, 0xF3, 0x7F, 0x39, 0x03, 0xA0, 0x3B, 0x13, 0xC1, 0x3D, 0x13, 0xE1, 0x3F, 0x14,
        #              0x01, 0x41, 0x14, 0x20, 0x43, 0x04, 0x40, 0x44, 0xE4, 0x5D, 0x46, 0xD4, 0x7C, 0x48, 0xA4, 0x99,
        #              0x4A, 0x84, 0xB8, 0x4C, 0x84, 0xD7, 0x4E, 0x84, 0xF7, 0x50, 0x75, 0x17, 0x52, 0x65, 0x35, 0x54,
        #              0x45, 0x53, 0x56, 0x15, 0x6F, 0x57, 0xD5, 0x8C, 0x59, 0x95, 0xA8, 0x5B, 0x65, 0xC4, 0x5D, 0x35,
        #              0xE1, 0x5E, 0xF5, 0xFD, 0x60, 0xB6, 0x19, 0x62, 0x76, 0x34, 0x64, 0x36, 0x51, 0x65, 0xF6, 0x6D,
        #              0x67, 0xA6, 0x88, 0x69, 0x66, 0xA6, 0x6B, 0x46, 0xC4, 0x6D, 0x26, 0xE1, 0x6F, 0x06, 0xFF, 0x70,
        #              0xD7, 0x1B, 0x72, 0x97, 0x36, 0x74, 0x37, 0x51, 0x75, 0xE7, 0x6B, 0x77, 0x97, 0x86, 0x79, 0x57,
        #              0xA3, 0x7B, 0x17, 0xBF, 0x7C, 0xE7, 0xDC, 0x7E, 0xA7, 0xF7, 0x80, 0x68, 0x16, 0x82, 0x58, 0x33,
        #              0x84, 0x38, 0x51, 0x86, 0x18, 0x70, 0x87, 0xF8, 0x8D, 0x89, 0xC8, 0xAB, 0x8B, 0x98, 0xC9, 0x8D,
        #              0x88, 0xE6, 0x8F, 0x59, 0x04, 0x91, 0x39, 0x22, 0x93, 0x09, 0x3F, 0x94, 0xE9, 0x5D, 0x96, 0xB9,
        #              0x79, 0x98, 0x79, 0x94, 0x9A, 0x29, 0xB0, 0x9B, 0xE9, 0xCB, 0x9D, 0x99, 0xE7, 0x9F, 0x7A, 0x05,
        #              0xA1, 0x4A, 0x22, 0xA3, 0x2A, 0x40, 0xA4, 0xFA, 0x5E, 0xA6, 0xDA, 0x7B, 0xA8, 0xAA, 0x98, 0xAA,
        #              0x7A, 0xB6, 0xAC, 0x4A, 0xD2, 0xAE, 0x0A, 0xEE, 0xAF, 0xCB, 0x0A, 0xB1, 0x24, 0x00, 0x7F, 0x7B,
        #              0x26, 0xB3, 0x4B, 0x41, 0xB5, 0x1B, 0x61, 0xB6, 0xFB, 0x7F, 0xB8, 0xEB, 0x9D, 0xBA, 0xDB, 0xBC,
        #              0xBC, 0xAB, 0xD9, 0xBE, 0x8B, 0xF7, 0xC0, 0x6C, 0x14, 0xC2, 0x2C, 0x31, 0xC4, 0x1C, 0x50, 0xC6,
        #              0x1C, 0x70, 0xC8, 0x0C, 0x8F, 0xC9, 0xEC, 0xAE, 0xCB, 0xDC, 0xCD, 0xCD, 0xCC, 0xEC, 0xCF, 0xBD,
        #              0x0A, 0xD1, 0xAD, 0x29, 0xD3, 0x7D, 0x47, 0xD5, 0x6D, 0x64, 0xD7, 0x3D, 0x82, 0xD9, 0x1D, 0xA0,
        #              0xDA, 0xFD, 0xBE, 0xDC, 0xDD, 0xDD, 0xDE, 0xCD, 0xFC, 0xE0, 0xBE, 0x1A, 0xE2, 0xAE, 0x39, 0xE4,
        #              0x8E, 0x57, 0xE6, 0x6E, 0x76, 0xE8, 0x5E, 0x93, 0xEA, 0x2E, 0xB0, 0xEB, 0xEE, 0xCD, 0xED, 0xAE,
        #              0xE9, 0xEF, 0x7F, 0x05, 0xF1, 0x3F, 0x21, 0xF2, 0xFF, 0x3C, 0xF4, 0xAF, 0x58, 0xF6, 0x5F, 0x73,
        #              0xF8, 0x1F, 0x8D, 0xF9, 0xAF, 0xA7, 0xFB, 0x3F, 0xC0, 0xFC, 0xDF, 0xD9, 0xFE, 0x6F, 0xFC, 0xFF,
        #              0xFF, 0x00, 0x97]
        self.data = []
        self.start = 0x2004
        self.packet_data = []
        self.data_start = 0
        self.crc = 0x00

    def check_crc(self, crc):
        if self.crc == crc:
            # 比对成功
            logger.info('check_crc 比对成功')
            signal_center.gamma_measure_event_signal.emit(MeasureType.CHECK_SUCCESS)

    def update_start(self):
        self.start += 256
        self.data_start += 256

    def reset_params(self):
        self.start = 0x2004
        self.data_start = 0

    def update_packet_data(self):
        if self.data_start + 256 < len(self.data):
            self.packet_data = self.data[self.data_start:self.data_start + 256]
        else:
            self.packet_data = self.data[self.data_start:]

    def update_crc(self):
        self.crc = crc32(bytes(self.data))
        logger.info('update_crc crc=%s' % hex(self.crc))
        logger.info('update_crc data_len=%s' % len(self.data))

    def write_completed(self):
        return self.data_start + 256 > len(self.data)

    def set_data(self, data):
        self.data = data


write_data_manager: WriteDataManager = WriteDataManager()
