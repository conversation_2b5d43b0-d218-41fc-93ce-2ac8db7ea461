﻿<?xml version="1.0" encoding="UTF-8"?>
<language>
    <head>
        <code>zh-CN</code>
        <name>简体中文</name>
        <provider src="XXX translation provided by XXX."></provider>
        <info src="You need to restart to make the language settings take effect!">需要重新启动以使语言设置生效！</info>
    </head>
    <vis>
        <item src="+PulseWidth">正脉宽</item>
        <item src="-PulseWidth">负脉宽</item>
        <item src="2.5~5V CMOS Compatible">兼容 2.5~5.0V CMOS</item>
        <item src="A Setting is Invalid">无效设置</item>
        <item src="About">关于</item>
        <item src="Add">增加</item>
        <item src="ALL">全部</item>
        <item src="All Protocol Analyzers">全部协议解析器</item>
        <item src="All Rights Reserved.">保留所有权利.</item>
        <item src="All time">全部时间</item>
        <item src="An error occurred">发生错误</item>
        <item src="Analyzer">解析器</item>
        <item src="Analyzers">解析器</item>
        <item src="Animate zooming">动画缩放</item>
        <item src="Are you sure to quit?">确定要退出吗？</item>
        <item src="Auto restart sampling">自动重复采样</item>
        <item src="Automatic update failed.">自动更新失败.</item>
        <item src="Timing markers">时间标尺</item>
        <item src="Bin">二进制</item>
        <item src="Binary file">二进制文件</item>
        <item src="Byte">字节</item>
        <item src="Cancel">取消</item>
        <item src="Cannot keep up with this sample rate.">无法保持当前采样率.</item>
        <item src="Channel">通道</item>
        <item src="Channel Enable Settings">通道使能设置</item>
        <item src="Channel Height">通道高度</item>
        <item src="Channel Quick Select">通道快速选择</item>
        <item src="Channel Select">通道选择</item>
        <item src="Check for Updates">检查更新</item>
        <item src="Check for updates on launch">启动时检查升级</item>
        <item src="Clear settings when the software starts next time">软件下次启动时清除设置</item>
        <item src="Close">关闭</item>
        <item src="Connected">已连接</item>
        <item src="CSV file">CSV文件</item>
        <item src="Custom I/O">自定义 I/O</item>
        <item src="Data reading">数据读取中</item>
        <item src="Data Review">数据回放</item>
        <item src="Dec">十进制</item>
        <item src="Decoded Results">解析结果</item>
        <item src="Device connect">设备连接</item>
        <item src="Device Connected">设备已连接</item>
        <item src="Device connecting, please wait">设备连接中，请稍候</item>
        <item src="Device Options">设备选项</item>
        <item src="Device Supported List">设备支持列表</item>
        <item src="Device Disconnected">设备未连接</item>
        <item src="Disable">禁止</item>
        <item src="Display">显示</item>
        <item src="Display Format">显示格式</item>
        <item src="DutyCycle">占空比</item>
        <item src="Edit">编辑</item>
        <item src="Enable">使能</item>
        <item src="Ending Time[s]">结束时间[s]</item>
        <item src="Even if the trigger is not met">即使未满足触发条件</item>
        <item src="Expected Sample Time">预期采样时间</item>
        <item src="Export">导出</item>
        <item src="Export Data">导出数据</item>
        <item src="Export File Select">导出文件选择</item>
        <item src="Falling Edge">下降沿</item>
        <item src="FallTime">下降时间</item>
        <item src="Frequency">频率</item>
        <item src="File open failed.">文件打开失败.</item>
        <item src="File save failed.">文件保存失败.</item>
        <item src="Find a new version: ">发现新版本: </item>
        <item src="for more information.">获取更多信息.</item>
        <item src="Glitch Filter Settings">毛刺过滤设置</item>
        <item src="Hex">十六进制</item>
        <item src="I/O Standard">I/O电平标准</item>
        <item src="In waveform window">在波形窗口内</item>
        <item src="In status bar">在状态栏内</item>
        <item src="It's the latest version, no updates required.">目前已是最新版本，无需更新.</item>
        <item src="Item Select">项目选择</item>
        <item src="Keep exactly the same position as before (if possible)">保持与前次相同的位置(可能的话)</item>
        <item src="Keep settings when the software starts next time">软件下次启动时保持本次设置</item>
        <item src="Kingst Electronics Co., Ltd.">青岛金思特电子有限公司</item>
        <item src="Language">语言</item>
        <item src="Loading file, please wait">正在加载文件，请稍候</item>
        <item src="Location">定位</item>
        <item src="Logic Analyzer">逻辑分析仪</item>
        <item src="Looking for updates, please wait">正在检查更新，请稍候</item>
        <item src="Maximum sample rate is">最高采样频率为</item>
        <item src="Measurements">测量</item>
        <item src="Model">型号</item>
        <item src="More Analyzers">更多解析器</item>
        <item src="More Settings">更多设置</item>
        <item src="Move next edge to center">移动后一个边沿到中心</item>
        <item src="Move previous edge to center">移动前一个边沿到中心</item>
        <item src="Negative Pulse">负脉冲</item>
        <item src="None">空</item>
        <item src="Normal Mode">正常模式</item>
        <item src="Not Supported">不支持</item>
        <item src="OK">确定</item>
        <item src="Only use the computer's memory to store the sampled data, the data is uploaded in real time.">仅使用电脑内存存储采样数据，数据需实时上传至电脑。</item>
        <item src="Only when the trigger is met">仅当满足触发条件时</item>
        <item src="Open">打开</item>
        <item src="Open File">打开文件</item>
        <item src="Options">选项</item>
        <item src="Out of memory, Sample abort.">内存不足，采样中止.</item>
        <item src="Overshoot">过冲</item>
        <item src="Period">周期</item>
        <item src="Place this setting on the toolbar.">将此设置放到工具栏上.</item>
        <item src="Please click the link below to download.">请点击以下链接下载.</item>
        <item src="Please enter an integer in the range">请输入范围内的整数</item>
        <item src="Please select at least one channel.">请至少选择一个通道。</item>
        <item src="Please try to reduce the sample rate.">请尝试降低采样率.</item>
        <item src="Positive Pulse">正脉冲</item>
        <item src="Pre-sampling">前置采样</item>
        <item src="Preshoot">预冲</item>
        <item src="Pulse Counters">脉冲计数</item>
        <item src="PWM Settings">PWM设置</item>
        <item src="Quitting can lead to unpredictable errors.">退出可能导致不可预知的错误.</item>
        <item src="Remove">移除</item>
        <item src="Remove all">全部移除</item>
        <item src="Reset All Channels">重置所有通道</item>
        <item src="RiseTime">上升时间</item>
        <item src="Rising Edge">上升沿</item>
        <item src="Sample Depth">采样深度</item>
        <item src="Sample Depth (samples per acquisition)">采样深度(每次采集的样点数)</item>
        <item src="Sample Mode">采样模式</item>
        <item src="Sample Mode Select">采样模式选择</item>
        <item src="Sample Rate">采样率</item>
        <item src="Sample Rate (samples per second)">采样率(每秒钟的采样次数)</item>
        <item src="Sample start">采样启动</item>
        <item src="Samples">采样周期</item>
        <item src="Sampling">采样中</item>
        <item src="Sampling progress display">采样进度显示</item>
        <item src="Save Data">保存数据</item>
        <item src="Save File">保存文件</item>
        <item src="Saving file, please wait">正在保存文件，请稍候</item>
        <item src="Save Settings">保存设置</item>
        <item src="Search">搜索</item>
        <item src="Select">选择</item>
        <item src="Show Real Time Status">显示实时状态</item>
        <item src="Show time zero, with previous zoom level">保持前次缩放级别，显示时间零点</item>
        <item src="Sorry, timing marker pairs has reached the maximum number.">抱歉，时间标尺已达到最大数量.</item>
        <item src="Sorry, there is no measurable data.">抱歉，没有可供测量的数据.</item>
        <item src="Sorry, there is no data that can be exported.">抱歉，没有可以导出的数据.</item>
        <item src="Sorry, there is no data that can be saved.">抱歉，没有可以保存的数据.</item>
        <item src="Specify time interval">指定时间间隔</item>
        <item src="Start single sampling">启动单次采样</item>
        <item src="Starting Time[s]">起始时间[s]</item>
        <item src="Stop">停止</item>
        <item src="Stop sampling">停止采样</item>
        <item src="Stream Mode">流模式</item>
        <item src="System Information">系统信息</item>
        <item src="Text file">文本文件</item>
        <item src="The effective range of threshold voltage is ">阈值电压的有效范围为 </item>
        <item src="The effective range of PWM:[0.1Hz,20MHz], duty cycle:[0,1].">PWM频率有效范围: [0.1Hz, 20MHz], 占空比范围: [0,1].</item>
        <item src="The update is not complete.">更新尚未完成.</item>
        <item src="This mode can provide higher sample rate.">此模式可提供更高的采样率。</item>
        <item src="This mode can provide larger sample depth.">此模式可提供更大的采样深度。</item>
        <item src="Time Select">时间选择</item>
        <item src="Timing Markers">时间标尺</item>
        <item src="Timing Marker Pairs">时间标尺</item>
        <item src="Trigger Position">触发位置</item>
        <item src="Trigger position centered">触发位置居中</item>
        <item src="Trigger position setting failed.">触发位置设置失败。</item>
        <item src="Disconnected">未连接</item>
        <item src="Update check failed.">检查更新失败.</item>
        <item src="Update Logs">更新日志</item>
        <item src="Update now?">现在更新?</item>
        <item src="Updates">更新</item>
        <item src="Use Color for Waveform">使用彩色波形线</item>
        <item src="Use the device's memory as the sample buffer, the data is uploaded to computer after sampled.">使用设备内部存储器作为采样缓冲区，数据在采样完成后上传至电脑。</item>
        <item src="User Guide">用户手册</item>
        <item src="Vamp">幅度值</item>
        <item src="Vavg">平均值</item>
        <item src="Vbase">低端值</item>
        <item src="Version: ">版本: </item>
        <item src="View state after new sampling">采样后的视图状态</item>
        <item src="Visit">访问</item>
        <item src="Data File">数据文件</item>
        <item src="Settings file">设置文件</item>
        <item src="Vmax">最大值</item>
        <item src="Vmin">最小值</item>
        <item src="Vpp">峰峰值</item>
        <item src="Vrms">均方根值</item>
        <item src="Vth:">阈值电压:</item>
        <item src="Vtop">顶端值</item>
        <item src="Waiting for trigger">等待触发</item>
        <item src="Warning">警告</item>
        <item src="When a channel is checked, pulses less than the set width will be filtered out.">勾选某一通道后，小于设定宽度的脉冲将被滤除。</item>
        <item src="Width">脉宽</item>
        <item src="You may need to install a PDF reader to open the user guide.">您可能需要安装PDF阅读器以打开用户手册.</item>
        <item src="Zoom all the way out">缩放到一屏</item>
        <item src="Zoom in">放大</item>
        <item src="Zoom out">缩小</item>
        <item src="Zoom to full screen">缩放至整屏</item>
    </vis>
    <analyzers>
        <item class="1-Wire" src="Maxim 1-Wire Interface">Maxim 1-Wire 接口</item>
        <item class="1-Wire" src="Overdrive only mode">高速模式</item>
        <item class="1-Wire" src="The analyzer will only operate at high speed overdrive mode.">解析器只在高速模式下运行。</item>
        <item class="Atmel SWI" src="Single Wire Interface SDA">单总线协议的SDA</item>
        <item class="Atmel SWI" src="Decode level">解码级别</item>
        <item class="Atmel SWI" src="Level of the communication to decode">通信数据的解码级别</item>
        <item class="Atmel SWI" src="Please select an input for the SDA channel.">请为SDA通道选择一个输入。</item>
        <item class="CAN" src="Controller Area Network - Input">CAN数据线</item>
        <item class="CAN" src="Bit Rate (Bits/S)">波特率(bps)</item>
        <item class="CAN" src="Specify the bit rate in bits per second.">设置波特率(bps)。</item>
        <item class="CAN" src="Inverted (CAN High)">反相(CAN-High)</item>
        <item class="CAN" src="Use this option when recording CAN High directly">直接采集CAN-High信号线时使用此选项</item>
        <item class="CAN" src="Please select a channel for the CAN interface">请为CAN接口选择一个通道</item>
        <item class="DMX-512" src="Standard DMX-512">标准 DMX-512</item>
        <item class="DMX-512" src="Accept DMX-1986 4us MAB"></item>
        <item class="DMX-512" src="Accept 4us MAB as per USITT DMX-512 (1986)"></item>
        <item class="HDMI CEC" src="HDMI Consumer Electronics Control (CEC)"></item>
        <item class="I2C" src="Serial Data Line">串行数据线</item>
        <item class="I2C" src="Serial Clock Line">串行时钟线</item>
        <item class="I2C" src="Address Display">地址显示格式</item>
        <item class="I2C" src="Data Export Level">数据导出格式</item>
        <item class="I2C" src="Specify how you would like the I2C address to be displayed.">指定I2C地址显示的方式。</item>
        <item class="I2C" src="-bit, read/write bit included [default]">-bit，包含读/写位[默认]</item>
        <item class="I2C" src="-bit, read/write bit set as 0">-bit，读/写位显示为0</item>
        <item class="I2C" src="-bit, address bits only">-bit，只显示地址位</item>
        <item class="I2C" src="SDA and SCL can't be assigned to the same input.">SDA和SCL不能分配相同的通道。</item>
        <item class="I2S/PCM" src="Clock, aka I2S SCK - Continuous Serial Clock, aka Bit Clock">时钟，又名I2S SCK - 连续串行时钟，又名位时钟</item>
        <item class="I2S/PCM" src="Frame Delimiter / aka I2S WS - Word Select, aka Sampling Clock">帧定界符/又名I2S WS - 字选择，又名采样时钟</item>
        <item class="I2S/PCM" src="Data, aka I2S SD - Serial Data">数据，又名I2S SD - 串行数据</item>
        <item class="I2S/PCM" src="Specify if data comes in MSB first, or LSB first.">指定数据高位(MSB)在前还是低位(LSB)在前。</item>
        <item class="I2S/PCM" src="DATA arrives MSB first">高位(MSB)在前</item>
        <item class="I2S/PCM" src="DATA arrives LSB first">低位(LSB)在前</item>
        <item class="I2S/PCM" src="Specify if data is valid (should be read) on the rising, or falling clock edge.">指定数据在上升/下降沿有效(被读取)</item>
        <item class="I2S/PCM" src="DATA is valid (should be read) on the CLOCK falling edge">数据在下降沿有效(被读取)</item>
        <item class="I2S/PCM" src="DATA is valid (should be read) on the CLOCK rising edge">数据在上升沿有效(被读取)</item>
        <item class="I2S/PCM" src="Specify the number of audio bits/word.  Any additional bits will be ignored">指定音频字长(bits/word)。多余的位将被忽略</item>
        <item class="I2S/PCM" src="Bits/Word (Audio bit depth, bits/sample)">Bits/Word (Audio bit depth, bits/sample)</item>
        <item class="I2S/PCM" src="Specify the type of frame signal used.">指定使用的帧信号类型。</item>
        <item class="I2S/PCM" src="FRAME signal transitions (changes state) twice each word."></item>
        <item class="I2S/PCM" src="FRAME signal transitions (changes state) once each word. (I2S, PCM standard)"></item>
        <item class="I2S/PCM" src="FRAME signal transitions(changes state) twice every four (4) words."></item>
        <item class="I2S/PCM" src="Specify whether data bits are left or right aligned wrt FRAME edges. Only needed if more bits are sent than needed each frame, and additional bits are ignored."></item>
        <item class="I2S/PCM" src="DATA bits are left-aligned with respect to FRAME edges"></item>
        <item class="I2S/PCM" src="DATA bits are right-aligned with respect to FRAME edges"></item>
        <item class="I2S/PCM" src="Specify the bit alignment type to use.">指定位对齐的类型。</item>
        <item class="I2S/PCM" src="Bits are right-shifted by one with respect to FRAME edges (I2S typical)"></item>
        <item class="I2S/PCM" src="Bits are not shifted with respect to FRAME edges (PCM typical)"></item>
        <item class="I2S/PCM" src="Select whether samples are unsigned or signed values (only shows up if the display type is decimal)"></item>
        <item class="I2S/PCM" src="Samples are unsigned numbers"></item>
        <item class="I2S/PCM" src="Samples are signed (two's compliment)"></item>
        <item class="I2S/PCM" src="Interpret samples as signed integers -- only when display type is set to decimal"></item>
        <item class="I2S/PCM" src="Select whether WS high is channel 1 or channel 2"></item>
        <item class="I2S/PCM" src="Word select high is channel 2 (right) (I2S typical)"></item>
        <item class="I2S/PCM" src="Word select high is channel 1 (left) (inverted)"></item>
        <item class="I2S/PCM" src="Please select a channel for I2S/PCM CLOCK signal">请为I2S/PCM CLOCK信号选择一个通道</item>
        <item class="I2S/PCM" src="Please select a channel for I2S/PCM FRAME signal">请为I2S/PCM FRAME信号选择一个通道</item>
        <item class="I2S/PCM" src="Please select a channel for I2S/PCM DATA signal">请为I2S/PCM DATA信号选择一个通道</item>
        <item class="I2S/PCM" src="Please select different channels for the I2S/PCM signals">请为I2S/PCM信号选择不同的通道</item>
        <item class="IR-NEC" src="Standard InfraRed">标准红外</item>
        <item class="IR-NEC" src="Bit Rate (Bits/S)">波特率(bps)</item>
        <item class="IR-NEC" src="Specify the bit rate used.">指定使用的波特率</item>
        <item class="IR-NEC" src="Protocol Type">协议类型</item>
        <item class="JTAG" src="JTAG Test mode select">JTAG测试模式选择</item>
        <item class="JTAG" src="JTAG Test clock">JTAG测试时钟</item>
        <item class="JTAG" src="JTAG Test data input">JTAG测试数据输入</item>
        <item class="JTAG" src="JTAG Test data output">JTAG测试数据输出</item>
        <item class="JTAG" src="JTAG Test reset">JTAG测试复位</item>
        <item class="JTAG" src="TAP initial state">TAP初始状态</item>
        <item class="JTAG" src="JTAG TAP controller initial state">JTAG TAP控制器初始状态</item>
        <item class="JTAG" src="Shift-IR bit order">IR移位位序</item>
        <item class="JTAG" src="Instruction register shift bit order">指令寄存器移位顺序</item>
        <item class="JTAG" src="Most significant bit first">高位(MSB)在前</item>
        <item class="JTAG" src="Least significant bit first">低位(LSB)在前</item>
        <item class="JTAG" src="Shift-DR bit order">DR移位位序</item>
        <item class="JTAG" src="Data register shift bit order">数据寄存器移位顺序</item>
        <item class="JTAG" src="Data register shift MOST significant bit first">高位(MSB)在前</item>
        <item class="JTAG" src="Data register shift LEAST significant bit first">低位(LSB)在前</item>
        <item class="JTAG" src="Show TDI/TDO bit counts">显示TDI/TDO位计数</item>
        <item class="JTAG" src="Used to count bits sent during Shift state">移位期间记录发送位数</item>
        <item class="JTAG" src="Please select inputs for TMS and TCK channels.">请为TMS和TCK通道选择输入。</item>
        <item class="JTAG" src="Please select different channels for each input.">请为每个输入选择不同的频道。</item>
        <item class="LIN" src="Standard LIN">标准LIN</item>
        <item class="LIN" src="LIN Version">LIN版本</item>
        <item class="LIN" src="Specify the LIN protocol version 1 or 2.">指定LIN协议版本(1或2)。</item>
        <item class="LIN" src="Bit Rate (Bits/s)">波特率(bps)</item>
        <item class="LIN" src="Specify the bit rate in bits per second.">以bps为单位指定波特率。</item>
        <item class="Manchester" src="Mode">模式</item>
        <item class="Manchester" src="Specify the Manchester Mode">指定曼彻斯特编码模式</item>
        <item class="Manchester" src="Manchester"></item>
        <item class="Manchester" src="Differential Manchester"></item>
        <item class="Manchester" src="Bi-Phase Mark Code (FM1)"></item>
        <item class="Manchester" src="Bi-Phase Space Code (FM0)"></item>
        <item class="Manchester" src="Bit Rate (Bits/s)">波特率(bps)</item>
        <item class="Manchester" src="Specify the bit rate in bits per second.">以bps为单位指定波特率。</item>
        <item class="Manchester" src="Specify the Manchester edge polarity (Normal Manchester mode only)">指定曼彻斯特编码边缘极性（仅限普通曼彻斯特编码模式）</item>
        <item class="Manchester" src="negative edge is binary one">下降沿为二进制"1"</item>
        <item class="Manchester" src="negative edge is binary zero">下降沿为二进制"0"</item>
        <item class="Manchester" src="Select the number of bits per frame">选择帧长度(bits/frame)</item>
        <item class="Manchester" src="Bit per Transfer">位单次传输字长</item>
        <item class="Manchester" src="Bits per Transfer">位单次传输字长</item>
        <item class="Manchester" src="Select if the most significant bit or least significant bit is transmitted first">选择高位(MSB)在前还是低位(LSB)在前</item>
        <item class="Manchester" src="Least Significant Bit Sent First">低位(LSB)在前</item>
        <item class="Manchester" src="Most Significant Bit Sent First">高位(MSB)在前</item>
        <item class="Manchester" src="Preamble bits to ignore">忽略的前导位数</item>
        <item class="Manchester" src="Specify the number of preamble bits to ignore.">指定要忽略的前导位位数。</item>
        <item class="Manchester" src="Tolerance">公差</item>
        <item class="Manchester" src="Specify the Manchester Tolerance as a percentage of period">以周期的百分比指定曼彻斯特编码公差</item>
        <item class="Manchester" src="% of period (default)"></item>
        <item class="Manchester" src="% of period"></item>
        <item class="MIDI" src="General MIDI">通用MIDI</item>
        <item class="Modbus" src="Modbus Mode">Modbus模式</item>
        <item class="Modbus" src="Specify which mode of Modbus this is">指定Modbus模式</item>
        <item class="Modbus" src="Modbus/RTU - Master"></item>
        <item class="Modbus" src="Modbus/RTU - Slave"></item>
        <item class="Modbus" src="Modbus/ASCII - Master"></item>
        <item class="Modbus" src="Modbus/ASCII - Slave"></item>
        <item class="Modbus" src="Bit Rate (Bits/S)">波特率(bps)</item>
        <item class="Modbus" src="Specify the bit rate in bits per second.">指定波特率。</item>
        <item class="Modbus" src="Specify if the serial signal is inverted">指定串行信号是否反相</item>
        <item class="Modbus" src="Non Inverted (Standard)">不反相(标准)</item>
        <item class="Modbus" src="Inverted">反相</item>
        <item class="Parallel 6800" src="Data is valid on E/Clock rising edge">数据在E/Clock上升沿有效</item>
        <item class="Parallel 6800" src="Data is valid on E/Clock falling edge">数据在E/Clock下降沿有效</item>
        <item class="Parallel 6800" src="Show decode marker or not">是否显示解码标记</item>
        <item class="Parallel 6800" src="Show Decode Marker">显示解码标记</item>
        <item class="Parallel 6800" src="Please select at least one channel to use in the parallel bus">请为并行总线指定至少一个通道</item>
        <item class="PS/2 Keyboard/Mouse" src="PS/2 - Clock"></item>
        <item class="PS/2 Keyboard/Mouse" src="PS/2 - Data"></item>
        <item class="PS/2 Keyboard/Mouse" src="Device Type">设备类型</item>
        <item class="PS/2 Keyboard/Mouse" src="Keyboard">键盘</item>
        <item class="PS/2 Keyboard/Mouse" src="Mouse (Standard PS/2)">鼠标(标准PS/2)</item>
        <item class="PS/2 Keyboard/Mouse" src="Mouse (IntelliMouse)">鼠标(IntelliMouse)</item>
        <item class="PS/2 Keyboard/Mouse" src="Clock and Data must be unique channels!">为Clock和Data指定不同通道！</item>
        <item class="QSPI-Flash" src="Clock Polarity">时钟极性</item>
        <item class="QSPI-Flash" src="Clock is Low when inactive (CPOL=0, CPHA=0)">时钟在空闲时为低电平</item>
        <item class="QSPI-Flash" src="Clock is High when inactive (CPOL=1, CPHA=1)">时钟在空闲时为高电平</item>
        <item class="QSPI-Flash" src="SPI Mode">SPI模式</item>
        <item class="QSPI-Flash" src="Self-Adaptive">自适应</item>
        <item class="QSPI-Flash" src="Forced Dual">强制双线</item>
        <item class="QSPI-Flash" src="Forced Quad">强制四线</item>
        <item class="QSPI-Flash" src="Dummy Clock Cycles">Dummy周期数</item>
        <item class="QSPI-Flash" src="Address Size">地址长度</item>
        <item class="QSPI-Flash" src="Ext for Manufacturer">厂商扩展支持</item>
        <item class="QSPI-Flash" src="Memory Type">存储器类型</item>
        <item class="SDIO" src="Standard SDIO Clock">标准 SDIO Clock</item>
        <item class="SDIO" src="Standard SDIO CMD">标准 SDIO CMD</item>
        <item class="SDIO" src="Standard SDIO Data 0">标准 SDIO Data 0</item>
        <item class="SDIO" src="Standard SDIO Data 1">标准 SDIO Data 1</item>
        <item class="SDIO" src="Standard SDIO Data 2">标准 SDIO Data 2</item>
        <item class="SDIO" src="Standard SDIO Data 3">标准 SDIO Data 3</item>
        <item class="SDIO" src="Sample On">采样时刻</item>
        <item class="SDIO" src="Determines if sample on rising or falling edge">指定在上升沿还是下降沿采样(读取数据)</item>
        <item class="SDIO" src="Read Samples on Falling clock edge">在时钟下降沿读取/采样</item>
        <item class="SDIO" src="Read Samples on Rising clock edge">在时钟上升沿读取/采样</item>
        <item class="SDIO" src="Please specify the channels for clock and command">请为Clock和CMD指定通道</item>
        <item class="SDIO" src="Only support 1-bit and 4-bit mode">仅支持1-bit和4-bit模式</item>
        <item class="SMBus" src="SMBus data line">SMBus数据线</item>
        <item class="SMBus" src="SMBus clock line">SMBus时钟线</item>
        <item class="SMBus" src="SMBus decode level">SMBus解码级别</item>
        <item class="SMBus" src="Type of decoded SMBus data">SMBus数据解码的类型</item>
        <item class="SMBus" src="Calculate PEC on packets">计算包上的PEC</item>
        <item class="SMBus" src="true - calculate PEC, false - no PEC on packets">选中 - 计算包上的PEC，未选中 - 不计算包上的PEC</item>
        <item class="SMBus" src="Please select an input for the SMBDAT.">请为SMBDAT通道选择一个输入。</item>
        <item class="SMBus" src="Please select an input for the SMBCLK.">请为SMBCLK通道选择一个输入。</item>
        <item class="SMBus" src="Please select different inputs for the channels.">请为通道选择不同的输入。</item>
        <item class="SPI" src="Bit per Transfer">位单次传输字长</item>
        <item class="SPI" src="Bits per Transfer">位单次传输字长</item>
        <item class="SPI" src="Bits per Transfer (Standard)">位单次传输字长(标准)</item>
        <item class="SPI" src="Master Out, Slave In">主出从入(MOSI)</item>
        <item class="SPI" src="Master In, Slave Out">主入从出(MISO)</item>
        <item class="SPI" src="Clock (CLK)">时钟(CLK)</item>
        <item class="SPI" src="Enable (SS, Slave Select)">使能(SS, Slave Select)</item>
        <item class="SPI" src="Most Significant Bit First (Standard)">高位(MSB)在前(标准)</item>
        <item class="SPI" src="Least Significant Bit First">低位(LSB)在前</item>
        <item class="SPI" src="Show decode marker or not">是否显示解码标记</item>
        <item class="SPI" src="Show Decode Marker">显示解码标记</item>
        <item class="SPI" src="Clock is Low when inactive (CPOL = 0)">总线空闲时，时钟为低电平 (CPOL = 0)</item>
        <item class="SPI" src="Clock is High when inactive (CPOL = 1)">总线空闲时，时钟为高电平 (CPOL = 1)</item>
        <item class="SPI" src="Data is Valid on Clock Leading Edge (CPHA = 0)">数据在前一个时钟沿有效(被读取) (CPHA = 0)</item>
        <item class="SPI" src="Data is Valid on Clock Trailing Edge (CPHA = 1)">数据在后一个时钟沿有效(被读取) (CPHA = 1)</item>
        <item class="SPI" src="Enable line is Active Low (Standard)">Enable信号低电平有效(标准)</item>
        <item class="SPI" src="Enable line is Active High">Enable信号高电平有效</item>
        <item class="SPI" src="Please select different channels for each input.">请为每个通道选择不同的输入。</item>
        <item class="SPI" src="Please select at least one input for either MISO or MOSI.">请在 MISO 和 MOSI 中至少选择一个通道。</item>
        <item class="SWD" src="Please select an input for SWDIO channel.">请为SWDIO通道选择一个输入。</item>
        <item class="SWD" src="Please select an input for SWCLK channel.">请为SWCLK通道选择一个输入。</item>
        <item class="SWD" src="Please select different inputs for the channels.">请为通道选择不同的输入。</item>
        <item class="UART/RS232/485" src="Standard Async Serial">标准异步串行接口</item>
        <item class="UART/RS232/485" src="Bit Rate (Bits/s)">波特率(bps)</item>
        <item class="UART/RS232/485" src="Specify the bit rate in bits per second.">指定波特率(bps)。</item>
        <item class="UART/RS232/485" src="Use Autobaud">自动检测波特率</item>
        <item class="UART/RS232/485" src="Automatically find the minimum pulse width and calculate the baud rate according to this pulse width.">自动查找最小脉宽，据此计算波特率。</item>
        <item class="UART/RS232/485" src="Inverted (RS232)">反相 (RS232)</item>
        <item class="UART/RS232/485" src="Specify if the serial signal is inverted">信号电平采用负逻辑时选用此项，例如RS232</item>
        <item class="UART/RS232/485" src="Select the number of bits per frame">选择每帧数据位数</item>
        <item class="UART/RS232/485" src="Bit per Transfer">位数据位</item>
        <item class="UART/RS232/485" src="Bits per Transfer">位数据位</item>
        <item class="UART/RS232/485" src="Bits per Transfer (Standard)">位数据位(标准)</item>
        <item class="UART/RS232/485" src="Specify the number of stop bits.">指定停止位数。</item>
        <item class="UART/RS232/485" src="Stop Bit (Standard)">位停止位(标准)</item>
        <item class="UART/RS232/485" src="Stop Bits">位停止位</item>
        <item class="UART/RS232/485" src="Specify None, Even, or Odd Parity.">指定有无奇偶校验。</item>
        <item class="UART/RS232/485" src="No Parity Bit (Standard)">无校验位(标准)</item>
        <item class="UART/RS232/485" src="Even Parity Bit">偶校验</item>
        <item class="UART/RS232/485" src="Odd Parity Bit">奇校验</item>
        <item class="UART/RS232/485" src="Select if the most significant bit or least significant bit is transmitted first">选择高位(MSB)在前还是低位(LSB)在前</item>
        <item class="UART/RS232/485" src="Least Significant Bit Sent First (Standard)">低位(LSB)在前 (标准)</item>
        <item class="UART/RS232/485" src="Most Significant Bit Sent First">高位(MSB)在前</item>
        <item class="UART/RS232/485" src="Special Mode">特殊模式</item>
        <item class="UART/RS232/485" src="Specify if this is normal, or MP serial (aka multi-drop, MP, multi-processor, 9-bit serial)">指定是否为多机通信模式</item>
        <item class="UART/RS232/485" src="None">无</item>
        <item class="UART/RS232/485" src="MP Mode: Address indicated by MSB=0">MP模式:地址由MSB=0标明</item>
        <item class="UART/RS232/485" src="MDB Mode: Address indicated by MSB=1">MDB模式:地址由MSB=1标明</item>
        <item class="UART/RS232/485" src="Sorry, but we don't support using parity at the same time as MP mode.">抱歉，不支持与MP模式同时使用奇偶校验。</item>
        <item class="USB LS/FS" src="USB D+ (green)">USB D+ (绿色)</item>
        <item class="USB LS/FS" src="USB D- (white)">USB D- (白色)</item>
        <item class="USB LS/FS" src="USB bit-rate">USB速率</item>
        <item class="USB LS/FS" src="USB data bit-rate">USB数据速率</item>
        <item class="USB LS/FS" src="Low speed (1.5 Mbps)">低速 (1.5 Mbps)</item>
        <item class="USB LS/FS" src="Full speed (12 Mbps)">全速 (12 Mbps)</item>
        <item class="USB LS/FS" src="USB decode level">USB解码级别</item>
        <item class="USB LS/FS" src="Type of decoded USB output">USB解码的输出类型</item>
        <item class="USB LS/FS" src="EP0 Control transfers"></item>
        <item class="USB LS/FS" src="Packets"></item>
        <item class="USB LS/FS" src="Bytes"></item>
        <item class="USB LS/FS" src="Signals"></item>
        <item class="USB LS/FS" src="Please select an input for the D+ channel.">请为D+通道选择一个输入。</item>
        <item class="USB LS/FS" src="Please select an input for the D- channel.">请为D-通道选择一个输入。</item>
        <item class="USB LS/FS" src="Please select different inputs for the D- and D+ channels.">请为D-和D+通道选择不同的输入。</item>
    </analyzers>
</language>
