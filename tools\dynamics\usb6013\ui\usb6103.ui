<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FormUSB6103</class>
 <widget class="QWidget" name="FormUSB6103">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1600</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1600</width>
    <height>900</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>防夹测试</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,1">
     <item>
      <widget class="QWidget" name="widget_menu" native="true">
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <property name="leftMargin">
         <number>2</number>
        </property>
        <property name="topMargin">
         <number>2</number>
        </property>
        <property name="rightMargin">
         <number>2</number>
        </property>
        <property name="bottomMargin">
         <number>2</number>
        </property>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout">
          <item>
           <layout class="QGridLayout" name="gridLayout">
            <item row="2" column="0">
             <widget class="QLabel" name="label_3">
              <property name="text">
               <string>通道</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="5" column="0">
             <widget class="QPushButton" name="pushButtonPeeling">
              <property name="text">
               <string>去皮</string>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QSpinBox" name="spinBoxChannel">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximum">
               <number>7</number>
              </property>
             </widget>
            </item>
            <item row="5" column="1">
             <widget class="QDoubleSpinBox" name="doubleSpinBoxPeeling">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="decimals">
               <number>1</number>
              </property>
              <property name="minimum">
               <double>-99.000000000000000</double>
              </property>
              <property name="maximum">
               <double>99.000000000000000</double>
              </property>
              <property name="value">
               <double>0.000000000000000</double>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QLabel" name="label_4">
              <property name="text">
               <string>卸力值</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QDoubleSpinBox" name="doubleSpinBoxProportion">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="decimals">
               <number>1</number>
              </property>
              <property name="maximum">
               <double>9999.000000000000000</double>
              </property>
              <property name="value">
               <double>130.000000000000000</double>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="label_2">
              <property name="text">
               <string>系数比例</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="4" column="1">
             <widget class="QSpinBox" name="spinBoxFrequency">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="minimum">
               <number>10</number>
              </property>
              <property name="maximum">
               <number>10000</number>
              </property>
              <property name="value">
               <number>1000</number>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLabel" name="labelStatus">
              <property name="text">
               <string>连接状态</string>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QPushButton" name="pushButtonConnect">
              <property name="text">
               <string>连接设备</string>
              </property>
             </widget>
            </item>
            <item row="4" column="0">
             <widget class="QLabel" name="label">
              <property name="text">
               <string>采集频率(n/s)</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item row="3" column="1">
             <widget class="QDoubleSpinBox" name="doubleSpinBoxLossPower">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="decimals">
               <number>1</number>
              </property>
              <property name="maximum">
               <double>999.000000000000000</double>
              </property>
              <property name="value">
               <double>20.000000000000000</double>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <widget class="QLCDNumber" name="lcdNumber">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>120</height>
             </size>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButtonStart">
            <property name="text">
             <string>开始采集</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButtonEnd">
            <property name="text">
             <string>结束采集</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButtonSaveImage">
            <property name="text">
             <string>导出图片</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="verticalSpacer">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QWidget" name="widget_display" native="true">
       <property name="styleSheet">
        <string notr="true">background-color: rgb(47, 47, 47);</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
