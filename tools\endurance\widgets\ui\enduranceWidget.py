# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'enduranceWidget.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(1000, 1200)
        Form.setMinimumSize(QtCore.QSize(1000, 1200))
        Form.setStyleSheet("")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(Form)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.groupBox = QtWidgets.QGroupBox(Form)
        self.groupBox.setObjectName("groupBox")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.groupBox)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout()
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.formLayout = QtWidgets.QFormLayout()
        self.formLayout.setObjectName("formLayout")
        self.label_2 = QtWidgets.QLabel(self.groupBox)
        self.label_2.setObjectName("label_2")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_2)
        self.label_3 = QtWidgets.QLabel(self.groupBox)
        self.label_3.setObjectName("label_3")
        self.formLayout.setWidget(1, QtWidgets.QFormLayout.LabelRole, self.label_3)
        self.label_4 = QtWidgets.QLabel(self.groupBox)
        self.label_4.setObjectName("label_4")
        self.formLayout.setWidget(2, QtWidgets.QFormLayout.LabelRole, self.label_4)
        self.label_5 = QtWidgets.QLabel(self.groupBox)
        self.label_5.setObjectName("label_5")
        self.formLayout.setWidget(3, QtWidgets.QFormLayout.LabelRole, self.label_5)
        self.label_6 = QtWidgets.QLabel(self.groupBox)
        self.label_6.setObjectName("label_6")
        self.formLayout.setWidget(4, QtWidgets.QFormLayout.LabelRole, self.label_6)
        self.label_7 = QtWidgets.QLabel(self.groupBox)
        self.label_7.setObjectName("label_7")
        self.formLayout.setWidget(5, QtWidgets.QFormLayout.LabelRole, self.label_7)
        self.label_8 = QtWidgets.QLabel(self.groupBox)
        self.label_8.setText("")
        self.label_8.setObjectName("label_8")
        self.formLayout.setWidget(6, QtWidgets.QFormLayout.LabelRole, self.label_8)
        self.comboBox_parity = QtWidgets.QComboBox(self.groupBox)
        self.comboBox_parity.setMinimumSize(QtCore.QSize(0, 40))
        self.comboBox_parity.setObjectName("comboBox_parity")
        self.formLayout.setWidget(2, QtWidgets.QFormLayout.FieldRole, self.comboBox_parity)
        self.comboBox_bytesize = QtWidgets.QComboBox(self.groupBox)
        self.comboBox_bytesize.setMinimumSize(QtCore.QSize(0, 40))
        self.comboBox_bytesize.setObjectName("comboBox_bytesize")
        self.formLayout.setWidget(3, QtWidgets.QFormLayout.FieldRole, self.comboBox_bytesize)
        self.comboBox_stopbits = QtWidgets.QComboBox(self.groupBox)
        self.comboBox_stopbits.setMinimumSize(QtCore.QSize(0, 40))
        self.comboBox_stopbits.setObjectName("comboBox_stopbits")
        self.formLayout.setWidget(4, QtWidgets.QFormLayout.FieldRole, self.comboBox_stopbits)
        self.spinBox_slave_id = QtWidgets.QSpinBox(self.groupBox)
        self.spinBox_slave_id.setMinimumSize(QtCore.QSize(0, 40))
        self.spinBox_slave_id.setObjectName("spinBox_slave_id")
        self.formLayout.setWidget(5, QtWidgets.QFormLayout.FieldRole, self.spinBox_slave_id)
        self.comboBox_baudrate = QtWidgets.QComboBox(self.groupBox)
        self.comboBox_baudrate.setMinimumSize(QtCore.QSize(0, 40))
        self.comboBox_baudrate.setObjectName("comboBox_baudrate")
        self.formLayout.setWidget(1, QtWidgets.QFormLayout.FieldRole, self.comboBox_baudrate)
        self.comboBoxComPort = QtWidgets.QComboBox(self.groupBox)
        self.comboBoxComPort.setMinimumSize(QtCore.QSize(0, 40))
        self.comboBoxComPort.setObjectName("comboBoxComPort")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.comboBoxComPort)
        self.verticalLayout_3.addLayout(self.formLayout)
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.pushButton_conf_update = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_conf_update.setObjectName("pushButton_conf_update")
        self.horizontalLayout.addWidget(self.pushButton_conf_update)
        self.pushButton_conf_save = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_conf_save.setObjectName("pushButton_conf_save")
        self.horizontalLayout.addWidget(self.pushButton_conf_save)
        self.verticalLayout.addLayout(self.horizontalLayout)
        self.pushButton_connect = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_connect.setObjectName("pushButton_connect")
        self.verticalLayout.addWidget(self.pushButton_connect)
        self.lineEdit_status = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_status.setMinimumSize(QtCore.QSize(0, 50))
        self.lineEdit_status.setAlignment(QtCore.Qt.AlignCenter)
        self.lineEdit_status.setReadOnly(True)
        self.lineEdit_status.setObjectName("lineEdit_status")
        self.verticalLayout.addWidget(self.lineEdit_status)
        self.verticalLayout_3.addLayout(self.verticalLayout)
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setObjectName("gridLayout")
        self.label_38 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_38.sizePolicy().hasHeightForWidth())
        self.label_38.setSizePolicy(sizePolicy)
        self.label_38.setObjectName("label_38")
        self.gridLayout.addWidget(self.label_38, 5, 0, 1, 1)
        self.label_y6_status = QtWidgets.QLabel(self.groupBox)
        self.label_y6_status.setMinimumSize(QtCore.QSize(0, 0))
        self.label_y6_status.setText("")
        self.label_y6_status.setObjectName("label_y6_status")
        self.gridLayout.addWidget(self.label_y6_status, 5, 1, 1, 1)
        self.label_y10_status = QtWidgets.QLabel(self.groupBox)
        self.label_y10_status.setText("")
        self.label_y10_status.setObjectName("label_y10_status")
        self.gridLayout.addWidget(self.label_y10_status, 9, 1, 1, 1)
        self.label_y4_status = QtWidgets.QLabel(self.groupBox)
        self.label_y4_status.setMinimumSize(QtCore.QSize(0, 0))
        self.label_y4_status.setText("")
        self.label_y4_status.setObjectName("label_y4_status")
        self.gridLayout.addWidget(self.label_y4_status, 3, 1, 1, 1)
        self.pushButton_y6_on = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y6_on.setObjectName("pushButton_y6_on")
        self.gridLayout.addWidget(self.pushButton_y6_on, 5, 2, 1, 1)
        self.label_44 = QtWidgets.QLabel(self.groupBox)
        self.label_44.setObjectName("label_44")
        self.gridLayout.addWidget(self.label_44, 14, 0, 1, 1)
        self.label_43 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_43.sizePolicy().hasHeightForWidth())
        self.label_43.setSizePolicy(sizePolicy)
        self.label_43.setObjectName("label_43")
        self.gridLayout.addWidget(self.label_43, 4, 0, 1, 1)
        self.pushButton_y3_on = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y3_on.setObjectName("pushButton_y3_on")
        self.gridLayout.addWidget(self.pushButton_y3_on, 2, 2, 1, 1)
        self.label_41 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_41.sizePolicy().hasHeightForWidth())
        self.label_41.setSizePolicy(sizePolicy)
        self.label_41.setObjectName("label_41")
        self.gridLayout.addWidget(self.label_41, 3, 0, 1, 1)
        self.pushButton_y11_on = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y11_on.setObjectName("pushButton_y11_on")
        self.gridLayout.addWidget(self.pushButton_y11_on, 10, 2, 1, 1)
        self.pushButton_y15_off = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y15_off.setObjectName("pushButton_y15_off")
        self.gridLayout.addWidget(self.pushButton_y15_off, 14, 3, 1, 1)
        self.pushButton_y7_off = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y7_off.setObjectName("pushButton_y7_off")
        self.gridLayout.addWidget(self.pushButton_y7_off, 6, 3, 1, 1)
        self.pushButton_y9_on = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y9_on.setObjectName("pushButton_y9_on")
        self.gridLayout.addWidget(self.pushButton_y9_on, 8, 2, 1, 1)
        self.label_y11_status = QtWidgets.QLabel(self.groupBox)
        self.label_y11_status.setText("")
        self.label_y11_status.setObjectName("label_y11_status")
        self.gridLayout.addWidget(self.label_y11_status, 10, 1, 1, 1)
        self.pushButton_y9_off = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y9_off.setObjectName("pushButton_y9_off")
        self.gridLayout.addWidget(self.pushButton_y9_off, 8, 3, 1, 1)
        self.label_37 = QtWidgets.QLabel(self.groupBox)
        self.label_37.setObjectName("label_37")
        self.gridLayout.addWidget(self.label_37, 12, 0, 1, 1)
        self.pushButton_y1_on = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y1_on.setObjectName("pushButton_y1_on")
        self.gridLayout.addWidget(self.pushButton_y1_on, 0, 2, 1, 1)
        self.pushButton_y15_on = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y15_on.setObjectName("pushButton_y15_on")
        self.gridLayout.addWidget(self.pushButton_y15_on, 14, 2, 1, 1)
        self.label_46 = QtWidgets.QLabel(self.groupBox)
        self.label_46.setObjectName("label_46")
        self.gridLayout.addWidget(self.label_46, 9, 0, 1, 1)
        self.pushButton_y14_off = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y14_off.setObjectName("pushButton_y14_off")
        self.gridLayout.addWidget(self.pushButton_y14_off, 13, 3, 1, 1)
        self.pushButton_y2_off = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y2_off.setObjectName("pushButton_y2_off")
        self.gridLayout.addWidget(self.pushButton_y2_off, 1, 3, 1, 1)
        self.pushButton_y13_on = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y13_on.setObjectName("pushButton_y13_on")
        self.gridLayout.addWidget(self.pushButton_y13_on, 12, 2, 1, 1)
        self.pushButton_y8_off = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y8_off.setObjectName("pushButton_y8_off")
        self.gridLayout.addWidget(self.pushButton_y8_off, 7, 3, 1, 1)
        self.pushButton_y4_off = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y4_off.setObjectName("pushButton_y4_off")
        self.gridLayout.addWidget(self.pushButton_y4_off, 3, 3, 1, 1)
        self.label_y12_status = QtWidgets.QLabel(self.groupBox)
        self.label_y12_status.setText("")
        self.label_y12_status.setObjectName("label_y12_status")
        self.gridLayout.addWidget(self.label_y12_status, 11, 1, 1, 1)
        self.label_y8_status = QtWidgets.QLabel(self.groupBox)
        self.label_y8_status.setMinimumSize(QtCore.QSize(0, 0))
        self.label_y8_status.setText("")
        self.label_y8_status.setObjectName("label_y8_status")
        self.gridLayout.addWidget(self.label_y8_status, 7, 1, 1, 1)
        self.label_47 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_47.sizePolicy().hasHeightForWidth())
        self.label_47.setSizePolicy(sizePolicy)
        self.label_47.setObjectName("label_47")
        self.gridLayout.addWidget(self.label_47, 2, 0, 1, 1)
        self.pushButton_y12_off = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y12_off.setObjectName("pushButton_y12_off")
        self.gridLayout.addWidget(self.pushButton_y12_off, 11, 3, 1, 1)
        self.label_y3_status = QtWidgets.QLabel(self.groupBox)
        self.label_y3_status.setMinimumSize(QtCore.QSize(0, 0))
        self.label_y3_status.setText("")
        self.label_y3_status.setObjectName("label_y3_status")
        self.gridLayout.addWidget(self.label_y3_status, 2, 1, 1, 1)
        self.pushButton_y13_off = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y13_off.setObjectName("pushButton_y13_off")
        self.gridLayout.addWidget(self.pushButton_y13_off, 12, 3, 1, 1)
        self.pushButton_y14_on = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y14_on.setObjectName("pushButton_y14_on")
        self.gridLayout.addWidget(self.pushButton_y14_on, 13, 2, 1, 1)
        self.label_y13_status = QtWidgets.QLabel(self.groupBox)
        self.label_y13_status.setText("")
        self.label_y13_status.setObjectName("label_y13_status")
        self.gridLayout.addWidget(self.label_y13_status, 12, 1, 1, 1)
        self.label_y5_status = QtWidgets.QLabel(self.groupBox)
        self.label_y5_status.setMinimumSize(QtCore.QSize(0, 0))
        self.label_y5_status.setText("")
        self.label_y5_status.setObjectName("label_y5_status")
        self.gridLayout.addWidget(self.label_y5_status, 4, 1, 1, 1)
        self.pushButton_y4_on = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y4_on.setObjectName("pushButton_y4_on")
        self.gridLayout.addWidget(self.pushButton_y4_on, 3, 2, 1, 1)
        self.pushButton_y10_on = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y10_on.setObjectName("pushButton_y10_on")
        self.gridLayout.addWidget(self.pushButton_y10_on, 9, 2, 1, 1)
        self.pushButton_y5_on = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y5_on.setObjectName("pushButton_y5_on")
        self.gridLayout.addWidget(self.pushButton_y5_on, 4, 2, 1, 1)
        self.label_36 = QtWidgets.QLabel(self.groupBox)
        self.label_36.setObjectName("label_36")
        self.gridLayout.addWidget(self.label_36, 13, 0, 1, 1)
        self.pushButton_y7_on = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y7_on.setObjectName("pushButton_y7_on")
        self.gridLayout.addWidget(self.pushButton_y7_on, 6, 2, 1, 1)
        self.label_30 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_30.sizePolicy().hasHeightForWidth())
        self.label_30.setSizePolicy(sizePolicy)
        self.label_30.setObjectName("label_30")
        self.gridLayout.addWidget(self.label_30, 0, 0, 1, 1)
        self.label_33 = QtWidgets.QLabel(self.groupBox)
        self.label_33.setObjectName("label_33")
        self.gridLayout.addWidget(self.label_33, 10, 0, 1, 1)
        self.label_y15_status = QtWidgets.QLabel(self.groupBox)
        self.label_y15_status.setText("")
        self.label_y15_status.setObjectName("label_y15_status")
        self.gridLayout.addWidget(self.label_y15_status, 14, 1, 1, 1)
        self.pushButton_y3_off = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y3_off.setObjectName("pushButton_y3_off")
        self.gridLayout.addWidget(self.pushButton_y3_off, 2, 3, 1, 1)
        self.pushButton_y6_off = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y6_off.setObjectName("pushButton_y6_off")
        self.gridLayout.addWidget(self.pushButton_y6_off, 5, 3, 1, 1)
        self.label_y14_status = QtWidgets.QLabel(self.groupBox)
        self.label_y14_status.setText("")
        self.label_y14_status.setObjectName("label_y14_status")
        self.gridLayout.addWidget(self.label_y14_status, 13, 1, 1, 1)
        self.pushButton_y8_on = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y8_on.setObjectName("pushButton_y8_on")
        self.gridLayout.addWidget(self.pushButton_y8_on, 7, 2, 1, 1)
        self.label_32 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_32.sizePolicy().hasHeightForWidth())
        self.label_32.setSizePolicy(sizePolicy)
        self.label_32.setObjectName("label_32")
        self.gridLayout.addWidget(self.label_32, 1, 0, 1, 1)
        self.label_y9_status = QtWidgets.QLabel(self.groupBox)
        self.label_y9_status.setText("")
        self.label_y9_status.setObjectName("label_y9_status")
        self.gridLayout.addWidget(self.label_y9_status, 8, 1, 1, 1)
        self.label_y7_status = QtWidgets.QLabel(self.groupBox)
        self.label_y7_status.setMinimumSize(QtCore.QSize(0, 0))
        self.label_y7_status.setText("")
        self.label_y7_status.setObjectName("label_y7_status")
        self.gridLayout.addWidget(self.label_y7_status, 6, 1, 1, 1)
        self.label_40 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_40.sizePolicy().hasHeightForWidth())
        self.label_40.setSizePolicy(sizePolicy)
        self.label_40.setObjectName("label_40")
        self.gridLayout.addWidget(self.label_40, 6, 0, 1, 1)
        self.pushButton_y2_on = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y2_on.setObjectName("pushButton_y2_on")
        self.gridLayout.addWidget(self.pushButton_y2_on, 1, 2, 1, 1)
        self.label_y2_status = QtWidgets.QLabel(self.groupBox)
        self.label_y2_status.setMinimumSize(QtCore.QSize(0, 0))
        self.label_y2_status.setText("")
        self.label_y2_status.setObjectName("label_y2_status")
        self.gridLayout.addWidget(self.label_y2_status, 1, 1, 1, 1)
        self.pushButton_y10_off = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y10_off.setObjectName("pushButton_y10_off")
        self.gridLayout.addWidget(self.pushButton_y10_off, 9, 3, 1, 1)
        self.pushButton_y5_off = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y5_off.setObjectName("pushButton_y5_off")
        self.gridLayout.addWidget(self.pushButton_y5_off, 4, 3, 1, 1)
        self.pushButton_y1_off = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y1_off.setObjectName("pushButton_y1_off")
        self.gridLayout.addWidget(self.pushButton_y1_off, 0, 3, 1, 1)
        self.label_45 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_45.sizePolicy().hasHeightForWidth())
        self.label_45.setSizePolicy(sizePolicy)
        self.label_45.setObjectName("label_45")
        self.gridLayout.addWidget(self.label_45, 7, 0, 1, 1)
        self.label_34 = QtWidgets.QLabel(self.groupBox)
        self.label_34.setObjectName("label_34")
        self.gridLayout.addWidget(self.label_34, 11, 0, 1, 1)
        self.label_31 = QtWidgets.QLabel(self.groupBox)
        self.label_31.setObjectName("label_31")
        self.gridLayout.addWidget(self.label_31, 8, 0, 1, 1)
        self.label_y1_status = QtWidgets.QLabel(self.groupBox)
        self.label_y1_status.setMinimumSize(QtCore.QSize(0, 0))
        self.label_y1_status.setText("")
        self.label_y1_status.setObjectName("label_y1_status")
        self.gridLayout.addWidget(self.label_y1_status, 0, 1, 1, 1)
        self.pushButton_y11_off = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y11_off.setObjectName("pushButton_y11_off")
        self.gridLayout.addWidget(self.pushButton_y11_off, 10, 3, 1, 1)
        self.pushButton_y12_on = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y12_on.setObjectName("pushButton_y12_on")
        self.gridLayout.addWidget(self.pushButton_y12_on, 11, 2, 1, 1)
        self.pushButton_y16_on = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y16_on.setObjectName("pushButton_y16_on")
        self.gridLayout.addWidget(self.pushButton_y16_on, 15, 2, 1, 1)
        self.pushButton_y16_off = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_y16_off.setObjectName("pushButton_y16_off")
        self.gridLayout.addWidget(self.pushButton_y16_off, 15, 3, 1, 1)
        self.label_y16_status = QtWidgets.QLabel(self.groupBox)
        self.label_y16_status.setText("")
        self.label_y16_status.setObjectName("label_y16_status")
        self.gridLayout.addWidget(self.label_y16_status, 15, 1, 1, 1)
        self.label_9 = QtWidgets.QLabel(self.groupBox)
        self.label_9.setObjectName("label_9")
        self.gridLayout.addWidget(self.label_9, 15, 0, 1, 1)
        self.gridLayout.setColumnStretch(0, 1)
        self.gridLayout.setColumnStretch(1, 99)
        self.gridLayout.setColumnStretch(2, 1)
        self.gridLayout.setColumnStretch(3, 1)
        self.verticalLayout_3.addLayout(self.gridLayout)
        spacerItem = QtWidgets.QSpacerItem(20, 122, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_3.addItem(spacerItem)
        self.verticalLayout_4.addLayout(self.verticalLayout_3)
        self.horizontalLayout_2.addWidget(self.groupBox)
        self.groupBox_2 = QtWidgets.QGroupBox(Form)
        self.groupBox_2.setObjectName("groupBox_2")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.groupBox_2)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.scrollArea = QtWidgets.QScrollArea(self.groupBox_2)
        self.scrollArea.setWidgetResizable(True)
        self.scrollArea.setObjectName("scrollArea")
        self.scrollAreaWidgetContents = QtWidgets.QWidget()
        self.scrollAreaWidgetContents.setGeometry(QtCore.QRect(0, 0, 465, 1147))
        self.scrollAreaWidgetContents.setObjectName("scrollAreaWidgetContents")
        self.scrollArea.setWidget(self.scrollAreaWidgetContents)
        self.verticalLayout_2.addWidget(self.scrollArea)
        self.horizontalLayout_2.addWidget(self.groupBox_2)
        self.verticalLayout_5.addLayout(self.horizontalLayout_2)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.groupBox.setTitle(_translate("Form", "继电器"))
        self.label_2.setText(_translate("Form", "串口号："))
        self.label_3.setText(_translate("Form", "波特率："))
        self.label_4.setText(_translate("Form", "校验位："))
        self.label_5.setText(_translate("Form", "数据位："))
        self.label_6.setText(_translate("Form", "停止位："))
        self.label_7.setText(_translate("Form", "设备地址："))
        self.pushButton_conf_update.setText(_translate("Form", "刷新"))
        self.pushButton_conf_save.setText(_translate("Form", "保存"))
        self.pushButton_connect.setText(_translate("Form", "连接"))
        self.label_38.setText(_translate("Form", "Y6"))
        self.pushButton_y6_on.setText(_translate("Form", "开"))
        self.label_44.setText(_translate("Form", "Y15"))
        self.label_43.setText(_translate("Form", "Y5"))
        self.pushButton_y3_on.setText(_translate("Form", "开"))
        self.label_41.setText(_translate("Form", "Y4"))
        self.pushButton_y11_on.setText(_translate("Form", "开"))
        self.pushButton_y15_off.setText(_translate("Form", "关"))
        self.pushButton_y7_off.setText(_translate("Form", "关"))
        self.pushButton_y9_on.setText(_translate("Form", "开"))
        self.pushButton_y9_off.setText(_translate("Form", "关"))
        self.label_37.setText(_translate("Form", "Y13"))
        self.pushButton_y1_on.setText(_translate("Form", "开"))
        self.pushButton_y15_on.setText(_translate("Form", "开"))
        self.label_46.setText(_translate("Form", "Y10"))
        self.pushButton_y14_off.setText(_translate("Form", "关"))
        self.pushButton_y2_off.setText(_translate("Form", "关"))
        self.pushButton_y13_on.setText(_translate("Form", "开"))
        self.pushButton_y8_off.setText(_translate("Form", "关"))
        self.pushButton_y4_off.setText(_translate("Form", "关"))
        self.label_47.setText(_translate("Form", "Y3"))
        self.pushButton_y12_off.setText(_translate("Form", "关"))
        self.pushButton_y13_off.setText(_translate("Form", "关"))
        self.pushButton_y14_on.setText(_translate("Form", "开"))
        self.pushButton_y4_on.setText(_translate("Form", "开"))
        self.pushButton_y10_on.setText(_translate("Form", "开"))
        self.pushButton_y5_on.setText(_translate("Form", "开"))
        self.label_36.setText(_translate("Form", "Y14"))
        self.pushButton_y7_on.setText(_translate("Form", "开"))
        self.label_30.setText(_translate("Form", "Y1"))
        self.label_33.setText(_translate("Form", "Y11"))
        self.pushButton_y3_off.setText(_translate("Form", "关"))
        self.pushButton_y6_off.setText(_translate("Form", "关"))
        self.pushButton_y8_on.setText(_translate("Form", "开"))
        self.label_32.setText(_translate("Form", "Y2"))
        self.label_40.setText(_translate("Form", "Y7"))
        self.pushButton_y2_on.setText(_translate("Form", "开"))
        self.pushButton_y10_off.setText(_translate("Form", "关"))
        self.pushButton_y5_off.setText(_translate("Form", "关"))
        self.pushButton_y1_off.setText(_translate("Form", "关"))
        self.label_45.setText(_translate("Form", "Y8"))
        self.label_34.setText(_translate("Form", "Y12"))
        self.label_31.setText(_translate("Form", "Y9"))
        self.pushButton_y11_off.setText(_translate("Form", "关"))
        self.pushButton_y12_on.setText(_translate("Form", "开"))
        self.pushButton_y16_on.setText(_translate("Form", "开"))
        self.pushButton_y16_off.setText(_translate("Form", "关"))
        self.label_9.setText(_translate("Form", "Y16"))
        self.groupBox_2.setTitle(_translate("Form", "休眠/唤醒"))
