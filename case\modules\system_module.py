"""
系统模块 - 处理系统相关命令
"""
import logging
import operator
import threading
import random
from typing import Dict, Any, Set
from case.module_manager import ModuleHandler

logger = logging.getLogger(__name__)


class SystemModule(ModuleHandler):
    """系统模块处理器"""
    
    def __init__(self):
        super().__init__()
        # 延迟加载的资源
        self.signals_manager = None
        self.timer_manager = None
        self.bat_manager = None
        self.auto_key_press = None
        self.relay_client = None
        self.env_test_auto = None
        self.oscilloscope_manager = None
        self.dtc_detect_manager = None
    
    def get_supported_commands(self) -> Set[str]:
        """获取支持的命令列表"""
        return {
            'SetDelayTime', 'SetRandomDelayTime', 'UpdateFwByRandom',
            'UpdateFwByStep', 'ExecuteBat', 'ExecuteBlockBat',
            'StartAutoCyclePress', 'StopAutoCyclePress',
            'StartCustomCyclePress', 'StopCustomCyclePress',
            'SetRelayStatus', 'EnvTemperatureTest', 'DetectAngle',
            'recordCanMsg', 'LinSender', 'LinStopSender',
            'StartProcessMonitor', 'StopProcessMonitor',
            'SetOscilloscopeDelayStop', 'MarkDtcNormalMsg'
        }
    
    def _load_resources(self):
        """加载系统模块资源"""
        logger.info("正在加载系统模块资源...")
        
        # 导入系统相关模块
        from utils.SignalsManager import signals_manager
        from case.TimerManager import timer_manager
        from case.BatManager import bat_manager
        from control_board.auto_test_m.auto_key_press import auto_key_press
        from control_board.relay_client import relay_client
        from case.EnvTestAuto import env_test_auto
        from case.OscilloscopeManager import oscilloscope_manager
        from case.DtcDetectManager import dtc_detect_manager
        
        self.signals_manager = signals_manager
        self.timer_manager = timer_manager
        self.bat_manager = bat_manager
        self.auto_key_press = auto_key_press
        self.relay_client = relay_client
        self.env_test_auto = env_test_auto
        self.oscilloscope_manager = oscilloscope_manager
        self.dtc_detect_manager = dtc_detect_manager
        
        logger.info("系统模块资源加载完成")
    
    def execute_command(self, command: str, step: Dict[str, Any]):
        """执行系统命令"""
        case_number = step.get("case_number", "")
        data = step.get("params", "")
        
        if operator.eq("SetDelayTime", command):
            self._handle_set_delay_time(case_number, command, data)
        elif operator.eq("SetRandomDelayTime", command):
            self._handle_set_random_delay_time(case_number, command, data)
        elif operator.eq("UpdateFwByRandom", command):
            self._handle_update_fw_by_random(case_number, command, data)
        elif operator.eq("UpdateFwByStep", command):
            self._handle_update_fw_by_step(case_number, command, data)
        elif operator.eq("ExecuteBat", command):
            self._handle_execute_bat(case_number, command, data)
        elif operator.eq("ExecuteBlockBat", command):
            self._handle_execute_block_bat(case_number, command, data)
        elif operator.eq("StartAutoCyclePress", command):
            self._handle_start_auto_cycle_press(case_number, command, data)
        elif operator.eq("StopAutoCyclePress", command):
            self._handle_stop_auto_cycle_press(case_number, command)
        elif operator.eq("StartCustomCyclePress", command):
            self._handle_start_custom_cycle_press(case_number, command, data)
        elif operator.eq("StopCustomCyclePress", command):
            self._handle_stop_custom_cycle_press(case_number, command)
        elif operator.eq("SetRelayStatus", command):
            self._handle_set_relay_status(case_number, command, data)
        elif operator.eq("EnvTemperatureTest", command):
            self._handle_env_temperature_test(case_number, command, data)
        elif operator.eq("DetectAngle", command):
            self._handle_detect_angle(case_number, command, data)
        elif operator.eq("recordCanMsg", command):
            self._handle_record_can_msg(case_number, command, data)
        elif operator.eq("LinSender", command):
            self._handle_lin_sender(case_number, command, data)
        elif operator.eq("LinStopSender", command):
            self._handle_lin_stop_sender(case_number, command, data)
        elif operator.eq("StartProcessMonitor", command):
            self._handle_start_process_monitor(case_number, command)
        elif operator.eq("StopProcessMonitor", command):
            self._handle_stop_process_monitor(case_number, command)
        elif operator.eq("SetOscilloscopeDelayStop", command):
            self._handle_set_oscilloscope_delay_stop(case_number, command, data)
        elif operator.eq("MarkDtcNormalMsg", command):
            self._handle_mark_dtc_normal_msg(case_number, command, data)
        else:
            logger.warning(f"系统模块不支持命令: {command}")
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", f"不支持的命令: {command}")
    
    def _handle_set_delay_time(self, case_number: str, command: str, data: str):
        """处理设置延时时间"""
        if data.__contains__(","):
            time_size = 0
            value = data.split(",")[0]
            unit = data.split(",")[1]
            if operator.eq("s", unit):
                time_size = float(value)
            elif operator.eq("ms", unit):
                time_size = float(value) / 1000
        else:
            time_size = float(data)
        
        if time_size >= 2:
            self.timer_manager.set_params(case_number, command, time_size)
            self.timer_manager.stop_timer()
            self.timer_manager.start_timer()
        else:
            from case.StepManager import step_manager
            threading.Timer(interval=time_size, function=step_manager.set_delay_time,
                            args=(case_number, command, time_size)).start()
    
    def _handle_set_random_delay_time(self, case_number: str, command: str, data: str):
        """处理设置随机延时时间"""
        datas = data.split(",")
        time_size = 0
        if len(datas) == 2:
            value = random.uniform(float(datas[0]), float(datas[1]))
            time_size = round(float(value), 3)
        elif len(datas) == 3:
            value = random.uniform(float(datas[0]), float(datas[1]))
            unit = datas[2]
            if operator.eq("s", unit):
                time_size = round(float(value), 3)
            elif operator.eq("ms", unit):
                time_size = round(float(value) / 1000, 3)
        
        if time_size >= 2:
            self.timer_manager.set_params(case_number, command, time_size)
            self.timer_manager.stop_timer()
            self.timer_manager.start_timer()
        else:
            from case.StepManager import step_manager
            threading.Timer(interval=time_size, function=step_manager.set_delay_time,
                            args=(case_number, command, time_size)).start()
    
    def _handle_update_fw_by_random(self, case_number: str, command: str, data: str):
        """处理随机固件更新"""
        self.bat_manager.handle_update_fw_by_random(case_number, command, data)
    
    def _handle_update_fw_by_step(self, case_number: str, command: str, data: str):
        """处理步进固件更新"""
        self.bat_manager.handle_update_fw_by_step(case_number, command, data)
    
    def _handle_execute_bat(self, case_number: str, command: str, data: str):
        """处理执行批处理"""
        self.bat_manager.handle_execute_bat(case_number, command, data)
    
    def _handle_execute_block_bat(self, case_number: str, command: str, data: str):
        """处理执行阻塞批处理"""
        self.bat_manager.handle_execute_block_bat(case_number, command, data)
    
    def _handle_start_auto_cycle_press(self, case_number: str, command: str, data: str):
        """处理开始自动循环按压"""
        if data.__contains__(","):
            delay = data.split(",")[0]
            counter = data.split(",")[1]
            self.auto_key_press.start_auto_cycle_press(delay, counter)
        else:
            self.auto_key_press.start_auto_cycle_press()
        self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
    
    def _handle_stop_auto_cycle_press(self, case_number: str, command: str):
        """处理停止自动循环按压"""
        self.auto_key_press.stop_mb()
        self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
    
    def _handle_start_custom_cycle_press(self, case_number: str, command: str, data: str):
        """处理开始自定义循环按压"""
        if data.__contains__(","):
            repeat = data.split(",")[0]
            position = data.split(",")[1]
            self.auto_key_press.reset_custom_params()
            self.auto_key_press.start_custom_cycle_press(repeat=int(repeat), position=position)
        else:
            self.auto_key_press.reset_custom_params()
            self.auto_key_press.start_custom_cycle_press()
        self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
    
    def _handle_stop_custom_cycle_press(self, case_number: str, command: str):
        """处理停止自定义循环按压"""
        self.auto_key_press.stop_custom_cycle_press()
        self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
    
    def _handle_set_relay_status(self, case_number: str, command: str, data: str):
        """处理设置继电器状态"""
        relay_number = int(data.split(",")[0])
        value = int(data.split(",")[1])
        relay_status = True if value == 0 else False
        ret = self.relay_client.set_relay_status(relay_number, relay_status)
        if ret is None:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "设置继电器开关失败")
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "设置继电器开关成功")
    
    def _handle_env_temperature_test(self, case_number: str, command: str, data: str):
        """处理环境温度测试"""
        device = data.split(",")[0]
        frequency = data.split(",")[1]
        total_test_time = data.split(",")[2]
        channels = data.split(",")[3].split("|")
        self.env_test_auto.command = command
        self.env_test_auto.case_number = case_number
        self.env_test_auto.set_parameter(device, frequency, total_test_time, channels)
        self.env_test_auto.start()
    
    def _handle_detect_angle(self, case_number: str, command: str, data: str):
        """处理角度检测"""
        import time
        import psutil
        import os
        from utils.execute_comand import execute_comand
        from adb.AdbConnectDevice import adb_connect_device
        from control_board.elevation_angle_tool import elevation_angle_tool
        
        angle = int(data.split(",")[0])
        deviation = int(data.split(",")[1])
        open_time = int(data.split(",")[2])
        
        # 检查server服务
        for proc in psutil.process_iter(['pid', 'name']):
            # 如果进程名匹配
            if proc.info['name'] == "orbbec_server.exe":
                break
        else:
            # 开启进程
            path = os.path.join(os.getcwd(), "orbbec", "orbbec_server.exe")
            threading.Thread(target=execute_comand, args=(path,)).start()
            logger.info("start orbbec_server...")
            time.sleep(.2)
        
        # 遍历所有的线程
        for thread in threading.enumerate():
            name = thread.name
            if thread.is_alive() and name == "elevation_angle_test":
                break
        else:
            threading.Thread(target=elevation_angle_tool.detect_angle, args=(True,), name="elevation_angle_test").start()
        
        time.sleep(1)
        adb_connect_device.adb_forward_send_data(action="open_ceiling")
        time.sleep(open_time)
        detect_angle = elevation_angle_tool.real_time_angle
        if angle - deviation <= detect_angle <= angle + deviation:
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", f"{detect_angle}")
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", f"{detect_angle}°")
        adb_connect_device.adb_forward_send_data(action="close_ceiling")
        time.sleep(open_time)
        elevation_angle_tool.stop_detect_angle = True
    
    def _handle_record_can_msg(self, case_number: str, command: str, data: str):
        """处理记录CAN消息"""
        from adb.AdbConnectDevice import adb_connect_device
        delay_time = int(data.split(",")[0])
        try:
            adb_connect_device.record_can_recv_msg(delay_time)
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "")
        except Exception as e:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", str(e.args))
    
    def _handle_lin_sender(self, case_number: str, command: str, data: str):
        """处理LIN发送器"""
        from adb.AdbConnectDevice import adb_connect_device
        from control_board.lin_sender import lin_sender
        
        try:
            device = int(data.split(",")[0].strip())
            pid = data.split(",")[1].strip()
            lin_data = data.split(",")[2].strip()
            interval = float(data.split(",")[3].strip())
        except ValueError:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "输入数据不规范")
            return
        
        if device == 1:
            if not lin_sender.is_connected:
                self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "设备未连接")
                return
            if interval > 0:
                # 使用周期性发送
                res, msg = lin_sender.start_periodic_send(pid, lin_data, interval)
                self.signals_manager.step_execute_finish.emit(case_number, command, "PASS" if res else "NG", msg)
            else:
                res, msg = lin_sender.send_message(pid, lin_data)
                self.signals_manager.step_execute_finish.emit(case_number, command, "PASS" if res else "NG", msg)
        elif device == 2:
            if adb_connect_device.tsmaster_lin_bus is None:
                self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "设备未连接")
                return
            if interval > 0:
                # 使用周期性发送
                res, msg = adb_connect_device.tsmaster_lin_bus.start_periodic_send(pid, lin_data, interval)
                self.signals_manager.step_execute_finish.emit(case_number, command, "PASS" if res else "NG", msg)
            else:
                res, msg = adb_connect_device.tsmaster_lin_bus.send_message(pid + " " + lin_data)
                self.signals_manager.step_execute_finish.emit(case_number, command, "PASS" if (res == 0) else "NG", msg)
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "设备选择错误")
    
    def _handle_lin_stop_sender(self, case_number: str, command: str, data: str):
        """处理停止LIN发送器"""
        from adb.AdbConnectDevice import adb_connect_device
        from control_board.lin_sender import lin_sender
        
        pid = data.split(",")[0]
        try:
            if pid == '-':
                lin_sender.stop_all_periodic_send()
                if adb_connect_device.tsmaster_lin_bus is not None:
                    adb_connect_device.tsmaster_lin_bus.stop_all_periodic_send()
            else:
                if pid in lin_sender.periodic_threads:
                    lin_sender.stop_periodic_send(pid)
                if adb_connect_device.tsmaster_lin_bus is not None:
                    if pid in adb_connect_device.tsmaster_lin_bus.periodic_threads:
                        adb_connect_device.tsmaster_lin_bus.stop_periodic_send(pid)
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        except Exception as e:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", str(e.args))
    
    def _handle_start_process_monitor(self, case_number: str, command: str):
        """处理开始进程监控"""
        from adb.AdbConnectDevice import adb_connect_device
        try:
            adb_connect_device.start_process_monitor()
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        except Exception as e:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", str(e.args))
    
    def _handle_stop_process_monitor(self, case_number: str, command: str):
        """处理停止进程监控"""
        from adb.AdbConnectDevice import adb_connect_device
        try:
            adb_connect_device.stop_process_monitor()
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "")
        except Exception as e:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", str(e.args))
    
    def _handle_set_oscilloscope_delay_stop(self, case_number: str, command: str, data: str):
        """处理设置示波器延时停止"""
        self.oscilloscope_manager.set_delay_stop_time(float(data))
        self.signals_manager.step_execute_finish.emit(case_number, command, "PASS",
                                                     f"示波器设置延时停止时长：{float(data)}秒")
    
    def _handle_mark_dtc_normal_msg(self, case_number: str, command: str, data: str):
        """处理标记DTC正常消息"""
        self.dtc_detect_manager.handle_dtc_detect(case_number, command, data)
