<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>EnduranceForm</class>
 <widget class="QWidget" name="EnduranceForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1600</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1600</width>
    <height>900</height>
   </size>
  </property>
  <property name="font">
   <font>
    <pointsize>12</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>耐久测试</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_9" stretch="2,3">
   <item>
    <widget class="QFrame" name="frame_2">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <item>
       <widget class="QGroupBox" name="groupBox">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>220</width>
          <height>140</height>
         </size>
        </property>
        <property name="title">
         <string>继电器</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout">
         <property name="spacing">
          <number>10</number>
         </property>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout">
           <property name="topMargin">
            <number>6</number>
           </property>
           <property name="bottomMargin">
            <number>6</number>
           </property>
           <item>
            <widget class="QLabel" name="label">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>串口:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QComboBox" name="serial_combo">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>24</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="refresh_btn">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>24</height>
              </size>
             </property>
             <property name="text">
              <string>刷新</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <property name="topMargin">
            <number>6</number>
           </property>
           <property name="bottomMargin">
            <number>6</number>
           </property>
           <item>
            <widget class="QLabel" name="label_2">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>设备地址:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QSpinBox" name="slave_spin">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>24</height>
              </size>
             </property>
             <property name="value">
              <number>17</number>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_10">
           <property name="topMargin">
            <number>6</number>
           </property>
           <property name="bottomMargin">
            <number>6</number>
           </property>
           <item>
            <widget class="QLabel" name="label_8">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>设备状态:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QRadioButton" name="device_radio">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>24</height>
              </size>
             </property>
             <property name="text">
              <string>已连接</string>
             </property>
             <property name="checkable">
              <bool>false</bool>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_13">
           <property name="topMargin">
            <number>6</number>
           </property>
           <property name="bottomMargin">
            <number>6</number>
           </property>
           <item>
            <widget class="QLabel" name="label_10">
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>24</height>
              </size>
             </property>
             <property name="text">
              <string>设备名称:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="device_name">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="text">
              <string>--</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_14">
           <property name="topMargin">
            <number>6</number>
           </property>
           <property name="bottomMargin">
            <number>6</number>
           </property>
           <item>
            <widget class="QLabel" name="label_12">
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>24</height>
              </size>
             </property>
             <property name="text">
              <string>硬件版本:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="device_hardware">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="text">
              <string>--</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_15">
           <property name="topMargin">
            <number>6</number>
           </property>
           <property name="bottomMargin">
            <number>6</number>
           </property>
           <item>
            <widget class="QLabel" name="label_14">
             <property name="minimumSize">
              <size>
               <width>60</width>
               <height>24</height>
              </size>
             </property>
             <property name="text">
              <string>软件版本:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="device_software">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="text">
              <string>--</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_12">
           <property name="topMargin">
            <number>6</number>
           </property>
           <property name="bottomMargin">
            <number>6</number>
           </property>
           <item>
            <widget class="QLabel" name="label_9">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>24</height>
              </size>
             </property>
             <property name="text">
              <string>开关量(DO):</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QCheckBox" name="do_1_checkbox">
             <property name="text">
              <string>DO1</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QCheckBox" name="do_2_checkbox">
             <property name="text">
              <string>DO2</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_5">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_3">
           <item>
            <widget class="QPushButton" name="connect_btn">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>24</height>
              </size>
             </property>
             <property name="text">
              <string>连接</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="disconnect_btn">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>24</height>
              </size>
             </property>
             <property name="text">
              <string>断开</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="frame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_4">
      <item>
       <widget class="QGroupBox" name="groupBox_3">
        <property name="title">
         <string>休眠/唤醒</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <property name="spacing">
          <number>10</number>
         </property>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_4">
           <item>
            <widget class="QLabel" name="label_3">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>开关量(DO):</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QRadioButton" name="do_1_radio">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>24</height>
              </size>
             </property>
             <property name="text">
              <string>DO1</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QRadioButton" name="do_2_radio">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>24</height>
              </size>
             </property>
             <property name="text">
              <string>DO2</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_4">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_6">
           <item>
            <widget class="QLabel" name="label_5">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>唤醒时长(s):</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QSpinBox" name="wakeup_spin">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>100</width>
               <height>45</height>
              </size>
             </property>
             <property name="maximum">
              <number>3600</number>
             </property>
             <property name="value">
              <number>60</number>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_5">
           <item>
            <widget class="QLabel" name="label_4">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>休眠时长(s):</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QSpinBox" name="sleep_spin">
             <property name="minimumSize">
              <size>
               <width>100</width>
               <height>45</height>
              </size>
             </property>
             <property name="maximum">
              <number>3600</number>
             </property>
             <property name="value">
              <number>60</number>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_2">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_7">
           <item>
            <widget class="QLabel" name="label_6">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>0</height>
              </size>
             </property>
             <property name="text">
              <string>循环次数:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QSpinBox" name="count_spin">
             <property name="minimumSize">
              <size>
               <width>100</width>
               <height>45</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_7">
             <property name="text">
              <string>(0:无限循环)</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_3">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_16">
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QLabel" name="label_11">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>24</height>
              </size>
             </property>
             <property name="text">
              <string>执行次数:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="count_label">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="text">
              <string>--</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_8">
           <item>
            <widget class="QPushButton" name="start_btn">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>24</height>
              </size>
             </property>
             <property name="text">
              <string>启动</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="stop_btn">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>24</height>
              </size>
             </property>
             <property name="text">
              <string>停止</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <spacer name="verticalSpacer_2">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
