# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'MainFrame.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_MainFrame(object):
    def setupUi(self, MainFrame):
        MainFrame.setObjectName("MainFrame")
        MainFrame.resize(1600, 900)
        MainFrame.setMinimumSize(QtCore.QSize(1600, 900))
        font = QtGui.QFont()
        font.setFamily("Microsoft YaHei UI")
        font.setPointSize(12)
        MainFrame.setFont(font)
        self.centralwidget = QtWidgets.QWidget(MainFrame)
        self.centralwidget.setObjectName("centralwidget")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.centralwidget)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.write_type_cb = QtWidgets.QComboBox(self.centralwidget)
        self.write_type_cb.setMinimumSize(QtCore.QSize(0, 45))
        self.write_type_cb.setObjectName("write_type_cb")
        self.horizontalLayout.addWidget(self.write_type_cb)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.write_btn = QtWidgets.QPushButton(self.centralwidget)
        self.write_btn.setMinimumSize(QtCore.QSize(0, 35))
        self.write_btn.setObjectName("write_btn")
        self.horizontalLayout.addWidget(self.write_btn)
        self.write_content_le = QtWidgets.QLineEdit(self.centralwidget)
        self.write_content_le.setMinimumSize(QtCore.QSize(0, 45))
        self.write_content_le.setObjectName("write_content_le")
        self.horizontalLayout.addWidget(self.write_content_le)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.read_btn = QtWidgets.QPushButton(self.centralwidget)
        self.read_btn.setMinimumSize(QtCore.QSize(0, 35))
        self.read_btn.setObjectName("read_btn")
        self.horizontalLayout.addWidget(self.read_btn)
        self.read_content_le = QtWidgets.QLineEdit(self.centralwidget)
        self.read_content_le.setMinimumSize(QtCore.QSize(0, 45))
        self.read_content_le.setObjectName("read_content_le")
        self.horizontalLayout.addWidget(self.read_content_le)
        self.verticalLayout.addLayout(self.horizontalLayout)
        spacerItem2 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem2)
        MainFrame.setCentralWidget(self.centralwidget)

        self.retranslateUi(MainFrame)
        QtCore.QMetaObject.connectSlotsByName(MainFrame)

    def retranslateUi(self, MainFrame):
        _translate = QtCore.QCoreApplication.translate
        MainFrame.setWindowTitle(_translate("MainFrame", "MainWindow"))
        self.write_btn.setText(_translate("MainFrame", "写号"))
        self.read_btn.setText(_translate("MainFrame", "读号"))
