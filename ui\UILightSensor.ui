<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LightSensorForm</class>
 <widget class="QWidget" name="LightSensorForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1600</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1600</width>
    <height>900</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">outline: none</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="leftMargin">
    <number>5</number>
   </property>
   <property name="topMargin">
    <number>5</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widget" native="true">
     <property name="styleSheet">
      <string notr="true">margin:10; border-radius:5; </string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout" stretch="1,99">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout" stretch="6,1,1,1,1">
          <item>
           <layout class="QGridLayout" name="gridLayout" columnstretch="0,0,0,0,0,0,0">
            <item row="1" column="3">
             <widget class="QLabel" name="label_4">
              <property name="styleSheet">
               <string notr="true">padding:5;</string>
              </property>
              <property name="text">
               <string>测试间隔(s):</string>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="label_6">
              <property name="styleSheet">
               <string notr="true">padding:5;</string>
              </property>
              <property name="text">
               <string>光源照度值</string>
              </property>
             </widget>
            </item>
            <item row="2" column="4">
             <widget class="QSpinBox" name="spinBox_light_value">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">padding:5;</string>
              </property>
              <property name="maximum">
               <number>999999</number>
              </property>
             </widget>
            </item>
            <item row="2" column="2">
             <widget class="QSlider" name="horizontalSlider_light_source">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="maximum">
               <number>255</number>
              </property>
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
             </widget>
            </item>
            <item row="0" column="2">
             <widget class="QCheckBox" name="checkBox_2">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="styleSheet">
               <string notr="true">padding:5;</string>
              </property>
              <property name="text">
               <string>百分比</string>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="1" column="4">
             <widget class="QSpinBox" name="spinBox_test_interval">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">padding:5;</string>
              </property>
              <property name="minimum">
               <number>2</number>
              </property>
              <property name="maximum">
               <number>99</number>
              </property>
              <property name="value">
               <number>2</number>
              </property>
             </widget>
            </item>
            <item row="0" column="5">
             <widget class="QLabel" name="label_5">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string>～</string>
              </property>
             </widget>
            </item>
            <item row="0" column="3">
             <widget class="QLabel" name="label_2">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="styleSheet">
               <string notr="true">padding:5;</string>
              </property>
              <property name="text">
               <string>光源亮度范围:</string>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QSpinBox" name="spinBoxTestTime">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">padding:5;</string>
              </property>
              <property name="maximum">
               <number>9999</number>
              </property>
              <property name="value">
               <number>100</number>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QSpinBox" name="spinBox_light_source">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">padding:5;</string>
              </property>
              <property name="maximum">
               <number>9999</number>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="label_3">
              <property name="styleSheet">
               <string notr="true">padding:5;</string>
              </property>
              <property name="text">
               <string>光源模式:</string>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="label">
              <property name="styleSheet">
               <string notr="true">padding:5;</string>
              </property>
              <property name="text">
               <string>测试次数</string>
              </property>
             </widget>
            </item>
            <item row="0" column="4">
             <widget class="QSpinBox" name="spinBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">padding:5;</string>
              </property>
              <property name="minimum">
               <number>1</number>
              </property>
             </widget>
            </item>
            <item row="0" column="6">
             <widget class="QSpinBox" name="spinBox_2">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">padding:5;</string>
              </property>
              <property name="maximum">
               <number>9999</number>
              </property>
              <property name="value">
               <number>100</number>
              </property>
             </widget>
            </item>
            <item row="2" column="3">
             <widget class="QLabel" name="label_7">
              <property name="styleSheet">
               <string notr="true">padding:5;</string>
              </property>
              <property name="text">
               <string>照度计照度值</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QCheckBox" name="checkBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="styleSheet">
               <string notr="true">padding:5;</string>
              </property>
              <property name="text">
               <string>量程</string>
              </property>
              <property name="checked">
               <bool>false</bool>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Preferred</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="pushButtonStart">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>200</width>
              <height>100</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">padding:5;</string>
            </property>
            <property name="text">
             <string>启动</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButtonPause">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>200</width>
              <height>100</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">padding:5;</string>
            </property>
            <property name="text">
             <string>暂停</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="pushButtonStop">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>200</width>
              <height>100</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">padding:5;</string>
            </property>
            <property name="text">
             <string>停止</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QTableWidget" name="tableWidget">
        <property name="styleSheet">
         <string notr="true">border-radius:0; margin:0;</string>
        </property>
        <column>
         <property name="text">
          <string>序号</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>光源亮度值</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>照度计值</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>产品光感值</string>
         </property>
        </column>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
