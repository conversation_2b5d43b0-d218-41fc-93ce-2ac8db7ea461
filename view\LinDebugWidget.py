import sys
import time
import datetime
import serial
import serial.tools.list_ports
import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, 
                            QPushButton, QHBoxLayout, QLabel, QComboBox,
                            QLineEdit, QApplication, QMessageBox, QFileDialog,
                            QGridLayout, QGroupBox, QTabWidget, QSizePolicy, QSpinBox, QCheckBox,
                            QHeaderView, QMenu, QDialog, QDialogButtonBox)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt5.QtGui import QFont, QRegExpValidator, QColor
from PyQt5.QtCore import QRegExp
import threading
try:
    from adb.TSMaster.LinComm import LinCommunicator
except:
    pass

# 尝试导入项目的日志模块，如果失败则创建一个简单的日志记录器
try:
    from common.LogUtils import logger
except ImportError:
    # 创建一个简单的日志记录器，用于独立运行时使用
    logger = logging.getLogger("LinDebugWidget")
    logger.setLevel(logging.INFO)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    
    # 添加处理器到日志记录器
    logger.addHandler(console_handler)
    
    logger.info("使用独立日志记录器")

class SerialThread(QThread):
    """串口通信线程"""
    # 定义信号
    received_data = pyqtSignal(str, str, str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, port, baudrate):
        super().__init__()
        self.port = port
        self.baudrate = baudrate
        self.is_running = False
        self.serial_port = None
    
    def debug_binary_data(self, data):
        """调试二进制数据，将其转换为可读形式并记录
        
        Args:
            data: 二进制数据
        """
        if not data:
            return
            
        # 十六进制表示
        hex_repr = ' '.join([f"{b:02X}" for b in data])
        
        # 只记录原始数据的十六进制表示
        logger.info(f"二进制数据 HEX: {hex_repr}")
        
        # 尝试简化解析为LIN格式的日志
        if len(data) >= 4 and data[0] == 0xA1:
            data_len = (data[1] << 8) + data[2]
            cmd = data[3]
            pid = data[4] if len(data) >= 5 else 0
            logger.info(f"LIN格式解析: 起始=0xA1, 长度={data_len}, CMD=0x{cmd:02X}, PID=0x{pid:02X}")
    
    def run(self):
        try:
            self.serial_port = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=0.1
            )
            self.is_running = True            
            buffer = bytearray()  # 用于缓存接收的数据
            while self.is_running:
                if self.serial_port.in_waiting:
                    # 读取数据
                    data = self.serial_port.read(self.serial_port.in_waiting)
                    logger.info(f"收到原始数据: {data}")
                    
                    # 使用调试函数深入分析数据
                    self.debug_binary_data(data)
                    
                    if data:
                        # 添加到缓冲区
                        buffer.extend(data)
                        
                        # 尝试解析完整报文
                        while len(buffer) >= 6:  # 至少需要包含开始符、长度、CMD、校验和和结束符
                            # 查找报文开始符
                            if buffer[0] != 0xA1:
                                buffer.pop(0)
                                continue
                                
                            # 检查缓冲区长度是否足够
                            if len(buffer) < 6:
                                break
                                
                            # 获取数据长度 (包含CMD和CheckSum)
                            data_len = (buffer[1] << 8) + buffer[2]
                            
                            # 计算总报文长度: 起始符(1) + 长度(2) + 数据长度(CMD+数据+校验和) + 结束符(可选,1)
                            expected_len = 1 + 2 + data_len
                            total_len = expected_len
                            
                            # 如果接收到的数据包含结束符，则包括它
                            if len(buffer) > expected_len and buffer[expected_len] == 0xB1:
                                total_len = expected_len + 1
                            
                            # 检查是否有足够的数据
                            if len(buffer) < expected_len:
                                break
                                
                            # 提取报文
                            cmd = buffer[3]  # CMD位于长度字段后
                            
                            # 在没有结束符的情况下，校验和可能不存在，所以直接提取数据
                            # 首先假设没有校验和，直接获取数据
                            data_bytes = buffer[4:expected_len]
                            
                            # 如果数据长度过长，可能包含校验和，则可以处理
                            # 在这种情况下，我们假设最后一个字节是校验和
                            if data_len > 1:  # 至少要有CMD+校验和
                                checksum = buffer[expected_len - 1]
                                
                                # 计算校验和 (从CMD字节到倒数第二个字节)
                                calculated_checksum = 0
                                for i in range(3, expected_len - 1):  # 从CMD开始，到校验和之前
                                    calculated_checksum += buffer[i]
                                calculated_checksum = calculated_checksum & 0xFF  # 取低8位
                                
                                # 检查校验和但不拒绝处理
                                if calculated_checksum != checksum:
                                    logger.warning(f"校验和不匹配：预期={checksum:02X}, 计算={calculated_checksum:02X}")
                            
                            # 提取完整报文(可能没有结束符)
                            complete_message = buffer[:total_len]
                            # 转换为十六进制字符串
                            hex_data = ' '.join([f"{b:02X}" for b in complete_message])
                            
                            # 获取当前时间
                            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                            
                            # 记录详细日志（减少冗余）
                            logger.info(f"解析到消息: CMD={cmd:02X}, 总长度={total_len}")
                            
                            # 发送信号
                            self.received_data.emit(current_time, f"{cmd:02X}", hex_data)
                            
                            # 从缓冲区移除已处理的报文
                            buffer = buffer[total_len:]
                
                # 短暂休眠，避免CPU占用过高
                time.sleep(0.01)
                
        except Exception as e:
            logger.error(f"串口通信错误: {str(e)}")
            self.error_occurred.emit(f"串口通信错误: {str(e)}")
            self.is_running = False
    
    def stop(self):
        self.is_running = False
        if self.serial_port and self.serial_port.is_open:
            self.serial_port.close()
            logger.info(f"串口 {self.port} 已关闭")
    
    def send_data(self, cmd, data_bytes):
        """
        发送指定格式的LIN报文
        
        Args:
            cmd: 命令字节（固定为71）
            data_bytes: 数据字节列表（包含PID和8字节数据，总长度应为9）
        
        Returns:
            bool: 发送是否成功
            str: 十六进制字符串形式的完整消息
        """
        if not self.serial_port or not self.serial_port.is_open:
            self.error_occurred.emit("串口未打开")
            return False, ""
        
        try:
            # 1. 报文开始符 (1字节，0xA1)
            message = bytearray([0xA1])
            
            # 将命令字节转换为整数
            cmd_byte = int(cmd, 16) if isinstance(cmd, str) else cmd
            
            # 确保数据长度固定为9（PID + 8字节数据）
            if len(data_bytes) != 9:
                logger.warning(f"数据长度不符，期望9字节，实际{len(data_bytes)}字节，将进行调整")
                # 确保至少有PID
                if len(data_bytes) < 1:
                    data_bytes = [0]  # 如果没有数据，添加默认PID为0
                # 调整到9字节长度
                data_bytes = data_bytes[:9] if len(data_bytes) > 9 else data_bytes + [0] * (9 - len(data_bytes))
            
            # 2. 数据长度 (2字节，高位在前) - 固定为10（CMD(1) + PID(1) + DATA(8) + 校验和(1)）
            data_len = 10  # 固定长度：CMD(1) + PID(1) + DATA(8) = 10
            message.append((data_len >> 8) & 0xFF)  # 高字节
            message.append(data_len & 0xFF)  # 低字节
            
            # 3. 命令字节 (1字节) - 固定为71
            message.append(cmd_byte)
            
            # 4. 数据字节 (9字节，PID + 8字节数据)
            message.extend(data_bytes)
            
            # 5. 校验和 (从CMD字节到数据字节的总和)
            checksum = 0
            for i in range(3, 3 + 1 + len(data_bytes)):  # 从CMD开始计算，包含CMD本身
                checksum += message[i]
            checksum = checksum & 0xFF  # 取低8位
            message.append(checksum)
            
            # 6. 报文结束符 (1字节，0xB1)
            message.append(0xB1)
            
            # 检查总长度是否正确（应为15字节）
            if len(message) != 15:
                logger.warning(f"生成的消息长度不正确，期望15字节，实际{len(message)}字节")
                        
            # 发送数据
            self.serial_port.write(message)
            
            # 记录发送的数据
            hex_message = ' '.join([f"{b:02X}" for b in message])
            logger.info(f"发送原始数据: {hex_message}")
            
            return True, hex_message
        except Exception as e:
            logger.error(f"发送数据错误: {str(e)}")
            self.error_occurred.emit(f"发送数据错误: {str(e)}")
            return False, ""

    def send_raw_data(self, raw_hex_str):
        """
        发送原始十六进制数据
        
        Args:
            raw_hex_str: 原始十六进制字符串，格式如"A1 00 0A 71 3F 00 00 00 00 00 00 00 00 BA B1"
            
        Returns:
            bool: 发送是否成功
            str: 发送的十六进制字符串
        """
        if not self.serial_port or not self.serial_port.is_open:
            self.error_occurred.emit("串口未打开")
            return False, ""
        
        try:
            # 解析十六进制字符串为字节列表
            try:
                hex_parts = raw_hex_str.strip().split()
                data_bytes = bytearray([int(part, 16) for part in hex_parts])
            except ValueError:
                logger.error("无效的十六进制字符串格式")
                self.error_occurred.emit("无效的十六进制字符串格式")
                return False, ""
            
            # 发送原始数据
            self.serial_port.write(data_bytes)
            
            # 记录发送的数据
            hex_message = ' '.join([f"{b:02X}" for b in data_bytes])
            logger.info(f"发送外部原始数据: {hex_message}")
            
            return True, hex_message
        except Exception as e:
            logger.error(f"发送外部数据错误: {str(e)}")
            self.error_occurred.emit(f"发送外部数据错误: {str(e)}")
            return False, ""

class LinDebugWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        # 设置窗口为弹出式窗口
        self.setWindowFlags(Qt.Window)
        self.setWindowTitle("LIN调试工具")
        
        # 初始化串口相关变量
        self.serial_thread = None
        self.is_connected = False

        # 初始化TSMaster相关变量
        self.lin_communicator = None
        self.is_tsmaster_connected = False
        
        self.init_ui()
        QTimer.singleShot(0, self.update_column_widths)
           
        # 初始化状态标签
        self.init_status_label()
        
        # 连接信号和槽
        self.connect_signals()

        # 刷新串口列表
        self.refresh_serial_ports()

    def init_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 创建tab控件并
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        #self.tab_widget.setDocumentMode(True)  # 使用更紧凑的文档模式
        
        # 创建USB-LIN标签页和面板
        usb_lin_tab = QWidget()
        usb_lin_layout = QVBoxLayout(usb_lin_tab)
        usb_lin_layout.setContentsMargins(5, 5, 5, 5)  # 减小边距
        usb_lin_layout.setSpacing(5)  # 减小间距
        
        # 创建串口控制面板
        serial_control_layout = self.create_serial_control_panel()
        usb_lin_layout.addLayout(serial_control_layout)
        
        # 添加USB-LIN标签页
        self.tab_widget.addTab(usb_lin_tab, "Usb-Lin")
        self.tab_widget.setStyleSheet("QTabBar::tab { height: 60px; }")
        
        # 创建TSMaster-LIN标签页和面板
        tsmaster_lin_tab = QWidget()
        tsmaster_lin_layout = QVBoxLayout(tsmaster_lin_tab)
        tsmaster_lin_layout.setContentsMargins(5, 5, 5, 5)  # 减小边距
        tsmaster_lin_layout.setSpacing(5)  # 减小间距
        
        # 创建TSMaster控制面板
        tsmaster_control_layout = self.create_tsmaster_control_panel()
        tsmaster_lin_layout.addLayout(tsmaster_control_layout)
        
        # 添加TSMaster-LIN标签页
        self.tab_widget.addTab(tsmaster_lin_tab, "Tsmaster-Lin")
        
        # 设置最大高度，避免占用过多空间
        self.tab_widget.setMaximumHeight(120)
        
        # 将tab控件添加到主布局
        layout.addWidget(self.tab_widget)
        
        # 创建报文控制面板
        message_control_group = QGroupBox("报文控制")
        message_control_layout = self.create_message_control_panel()
        message_control_group.setLayout(message_control_layout)
        layout.addWidget(message_control_group)

        #创建周期性发送列表
        # self.periodic_messages_group = QGroupBox("周期发送列表", self)
        # self.periodic_messages_layout = QVBoxLayout(self.periodic_messages_group)
        
        # 创建列表控件
        self.periodic_messages_list = QTableWidget(self)
        self.periodic_messages_list.setColumnCount(4)
        self.periodic_messages_list.setHorizontalHeaderLabels([" PID ", "数据", "  周期(ms)  ", " 状态 "])
        self.periodic_messages_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.periodic_messages_list.customContextMenuRequested.connect(self.show_periodic_message_context_menu)
        self.periodic_messages_list.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)

        self.msglist_ratios = [0.3, 0.5, 0.10, 0.1]

        layout.addWidget(self.periodic_messages_list)
        
        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(['时间', 'CMD/PID', '数据', '方向', '状态'])
        
        # 设置表格样式
        header_font = QFont()
        header_font.setBold(True)
        header_font.setPointSize(10)
        self.table.horizontalHeader().setFont(header_font)
        
        # 设置表格属性
        self.table.horizontalHeader().setSectionsMovable(False)
        self.table.horizontalHeader().setVisible(True)
        self.table.horizontalHeader().setStretchLastSection(False)  # 不拉伸最后一列
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setAlternatingRowColors(True)
        
        # 设置表格填充策略
        self.table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # 设置列宽比例 - 调整这些值来更改列宽
        self.column_ratios = [0.18, 0.12, 0.50, 0.10, 0.10]  # 调整比例以更合理地分配空间
        self.update_column_widths()
        
        # # 设置表格样式
        # self.table.setStyleSheet("""
        #     QTableWidget {
        #         background-color: white;
        #         alternate-background-color: #f5f5f5;
        #         border: 1px solid #dcdcdc;
        #         outline: 0;
        #     }
        #     QTableWidget::item {
        #         padding: 5px;
        #         border-bottom: 1px solid #eeeeee;
        #     }
        #     QTableWidget::item:selected {
        #         background-color: #4a90e2;
        #         color: white;
        #     }
        #     QHeaderView::section {
        #         background-color: #f0f0f0;
        #         padding: 5px;
        #         border: 1px solid #dcdcdc;
        #     }
        # """)
        
        # 设置表格权重大，使其在全屏时能够扩展
        layout.addWidget(self.table, 1)
        
        # 创建底部按钮
        button_layout = self.create_bottom_buttons()
        layout.addLayout(button_layout)  
        self.setLayout(layout)

    def create_serial_control_panel(self):
        """创建串口控制面板"""
        control_layout = QHBoxLayout()
        control_layout.setContentsMargins(0, 0, 0, 0)  # 移除边距
        control_layout.setSpacing(10)  # 合理的间距
        
        # 串口选择
        port_label = QLabel(" 串口:")
        port_label.setMaximumWidth(100)  # 限制标签宽度
        self.port_combo = QComboBox()
        self.port_combo.setMinimumWidth(150)
        self.port_combo.setMaximumWidth(200)
        self.port_combo.setMinimumHeight(40)
        
        # 刷新串口按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.setMaximumWidth(150)
        self.refresh_btn.clicked.connect(self.refresh_serial_ports)
        
        # 波特率设置
        baudrate_label = QLabel("波特率:")
        baudrate_label.setMaximumWidth(100)  # 限制标签宽度
        self.baudrate_combo = QComboBox()
        self.baudrate_combo.addItems(["9600", "19200", "38400", "57600", "115200"])
        self.baudrate_combo.setCurrentText("115200")  # LIN默认波特率
        self.baudrate_combo.setMinimumWidth(150)
        self.baudrate_combo.setMaximumWidth(300)
        self.baudrate_combo.setMinimumHeight(40)
        
        # 连接/断开按钮
        self.connect_btn = QPushButton("连接")
        self.connect_btn.setMaximumWidth(120)
        
        # 添加到布局
        control_layout.addWidget(port_label)
        control_layout.addWidget(self.port_combo)
        control_layout.addWidget(self.refresh_btn)
        control_layout.addWidget(baudrate_label)
        control_layout.addWidget(self.baudrate_combo)
        control_layout.addWidget(self.connect_btn)
        control_layout.addStretch()
        
        return control_layout

    def create_tsmaster_control_panel(self):
        """创建TSMaster控制面板"""
        control_layout = QHBoxLayout()
        control_layout.setContentsMargins(0, 0, 0, 0)  # 移除边距
        control_layout.setSpacing(10)  # 合理的间距
        
        # 通道选择
        channel_label = QLabel(" 通道:")
        channel_label.setMaximumWidth(100)  # 限制标签宽度
        self.tsmaster_channel_combo = QComboBox()
        self.tsmaster_channel_combo.setMinimumWidth(100)
        self.tsmaster_channel_combo.setMaximumWidth(200)
        self.tsmaster_channel_combo.setMinimumHeight(40)
        self.tsmaster_channel_combo.addItems(["0", "1", "2", "3"])
        
        # 波特率设置
        baudrate_label = QLabel("波特率:")
        baudrate_label.setMaximumWidth(100)  # 限制标签宽度
        self.tsmaster_baudrate_combo = QComboBox()
        self.tsmaster_baudrate_combo.addItems(["9.6", "19.2", "20.0"])  # 根据TSMaster支持的波特率设置
        self.tsmaster_baudrate_combo.setCurrentText("19.2")  # LIN默认波特率
        self.tsmaster_baudrate_combo.setMinimumWidth(150)
        self.tsmaster_baudrate_combo.setMaximumWidth(200)
        self.tsmaster_baudrate_combo.setMinimumHeight(40)
        
        # 连接/断开按钮
        self.tsmaster_connect_btn = QPushButton("连接")
        self.tsmaster_connect_btn.setMaximumWidth(120)

        self.tsmaster_connect_btn.clicked.connect(self.toggle_tsmaster_connection)
        
        # 添加到布局
        control_layout.addWidget(channel_label)
        control_layout.addWidget(self.tsmaster_channel_combo)
        control_layout.addWidget(baudrate_label)
        control_layout.addWidget(self.tsmaster_baudrate_combo)
        control_layout.addWidget(self.tsmaster_connect_btn)
        control_layout.addStretch()
        
        return control_layout

    def toggle_tsmaster_connection(self):
        """切换TSMaster连接/断开状态"""
        if not self.is_tsmaster_connected:
            self.connect_tsmaster()
        else:
            self.disconnect_tsmaster()

    def connect_tsmaster(self):
        """连接TSMaster设备"""
        try:
            channel = int(self.tsmaster_channel_combo.currentText())
            baudrate = float(self.tsmaster_baudrate_combo.currentText())
            
            # 创建LinCommunicator实例
            self.lin_communicator = LinCommunicator(baudrate=baudrate, channel=channel)
            
            # 尝试连接
            res = self.lin_communicator.connect()
            
            if res == 0:
                # 连接成功
                self.is_tsmaster_connected = True
                self.tsmaster_connect_btn.setText("断开")

                self.tsmaster_channel_combo.setEnabled(False)
                self.tsmaster_baudrate_combo.setEnabled(False)
                
                # 启用发送按钮（如果当前是TSMaster标签页）
                if self.tab_widget.currentIndex() == 1:
                    self.send_btn.setEnabled(True)
                    #self.raw_send_btn.setEnabled(True)
                
                # 记录连接信息
                self.show_status(True, "已连接TSMaster")
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                self.add_message_to_table(current_time, "--", f"已连接到TSMaster LIN设备，通道: {channel}，波特率: {baudrate}", "--", "成功")
                
                logger.info(f"已连接到TSMaster LIN设备，通道: {channel}，波特率: {baudrate}")
            else:
                # 连接失败
                self.lin_communicator = None
                QMessageBox.critical(self, "错误", f"连接TSMaster设备失败，错误码: {res}")
                logger.error(f"连接TSMaster设备失败，错误码: {res}")
        
        except Exception as e:
            self.lin_communicator = None
            QMessageBox.critical(self, "错误", f"连接TSMaster设备异常: {str(e)}")
            logger.exception(f"连接TSMaster设备异常: {str(e)}")

    def disconnect_tsmaster(self):
        """断开TSMaster设备连接"""
        if self.lin_communicator:
            try:
                res = self.lin_communicator.disconnect()
                
                # 无论断开结果如何，都更新UI状态
                self.is_tsmaster_connected = False
                self.tsmaster_connect_btn.setText("连接")

                self.tsmaster_channel_combo.setEnabled(True)
                self.tsmaster_baudrate_combo.setEnabled(True)
                
                # 禁用发送按钮（如果当前是TSMaster标签页）
                if self.tab_widget.currentIndex() == 1:
                    self.send_btn.setEnabled(False)
                   # self.raw_send_btn.setEnabled(False)
                
                # 记录断开信息
                self.show_status(True, "已断开TSMaster")
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                self.add_message_to_table(current_time, "--", "已断开TSMaster LIN设备连接", "--", "成功")
                
                logger.info("已断开TSMaster LIN设备连接")
                
                # 清理资源
                self.lin_communicator = None
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"断开TSMaster设备异常: {str(e)}")
                logger.exception(f"断开TSMaster设备异常: {str(e)}")
                self.lin_communicator = None

    def create_message_control_panel(self):
        """创建报文控制面板"""
        control_layout = QGridLayout()

        # 将命令字节标签改为PID
        cmd_label = QLabel("PID:")
        self.cmd_edit = QLineEdit()
        cmd_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.cmd_edit.setPlaceholderText("输入字节PID(例如:3F)")
        self.cmd_edit.setMinimumWidth(300)
        self.cmd_edit.setMinimumHeight(45)
        self.cmd_edit.setMaxLength(2)
        # 设置16进制输入验证
        hex_validator = QRegExpValidator(QRegExp("[0-9A-Fa-f]{1,2}"))
        self.cmd_edit.setValidator(hex_validator)

        # 数据输入
        data_label = QLabel("DATA:")
        self.byte_edit_container = QWidget()
        byte_container_layout = QHBoxLayout(self.byte_edit_container) # Layout FOR the container widget
        byte_container_layout.setContentsMargins(0, 0, 0, 0) # No internal margins for the container
        byte_container_layout.setSpacing(1) # Spacing between byte edits

        self.byte_edits = []  # Store references to the QLineEdit widgets
        for i in range(8):
            byte_edit = QLineEdit()
            byte_edit.setInputMask("HH")
            byte_edit.setMaxLength(1)
            byte_edit.setFixedWidth(75)
            byte_edit.setFixedHeight(45)
            byte_edit.setAlignment(Qt.AlignCenter)
            byte_edit.setText("00") # Default value
            self.byte_edits.append(byte_edit)
            byte_container_layout.addWidget(byte_edit) # Add to the container's layout
        
        self.byte_edit_container.setFixedWidth(8 * 50 + 7 * 10)  # 8个输入框 + 7个像素的间距

        self.periodic_checkbox = QCheckBox("周期发送:")
        self.periodic_checkbox.setLayoutDirection(Qt.RightToLeft)
        self.period_spinbox = QSpinBox()
        self.period_spinbox.setSuffix(" ms") # Add "ms" suffix
        self.period_spinbox.setMinimum(0)    # 0 could mean send once
        self.period_spinbox.setMaximum(600000) # Max 10 minutes, adjust as needed
        self.period_spinbox.setValue(1000)   # Default to 1000 ms (1 second)
        self.period_spinbox.setFixedWidth(180) # Adjust width as needed
        self.period_spinbox.setFixedHeight(45)  # Adjust width as needed
        self.period_spinbox.setEnabled(False)

        # 创建一个容器放置周期相关控件
        period_container = QWidget()
        period_layout = QHBoxLayout(period_container)
        period_layout.setContentsMargins(0, 0, 0, 0)  # 无内边距
        period_layout.addWidget( self.periodic_checkbox)
        period_layout.addWidget(self.period_spinbox)

        # 报文格式说明
        format_label = QLabel("报文格式: 0xA1 + [长度高字节] + [长度低字节] + [CMD=71] + [PID] + [数据...] + [校验和] + 0xB1")

        # 停止全部周期发送报文 按钮
        self.stop_period_btn = QPushButton("停止全部周期")
        self.stop_period_btn.setStyleSheet("""
            QPushButton {
                background-color: #4a90e2;
                color: white;
                border: none;
                padding: 5px 2px;
                border-radius: 4px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #357abd;
            }
        """)
        self.stop_period_btn.setEnabled(False)  # 初始禁用

        # 发送按钮
        self.send_btn = QPushButton("发送")

        self.send_btn.setEnabled(False)  # 初始禁用

        # 添加到布局
        control_layout.addWidget(cmd_label, 0, 0)
        control_layout.addWidget(self.cmd_edit, 0, 1) # cmd_edit 在第 1 列
        control_layout.addWidget(data_label, 0, 2)
        control_layout.addWidget(self.byte_edit_container, 0, 3) # byte_edit_container 在第 3 列
        control_layout.addWidget(period_container, 0, 4, 1, 2)  # 占据第4和第5列
        control_layout.addWidget(self.send_btn,    0, 6, 1, 1) # Button moved
        spacer_widget = QWidget()
        control_layout.addWidget(spacer_widget, 0, 7, 1, 1)

        # Row 1
        # Span 1 row, 7 columns (0 through 6)
        control_layout.addWidget(format_label,          1, 0, 1, 6)
        control_layout.addWidget(self.stop_period_btn,    1, 6, 1, 1) # Button moved

        # 设置列伸缩因子，控制各列的宽度比例
        control_layout.setColumnStretch(0, 0)  # 标签列不伸缩
        control_layout.setColumnStretch(1, 1)  # cmd_edit可以适当伸缩
        control_layout.setColumnStretch(2, 0)  # 标签列不伸缩
        control_layout.setColumnStretch(3, 0)  # 字节编辑区域可以多伸缩
        control_layout.setColumnStretch(4, 0)  # 标签列不伸缩
        control_layout.setColumnStretch(5, 0)  # spinbox可以适当伸缩
        control_layout.setColumnStretch(6, 2)  # 按钮可以适当伸缩
        control_layout.setColumnStretch(7, 8)  # 空白区域获得最大伸缩比例

        # --- Connect checkbox state change to enable/disable spinbox ---
        self.periodic_checkbox.stateChanged.connect(self._update_period_spinbox_state)

        # # 添加原始报文输入区域
        # raw_label = QLabel("原始报文:")
        # self.raw_edit = QLineEdit()
        # self.raw_edit.setPlaceholderText("输入完整的16进制报文，以空格分隔 (例如: A1 00 0A 71 3F ...)") # 稍微缩短提示

        # # 原始报文发送按钮
        # self.raw_send_btn = QPushButton("发送原始报文")
        # self.raw_send_btn.setStyleSheet("""
        #     QPushButton {
        #         background-color: #FF9800;
        #         color: white;
        #         border: none;
        #         padding: 5px 10px;
        #         border-radius: 4px;
        #         min-width: 120px;
        #     }
        #     QPushButton:hover {
        #         background-color: #F57C00;
        #     }
        # """)
        # self.raw_send_btn.setEnabled(False)  # 初始禁用

        # # 添加原始报文区域到布局
        # control_layout.addWidget(raw_label, 2, 0)
        # control_layout.addWidget(self.raw_edit, 2, 1, 1, 3) # raw_edit 跨越 1, 2, 3 列
        # control_layout.addWidget(self.raw_send_btn, 2, 4)

        # # 添加原始报文格式说明
        # raw_format_label = QLabel("直接输入完整的报文，将按原样发送，不进行校验和计算")
        # raw_format_label.setStyleSheet("color: gray; font-style: italic;")
        # control_layout.addWidget(raw_format_label, 3, 0, 1, 5)

        # 设置列比例
        # control_layout.setColumnStretch(3, 1) # <--- 移除或注释掉只拉伸第3列的设置

        # 设置新的列伸展比例：让第1列(PID)占1份，第3列(DATA)占4份空间
        control_layout.setColumnStretch(1, 1)
        control_layout.setColumnStretch(3, 4) # 可以调整这个比例，比如 1:3 或 1:5

        # 可选：确保其他列不被拉伸 (通常不需要显式设置)
        # control_layout.setColumnStretch(0, 0)
        # control_layout.setColumnStretch(2, 0)
        # control_layout.setColumnStretch(4, 0)

        return control_layout

    def show_periodic_message_context_menu(self, position):
        """显示周期消息列表的右键菜单"""
        # 仅当有选中项时才显示菜单
        if self.periodic_messages_list.currentRow() < 0:
            return
        
        # 创建右键菜单
        context_menu = QMenu(self)
        
        # 添加菜单项
        stop_action = context_menu.addAction("停止发送")
        edit_action = context_menu.addAction("编辑参数")
        
        # 显示菜单并获取用户选择的操作
        action = context_menu.exec_(self.periodic_messages_list.mapToGlobal(position))
        
        # 根据用户选择执行相应操作
        if action == stop_action:
            self.stop_selected_periodic_message()
        elif action == edit_action:
            self.edit_selected_periodic_message()

    def stop_selected_periodic_message(self):
        """停止选中的周期性消息"""
        current_row = self.periodic_messages_list.currentRow()
        if current_row >= 0:
            # 获取存储在表格单元格中的消息ID
            message_id = self.periodic_messages_list.item(current_row, 0).data(Qt.UserRole)
            self._stop_periodic_message(message_id)

    def edit_selected_periodic_message(self):
        """编辑选中的周期性消息参数"""
        current_row = self.periodic_messages_list.currentRow()
        if current_row >= 0:
            # 获取消息ID
            message_id = self.periodic_messages_list.item(current_row, 0).data(Qt.UserRole)
            
            # 检查消息是否存在
            if not hasattr(self, '_periodic_messages') or message_id not in self._periodic_messages:
                return
                
            message_data = self._periodic_messages[message_id]
            
            # 创建对话框以编辑周期参数
            dialog = QDialog(self)
            dialog.setWindowTitle("编辑周期参数")
            layout = QVBoxLayout(dialog)
            
            # 创建周期输入控件
            period_layout = QHBoxLayout()
            period_layout.addWidget(QLabel("发送周期(ms):"))
            period_spinbox = QSpinBox(dialog)
            period_spinbox.setRange(10, 60000)  # 10ms到60秒
            period_spinbox.setValue(message_data['period_ms'])
            period_layout.addWidget(period_spinbox)
            layout.addLayout(period_layout)
            
            # 创建确认和取消按钮
            buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel, dialog)
            buttons.accepted.connect(dialog.accept)
            buttons.rejected.connect(dialog.reject)
            layout.addWidget(buttons)
            
            # 显示对话框并处理结果
            if dialog.exec_() == QDialog.Accepted:
                # 更新周期
                new_period = period_spinbox.value()
                if new_period != message_data['period_ms']:
                    # 停止当前定时器
                    if message_data['timer'] and message_data['timer'].isActive():
                        message_data['timer'].stop()
                    
                    # 更新周期并重启定时器
                    message_data['period_ms'] = new_period
                    message_data['timer'].start(new_period)
                    
                    # 更新UI显示
                    period_item = self.periodic_messages_list.item(current_row, 2)
                    period_item.setText(str(new_period))
                    
                    self.show_status(True, f"已更新PID={message_data['pid']}的发送周期为{new_period}ms")
                    QTimer.singleShot(2000, lambda: self.show_status(False))

    def _update_period_spinbox_state(self, state):
        """
        Enables or disables the period spinbox based on the checkbox state.
        'state' will be Qt.Checked or Qt.Unchecked.
        """
        is_checked = (state == Qt.Checked)
        self.period_spinbox.setEnabled(is_checked)
        self.stop_period_btn.setEnabled(is_checked)

    def create_bottom_buttons(self):
        """创建底部按钮"""
        button_layout = QHBoxLayout()
        
        # 清空按钮
        clear_btn = QPushButton("清空")

        clear_btn.clicked.connect(self.clear_table)
        
        # 导出按钮
        export_btn = QPushButton("导出")

        export_btn.clicked.connect(self.export_data)
        
        button_layout.addWidget(clear_btn)
        button_layout.addWidget(export_btn)
        button_layout.addStretch()
        
        return button_layout

    def connect_signals(self):
        """连接信号和槽"""
        self.connect_btn.clicked.connect(self.toggle_connection)
        self.send_btn.clicked.connect(self.send_message)
       # self.raw_send_btn.clicked.connect(self.send_raw_message)
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
        self.stop_period_btn.clicked.connect(self.stop_all_periodic_messages)

    def on_tab_changed(self, index):
        """处理标签页切换事件"""
        # 根据当前选择的标签页更新UI状态
        if index == 0:  # USB-LIN标签页
            self.send_btn.setEnabled(self.is_connected)
            #self.raw_send_btn.setEnabled(self.is_connected)
        else:  # TSMaster-LIN标签页
            self.send_btn.setEnabled(self.is_tsmaster_connected)
            #self.raw_send_btn.setEnabled(self.is_tsmaster_connected)

    def refresh_serial_ports(self):
        """刷新可用串口列表"""
        self.port_combo.clear()
        ports = serial.tools.list_ports.comports()
        for port in ports:
            self.port_combo.addItem(port.device)
        
        if self.port_combo.count() == 0:
            self.show_status(True, "未检测到串口")
            logger.warning("未检测到串口设备")
        else:
            self.show_status(False)
            logger.info(f"检测到 {self.port_combo.count()} 个串口设备")

    def toggle_connection(self):
        """切换连接/断开状态"""
        if not self.is_connected:
            self.connect_serial()
        else:
            self.disconnect_serial()

    def connect_serial(self):
        """连接串口"""
        if self.port_combo.count() == 0:
            QMessageBox.warning(self, "警告", "未检测到串口设备")
            return
        
        port = self.port_combo.currentText()
        baudrate = int(self.baudrate_combo.currentText())
        
        try:
            # 创建并启动串口线程
            self.serial_thread = SerialThread(port, baudrate)
            self.serial_thread.received_data.connect(self.handle_received_data)
            self.serial_thread.error_occurred.connect(self.handle_error)
            self.serial_thread.start()
            
            # 更新UI状态
            self.is_connected = True
            self.connect_btn.setText("断开")
            self.send_btn.setEnabled(True)
            #self.raw_send_btn.setEnabled(True)  # 启用原始报文发送按钮
            self.port_combo.setEnabled(False)
            self.baudrate_combo.setEnabled(False)
            self.refresh_btn.setEnabled(False)
            
            self.show_status(True, "已连接")
            logger.info(f"已连接到串口 {port}，波特率: {baudrate}")
            
            # 添加连接信息到表格
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            self.add_message_to_table(current_time, "--", f"已连接到串口 {port}，波特率: {baudrate}", "--", "成功")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"连接串口失败: {str(e)}")
            logger.error(f"连接串口失败: {str(e)}")

    def disconnect_serial(self):
        """断开串口连接"""
        if self.serial_thread:
            self.serial_thread.stop()
            self.serial_thread.wait()  # 等待线程结束
            self.serial_thread = None
        
        # 更新UI状态
        self.is_connected = False
        self.connect_btn.setText("连接")
        self.send_btn.setEnabled(False)
        #self.raw_send_btn.setEnabled(False)  # 禁用原始报文发送按钮
        self.port_combo.setEnabled(True)
        self.baudrate_combo.setEnabled(True)
        self.refresh_btn.setEnabled(True)
        
        self.show_status(True, "已断开")
        logger.info("已断开串口连接")
        
        # 添加断开信息到表格
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        self.add_message_to_table(current_time, "--", "已断开串口连接", "--", "成功")

    # --- Helper methods (now part of the main window class) ---
    def get_data_hex_string(self):
        """Returns the data as a space-separated hex string."""
        hex_values = []
        for byte_edit in self.byte_edits:
            text = byte_edit.text().upper()
            if len(text) == 0:
                hex_values.append("00")
            elif len(text) == 1:
                hex_values.append("0" + text)
            else:
                hex_values.append(text)
        return " ".join(hex_values)

    def send_message(self):
        """发送LIN报文"""
        # 检查当前选择的是哪个Tab
        current_tab = self.tab_widget.currentIndex()
        
        if current_tab == 0:  # USB-LIN
            if not self.is_connected or not self.serial_thread:
                QMessageBox.warning(self, "警告", "请先连接串口")
                return
            
            try:
                # CMD固定为71，获取PID
                cmd = "71"  # CMD固定为71
                pid = self.cmd_edit.text().strip()
                data = self.get_data_hex_string().strip()
                
                # 验证PID输入
                if not pid:
                    QMessageBox.warning(self, "警告", "请输入PID值")
                    return
                
                # 验证PID格式
                try:
                    pid_value = int(pid, 16)
                    if pid_value < 0 or pid_value > 255:
                        QMessageBox.warning(self, "警告", "PID必须在00-FF范围内")
                        return
                    pid = f"{pid_value:02X}"
                except ValueError:
                    QMessageBox.warning(self, "警告", "PID必须是有效的十六进制值")
                    return
                
                # 将数据转换为字节列表，包含PID作为第一个字节
                data_bytes = [int(pid, 16)]
                
                # 处理数据字节
                if data:
                    try:
                        # 检查数据格式
                        data_parts = data.split()
                        for part in data_parts:
                            data_bytes.append(int(part, 16))
                    except ValueError:
                        QMessageBox.warning(self, "警告", "数据必须是有效的十六进制值，以空格分隔")
                        return
                
                # 确保总数据长度为9（PID + 8字节数据）
                while len(data_bytes) < 9:
                    data_bytes.append(0)  # 不足部分用0填充

                # 检查是否开启周期发送
                if self.periodic_checkbox.isChecked():
                    # 获取周期时间(ms)
                    period_ms = self.period_spinbox.value()
                    
                    # 创建唯一ID用于标识此消息
                    message_id = f"{pid}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')}"
                    
                    # 创建消息对象
                    message_data = {
                        'id': message_id,
                        'cmd': cmd,
                        'data_bytes': data_bytes.copy(),  # 使用copy避免后续修改影响此数据
                        'pid': pid,
                        'period_ms': period_ms,
                        'timer': None
                    }
                    
                    # 初始化周期性消息字典（如果尚未初始化）
                    if not hasattr(self, '_periodic_messages'):
                        self._periodic_messages = {}
                    
                    # 创建定时器
                    timer = QTimer(self)
                    timer.timeout.connect(lambda msg_id=message_id: self._send_periodic_message(msg_id))
                    message_data['timer'] = timer
                    
                    # 保存消息到消息字典
                    self._periodic_messages[message_id] = message_data
                    
                    # 启动定时器
                    timer.start(period_ms)
                    
                    # 显示状态提示
                    self.show_status(True, f"已添加PID={pid}的周期发送 (周期: {period_ms}ms)")
                    
                    # 添加到周期发送列表UI中
                    self._add_periodic_message_to_list(message_id, pid, period_ms)                
                else:
                    # 非周期性发送消息
                    self._send_single_message(cmd, data_bytes, pid)
                
            except Exception as e:
                self.show_status(True, "发送错误")
                QMessageBox.critical(self, "错误", f"发送消息失败: {str(e)}")
                logger.error(f"发送消息失败: {str(e)}")
                # 2秒后自动隐藏状态
                QTimer.singleShot(2000, lambda: self.show_status(False))
        
        elif current_tab == 1:  # TSMaster-LIN
            if not self.is_tsmaster_connected or not self.lin_communicator:
                QMessageBox.warning(self, "警告", "请先连接TSMaster设备")
                return
            
            try:
                # 获取PID
                pid = self.cmd_edit.text().strip()
                data = self.get_data_hex_string().strip()
                
                # 验证PID输入
                if not pid:
                    QMessageBox.warning(self, "警告", "请输入PID值")
                    return
                
                # 验证PID格式
                try:
                    pid_value = int(pid, 16)
                    if pid_value < 0 or pid_value > 255:
                        QMessageBox.warning(self, "警告", "PID必须在00-FF范围内")
                        return
                    pid = f"{pid_value:02X}"
                except ValueError:
                    QMessageBox.warning(self, "警告", "PID必须是有效的十六进制值")
                    return
                
                # 构建消息字符串，格式: "PID DATA1 DATA2..."
                message = pid
                if data:
                    # 检查数据格式
                    try:
                        data_parts = data.split()
                        for part in data_parts:
                            int(part, 16)  # 检查是否为有效十六进制值
                        message += " " + data
                    except ValueError:
                        QMessageBox.warning(self, "警告", "数据必须是有效的十六进制值，以空格分隔")
                        return
                
                # 发送消息
                res, msg = self.lin_communicator.send_message(message)
                
                if res == 0:
                    # 记录发送的消息
                    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                    
                    # 构建显示的数据字符串
                    display_data = f"PID:0x{pid}"
                    if data:
                        display_data += f", Data: {data}"
                    
                    # 在表格中添加发送记录
                    self.add_message_to_table(current_time, pid, display_data, "发送(TSMaster)", "成功")
                    
                    # 显示状态提示
                    self.show_status(True, "已发送LIN帧")
                    
                    # 2秒后自动隐藏状态
                    QTimer.singleShot(2000, lambda: self.show_status(False))
                else:
                    self.show_status(True, f"发送失败: {msg}")
                    logger.error(f"通过TSMaster发送消息失败: {msg}")
                    QTimer.singleShot(2000, lambda: self.show_status(False))
                    
            except Exception as e:
                self.show_status(True, "发送错误")
                QMessageBox.critical(self, "错误", f"通过TSMaster发送消息异常: {str(e)}")
                logger.exception(f"通过TSMaster发送消息异常: {str(e)}")
                QTimer.singleShot(2000, lambda: self.show_status(False))

    def _send_single_message(self, cmd, data_bytes, pid):
        """发送单条消息"""
        success, hex_message = self.serial_thread.send_data(cmd, data_bytes)
        
        if success:
            # 记录发送的消息
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            
            # 在表格中添加发送记录，显示"CMD/PID"格式
            cmd_display = f"71/{pid}"
            self.add_message_to_table(current_time, cmd_display, hex_message, "发送", "成功")
            
            # 显示状态提示
            self.show_status(True, "已发送")
            
            # 2秒后自动隐藏状态
            QTimer.singleShot(2000, lambda: self.show_status(False))
        else:
            self.show_status(True, "发送失败")

    def _send_periodic_message(self, message_id):
        """周期性发送指定ID的消息"""
        if not hasattr(self, '_periodic_messages'):
            return
            
        # 如果消息ID不存在，直接返回
        if message_id not in self._periodic_messages:
            return
            
        message_data = self._periodic_messages[message_id]
        
        # 检查连接状态
        if not self.is_connected or not self.serial_thread:
            self._stop_periodic_message(message_id)
            self.show_status(True, f"PID={message_data['pid']}的周期发送已停止：串口已断开")
            return
        
        # 发送消息
        cmd = message_data['cmd']
        data_bytes = message_data['data_bytes']
        pid = message_data['pid']
        
        success, hex_message = self.serial_thread.send_data(cmd, data_bytes)
        
        if success:
            # 记录发送的消息
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            
            # 在表格中添加发送记录，显示"CMD/PID"格式
            cmd_display = f"71/{pid}"
            self.add_message_to_table(current_time, cmd_display, hex_message, "发送", "成功")
        else:
            # 如果发送失败，停止该消息的周期发送
            self._stop_periodic_message(message_id)
            self.show_status(True, f"PID={pid}的周期发送失败，已停止")
            QTimer.singleShot(2000, lambda: self.show_status(False))

    def _stop_periodic_message(self, message_id):
        """停止指定ID的周期发送"""
        if hasattr(self, '_periodic_messages') and message_id in self._periodic_messages:
            message_data = self._periodic_messages[message_id]
            
            # 停止定时器
            if message_data['timer'] and message_data['timer'].isActive():
                message_data['timer'].stop()
            
            # 从周期发送列表中移除
            pid = message_data['pid']
            self._remove_periodic_message_from_list(message_id)
            
            # 从字典中删除
            del self._periodic_messages[message_id]
            
            self.show_status(True, f"已停止PID={pid}的周期发送")
            QTimer.singleShot(2000, lambda: self.show_status(False))

    def stop_all_periodic_messages(self):
        """停止所有周期性发送"""
        if hasattr(self, '_periodic_messages'):
            message_ids = list(self._periodic_messages.keys())
            for message_id in message_ids:
                self._stop_periodic_message(message_id)
            
            self.show_status(True, "已停止所有周期发送")
            QTimer.singleShot(2000, lambda: self.show_status(False))

    def closeEvent(self, event):
        """关闭窗口时停止所有定时器"""
        # 确保停止所有周期发送
        self.stop_all_periodic_messages()
        
        # 原有的closeEvent逻辑...
        super().closeEvent(event)

    # 以下方法需要根据实际UI设计来实现
    def _add_periodic_message_to_list(self, message_id, pid, period_ms):
        """将周期消息添加到UI表格中"""
        if not hasattr(self, 'periodic_messages_list'):
            return
        
        # 准备数据字符串显示(例如: "01 02 03 04...")
        data_str = self.get_data_hex_string()
        
        # 添加新行
        row = self.periodic_messages_list.rowCount()
        self.periodic_messages_list.insertRow(row)
        
        # 创建单元格并设置数据
        pid_item = QTableWidgetItem(pid)
        pid_item.setData(Qt.UserRole, message_id)  # 存储消息ID用于后续操作
        
        data_item = QTableWidgetItem(data_str)
        period_item = QTableWidgetItem(str(period_ms))
        status_item = QTableWidgetItem("运行中")
        
        # 设置单元格不可编辑
        pid_item.setFlags(pid_item.flags() & ~Qt.ItemIsEditable)
        data_item.setFlags(data_item.flags() & ~Qt.ItemIsEditable)
        period_item.setFlags(period_item.flags() & ~Qt.ItemIsEditable)
        status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
        
        # 添加到表格
        self.periodic_messages_list.setItem(row, 0, pid_item)
        self.periodic_messages_list.setItem(row, 1, data_item)
        self.periodic_messages_list.setItem(row, 2, period_item)
        self.periodic_messages_list.setItem(row, 3, status_item)
        
        # 设置固定的列宽比例
        total_width = self.periodic_messages_list.width()
        self.periodic_messages_list.setColumnWidth(0, int(total_width * 0.15))  # PID列
        self.periodic_messages_list.setColumnWidth(1, int(total_width * 0.5))   # 数据列
        self.periodic_messages_list.setColumnWidth(2, int(total_width * 0.15))  # 周期列
        self.periodic_messages_list.setColumnWidth(3, int(total_width * 0.2))   # 状态列

    def _remove_periodic_message_from_list(self, message_id):
        """从UI表格中移除周期消息"""
        if not hasattr(self, 'periodic_messages_list'):
            return
        
        # 查找包含该message_id的行
        for row in range(self.periodic_messages_list.rowCount()):
            pid_item = self.periodic_messages_list.item(row, 0)
            if pid_item and pid_item.data(Qt.UserRole) == message_id:
                self.periodic_messages_list.removeRow(row)
                break

    def handle_received_data(self, time_str, cmd, data):
        """处理接收到的数据"""
        # 简化日志输出
        logger.info(f"处理接收数据：CMD={cmd}")
        
        # 尝试从接收的数据中提取PID（如果数据格式符合预期）
        parts = data.split()
        
        # 更宽松的匹配条件，只要cmd是71就尝试解析PID
        if cmd == "71" and len(parts) >= 5:
            # 直接尝试获取PID (CMD后的第一个字节)
            try:
                pid_index = 4  # 通常PID在第5个位置（0-based index为4）
                pid = parts[pid_index]
                cmd_display = f"71/{pid}"
            except IndexError:
                # 如果索引超出范围，则使用原始CMD
                cmd_display = cmd
                logger.warning(f"无法提取PID，原始数据过短")
        elif len(parts) >= 5 and parts[0] == "A1" and parts[3] == "71" and len(parts) >= 6:
            # 原始逻辑保留，处理标准格式的消息
            pid = parts[4]
            cmd_display = f"71/{pid}"
        else:
            # 保持原来的显示方式
            cmd_display = cmd
            
        # 添加到表格并高亮显示
        self.add_message_to_table(time_str, cmd_display, data, "接收", "成功", is_received=True)
        
        # 显示状态提示
        if '/' in cmd_display:
            self.show_status(True, f"收到消息: {cmd_display}")
        else:
            self.show_status(True, f"收到CMD={cmd}")
           
        # 2秒后自动隐藏状态
        QTimer.singleShot(2000, lambda: self.show_status(False))

    def handle_error(self, error_msg):
        """处理错误"""
        self.show_status(True, "错误")
        logger.error(error_msg)
        
        # 如果是严重错误，可能需要断开连接
        if "串口未打开" in error_msg or "串口通信错误" in error_msg:
            self.disconnect_serial()
            QMessageBox.critical(self, "错误", error_msg)

    def init_status_label(self):
        """初始化状态标签"""
        self.status_label = QLabel(self)
        self.status_label.setFixedSize(220, 42)
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.hide()

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        self.update_status_position()
        self.update_column_widths()

    def update_status_position(self):
        """更新状态标签位置"""
        x = self.width() - self.status_label.width() - 20
        y = self.height() - self.status_label.height() - 8
        self.status_label.move(x, y)

    def show_status(self, show=True, text="处理中..."):
        """显示/隐藏状态标签"""
        if show:
            self.status_label.setText(text)
            self.status_label.show()
            self.update_status_position()
        else:
            self.status_label.hide()

    def update_column_widths(self):
        """更新表格列宽"""
        total_width = self.table.viewport().width()
        for i, ratio in enumerate(self.column_ratios):
            self.table.setColumnWidth(i, int(total_width * ratio))
        msg_width = self.periodic_messages_list.viewport().width()
        for i, ratio in enumerate(self.msglist_ratios):
            self.periodic_messages_list.setColumnWidth(i, int(msg_width * ratio))

    def clear_table(self):
        """清空表格数据"""
        self.table.setRowCount(0)
        logger.info("表格数据已清空")
        QMessageBox.information(self, "提示", "数据已清空")

    def export_data(self):
        """导出表格数据"""
        if self.table.rowCount() == 0:
            QMessageBox.information(self, "提示", "没有数据可导出")
            return
        
        try:
            # 获取保存文件路径
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出数据", "", "CSV文件 (*.csv);;文本文件 (*.txt)"
            )
            
            if not file_path:
                return  # 用户取消
            
            # 导出数据
            with open(file_path, 'w', encoding='utf-8') as f:
                # 写入表头
                headers = ["时间", "CMD/PID", "数据", "方向", "状态"]
                f.write(','.join(headers) + '\n')
                
                # 写入数据
                for row in range(self.table.rowCount()):
                    row_data = []
                    for col in range(self.table.columnCount()):
                        item = self.table.item(row, col)
                        text = item.text() if item else ""
                        # 替换逗号，防止CSV格式错误
                        text = text.replace(',', ';')
                        row_data.append(text)
                    f.write(','.join(row_data) + '\n')
            
            logger.info(f"数据已导出到: {file_path}")
            QMessageBox.information(self, "提示", f"数据已导出到: {file_path}")
            
        except Exception as e:
            logger.error(f"导出数据失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"导出数据失败: {str(e)}")


    def add_message_to_table(self, time_str, cmd, data, direction, status, is_received=False):
        """添加消息到表格
        
        Args:
            time_str: 时间字符串
            cmd: 命令字节
            data: 数据内容
            direction: 方向（发送/接收）
            status: 状态
            is_received: 是否为接收的消息
        """
        row = self.table.rowCount()
        self.table.insertRow(row)
        
        # 创建表格项
        time_item = QTableWidgetItem(time_str)
        cmd_item = QTableWidgetItem(cmd)
        data_item = QTableWidgetItem(data)
        direction_item = QTableWidgetItem(direction)
        status_item = QTableWidgetItem(status)
        
        # 设置接收消息的样式
        if is_received:
            # 接收消息使用蓝色字体
            font = QFont()
            font.setBold(True)
            time_item.setFont(font)
            cmd_item.setFont(font)
            direction_item.setFont(font)
            status_item.setFont(font)
            
            # 设置背景色为淡蓝色
            bg_color = QColor(230, 240, 255)  # 淡蓝色
            time_item.setBackground(bg_color)
            cmd_item.setBackground(bg_color)
            data_item.setBackground(bg_color)
            direction_item.setBackground(bg_color)
            status_item.setBackground(bg_color)
            
            # 设置文本颜色
            text_color = QColor(0, 0, 180)  # 蓝色
            cmd_item.setForeground(text_color)
            direction_item.setForeground(text_color)
        
        # 添加到表格
        self.table.setItem(row, 0, time_item)
        self.table.setItem(row, 1, cmd_item)
        self.table.setItem(row, 2, data_item)
        self.table.setItem(row, 3, direction_item)
        self.table.setItem(row, 4, status_item)
        
        # 滚动到最新行
        self.table.scrollToBottom()
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 断开串口连接
        if self.is_connected:
            self.disconnect_serial()
        
        # 断开TSMaster连接
        if self.is_tsmaster_connected and self.lin_communicator:
            self.disconnect_tsmaster()

        # 确保停止所有周期发送
        self.stop_all_periodic_messages()
        
        event.accept()

    # def send_raw_message(self):
    #     """发送原始LIN报文"""
    #     # 检查当前选择的是哪个Tab
    #     current_tab = self.tab_widget.currentIndex()
        
    #     if current_tab == 0:  # USB-LIN
    #         if not self.is_connected or not self.serial_thread:
    #             QMessageBox.warning(self, "警告", "请先连接串口")
    #             return
            
    #         try:
    #             # 获取原始报文数据
    #             raw_data = self.raw_edit.text().strip()
                
    #             # 验证是否有数据
    #             if not raw_data:
    #                 QMessageBox.warning(self, "警告", "请输入原始报文数据")
    #                 return
                
    #             # 验证格式
    #             try:
    #                 # 简单检查是否都是有效的十六进制值
    #                 hex_parts = raw_data.split()
    #                 for part in hex_parts:
    #                     int(part, 16)
    #             except ValueError:
    #                 QMessageBox.warning(self, "警告", "原始报文必须是有效的十六进制值，以空格分隔")
    #                 return
                
    #             # 发送消息
    #             success, hex_message = self.serial_thread.send_raw_data(raw_data)
                
    #             if success:
    #                 # 记录发送的消息
    #                 current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                    
    #                 # 提取报文中的CMD（如果存在）
    #                 cmd_display = "RAW"
    #                 hex_parts = hex_message.split()
    #                 if len(hex_parts) >= 4 and hex_parts[0] == "A1":
    #                     try:
    #                         cmd = hex_parts[3]
    #                         if cmd == "71" and len(hex_parts) >= 5:
    #                             pid = hex_parts[4]
    #                             cmd_display = f"71/{pid}"
    #                         else:
    #                             cmd_display = cmd
    #                     except IndexError:
    #                         pass
                    
    #                 # 在表格中添加发送记录
    #                 self.add_message_to_table(current_time, cmd_display, hex_message, "发送(原始)", "成功")
                    
    #                 # 显示状态提示
    #                 self.show_status(True, "已发送原始报文")
                    
    #                 # 2秒后自动隐藏状态
    #                 QTimer.singleShot(2000, lambda: self.show_status(False))
    #             else:
    #                 self.show_status(True, "发送失败")
                
    #         except Exception as e:
    #             self.show_status(True, "发送错误")
    #             QMessageBox.critical(self, "错误", f"发送原始报文失败: {str(e)}")
    #             logger.error(f"发送原始报文失败: {str(e)}")
    #             # 2秒后自动隐藏状态
    #             QTimer.singleShot(2000, lambda: self.show_status(False))
        
    #     elif current_tab == 1:  # TSMaster-LIN
    #         if not self.is_tsmaster_connected or not self.lin_communicator:
    #             QMessageBox.warning(self, "警告", "请先连接TSMaster设备")
    #             return
            
    #         try:
    #             # 获取原始报文数据
    #             raw_data = self.raw_edit.text().strip()
                
    #             # 验证是否有数据
    #             if not raw_data:
    #                 QMessageBox.warning(self, "警告", "请输入原始报文数据")
    #                 return
                
    #             # 验证格式
    #             try:
    #                 # 简单检查是否都是有效的十六进制值
    #                 hex_parts = raw_data.split()
    #                 for part in hex_parts:
    #                     int(part, 16)
    #             except ValueError:
    #                 QMessageBox.warning(self, "警告", "原始报文必须是有效的十六进制值，以空格分隔")
    #                 return
                
    #             # 发送消息
    #             res, msg = self.lin_communicator.send_message(raw_data)
                
    #             if res == 0:
    #                 # 记录发送的消息
    #                 current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                    
    #                 # 提取报文中的PID（第一个字节）
    #                 try:
    #                     hex_parts = raw_data.split()
    #                     pid = hex_parts[0]
    #                     cmd_display = pid
    #                 except IndexError:
    #                     cmd_display = "RAW"
                    
    #                 # 在表格中添加发送记录
    #                 self.add_message_to_table(current_time, cmd_display, raw_data, "发送原始(TSMaster)", "成功")
                    
    #                 # 显示状态提示
    #                 self.show_status(True, "已发送原始报文")
                    
    #                 # 2秒后自动隐藏状态
    #                 QTimer.singleShot(2000, lambda: self.show_status(False))
    #             else:
    #                 self.show_status(True, f"发送失败: {msg}")
    #                 logger.error(f"通过TSMaster发送原始报文失败: {msg}")
    #                 QTimer.singleShot(2000, lambda: self.show_status(False))
                    
    #         except Exception as e:
    #             self.show_status(True, "发送错误")
    #             QMessageBox.critical(self, "错误", f"通过TSMaster发送原始报文异常: {str(e)}")
    #             logger.exception(f"通过TSMaster发送原始报文异常: {str(e)}")
    #             QTimer.singleShot(2000, lambda: self.show_status(False))

    def send_external_message(self, hex_message):
        """
        外部接口：发送原始十六进制报文
        
        Args:
            hex_message: 十六进制字符串，格式如"A1 00 0A 71 3F 00 00 00 00 00 00 00 00 BA B1"
            
        Returns:
            bool: 是否成功发送
        """
        # 检查当前活动的Tab
        current_tab = self.tab_widget.currentIndex()
        
        if current_tab == 0:  # USB-LIN
            # 检查是否已连接
            if not self.is_connected or not self.serial_thread:
                logger.error("发送外部消息失败：串口未连接")
                return False
                
            try:
                # 发送消息
                success, sent_hex = self.serial_thread.send_raw_data(hex_message)
                
                if success:
                    # 记录发送的消息
                    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                    
                    # 提取报文中的CMD（如果存在）
                    cmd_display = "RAW"
                    hex_parts = sent_hex.split()
                    if len(hex_parts) >= 4 and hex_parts[0] == "A1":
                        try:
                            cmd = hex_parts[3]
                            if cmd == "71" and len(hex_parts) >= 5:
                                pid = hex_parts[4]
                                cmd_display = f"71/{pid}"
                            else:
                                cmd_display = cmd
                        except IndexError:
                            pass
                    
                    # 在表格中添加发送记录
                    self.add_message_to_table(current_time, cmd_display, sent_hex, "发送(外部)", "成功")
                    
                    logger.info(f"外部接口发送成功: {sent_hex}")
                    return True
                else:
                    logger.error("外部接口发送失败")
                    return False
                    
            except Exception as e:
                logger.error(f"外部接口发送异常: {str(e)}")
                return False
        
        elif current_tab == 1:  # TSMaster-LIN
            # TODO: 实现TSMaster外部接口发送
            logger.error("TSMaster-LIN外部接口发送功能待实现")
            return False
            
    def send_external_message_with_pid(self, pid, data=""):
        """
        外部接口：通过提供PID和数据来发送报文，类似GUI界面输入方式
        
        Args:
            pid: 十六进制字符串PID，如"3F"
            data: 十六进制字符串数据，以空格分隔，如"01 02 03 04"，不足8字节会自动填充00
            
        Returns:
            bool: 是否成功发送
        """
        # 检查当前活动的Tab
        current_tab = self.tab_widget.currentIndex()
        
        if current_tab == 0:  # USB-LIN
            # 检查是否已连接
            if not self.is_connected or not self.serial_thread:
                logger.error("发送外部消息失败：串口未连接")
                return False
                
            try:
                # CMD固定为71
                cmd = "71"
                
                # 验证PID格式
                try:
                    pid_value = int(pid, 16)
                    if pid_value < 0 or pid_value > 255:
                        logger.error(f"PID值无效: {pid}，必须在00-FF范围内")
                        return False
                    pid = f"{pid_value:02X}"
                except ValueError:
                    logger.error(f"PID不是有效的十六进制值: {pid}")
                    return False
                
                # 将数据转换为字节列表，PID作为第一个字节
                data_bytes = [int(pid, 16)]
                
                # 处理数据字节
                if data:
                    try:
                        # 检查数据格式
                        data_parts = data.split()
                        for part in data_parts:
                            data_bytes.append(int(part, 16))
                    except ValueError:
                        logger.error(f"数据包含无效的十六进制值: {data}")
                        return False
                
                # 确保总数据长度为9（PID + 8字节数据）
                while len(data_bytes) < 9:
                    data_bytes.append(0)  # 不足部分用0填充
                
                # 发送消息
                success, hex_message = self.serial_thread.send_data(cmd, data_bytes)
                
                if success:
                    # 记录发送的消息
                    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                    
                    # 在表格中添加发送记录
                    cmd_display = f"71/{pid}"
                    self.add_message_to_table(current_time, cmd_display, hex_message, "发送(外部PID)", "成功")
                    
                    logger.info(f"外部PID接口发送成功: PID={pid}, 数据={data}, 消息={hex_message}")
                    return True
                else:
                    logger.error("外部PID接口发送失败")
                    return False
                    
            except Exception as e:
                logger.error(f"外部PID接口发送异常: {str(e)}")
                return False
        
        elif current_tab == 1:  # TSMaster-LIN
            # TODO: 实现TSMaster外部PID接口发送
            logger.error("TSMaster-LIN外部PID接口发送功能待实现")
            return False


class LinSender:
    """LIN报文发送器类"""
    
    def __init__(self):
        """初始化"""
        self.serial_port = None
        self.is_connected = False
        self.receive_thread = None
        self.is_receiving = False
        self.receive_callback = None
        self.periodic_threads={}    # 格式: {pid: (thread, stop_event)}
    
    def set_receive_callback(self, callback):
        """
        设置接收数据的回调函数
        
        Args:
            callback: 回调函数，接收一个参数(bytes_data)，返回值将被忽略
        """
        self.receive_callback = callback
    
    def _receive_thread_func(self):
        """接收线程函数，用于接收并打印原始报文"""
        logger.info("接收线程已启动")
        
        try:
            while self.is_receiving and self.serial_port and self.serial_port.is_open:
                if self.serial_port.in_waiting:
                    # 读取数据
                    data = self.serial_port.read(self.serial_port.in_waiting)
                    if data:
                        # 打印原始报文
                        hex_data = ' '.join([f"{b:02X}" for b in data])
                        logger.info(f"收到原始数据: {hex_data}")
                        
                        # 调用回调函数(如果有)
                        if self.receive_callback:
                            try:
                                self.receive_callback(data)
                            except Exception as e:
                                logger.error(f"回调函数异常: {str(e)}")
                
                # 短暂休眠，避免CPU占用过高
                time.sleep(0.01)
                
        except Exception as e:
            logger.error(f"接收线程异常: {str(e)}")
        finally:
            logger.info("接收线程已退出")
    
    def connect(self, port, baudrate=115200):
        """
        连接串口
        
        Args:
            port: 串口名称，例如'COM1'
            baudrate: 波特率，默认115200
            
        Returns:
            bool: 是否成功连接
        """
        try:
            self.serial_port = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=0.1
            )
            self.is_connected = True
            logger.info(f"已连接到串口 {port}，波特率: {baudrate}")
            
            # 启动接收线程
            self.is_receiving = True
            self.receive_thread = threading.Thread(target=self._receive_thread_func)
            self.receive_thread.daemon = True  # 设置为守护线程，主线程退出时自动结束
            self.receive_thread.start()
            
            return True
        except Exception as e:
            logger.error(f"连接串口失败: {str(e)}")
            self.is_connected = False
            return False
    
    def disconnect(self):
        """
        断开串口连接
        
        Returns:
            bool: 是否成功断开
        """
        # 停止接收线程
        if self.receive_thread and self.receive_thread.is_alive():
            self.is_receiving = False
            # 等待接收线程结束，但设置超时避免阻塞
            self.receive_thread.join(timeout=1.0)
            self.receive_thread = None
        
        # 关闭串口
        if self.serial_port and self.serial_port.is_open:
            try:
                self.serial_port.close()
                self.is_connected = False
                logger.info("已断开串口连接")
                return True
            except Exception as e:
                logger.error(f"断开串口失败: {str(e)}")
                return False
        
        self.is_connected = False
        return True  # 如果没有连接，也算成功断开
    
    def send_message(self, pid, data=""):
        """
        发送LIN报文
        
        Args:
            pid: 十六进制字符串PID，如"3F"
            data: 十六进制字符串数据，以空格分隔，如"01 02 03 04"，不足8字节会自动填充00
            
        Returns:
            bool: 是否成功发送
            str: 发送的十六进制字符串
        """
        if not self.is_connected or not self.serial_port or not self.serial_port.is_open:
            logger.error("发送失败：串口未连接")
            return False, "failed:串口未连接"
        
        try:
            # CMD固定为71
            cmd = 0x71
            
            # 验证PID格式
            try:
                pid_value = int(pid, 16)
                if pid_value < 0 or pid_value > 255:
                    logger.error(f"PID值无效: {pid}，必须在00-FF范围内")
                    return False, ""
            except ValueError:
                logger.error(f"PID不是有效的十六进制值: {pid}")
                return False, ""
            
            # 准备消息
            # 1. 报文开始符 (1字节，0xA1)
            message = bytearray([0xA1])
            
            # 将数据转换为字节列表
            data_bytes = []
            if data:
                try:
                    # 处理数据字节
                    data_parts = data.split()
                    for part in data_parts:
                        data_bytes.append(int(part, 16))
                except ValueError:
                    logger.error(f"数据包含无效的十六进制值: {data}")
                    return False, ""
            
            # 确保数据长度不超过8字节
            data_bytes = data_bytes[:8]
            
            # 确保数据长度为8字节，不足则填充0
            while len(data_bytes) < 8:
                data_bytes.append(0)
            
            # 2. 数据长度 (2字节，高位在前) - 固定为10（CMD(1) + PID(1) + DATA(8) + 校验和(1)）
            data_len = 10
            message.append((data_len >> 8) & 0xFF)  # 高字节
            message.append(data_len & 0xFF)  # 低字节
            
            # 3. 命令字节 (1字节) - 固定为71
            message.append(cmd)
            
            # 4. PID (1字节)
            message.append(pid_value)
            
            # 5. 数据字节 (8字节)
            message.extend(data_bytes)
            
            # 6. 校验和 (从CMD字节到数据字节的总和)
            checksum = 0
            for i in range(3, 3 + 1 + 1 + len(data_bytes)):  # 从CMD开始，包含CMD、PID和数据
                checksum += message[i]
            checksum = checksum & 0xFF  # 取低8位
            message.append(checksum)
            
            # 7. 报文结束符 (1字节，0xB1)
            message.append(0xB1)
            
            # 发送数据
            self.serial_port.write(message)
            
            # 记录发送的数据
            hex_message = ' '.join([f"{b:02X}" for b in message])
            logger.info(f"发送数据: PID={pid}, 数据长度={len(data_bytes)}, 消息={hex_message}")
            
            return True, hex_message
        except Exception as e:
            logger.error(f"发送数据错误: {str(e)}")
            return False, ""

    # 添加周期性发送方法
    def start_periodic_send(self, pid, lin_data, interval):
        """启动周期性发送LIN消息
        
        Args:
            pid: LIN消息ID
            lin_data: LIN消息数据
            interval: 发送间隔(毫秒)
            
        Returns:
            (bool, str): 成功标志和消息
        """        
        # 先停止之前可能运行的相同PID的周期性发送
        self.stop_periodic_send(pid)
        
        # 创建停止事件
        stop_event = threading.Event()
        
        # 创建并启动线程
        def send_task():
            while not stop_event.is_set():
                self.send_message(pid, lin_data)
                if stop_event.wait(float(interval)/1000.0):
                    break
        
        thread = threading.Thread(target=send_task, daemon=True)
        self.periodic_threads[pid] = (thread, stop_event)
        thread.start()
        return True, "周期性发送已启动"

    def stop_periodic_send(self, pid):
        """停止特定PID的周期性发送
        
        Args:
            pid: 要停止发送的LIN消息ID
            
        Returns:
            (bool, str): 成功标志和消息
        """
        if pid in self.periodic_threads:
            thread, stop_event = self.periodic_threads[pid]
            stop_event.set()
            thread.join(1.0)  # 等待最多1秒
            del self.periodic_threads[pid]
            return True, "周期性发送已停止"
        return False, "没有找到对应的周期性发送任务"

    def stop_all_periodic_send(self):
        """停止所有周期性发送
        
        Returns:
            (bool, str): 成功标志和消息
        """
        for pid in list(self.periodic_threads.keys()):
            self.stop_periodic_send(pid)
        return True, "所有周期性发送已停止"


# 单例模式，方便外部调用
lin_sender = LinSender()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    widget = LinDebugWidget()
    widget.show()
    sys.exit(app.exec_())
