{
    "sequence":
    [
        //间隙拍照
        {
            "name":"左间隙拍照流程",
            "command":
            [
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"."
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取字符串长度",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取字符串长度结果",
                        "string":"获取字符串长度"
                    }
                },
                {
                    "name":"判断获取字符串长度结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"读取间隙图片",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"间隙拍照",
                    "type":"get_camera_frame",
                    "in":
                    {
                        "camera":"左拍后壳相机",
                        "exposure":"550000",
                        "gain":"8.0"
                    },
                    "out":
                    {
                        "result":"间隙拍照结果",
                        "image":"间隙拍照图片"
                    }
                },
                {
                    "name":"跳转获取时间",
                    "type":"jump",
                    "in":
                    {
                        "tag":"获取当前时间"
                    }
                },
                {
                    "name":"读取间隙图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "image":"间隙拍照图片",
                        "result":"间隙拍照结果"
                    }
                },
                {
                    "name":"获取当前时间",
                    "type":"get_date_time",
                    "out":
                    {
                        "result":"获取当前时间结果",
                        "date_time":"当前时间"  
                    }
                    
                },
                {
                    "name":"生成图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"gap_\",\"@当前时间\",\"_\",\"$左SN码\",\".jpg\"]"
                    },
                    "out":
                    {
                        "result":"生成图片名称结果",
                        "combine":"图片名称"
                    }
                },
                {
                    "name":"保存间隙图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@间隙拍照图片",
                        "path":"$左图片文件夹",
                        "name":"@图片名称"
                    },
                    "out":
                    {
                        "result":"保存图片结果",
                        "full_name":"图片路径"
                    }
                },
                {
                    "name":"发送间隙拍照结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"178",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"右间隙拍照流程",
            "command":
            [
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"."
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取字符串长度",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取字符串长度结果",
                        "string":"获取字符串长度"
                    }
                },
                {
                    "name":"判断获取字符串长度结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"读取间隙图片",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"间隙拍照",
                    "type":"get_camera_frame",
                    "in":
                    {
                        "camera":"右拍后壳相机",
                        "exposure":"550000",
                        "gain":"8.0"
                    },
                    "out":
                    {
                        "result":"间隙拍照结果",
                        "image":"间隙拍照图片"
                    }
                },
                {
                    "name":"跳转获取时间",
                    "type":"jump",
                    "in":
                    {
                        "tag":"获取当前时间"
                    }
                },
                {
                    "name":"读取间隙图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "image":"间隙拍照图片",
                        "result":"间隙拍照结果"
                    }
                },
                {
                    "name":"获取当前时间",
                    "type":"get_date_time",
                    "out":
                    {
                        "result":"获取当前时间结果",
                        "date_time":"当前时间"  
                    }
                    
                },
                {
                    "name":"生成图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"gap_\",\"@当前时间\",\"_\",\"$右SN码\",\".jpg\"]"
                    },
                    "out":
                    {
                        "result":"生成图片名称结果",
                        "combine":"图片名称"
                    }
                },
                {
                    "name":"保存间隙图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@间隙拍照图片",
                        "path":"$右图片文件夹",
                        "name":"@图片名称"
                    },
                    "out":
                    {
                        "result":"保存图片结果",
                        "full_name":"图片路径"
                    }
                },
                {
                    "name":"发送间隙拍照结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"178",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        //角度检测
        {
            "name":"左角度检测流程",
            "command":
            [
                {
                    "name":"读取模板图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"temp/cb_temp.jpg"
                    },
                    "out":
                    {
                        "image":"棋盘格模板图片",
                        "result":"棋盘格模板结果"
                    }
                },
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"."
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取字符串长度",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取字符串长度结果",
                        "string":"获取字符串长度"
                    }
                },
                {
                    "name":"判断获取字符串长度结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"读取棋盘格图片",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"棋盘格拍照",
                    "type":"get_camera_frame",
                    "in":
                    {
                        "camera":"左拍后壳相机",
                        "exposure":"150000",
                        "gain":"8.0"
                    },
                    "out":
                    {
                        "result":"棋盘格拍照结果",
                        "image":"棋盘格拍照图片"
                    }
                },
                {
                    "name":"跳转获取时间",
                    "type":"jump",
                    "in":
                    {
                        "tag":"获取当前时间"
                    }
                },
                {
                    "name":"读取棋盘格图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "image":"棋盘格拍照图片",
                        "result":"棋盘格拍照结果"
                    }
                },
                {
                    "name":"获取当前时间",
                    "type":"get_date_time",
                    "out":
                    {
                        "result":"获取当前时间结果",
                        "date_time":"当前时间"  
                    }
                    
                },
                {
                    "name":"生成图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"cb_\",\"@当前时间\",\"_\",\"$左SN码\",\".jpg\"]"
                    },
                    "out":
                    {
                        "result":"生成图片名称结果",
                        "combine":"图片名称"
                    }
                },
                {
                    "name":"离线模式不保存图片",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"判断拍棋盘格结果",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"保存棋盘格图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@棋盘格拍照图片",
                        "path":"$左图片文件夹",
                        "name":"@图片名称"
                    },
                    "out":
                    {
                        "result":"保存图片结果",
                        "full_name":"图片路径"
                    }
                },
                {
                    "name":"判断拍棋盘格结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"跳转算法流程",
                        "condition":"@棋盘格拍照结果"
                    }
                },
                {
                    "name":"跳转拍棋盘格失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示拍棋盘格失败"
                    }
                },
                {
                    "name":"跳转算法流程",
                    "type":"jump",
                    "in":
                    {
                        "tag":"棋盘格向下采样"
                    }
                },
                {
                    "name":"显示拍棋盘格失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"左拍角度检测失败",
                        "level":"error"
                    }
                },
                {
                    "name":"拍棋盘格失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送角度检测NG结果"
                    }
                },
                {
                    "name":"棋盘格向下采样",
                    "type":"pyramid",
                    "in":
                    {
                        "image":"@棋盘格拍照图片",
                        "type":"down",
                        "count":"1"
                    },
                    "out":
                    {
                        "result":"棋盘格向下采样结果",
                        "image_result":"棋盘格向下采样图片"
                    }
                },
                {
                    "name":"棋盘格模板查找棋盘格",
                    "type":"find_chessboard",
                    "in":
                    {
                        "image_in":"@棋盘格模板图片",
                        "w":"5",
                        "h":"7",
                        "flag":"48"
                    },
                    "out":
                    {
                        "result":"棋盘格模板查找棋盘格结果",
                        "x":"棋盘格模板X",
                        "y":"棋盘格模板Y",
                        "image_debug":"棋盘格模板查找图片"
                    }
                },
                {
                    "name":"棋盘格查找棋盘格",
                    "type":"find_chessboard",
                    "in":
                    {
                        "image_in":"@棋盘格向下采样图片",
                        "w":"5",
                        "h":"7",
                        "flag":"48"
                    },
                    "out":
                    {
                        "result":"棋盘格查找棋盘格结果",
                        "x":"棋盘格X",
                        "y":"棋盘格Y",
                        "image_debug":"棋盘格查找图片"
                    }
                },
                {
                    "name":"离线模式XML名字有变化",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"生成xml名称2",
                        //"tag":"判断棋盘格模板查找结果",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"保存棋盘格模板查找图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@棋盘格查找图片",
                        "path":"$左调试文件夹",
                        "name":"cb_temp.jpg"
                    },
                    "out":
                    {
                        "result":"保存棋盘格模板查找图片结果",
                        "full_name":"保存棋盘格模板查找图片路径"
                    }
                },
                {
                    "name":"棋盘格查找图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@棋盘格模板查找图片",
                        "path":"$左调试文件夹",
                        "name":"cb.jpg"
                    },
                    "out":
                    {
                        "result":"保存棋盘格查找图片结果",
                        "full_name":"保存棋盘格查找图片路径"
                    }
                },
                {
                    "name":"判断棋盘格模板查找结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"跳转判断棋盘格查找",
                        "condition":"@棋盘格模板查找棋盘格结果"
                    }
                },
                {
                    "name":"跳转棋盘格模板查找失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示棋盘格模板查找失败"
                    }
                },
                {
                    "name":"跳转判断棋盘格查找",
                    "type":"jump",
                    "in":
                    {
                        "tag":"判断棋盘格查找结果"
                    }
                },
                {
                    "name":"显示棋盘格模板查找失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"左角度检测模板查找棋盘格失败",
                        "level":"error"
                    }
                },
                {
                    "name":"棋盘格模板查找失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送角度检测NG结果"
                    }
                },
                {
                    "name":"判断棋盘格查找结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"跳转棋盘格仿射变换",
                        "condition":"@棋盘格查找棋盘格结果"
                    }
                },
                {
                    "name":"跳转棋盘格查找失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示棋盘格查找失败"
                    }
                },
                {
                    "name":"跳转棋盘格仿射变换",
                    "type":"jump",
                    "in":
                    {
                        "tag":"棋盘格仿射变换"
                    }
                },
                {
                    "name":"显示棋盘格查找失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"左角度检测查找棋盘格失败",
                        "level":"error"
                    }
                },
                {
                    "name":"棋盘格查找失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送角度检测NG结果"
                    }
                },
                {
                    "name":"棋盘格仿射变换",
                    "type":"estimate_affine2D",
                    "in":
                    {
                        "from_point_x":"@棋盘格模板X",
                        "from_point_y":"@棋盘格模板Y",
                        "to_point_x":"@棋盘格X",
                        "to_point_y":"@棋盘格Y"
                    },
                    "out":
                    {
                        "result":"棋盘格仿射变换结果",
                        "A":"棋盘格仿射变换A",
                        "B":"棋盘格仿射变换B",
                        "C":"棋盘格仿射变换C",
                        "D":"棋盘格仿射变换D",
                        "E":"棋盘格仿射变换E",
                        "F":"棋盘格仿射变换F"
                    }
                },
                {
                    "name":"起点仿射变换",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"240",
                        "y_in":"240",
                        "A":"@棋盘格仿射变换A",
                        "B":"@棋盘格仿射变换B",
                        "C":"@棋盘格仿射变换C",
                        "D":"@棋盘格仿射变换D",
                        "E":"@棋盘格仿射变换E",
                        "F":"@棋盘格仿射变换F"
                    },
                    "out":
                    {
                        "result":"起点仿射变换结果",
                        "x_out":"起点X",
                        "y_out":"起点Y"
                    }
                },
                {
                    "name":"终点仿射变换",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"240",
                        "y_in":"290",
                        "A":"@棋盘格仿射变换A",
                        "B":"@棋盘格仿射变换B",
                        "C":"@棋盘格仿射变换C",
                        "D":"@棋盘格仿射变换D",
                        "E":"@棋盘格仿射变换E",
                        "F":"@棋盘格仿射变换F"
                    },
                    "out":
                    {
                        "result":"终点仿射变换结果",
                        "x_out":"终点X",
                        "y_out":"终点Y"
                    }
                },
                {
                    "name":"生成标准线",
                    "type":"creat_line",
                    "in":
                    {
                        "x_start":"240",
                        "y_start":"240",
                        "x_end":"240",
                        "y_end":"290"
                    },
                    "out":
                    {
                        "result":"生成标准线结果",
                        "line":"标准线"
                    }
                },
                {
                    "name":"生成测量线",
                    "type":"creat_line",
                    "in":
                    {
                        "x_start":"@起点X",
                        "y_start":"@起点Y",
                        "x_end":"@终点X",
                        "y_end":"@终点Y"
                    },
                    "out":
                    {
                        "result":"生成测量线结果",
                        "line":"测量线"
                    }
                },
                {
                    "name":"计算两线夹角",
                    "type":"calc_line_angle",
                    "in":
                    {
                        "line_1":"@标准线",
                        "line_2":"@测量线"
                    },
                    "out":
                    {
                        "result":"计算两线夹角结果",
                        "angle":"两线夹角"
                    }
                },
                {
                    "name":"判断角度计算结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"跳转推送显示角度检测结果",
                        "condition":"@计算两线夹角结果"
                    }
                },
                {
                    "name":"跳转角度计算失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示角度计算失败"
                    }
                },
                {
                    "name":"跳转推送显示角度检测结果",
                    "type":"jump",
                    "in":
                    {
                        "tag":"推送显示角度检测结果"
                    }
                },
                {
                    "name":"显示角度计算失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"左角度检测角度计算失败",
                        "level":"error"
                    }
                },
                {
                    "name":"角度计算失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送角度检测NG结果"
                    }
                },
                {
                    "name":"推送显示角度检测结果",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"左角度检测角度:\",\"@两线夹角\",\"°\"]"
                    },
                    "out":
                    {
                        "result":"推送显示角度检测结果结果",
                        "combine":"推送角度检测结果"
                    }
                },
                {
                    "name":"显示角度检测结果",
                    "type":"log_output",
                    "in":
                    {
                        "input":"@推送角度检测结果",
                        "level":"info"
                    }
                },
                {
                    "name":"推送测量角度公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"(\",\"@两线夹角\",\">-3)&(\",\"@两线夹角\",\"<3)\"]"
                    },
                    "out":
                    {
                        "result":"推送测量角度公式结果",
                        "combine":"测量角度公式"
                    }
                },
                {
                    "name":"计算测量角度",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@测量角度公式" 
                    },
                    "out":
                    {
                        "result":"计算测量角度",
                        "value":"测量角度结果"
                    }
                },
                {
                    "name":"测量角度结果整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@测量角度结果"                
                    },
                    "out":
                    {
                        "result":"测量角度结果整型结果",
                        "integer":"测量角度结果整型"
                    }
                },
                {
                    "name":"判断测量角度结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"发送角度检测OK结果",
                        "condition":"@测量角度结果整型"
                    }
                },
                {
                    "name":"显示角度测量结果",
                    "type":"log_output",
                    "in":
                    {
                        "input":"左角度检测超范围",
                        "level":"error"
                    }
                },
                {
                    "name":"角度测量失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送角度检测NG结果"
                    }
                },
                {
                    "name":"发送角度检测OK结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"182",
                        "type":"short",
                        "content":"1"
                    }
                },
                {
                    "name":"结束角度检测OK",
                    "type":"end"
                },
                {
                    "name":"发送角度检测NG结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"182",
                        "type":"short",
                        "content":"2"
                    }
                },
                {
                    "name":"结束角度检测NG",
                    "type":"end"
                },
                {
                    "name":"生成xml名称2",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"cb.xml\"]"
                    },
                    "out":
                    {
                        "result":"生成xml名称2结果",
                        "combine":"xml名称2"
                    }
                },
                {
                    "name":"保存棋盘格模板查找图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@棋盘格模板查找图片",
                        "path":"$左图片文件夹",
                        "name":"cb_temp.jpg"
                    },
                    "out":
                    {
                        "result":"保存棋盘格模板查找图片结果",
                        "full_name":"保存棋盘格模板查找图片路径"
                    }
                },
                {
                    "name":"棋盘格查找图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@棋盘格查找图片",
                        "path":"$左图片文件夹",
                        "name":"cb.jpg"
                    },
                    "out":
                    {
                        "result":"保存棋盘格查找图片结果",
                        "full_name":"保存棋盘格查找图片路径"
                    }
                }
            ] 
        },
        {
            "name":"右角度检测流程",
            "command":
            [
                {
                    "name":"读取模板图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"temp/cb_temp.jpg"
                    },
                    "out":
                    {
                        "image":"棋盘格模板图片",
                        "result":"棋盘格模板结果"
                    }
                },
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"."
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取字符串长度",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取字符串长度结果",
                        "string":"获取字符串长度"
                    }
                },
                {
                    "name":"判断获取字符串长度结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"读取棋盘格图片",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"棋盘格拍照",
                    "type":"get_camera_frame",
                    "in":
                    {
                        "camera":"右拍后壳相机",
                        "exposure":"150000",
                        "gain":"8.0"
                    },
                    "out":
                    {
                        "result":"棋盘格拍照结果",
                        "image":"棋盘格拍照图片"
                    }
                },
                {
                    "name":"跳转获取时间",
                    "type":"jump",
                    "in":
                    {
                        "tag":"获取当前时间"
                    }
                },
                {
                    "name":"读取棋盘格图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "image":"棋盘格拍照图片",
                        "result":"棋盘格拍照结果"
                    }
                },
                {
                    "name":"获取当前时间",
                    "type":"get_date_time",
                    "out":
                    {
                        "result":"获取当前时间结果",
                        "date_time":"当前时间"  
                    }
                    
                },
                {
                    "name":"生成图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"cb_\",\"@当前时间\",\"_\",\"$右SN码\",\".jpg\"]"
                    },
                    "out":
                    {
                        "result":"生成图片名称结果",
                        "combine":"图片名称"
                    }
                },
                {
                    "name":"离线模式不保存图片",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"判断拍棋盘格结果",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"保存棋盘格图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@棋盘格拍照图片",
                        "path":"$右图片文件夹",
                        "name":"@图片名称"
                    },
                    "out":
                    {
                        "result":"保存图片结果",
                        "full_name":"图片路径"
                    }
                },
                {
                    "name":"判断拍棋盘格结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"跳转算法流程",
                        "condition":"@棋盘格拍照结果"
                    }
                },
                {
                    "name":"跳转拍棋盘格失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示拍棋盘格失败"
                    }
                },
                {
                    "name":"跳转算法流程",
                    "type":"jump",
                    "in":
                    {
                        "tag":"棋盘格向下采样"
                    }
                },
                {
                    "name":"显示拍棋盘格失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右拍角度检测失败",
                        "level":"error"
                    }
                },
                {
                    "name":"拍棋盘格失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送角度检测NG结果"
                    }
                },
                {
                    "name":"棋盘格向下采样",
                    "type":"pyramid",
                    "in":
                    {
                        "image":"@棋盘格拍照图片",
                        "type":"down",
                        "count":"1"
                    },
                    "out":
                    {
                        "result":"棋盘格向下采样结果",
                        "image_result":"棋盘格向下采样图片"
                    }
                },
                {
                    "name":"棋盘格模板查找棋盘格",
                    "type":"find_chessboard",
                    "in":
                    {
                        "image_in":"@棋盘格模板图片",
                        "w":"5",
                        "h":"7",
                        "flag":"48"
                    },
                    "out":
                    {
                        "result":"棋盘格模板查找棋盘格结果",
                        "x":"棋盘格模板X",
                        "y":"棋盘格模板Y",
                        "image_debug":"棋盘格模板查找图片"
                    }
                },
                {
                    "name":"棋盘格查找棋盘格",
                    "type":"find_chessboard",
                    "in":
                    {
                        "image_in":"@棋盘格向下采样图片",
                        "w":"5",
                        "h":"7",
                        "flag":"48"
                    },
                    "out":
                    {
                        "result":"棋盘格查找棋盘格结果",
                        "x":"棋盘格X",
                        "y":"棋盘格Y",
                        "image_debug":"棋盘格查找图片"
                    }
                },
                {
                    "name":"离线模式XML名字有变化",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"生成xml名称2",
                        //"tag":"判断棋盘格模板查找结果",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"保存棋盘格模板查找图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@棋盘格查找图片",
                        "path":"$右调试文件夹",
                        "name":"cb_temp.jpg"
                    },
                    "out":
                    {
                        "result":"保存棋盘格模板查找图片结果",
                        "full_name":"保存棋盘格模板查找图片路径"
                    }
                },
                {
                    "name":"棋盘格查找图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@棋盘格模板查找图片",
                        "path":"$右调试文件夹",
                        "name":"cb.jpg"
                    },
                    "out":
                    {
                        "result":"保存棋盘格查找图片结果",
                        "full_name":"保存棋盘格查找图片路径"
                    }
                },
                {
                    "name":"判断棋盘格模板查找结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"跳转判断棋盘格查找",
                        "condition":"@棋盘格模板查找棋盘格结果"
                    }
                },
                {
                    "name":"跳转棋盘格模板查找失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示棋盘格模板查找失败"
                    }
                },
                {
                    "name":"跳转判断棋盘格查找",
                    "type":"jump",
                    "in":
                    {
                        "tag":"判断棋盘格查找结果"
                    }
                },
                {
                    "name":"显示棋盘格模板查找失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右角度检测模板查找棋盘格失败",
                        "level":"error"
                    }
                },
                {
                    "name":"棋盘格模板查找失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送角度检测NG结果"
                    }
                },
                {
                    "name":"判断棋盘格查找结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"跳转棋盘格仿射变换",
                        "condition":"@棋盘格查找棋盘格结果"
                    }
                },
                {
                    "name":"跳转棋盘格查找失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示棋盘格查找失败"
                    }
                },
                {
                    "name":"跳转棋盘格仿射变换",
                    "type":"jump",
                    "in":
                    {
                        "tag":"棋盘格仿射变换"
                    }
                },
                {
                    "name":"显示棋盘格查找失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右角度检测查找棋盘格失败",
                        "level":"error"
                    }
                },
                {
                    "name":"棋盘格查找失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送角度检测NG结果"
                    }
                },
                {
                    "name":"棋盘格仿射变换",
                    "type":"estimate_affine2D",
                    "in":
                    {
                        "from_point_x":"@棋盘格模板X",
                        "from_point_y":"@棋盘格模板Y",
                        "to_point_x":"@棋盘格X",
                        "to_point_y":"@棋盘格Y"
                    },
                    "out":
                    {
                        "result":"棋盘格仿射变换结果",
                        "A":"棋盘格仿射变换A",
                        "B":"棋盘格仿射变换B",
                        "C":"棋盘格仿射变换C",
                        "D":"棋盘格仿射变换D",
                        "E":"棋盘格仿射变换E",
                        "F":"棋盘格仿射变换F"
                    }
                },
                {
                    "name":"起点仿射变换",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"240",
                        "y_in":"240",
                        "A":"@棋盘格仿射变换A",
                        "B":"@棋盘格仿射变换B",
                        "C":"@棋盘格仿射变换C",
                        "D":"@棋盘格仿射变换D",
                        "E":"@棋盘格仿射变换E",
                        "F":"@棋盘格仿射变换F"
                    },
                    "out":
                    {
                        "result":"起点仿射变换结果",
                        "x_out":"起点X",
                        "y_out":"起点Y"
                    }
                },
                {
                    "name":"终点仿射变换",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"240",
                        "y_in":"290",
                        "A":"@棋盘格仿射变换A",
                        "B":"@棋盘格仿射变换B",
                        "C":"@棋盘格仿射变换C",
                        "D":"@棋盘格仿射变换D",
                        "E":"@棋盘格仿射变换E",
                        "F":"@棋盘格仿射变换F"
                    },
                    "out":
                    {
                        "result":"终点仿射变换结果",
                        "x_out":"终点X",
                        "y_out":"终点Y"
                    }
                },
                {
                    "name":"生成标准线",
                    "type":"creat_line",
                    "in":
                    {
                        "x_start":"240",
                        "y_start":"240",
                        "x_end":"240",
                        "y_end":"290"
                    },
                    "out":
                    {
                        "result":"生成标准线结果",
                        "line":"标准线"
                    }
                },
                {
                    "name":"生成测量线",
                    "type":"creat_line",
                    "in":
                    {
                        "x_start":"@起点X",
                        "y_start":"@起点Y",
                        "x_end":"@终点X",
                        "y_end":"@终点Y"
                    },
                    "out":
                    {
                        "result":"生成测量线结果",
                        "line":"测量线"
                    }
                },
                {
                    "name":"计算两线夹角",
                    "type":"calc_line_angle",
                    "in":
                    {
                        "line_1":"@标准线",
                        "line_2":"@测量线"
                    },
                    "out":
                    {
                        "result":"计算两线夹角结果",
                        "angle":"两线夹角"
                    }
                },
                {
                    "name":"判断角度计算结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"跳转推送显示角度检测结果",
                        "condition":"@计算两线夹角结果"
                    }
                },
                {
                    "name":"跳转角度计算失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示角度计算失败"
                    }
                },
                {
                    "name":"跳转推送显示角度检测结果",
                    "type":"jump",
                    "in":
                    {
                        "tag":"推送显示角度检测结果"
                    }
                },
                {
                    "name":"显示角度计算失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右角度检测角度计算失败",
                        "level":"error"
                    }
                },
                {
                    "name":"角度计算失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送角度检测NG结果"
                    }
                },
                {
                    "name":"推送显示角度检测结果",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"右角度检测角度:\",\"@两线夹角\",\"°\"]"
                    },
                    "out":
                    {
                        "result":"推送显示角度检测结果结果",
                        "combine":"推送角度检测结果"
                    }
                },
                {
                    "name":"显示角度检测结果",
                    "type":"log_output",
                    "in":
                    {
                        "input":"@推送角度检测结果",
                        "level":"info"
                    }
                },
                {
                    "name":"推送测量角度公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"(\",\"@两线夹角\",\">-3)&(\",\"@两线夹角\",\"<3)\"]"
                    },
                    "out":
                    {
                        "result":"推送测量角度公式结果",
                        "combine":"测量角度公式"
                    }
                },
                {
                    "name":"计算测量角度",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@测量角度公式" 
                    },
                    "out":
                    {
                        "result":"计算测量角度",
                        "value":"测量角度结果"
                    }
                },
                {
                    "name":"测量角度结果整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@测量角度结果"                
                    },
                    "out":
                    {
                        "result":"测量角度结果整型结果",
                        "integer":"测量角度结果整型"
                    }
                },
                {
                    "name":"判断测量角度结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"发送角度检测OK结果",
                        "condition":"@测量角度结果整型"
                    }
                },
                {
                    "name":"显示角度测量结果",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右角度检测超范围",
                        "level":"error"
                    }
                },
                {
                    "name":"角度测量失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送角度检测NG结果"
                    }
                },
                {
                    "name":"发送角度检测OK结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"182",
                        "type":"short",
                        "content":"1"
                    }
                },
                {
                    "name":"结束角度检测OK",
                    "type":"end"
                },
                {
                    "name":"发送角度检测NG结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"182",
                        "type":"short",
                        "content":"2"
                    }
                },
                {
                    "name":"结束角度检测NG",
                    "type":"end"
                },
                {
                    "name":"生成xml名称2",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"cb.xml\"]"
                    },
                    "out":
                    {
                        "result":"生成xml名称2结果",
                        "combine":"xml名称2"
                    }
                },
                {
                    "name":"保存棋盘格模板查找图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@棋盘格模板查找图片",
                        "path":"$右图片文件夹",
                        "name":"cb_temp.jpg"
                    },
                    "out":
                    {
                        "result":"保存棋盘格模板查找图片结果",
                        "full_name":"保存棋盘格模板查找图片路径"
                    }
                },
                {
                    "name":"棋盘格查找图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@棋盘格查找图片",
                        "path":"$右图片文件夹",
                        "name":"cb.jpg"
                    },
                    "out":
                    {
                        "result":"保存棋盘格查找图片结果",
                        "full_name":"保存棋盘格查找图片路径"
                    }
                }
            ] 
        }
    ]
}
