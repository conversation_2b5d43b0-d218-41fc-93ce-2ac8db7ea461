# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'StereoCamera.ui'
#
# Created by: PyQt5 UI code generator 5.15.6
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON>ore, QtGui, QtWidgets


class Ui_Stereo(object):
    def setupUi(self, Stereo):
        Stereo.setObjectName("Stereo")
        Stereo.resize(800, 830)
        Stereo.setMinimumSize(QtCore.QSize(0, 60))
        Stereo.setStyleSheet("")
        self.verticalLayout = QtWidgets.QVBoxLayout(Stereo)
        self.verticalLayout.setContentsMargins(10, 5, 0, 0)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.splitter = QtWidgets.QSplitter(Stereo)
        self.splitter.setStyleSheet("")
        self.splitter.setOrientation(QtCore.Qt.Horizontal)
        self.splitter.setHandleWidth(0)
        self.splitter.setObjectName("splitter")
        self.dockWidget = QtWidgets.QDockWidget(self.splitter)
        self.dockWidget.setStyleSheet("")
        self.dockWidget.setObjectName("dockWidget")
        self.dockWidgetContents_3 = QtWidgets.QWidget()
        self.dockWidgetContents_3.setStyleSheet("")
        self.dockWidgetContents_3.setObjectName("dockWidgetContents_3")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.dockWidgetContents_3)
        self.verticalLayout_6.setContentsMargins(5, 5, 5, 5)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout()
        self.verticalLayout_5.setContentsMargins(0, 0, -1, -1)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setContentsMargins(-1, 10, -1, -1)
        self.horizontalLayout.setObjectName("horizontalLayout")
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.pushButtonConnectCamera = QtWidgets.QPushButton(self.dockWidgetContents_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButtonConnectCamera.sizePolicy().hasHeightForWidth())
        self.pushButtonConnectCamera.setSizePolicy(sizePolicy)
        self.pushButtonConnectCamera.setMinimumSize(QtCore.QSize(150, 60))
        self.pushButtonConnectCamera.setStyleSheet("")
        self.pushButtonConnectCamera.setObjectName("pushButtonConnectCamera")
        self.horizontalLayout.addWidget(self.pushButtonConnectCamera)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.pushButtonDisconnectCamera = QtWidgets.QPushButton(self.dockWidgetContents_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButtonDisconnectCamera.sizePolicy().hasHeightForWidth())
        self.pushButtonDisconnectCamera.setSizePolicy(sizePolicy)
        self.pushButtonDisconnectCamera.setMinimumSize(QtCore.QSize(150, 60))
        self.pushButtonDisconnectCamera.setStyleSheet("")
        self.pushButtonDisconnectCamera.setObjectName("pushButtonDisconnectCamera")
        self.horizontalLayout.addWidget(self.pushButtonDisconnectCamera)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem2)
        self.verticalLayout_5.addLayout(self.horizontalLayout)
        self.groupBoxCameraMatrix = QtWidgets.QGroupBox(self.dockWidgetContents_3)
        self.groupBoxCameraMatrix.setStyleSheet("")
        self.groupBoxCameraMatrix.setTitle("")
        self.groupBoxCameraMatrix.setObjectName("groupBoxCameraMatrix")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.groupBoxCameraMatrix)
        self.verticalLayout_2.setContentsMargins(-1, 9, -1, -1)
        self.verticalLayout_2.setSpacing(6)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setContentsMargins(-1, 0, -1, -1)
        self.gridLayout.setVerticalSpacing(0)
        self.gridLayout.setObjectName("gridLayout")
        self.lineEdit2Start = QtWidgets.QLineEdit(self.groupBoxCameraMatrix)
        self.lineEdit2Start.setStyleSheet("")
        self.lineEdit2Start.setObjectName("lineEdit2Start")
        self.gridLayout.addWidget(self.lineEdit2Start, 2, 1, 1, 1)
        self.lineEdit1End = QtWidgets.QLineEdit(self.groupBoxCameraMatrix)
        self.lineEdit1End.setStyleSheet("")
        self.lineEdit1End.setObjectName("lineEdit1End")
        self.gridLayout.addWidget(self.lineEdit1End, 1, 1, 1, 1)
        self.lineEdit2End = QtWidgets.QLineEdit(self.groupBoxCameraMatrix)
        self.lineEdit2End.setStyleSheet("")
        self.lineEdit2End.setObjectName("lineEdit2End")
        self.gridLayout.addWidget(self.lineEdit2End, 3, 1, 1, 1)
        self.lineEdit3Start = QtWidgets.QLineEdit(self.groupBoxCameraMatrix)
        self.lineEdit3Start.setStyleSheet("")
        self.lineEdit3Start.setObjectName("lineEdit3Start")
        self.gridLayout.addWidget(self.lineEdit3Start, 4, 1, 1, 1)
        self.lineEdit3End = QtWidgets.QLineEdit(self.groupBoxCameraMatrix)
        self.lineEdit3End.setStyleSheet("")
        self.lineEdit3End.setObjectName("lineEdit3End")
        self.gridLayout.addWidget(self.lineEdit3End, 5, 1, 1, 1)
        self.lineEdit4Start = QtWidgets.QLineEdit(self.groupBoxCameraMatrix)
        self.lineEdit4Start.setStyleSheet("")
        self.lineEdit4Start.setObjectName("lineEdit4Start")
        self.gridLayout.addWidget(self.lineEdit4Start, 6, 1, 1, 1)
        self.lineEdit1Start = QtWidgets.QLineEdit(self.groupBoxCameraMatrix)
        self.lineEdit1Start.setStyleSheet("")
        self.lineEdit1Start.setObjectName("lineEdit1Start")
        self.gridLayout.addWidget(self.lineEdit1Start, 0, 1, 1, 1)
        self.lineEdit4End = QtWidgets.QLineEdit(self.groupBoxCameraMatrix)
        self.lineEdit4End.setStyleSheet("")
        self.lineEdit4End.setObjectName("lineEdit4End")
        self.gridLayout.addWidget(self.lineEdit4End, 7, 1, 1, 1)
        self.checkBox1start = QtWidgets.QCheckBox(self.groupBoxCameraMatrix)
        self.checkBox1start.setText("")
        self.checkBox1start.setObjectName("checkBox1start")
        self.gridLayout.addWidget(self.checkBox1start, 0, 2, 1, 1)
        self.checkBox1end = QtWidgets.QCheckBox(self.groupBoxCameraMatrix)
        self.checkBox1end.setText("")
        self.checkBox1end.setObjectName("checkBox1end")
        self.gridLayout.addWidget(self.checkBox1end, 1, 2, 1, 1)
        self.checkBox2start = QtWidgets.QCheckBox(self.groupBoxCameraMatrix)
        self.checkBox2start.setText("")
        self.checkBox2start.setObjectName("checkBox2start")
        self.gridLayout.addWidget(self.checkBox2start, 2, 2, 1, 1)
        self.checkBox2end = QtWidgets.QCheckBox(self.groupBoxCameraMatrix)
        self.checkBox2end.setText("")
        self.checkBox2end.setObjectName("checkBox2end")
        self.gridLayout.addWidget(self.checkBox2end, 3, 2, 1, 1)
        self.checkBox3start = QtWidgets.QCheckBox(self.groupBoxCameraMatrix)
        self.checkBox3start.setText("")
        self.checkBox3start.setObjectName("checkBox3start")
        self.gridLayout.addWidget(self.checkBox3start, 4, 2, 1, 1)
        self.checkBox3end = QtWidgets.QCheckBox(self.groupBoxCameraMatrix)
        self.checkBox3end.setText("")
        self.checkBox3end.setObjectName("checkBox3end")
        self.gridLayout.addWidget(self.checkBox3end, 5, 2, 1, 1)
        self.checkBox4start = QtWidgets.QCheckBox(self.groupBoxCameraMatrix)
        self.checkBox4start.setText("")
        self.checkBox4start.setObjectName("checkBox4start")
        self.gridLayout.addWidget(self.checkBox4start, 6, 2, 1, 1)
        self.checkBox4end = QtWidgets.QCheckBox(self.groupBoxCameraMatrix)
        self.checkBox4end.setText("")
        self.checkBox4end.setObjectName("checkBox4end")
        self.gridLayout.addWidget(self.checkBox4end, 7, 2, 1, 1)
        self.label = QtWidgets.QLabel(self.groupBoxCameraMatrix)
        self.label.setStyleSheet("")
        self.label.setObjectName("label")
        self.gridLayout.addWidget(self.label, 0, 0, 2, 1)
        self.label_2 = QtWidgets.QLabel(self.groupBoxCameraMatrix)
        self.label_2.setStyleSheet("")
        self.label_2.setObjectName("label_2")
        self.gridLayout.addWidget(self.label_2, 2, 0, 2, 1)
        self.label_3 = QtWidgets.QLabel(self.groupBoxCameraMatrix)
        self.label_3.setStyleSheet("")
        self.label_3.setObjectName("label_3")
        self.gridLayout.addWidget(self.label_3, 4, 0, 2, 1)
        self.label_4 = QtWidgets.QLabel(self.groupBoxCameraMatrix)
        self.label_4.setStyleSheet("")
        self.label_4.setObjectName("label_4")
        self.gridLayout.addWidget(self.label_4, 6, 0, 2, 1)
        self.verticalLayout_2.addLayout(self.gridLayout)
        self.verticalLayout_5.addWidget(self.groupBoxCameraMatrix)
        self.pushButtonTakePhotos = QtWidgets.QPushButton(self.dockWidgetContents_3)
        self.pushButtonTakePhotos.setMinimumSize(QtCore.QSize(0, 60))
        self.pushButtonTakePhotos.setStyleSheet("")
        self.pushButtonTakePhotos.setObjectName("pushButtonTakePhotos")
        self.verticalLayout_5.addWidget(self.pushButtonTakePhotos)
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.pushButtonCoordinateTransformation = QtWidgets.QPushButton(self.dockWidgetContents_3)
        self.pushButtonCoordinateTransformation.setMinimumSize(QtCore.QSize(0, 60))
        self.pushButtonCoordinateTransformation.setStyleSheet("")
        self.pushButtonCoordinateTransformation.setObjectName("pushButtonCoordinateTransformation")
        self.horizontalLayout_4.addWidget(self.pushButtonCoordinateTransformation)
        self.pushButtonCalc = QtWidgets.QPushButton(self.dockWidgetContents_3)
        self.pushButtonCalc.setMinimumSize(QtCore.QSize(0, 60))
        self.pushButtonCalc.setStyleSheet("")
        self.pushButtonCalc.setObjectName("pushButtonCalc")
        self.horizontalLayout_4.addWidget(self.pushButtonCalc)
        self.checkBoxYAW = QtWidgets.QCheckBox(self.dockWidgetContents_3)
        self.checkBoxYAW.setMinimumSize(QtCore.QSize(0, 60))
        self.checkBoxYAW.setStyleSheet("")
        self.checkBoxYAW.setChecked(True)
        self.checkBoxYAW.setObjectName("checkBoxYAW")
        self.horizontalLayout_4.addWidget(self.checkBoxYAW)
        self.pushButtonCalcAngle = QtWidgets.QPushButton(self.dockWidgetContents_3)
        self.pushButtonCalcAngle.setMinimumSize(QtCore.QSize(0, 60))
        self.pushButtonCalcAngle.setStyleSheet("")
        self.pushButtonCalcAngle.setObjectName("pushButtonCalcAngle")
        self.horizontalLayout_4.addWidget(self.pushButtonCalcAngle)
        self.verticalLayout_5.addLayout(self.horizontalLayout_4)
        self.labelResult = QtWidgets.QLabel(self.dockWidgetContents_3)
        self.labelResult.setStyleSheet("")
        self.labelResult.setText("")
        self.labelResult.setObjectName("labelResult")
        self.verticalLayout_5.addWidget(self.labelResult)
        self.groupBoxTest = QtWidgets.QGroupBox(self.dockWidgetContents_3)
        self.groupBoxTest.setStyleSheet("")
        self.groupBoxTest.setTitle("")
        self.groupBoxTest.setObjectName("groupBoxTest")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.groupBoxTest)
        self.verticalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_4.setSpacing(6)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout()
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.label_5 = QtWidgets.QLabel(self.groupBoxTest)
        self.label_5.setStyleSheet("")
        self.label_5.setObjectName("label_5")
        self.horizontalLayout_2.addWidget(self.label_5)
        self.spinBoxAngle = QtWidgets.QSpinBox(self.groupBoxTest)
        self.spinBoxAngle.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBoxAngle.setStyleSheet("")
        self.spinBoxAngle.setMinimum(-354)
        self.spinBoxAngle.setMaximum(354)
        self.spinBoxAngle.setProperty("value", 15)
        self.spinBoxAngle.setObjectName("spinBoxAngle")
        self.horizontalLayout_2.addWidget(self.spinBoxAngle)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem3)
        self.comboBoxRotateType = QtWidgets.QComboBox(self.groupBoxTest)
        self.comboBoxRotateType.setMinimumSize(QtCore.QSize(150, 45))
        font = QtGui.QFont()
        font.setPointSize(11)
        self.comboBoxRotateType.setFont(font)
        self.comboBoxRotateType.setStyleSheet("")
        self.comboBoxRotateType.setObjectName("comboBoxRotateType")
        self.comboBoxRotateType.addItem("")
        self.comboBoxRotateType.addItem("")
        self.horizontalLayout_2.addWidget(self.comboBoxRotateType)
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem4)
        self.pushButtonAdd = QtWidgets.QPushButton(self.groupBoxTest)
        self.pushButtonAdd.setMinimumSize(QtCore.QSize(75, 60))
        self.pushButtonAdd.setMaximumSize(QtCore.QSize(75, 16777215))
        self.pushButtonAdd.setStyleSheet("")
        self.pushButtonAdd.setObjectName("pushButtonAdd")
        self.horizontalLayout_2.addWidget(self.pushButtonAdd)
        self.pushButtonSub = QtWidgets.QPushButton(self.groupBoxTest)
        self.pushButtonSub.setMinimumSize(QtCore.QSize(75, 60))
        self.pushButtonSub.setMaximumSize(QtCore.QSize(75, 16777215))
        self.pushButtonSub.setStyleSheet("")
        self.pushButtonSub.setObjectName("pushButtonSub")
        self.horizontalLayout_2.addWidget(self.pushButtonSub)
        self.verticalLayout_3.addLayout(self.horizontalLayout_2)
        self.listWidgetTest = QtWidgets.QListWidget(self.groupBoxTest)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.listWidgetTest.sizePolicy().hasHeightForWidth())
        self.listWidgetTest.setSizePolicy(sizePolicy)
        self.listWidgetTest.setStyleSheet("")
        self.listWidgetTest.setAutoScrollMargin(6)
        self.listWidgetTest.setObjectName("listWidgetTest")
        self.verticalLayout_3.addWidget(self.listWidgetTest)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.pushButtonSaveTest = QtWidgets.QPushButton(self.groupBoxTest)
        self.pushButtonSaveTest.setMinimumSize(QtCore.QSize(0, 60))
        self.pushButtonSaveTest.setStyleSheet("")
        self.pushButtonSaveTest.setObjectName("pushButtonSaveTest")
        self.horizontalLayout_3.addWidget(self.pushButtonSaveTest)
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem5)
        self.pushButtonExportData = QtWidgets.QPushButton(self.groupBoxTest)
        self.pushButtonExportData.setMinimumSize(QtCore.QSize(0, 60))
        self.pushButtonExportData.setStyleSheet("")
        self.pushButtonExportData.setObjectName("pushButtonExportData")
        self.horizontalLayout_3.addWidget(self.pushButtonExportData)
        spacerItem6 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem6)
        self.pushButtonClearResult = QtWidgets.QPushButton(self.groupBoxTest)
        self.pushButtonClearResult.setMinimumSize(QtCore.QSize(0, 60))
        self.pushButtonClearResult.setStyleSheet("")
        self.pushButtonClearResult.setObjectName("pushButtonClearResult")
        self.horizontalLayout_3.addWidget(self.pushButtonClearResult)
        self.verticalLayout_3.addLayout(self.horizontalLayout_3)
        self.verticalLayout_4.addLayout(self.verticalLayout_3)
        self.verticalLayout_5.addWidget(self.groupBoxTest)
        self.verticalLayout_6.addLayout(self.verticalLayout_5)
        self.dockWidget.setWidget(self.dockWidgetContents_3)
        self.widgetDevices = QtWidgets.QWidget(self.splitter)
        self.widgetDevices.setObjectName("widgetDevices")
        self.verticalLayout.addWidget(self.splitter)

        self.retranslateUi(Stereo)
        QtCore.QMetaObject.connectSlotsByName(Stereo)

    def retranslateUi(self, Stereo):
        _translate = QtCore.QCoreApplication.translate
        Stereo.setWindowTitle(_translate("Stereo", "Form"))
        self.pushButtonConnectCamera.setText(_translate("Stereo", "连接相机"))
        self.pushButtonDisconnectCamera.setText(_translate("Stereo", "断开相机"))
        self.label.setText(_translate("Stereo", "双目1"))
        self.label_2.setText(_translate("Stereo", "双目2"))
        self.label_3.setText(_translate("Stereo", "双目3"))
        self.label_4.setText(_translate("Stereo", "双目4"))
        self.pushButtonTakePhotos.setText(_translate("Stereo", "拍照"))
        self.pushButtonCoordinateTransformation.setText(_translate("Stereo", "坐标系转换"))
        self.pushButtonCalc.setText(_translate("Stereo", "计算坐标"))
        self.checkBoxYAW.setText(_translate("Stereo", "测量YAW角度"))
        self.pushButtonCalcAngle.setText(_translate("Stereo", "计算角度"))
        self.label_5.setText(_translate("Stereo", "角度"))
        self.comboBoxRotateType.setItemText(0, _translate("Stereo", "yaw"))
        self.comboBoxRotateType.setItemText(1, _translate("Stereo", "pitch"))
        self.pushButtonAdd.setText(_translate("Stereo", "+"))
        self.pushButtonSub.setText(_translate("Stereo", "-"))
        self.pushButtonSaveTest.setText(_translate("Stereo", "保存用例"))
        self.pushButtonExportData.setText(_translate("Stereo", "导出数据"))
        self.pushButtonClearResult.setText(_translate("Stereo", "  清除  "))
