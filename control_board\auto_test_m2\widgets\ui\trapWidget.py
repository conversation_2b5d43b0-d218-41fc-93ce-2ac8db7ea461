# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'trapWidget.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(1317, 488)
        self.verticalLayout = QtWidgets.QVBoxLayout(Form)
        self.verticalLayout.setObjectName("verticalLayout")
        self.groupBox_4 = QtWidgets.QGroupBox(Form)
        self.groupBox_4.setTitle("")
        self.groupBox_4.setObjectName("groupBox_4")
        self.gridLayout_4 = QtWidgets.QGridLayout(self.groupBox_4)
        self.gridLayout_4.setObjectName("gridLayout_4")
        self.label_57 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_57.sizePolicy().hasHeightForWidth())
        self.label_57.setSizePolicy(sizePolicy)
        self.label_57.setObjectName("label_57")
        self.gridLayout_4.addWidget(self.label_57, 0, 0, 1, 1)
        self.label_63 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_63.sizePolicy().hasHeightForWidth())
        self.label_63.setSizePolicy(sizePolicy)
        self.label_63.setObjectName("label_63")
        self.gridLayout_4.addWidget(self.label_63, 0, 1, 1, 1)
        self.label_68 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_68.sizePolicy().hasHeightForWidth())
        self.label_68.setSizePolicy(sizePolicy)
        self.label_68.setObjectName("label_68")
        self.gridLayout_4.addWidget(self.label_68, 0, 2, 1, 1)
        self.label_69 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_69.sizePolicy().hasHeightForWidth())
        self.label_69.setSizePolicy(sizePolicy)
        self.label_69.setObjectName("label_69")
        self.gridLayout_4.addWidget(self.label_69, 0, 3, 1, 1)
        self.label_70 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_70.sizePolicy().hasHeightForWidth())
        self.label_70.setSizePolicy(sizePolicy)
        self.label_70.setObjectName("label_70")
        self.gridLayout_4.addWidget(self.label_70, 0, 4, 1, 1)
        self.label_71 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_71.sizePolicy().hasHeightForWidth())
        self.label_71.setSizePolicy(sizePolicy)
        self.label_71.setObjectName("label_71")
        self.gridLayout_4.addWidget(self.label_71, 0, 5, 1, 1)
        self.label_72 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_72.sizePolicy().hasHeightForWidth())
        self.label_72.setSizePolicy(sizePolicy)
        self.label_72.setObjectName("label_72")
        self.gridLayout_4.addWidget(self.label_72, 0, 6, 1, 1)
        self.label_73 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_73.sizePolicy().hasHeightForWidth())
        self.label_73.setSizePolicy(sizePolicy)
        self.label_73.setObjectName("label_73")
        self.gridLayout_4.addWidget(self.label_73, 0, 7, 1, 1)
        self.label_74 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_74.sizePolicy().hasHeightForWidth())
        self.label_74.setSizePolicy(sizePolicy)
        self.label_74.setObjectName("label_74")
        self.gridLayout_4.addWidget(self.label_74, 0, 8, 1, 1)
        self.label_75 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_75.sizePolicy().hasHeightForWidth())
        self.label_75.setSizePolicy(sizePolicy)
        self.label_75.setObjectName("label_75")
        self.gridLayout_4.addWidget(self.label_75, 0, 9, 1, 1)
        self.label_76 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_76.sizePolicy().hasHeightForWidth())
        self.label_76.setSizePolicy(sizePolicy)
        self.label_76.setObjectName("label_76")
        self.gridLayout_4.addWidget(self.label_76, 0, 10, 1, 1)
        self.label_77 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_77.sizePolicy().hasHeightForWidth())
        self.label_77.setSizePolicy(sizePolicy)
        self.label_77.setObjectName("label_77")
        self.gridLayout_4.addWidget(self.label_77, 0, 11, 1, 1)
        self.label_61 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_61.sizePolicy().hasHeightForWidth())
        self.label_61.setSizePolicy(sizePolicy)
        self.label_61.setObjectName("label_61")
        self.gridLayout_4.addWidget(self.label_61, 1, 0, 1, 1)
        self.label_65 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_65.sizePolicy().hasHeightForWidth())
        self.label_65.setSizePolicy(sizePolicy)
        self.label_65.setObjectName("label_65")
        self.gridLayout_4.addWidget(self.label_65, 1, 1, 1, 1)
        self.comboBox_trap_mode_1 = QtWidgets.QComboBox(self.groupBox_4)
        self.comboBox_trap_mode_1.setObjectName("comboBox_trap_mode_1")
        self.comboBox_trap_mode_1.addItem("")
        self.comboBox_trap_mode_1.addItem("")
        self.gridLayout_4.addWidget(self.comboBox_trap_mode_1, 1, 2, 1, 1)
        self.doubleSpinBox_trap_vel_1 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_1.setDecimals(3)
        self.doubleSpinBox_trap_vel_1.setProperty("value", 50.0)
        self.doubleSpinBox_trap_vel_1.setObjectName("doubleSpinBox_trap_vel_1")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_1, 1, 4, 1, 1)
        self.doubleSpinBox_trap_acc_1 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_acc_1.setDecimals(3)
        self.doubleSpinBox_trap_acc_1.setProperty("value", 0.05)
        self.doubleSpinBox_trap_acc_1.setObjectName("doubleSpinBox_trap_acc_1")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_acc_1, 1, 5, 1, 1)
        self.doubleSpinBox_trap_dec_1 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_dec_1.setDecimals(3)
        self.doubleSpinBox_trap_dec_1.setProperty("value", 0.05)
        self.doubleSpinBox_trap_dec_1.setObjectName("doubleSpinBox_trap_dec_1")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_dec_1, 1, 6, 1, 1)
        self.doubleSpinBox_trap_vel_start_1 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_start_1.setDecimals(3)
        self.doubleSpinBox_trap_vel_start_1.setObjectName("doubleSpinBox_trap_vel_start_1")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_start_1, 1, 7, 1, 1)
        self.spinBox_trap_smooth_time_1 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_smooth_time_1.setMaximum(49)
        self.spinBox_trap_smooth_time_1.setProperty("value", 30)
        self.spinBox_trap_smooth_time_1.setObjectName("spinBox_trap_smooth_time_1")
        self.gridLayout_4.addWidget(self.spinBox_trap_smooth_time_1, 1, 8, 1, 1)
        self.spinBox = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox.setObjectName("spinBox")
        self.gridLayout_4.addWidget(self.spinBox, 1, 9, 1, 1)
        self.spinBox_2 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_2.setObjectName("spinBox_2")
        self.gridLayout_4.addWidget(self.spinBox_2, 1, 10, 1, 1)
        self.pushButton_trap_start_1 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_trap_start_1.setObjectName("pushButton_trap_start_1")
        self.gridLayout_4.addWidget(self.pushButton_trap_start_1, 1, 11, 1, 1)
        self.label_67 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_67.sizePolicy().hasHeightForWidth())
        self.label_67.setSizePolicy(sizePolicy)
        self.label_67.setObjectName("label_67")
        self.gridLayout_4.addWidget(self.label_67, 2, 0, 1, 1)
        self.label_58 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_58.sizePolicy().hasHeightForWidth())
        self.label_58.setSizePolicy(sizePolicy)
        self.label_58.setObjectName("label_58")
        self.gridLayout_4.addWidget(self.label_58, 2, 1, 1, 1)
        self.comboBox_trap_mode_2 = QtWidgets.QComboBox(self.groupBox_4)
        self.comboBox_trap_mode_2.setObjectName("comboBox_trap_mode_2")
        self.comboBox_trap_mode_2.addItem("")
        self.comboBox_trap_mode_2.addItem("")
        self.gridLayout_4.addWidget(self.comboBox_trap_mode_2, 2, 2, 1, 1)
        self.doubleSpinBox_trap_vel_2 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_2.setDecimals(3)
        self.doubleSpinBox_trap_vel_2.setProperty("value", 50.0)
        self.doubleSpinBox_trap_vel_2.setObjectName("doubleSpinBox_trap_vel_2")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_2, 2, 4, 1, 1)
        self.doubleSpinBox_trap_acc_2 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_acc_2.setDecimals(3)
        self.doubleSpinBox_trap_acc_2.setProperty("value", 0.05)
        self.doubleSpinBox_trap_acc_2.setObjectName("doubleSpinBox_trap_acc_2")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_acc_2, 2, 5, 1, 1)
        self.doubleSpinBox_trap_dec_2 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_dec_2.setDecimals(3)
        self.doubleSpinBox_trap_dec_2.setProperty("value", 0.05)
        self.doubleSpinBox_trap_dec_2.setObjectName("doubleSpinBox_trap_dec_2")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_dec_2, 2, 6, 1, 1)
        self.doubleSpinBox_trap_vel_start_2 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_start_2.setDecimals(3)
        self.doubleSpinBox_trap_vel_start_2.setObjectName("doubleSpinBox_trap_vel_start_2")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_start_2, 2, 7, 1, 1)
        self.spinBox_trap_smooth_time_2 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_smooth_time_2.setMaximum(49)
        self.spinBox_trap_smooth_time_2.setProperty("value", 30)
        self.spinBox_trap_smooth_time_2.setObjectName("spinBox_trap_smooth_time_2")
        self.gridLayout_4.addWidget(self.spinBox_trap_smooth_time_2, 2, 8, 1, 1)
        self.spinBox_6 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_6.setObjectName("spinBox_6")
        self.gridLayout_4.addWidget(self.spinBox_6, 2, 9, 1, 1)
        self.spinBox_5 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_5.setObjectName("spinBox_5")
        self.gridLayout_4.addWidget(self.spinBox_5, 2, 10, 1, 1)
        self.pushButton_trap_start_2 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_trap_start_2.setObjectName("pushButton_trap_start_2")
        self.gridLayout_4.addWidget(self.pushButton_trap_start_2, 2, 11, 1, 1)
        self.label_66 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_66.sizePolicy().hasHeightForWidth())
        self.label_66.setSizePolicy(sizePolicy)
        self.label_66.setObjectName("label_66")
        self.gridLayout_4.addWidget(self.label_66, 3, 0, 1, 1)
        self.label_56 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_56.sizePolicy().hasHeightForWidth())
        self.label_56.setSizePolicy(sizePolicy)
        self.label_56.setObjectName("label_56")
        self.gridLayout_4.addWidget(self.label_56, 3, 1, 1, 1)
        self.comboBox_trap_mode_3 = QtWidgets.QComboBox(self.groupBox_4)
        self.comboBox_trap_mode_3.setObjectName("comboBox_trap_mode_3")
        self.comboBox_trap_mode_3.addItem("")
        self.comboBox_trap_mode_3.addItem("")
        self.gridLayout_4.addWidget(self.comboBox_trap_mode_3, 3, 2, 1, 1)
        self.doubleSpinBox_trap_vel_3 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_3.setDecimals(3)
        self.doubleSpinBox_trap_vel_3.setProperty("value", 50.0)
        self.doubleSpinBox_trap_vel_3.setObjectName("doubleSpinBox_trap_vel_3")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_3, 3, 4, 1, 1)
        self.doubleSpinBox_trap_acc_3 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_acc_3.setDecimals(3)
        self.doubleSpinBox_trap_acc_3.setProperty("value", 0.05)
        self.doubleSpinBox_trap_acc_3.setObjectName("doubleSpinBox_trap_acc_3")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_acc_3, 3, 5, 1, 1)
        self.doubleSpinBox_trap_dec_3 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_dec_3.setDecimals(3)
        self.doubleSpinBox_trap_dec_3.setProperty("value", 0.05)
        self.doubleSpinBox_trap_dec_3.setObjectName("doubleSpinBox_trap_dec_3")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_dec_3, 3, 6, 1, 1)
        self.doubleSpinBox_trap_vel_start_3 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_start_3.setDecimals(3)
        self.doubleSpinBox_trap_vel_start_3.setObjectName("doubleSpinBox_trap_vel_start_3")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_start_3, 3, 7, 1, 1)
        self.spinBox_trap_smooth_time_3 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_smooth_time_3.setMaximum(49)
        self.spinBox_trap_smooth_time_3.setProperty("value", 30)
        self.spinBox_trap_smooth_time_3.setObjectName("spinBox_trap_smooth_time_3")
        self.gridLayout_4.addWidget(self.spinBox_trap_smooth_time_3, 3, 8, 1, 1)
        self.spinBox_9 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_9.setObjectName("spinBox_9")
        self.gridLayout_4.addWidget(self.spinBox_9, 3, 9, 1, 1)
        self.spinBox_8 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_8.setObjectName("spinBox_8")
        self.gridLayout_4.addWidget(self.spinBox_8, 3, 10, 1, 1)
        self.pushButton_trap_start_3 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_trap_start_3.setObjectName("pushButton_trap_start_3")
        self.gridLayout_4.addWidget(self.pushButton_trap_start_3, 3, 11, 1, 1)
        self.label_59 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_59.sizePolicy().hasHeightForWidth())
        self.label_59.setSizePolicy(sizePolicy)
        self.label_59.setObjectName("label_59")
        self.gridLayout_4.addWidget(self.label_59, 4, 0, 1, 1)
        self.label_62 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_62.sizePolicy().hasHeightForWidth())
        self.label_62.setSizePolicy(sizePolicy)
        self.label_62.setObjectName("label_62")
        self.gridLayout_4.addWidget(self.label_62, 4, 1, 1, 1)
        self.comboBox_trap_mode_4 = QtWidgets.QComboBox(self.groupBox_4)
        self.comboBox_trap_mode_4.setObjectName("comboBox_trap_mode_4")
        self.comboBox_trap_mode_4.addItem("")
        self.comboBox_trap_mode_4.addItem("")
        self.gridLayout_4.addWidget(self.comboBox_trap_mode_4, 4, 2, 1, 1)
        self.doubleSpinBox_trap_vel_4 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_4.setDecimals(3)
        self.doubleSpinBox_trap_vel_4.setProperty("value", 50.0)
        self.doubleSpinBox_trap_vel_4.setObjectName("doubleSpinBox_trap_vel_4")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_4, 4, 4, 1, 1)
        self.doubleSpinBox_trap_acc_4 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_acc_4.setDecimals(3)
        self.doubleSpinBox_trap_acc_4.setProperty("value", 0.05)
        self.doubleSpinBox_trap_acc_4.setObjectName("doubleSpinBox_trap_acc_4")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_acc_4, 4, 5, 1, 1)
        self.doubleSpinBox_trap_dec_4 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_dec_4.setDecimals(3)
        self.doubleSpinBox_trap_dec_4.setProperty("value", 0.05)
        self.doubleSpinBox_trap_dec_4.setObjectName("doubleSpinBox_trap_dec_4")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_dec_4, 4, 6, 1, 1)
        self.doubleSpinBox_trap_vel_start_4 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_start_4.setDecimals(3)
        self.doubleSpinBox_trap_vel_start_4.setObjectName("doubleSpinBox_trap_vel_start_4")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_start_4, 4, 7, 1, 1)
        self.spinBox_trap_smooth_time_4 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_smooth_time_4.setMaximum(49)
        self.spinBox_trap_smooth_time_4.setProperty("value", 30)
        self.spinBox_trap_smooth_time_4.setObjectName("spinBox_trap_smooth_time_4")
        self.gridLayout_4.addWidget(self.spinBox_trap_smooth_time_4, 4, 8, 1, 1)
        self.spinBox_12 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_12.setObjectName("spinBox_12")
        self.gridLayout_4.addWidget(self.spinBox_12, 4, 9, 1, 1)
        self.spinBox_11 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_11.setObjectName("spinBox_11")
        self.gridLayout_4.addWidget(self.spinBox_11, 4, 10, 1, 1)
        self.pushButton_trap_start_4 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_trap_start_4.setObjectName("pushButton_trap_start_4")
        self.gridLayout_4.addWidget(self.pushButton_trap_start_4, 4, 11, 1, 1)
        self.label_60 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_60.sizePolicy().hasHeightForWidth())
        self.label_60.setSizePolicy(sizePolicy)
        self.label_60.setObjectName("label_60")
        self.gridLayout_4.addWidget(self.label_60, 5, 0, 1, 1)
        self.label_64 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_64.sizePolicy().hasHeightForWidth())
        self.label_64.setSizePolicy(sizePolicy)
        self.label_64.setObjectName("label_64")
        self.gridLayout_4.addWidget(self.label_64, 5, 1, 1, 1)
        self.comboBox_trap_mode_5 = QtWidgets.QComboBox(self.groupBox_4)
        self.comboBox_trap_mode_5.setObjectName("comboBox_trap_mode_5")
        self.comboBox_trap_mode_5.addItem("")
        self.comboBox_trap_mode_5.addItem("")
        self.gridLayout_4.addWidget(self.comboBox_trap_mode_5, 5, 2, 1, 1)
        self.doubleSpinBox_trap_vel_5 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_5.setDecimals(3)
        self.doubleSpinBox_trap_vel_5.setMaximum(9.99)
        self.doubleSpinBox_trap_vel_5.setProperty("value", 2.0)
        self.doubleSpinBox_trap_vel_5.setObjectName("doubleSpinBox_trap_vel_5")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_5, 5, 4, 1, 1)
        self.doubleSpinBox_trap_acc_5 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_acc_5.setDecimals(3)
        self.doubleSpinBox_trap_acc_5.setProperty("value", 0.05)
        self.doubleSpinBox_trap_acc_5.setObjectName("doubleSpinBox_trap_acc_5")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_acc_5, 5, 5, 1, 1)
        self.doubleSpinBox_trap_dec_5 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_dec_5.setDecimals(3)
        self.doubleSpinBox_trap_dec_5.setProperty("value", 0.05)
        self.doubleSpinBox_trap_dec_5.setObjectName("doubleSpinBox_trap_dec_5")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_dec_5, 5, 6, 1, 1)
        self.doubleSpinBox_trap_vel_start_5 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_start_5.setDecimals(3)
        self.doubleSpinBox_trap_vel_start_5.setObjectName("doubleSpinBox_trap_vel_start_5")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_start_5, 5, 7, 1, 1)
        self.spinBox_trap_smooth_time_5 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_smooth_time_5.setMaximum(49)
        self.spinBox_trap_smooth_time_5.setProperty("value", 30)
        self.spinBox_trap_smooth_time_5.setObjectName("spinBox_trap_smooth_time_5")
        self.gridLayout_4.addWidget(self.spinBox_trap_smooth_time_5, 5, 8, 1, 1)
        self.spinBox_15 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_15.setObjectName("spinBox_15")
        self.gridLayout_4.addWidget(self.spinBox_15, 5, 9, 1, 1)
        self.spinBox_14 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_14.setObjectName("spinBox_14")
        self.gridLayout_4.addWidget(self.spinBox_14, 5, 10, 1, 1)
        self.pushButton_trap_start_5 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_trap_start_5.setObjectName("pushButton_trap_start_5")
        self.gridLayout_4.addWidget(self.pushButton_trap_start_5, 5, 11, 1, 1)
        self.spinBox_trap_step_1 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_step_1.setMinimum(-999999999)
        self.spinBox_trap_step_1.setMaximum(999999999)
        self.spinBox_trap_step_1.setObjectName("spinBox_trap_step_1")
        self.gridLayout_4.addWidget(self.spinBox_trap_step_1, 1, 3, 1, 1)
        self.spinBox_trap_step_2 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_step_2.setMinimum(-999999999)
        self.spinBox_trap_step_2.setMaximum(999999999)
        self.spinBox_trap_step_2.setObjectName("spinBox_trap_step_2")
        self.gridLayout_4.addWidget(self.spinBox_trap_step_2, 2, 3, 1, 1)
        self.spinBox_trap_step_3 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_step_3.setMinimum(-999999999)
        self.spinBox_trap_step_3.setMaximum(999999999)
        self.spinBox_trap_step_3.setObjectName("spinBox_trap_step_3")
        self.gridLayout_4.addWidget(self.spinBox_trap_step_3, 3, 3, 1, 1)
        self.spinBox_trap_step_4 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_step_4.setMinimum(-999999999)
        self.spinBox_trap_step_4.setMaximum(999999999)
        self.spinBox_trap_step_4.setProperty("value", 0)
        self.spinBox_trap_step_4.setObjectName("spinBox_trap_step_4")
        self.gridLayout_4.addWidget(self.spinBox_trap_step_4, 4, 3, 1, 1)
        self.spinBox_trap_step_5 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_step_5.setMinimum(-999999999)
        self.spinBox_trap_step_5.setMaximum(999999999)
        self.spinBox_trap_step_5.setObjectName("spinBox_trap_step_5")
        self.gridLayout_4.addWidget(self.spinBox_trap_step_5, 5, 3, 1, 1)
        self.verticalLayout.addWidget(self.groupBox_4)
        spacerItem = QtWidgets.QSpacerItem(20, 242, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.label_57.setText(_translate("Form", "核"))
        self.label_63.setText(_translate("Form", "轴"))
        self.label_68.setText(_translate("Form", "模式"))
        self.label_69.setText(_translate("Form", "步长（pulse）"))
        self.label_70.setText(_translate("Form", "速度（pulse/ms）"))
        self.label_71.setText(_translate("Form", "加速度（pulse/ms^2）"))
        self.label_72.setText(_translate("Form", "减速度（pulse/ms^2）"))
        self.label_73.setText(_translate("Form", "起跳速度（pulse/ms）"))
        self.label_74.setText(_translate("Form", "平滑时间（ms）"))
        self.label_75.setText(_translate("Form", "循环次数"))
        self.label_76.setText(_translate("Form", "循环间隔（ms）"))
        self.label_77.setText(_translate("Form", "操作"))
        self.label_61.setText(_translate("Form", "1"))
        self.label_65.setText(_translate("Form", "1"))
        self.comboBox_trap_mode_1.setItemText(0, _translate("Form", "绝对运动"))
        self.comboBox_trap_mode_1.setItemText(1, _translate("Form", "相对运动"))
        self.pushButton_trap_start_1.setText(_translate("Form", "启动"))
        self.label_67.setText(_translate("Form", "1"))
        self.label_58.setText(_translate("Form", "2"))
        self.comboBox_trap_mode_2.setItemText(0, _translate("Form", "绝对运动"))
        self.comboBox_trap_mode_2.setItemText(1, _translate("Form", "相对运动"))
        self.pushButton_trap_start_2.setText(_translate("Form", "启动"))
        self.label_66.setText(_translate("Form", "1"))
        self.label_56.setText(_translate("Form", "3"))
        self.comboBox_trap_mode_3.setItemText(0, _translate("Form", "绝对运动"))
        self.comboBox_trap_mode_3.setItemText(1, _translate("Form", "相对运动"))
        self.pushButton_trap_start_3.setText(_translate("Form", "启动"))
        self.label_59.setText(_translate("Form", "1"))
        self.label_62.setText(_translate("Form", "4"))
        self.comboBox_trap_mode_4.setItemText(0, _translate("Form", "绝对运动"))
        self.comboBox_trap_mode_4.setItemText(1, _translate("Form", "相对运动"))
        self.pushButton_trap_start_4.setText(_translate("Form", "启动"))
        self.label_60.setText(_translate("Form", "1"))
        self.label_64.setText(_translate("Form", "5"))
        self.comboBox_trap_mode_5.setItemText(0, _translate("Form", "绝对运动"))
        self.comboBox_trap_mode_5.setItemText(1, _translate("Form", "相对运动"))
        self.pushButton_trap_start_5.setText(_translate("Form", "启动"))
