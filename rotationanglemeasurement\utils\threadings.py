from itertools import combinations

import copy

import os

import time

from PyQt5.QtCore import QThread
import cv2
import numpy as np
from rotationanglemeasurement.utils.manager import managerQ

CAMERA_RESOLUTION={
    "1280p":{"WIDTH":2272,"HEIGHT":1280},
    "1080p":{"WIDTH":1920,"HEIGHT":1080},
    "720p":{"WIDTH":1280,"HEIGHT":720},
    # "720p":{"WIDTH":1024,"HEIGHT":768},
    "480p":{"WIDTH":640,"HEIGHT":480},
}

class CameraThread(QThread):
    def __init__(self, camera_num, imgQ):
        super().__init__()
        self.camera_num = int(camera_num)
        self.run_flag = True
        self.imgQ = imgQ
        self.take_photo = False  # 新增标志 拍照
        self.center = None
        self.radius = 0
    def run(self):
        capture = cv2.VideoCapture(self.camera_num)
        # capture.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('m', 'j', 'p', 'g'))
        # capture.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M', 'J', 'P', 'G'))
        capture.set(cv2.CAP_PROP_FRAME_WIDTH, CAMERA_RESOLUTION["1080p"]["WIDTH"]*2)  # 设置分辨率
        capture.set(cv2.CAP_PROP_FRAME_HEIGHT, CAMERA_RESOLUTION["1080p"]["HEIGHT"]*2)  # 设置分辨率
        # print(capture.get(cv2.CAP_PROP_EXPOSURE),"capture")
        # new_exposure = -1
        # capture.set(cv2.CAP_PROP_EXPOSURE, new_exposure)
        # print(capture.get(cv2.CAP_PROP_EXPOSURE), "new capture")

        prev_frame_gray = None

        while self.run_flag:
            ret, frame = capture.read()
            if ret:
                # rgb_frame = frame

                # kernel = np.ones(shape=[1, 1], dtype=np.uint8)
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                # # 开运算，先腐蚀，后膨胀
                # frame = cv2.morphologyEx(frame, op=cv2.MORPH_OPEN,
                #                          kernel=kernel, iterations=1)
                # 第二个参数是孔径的线性尺寸，它的值必须是大于1的奇数。
                rgb_frame = cv2.medianBlur(frame, 3)
                self.imgQ.put(rgb_frame)
                # IsThreePoints = self.detect_red_points(rgb_frame)
         

                if self.take_photo:
                    self.take_photo = False  # 新增标志
                    managerQ.photo_Q.put([self.camera_num,rgb_frame])
                    # managerQ.calibration_signal.emit(self.camera_num,rgb_frame)
                time.sleep(0.01)
                # prev_frame_gray = curr_frame_gray
        capture.release()
    def find_brightness(self, edge_coords, gray):
        ret_list = []
        radius = 10  # 半径设置为 10
        for [y, x] in edge_coords:
            max_intensity = 0
            # 首先确定区域内的最亮点
            for dx in range(-radius, radius + 1):
                for dy in range(-radius, radius + 1):
                    if dx * dx + dy * dy <= radius * radius:  # 确保点在圆内
                        nx, ny = x + dx, y + dy
                        if 0 <= nx < gray.shape[1] and 0 <= ny < gray.shape[0]:
                            intensity = gray[ny, nx]
                            if intensity > max_intensity:
                                max_intensity = intensity

            # 确定亮度阈值
            threshold = 0.7 * max_intensity  # 举例定为最高亮度的 80%

            # 寻找区域内高于阈值的所有点
            coords_x, coords_y = [], []
            for dx in range(-radius, radius + 1):
                for dy in range(-radius, radius + 1):
                    if dx * dx + dy * dy <= radius * radius:
                        nx, ny = x + dx, y + dy
                        if 0 <= nx < gray.shape[1] and 0 <= ny < gray.shape[0] and gray[ny, nx] >= threshold:
                            coords_x.append(nx)
                            coords_y.append(ny)

            # 如果有符合的点，则计算它们的平均位置作为亮斑中心
            if coords_x and coords_y:
                spot_center = [int(np.mean(coords_x)), int(np.mean(coords_y))]
                ret_list.append(spot_center)
        return ret_list

    def min_enclosing_circle(self,points):
        """
        返回一个圆，该圆包含给定的所有点，并且具有最小的可能半径。
        请注意，这不是最优解，但对于小规模点集足够有效。
        """
        center, radius = cv2.minEnclosingCircle(points)
        return center, radius
    def detect_red_points(self, gray):
        # gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        image_gray_origin = copy.deepcopy(gray)

        # 使用 Canny 算法检测边缘
        edges = cv2.Canny(gray, 100, 200)
        # 查找边界
        edge_coordinates = np.argwhere(edges != 0)

        ret_list = []
        if len(edge_coordinates) <3:
            return False
        # 遍历所有边缘点

        for point in edge_coordinates[-300:]:
            y, x = point

            # 只有当点与ret_list中所有点的距离都大于20时，才加入该点
            is_distinct = True
            for py, px in ret_list:
                if np.linalg.norm(np.array([y, x]) - np.array([py, px])) < 50:
                    is_distinct = False
                    break  # 如果发现一个小于20的距离，中断内层循环

            if is_distinct and x > managerQ.min_x and y > managerQ.min_y and x < managerQ.max_x and y < managerQ.max_y:
                ret_list.append([y, x])  # 加入点到结果列表
        ret_list = self.find_brightness(ret_list, image_gray_origin)

        if len(ret_list) > 5:  # 6个点
            # 对滤波后的角点进行组合，选择五个点
            combos = list(combinations(ret_list, 5))
            # 初始化最小半径为正无穷
            min_radius = float('inf')
            best_combo = None
            # 对所有可能的组合进行迭代
            for combo in combos:
                # 计算当前组合的外接圆
                _, radius = self.min_enclosing_circle(np.array(combo))
                # 更新最小半径和最好的组合
                if radius < min_radius:
                    min_radius = radius
                    best_combo = combo
            ret_list = best_combo
        if len(ret_list) == 4 or len(ret_list) == 5:
            #
            # print("len(edge_coordinates)", len(edge_coordinates))
            return True

        return False
class SavePhotoThread(QThread):
    def __init__(self):
        super().__init__()
        self.run_flag = True

    def run(self):
        # print("SavePhotoThread run")
        while self.run_flag:
            if managerQ.photo_Q.empty():
                time.sleep(0.2)
                continue
            img_data=managerQ.photo_Q.get(block=False)
            cameraId ,img = img_data[0],img_data[1]

            frame_left = img[:, :1920]
            frame_right = img[:, 1920:]
            t = int(time.time() * 1000)
            path_left = os.path.join(os.getcwd(), "images", "sample", "left_{}.jpg".format(t))
            path_right = os.path.join(os.getcwd(), "images", "sample", "right_{}.jpg".format(t))
            cv2.imwrite(path_left, frame_left)
            cv2.imwrite(path_right, frame_right)
            # if str(cameraId)=="0":
            #     cv2.imwrite("imgTest.jpg", images)

            # t = str(int(time.time()))
            # path = os.path.join(os.getcwd(),"images","sample",f"{t}_{cameraId}.jpg")
            #
            #
            # bgr_img = cv2.cvtColor(images, cv2.COLOR_BGR2RGB)
            #
            # cv2.imwrite(path, bgr_img)
            # print("path_right:",path_right)
            managerQ.photo_path_signal.emit(str(cameraId),path_left,path_right)
