import struct
import subprocess
import time

# -*- coding: utf-8 -*-
cmd_dict = {
    "read_pn":'adb shell "i2ctransfer -f -y 4 w1@0x2b 0x64 && i2ctransfer -f -y 4 w1@0x2b 0x64 r28"',
    "display_mode":"adb shell i2ctransfer -f -y 4 w1@0x2b 0x90 r5",
    "switch_boot":"adb shell i2ctransfer -f -y 4 w5@0x2b 0x80 0x00 0x02 0x02 0x80",
    "checksum":"adb shell i2ctransfer -f -y 4 w5@0x2b 0x84 0x05 ",

}


def calculate_checksum(data):
    checksum = 0
    for byte in data:
        if byte == '':
            continue
        byte = int(byte, 16)
        checksum ^= byte
    return f"{checksum:#04x}"


def execute_cmd(cmd):
    procId = subprocess.Popen(cmd, stdin=subprocess.PIPE,
                              shell=False,
                              stdout=subprocess.PIPE, encoding="utf8")
    # 获取执行的结果
    out = procId.communicate()[0]
    print("out ", out)
    return out


def hexstr_to_int(hexstr, big_endian=True):
    # 分割字符串以获取每个十六进制数，并去除可能的空格
    hex_numbers = hexstr.replace("0x", "").replace(" ", "").split()

    # 检查是否需要按大端字节序排列
    if big_endian:
        hex_numbers.reverse()

    # 将每个十六进制数转换为对应的字节，然后组合起来
    combined_hex = ''.join(hex_numbers)

    # 将十六进制字符串转换为十进制整数
    return int(combined_hex, 16)


def normal_flash(filename):
    with open(filename, 'rb') as f:
        # ①4位长度的checksum，②将读取的每个byte转换为0xXX形式并加入列表，③将列表转换为字符串
        checksum_bytes = f.read(4)
        checksum_hex_list = [f"0x{byte:02X}" for byte in checksum_bytes]
        checksum_str = ' '.join(checksum_hex_list)
        print("checksum_str", checksum_str)

        # 4位 读取并解析BIN文件头长度
        header_length_count_bytes = f.read(4)
        block_count_b1, block_count_b2, block_count_b3, block_count_b4 = struct.unpack('>BBBB',header_length_count_bytes)  # 将Block数量分解为四个字节
        header_length = f"0x{block_count_b1:02X} 0x{block_count_b2:02X} 0x{block_count_b3:02X} 0x{block_count_b4:02X}"

        # 读取并解析零件号
        part_number = f.read(20).rstrip(b'\x00')
        part_number = part_number.hex()
        part_number = part_number.upper()
        PN = execute_cmd(cmd_dict["read_pn"])[35:85].replace("0x", "").replace(" ","")
        print("read_PN",PN)
        print("hex_part_number", part_number)
        if PN != part_number:
            print("PN号不一致！")
            return

        # 读取并解析版本号
        version_bytes = f.read(2)  # 直接读取2字节的版本号
        version_high, version_low = struct.unpack('>BB', version_bytes)  # 将版本号分解为两个字节
        version = f"0x{version_high:02X} 0x{version_low:02X}"
        print(f"Version: {version}")

        # 80服务 设置boot模式
        for i in range(10):
            # 获取display当前模式
            display_mode = execute_cmd(cmd_dict["display_mode"])
            display_mode_state = display_mode[15:20]
            if int(display_mode_state, 16) == 4:
                # 为app模式→ 切换至boot模式
                switch_boot_state =  execute_cmd(cmd_dict["switch_boot"])
                print(f"switch_boot_state: {switch_boot_state}")
                time.sleep(1)
                continue
            elif int(display_mode_state, 16) == 5:
                break

        # 读取并解析Block数量
        block_count_bytes = f.read(4)  # 直接读取4字节的Block数量
        block_count_b1, block_count_b2, block_count_b3, block_count_b4 = struct.unpack('>BBBB', block_count_bytes)  # 将Block数量分解为四个字节
        block_count = f"0x{block_count_b1:02X} 0x{block_count_b2:02X} 0x{block_count_b3:02X} 0x{block_count_b4:02X}"

        # 循环读取每个Block的标识区
        integer_block_count = struct.unpack('>I', block_count_bytes)[0]
        head_style = ""
        for i in range(integer_block_count):
            block_identifier_bytes = f.read(12)  # 读取12字节作为Block标识区
            # 将每个byte转换为0xXX形式并加入列表
            block_identifier_hex_list = [f"0x{byte:02X}" for byte in block_identifier_bytes]
            # 将列表转换为字符串
            block_identifier_str = ' '.join(block_identifier_hex_list).strip()
            # print(f"Block {i + 1} Identifier: {block_identifier_str}")
            head_style+=block_identifier_str + " "
        block_data = ""
        # 然后读取并打印所有Block的起始地址和长度
        block_data_list =[]
        for i in range(integer_block_count):
            # 读取4字节的Block起始地址并转换为0xXX格式
            item = []
            start_address_bytes = f.read(4)
            start_address_hex_list = [f"0x{byte:02X}" for byte in start_address_bytes]
            start_address_str = ' '.join(start_address_hex_list).strip()
            print(f"Block {i + 1} Start Address: {start_address_str}")
            block_data += start_address_str + " "
            item.append(start_address_str)
            # 读取4字节的Block长度并转换为0xXX格式
            block_length_bytes = f.read(4)
            block_length_hex_list = [f"0x{byte:02X}" for byte in block_length_bytes]
            block_length_str = ' '.join(block_length_hex_list).strip()
            print(f"Block {i + 1} Length: {block_length_str}")
            block_data += block_length_str + " "
            item.append(block_length_str)
            block_data_list.append(item)

        print("version: ",version)
        print("block_data_list:",block_data_list)
        print("block_count: ",block_count)
        print("header_length: ", header_length)
        print("head_style: ",head_style)
        print("block_data: ",block_data)

        param = f"{version} {block_count} {header_length} {head_style} {block_data}"
        param_length = int(len(param)/5)+1
        # 转换为16进制并填充至2字节（4个16进制字符）的长度
        hex_value_2_bytes = f"{param_length:04x}"

        # 添加前缀0x
        hex_value_2_bytes_with_prefix = "0x" + hex_value_2_bytes[0:2] + " " + "0x" + hex_value_2_bytes[2:4]
        w_length = int(len(param) / 5) + 4

        tmp = f"0x81 {hex_value_2_bytes_with_prefix} {param}"
        tmp_list = tmp.split(" ")
        checksum_crc = calculate_checksum(tmp_list)
        # 81传输包头数据
        cmd = f"adb shell i2ctransfer -f -y 4 w{w_length}@0x2b 0x81 {hex_value_2_bytes_with_prefix} {param}{checksum_crc}"
        print(f"cmd:{cmd}")
        execute_cmd(cmd)
        # print(f"state: {state}")
        time.sleep(1)
        for i in range(10):
            # 获取display当前模式
            display_mode = execute_cmd(cmd_dict["display_mode"])
            display_mode_state = display_mode[15:20]
            print(f"display_mode_state: {display_mode_state}")
            if i == 10:
                break
            else:
                if int(display_mode_state, 16) != 0:
                    # #为app模式→ 切换至boot模式
                    time.sleep(1)
                    continue
        else:
            print(f"display_mode_state:{display_mode_state}")
        # checksum
        # 83 传输数据
        send_num = 1
        f.read(4)
        for item in block_data_list:
            length = hexstr_to_int(item[1])
            start =  hexstr_to_int(item[0])
            num = 0
            f.seek(start)
            while num<length:
                # 读取2048字节的数据 最大只能传输2028
                if length-num >= 2048:
                    p = 2048
                else:
                    p = length - num
                data_bytes = f.read(p)
                num+=p
                if not data_bytes:
                    break
                # 将每个byte转换为0xXX形式并加入列表
                data_bytes_hex_list = [f"0x{byte:02X}" for byte in data_bytes]
                # 将列表转换为以空格分隔的字符串
                data_bytes_str = ' '.join(data_bytes_hex_list)
                data_length = int((len(data_bytes_str)+1)/5)
                hex_value_2_bytes = f"{data_length+2:04x}"

                # 添加前缀0x
                hex_value_2_bytes_with_prefix = "0x" + hex_value_2_bytes[0:2] + " " + "0x" + hex_value_2_bytes[2:4]
                if send_num == 256:
                    send_num = 1
                tmp = f"0x83 {hex_value_2_bytes_with_prefix} {hex(send_num)} {data_bytes_str}"
                tmp_list = tmp.split(" ")
                checksum_crc = calculate_checksum(tmp_list)
                # 0x83传输升级数据
                cmd = f"adb shell i2ctransfer -f -y 4 w{data_length+5}@0x2b 0x83 {hex_value_2_bytes_with_prefix} {hex(send_num)} {data_bytes_str} {checksum_crc.strip()}"
                print("send_data:",cmd)
                time.sleep(1)

                # 每传输一个数据判断一下display当前状态是否为OK状态
                display_mode = execute_cmd(cmd_dict["display_mode"])
                display_mode_state = display_mode[15:20]
                print(f"display_mode_state: {display_mode_state}")
                # time.sleep(1)
                # 判断如果不为OK模式则退出升级
                if int(display_mode_state, 16) != 0:
                    break
                else:
                    # 状态为OK模式继续传输
                    send_num += 1



        for i in range(10):
            # 获取display当前模式为00
            display_mode = execute_cmd(cmd_dict["display_mode"])
            # print(f"display_mode: {display_mode}")
            display_mode_state = display_mode[15:20]
            # print(f"display_mode_state: {display_mode_state}")
            if int(display_mode_state, 16) != 0:
                # #为app模式→ 切换至boot模式
                time.sleep(1)
                continue
            else:
                break
            print(f"display_mode_state:{display_mode_state}")
        length = int(len(checksum_str) / 5)
        hex_value_2_bytes = f"{length + 2:04x}"
        # 添加前缀0x
        hex_value_2_bytes_with_prefix = "0x" + hex_value_2_bytes[0:2] + " " + "0x" + hex_value_2_bytes[2:4]
        tmp_list = f"0x84 {hex_value_2_bytes_with_prefix} {checksum_str}".split(" ")
        checksum_crc = calculate_checksum(tmp_list)

        time.sleep(1)
        # 校验checksum
        cmd = f"adb shell i2ctransfer -f -y 4 w{len(tmp_list)+1}@0x2b 0x84 {hex_value_2_bytes_with_prefix} {checksum_str} {checksum_crc.strip()}"
        print("checksum cmd:",cmd)
        execute_cmd(cmd)
        # 获取display当前模式是否为app模式
        time.sleep(1)
        for i in range(10):
            #
            display_mode = execute_cmd(cmd_dict["display_mode"])
            display_mode_state = display_mode[15:20]
            if int(display_mode_state, 16) != 0:
                continue
            else:
                break
        # 复位
        cmd = "adb shell i2ctransfer -f -y 4 w5@0x2b 0x85 0x00 0x02 0x01 0x86"
        execute_cmd(cmd)
        time.sleep(60)

        print("Parsing Completed.")


if __name__ == '__main__':
    ver16 = 'zeekerdispprot.bin'

    normal_flash(ver16)

