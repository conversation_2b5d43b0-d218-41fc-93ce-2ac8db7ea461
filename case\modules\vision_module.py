"""
视觉模块 - 处理视觉相关命令
"""
import logging
import operator
import threading
import time
import shutil
import os
import datetime
from typing import Dict, Any, Set
from case.module_manager import ModuleHandler

logger = logging.getLogger(__name__)


class VisionModule(ModuleHandler):
    """视觉模块处理器"""
    
    def __init__(self):
        super().__init__()
        # 延迟加载的资源
        self.signals_manager = None
        self.frequency_test = None
        self.camera_config = None
        self.camera_manager = None
        self.visual_detect_signal = None
        self.case_manager = None
        self.project_manager = None
        self.app_config = None
        self.vision_manager = None
        self.alg_recorder = None
    
    def get_supported_commands(self) -> Set[str]:
        """获取支持的命令列表"""
        return {
            'StartRecord', 'StopRecord', 'StartCollectVisionBrightness',
            'DetectVisionBrightness', 'RecordImageAlgorithm'
        }
    
    def _load_resources(self):
        """加载视觉模块资源"""
        logger.info("正在加载视觉模块资源...")
        
        # 导入视觉相关模块
        from utils.SignalsManager import signals_manager
        from vision.frequency_test import frequency_test
        from vision.camera_config import camera_config
        from vision.camera_manager import camera_manager
        from vision.visual_detect_signal import visual_detect_signal
        from case.CaseManager import case_manager
        from case.ProjectManager import project_manager
        from config.app_config import app_config
        from vision.vision_manager import vision_manager
        from vision.alg_recorder import alg_recorder
        
        self.signals_manager = signals_manager
        self.frequency_test = frequency_test
        self.camera_config = camera_config
        self.camera_manager = camera_manager
        self.visual_detect_signal = visual_detect_signal
        self.case_manager = case_manager
        self.project_manager = project_manager
        self.app_config = app_config
        self.vision_manager = vision_manager
        self.alg_recorder = alg_recorder
        
        logger.info("视觉模块资源加载完成")
    
    def execute_command(self, command: str, step: Dict[str, Any]):
        """执行视觉命令"""
        case_number = step.get("case_number", "")
        data = step.get("params", "")
        
        if operator.eq("StartRecord", command):
            self._handle_start_record(case_number, command)
        elif operator.eq("StopRecord", command):
            self._handle_stop_record(case_number, command, data)
        elif operator.eq("StartCollectVisionBrightness", command):
            self._handle_start_collect_vision_brightness(case_number, command)
        elif operator.eq("DetectVisionBrightness", command):
            self._handle_detect_vision_brightness(case_number, command, data)
        elif operator.eq("RecordImageAlgorithm", command):
            self._handle_record_image_algorithm(case_number, command, data)
        else:
            logger.warning(f"视觉模块不支持命令: {command}")
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", f"不支持的命令: {command}")
    
    def _handle_start_record(self, case_number: str, command: str):
        """处理开始录制"""
        if self.case_manager.demarcated:
            case_start_time = self.case_manager.get_case_start_time()
            start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
            file_date = f"{case_number}_{start_time}"
            date = f"{datetime.datetime.now().strftime('%Y-%m-%d')}"
            base_path = os.path.join(self.app_config.vision_folder, self.project_manager.get_test_plan_project_number(), date)
            self.camera_config.set_resource_path(base_path)
            threading.Thread(target=self.frequency_test.start_record,
                             args=(self.camera_manager.video_list_function, file_date)).start()
            from case.StepManager import step_manager
            step_manager.switch_vision_collect(True)
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG: 视觉算法画面未标定")
    
    def _handle_stop_record(self, case_number: str, command: str, data: str):
        """处理停止录制"""
        from case.StepManager import step_manager
        
        vision_revert = step_manager.vision_reverts[case_number]
        case_start_time = self.case_manager.get_case_start_time()
        start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
        file_date = f"{case_number}_{start_time}"
        self.visual_detect_signal.function_test_clear_result.emit()
        
        if self.case_manager.demarcated:
            step_manager.switch_vision_collect(False)
            test_mode = int(data.split(",")[0])
            threshold_flicker = float(data.split(",")[1])
            threshold_grainy = float(data.split(",")[2])
            try:
                threshold_black = float(data.split(",")[3])
            except Exception as e:
                threshold_black = 10
                logger.info(f"execute_customize_cmd exception: {str(e.args)}")
            
            # 获取当前次数和总次数
            current_times = 1  # 这里需要从step中获取
            total_times = 1    # 这里需要从step中获取
            
            step_manager.functionality_stop_record(file_date, current_times, total_times, threshold_flicker,
                                           threshold_grainy, threshold_black, test_mode)
            result_dict = self.frequency_test.get_functionality_result()
            logger.info(f"execute_customize_cmd result_dict={result_dict}")
            if result_dict is None:
                return self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")

            if len(result_dict) == 0:
                return self.signals_manager.step_execute_finish.emit(case_number, command, "NG",
                                                                "视觉检测异常：未检测到结果")

            result = step_manager.detect_functionality_result(vision_revert, result_dict)
            if result[0]:
                self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                self.signals_manager.step_execute_finish.emit(case_number, command, "NG", f"视觉检测异常：{result[1]}")

            self.signals_manager.update_grainy_screen_detect_value.emit(result[2])
            self.signals_manager.update_flicker_screen_detect_value.emit(result[3])
            self.signals_manager.update_black_screen_detect_value.emit(result[4])

            if self.frequency_test.current_files_path is not None:
                shutil.rmtree(self.frequency_test.current_files_path)
                logger.info(f"execute_customize_cmd delete {self.frequency_test.current_files_path} success")
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG: 视觉算法画面未标定")
    
    def _handle_start_collect_vision_brightness(self, case_number: str, command: str):
        """处理开始采集视觉亮度"""
        self.visual_detect_signal.start_brightness_test.emit()
        self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
    
    def _handle_detect_vision_brightness(self, case_number: str, command: str, data: str):
        """处理检测视觉亮度"""
        # 开启采集视觉亮度之后需要延时一段时间(例如5s)再去检测视觉亮度
        if data.__contains__(","):
            min_brightness = int(data.split(",")[0])
            max_brightness = int(data.split(",")[1])
            detect_brightness = self.frequency_test.get_black_screen_value()
            if min_brightness <= detect_brightness <= max_brightness:
                self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", str(detect_brightness))
            else:
                self.signals_manager.step_execute_finish.emit(case_number, command, "NG", str(detect_brightness))
    
    def _handle_record_image_algorithm(self, case_number: str, command: str, data: str):
        """处理记录图像算法"""
        import traceback
        
        header = ["时间", "算法", "路径", "状态", "产品型号"]
        
        try:
            self.alg_recorder.set_info(case_number, command)
            self.alg_recorder.check_server()  # 检测开启server
            is_conn = self.alg_recorder.connect()
            if not is_conn:
                self.signals_manager.step_execute_finish.emit(case_number, command, "NG", f"{traceback.format_exc()}")
                return
            
            alg_name = data.split(",")[0]
            path = data.split(",")[1]
            product_type = data.split(",")[2]
            self.alg_recorder.send(f"init:{alg_name};{path};{product_type}")
            time.sleep(1)
            result = self.alg_recorder.recv()

            item = {
                "test_function": "RecordImageAlgorithm", "header": header, "content": result,
            }
            self.alg_recorder.test_result = item
            logger.info(f"RecordImageAlgorithm result:{item}")
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", f"{traceback.format_exc()}")
        except Exception:
            logger.error(traceback.format_exc())
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", f"{traceback.format_exc()}")
