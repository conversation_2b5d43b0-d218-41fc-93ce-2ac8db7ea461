from power.tools.ETM3020PCControl import etm_3020pc_control
from power.tools.etm_mu3_control import etm_mu3_control
from power.tools.it_m3200_control import it_m3200_control


class PowerManager:

    def __init__(self):
        super().__init__()

    @staticmethod
    def set_volt(volt, channel=1):
        power_status = True
        set_status = False, ''
        if it_m3200_control.is_connect():
            set_status = it_m3200_control.set_volt(volt)
        elif etm_3020pc_control.is_open():
            set_status = etm_3020pc_control.set_voltage(volt)
        elif etm_mu3_control.is_open():
            set_status = etm_mu3_control.set_voltage(channel=channel, value=volt)
        else:
            power_status = False

        return power_status, set_status


power_manager: PowerManager = PowerManager()
