import subprocess
import time
import struct
import os

# from PyQt5.QtCore import QObject

from utils.SignalsManager import signals_manager

# Constants
I2C_DEVICE = "4"
I2C_SLAVE_ADDR = 0x1a
GPIO_GROUP = "gpa"
IRQ_NUM = 2
GPIO_FLAG = 1
PROJECT_NAME = "jietu"
REGION = "international"
current_dir = os.path.join(os.getcwd(),"i2c_update")
# 这里是刷机包的地址
UPDATE_FILENAME =  os.path.join(current_dir,"bin","display.s19")
# UPDATE_FILENAME =  r"C:\Users\<USER>\Downloads\02.05\displayupdate\display.s19"
# UPDATE_PACKAGE = r"C:\Users\<USER>\Downloads\mcu_update/displayupdate.zip"

# Commands
CHECK_PASSWORD = [0x87, 0x08, 0x00, 0x00, 0x00, 0x00, 0x90]
REQUEST_BOOT = [0x5a, 0x05, 0x07, 0x02, 0x31, 0x01, 0x9a]
KEY1 = [0x5a, 0x05, 0x07, 0x04, 0x31, 0x03, 0xA5, 0x5A, 0x9D]
KEY2 = [0x5a, 0x05, 0x07, 0x04, 0x31, 0x04, 0xc3, 0x3C, 0x9E]
BOOTLOADER_STATUS = [0x5a, 0x05, 0x0B, 0x06, 0xB0, 0x01, 0x01, 0x00, 0x00, 0x00, 0x22]
ERASURE_APP = [0x5a, 0x05, 0x0B, 0x06, 0xB0, 0x01, 0x02, 0x00, 0x00, 0x00, 0x23]
PROGRAM = [0x5a, 0x05, 0x0B, 0x06, 0xB0, 0x01, 0x03, 0x00, 0x00, 0x00, 0x24]
FLASH_CHECKSUM = [0x5a, 0x05, 0x0B, 0x06, 0xB0, 0x01, 0x04, 0x56, 0x66, 0x00]
RESET = [0x5a, 0x05, 0x0B, 0x06, 0xB0, 0x01, 0x05, 0x00, 0x00, 0x00, 0x26]


class E02():
    def __init__(self):
        self.all_checksum = 0
        self.UPDATE_FILENAME = UPDATE_FILENAME
    def calculate_checksum(self, data):
        data = [int(byte, 16) for byte in data]
        checksum = (sum(data)  )& 0xFF
        return f"{checksum:#04x}".strip()

    def adb_shell_command(self, cmd):
        if isinstance(cmd, list):
            cmd = " ".join(cmd)
        if not cmd.startswith("adb"):
            cmd = f"adb shell {cmd}"
        command = [ cmd]
        print("cmd"," ".join(command))
        proc = subprocess.Popen(
            " ".join(command),
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            encoding='utf-8'
        )

        # 获取标准输出和标准错误
        out, err = proc.communicate()

        # 检查返回码
        if proc.returncode == 0:
            print("out:",out)
        else:
            print("错误信息:")
            signals_manager.flash_log.emit(f"error:{err}")
            print(err)

        return out

    def i2c_open(self,device, addr):
        self.adb_shell_command(["adb shell ","i2cset", "-y", device, str(addr), "0x00"])

    def i2c_write(self,wdata,readData,is_check=True):
        # i2ctransfer -f -y 4 w7@0x38 0x82 0x64 0x00 0x00 0x00 0x00 0xe7
        # cmd = ["adb  shell ","i2ctransfer", "-y 4", I2C_DEVICE, "w",""] + [f"0x{byte:02x}" for byte in data]

        data = [f"0x{byte:02x}".upper() for byte in wdata]
        length = len(data) +1
        data_str = " ".join(data).strip()

        checksum = self.calculate_checksum(data)
        cmd = f"i2ctransfer -f -y 4 w{length}@0x38 {data_str} {checksum};{readData}"
        result = self.adb_shell_command(cmd)
        return result.strip()

    def i2c_read(self,length):
        cmd = ["i2ctransfer", "-f -y ", I2C_DEVICE,f" w1@0x38 0xfe r{length}"]
        output = self.adb_shell_command(cmd)
        return [int(byte, 16) for byte in output.split()]

    def wait_mcu_irq(self,ms):
        time.sleep(ms / 1000)

    def change_ascil_to_hex(self,ch):
        if ch == "S":
            return ord(ch)
        else:
            return int(ch, 16)

    def convert_hex(self,input_str):
        # 将'S'替换为它的ASCII码(0x53)
        input_str = input_str.decode().strip()
        result = input_str.replace('S', '53')
        p = ["53", result[2]]
        # 将字符串每两个字符分组
        hex_pairs = [result[i:i + 2] for i in range(3, len(result), 2)]
        p += hex_pairs
        # 转换为十六进制格式并打印
        formatted_hex = ' '.join([f"0x{pair}" for pair in p])
        print(formatted_hex)
        return formatted_hex
    def parse_line(self, buf):
        """解析单行数据"""
        try:
            # 确保buf是字节串
            if isinstance(buf, str):
                buf = buf.encode()

            buf = buf.strip()
            if not buf:
                return None

            # 基本数据包头部
            len_data = (len(buf) - 2) // 2 + 1
            send_buf = [0x5a, 0x05, 0x0B, len_data]

            # 添加第一个数据字节
            send_buf.append(buf[0])
            # 第二个字节需要调整
            send_buf.append(buf[1] - 0x30)

            # 处理剩余数据
            for i in range(2, len(buf) - 2, 2):
                ch1 = self.change_ascil_to_hex(chr(buf[i]))
                # ch1 = self.change_ascil_to_hex(buf[i])
                ch2 = self.change_ascil_to_hex(chr(buf[i + 1]))
                if ch1 >= 0 and ch2 >= 0:
                    byte_val = (ch1 << 4) + ch2
                    send_buf.append(byte_val)

                    # 更新S19校验和
                    if i >= 14 and send_buf[5] == 0x03 and i < len(buf) - 4:
                        self.s19_checksum += byte_val

            return send_buf
        except Exception as e:
            print(f"Error parsing line: {e}")
            return None
    def parse_and_send_update_data(self,):
        signals_manager.flash_process.emit("读取文件")
        readData = f"i2ctransfer -f -y  4  w1@0x38 0xfe r11;"
        with open(self.UPDATE_FILENAME, "rb") as fd_file:
            line = 0
            signals_manager.flash_process.emit("发送文件")
            while True:
                buf = fd_file.readline()
                if not buf:
                    break
                len_data = (len(buf) - 2) // 2 + 1
                new_buf =self.convert_hex(buf)
                new_buf = [int(byte, 16) for byte in new_buf.split(" ")]
                tmp_check_sum = sum(new_buf[7:-1])
                send_buf = [0x5a, 0x05, 0x0B, len_data] +new_buf
                # checksum = (sum(send_buf))& 0xFF
                # send_buf.append(checksum)
                for i in range(3):
                    response =self.i2c_write(send_buf,readData)
                    if response =='0x5a 0x06 0x0b 0x06 0xb0 0x03 0x01 0x00 0x00 0x00 0x25':
                        line += 1
                        if line >1:
                            self.all_checksum +=tmp_check_sum
                        break
                    else:
                        print("Error:", response)
                        signals_manager.flash_log.emit(f"fail and retray response = {response}")
                        signals_manager.flash_log.emit("Retry the line")
                        continue
    # def check_md5(self,filename):
    #     self.adb_shell_command(["unzip", UPDATE_PACKAGE, "-d", "/data/mcu_update"])
    #     md5_cmd = f"md5sum {UPDATE_FILENAME} > /data/upgrade.md5"
    #     self.adb_shell_command(["sh", "-c", md5_cmd])
    #     md5_a = self.adb_shell_command(["md5sum", f"{filename}.md5"]).split()[0]
    #     md5_b = self.adb_shell_command(["cat", "/data/upgrade.md5"]).split()[0]
    #     self.adb_shell_command(["rm", "-f", "/data/upgrade.md5"])
    #     return md5_a == md5_b

    def main(self,):
        cmd = 'adb shell "echo 4-0038 > /sys/bus/i2c/drivers/CHERY_DISP_PROT/unbind"'
        self.adb_shell_command(cmd)
        signals_manager.flash_log.emit(f"cmd:{cmd}")
        length = 7
        readData = f"i2ctransfer -f -y  4  w1@0x38 0xfe r{length};"

        response = self.i2c_write(REQUEST_BOOT,readData) # 4
        if response != '0x5a 0x06 0x07 0x02 0x31 0x01 0x9b':
            print("REQUEST_BOOT failed")
            signals_manager.flash_log.emit("REQUEST_BOOT failed")
            return
        print("REQUEST_BOOT success!")
        signals_manager.flash_log.emit("REQUEST_BOOT success!")
        response = self.i2c_write(KEY1,readData) #5
        if response != '0x5a 0x06 0x07 0x02 0x31 0x03 0x9d':
            print("Key1 failed")
            signals_manager.flash_log.emit("Key1 failed")

            return
        print("KEY1 success!")
        signals_manager.flash_log.emit("KEY1 success!")

        response = self.i2c_write(KEY2,readData) #6
        if response != '0x5a 0x06 0x07 0x02 0x31 0x04 0x9e':
            print("Key2 failed")
            signals_manager.flash_log.emit("Key2 failed")
            return
        print("KEY2 success!")
        signals_manager.flash_log.emit("KEY2 success!")
        time.sleep(3)
        length = 11
        readData = f"i2ctransfer -f -y  4  w1@0x38 0xfe r{length};"
        response = self.i2c_write(BOOTLOADER_STATUS,readData) #7.1
        if response != '0x5a 0x06 0x0b 0x06 0xb0 0x02 0x01 0x01 0x00 0x00 0x25':
            print("Bootloader not ready")
            signals_manager.flash_log.emit("Bootloader not ready")
            return
        print("Bootloader ready")
        signals_manager.flash_log.emit("Bootloader ready")
        length = 1
        readData = f"i2ctransfer -f -y  4  w1@0x38 0xfe r{length};"
        response = self.i2c_write(CHECK_PASSWORD,readData)  # 7.3

        if response != "0x44":#7.4
            print(" CHECK_PASSWORD failed")
            signals_manager.flash_log.emit("CHECK_PASSWORD failed")
            return
        print(" CHECK_PASSWORD success!")
        signals_manager.flash_log.emit("CHECK_PASSWORD success!")
        response = self.i2c_write(ERASURE_APP,"") #8
        print(f"Eraser app: {response}")
        signals_manager.flash_log.emit("Eraser app!")
        self.wait_mcu_irq(2000)
        length = 11
        readData = f"i2ctransfer -f -y  4  w1@0x38 0xfe r{length};"
        response = self.i2c_write(BOOTLOADER_STATUS,readData)

        if response != '0x5a 0x06 0x0b 0x06 0xb0 0x02 0x01 0x02 0x00 0x00 0x26':
            print("Eraser app failed")
            signals_manager.flash_log.emit("Eraser app failed")
            return
        print("Eraser app success!")
        signals_manager.flash_log.emit("Eraser app success!")

        for i in range(3):
            response = self.i2c_write(PROGRAM,"") # return None
            response = self.i2c_write(BOOTLOADER_STATUS,readData)
            if response != '0x5a 0x06 0x0b 0x06 0xb0 0x02 0x01 0x03 0x00 0x00 0x27':
                print("Program failed")
                signals_manager.flash_log.emit("Program failed")
                continue
            else:
                print("Program success!")
                signals_manager.flash_log.emit("Program success!")
                length = 11
                readData = f"i2ctransfer -f -y  4  w1@0x38 0xfe r{length};"
                response = self.i2c_write(BOOTLOADER_STATUS, readData)  # 7.1
                if response != '0x5a 0x06 0x0b 0x06 0xb0 0x02 0x01 0x03 0x00 0x00 0x27':
                    print("Bootloader not ready")
                    signals_manager.flash_log.emit("Bootloader not ready!")
                    return
                print("Bootloader ready")
                signals_manager.flash_log.emit("Bootloader ready")

                break


        self.parse_and_send_update_data()

        length = 11
        readData = f"i2ctransfer -f -y  4  w1@0x38 0xfe r{length};"
        print("self.all_checksum",self.all_checksum)


        hex_representation = format(self.all_checksum, '04x')

        # 将十六进制字符串分割为两个字节
        hex_byte2 = '0x' + hex_representation[2:4]
        hex_byte1= '0x' + hex_representation[4:]
        FLASH_CHECKSUM[7] = int(hex_byte1,16)  # Update with actual checksum
        FLASH_CHECKSUM[8] = int(hex_byte2,16)   # Update with actual checksum
        # FLASH_CHECKSUM[10] = sum(FLASH_CHECKSUM[:10]) & 0xFF
        response = self.i2c_write(FLASH_CHECKSUM,readData)
        print("response：",response)
        signals_manager.flash_log.emit("Flash checksum success!")
        print("Flash checksum success!")

        length = 11
        readData = f"i2ctransfer -f -y  4  w1@0x38 0xfe r{length};"
        response = self.i2c_write(RESET, readData)
        print("response：", response)
        signals_manager.flash_log.emit("RESET app success!")
        print("RESET app success!")
        time.sleep(.1)
        length = 11
        readData = f"i2ctransfer -f -y  4  w1@0x38 0xfe r{length};"
        response = self.i2c_write(BOOTLOADER_STATUS, readData)  # 7.1
        if response != '0x5a 0x06 0x0b 0x06 0xb0 0x02 0x01 0x05 0x00 0x00 0x29':
            print("Bootloader not ready")
            signals_manager.flash_log.emit("Bootloader not ready")
            return
        print("Bootloader ready")
        signals_manager.flash_log.emit("Bootloader ready")
        time.sleep(5)
        cmd = "adb reboot"
        self.adb_shell_command(cmd)
        signals_manager.flash_log.emit("device reboot")
        signals_manager.flash_log.emit("flash done")

if __name__ == "__main__":
    c = E02()
    c.main()
    # hex_representation = format(7604130, '04x')
    # print(hex_representation)