# -*- coding: utf-8 -*-
"""
user:Created by jid on 2021/3/25 17:50
email:<EMAIL>
description:
"""
from ctypes import windll, c_char_p, c_bool, byref, c_float, c_int

from common.LogUtils import logger


class SysHXCTAManager:

    def __init__(self):
        super(SysHXCTAManager, self).__init__()
        self.SysHXCTA = windll.LoadLibrary('dll/SysHXCTA.dll')
        self.get_version_support_ic()

    def get_version_support_ic(self):
        version_p = c_char_p(''.encode())
        version_state = self.SysHXCTA.DLL_Version(version_p)
        logger.info("DLL_Version: " + version_p.value.decode())

        ic_list_p = c_char_p(''.encode())
        support_ic_state = self.SysHXCTA.DLL_SupportIC(ic_list_p)
        logger.info("DLL_SupportIC: " + ic_list_p.value.decode())

    def get_DGCTable(self, input_R, input_G, input_B, input_grey_x, input_grey_y, input_grey_Y):
        ErrMsg_p = c_char_p(''.encode())
        Y_lose_flag = byref(c_bool(False))
        R_STR_p = c_char_p(''.encode())
        G_STR_p = c_char_p(''.encode())
        B_STR_p = c_char_p(''.encode())
        ChipID_p = c_char_p('HX8880-D'.encode())
        GammaSet = c_float(2.2)
        Only_Tune_Gamma = c_bool(False)
        Set_xy_range_flag = c_bool(False)
        Y_dw_limit = c_float(0.5)
        Y_up_limit = c_float(1.0)
        Target_sx_dw = c_float(0.2900)
        Target_sx_up = c_float(0.0)
        Target_sy_dw = c_float(0.3150)
        Target_sy_up = c_float(0.0)

        Ref_Gray_sx = (c_float * 1)(0.0)
        Ref_Gray_sy = (c_float * 1)(0.0)
        Ref_Gray_Y = (c_float * 1)(0.0)
        Ref_Gray_Len = c_int(len(Ref_Gray_Y))

        level = [1023, 993, 963, 933, 903, 873, 843, 813, 783, 753, 723, 693, 663, 633, 603, 573, 543, 513, 483, 453,
                 423, 393, 363, 333, 303, 273, 243, 213, 183, 153, 123, 93, 63, 33, 0]

        # level = [255, 248, 240, 233, 225, 218, 210, 203, 195, 188, 180, 173, 165, 158, 150, 143, 135, 128, 120, 113,
        #          105, 98, 90, 83, 75, 68, 60, 53, 45, 38, 30, 23, 15, 8, 0]
        Input_Level = (c_int * len(level))()
        for i in range(len(level)):
            Input_Level[i] = level[i]
            logger.debug("i=%d, level=%d" % (i, Input_Level[i]))

        # grey_sx = [0.3428, 0.3464, 0.3480, 0.3489, 0.3495, 0.3498, 0.3501, 0.3505, 0.3506, 0.3505, 0.3507, 0.3507,
        #            0.3506, 0.3513, 0.3505, 0.3504, 0.3507, 0.3508, 0.3509, 0.3506, 0.3510, 0.3513, 0.3498, 0.3510,
        #            0.3512, 0.3510, 0.3499, 0.3494, 0.3483, 0.3477, 0.3453, 0.3407, 0.3299, 0.3084, 0.2904]
        Input_Gray_sx = (c_float * len(input_grey_x))()
        for i in range(len(input_grey_x)):
            Input_Gray_sx[i] = input_grey_x[i]

        # grey_sy = [0.3508, 0.3547, 0.3563, 0.3571, 0.3577, 0.3580, 0.3583, 0.3584, 0.3585, 0.3585, 0.3585, 0.3585,
        #            0.3584, 0.3582, 0.3583, 0.3582, 0.3580, 0.3578, 0.3577, 0.3576, 0.3574, 0.3571, 0.3571, 0.3567,
        #            0.3564, 0.3561, 0.3558, 0.3553, 0.3544, 0.3530, 0.3503, 0.3455, 0.3333, 0.3068, 0.2856]
        Input_Gray_sy = (c_float * len(input_grey_y))()
        for i in range(len(input_grey_y)):
            Input_Gray_sy[i] = input_grey_y[i]
            # logger.debug(Input_Gray_sy[i])

        # grey_Y = [1371.0000, 1292.0000, 1201.0000, 1119.0000, 1035.0000, 962.1000, 879.8000, 809.6000, 737.4000,
        #           676.6000, 606.5000, 548.0000, 484.2000, 428.6000, 374.9000, 331.0000, 283.7000, 247.1000, 204.2000,
        #           171.8000, 137.6000, 112.0000, 87.4700, 67.7300, 49.2800, 36.1900, 24.4600, 16.4200, 9.6880, 5.5600,
        #           2.6550, 1.1700, 0.3398, 0.0757, 0.0155]
        Input_Gray_Y = (c_float * len(input_grey_Y))()
        for i in range(len(input_grey_Y)):
            Input_Gray_Y[i] = input_grey_Y[i]

        Input_Gray_Len = c_int(len(Input_Gray_Y))

        Input_R_sx = (c_float * 1)(input_R[0])
        Input_R_sy = (c_float * 1)(input_R[1])
        Input_R_Y = (c_float * 1)(input_R[2])
        Input_R_Len = c_int(len(Input_R_Y))

        Input_G_sx = (c_float * 1)(input_G[0])
        Input_G_sy = (c_float * 1)(input_G[1])
        Input_G_Y = (c_float * 1)(input_G[2])
        Input_G_Len = c_int(len(Input_G_Y))

        Input_B_sx = (c_float * 1)(input_B[0])
        Input_B_sy = (c_float * 1)(input_B[1])
        Input_B_Y = (c_float * 1)(input_B[2])
        Input_B_Len = c_int(len(Input_B_Y))

        DGCTable_p = c_char_p(''.encode())

        self.SysHXCTA.CTA_Main(ErrMsg_p, Y_lose_flag, R_STR_p, G_STR_p, B_STR_p, ChipID_p, GammaSet,
                               Only_Tune_Gamma, Set_xy_range_flag, Y_dw_limit, Y_up_limit, Target_sx_dw,
                               Target_sx_up, Target_sy_dw, Target_sy_up, Ref_Gray_sx, Ref_Gray_sy,
                               Ref_Gray_Y, Ref_Gray_Len, Input_Level, Input_Gray_sx, Input_Gray_sy, Input_Gray_Y,
                               Input_Gray_Len, Input_R_sx, Input_R_sy, Input_R_Y, Input_R_Len, Input_G_sx,
                               Input_G_sy, Input_G_Y, Input_G_Len, Input_B_sx, Input_B_sy, Input_B_Y, Input_B_Len,
                               DGCTable_p)

        r_str = R_STR_p.value.decode()
        g_str = G_STR_p.value.decode()
        b_str = B_STR_p.value.decode()
        logger.info('r_str=%s' % r_str)
        logger.info('g_str=%s' % g_str)
        logger.info('b_str=%s' % b_str)

        DGCTable = DGCTable_p.value.decode()
        logger.info('DGCTable=%s' % DGCTable)
        logger.info('ChipID_p=%s' % ChipID_p.value.decode())
        return DGCTable

    def get_local_DGCTable(self):
        ErrMsg_p = c_char_p(''.encode())
        Y_lose_flag = byref(c_bool(False))
        R_STR_p = c_char_p(''.encode())
        G_STR_p = c_char_p(''.encode())
        B_STR_p = c_char_p(''.encode())
        ChipID_p = c_char_p('HX8880-D'.encode())
        GammaSet = c_float(2.2)
        Only_Tune_Gamma = c_bool(False)
        Set_xy_range_flag = c_bool(False)
        Y_dw_limit = c_float(0.5)
        Y_up_limit = c_float(1.0)
        Target_sx_dw = c_float(0.2900)
        Target_sx_up = c_float(0.0)
        Target_sy_dw = c_float(0.3150)
        Target_sy_up = c_float(0.0)

        Ref_Gray_sx = (c_float * 1)(0.0)
        Ref_Gray_sy = (c_float * 1)(0.0)
        Ref_Gray_Y = (c_float * 1)(0.0)
        Ref_Gray_Len = c_int(len(Ref_Gray_Y))

        level = [1023, 993, 963, 933, 903, 873, 843, 813, 783, 753, 723, 693, 663, 633, 603, 573, 543, 513, 483, 453,
                 423, 393, 363, 333, 303, 273, 243, 213, 183, 153, 123, 93, 63, 33, 0]

        # level = [255, 248, 240, 233, 225, 218, 210, 203, 195, 188, 180, 173, 165, 158, 150, 143, 135, 128, 120, 113,
        #          105, 98, 90, 83, 75, 68, 60, 53, 45, 38, 30, 23, 15, 8, 0]
        Input_Level = (c_int * len(level))()
        for i in range(len(level)):
            Input_Level[i] = level[i]
            logger.debug("i=%d, level=%d" % (i, Input_Level[i]))

        grey_sx = [0.3428, 0.3464, 0.3480, 0.3489, 0.3495, 0.3498, 0.3501, 0.3505, 0.3506, 0.3505, 0.3507, 0.3507,
                   0.3506, 0.3513, 0.3505, 0.3504, 0.3507, 0.3508, 0.3509, 0.3506, 0.3510, 0.3513, 0.3498, 0.3510,
                   0.3512, 0.3510, 0.3499, 0.3494, 0.3483, 0.3477, 0.3453, 0.3407, 0.3299, 0.3084, 0.2904]
        Input_Gray_sx = (c_float * len(grey_sx))()
        for i in range(len(grey_sx)):
            Input_Gray_sx[i] = grey_sx[i]

        grey_sy = [0.3508, 0.3547, 0.3563, 0.3571, 0.3577, 0.3580, 0.3583, 0.3584, 0.3585, 0.3585, 0.3585, 0.3585,
                   0.3584, 0.3582, 0.3583, 0.3582, 0.3580, 0.3578, 0.3577, 0.3576, 0.3574, 0.3571, 0.3571, 0.3567,
                   0.3564, 0.3561, 0.3558, 0.3553, 0.3544, 0.3530, 0.3503, 0.3455, 0.3333, 0.3068, 0.2856]
        Input_Gray_sy = (c_float * len(grey_sy))()
        for i in range(len(grey_sy)):
            Input_Gray_sy[i] = grey_sy[i]
            # logger.debug(Input_Gray_sy[i])

        grey_Y = [1371.0000, 1292.0000, 1201.0000, 1119.0000, 1035.0000, 962.1000, 879.8000, 809.6000, 737.4000,
                  676.6000, 606.5000, 548.0000, 484.2000, 428.6000, 374.9000, 331.0000, 283.7000, 247.1000, 204.2000,
                  171.8000, 137.6000, 112.0000, 87.4700, 67.7300, 49.2800, 36.1900, 24.4600, 16.4200, 9.6880, 5.5600,
                  2.6550, 1.1700, 0.3398, 0.0757, 0.0155]
        Input_Gray_Y = (c_float * len(grey_Y))()
        for i in range(len(grey_Y)):
            Input_Gray_Y[i] = grey_Y[i]

        Input_Gray_Len = c_int(len(Input_Gray_Y))

        Input_R_sx = (c_float * 1)(0.6838)
        Input_R_sy = (c_float * 1)(0.3121)
        Input_R_Y = (c_float * 1)(332.1000)
        Input_R_Len = c_int(len(Input_R_Y))

        Input_G_sx = (c_float * 1)(0.2814)
        Input_G_sy = (c_float * 1)(0.6811)
        Input_G_Y = (c_float * 1)(924.8000)
        Input_G_Len = c_int(len(Input_G_Y))

        Input_B_sx = (c_float * 1)(0.1507)
        Input_B_sy = (c_float * 1)(0.0707)
        Input_B_Y = (c_float * 1)(102.6000)
        Input_B_Len = c_int(len(Input_B_Y))

        DGCTable_p = c_char_p(''.encode())

        self.SysHXCTA.CTA_Main(ErrMsg_p, Y_lose_flag, R_STR_p, G_STR_p, B_STR_p, ChipID_p, GammaSet,
                               Only_Tune_Gamma, Set_xy_range_flag, Y_dw_limit, Y_up_limit, Target_sx_dw,
                               Target_sx_up, Target_sy_dw, Target_sy_up, Ref_Gray_sx, Ref_Gray_sy,
                               Ref_Gray_Y, Ref_Gray_Len, Input_Level, Input_Gray_sx, Input_Gray_sy, Input_Gray_Y,
                               Input_Gray_Len, Input_R_sx, Input_R_sy, Input_R_Y, Input_R_Len, Input_G_sx,
                               Input_G_sy, Input_G_Y, Input_G_Len, Input_B_sx, Input_B_sy, Input_B_Y, Input_B_Len,
                               DGCTable_p)

        # logger.info(ErrMsg_p.value.decode())
        logger.info(DGCTable_p.value.decode())


SysHXCTA_manager: SysHXCTAManager = SysHXCTAManager()
