from queue import Queue
import numpy as np
from PyQt5.QtCore import QObject, pyqtSignal


class Manager(QObject):
    stop_get_frame = False
    left_img_Q = Queue()
    right_img_Q = Queue()
    photo_Q =  Queue()
    red_dot_finish=pyqtSignal(str,list,str)
    photo_path_signal = pyqtSignal(str,str,str)
    calibration_signal = pyqtSignal(int, np.ndarray)
    camera_config = {}
    num_cameras = 4
    dot_num = 3
    max_x = 1800
    min_x =500
    max_y= 930
    min_y = 380

    # 修正计算角度的结果
    @staticmethod
    def fix_camera_results(camera_num, result):
        result = abs(result)
        print("fix_camera_results:", camera_num, result)
        if str(camera_num) == "7" :  # 第一组相机
            if result > 20:
                return result -3.5
            return result
        # else:
        #     return result
        if str(camera_num) == "6":
            if result > 20:
                return result - 3.5
            return result
        else:
            return result
        # if str(camera_num) == "2":  # 第2组相机
        #     # if result >= 70:
        #     #     return result * 1.3
        #     if result > 40:
        #         return result * 1.1
        #     return result + 2
        # if str(camera_num) == "3":  # 第3组相机
        #     # if result >= 70:
        #     #     return result * 1.3
        #     if result > 40:
        #         return result * 1.1
        #     return result + 2


managerQ = Manager()
