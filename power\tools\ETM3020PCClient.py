import time

import serial
from modbus_tk import defines
from modbus_tk.modbus_rtu import RtuMaster

from common.LogUtils import logger
from utils.SignalsManager import signals_manager


class ETM3020PCClient:

    def __init__(self):
        self.device_name = None
        self.port = None
        self.baudrate = None
        self.bytesize = None
        self.parity = None
        self.stopbits = None
        self.slave_id = None
        self.timeout = None
        self._is_open = False
        self._master = None

    @property
    def is_open(self):
        return self._is_open

    def open(self, device_name, port, baudrate=9600, bytesize=8, parity="N", stopbits=1, slave_id=1, timeout=5.0):
        self.device_name = device_name
        self.port = port
        self.baudrate = baudrate
        self.bytesize = bytesize
        self.parity = parity
        self.stopbits = stopbits
        self.slave_id = slave_id
        self.timeout = timeout

        if not self._is_open:
            try:
                if self._master:
                    self._master.close()
                self._master = RtuMaster(
                    serial.Serial(port=self.port, baudrate=self.baudrate, bytesize=self.bytesize,
                                  parity=self.parity, stopbits=self.stopbits)
                )
                self._master.set_timeout(self.timeout)
                self._master.open()
                self._is_open = True
            except Exception as e:
                logger.error("open exception: {}".format(str(e.args)))
                self._is_open = False
        signals_manager.update_device_status_signal.emit(device_name, self._is_open)
        return self._is_open

    def close(self):
        logger.info("close")
        if self._is_open:
            if self._master:
                self._master.close()
                self._master = None
                self._is_open = False

        return True

    def execute(self, function_code, starting_address, quantity_of_x=0, output_value=0,
                data_format="", expected_length=-1, write_starting_address_fc23=0):
        if not self._master:
            return None

        try:
            r = self._master.execute(
                self.slave_id, function_code, starting_address, quantity_of_x,
                output_value, data_format, expected_length, write_starting_address_fc23
            )
            if r is None:
                return None
            return r
        except Exception as e:
            logger.error(f"execute exception: {str(e.args)}")
            return None

    def retry_execute(self, function_code, starting_address, quantity_of_x=0, output_value=0, data_format="",
                      expected_length=-1, write_starting_address_fc23=0, retry_times=5):
        r = None
        for i in range(retry_times):
            r = self.execute(function_code, starting_address, quantity_of_x, output_value, data_format, expected_length,
                         write_starting_address_fc23)
            if r is not None:
                return r
            time.sleep(0.5)
        return r

    def read_hr_one(self, starting_address):
        """保持寄存器单个读取"""
        r = self.retry_execute(defines.READ_HOLDING_REGISTERS, starting_address, 1)
        logger.info(f"read_hr_one r={r}")
        if r is None:
            return None
        return r[0]

    def read_hr_many(self, starting_address, quantity_of_x):
        """保持寄存器多个读取"""
        r = self.retry_execute(defines.READ_HOLDING_REGISTERS, starting_address, quantity_of_x)
        return r

    def write_hr_one(self, starting_address, value):
        """保持寄存器单个写入"""
        r = self.retry_execute(defines.WRITE_SINGLE_REGISTER, starting_address, 1, value)
        return r

    def write_hr_many(self, starting_address, data, data_format=""):
        """保持寄存器多个写入"""
        r = self.retry_execute(defines.WRITE_MULTIPLE_REGISTERS, starting_address=starting_address,
                         output_value=data, data_format=data_format)
        return r

    def get_voltage(self):
        return self.read_hr_one(0x10)

    def get_electric_current(self):
        return self.read_hr_one(0x11)

    def get_electric_power(self):
        return self.retry_execute(defines.READ_HOLDING_REGISTERS, starting_address=0x12, quantity_of_x=2, data_format=">I")

    def get_vcp(self):
        return self.retry_execute(defines.READ_HOLDING_REGISTERS, starting_address=0x10, quantity_of_x=4, data_format=">HHI")

    def power_on(self):
        return self.write_hr_one(0x1, 1)

    def power_off(self):
        return self.write_hr_one(0x1, 0)

    def get_preinstall_voltage(self):
        return self.read_hr_one(0x30)

    def get_preinstall_electric_current(self):
        return self.read_hr_one(0x31)

    def set_preinstall_voltage(self, value):
        return self.write_hr_one(0x30, value)

    def set_preinstall_electric_current(self, value):
        return self.write_hr_one(0x31, value)

    def get_protection_status(self):
        return self.read_hr_one(0x2)

    def set_ovp(self, value):
        return self.write_hr_one(0x20, value)

    def set_ocp(self, value):
        return self.write_hr_one(0x21, value)

    def set_opp(self, value):
        return self.write_hr_many(0x22, value, data_format=">I")

    def get_decimal_digits(self):
        return self.read_hr_one(0x5)
