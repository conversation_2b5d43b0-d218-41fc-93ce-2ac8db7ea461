import ctypes
import inspect
import logging
import os
import threading
import time
import traceback

import cv2
from PyQt5.QtCore import QThread

from utils.SignalsManager import signals_manager

logger = logging.getLogger("tools")


class ThreadStopException:
    pass


def convert_seconds(seconds):
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = int(seconds % 60)
    return int(hours), int(minutes), seconds


def threadsafe_function(fcn):
    """decorator making sure that the decorated function is thread safe"""
    lock = threading.RLock()

    def new(*args, **kwargs):
        """Lock and call the decorated function

           Unless kwargs['threadsafe'] == False
        """
        threadsafe = kwargs.pop('threadsafe', True)
        if threadsafe:
            with lock:
                ret = fcn(*args, **kwargs)
        else:
            ret = fcn(*args, **kwargs)
        return ret

    return new


class ThreadRunner:
    def __init__(self, func, *args, **kwargs):
        self.func = func
        self.args = args
        self.kwargs = kwargs

        self.thread = threading.Thread(target=self.run, daemon=True)

        self.exitcode = 0
        self.exception = None
        self.exc_traceback = ""

    def run(self):
        try:
            logger.info("%s 工作线程开始", self.func.__name__)
            self.func(*self.args, **self.kwargs)
            logger.info("%s 工作线程结束", self.func.__name__)
        except ThreadStopException:
            logger.info("%s 工作线程结束", self.func.__name__)
        except Exception as e:
            print(e.args)
            logger.warning("工作线程崩溃！%s\n%s", self.func.__name__, traceback.format_exc())

    @property
    def thread_id(self):
        return self.thread.ident

    def _async_raise(self, tid, exctype):
        if not inspect.isclass(exctype):
            logger.warning("Only types can be raised (not instances)")
        res = ctypes.pythonapi.PyThreadState_SetAsyncExc(ctypes.c_long(tid), ctypes.py_object(exctype))
        if res == 0:
            logger.warning("invalid thread id")
        elif res != 1:
            ctypes.pythonapi.PyThreadState_SetAsyncExc(ctypes.c_long(tid), 0)
            logger.warning("PyThreadState_SetAsyncExc failed")

    def stop(self):
        while self.thread.is_alive():
            self._async_raise(self.thread_id, ThreadStopException)
            time.sleep(0.1)

    def start(self):
        self.thread.start()

    def __del__(self):
        self.stop()


class CustomQThread(QThread):
    def __init__(self):
        super().__init__()

        self.thread_id = None

    @staticmethod
    def _async_raise(tid, exctype):
        """raises the exception, performs cleanup if needed"""
        tid = ctypes.c_long(tid)
        if not inspect.isclass(exctype):
            exctype = type(exctype)
        res = ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, ctypes.py_object(exctype))
        if res == 0:
            raise ValueError("invalid thread id")
        elif res != 1:
            # """if it returns a number greater than one, you're in trouble,
            # and you should call it again with exc=NULL to revert the effect"""
            ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, None)
            raise SystemError("PyThreadState_SetAsyncExc failed")

    def stop(self):
        while self.isRunning() and self.thread_id:
            self._async_raise(self.thread_id, ThreadStopException)
            time.sleep(0.1)


class CustomThread(threading.Thread):
    def __init__(self):
        super(CustomThread, self).__init__()
        # super().__init__()

        self.thread_id = None

    @staticmethod
    def _async_raise(tid, exctype):
        """raises the exception, performs cleanup if needed"""
        tid = ctypes.c_long(tid)
        if not inspect.isclass(exctype):
            exctype = type(exctype)
        res = ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, ctypes.py_object(exctype))
        if res == 0:
            raise ValueError("invalid thread id")
        elif res != 1:
            # """if it returns a number greater than one, you're in trouble,
            # and you should call it again with exc=NULL to revert the effect"""
            ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, None)
            raise SystemError("PyThreadState_SetAsyncExc failed")

    def stop(self):
        while self.is_alive() and self.thread_id:
            self._async_raise(self.thread_id, ThreadStopException)
            time.sleep(0.1)


class SearchCameraCount(CustomQThread):

    def __init__(self, cam_preset_number=20):
        super(SearchCameraCount, self).__init__()
        self.cam_preset_number = cam_preset_number

    def run(self):
        cnt = 0
        for device in range(0, self.cam_preset_number):
            stream = cv2.VideoCapture(device)
            grabbed = stream.grab()
            stream.release()
            if not grabbed:
                break
            cnt = cnt + 1
        logger.info(f"run cnt={cnt}")
        signals_manager.available_camera_number.emit(cnt)
