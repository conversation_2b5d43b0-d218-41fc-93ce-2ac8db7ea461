from PyQt5.QtCore import QObject, pyqtSignal

from common.modbus.RTUClient import RTUClient


class ZQWLIO(QObject):

    state_signal = pyqtSignal(bool)

    def __init__(self):
        super().__init__()
        self.client: RTUClient = None

    def connect(self, port, baud_rate=115200, bytesize=8, parity="N", stop_bits=1, slave_id=1, timeout=2.0):
        try:
            self.client = RTUClient(port=port, baud_rate=baud_rate, bytesize=bytesize,
                                    parity=parity, stop_bits=stop_bits, slave_id=slave_id,
                                    timeout=timeout)
            self.client.open()
            self.state_signal.emit(self.is_open())
            return self.is_open()
        except Exception as e:
            print(e.args)
            return False

    def disconnect(self):
        if self.is_open():
            self.client.close()
            self.state_signal.emit(False)

    def is_open(self):
        if self.client is None:
            return False
        else:
            return self.client.is_open()

    def read_output_status(self):
        """
        读取输出信号量
        :return:
        """
        starting_address = 0
        quantity_of_x = 4
        r = self.client.read_coil_many(starting_address, quantity_of_x)
        return r

    def read_input_status(self):
        """
        读取输入信号量
        :return:
        """
        starting_address = 0
        quantity_of_x = 4
        r = self.client.read_discrete_input_many(starting_address, quantity_of_x)
        return r

    def write_output_status(self, position, value):
        starting_address = position
        value = value
        r = self.client.write_coil_one(starting_address, value)
        return r

    def power_on(self, position):
        """
        上电
        :param position:
        :return:
        """
        print("power on")
        r = self.write_output_status(position, 1)
        return r

    def power_off(self, position):
        """
        断电
        :param position:
        :return:
        """
        print("power off")
        r = self.write_output_status(position, 0)
        return r
