import numpy as np
from scipy.spatial.transform import Rotation as R
from scipy.optimize import least_squares

# 定义两组观察到的3D点

def fun(points1,points2):
   # 计算两组点的质心
   centroid1 = np.mean(points1, axis=0)
   centroid2 = np.mean(points2, axis=0)

   # 重新中心化点
   points1_centered = points1 - centroid1
   points2_centered = points2 - centroid2

   # 计算旋转矩阵
   U, S, Vt = np.linalg.svd(np.dot(points2_centered.T, points1_centered))
   R = np.dot(U, Vt)

   # 为了保证我们得到的是一个正确的旋转矩阵，需要检查并确保行列式为1
   if np.linalg.det(R) < 0:
      Vt[-1, :] *= -1
      R = np.dot(U, Vt)

   # 计算平移向量
   t = -R.dot(centroid1) + centroid2

   # 输出结果
   print("旋转矩阵 R:")
   print(R)
   print("平移向量 t:")
   print(t)

   # 验证变换
   points1_transformed = np.dot(R, points1.T).T + t
   print("变换后的点应该与第二组点非常接近:")
   print(points1_transformed)
   print("第二组点:")
   print(points2)

   # 可以通过计算误差来评估变换的质量
   error = np.linalg.norm(points1_transformed - points2)
   print("误差:", error)


import numpy as np
import cv2

def get_3d_transformation_matrix(src_points, dst_points):
   assert len(src_points) == len(dst_points) and len(
      src_points) >= 3, "There should be at least 3 pairs of points, and the number of source and destination points must be the same."

   # Convert input lists to numpy arrays
   src_points = np.array(src_points)
   dst_points = np.array(dst_points)

   # Calculate centroids
   centroid_src = np.mean(src_points, axis=0)
   centroid_dst = np.mean(dst_points, axis=0)

   # Subtract centroids
   src_points_centered = src_points - centroid_src
   dst_points_centered = dst_points - centroid_dst

   # Calculate covariance matrix and perform Singular Value Decomposition (SVD)
   H = np.dot(src_points_centered.T, dst_points_centered)
   U, S, Vt = np.linalg.svd(H)

   # Calculate rotation matrix
   R = np.dot(Vt.T, U.T)

   # Ensure the rotation matrix is orthogonal (handle reflection case)
   if np.linalg.det(R) < 0:
      Vt[2, :] *= -1
      R = np.dot(Vt.T, U.T)

   # Calculate translation vector
   t = centroid_dst - np.dot(R, centroid_src)

   # Construct homogeneous transformation matrix
   transformation_matrix = np.identity(4)
   transformation_matrix[:3, :3] = R
   transformation_matrix[:3, 3] = t

   return transformation_matrix


def apply_transformation(src_points, transformation_matrix):
    # 将源点转换为齐次坐标
    src_homogeneous = np.hstack((src_points, np.ones((len(src_points), 1))))
    # 应用变换矩阵
    dst_transformed_homogeneous = np.dot(transformation_matrix, src_homogeneous.T).T
    # 将齐次坐标转换回普通坐标
    dst_transformed = dst_transformed_homogeneous[:, :3] / dst_transformed_homogeneous[:, [3]]
    return dst_transformed


def save_transformation_matrix(matrix, file_path):
    """
    Save the transformation matrix to a file.

    Parameters:
    - matrix: numpy.ndarray, the transformation matrix to save
    - file_path: str, the path including filename where the matrix will be saved
    """
    # Save the matrix using numpy's built-in save function
    np.save(file_path, matrix)


def load_transformation_matrix(file_path):
    """
    Load a transformation matrix from a file.

    Parameters:
    - file_path: str, the path including filename from where the matrix will be loaded

    Returns:
    - matrix: numpy.ndarray, the loaded transformation matrix
    """
    # Load the matrix using numpy's built-in load function
    matrix = np.load(file_path)
    return matrix

def validate_transformation(src_points, dst_points, transformation_matrix):
    # 应用变换
    transformed_points = apply_transformation(src_points, transformation_matrix)
    # 计算误差
    errors = np.linalg.norm(dst_points - transformed_points, axis=1)
    # 输出误差统计
    mean_error = np.mean(errors)
    std_dev_error = np.std(errors)
    max_error = np.max(errors)
    return mean_error, std_dev_error, max_error
src_points =[[-118.98481591215484, -135.43196109223672, -212.4838554362652], [-119.50346168345857, -98.48407810672376, -195.2744192108487], [-120.731926658634, -114.52918547250232, -183.1643777094007], [-120.77148495701492, -152.36161180693836, -200.92010820163853], [-120.55135466131864, -125.35419747651062, -198.55634222279573]]
dst_points =[[242.2894980298388, 72.07227958304966, 164.7354291196295], [276.1204420106083, 55.837689384367465, 180.92330365739653], [311.1191275510884, 85.44821108797497, 193.80638719956409], [277.0517229340168, 101.58563174247284, 177.58712926371405], [276.3925683403597, 78.76681984205122, 179.1114823046043]]

transformation_matrix = get_3d_transformation_matrix(src_points, dst_points)
# 假设你已经有了变换矩阵

# 验证变换矩阵并计算误差
mean_error, std_dev_error, max_error = validate_transformation(src_points, dst_points, transformation_matrix)

print(f"Mean Error: {mean_error}")
print(f"Standard Deviation of Error: {std_dev_error}")
print(f"Maximum Error: {max_error}")
# if __name__ == '__main__':
#    points1 = np.array([[115.76719783054583, 146.49308081639276, 175.34863603770086],
#                        [178.7479042022222, 98.61953335295017, 201.93375182406194],
#                        [224.14268857979894, 168.2329392936019, 222.7738899126662]])
#    points2 = np.array([[418.6448984568336, 232.2737153021014, 285.36479135884895],
#                        [472.855423927354, 153.50668834743087, 283.3118072483536],
#                        [487.92198615299804, 211.44353963294728, 264.8901874945369]])
#    fun(points1,points2)
