import traceback

from can import BusABC

from common.LogUtils import logger
from adb.pcan.HWMessage import HWMessage
from adb.pcan.PCANBasic import PCAN_USBBUS1, PCAN_BAUD_10K, PCAN_BAUD_20K, PCAN_BAUD_50K, PCAN_BAUD_100K, \
    PCAN_BAUD_125K, PCAN_BAUD_250K, PCAN_BAUD_500K, PCAN_BAUD_800K, PCAN_BAUD_1M, PCANBasic, PCAN_USBBUS2, \
    TPCANBitrateFD, PCAN_BUSOFF_AUTORESET, PCAN_ERROR_OK, TPCANMsg, PCAN_MESSAGE_STANDARD, TPCANMsgFD, PCAN_MESSAGE_FD

# CANFD的消息DLC和消息数据长度对应关系
CANFD_MSG_DLC = {8: 8, 12: 9, 16: 10, 20: 11, 24: 12, 32: 13, 48: 14, 64: 15}
CANFD_MSG_LEN = {8: 8, 9: 12, 10: 16, 11: 20, 12: 24, 13: 32, 14: 48, 15: 64}


class PcanBus(BusABC):

    def __init__(self, ratio, can_type):
        self.ratio = ratio
        self.can_type = can_type
        self.pcan_channel = PCAN_USBBUS1
        self.pcan_channel2 = PCAN_USBBUS2
        self.ratio_dict = {10000: PCAN_BAUD_10K, 20000: PCAN_BAUD_20K, 50000: PCAN_BAUD_50K, 100000: PCAN_BAUD_100K,
                           125000: PCAN_BAUD_125K, 250000: PCAN_BAUD_250K, 500000: PCAN_BAUD_500K,
                           800000: PCAN_BAUD_800K, 1000000: PCAN_BAUD_1M
                           }
        self.status = False
        self.pcan_basic = PCANBasic()
        self.init_device()
        self.chl_active = None

    def set_channel(self, chl):
        self.chl_active = chl

    def init_device(self):
        try:
            result = None
            self.pcan_channel = PCAN_USBBUS1
            self.pcan_channel2 = PCAN_USBBUS2
            if self.can_type == "CAN":
                # 总线初始化
                result = self.pcan_basic.Initialize(self.pcan_channel, self.ratio_dict[self.ratio])
                result1 = self.pcan_basic.Initialize(self.pcan_channel2, self.ratio_dict[self.ratio])
                print("init_device PCAN_USBBUS1: {}, PCAN_USBBUS2: {}".format(result, result1))
                logger.info("init_device PCAN-USB 总线CAN初始化")
            elif self.can_type == "CANFD":
                # 总线初始化
                bitrate = TPCANBitrateFD(
                    b"f_clock_mhz=80, nom_brp=2, nom_tseg1=63, nom_tseg2=16, nom_sjw=16, data_brp=2, data_tseg1=15, data_tseg2=4, data_sjw=4"
                )
                result = self.pcan_basic.InitializeFD(self.pcan_channel, bitrate)
                result1 = self.pcan_basic.InitializeFD(self.pcan_channel2, bitrate)
                logger.info(f"init_device PCAN-USB 总线CANFD初始化 result:{result} result1:{result1}")
            self.pcan_basic.SetValue(self.pcan_channel, PCAN_BUSOFF_AUTORESET, 1000000)
            self.pcan_basic.SetValue(self.pcan_channel2, PCAN_BUSOFF_AUTORESET, 1000000)
            if result != PCAN_ERROR_OK:
                logger.warning("init_device PCAN-USB 初始化失败: {}".format(self.pcan_basic.GetErrorText(result)))
                self.status = False
            else:
                logger.info("init_device PCAN-USB 已初始化")
                self.status = True
        except Exception as e:
            logger.error("init_device exception: {}".format(str(e.args)))

    def send(self, msg, timeout=None):
        try:
            result = None
            if self.can_type == "CAN":
                # 构建CAN消息
                msgs = TPCANMsg()
                msgs.ID = msg.arbitration_id
                msgs.MSGTYPE = PCAN_MESSAGE_STANDARD
                msgs.LEN = msg.dlc
                for j in range(msg.dlc):
                    msgs.DATA[j] = msg.data[j]
                # 写入CAN消息
                result = self.pcan_basic.Write(self.chl_active, msgs)
            elif self.can_type == "CANFD":
                # 构建CANFD消息
                msgs = TPCANMsgFD()
                msgs.ID = msg.arbitration_id
                msgs.MSGTYPE = PCAN_MESSAGE_FD
                if msg.dlc > 8:
                    msgs.DLC = CANFD_MSG_DLC[msg.dlc]
                else:
                    msgs.DLC = msg.dlc
                for j in range(msg.dlc):
                    msgs.DATA[j] = msg.data[j]
                # 写入CANFD消息
                result = self.pcan_basic.WriteFD(self.chl_active, msgs)

            if result is not None and result == PCAN_ERROR_OK:
                pass
                # logger.info(f"send CAN msg success")
            else:
                error = self.pcan_basic.GetErrorText(result)
                logger.info(f"send CAN msg fail error={error}")
        except Exception as e:
            print(str(e.args))
            logger.error(f"send CAN msg exception: {traceback.format_exc()}")

    def _recv_internal(self, timeout):
        read_result = None
        data = None
        if self.can_type == "CAN":
            read_result = self.pcan_basic.Read(self.chl_active)
        elif self.can_type == "CANFD":
            read_result = self.pcan_basic.ReadFD(self.chl_active)
        if read_result is not None and read_result[1].ID:

            if self.can_type == "CAN":
                data = read_result[1].DATA[:read_result[1].LEN]
            elif self.can_type == "CANFD":
                if read_result[1].DLC >= 8:
                    data = read_result[1].DATA[:CANFD_MSG_LEN[read_result[1].DLC]]
            if self.can_type.upper() == "CAN":
                t = read_result[2].millis / 1000
            else:
                t = read_result[2].value / 1000
            # print("_ recv_internal read_result:", data)
            if data:
                msg = HWMessage(
                    timestamp=t,
                    arbitration_id=read_result[1].ID,
                    is_extended_id=False,
                    is_remote_frame=False,
                    is_error_frame=False,
                    channel=1,
                    dlc=read_result[1].LEN if self.can_type == "CAN" else read_result[1].DLC,
                    data=data,
                    is_fd=False if self.can_type == "CAN" else True,
                    count=0
                )
                # print("massage: {}".format(msg))
                return msg, True
        return None, True

    def close_pcan_channel(self):
        logger.info("close_pcan_channel")
        try:
            status = self.pcan_basic.Uninitialize(self.pcan_channel)
            status2 = self.pcan_basic.Uninitialize(self.pcan_channel2)
            logger.info("close_pcan_channel status={} status2".format(status, status2))
        except Exception as e:
            logger.error("close_pcan_channel exception: {}".format(str(e.args)))