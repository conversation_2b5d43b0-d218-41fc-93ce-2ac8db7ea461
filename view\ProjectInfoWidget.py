from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON>er<PERSON>iew, QTableWidgetItem
from PyQt5.QtWidgets import QWidget

from common.AppConfig import app_config
from common.LogUtils import logger
from res import bold_font
from ui.UIProjectInfoDialog import Ui_ProjectDialog
from utils.ProjectManager import project_manager
from utils.SignalsManager import signals_manager


class ProjectInfoWidget(Ui_ProjectDialog, QWidget):

    def __init__(self, parent=None):
        super(ProjectInfoWidget, self).__init__(parent)
        self.setupUi(self)
        self.showMaximized()
        self.tableWidget.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.tableWidget.verticalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.tableWidget.horizontalHeader().setFixedHeight(40)
        self.tableWidget.horizontalHeader().setFont(bold_font)
        # self.tableWidget.setFrameShape(QTableWidget.NoFrame)
        self.tableWidget.setEnabled(False)
        signals_manager.update_project_info.connect(self.update_project_info)
        signals_manager.update_project_extra_info.connect(self.update_project_extra_info)

    def update_project_info(self):
        self.project_number_label.setText(project_manager.get_test_plan_project_number())
        self.project_name_label.setText(project_manager.get_test_plan_project_name())
        self.workspace_label.setText(app_config.work_folder)
        self.test_plan_label.setText(project_manager.get_test_plan_name())
        self.product_sw_label.setText(project_manager.get_test_version())
        # 筛选出当前子计划对应父计划下关联的所有子计划
        project_manager.related_plans.clear()
        for i in range(len(project_manager.project_plans)):
            plan = project_manager.project_plans[i]
            if project_manager.get_test_plan_id() == plan.get("plan_id", 0):
                project_manager.related_plans.append(plan)
        logger.info(f"update_project_info plans_size={len(project_manager.related_plans)}")
        self.tableWidget.clearContents()
        for i in range(len(project_manager.related_plans)):
            plan_name = project_manager.related_plans[i].get("name", "")
            machine_number = project_manager.related_plans[i].get("machine_number", "")
            test_cases = project_manager.related_plans[i].get("test_cases", [])
            tester_name = project_manager.related_plans[i].get("tester_name", "")
            logger.debug(f"update_project_info plan_name={plan_name}, machine_number={machine_number}")
            item = QTableWidgetItem(str(i + 1))
            item.setTextAlignment(Qt.AlignCenter)
            self.tableWidget.setItem(i, 0, item)
            item = QTableWidgetItem(plan_name)
            item.setTextAlignment(Qt.AlignCenter)
            self.tableWidget.setItem(i, 1, item)
            item = QTableWidgetItem(machine_number)
            item.setTextAlignment(Qt.AlignCenter)
            self.tableWidget.setItem(i, 2, item)
            item = QTableWidgetItem(str(len(test_cases)))
            item.setTextAlignment(Qt.AlignCenter)
            self.tableWidget.setItem(i, 3, item)
            item = QTableWidgetItem(tester_name)
            item.setTextAlignment(Qt.AlignCenter)
            self.tableWidget.setItem(i, 4, item)
            item = QTableWidgetItem("")
            item.setTextAlignment(Qt.AlignCenter)
            self.tableWidget.setItem(i, 5, item)

        # threading.Thread(target=self.get_project_extra_info).start()

    def update_project_extra_info(self, related_people):
        related_peoples = "，".join(related_people)
        for i in range(len(project_manager.related_plans)):
            self.tableWidget.item(i, 5).setText(related_peoples)
