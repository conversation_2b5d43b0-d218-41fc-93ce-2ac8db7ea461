{
    "sequence":
    [
        //点胶标定
        {
            "name":"点胶标定流程",
            "command":
            [
                /*{
                    "name":"标定拍照",
                    "type":"get_camera_frame",
                    "in":
                    {
                        "camera":"点胶相机",
                        "exposure":"10000",
                        "gain":"2.0"
                    },
                    "out":
                    {
                        "result":"标定拍照结果",
                        "image":"标定拍照图片"
                    }
                },*/
                {
                    "name":"读取标定图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "image":"标定拍照图片",
                        "result":"标定拍照结果"
                    }
                },
                {
                    "name":"生成调试文件夹",
                    "type":"creat_folder",
                    "in":
                    {
                        "path":"$点胶调试文件夹"
                    },
                    "out":
                    {
                        "result":"生成调试文件夹",
                        "full_path":"调试文件夹"
                    }
                },
                {
                    "name":"生成文件夹",
                    "type":"creat_unique_folder",
                    "in":
                    {
                        "path":"inspection/Glue",
                        "length":"3"
                    },
                    "out":
                    {
                        "result":"生成文件夹结果",
                        "full_path":"文件夹"
                    }
                },
                {
                    "name":"获取当前时间",
                    "type":"get_date_time",
                    "out":
                    {
                        "result":"获取当前时间结果",
                        "date_time":"当前时间"  
                    }
                },
                {
                    "name":"生成图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"calib_\",\"@当前时间\",\".jpg\"]"
                    },
                    "out":
                    {
                        "result":"生成图片名称结果",
                        "combine":"图片名称"
                    }
                },
                {
                    "name":"判断拍标定图片",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"跳转算法流程",
                        "condition":"@标定拍照结果"
                    }
                },
                {
                    "name":"跳转拍标定失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示拍标定失败"
                    }
                },
                {
                    "name":"跳转算法流程",
                    "type":"jump",
                    "in":
                    {
                        "tag":"保存图片"
                    }
                },
                {
                    "name":"显示拍标定失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"点胶拍标定失败",
                        "level":"error"
                    }
                },
                {
                    "name":"拍标定失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送标定失败NG结果"
                    }
                },
                {
                    "name":"保存图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@标定拍照图片",
                        "path":"@文件夹",
                        "name":"@图片名称"
                    },
                    "out":
                    {
                        "result":"保存图片结果",
                        "full_name":"保存图片路径"
                    }
                },
                {
                    "name":"生成xml名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"calib_\",\"@当前时间\",\".xml\"]"
                    },
                    "out":
                    {
                        "result":"生成xml名称结果",
                        "combine":"xml名称"
                    }
                },
                {
                    "name":"获取标定图片信息",
                    "type":"get_image_info",
                    "in":
                    {
                        "image":"@标定拍照图片"
                    },
                    "out":
                    {
                        "result":"获取标定图片信息结果",
                        "width":"获取标定图片信息宽",
                        "height":"获取标定图片信息高",
                        "channel":"获取标定图片信息通道"
                    }
                },
                {
                    "name":"blob检测",
                    "type":"blob_detect",
                    "in":
                    {
                        "image":"@标定拍照图片",
                        "min_threshold":"10",
                        "max_threshold":"200",
                        "min_dist":"0",
                        "filter_by_area":"1",
                        "min_area":"5000",
                        "max_area":"10000",
                        "filter_by_circularity":"1",
                        "min_circularity":"0.5",
                        "max_circularity":"1",
                        "filter_by_inertia":"0",
                        "min_inertia":"0",
                        "max_inertia":"1",
                        "filter_by_convexity":"0",
                        "min_convexity":"0",
                        "max_convexity":"1"
                    },
                    "out":
                    {
                        "result":"blob检测结果",
                        "x":"blob检测x",
                        "y":"blob检测y",
                        "radius":"blob检测半径"
                    }
                },
                {
                    "name":"blob排序",
                    "type":"sort_circle",
                    "in":
                    {
                        "x":"@blob检测x",
                        "y":"@blob检测y",
                        "radius":"@blob检测半径",
                        "size":"4",
                        "perfect_radius":"55",
                        "radius_dif_min":"0",
                        "radius_dif_max":"15",
                        "clockwise":"-1",
                        "start_angle":"-90"
                    },
                    "out":
                    {
                        "result":"blob排序结果",
                        "x_out":"blob排序x",
                        "y_out":"blob排序y",
                        "radius_out":"blob排序半径"
                    }
                },
                {
                    "name":"判断blob排序结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"半径转为直径",
                        "condition":"@blob排序结果"
                    }
                },
                {
                    "name":"跳转标定失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示标定失败"
                    }
                },
                {
                    "name":"半径转为直径",
                    "type":"vfloat_mul",
                    "in":
                    {
                        "vfloat_in":"@blob排序半径",
                        "mul":"2"
                    },
                    "out":
                    {
                        "result":"半径转为直径结果",
                        "vfloat_out":"blob排序直径"
                    }
                },
                {
                    "name":"获取第一点x",
                    "type":"get_float",
                    "in":
                    {
                        "vfloat":"@blob排序x",
                        "index":"0"
                    },
                    "out":
                    {
                        "result":"获取第一点x结果",
                        "float":"第一点x"
                    }
                },
                {
                    "name":"获取第二点x",
                    "type":"get_float",
                    "in":
                    {
                        "vfloat":"@blob排序x",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取第二点x结果",
                        "float":"第二点x"
                    }
                },
                {
                    "name":"获取第三点x",
                    "type":"get_float",
                    "in":
                    {
                        "vfloat":"@blob排序x",
                        "index":"2"
                    },
                    "out":
                    {
                        "result":"获取第三点x结果",
                        "float":"第三点x"
                    }
                },
                {
                    "name":"获取第四点x",
                    "type":"get_float",
                    "in":
                    {
                        "vfloat":"@blob排序x",
                        "index":"3"
                    },
                    "out":
                    {
                        "result":"获取第四点x结果",
                        "float":"第四点x"
                    }
                },
                {
                    "name":"获取第一点y",
                    "type":"get_float",
                    "in":
                    {
                        "vfloat":"@blob排序y",
                        "index":"0"
                    },
                    "out":
                    {
                        "result":"获取第一点y结果",
                        "float":"第一点y"
                    }
                },
                {
                    "name":"获取第二点y",
                    "type":"get_float",
                    "in":
                    {
                        "vfloat":"@blob排序y",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取第二点y结果",
                        "float":"第二点y"
                    }
                },
                {
                    "name":"获取第三点y",
                    "type":"get_float",
                    "in":
                    {
                        "vfloat":"@blob排序y",
                        "index":"2"
                    },
                    "out":
                    {
                        "result":"获取第三点y结果",
                        "float":"第三点y"
                    }
                },
                {
                    "name":"获取第四点y",
                    "type":"get_float",
                    "in":
                    {
                        "vfloat":"@blob排序y",
                        "index":"3"
                    },
                    "out":
                    {
                        "result":"获取第四点y结果",
                        "float":"第四点y"
                    }
                },
                {
                    "name":"获取第一点直径",
                    "type":"get_float",
                    "in":
                    {
                        "vfloat":"@blob排序直径",
                        "index":"0"
                    },
                    "out":
                    {
                        "result":"获取第一点直径结果",
                        "float":"第一点直径"
                    }
                },
                {
                    "name":"获取第二点直径",
                    "type":"get_float",
                    "in":
                    {
                        "vfloat":"@blob排序直径",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取第二点直径结果",
                        "float":"第二点直径"
                    }
                },
                {
                    "name":"获取第三点直径",
                    "type":"get_float",
                    "in":
                    {
                        "vfloat":"@blob排序直径",
                        "index":"2"
                    },
                    "out":
                    {
                        "result":"获取第三点直径结果",
                        "float":"第三点直径"
                    }
                },
                {
                    "name":"获取第四点直径",
                    "type":"get_float",
                    "in":
                    {
                        "vfloat":"@blob排序直径",
                        "index":"3"
                    },
                    "out":
                    {
                        "result":"获取第四点直径结果",
                        "float":"第四点直径"
                    }
                },
                {
                    "name":"计算第一圆矩形",
                    "type":"bounding_box",
                    "in":
                    {
                        "x":"@第一点x",
                        "y":"@第一点y",
                        "w":"@第一点直径",
                        "h":"@第一点直径",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"计算第一圆矩形结果",
                        "xmin":"第一圆矩形xmin",
                        "ymin":"第一圆矩形ymin",
                        "xmax":"第一圆矩形xmax",
                        "ymax":"第一圆矩形ymax"
                    }
                },
                {
                    "name":"计算第二圆矩形",
                    "type":"bounding_box",
                    "in":
                    {
                        "x":"@第二点x",
                        "y":"@第二点y",
                        "w":"@第二点直径",
                        "h":"@第二点直径",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"计算第二圆矩形结果",
                        "xmin":"第二圆矩形xmin",
                        "ymin":"第二圆矩形ymin",
                        "xmax":"第二圆矩形xmax",
                        "ymax":"第二圆矩形ymax"
                    }
                },
                {
                    "name":"计算第三圆矩形",
                    "type":"bounding_box",
                    "in":
                    {
                        "x":"@第三点x",
                        "y":"@第三点y",
                        "w":"@第三点直径",
                        "h":"@第三点直径",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"计算第三圆矩形结果",
                        "xmin":"第三圆矩形xmin",
                        "ymin":"第三圆矩形ymin",
                        "xmax":"第三圆矩形xmax",
                        "ymax":"第三圆矩形ymax"
                    }
                },
                {
                    "name":"计算第四圆矩形",
                    "type":"bounding_box",
                    "in":
                    {
                        "x":"@第四点x",
                        "y":"@第四点y",
                        "w":"@第四点直径",
                        "h":"@第四点直径",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"计算第四圆矩形结果",
                        "xmin":"第四圆矩形xmin",
                        "ymin":"第四圆矩形ymin",
                        "xmax":"第四圆矩形xmax",
                        "ymax":"第四圆矩形ymax"
                    }
                },
                {
                    "name":"保存XML",
                    "type":"save_xml",
                    "in":
                    {
                        "path":"@文件夹",
                        "name":"@xml名称",
                        "product":"mate3.0",
                        "sn":"NULL",
                        "station":"Glue",
                        "mac":"NULL",
                        "detection_type":"glue",
                        "result":"1",
                        "folder":"@文件夹",
                        "image_name":"@图片名称",
                        "width":"@获取标定图片信息宽",
                        "height":"@获取标定图片信息高",
                        "depth":"@获取标定图片信息通道",
                        "name_b":"[\"blob1\",\"blob2\",\"blob3\",\"blob4\"]",
                        "label":"[\"1\",\"1\",\"1\",\"1\"]",
                        "standard":"[\"1\",\"1\",\"1\",\"1\"]",
                        "xmin":"[\"@第一圆矩形xmin\",\"@第二圆矩形xmin\",\"@第三圆矩形xmin\",\"@第四圆矩形xmin\"]",
                        "ymin":"[\"@第一圆矩形ymin\",\"@第二圆矩形ymin\",\"@第三圆矩形ymin\",\"@第四圆矩形ymin\"]",
                        "xmax":"[\"@第一圆矩形xmax\",\"@第二圆矩形xmax\",\"@第三圆矩形xmax\",\"@第四圆矩形xmax\"]",
                        "ymax":"[\"@第一圆矩形ymax\",\"@第二圆矩形ymax\",\"@第三圆矩形ymax\",\"@第四圆矩形ymax\"]"
                    },
                    "out":
                    {
                        "result":"保存XML结果",
                        "full_name":"保存XML全称"
                    }
                },
                {
                    "name":"画第一圆",
                    "type":"draw_ellipse",
                    "in":
                    {
                        "image":"@标定拍照图片",
                        "x":"@第一点x",
                        "y":"@第一点y",
                        "a":"@第一点直径",
                        "b":"@第一点直径",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"画第一圆结果",
                        "image":"画第一圆图片"
                    }
                },
                {
                    "name":"画第二圆",
                    "type":"draw_ellipse",
                    "in":
                    {
                        "image":"@画第一圆图片",
                        "x":"@第二点x",
                        "y":"@第二点y",
                        "a":"@第二点直径",
                        "b":"@第二点直径",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"画第二圆结果",
                        "image":"画第二圆图片"
                    }
                },
                {
                    "name":"画第三圆",
                    "type":"draw_ellipse",
                    "in":
                    {
                        "image":"@画第二圆图片",
                        "x":"@第三点x",
                        "y":"@第三点y",
                        "a":"@第三点直径",
                        "b":"@第三点直径",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"画第三圆结果",
                        "image":"画第三圆图片"
                    }
                },
                {
                    "name":"画第四圆",
                    "type":"draw_ellipse",
                    "in":
                    {
                        "image":"@画第三圆图片",
                        "x":"@第四点x",
                        "y":"@第四点y",
                        "a":"@第四点直径",
                        "b":"@第四点直径",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"画第四圆结果",
                        "image":"画第四圆图片"
                    }
                },
                {
                    "name":"保存调试图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@画第四圆图片",
                        "path":"$点胶调试文件夹",
                        "name":"calib_blob.jpg"
                    },
                    "out":
                    {
                        "result":"保存调试图片结果",
                        "full_name":"保存调试图片路径"
                    }
                },
                {
                    "name":"计算标定矩阵",
                    "type":"estimate_affine2D",
                    "in":
                    {
                        "from_point_x":"@blob排序x",
                        "from_point_y":"@blob排序y",
                        "to_point_x":"[\"369.813\",\"349.813\",\"349.813\",\"369.813\"]",
                        "to_point_y":"[\"26.624\",\"26.624\",\"46.624\",\"46.624\"]"
                    },
                    "out":
                    {
                        "result":"计算标定矩阵结果",
                        "A":"计算标定矩阵A",
                        "B":"计算标定矩阵B",
                        "C":"计算标定矩阵C",
                        "D":"计算标定矩阵D",
                        "E":"计算标定矩阵E",
                        "F":"计算标定矩阵F"
                    }
                },
                {
                    "name":"判断标定结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"发送标定A",
                        "condition":"@计算标定矩阵结果"
                    }
                },
                {
                    "name":"显示标定失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"标定矩阵计算错误",
                        "level":"error"
                    }
                },
                {
                    "name":"发送标定失败NG结果",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "master":"点胶控制卡",
                        "offset":"22",
                        "type":"short",
                        "content":"2"
                    }
                },
                {
                    "name":"结束标定失败",
                    "type":"end"
                },
                {
                    "name":"显示blob失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"点胶标定无法找到MARK点",
                        "level":"error"
                    }
                },
                {
                    "name":"发送blob失败NG结果",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "master":"点胶控制卡",
                        "offset":"22",
                        "type":"short",
                        "content":"2"
                    }
                },
                {
                    "name":"结束blob失败NG",
                    "type":"end"
                },
                {
                    "name":"发送标定A",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "master":"点胶控制卡",
                        "offset":"80",
                        "type":"float",
                        "content":"@计算标定矩阵A"
                    },
                    "out":
                    {
                        "result":"发送标定A结果"
                    }
                },
                {
                    "name":"发送标定B",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "wait":"@发送标定A结果",
                        "master":"点胶控制卡",
                        "offset":"82",
                        "type":"float",
                        "content":"@计算标定矩阵B"
                    },
                    "out":
                    {
                        "result":"发送标定B结果"
                    }
                },
                {
                    "name":"发送标定C",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "wait":"@发送标定B结果",
                        "master":"点胶控制卡",
                        "offset":"84",
                        "type":"float",
                        "content":"@计算标定矩阵C"
                    },
                    "out":
                    {
                        "result":"发送标定C结果"
                    }
                },
                {
                    "name":"发送标定D",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "wait":"@发送标定C结果",
                        "master":"点胶控制卡",
                        "offset":"86",
                        "type":"float",
                        "content":"@计算标定矩阵D"
                    },
                    "out":
                    {
                        "result":"发送标定D结果"
                    }
                },
                {
                    "name":"发送标定E",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "wait":"@发送标定D结果",
                        "master":"点胶控制卡",
                        "offset":"88",
                        "type":"float",
                        "content":"@计算标定矩阵E"
                    },
                    "out":
                    {
                        "result":"发送标定E结果"
                    }
                },
                {
                    "name":"发送标定F",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "wait":"@发送标定E结果",
                        "master":"点胶控制卡",
                        "offset":"90",
                        "type":"float",
                        "content":"@计算标定矩阵F"
                    },
                    "out":
                    {
                        "result":"发送标定F结果"
                    }
                },
                {
                    "name":"延时1",
                    "type":"sleep",
                    "in":
                    {
                        "wait":"@发送标定F结果",
                        "time":"50"
                    },
                    "out":
                    {
                        "result":"延时1结果"
                    }
                },
                {
                    "name":"发送点胶标定OK结果",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "wait":"@延时1结果",
                        "master":"点胶控制卡",
                        "offset":"22",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        //点胶
        {
            "name":"左点胶流程",
            "command":
            [
                {
                    "name":"发送点胶OK结果",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "master":"点胶控制卡",
                        "offset":"37",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"右点胶流程",
            "command":
            [
                /*{
                    "name":"后壳拍照",
                    "type":"get_camera_frame",
                    "in":
                    {
                        "camera":"点胶相机",
                        "exposure":"50000",
                        "gain":"2.0"
                    },
                    "out":
                    {
                        "result":"后壳拍照结果",
                        "image":"后壳拍照图片"
                    }
                },*/
                {
                    "name":"读取后壳图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "image":"后壳拍照图片",
                        "result":"后壳拍照结果"
                    }
                },
                {
                    "name":"获取当前时间",
                    "type":"get_date_time",
                    "out":
                    {
                        "result":"获取当前时间结果",
                        "date_time":"当前时间"  
                    }
                    
                },
                {
                    "name":"生成图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"glue_\",\"@当前时间\",\"_\",\"$右SN码\",\".jpg\"]"
                    },
                    "out":
                    {
                        "result":"生成后壳名称结果",
                        "combine":"图片名称"
                    }
                },
                {
                    "name":"生成xml名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"glue_\",\"@当前时间\",\"_\",\"$右SN码\",\".xml\"]"
                    },
                    "out":
                    {
                        "result":"生成xml名称结果",
                        "combine":"生成xml名称"
                    }
                },
                {
                    "name":"获取后壳图片信息",
                    "type":"get_image_info",
                    "in":
                    {
                        "image":"@后壳拍照图片"
                    },
                    "out":
                    {
                        "result":"获取后壳图片信息结果",
                        "width":"获取后壳图片信息宽",
                        "height":"获取后壳图片信息高",
                        "channel":"获取后壳图片信息通道"
                    }
                },
                {
                    "name":"保存后壳图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳拍照图片",
                        "path":"$右图片文件夹",
                        "name":"@图片名称"
                    },
                    "out":
                    {
                        "result":"保存图片结果",
                        "full_name":"图片路径"
                    }
                },
                {
                    "name":"判断拍后壳结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"跳转算法流程",
                        "condition":"@后壳拍照结果"
                    }
                },
                {
                    "name":"跳转拍后壳失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示拍后壳失败"
                    }
                },
                {
                    "name":"跳转算法流程",
                    "type":"jump",
                    "in":
                    {
                        "tag":"后壳向下采样"
                    }
                },
                {
                    "name":"显示拍后壳失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右点胶拍后壳失败",
                        "level":"error"
                    }
                },
                {
                    "name":"拍后壳失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送点胶NG结果"
                    }
                },
                {
                    "name":"后壳向下采样",
                    "type":"pyramid",
                    "in":
                    {
                        "image":"@后壳拍照图片",
                        "type":"down",
                        "count":"2"
                    },
                    "out":
                    {
                        "result":"后壳向下采样结果",
                        "image_result":"后壳向下采样图片"
                    }
                },
                {
                    "name":"后壳找圆",
                    "type":"asls_ellipse",
                    "in":
                    {
                        "image":"@后壳向下采样图片",
                        "angle_tolerance":"9.0",
                        "ang_th":"45.0",
                        "density_th":"0.6",
                        "edge_process_select":"1",
                        "inner_unit_dis_tolerance":"1",
                        "log_eps":"0.0",
                        "min_num_pnts":"100",
                        "max_raid":"400",
                        "min_raid":"300",
                        "ab_ratio":"0.8",
                        "n_bins":"2048",
                        "quant":"2.0",
                        "scale":"0.8",
                        "sigma_scale":"0.1",
                        "specified_polarity":"-1",
                        "support_angle_tolerance":"15",
                        "Tac":"90",
                        "Tmin":"0.5",
                        "Tr":"0.1"
                    },
                    "out":
                    {
                        "result":"后壳找圆结果",
                        "x":"circles_X",
                        "y":"circles_Y",
                        "a":"circles_A",
                        "b":"circles_B",
                        "angle":"circles_ANGLE"
                    }
                },
                {
                    "name":"后壳筛选圆",
                    "type":"filter_ellipse",
                    "in":
                    {
                        "x":"@circles_X",
                        "y":"@circles_Y",
                        "a":"@circles_A",
                        "b":"@circles_B",
                        "angle":"@circles_ANGLE",
                        "perfect_diam":"660",
                        "diam_dif_min":"0",
                        "diam_dif_max":"20",
                        "diam_dif_coffee":"1.0",
                        "perfect_x":"0",
                        "perfect_y":"0",
                        "distance_min":"0",
                        "distance_max":"0",
                        "distance_coffee":"0.0"
                    },
                    "out":
                    {
                        "result":"后壳筛选圆结果",
                        "x_out":"circle_X",
                        "y_out":"circle_Y",
                        "a_out":"circle_A",
                        "b_out":"circle_B",
                        "angle_out":"circle_ANGLE"
                    }
                },
                {
                    "name":"判断拍找圆结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"画筛选圆",
                        "condition":"@后壳筛选圆结果"
                    }
                },
                {
                    "name":"显示找圆失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右点胶后壳找圆失败",
                        "level":"error"
                    }
                },
                {
                    "name":"找圆失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送点胶NG结果"
                    }
                },
                {
                    "name":"画筛选圆",
                    "type":"draw_ellipse",
                    "in":
                    {
                        "image":"@后壳向下采样图片",
                        "x":"@circle_X",
                        "y":"@circle_Y",
                        "a":"@circle_A",
                        "b":"@circle_B",
                        "angle":"@circle_ANGLE"
                    },
                    "out":
                    {
                        "result":"画筛选圆结果",
                        "image":"画筛选圆图片"
                    }
                },
                {
                    "name":"保存筛选圆图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@画筛选圆图片",
                        "path":"$右调试文件夹",
                        "name":"glue_ellipse.jpg"
                    },
                    "out":
                    {
                        "result":"保存筛选圆图片结果",
                        "full_name":"保存筛选圆图片路径"
                    }
                },
                {
                    "name":"计算椭圆矩形",
                    "type":"bounding_box",
                    "in":
                    {
                        "x":"@circle_X",
                        "y":"@circle_Y",
                        "w":"@circle_A",
                        "h":"@circle_B",
                        "angle":"@circle_ANGLE"
                    },
                    "out":
                    {
                        "result":"计算椭圆矩形结果",
                        "xmin":"椭圆矩形xmin",
                        "ymin":"椭圆矩形ymin",
                        "xmax":"椭圆矩形xmax",
                        "ymax":"椭圆矩形ymax"
                    }
                },
                {
                    "name":"椭圆矩形还原",
                    "type":"bounding_box_scale",
                    "in":
                    {
                        "xmin_in":"@椭圆矩形xmin",
                        "ymin_in":"@椭圆矩形ymin",
                        "xmax_in":"@椭圆矩形xmax",
                        "ymax_in":"@椭圆矩形ymax",
                        "scale":"4"
                    },
                    "out":
                    {
                        "result":"椭圆矩形还原",
                        "xmin_out":"椭圆矩形还原xmin",
                        "ymin_out":"椭圆矩形还原ymin",
                        "xmax_out":"椭圆矩形还原xmax",
                        "ymax_out":"椭圆矩形还原ymax"
                    }
                },
                {
                    "name":"保存XML",
                    "type":"save_xml",
                    "in":
                    {
                        "path":"$右图片文件夹",
                        "name":"@生成xml名称",
                        "product":"mate3.0",
                        "sn":"$右SN码",
                        "station":"Glue",
                        "mac":"NULL",
                        "detection_type":"back",
                        "result":"1",
                        "folder":"$右图片文件夹",
                        "image_name":"@图片名称",
                        "width":"@获取后壳图片信息宽",
                        "height":"@获取后壳图片信息高",
                        "depth":"@获取后壳图片信息通道",
                        "name_b":"[\"Ellipse\"]",
                        "label":"[\"1\"]",
                        "standard":"[\"1\"]",
                        "xmin":"[\"@椭圆矩形还原xmin\"]",
                        "ymin":"[\"@椭圆矩形还原ymin\"]",
                        "xmax":"[\"@椭圆矩形还原xmax\"]",
                        "ymax":"[\"@椭圆矩形还原ymax\"]"
                    },
                    "out":
                    {
                        "result":"保存XML结果",
                        "full_name":"保存XML全称"
                    }
                },
                {
                    "name":"推送计算像素圆心x",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@circle_X\",\"*4\"]"
                    },
                    "out":
                    {
                        "result":"推送计算像素圆心x结果",
                        "combine":"推送计算像素圆心x"
                    }
                },
                {
                    "name":"计算像素圆心x",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算像素圆心x" 
                    },
                    "out":
                    {
                        "result":"计算像素圆心x结果",
                        "value":"像素圆心x"
                    }
                },
                {
                    "name":"推送计算像素圆心y",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@circle_Y\",\"*4\"]"
                    },
                    "out":
                    {
                        "result":"推送计算像素圆心y结果",
                        "combine":"推送计算像素圆心y"
                    }
                },
                {
                    "name":"计算像素圆心y",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算像素圆心y" 
                    },
                    "out":
                    {
                        "result":"计算像素圆心y结果",
                        "value":"像素圆心y"
                    }
                },
                {
                    "name":"推送计算像素半径",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@circle_A\",\"+\",\"@circle_B\",\"+\",\"$右点胶半径偏移量\"]"
                    },
                    "out":
                    {
                        "result":"推送计算像素半径结果",
                        "combine":"推送计算像素半径"
                    }
                },
                {
                    "name":"计算像素半径",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算像素半径" 
                    },
                    "out":
                    {
                        "result":"计算像素半径结果",
                        "value":"像素半径"
                    }
                },
                {
                    "name":"推送计算像素起点x",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@像素圆心x\",\"+\",\"@像素半径\",\"*cos(\",\"$右点胶起点角度\",\")\"]"
                    },
                    "out":
                    {
                        "result":"推送计算像素起点x结果",
                        "combine":"推送计算像素起点x"
                    }
                },
                {
                    "name":"计算像素起点x",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算像素起点x" 
                    },
                    "out":
                    {
                        "result":"计算像素起点x结果",
                        "value":"像素起点x"
                    }
                },
                {
                    "name":"推送计算像素起点y",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@像素圆心y\",\"+\",\"@像素半径\",\"*sin(\",\"$右点胶起点角度\",\")\"]"
                    },
                    "out":
                    {
                        "result":"推送计算像素起点y结果",
                        "combine":"推送计算像素起点y"
                    }
                },
                {
                    "name":"计算像素起点y",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算像素起点y" 
                    },
                    "out":
                    {
                        "result":"计算像素起点y结果",
                        "value":"像素起点y"
                    }
                },
                {
                    "name":"推送计算像素终点x",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@像素圆心x\",\"+\",\"@像素半径\",\"*cos(\",\"$右点胶终点角度\",\")\"]"
                    },
                    "out":
                    {
                        "result":"推送计算像素终点x结果",
                        "combine":"推送计算像素终点x"
                    }
                },
                {
                    "name":"计算像素终点x",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算像素终点x" 
                    },
                    "out":
                    {
                        "result":"计算像素终点x结果",
                        "value":"像素终点x"
                    }
                },
                {
                    "name":"推送计算像素终点y",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@像素圆心y\",\"+\",\"@像素半径\",\"*sin(\",\"$右点胶终点角度\",\")\"]"
                    },
                    "out":
                    {
                        "result":"推送计算像素终点y结果",
                        "combine":"推送计算像素终点y"
                    }
                },
                {
                    "name":"计算像素终点y",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算像素终点y" 
                    },
                    "out":
                    {
                        "result":"计算像素终点y结果",
                        "value":"像素终点y"
                    }
                },
                {
                    "name":"推送计算标定A",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"0.0000465725619774002\"]"
                    },
                    "out":
                    {
                        "result":"推送计算标定A结果",
                        "combine":"推送计算标定A"
                    }
                },
                {
                    "name":"计算标定A",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算标定A" 
                    },
                    "out":
                    {
                        "result":"计算标定A",
                        "value":"标定A"
                    }
                },
                {
                    "name":"推送计算标定B",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"-0.0229918147287836\"]"
                    },
                    "out":
                    {
                        "result":"推送计算标定B结果",
                        "combine":"推送计算标定B"
                    }
                },
                {
                    "name":"计算标定B",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算标定B" 
                    },
                    "out":
                    {
                        "result":"计算标定B",
                        "value":"标定B"
                    }
                },
                {
                    "name":"推送计算标定C",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"401.835994319612\"]"
                    },
                    "out":
                    {
                        "result":"推送计算标定C结果",
                        "combine":"推送计算标定C"
                    }
                },
                {
                    "name":"计算标定C",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算标定C" 
                    },
                    "out":
                    {
                        "result":"计算标定C",
                        "value":"标定C"
                    }
                },
                {
                    "name":"推送计算标定D",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"0.0229401262388154\"]"
                    },
                    "out":
                    {
                        "result":"推送计算标定D结果",
                        "combine":"推送计算标定D"
                    }
                },
                {
                    "name":"计算标定D",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算标定D" 
                    },
                    "out":
                    {
                        "result":"计算标定D",
                        "value":"标定D"
                    }
                },
                {
                    "name":"推送计算标定E",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"0.0000295813063613434\"]"
                    },
                    "out":
                    {
                        "result":"推送计算标定E结果",
                        "combine":"推送计算标定E"
                    }
                },
                {
                    "name":"计算标定E",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算标定E" 
                    },
                    "out":
                    {
                        "result":"计算标定E",
                        "value":"标定E"
                    }
                },
                {
                    "name":"推送计算标定F",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"-26.7483825016704\"]"
                    },
                    "out":
                    {
                        "result":"推送计算标定F结果",
                        "combine":"推送计算标定F"
                    }
                },
                {
                    "name":"计算标定F",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算标定F" 
                    },
                    "out":
                    {
                        "result":"计算标定F",
                        "value":"标定F"
                    }
                },
                {
                    "name":"推送计算实际圆心X",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@像素圆心x\",\"*\",\"@标定A\",\"+\",\"@像素圆心y\",\"*\",\"@标定B\",\"+\",\"@标定C\",\"-305.769+\",\"$右点胶偏移量X\"]"
                    },
                    "out":
                    {
                        "result":"推送计算实际圆心X结果",
                        "combine":"推送计算实际圆心X"
                    }
                },
                {
                    "name":"计算实际圆心X",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算实际圆心X" 
                    },
                    "out":
                    {
                        "result":"计算实际圆心X",
                        "value":"实际圆心X"
                    }
                },
                {
                    "name":"推送计算实际圆心Y",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@像素圆心x\",\"*\",\"@标定D\",\"+\",\"@像素圆心y\",\"*\",\"@标定E\",\"+\",\"@标定F\",\"-25.689+\",\"$右点胶偏移量Y\"]"
                    },
                    "out":
                    {
                        "result":"推送计算实际圆心Y结果",
                        "combine":"推送计算实际圆心Y"
                    }
                },
                {
                    "name":"计算实际圆心Y",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算实际圆心Y" 
                    },
                    "out":
                    {
                        "result":"计算实际圆心Y",
                        "value":"实际圆心Y"
                    }
                },
                {
                    "name":"推送计算实际起点X",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@像素起点x\",\"*\",\"@标定A\",\"+\",\"@像素起点y\",\"*\",\"@标定B\",\"+\",\"@标定C\",\"-305.769+\",\"$右点胶偏移量X\"]"
                    },
                    "out":
                    {
                        "result":"推送计算实际起点X结果",
                        "combine":"推送计算实际起点X"
                    }
                },
                {
                    "name":"计算实际起点X",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算实际起点X" 
                    },
                    "out":
                    {
                        "result":"计算实际起点X",
                        "value":"实际起点X"
                    }
                },
                {
                    "name":"推送计算实际起点Y",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@像素起点x\",\"*\",\"@标定D\",\"+\",\"@像素起点y\",\"*\",\"@标定E\",\"+\",\"@标定F\",\"-25.689+\",\"$右点胶偏移量Y\"]"
                    },
                    "out":
                    {
                        "result":"推送计算实际起点Y结果",
                        "combine":"推送计算实际起点Y"
                    }
                },
                {
                    "name":"计算实际起点Y",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算实际起点Y" 
                    },
                    "out":
                    {
                        "result":"计算实际起点Y",
                        "value":"实际起点Y"
                    }
                },
                {
                    "name":"推送计算实际终点X",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@像素终点x\",\"*\",\"@标定A\",\"+\",\"@像素终点y\",\"*\",\"@标定B\",\"+\",\"@标定C\",\"-305.769+\",\"$右点胶偏移量X\"]"
                    },
                    "out":
                    {
                        "result":"推送计算实际终点X结果",
                        "combine":"推送计算实际终点X"
                    }
                },
                {
                    "name":"计算实际终点X",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算实际终点X" 
                    },
                    "out":
                    {
                        "result":"计算实际终点X",
                        "value":"实际终点X"
                    }
                },
                {
                    "name":"推送计算实际终点Y",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@像素终点x\",\"*\",\"@标定D\",\"+\",\"@像素终点y\",\"*\",\"@标定E\",\"+\",\"@标定F\",\"-25.689+\",\"$右点胶偏移量Y\"]"
                    },
                    "out":
                    {
                        "result":"推送计算实际终点Y结果",
                        "combine":"推送计算实际终点Y"
                    }
                },
                {
                    "name":"计算实际终点Y",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算实际终点Y" 
                    },
                    "out":
                    {
                        "result":"计算实际终点Y",
                        "value":"实际终点Y"
                    }
                },
                {
                    "name":"推送计算实际圆心偏移量X",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@实际圆心X\",\"-\",\"@实际起点X\"]"
                    },
                    "out":
                    {
                        "result":"推送计算实际圆心偏移量X结果",
                        "combine":"推送计算实际圆心偏移量X"
                    }
                },
                {
                    "name":"计算实际圆心偏移量X",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算实际圆心偏移量X" 
                    },
                    "out":
                    {
                        "result":"计算实际圆心偏移量X",
                        "value":"实际圆心偏移量X"
                    }
                },
                {
                    "name":"推送计算实际圆心偏移量Y",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@实际圆心Y\",\"-\",\"@实际起点Y\"]"
                    },
                    "out":
                    {
                        "result":"推送计算实际圆心偏移量Y结果",
                        "combine":"推送计算实际圆心偏移量Y"
                    }
                },
                {
                    "name":"计算实际圆心偏移量Y",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算实际圆心偏移量Y" 
                    },
                    "out":
                    {
                        "result":"计算实际圆心偏移量Y",
                        "value":"实际圆心偏移量Y"
                    }
                },
                {
                    "name":"推送计算实际终点偏移量X",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@实际终点X\",\"-\",\"@实际起点X\"]"
                    },
                    "out":
                    {
                        "result":"推送计算实际终点偏移量X结果",
                        "combine":"推送计算实际终点偏移量X"
                    }
                },
                {
                    "name":"计算实际终点偏移量X",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算实际终点偏移量X" 
                    },
                    "out":
                    {
                        "result":"计算实际终点偏移量X",
                        "value":"实际终点偏移量X"
                    }
                },
                {
                    "name":"推送计算实际终点偏移量Y",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@实际终点Y\",\"-\",\"@实际起点Y\"]"
                    },
                    "out":
                    {
                        "result":"推送计算实际终点偏移量Y结果",
                        "combine":"推送计算实际终点偏移量Y"
                    }
                },
                {
                    "name":"计算实际终点偏移量Y",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算实际终点偏移量Y" 
                    },
                    "out":
                    {
                        "result":"计算实际终点偏移量Y",
                        "value":"实际终点偏移量Y"
                    }
                },
                {
                    "name":"判断计算实际圆心偏移量X",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"判断计算实际圆心偏移量Y",
                        "condition":"@计算实际圆心偏移量X"
                    }
                },
                {
                    "name":"跳转计算NG流程1",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示计算失败"
                    }
                },
                {
                    "name":"判断计算实际圆心偏移量Y",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"判断计算实际终点偏移量X",
                        "condition":"@计算实际圆心偏移量Y"
                    }
                },
                {
                    "name":"跳转计算NG流程2",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示计算失败"
                    }
                },
                {
                    "name":"判断计算实际终点偏移量X",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"判断计算实际终点偏移量Y",
                        "condition":"@计算实际终点偏移量X"
                    }
                },
                {
                    "name":"跳转计算NG流程3",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示计算失败"
                    }
                },
                {
                    "name":"判断计算实际终点偏移量Y",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"发送起点X",
                        "condition":"@计算实际终点偏移量Y"
                    }
                },
                {
                    "name":"显示计算失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"点位计算失败",
                        "level":"error"
                    }
                },
                {
                    "name":"发送点胶NG结果",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "master":"点胶控制卡",
                        "offset":"37",
                        "type":"short",
                        "content":"2"
                    }
                },
                {
                    "name":"结束点胶失败NG",
                    "type":"end"
                },
                {
                    "name":"发送起点X",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "master":"点胶控制卡",
                        "offset":"39",
                        "type":"float",
                        "content":"@实际起点X"
                    },
                    "out":
                    {
                        "result":"发送起点X结果"
                    }
                },
                {
                    "name":"发送起点Y",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "wait":"@发送起点X结果",
                        "master":"点胶控制卡",
                        "offset":"41",
                        "type":"float",
                        "content":"@实际起点Y"
                    },
                    "out":
                    {
                        "result":"发送起点Y结果"
                    }
                },
                {
                    "name":"发送终点X",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "wait":"@发送起点Y结果",
                        "master":"点胶控制卡",
                        "offset":"43",
                        "type":"float",
                        "content":"@实际终点偏移量X"
                    },
                    "out":
                    {
                        "result":"发送终点X结果"
                    }
                },
                {
                    "name":"发送终点Y",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "wait":"@发送终点X结果",
                        "master":"点胶控制卡",
                        "offset":"45",
                        "type":"float",
                        "content":"@实际终点偏移量Y"
                    },
                    "out":
                    {
                        "result":"发送终点Y结果"
                    }
                },
                {
                    "name":"发送圆心X",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "wait":"@发送终点Y结果",
                        "master":"点胶控制卡",
                        "offset":"47",
                        "type":"float",
                        "content":"@实际圆心偏移量X"
                    },
                    "out":
                    {
                        "result":"发送圆心X结果"
                    }
                },
                {
                    "name":"发送圆心Y",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "wait":"@发送圆心X结果",
                        "master":"点胶控制卡",
                        "offset":"49",
                        "type":"float",
                        "content":"@实际圆心偏移量Y"
                    },
                    "out":
                    {
                        "result":"发送圆心Y结果"
                    }
                },
                {
                    "name":"延时1",
                    "type":"sleep",
                    "in":
                    {
                        "wait":"@发送圆心Y结果",
                        "time":"50"
                    },
                    "out":
                    {
                        "result":"延时1结果"
                    }
                },
                {
                    "name":"发送点胶OK结果",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "wait":"@延时1结果",
                        "master":"点胶控制卡",
                        "offset":"37",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        }
    ]
}
