import cv2
import math
import numpy as np


class AngleCalculator:
    def __init__(self, image_path):
        self.point_list = []
        self.image = cv2.imread(image_path)
        self.refresh_image = self.image.copy()

    def get_mouse_points(self, events, x, y, flags, params):
        if events == cv2.EVENT_LBUTTONDOWN:
            size = len(self.point_list)

            if size != 0 and size % 3 != 0:
                cv2.line(self.image, tuple(self.point_list[-1]), (x, y), (0, 255, 0), 2)

            cv2.circle(self.image, (x, y), 5, (0, 255, 0), -1)
            self.point_list.append([x, y])
            print(self.point_list)

    def calculate_angle(self):
        p1, p2, p3 = np.array(self.point_list[-3:])
        a = np.linalg.norm(p2 - p1)
        b = np.linalg.norm(p3 - p2)
        c = np.linalg.norm(p1 - p3)
        cos_C = (a ** 2 + b ** 2 - c ** 2) / (2 * a * b)
        angle = int(np.arccos(cos_C) * 180 / np.pi)
        print(angle)
        return angle, p2

    def __call__(self):
        while True:
            if len(self.point_list) % 3 == 0 and len(self.point_list) > 0:
                angle, point = self.calculate_angle()
                cv2.putText(self.image, str(angle), (point[0] - 40, point[1] - 20),
                            cv2.FONT_HERSHEY_COMPLEX, 1.5, (0, 255, 0), 2)

            cv2.imshow('Image', self.image)
            cv2.setMouseCallback('Image', self.get_mouse_points)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                self.point_list = []
                self.image = self.refresh_image.copy()


if __name__ == '__main__':
    image_path = r'C:\Users\<USER>\Pictures\Camera Roll\WIN_20241115_13_28_17_Pro.jpg'
    calculator = AngleCalculator(image_path)
    calculator()
