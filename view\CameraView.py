from PyQt5.QtWidgets import QWidget, QListView

from rotationanglemeasurement.UI.cameraFrame import Ui_cameraForm
from rotationanglemeasurement.utils.manager import managerQ
from rotationanglemeasurement.view.imageView import ImageView


class CameraWindow(QWidget, Ui_cameraForm):
    def __init__(self, parent=None):
        super(CameraWindow, self).__init__(parent=parent)
        self.setupUi(self)
        self.camera_id = None
        self.img_Lpath = None
        self.img_Rpath = None
        self.coordinate_list = []
        self.image_view = ImageView()
        managerQ.red_dot_finish.connect(self.set_coordinate_list)
        self.pushButtonloadIMGRight.clicked.connect(self.load_img)
        self.pushButtonloadIMGLeft.clicked.connect(self.load_img)
        self.comboBox.currentIndexChanged.connect(self.combox_switch)
        self.position_id = None
        self.reset_data()

    def load_img(self):
        if not self.img_Lpath or not self.img_Rpath:
            return
        sender = self.sender()
        if sender.objectName() == "pushButtonloadIMGRight":
            self.image_view.load_image(self.camera_id, self.img_Rpath, self.position_id, dot_num=managerQ.dot_num)
        else:
            self.image_view.load_image(self.camera_id, self.img_Lpath, self.position_id, dot_num=managerQ.dot_num)
        if not self.image_view.isVisible():
            self.image_view.showMaximized()

    def set_coordinate_list(self, camera_id, coordinate_list, direction):
        if self.camera_id == camera_id:
            self.coordinate_list = coordinate_list
            coordinate_string = ""
            for i in range(len(coordinate_list)):
                coordinate_string += "(" + str(coordinate_list[i][0]) + "," + str(coordinate_list[i][1]) + ")"
                # Add a comma if it's not the last element
                if i < len(coordinate_list) - 1:
                    coordinate_string += ","
            if direction == "left":
                self.label_coordinate_left.setText(coordinate_string)
                self.coordinate_left_list = coordinate_list
            elif direction == "right":
                self.label_coordinate_right.setText(coordinate_string)
                self.coordinate_right_list = coordinate_list

    def reset_data(self):
        self.coordinate_left_list = []
        self.coordinate_right_list = []

    def combox_switch(self):
        self.camera_id = self.comboBox.currentText()
