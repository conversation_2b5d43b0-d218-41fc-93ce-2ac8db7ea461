import os
import subprocess
import sys
import threading

import time

import psutil
from PyQt5.QtWidgets import QWidget, QApplication

from common.LogUtils import logger
from ui.ElevationAngle import Ui_elevationForm
from ui.canvas import CustomPlotWidget
from utils.elevation_angle_tool import elevation_angle_tool

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)


class ElevationAngleView(QWidget, Ui_elevationForm):
    def __init__(self, parent=None):
        super(ElevationAngleView, self).__init__(parent)
        self.setupUi(self)
        self.elevation_angle_tool = elevation_angle_tool
        self.setWindowTitle("仰角测试工具")

        # 初始化绘图组件
        # self.widget_display = CustomPlotWidget()
        # 如果你的UI中已经有一个占位符widget，可以用布局替换它
        # 或者直接添加到现有布局中

        self.elevation_angle_tool.angle_changed.connect(self.set_angle)
        self.pushButtonStart.clicked.connect(self.start_elevation_angle)
        self.pushButtonStop.clicked.connect(self.stop_elevation_angle)

    def execute_comand(self, cmd):
        logger.info("execute_comand cmd={}".format(cmd))
        out = subprocess.Popen(cmd, shell=True, stderr=subprocess.STDOUT,
                               stdin=subprocess.PIPE,
                               bufsize=0,
                               stdout=subprocess.PIPE)
        output, error = out.communicate()

    def check_server(self):
        for proc in psutil.process_iter(['pid', 'name']):
            # 如果进程名匹配
            if proc.info['name'] == "orbbec_server.exe":
                break
        else:
            # 开启进程
            path = os.path.join(parent_dir, "orbbec", "orbbec_server.exe")
            threading.Thread(target=self.execute_comand, args=(path,)).start()
            logger.info("start orbbec_server...")
            time.sleep(.2)

    def start_elevation_angle(self):
        # distance = self.spinBox.value()
        # compensate = self.spinBox_compensate.value()
        self.check_server()

        # 清空之前的数据并重置时间
        self.widget_display.clear_data()
        self.widget_display.reset_start_time()

        self.elevation_angle_tool.stop_detect_angle = False
        # threading.Thread(target=self.elevation_angle_tool.detect_angle).start()
        # 遍历所有的线程
        for thread in threading.enumerate():
            name = thread.name
            if thread.is_alive() and name == "elevation_angle_test":
                break
        else:
            threading.Thread(target=elevation_angle_tool.detect_angle,name="elevation_angle_test").start()
        self.pushButtonStart.setEnabled(False)
        self.pushButtonStart.setText("正在检测")

    def stop_elevation_angle(self):
        self.elevation_angle_tool.stop_detect_angle = True
        self.pushButtonStart.setEnabled(True)
        self.pushButtonStart.setText("测量")

    def set_angle(self, value):
        # 显示角度值到LCD
        formatted_number = "{:.1f}".format(value)
        self.lcdNumber.display(formatted_number)

        # 添加数据点到图表
        if self.widget_display.start_time is None:
            self.widget_display.reset_start_time()

        current_time = time.time() - self.widget_display.start_time
        self.widget_display.add_data_point(current_time, value)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    widget = ElevationAngleView()
    widget.show()
    sys.exit(app.exec_())
