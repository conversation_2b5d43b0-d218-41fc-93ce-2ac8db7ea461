import os
import threading
from datetime import datetime

from PyQt5.QtGui import QDragEnterEvent, QDropEvent
from PyQt5.QtWidgets import QWidget, QFileDialog, QListView

from common.view.MessageDialog import MessageDialog
from i2c_update.script.E02_update import E02
from i2c_update.script.T29_update import T29
from i2c_update.script.jietu import Tt2c
from i2c_update.ui.I2cUpdate import Ui_I2CUpdateForm
from i2c_update.script.zeekr_update import Zeekr_Flash
from utils.SignalsManager import signals_manager


class ViewI2cUpdate(QWidget, Ui_I2CUpdateForm):
    def __init__(self, parent=None):
        super(ViewI2cUpdate, self).__init__(parent)
        self.setupUi(self)
        self.setWindowTitle("I2C升级")
        # 设置窗口接受拖拽
        self.setAcceptDrops(True)
        self.pushButtonStart.clicked.connect(self.update)
        signals_manager.flash_log.connect(self.flash_log)
        signals_manager.flash_process.connect(self.flash_process)
        # 连接按钮的点击事件
        self.open_file_btn.clicked.connect(self.open_file_dialog)
        self.comboBoxProduct.addItems(["极氪CX1E", "奇瑞捷途", "奇瑞E02", "奇瑞T29"])
        self.textBrowser.document().setMaximumBlockCount(1000)

    def flash_process(self, process):
        self.label_process.setText(process)

    def flash_log(self, process):
        # 获取当前的时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.textBrowser.append(current_time + ":" + process)
        current_text = self.textBrowser.toPlainText()
        # 检查当前文本的字符数
        if len(current_text) > 100000:
            # 截取到最多 10000 个字符
            truncated_text = current_text[-100000:]  # 保留最后 10000 个字符
            self.textBrowser.setPlainText(truncated_text)  # 更新显示内容
        if "flash done" == process or process.startswith("error:"):
            self.pushButtonStart.setText("升级")
            self.pushButtonStart.setEnabled(True)
            self.label_process.setText("")

    def update(self):
        product_type = self.comboBoxProduct.currentText().strip()
        bin_path = self.lineEdit.text().strip()
        if not bin_path:
            MessageDialog.show_message("提示", "请选择升级文件")
            return
        if product_type == "极氪CX1E":
            F = Zeekr_Flash()
            threading.Thread(target=F.flash, args=(bin_path,)).start()
            self.pushButtonStart.setText("升级中...")
            self.pushButtonStart.setEnabled(False)
        elif product_type == "奇瑞捷途":
            T = Tt2c()
            T.UPDATE_FILENAME = bin_path
            threading.Thread(target=T.main).start()
            self.pushButtonStart.setText("升级中...")
            self.pushButtonStart.setEnabled(False)
        elif product_type == "奇瑞E02":

            T = E02()
            T.UPDATE_FILENAME = bin_path
            threading.Thread(target=T.main).start()
            self.pushButtonStart.setText("升级中...")
            self.pushButtonStart.setEnabled(False)
        elif product_type == "奇瑞T29":
            T = T29()
            T.UPDATE_FILENAME = bin_path
            threading.Thread(target=T.main).start()
            self.pushButtonStart.setText("升级中...")
            self.pushButtonStart.setEnabled(False)

    def open_file_dialog(self):
        """ 打开文件对话框选择文件 """
        file_path, _ = QFileDialog.getOpenFileName(self, "选择文件", "", "All Files (*)")
        if file_path:
            self.lineEdit.setText(file_path)

    # def update(self):
    #     """ 启动升级操作的处理 """
    #     file_path = self.lineEdit.text()
    #     if not file_path:
    #         self.textBrowser.append("未选择文件，无法开始升级！")
    #         return
    #     # 在这里添加处理升级的代码
    #     self.textBrowser.append(f"正在升级：{file_path}")

    # 重载拖拽进入事件
    def dragEnterEvent(self, event: QDragEnterEvent):
        """ 处理拖拽进入的事件 """
        mime_data = event.mimeData()
        if mime_data.hasUrls():
            event.accept()
        else:
            event.ignore()

    # 重载放置事件
    def dropEvent(self, event: QDropEvent):
        """ 处理拖拽放置事件 """
        mime_data = event.mimeData()
        if mime_data.hasUrls():
            url = mime_data.urls()[0]  # 获取拖拽的第一个文件
            file_path = url.toLocalFile()
            if os.path.exists(file_path):
                self.lineEdit.setText(file_path)
            else:
                self.textBrowser.append(f"文件不存在: {file_path}")
        else:
            self.textBrowser.append("拖拽的不是文件！")


if __name__ == '__main__':
    import sys
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)
    w = ViewI2cUpdate()
    w.show()
    sys.exit(app.exec_())
