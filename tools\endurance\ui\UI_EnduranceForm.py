# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'UI_EnduranceForm.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_EnduranceForm(object):
    def setupUi(self, EnduranceForm):
        EnduranceForm.setObjectName("EnduranceForm")
        EnduranceForm.resize(1600, 900)
        EnduranceForm.setMinimumSize(QtCore.QSize(1600, 900))
        font = QtGui.QFont()
        font.setPointSize(12)
        EnduranceForm.setFont(font)
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(EnduranceForm)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.frame_2 = QtWidgets.QFrame(EnduranceForm)
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.frame_2)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.groupBox = QtWidgets.QGroupBox(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox.sizePolicy().hasHeightForWidth())
        self.groupBox.setSizePolicy(sizePolicy)
        self.groupBox.setMinimumSize(QtCore.QSize(220, 140))
        self.groupBox.setObjectName("groupBox")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.groupBox)
        self.verticalLayout.setSpacing(10)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setContentsMargins(-1, 6, -1, 6)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.label = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setMinimumSize(QtCore.QSize(60, 0))
        self.label.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.label.setObjectName("label")
        self.horizontalLayout.addWidget(self.label)
        self.serial_combo = QtWidgets.QComboBox(self.groupBox)
        self.serial_combo.setMinimumSize(QtCore.QSize(0, 24))
        self.serial_combo.setObjectName("serial_combo")
        self.horizontalLayout.addWidget(self.serial_combo)
        self.refresh_btn = QtWidgets.QPushButton(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.refresh_btn.sizePolicy().hasHeightForWidth())
        self.refresh_btn.setSizePolicy(sizePolicy)
        self.refresh_btn.setMinimumSize(QtCore.QSize(80, 24))
        self.refresh_btn.setObjectName("refresh_btn")
        self.horizontalLayout.addWidget(self.refresh_btn)
        self.verticalLayout.addLayout(self.horizontalLayout)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setContentsMargins(-1, 6, -1, 6)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.label_2 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_2.sizePolicy().hasHeightForWidth())
        self.label_2.setSizePolicy(sizePolicy)
        self.label_2.setMinimumSize(QtCore.QSize(60, 0))
        self.label_2.setAlignment(QtCore.Qt.AlignRight|QtCore.Qt.AlignTrailing|QtCore.Qt.AlignVCenter)
        self.label_2.setObjectName("label_2")
        self.horizontalLayout_2.addWidget(self.label_2)
        self.slave_spin = QtWidgets.QSpinBox(self.groupBox)
        self.slave_spin.setMinimumSize(QtCore.QSize(0, 24))
        self.slave_spin.setProperty("value", 17)
        self.slave_spin.setObjectName("slave_spin")
        self.horizontalLayout_2.addWidget(self.slave_spin)
        self.verticalLayout.addLayout(self.horizontalLayout_2)
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_10.setContentsMargins(-1, 6, -1, 6)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.label_8 = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_8.sizePolicy().hasHeightForWidth())
        self.label_8.setSizePolicy(sizePolicy)
        self.label_8.setMinimumSize(QtCore.QSize(60, 0))
        self.label_8.setObjectName("label_8")
        self.horizontalLayout_10.addWidget(self.label_8)
        self.device_radio = QtWidgets.QRadioButton(self.groupBox)
        self.device_radio.setMinimumSize(QtCore.QSize(0, 24))
        self.device_radio.setCheckable(False)
        self.device_radio.setObjectName("device_radio")
        self.horizontalLayout_10.addWidget(self.device_radio)
        self.verticalLayout.addLayout(self.horizontalLayout_10)
        self.horizontalLayout_13 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_13.setContentsMargins(-1, 6, -1, 6)
        self.horizontalLayout_13.setObjectName("horizontalLayout_13")
        self.label_10 = QtWidgets.QLabel(self.groupBox)
        self.label_10.setMinimumSize(QtCore.QSize(60, 24))
        self.label_10.setObjectName("label_10")
        self.horizontalLayout_13.addWidget(self.label_10)
        self.device_name = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.device_name.sizePolicy().hasHeightForWidth())
        self.device_name.setSizePolicy(sizePolicy)
        self.device_name.setObjectName("device_name")
        self.horizontalLayout_13.addWidget(self.device_name)
        self.verticalLayout.addLayout(self.horizontalLayout_13)
        self.horizontalLayout_14 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_14.setContentsMargins(-1, 6, -1, 6)
        self.horizontalLayout_14.setObjectName("horizontalLayout_14")
        self.label_12 = QtWidgets.QLabel(self.groupBox)
        self.label_12.setMinimumSize(QtCore.QSize(60, 24))
        self.label_12.setObjectName("label_12")
        self.horizontalLayout_14.addWidget(self.label_12)
        self.device_hardware = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.device_hardware.sizePolicy().hasHeightForWidth())
        self.device_hardware.setSizePolicy(sizePolicy)
        self.device_hardware.setObjectName("device_hardware")
        self.horizontalLayout_14.addWidget(self.device_hardware)
        self.verticalLayout.addLayout(self.horizontalLayout_14)
        self.horizontalLayout_15 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_15.setContentsMargins(-1, 6, -1, 6)
        self.horizontalLayout_15.setObjectName("horizontalLayout_15")
        self.label_14 = QtWidgets.QLabel(self.groupBox)
        self.label_14.setMinimumSize(QtCore.QSize(60, 24))
        self.label_14.setObjectName("label_14")
        self.horizontalLayout_15.addWidget(self.label_14)
        self.device_software = QtWidgets.QLabel(self.groupBox)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.device_software.sizePolicy().hasHeightForWidth())
        self.device_software.setSizePolicy(sizePolicy)
        self.device_software.setObjectName("device_software")
        self.horizontalLayout_15.addWidget(self.device_software)
        self.verticalLayout.addLayout(self.horizontalLayout_15)
        self.horizontalLayout_12 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_12.setContentsMargins(-1, 6, -1, 6)
        self.horizontalLayout_12.setObjectName("horizontalLayout_12")
        self.label_9 = QtWidgets.QLabel(self.groupBox)
        self.label_9.setMinimumSize(QtCore.QSize(0, 24))
        self.label_9.setObjectName("label_9")
        self.horizontalLayout_12.addWidget(self.label_9)
        self.do_1_checkbox = QtWidgets.QCheckBox(self.groupBox)
        self.do_1_checkbox.setObjectName("do_1_checkbox")
        self.horizontalLayout_12.addWidget(self.do_1_checkbox)
        self.do_2_checkbox = QtWidgets.QCheckBox(self.groupBox)
        self.do_2_checkbox.setObjectName("do_2_checkbox")
        self.horizontalLayout_12.addWidget(self.do_2_checkbox)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_12.addItem(spacerItem)
        self.verticalLayout.addLayout(self.horizontalLayout_12)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.connect_btn = QtWidgets.QPushButton(self.groupBox)
        self.connect_btn.setMinimumSize(QtCore.QSize(0, 24))
        self.connect_btn.setObjectName("connect_btn")
        self.horizontalLayout_3.addWidget(self.connect_btn)
        self.disconnect_btn = QtWidgets.QPushButton(self.groupBox)
        self.disconnect_btn.setMinimumSize(QtCore.QSize(0, 24))
        self.disconnect_btn.setObjectName("disconnect_btn")
        self.horizontalLayout_3.addWidget(self.disconnect_btn)
        self.verticalLayout.addLayout(self.horizontalLayout_3)
        spacerItem1 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem1)
        self.verticalLayout_3.addWidget(self.groupBox)
        self.horizontalLayout_9.addWidget(self.frame_2)
        self.frame = QtWidgets.QFrame(EnduranceForm)
        self.frame.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame.setObjectName("frame")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.frame)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.groupBox_3 = QtWidgets.QGroupBox(self.frame)
        self.groupBox_3.setObjectName("groupBox_3")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.groupBox_3)
        self.verticalLayout_2.setSpacing(10)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.label_3 = QtWidgets.QLabel(self.groupBox_3)
        self.label_3.setMinimumSize(QtCore.QSize(80, 0))
        self.label_3.setObjectName("label_3")
        self.horizontalLayout_4.addWidget(self.label_3)
        self.do_1_radio = QtWidgets.QRadioButton(self.groupBox_3)
        self.do_1_radio.setMinimumSize(QtCore.QSize(0, 24))
        self.do_1_radio.setChecked(True)
        self.do_1_radio.setObjectName("do_1_radio")
        self.horizontalLayout_4.addWidget(self.do_1_radio)
        self.do_2_radio = QtWidgets.QRadioButton(self.groupBox_3)
        self.do_2_radio.setMinimumSize(QtCore.QSize(0, 24))
        self.do_2_radio.setObjectName("do_2_radio")
        self.horizontalLayout_4.addWidget(self.do_2_radio)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem2)
        self.verticalLayout_2.addLayout(self.horizontalLayout_4)
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.label_5 = QtWidgets.QLabel(self.groupBox_3)
        self.label_5.setMinimumSize(QtCore.QSize(80, 0))
        self.label_5.setObjectName("label_5")
        self.horizontalLayout_6.addWidget(self.label_5)
        self.wakeup_spin = QtWidgets.QSpinBox(self.groupBox_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.wakeup_spin.sizePolicy().hasHeightForWidth())
        self.wakeup_spin.setSizePolicy(sizePolicy)
        self.wakeup_spin.setMinimumSize(QtCore.QSize(100, 45))
        self.wakeup_spin.setMaximum(3600)
        self.wakeup_spin.setProperty("value", 60)
        self.wakeup_spin.setObjectName("wakeup_spin")
        self.horizontalLayout_6.addWidget(self.wakeup_spin)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_6.addItem(spacerItem3)
        self.verticalLayout_2.addLayout(self.horizontalLayout_6)
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.label_4 = QtWidgets.QLabel(self.groupBox_3)
        self.label_4.setMinimumSize(QtCore.QSize(80, 0))
        self.label_4.setObjectName("label_4")
        self.horizontalLayout_5.addWidget(self.label_4)
        self.sleep_spin = QtWidgets.QSpinBox(self.groupBox_3)
        self.sleep_spin.setMinimumSize(QtCore.QSize(100, 45))
        self.sleep_spin.setMaximum(3600)
        self.sleep_spin.setProperty("value", 60)
        self.sleep_spin.setObjectName("sleep_spin")
        self.horizontalLayout_5.addWidget(self.sleep_spin)
        spacerItem4 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_5.addItem(spacerItem4)
        self.verticalLayout_2.addLayout(self.horizontalLayout_5)
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.label_6 = QtWidgets.QLabel(self.groupBox_3)
        self.label_6.setMinimumSize(QtCore.QSize(80, 0))
        self.label_6.setObjectName("label_6")
        self.horizontalLayout_7.addWidget(self.label_6)
        self.count_spin = QtWidgets.QSpinBox(self.groupBox_3)
        self.count_spin.setMinimumSize(QtCore.QSize(100, 45))
        self.count_spin.setObjectName("count_spin")
        self.horizontalLayout_7.addWidget(self.count_spin)
        self.label_7 = QtWidgets.QLabel(self.groupBox_3)
        self.label_7.setObjectName("label_7")
        self.horizontalLayout_7.addWidget(self.label_7)
        spacerItem5 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_7.addItem(spacerItem5)
        self.verticalLayout_2.addLayout(self.horizontalLayout_7)
        self.horizontalLayout_16 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_16.setContentsMargins(-1, 0, -1, 0)
        self.horizontalLayout_16.setObjectName("horizontalLayout_16")
        self.label_11 = QtWidgets.QLabel(self.groupBox_3)
        self.label_11.setMinimumSize(QtCore.QSize(80, 24))
        self.label_11.setObjectName("label_11")
        self.horizontalLayout_16.addWidget(self.label_11)
        self.count_label = QtWidgets.QLabel(self.groupBox_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.count_label.sizePolicy().hasHeightForWidth())
        self.count_label.setSizePolicy(sizePolicy)
        self.count_label.setObjectName("count_label")
        self.horizontalLayout_16.addWidget(self.count_label)
        self.verticalLayout_2.addLayout(self.horizontalLayout_16)
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.start_btn = QtWidgets.QPushButton(self.groupBox_3)
        self.start_btn.setMinimumSize(QtCore.QSize(0, 24))
        self.start_btn.setObjectName("start_btn")
        self.horizontalLayout_8.addWidget(self.start_btn)
        self.stop_btn = QtWidgets.QPushButton(self.groupBox_3)
        self.stop_btn.setMinimumSize(QtCore.QSize(0, 24))
        self.stop_btn.setObjectName("stop_btn")
        self.horizontalLayout_8.addWidget(self.stop_btn)
        self.verticalLayout_2.addLayout(self.horizontalLayout_8)
        self.verticalLayout_4.addWidget(self.groupBox_3)
        spacerItem6 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_4.addItem(spacerItem6)
        self.horizontalLayout_9.addWidget(self.frame)
        self.horizontalLayout_9.setStretch(0, 2)
        self.horizontalLayout_9.setStretch(1, 3)

        self.retranslateUi(EnduranceForm)
        QtCore.QMetaObject.connectSlotsByName(EnduranceForm)

    def retranslateUi(self, EnduranceForm):
        _translate = QtCore.QCoreApplication.translate
        EnduranceForm.setWindowTitle(_translate("EnduranceForm", "耐久测试"))
        self.groupBox.setTitle(_translate("EnduranceForm", "继电器"))
        self.label.setText(_translate("EnduranceForm", "串口:"))
        self.refresh_btn.setText(_translate("EnduranceForm", "刷新"))
        self.label_2.setText(_translate("EnduranceForm", "设备地址:"))
        self.label_8.setText(_translate("EnduranceForm", "设备状态:"))
        self.device_radio.setText(_translate("EnduranceForm", "已连接"))
        self.label_10.setText(_translate("EnduranceForm", "设备名称:"))
        self.device_name.setText(_translate("EnduranceForm", "--"))
        self.label_12.setText(_translate("EnduranceForm", "硬件版本:"))
        self.device_hardware.setText(_translate("EnduranceForm", "--"))
        self.label_14.setText(_translate("EnduranceForm", "软件版本:"))
        self.device_software.setText(_translate("EnduranceForm", "--"))
        self.label_9.setText(_translate("EnduranceForm", "开关量(DO):"))
        self.do_1_checkbox.setText(_translate("EnduranceForm", "DO1"))
        self.do_2_checkbox.setText(_translate("EnduranceForm", "DO2"))
        self.connect_btn.setText(_translate("EnduranceForm", "连接"))
        self.disconnect_btn.setText(_translate("EnduranceForm", "断开"))
        self.groupBox_3.setTitle(_translate("EnduranceForm", "休眠/唤醒"))
        self.label_3.setText(_translate("EnduranceForm", "开关量(DO):"))
        self.do_1_radio.setText(_translate("EnduranceForm", "DO1"))
        self.do_2_radio.setText(_translate("EnduranceForm", "DO2"))
        self.label_5.setText(_translate("EnduranceForm", "唤醒时长(s):"))
        self.label_4.setText(_translate("EnduranceForm", "休眠时长(s):"))
        self.label_6.setText(_translate("EnduranceForm", "循环次数:"))
        self.label_7.setText(_translate("EnduranceForm", "(0:无限循环)"))
        self.label_11.setText(_translate("EnduranceForm", "执行次数:"))
        self.count_label.setText(_translate("EnduranceForm", "--"))
        self.start_btn.setText(_translate("EnduranceForm", "启动"))
        self.stop_btn.setText(_translate("EnduranceForm", "停止"))
