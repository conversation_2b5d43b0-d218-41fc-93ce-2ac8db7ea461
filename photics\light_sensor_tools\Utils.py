import subprocess

from common.LogUtils import logger


# 把一个3x3的矩阵，每一个数转换成4个字节，如果是正数，高位补零，如果是负数，高位补80 一共36个字节
def generate_matrix_data(matrix):
    byte_array = bytearray()
    for row in matrix:
        for element in row:
            if element >= 0:
                # 正数直接转成4个字节
                byte_array.extend(element.to_bytes(4, 'big', signed=False))
            else:
                # 负数转成正数，转成二进制，补1，再转成16进制
                positive_number = abs(element)
                binary_string = bin(positive_number)[2:].zfill(32)
                binary_string = '1' + binary_string[1:]  # 补1

                # 将二进制字符串转换为16进制字符串
                hex_string = hex(int(binary_string, 2))[2:].zfill(8)
                byte_array.extend(bytearray.fromhex(hex_string))

    return byte_array


# 把一个字节数据异或求和，得到一个数，并且把这个数添加到这个数组的最后一位
def add_xor_sum(byte_array):
    xor_sum = 0
    for byte in byte_array:
        xor_sum ^= byte
    byte_array.append(xor_sum)
    return byte_array


# 将两个字节数组合并成一个字节数组，并且异或求和得到一个数。添加到最后一位
def merge_and_add_xor_sum(byte_array1, byte_array2):
    merged_array = add_xor_sum(byte_array1 + byte_array2)
    return merged_array


# 将一个整数转成两个字节，返回一个字节数组的工具类
def int_to_bytes(value):
    byte1 = (value >> 8) & 0xFF
    byte2 = value & 0xFF
    byte_array = bytearray([byte1, byte2])
    return byte_array


# 执行adb命名
def execute_adb_command(command):
    logger.info(f"execute_adb_command command={command}")
    try:
        # 使用subprocess模块执行ADB命令
        process = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        # 获取命令输出
        output, error = process.communicate()

        # 输出命令执行结果
        if process.returncode == 0:
            logger.info("execute_adb_command ADB命令执行成功！")
            logger.info(f"execute_adb_command 输出结果: {output.decode()}")
        else:
            logger.info("execute_adb_command ADB命令执行失败！")
            logger.info(f"execute_adb_command 错误信息: {error.decode()}")
    except Exception as e:
        logger.error(f"execute_adb_command 执行ADB命令时出现异常: {str(e.args)}")
