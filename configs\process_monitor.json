{"ip": "127.0.0.1", "port": 30000, "config": {"ICSCN25": {"2B1": 1, "2B2": 1, "2A:": 0, "2C": 100, "ERROR_LIST": ["亮度故障", "TCON故障", "电压故障", "触摸故障", "息屏故障", "pmicerr故障", "bridgeerr故障", "温度异常"], "error_info": [[], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"ics_warningrsp_thi": 7, "ics_warningrsp_brilimt": 0, "ics_warningrsp_tconerr": 1, "ics_warningrsp_uhilo": 2, "ics_warningrsp_toucherr": 3, "ics_warningrsp_locksts": 4, "ics_warningrsp_pmicerr": 5, "ics_warningrsp_bridgeerr": 6}}, "ICSCN38": {"2B1": 1, "2B2": 1, "2A:": 0, "2C": 100, "ERROR_LIST": ["亮度故障", "TCON故障", "电压故障", "触摸故障", "息屏故障", "pmicerr故障", "bridgeerr故障", "温度异常"], "error_info": [[], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"ics_warningrsp_thi": 7, "ics_warningrsp_brilimt": 0, "ics_warningrsp_tconerr": 1, "ics_warningrsp_uhilo": 2, "ics_warningrsp_toucherr": 3, "ics_warningrsp_locksts": 4, "ics_warningrsp_pmicerr": 5, "ics_warningrsp_bridgeerr": 6}}, "NT2.0IC": {"2B1": 1, "2B2": 1, "2A:": 0, "2C": 100, "ERROR_LIST": ["电压故障", "LCD故障", "Tcon故障", "亮度故障", "温度异常", "leddrvr故障", "息屏故障"], "error_info": [[], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0], "FlagDict": {"ic_warningrsp_uhilo": 0, "ic_warningrsp_lcdfail": 1, "ic_warningrsp_tconfail": 2, "ic_warningrsp_brilimt": 3, "ic_warningrsp_thi": 4, "ic_warningrsp_leddrvr": 5, "ic_warningrsp_locksts": 6}}, "LCMCN11": {"2B1": 1, "2B2": 1, "2A:": 0, "2C": 100, "ERROR_LIST": ["电压故障", "LCD故障", "Tcon故障", "亮度故障", "温度异常", "leddrvr故障", "息屏故障"], "error_info": [[], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0], "FlagDict": {"ic_warningrsp_uhilo": 0, "ic_warningrsp_lcdfail": 1, "ic_warningrsp_tconfail": 2, "ic_warningrsp_brilimt": 3, "ic_warningrsp_thi": 4, "ic_warningrsp_leddrvr": 5, "ic_warningrsp_locksts": 6}}, "NT2.0FIR": {"2B1": 1, "2B2": 1, "2A:": 0, "2C": 100, "ERROR_LIST": ["亮度故障", "LCD故障", "leddrvr故障", "Tcon故障", "息屏故障", "温度异常", "电压故障", "触摸故障"], "error_info": [[], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"fi_r_warningrsp_brilimt": 0, "fi_r_warningrsp_lcd": 1, "fi_r_warningrsp_leddrvr": 2, "fi_r_warningrsp_tconfail": 3, "fi_r_warningrsp_locksts": 4, "fi_r_warningrsp_thi": 5, "fi_r_warningrsp_uhilo": 6, "fi_r_warningrsp_toucherr": 7}}, "ICSCN37": {"2B1": 10, "2B2": 10, "2A:": 0, "2C": 100, "ERROR_LIST": ["亮度故障", "电压故障", "OLED故障", "温度异常", "息屏故障", "TCON故障", "触摸故障"], "error_info": [[], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0], "FlagDict": {"ics_warning_brilimt": 0, "ics_warning_uhilo": 1, "ics_warning_oledfail": 2, "ics_warning_thi": 3, "ics_warning_locksts": 4, "ics_warning_tcon": 5, "ics_warning_toucherr": 6}}, "MSNCN15": {"2B1": 1, "2B2": 1, "2A:": 0, "2C": 100, "ERROR_LIST": ["亮度故障-L", "亮度故障-R", "息屏故障", "温度异常-L", "温度异常-R", "电压故障", "tcon故障-L", "tcon故障-R", "rohm故障-L", "rohm故障-R"], "error_info": [[], [], [], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"ic_warningrsp_brilimt_l": 0, "ic_warningrsp_brilimt_r": 1, "ic_warningrsp_locksts": 2, "ic_warningrsp_thi_l": 3, "ic_warningrsp_thi_r": 4, "ic_warningrsp_uhilo_l": 5, "ic_warningrsp_tconfail_l": 6, "ic_warningrsp_tconfail_r": 7, "ic_warningrsp_rohmfail_l": 8, "ic_warningrsp_rohmfail_r": 9}}, "ALPS_DOM_FIR": {"2B1": 1, "2B2": 10, "2A:": 0, "2C": 100, "ERROR_LIST": ["sts故障", "brightnesssts故障", "息屏故障", "温度异常", "Touch故障", "电压故障", "tcon故障", "pmicerr故障", "bridgeerr故障"], "error_info": [[], [], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"fi_r_sts": 0, "fi_r_warningrsp_brilimt": 1, "fi_r_warningrsp_locksts": 2, "fi_r_warningrsp_thi": 3, "fi_r_warningrsp_toucherr": 4, "fi_r_warningrsp_uhilo": 5, "fi_r_warningrsp_tconerr": 6, "fi_r_warningrsp_pmicerr": 7, "fi_r_warningrsp_bridgeerr": 8}}, "ICSCN22": {"2B1": 1, "2B2": 0, "2A:": 0, "2C": 100, "ERROR_LIST": ["锁舌故障", "电机状态故障", "右侧锁舌位置故障", "亮度受限状态故障", "lock故障", "PMIC故障", "TCON IC故障", "TDDI故障", "高温故障", "触摸故障", "高低压故障", "背光控制IC故障", "电机故障信息1", "电机故障信息2", "电机故障信息3"], "error_info": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"res_motor_leftlockpos": 0, "res_motor_resmotorsts": 1, "res_motor_rightlockpos": 2, "res_waringrsp_brilimt": 3, "res_warning_locksts": 4, "res_warning_pmicerr": 5, "res_warning_tconerr": 6, "res_warning_tddierr": 7, "res_warning_thi": 8, "res_warning_toucherr": 9, "res_warning_uhilo": 10, "res_bl_error": 11, "res_warning_errorcode1": 12, "res_warning_errorcode2": 13, "res_warning_errorcode3": 14}}, "RESCN12": {"2B1": 1, "2B2": 1, "2A:": 0, "2C": 100, "ERROR_LIST": ["Oldi/EdpError", "DisplayError", "显示临时关闭", "门/源驱动错误", "看门狗或MCU软件复位错", "异常上电复位", "Pmic电源管理IC错误", "LVDS链路锁定错误", "背光错误", "背光LED故障", "背光过电压", "背光欠电压", "背光过电流", "背光温度高", "背光温度低", "过去发生过背光错误", "SysVoltageValue", "NtcTempValue", "TemStatusValue", "BacklightStatusValue", "BacklightLevelValue"], "error_info": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"oldi_edp": 0, "display": 1, "display_temp_shutoff": 2, "gate_source_driver": 3, "watchdog_mcu_reset": 4, "abnormal_poweron_reset": 5, "pmic": 6, "lvds_link": 7, "backlight_error": 8, "backlight_led": 9, "backlight_overvoltage": 10, "backlight_undervoltage": 11, "backlight_overcurrent": 12, "backlight_temp_high": 13, "backlight_temp_low": 14, "backlight_past_error": 15, "system_voltage_value": 16, "ntc_temperature_value": 17, "temperature_status_value": 18, "backlight_status_value": 19, "backlight_level_value": 20}}, "MSNCN19": {"2B1": 1, "2B2": 1, "2A:": 0, "2C": 100, "ERROR_LIST": ["右屏高温故障", "LOCK信号状态", "左屏高温故障", "左屏亮度受限状态", "ic_warningrsp_tcon_source_l", "ic_warningrsp_bridge_l", "左屏LED灯驱动故障", "左屏高低压故障", "右屏亮度受限状态", "ic_warningrsp_tcon_source_r", "ic_warningrsp_bridge_r", "右屏LED灯驱动故障", "右屏高低压故障", "系统状态", "ic_power_fail", "ic_tconfaultsts_0x0203", "ic_bridgefaultsts_0x1d20", "ic_bridgefaultsts_0x1d21", "ic_bridgefaultsts_0x1d22", "ic_bridgefaultsts_0x1d23", "ic_fusafaultsts_1", "ic_fusafaultsts_2", "ic_fusafaultsts_3"], "error_info": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"ic_warningrsp_thi_r": 0, "ic_warningrsp_locksts": 1, "ic_warningrsp_thi_l": 2, "ic_warningrsp_brilimt_l": 3, "ic_warningrsp_tcon_source_l": 4, "ic_warningrsp_bridge_l": 5, "ic_warningrsp_leddrvr_l": 6, "ic_warningrsp_uhilo_l": 7, "ic_warningrsp_brilimt_r": 8, "ic_warningrsp_tcon_source_r": 9, "ic_warningrsp_bridge_r": 10, "ic_warningrsp_leddrvr_r": 11, "ic_warningrsp_uhilo_r": 12, "ic_sts": 13, "ic_power_fail": 14, "ic_tconfaultsts_0x0203": 15, "ic_bridgefaultsts_0x1d20": 16, "ic_bridgefaultsts_0x1d21": 17, "ic_bridgefaultsts_0x1d22": 18, "ic_bridgefaultsts_0x1d23": 19, "ic_fusafaultsts_1": 20, "ic_fusafaultsts_2": 21, "ic_fusafaultsts_3": 22}}, "RESCN11": {"2B1": 1, "2B2": 0, "2A:": 0, "2C": 100, "ERROR_LIST": ["锁舌故障", "电机状态故障", "右侧锁舌位置故障", "亮度受限状态故障", "lock故障", "PMIC故障", "TCON IC故障", "TDDI故障", "高温故障", "触摸故障", "高低压故障", "背光控制IC故障", "电机故障信息1", "电机故障信息2", "电机故障信息3"], "error_info": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"res_motor_leftlockpos": 0, "res_motor_resmotorsts": 1, "res_motor_rightlockpos": 2, "res_waringrsp_brilimt": 3, "res_warning_locksts": 4, "res_warning_pmicerr": 5, "res_warning_tconerr": 6, "res_warning_tddierr": 7, "res_warning_thi": 8, "res_warning_toucherr": 9, "res_warning_uhilo": 10, "res_bl_error": 11, "res_warning_errorcode1": 12, "res_warning_errorcode2": 13, "res_warning_errorcode3": 14}}, "RESCN14": {"2B1": 1, "2B2": 0, "2A:": 0, "2C": 100, "ERROR_LIST": ["主电机堵转", "夹手故障", "锁舌堵转", "高压异常", "低压异常", "过流异常", "过速度异常", "过载异常", "电机过温异常", "电流零偏异常", "显示角度异常", "锁舌驱动IC异常", "锁舌到位异常", "电机同步异常", "编码器通信异常", "高温异常", "背光异常", "Tcon IC异常", "TDDI异常", "PMIC异常", "USB通信异常", "显示屏零位未校准", "串口通信异常", "输入电压异常", "温度NTC异常"], "error_info": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "info_result": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "FlagDict": {"motor_stall": 0, "clamp_hand": 1, "lock_motor_stall": 2, "over_voltage": 3, "under_voltage": 4, "over_current": 5, "over_speed": 6, "over_load": 7, "motor_high_temperature": 8, "abnormal_current_sampling_bias": 9, "abnormal_angle_sensor": 10, "abnormal_locking_motor_driver": 11, "abnormal_locking_motor": 12, "motor_sync_error": 13, "encoder_com_error": 14, "high_temperature": 15, "backlight_error": 16, "tcon_ic_error": 17, "tddi_error": 18, "pmic_error": 19, "usb_com_error": 20, "display_uncalib": 21, "serial_com_error": 22, "input_voltage_error": 23, "temp_ntc_error": 24}}}, "DEBUG": 0}