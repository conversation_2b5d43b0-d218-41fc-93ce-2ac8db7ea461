# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'VoltageItem.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_VoltageItemForm(object):
    def setupUi(self, VoltageItemForm):
        VoltageItemForm.setObjectName("VoltageItemForm")
        VoltageItemForm.resize(242, 60)
        self.verticalLayout = QtWidgets.QVBoxLayout(VoltageItemForm)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.label = QtWidgets.QLabel(VoltageItemForm)
        self.label.setObjectName("label")
        self.horizontalLayout.addWidget(self.label)
        self.doubleSpinBox = QtWidgets.QDoubleSpinBox(VoltageItemForm)
        self.doubleSpinBox.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox.setProperty("value", 13.0)
        self.doubleSpinBox.setObjectName("doubleSpinBox")
        self.horizontalLayout.addWidget(self.doubleSpinBox)
        self.label_2 = QtWidgets.QLabel(VoltageItemForm)
        self.label_2.setObjectName("label_2")
        self.horizontalLayout.addWidget(self.label_2)
        self.doubleSpinBoxSleepTime = QtWidgets.QDoubleSpinBox(VoltageItemForm)
        self.doubleSpinBoxSleepTime.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBoxSleepTime.setDecimals(3)
        self.doubleSpinBoxSleepTime.setMaximum(999.99)
        self.doubleSpinBoxSleepTime.setProperty("value", 1.0)
        self.doubleSpinBoxSleepTime.setObjectName("doubleSpinBoxSleepTime")
        self.horizontalLayout.addWidget(self.doubleSpinBoxSleepTime)
        self.verticalLayout.addLayout(self.horizontalLayout)

        self.retranslateUi(VoltageItemForm)
        QtCore.QMetaObject.connectSlotsByName(VoltageItemForm)

    def retranslateUi(self, VoltageItemForm):
        _translate = QtCore.QCoreApplication.translate
        VoltageItemForm.setWindowTitle(_translate("VoltageItemForm", "Form"))
        self.label.setText(_translate("VoltageItemForm", "电压(V)"))
        self.label_2.setText(_translate("VoltageItemForm", "时间(s)"))
