<?xml version="1.0" encoding="UTF-8"?>
<settings>
    <global>
        <version>3.6.1</version>
        <windowPosition>712,-2053,2048,1088</windowPosition>
        <viewState/>
        <disAnimateZoom/>
        <autoWaitTrig/>
        <LogicProbe>0</LogicProbe>
        <smpProgPosition/>
        <colorWaveform/>
        <userLang>zh-CN</userLang>
        <devModel>8</devModel>
        <extparam/>
        <windowMaximized>0</windowMaximized>
        <anlyDbgPath/>
        <enaSocket>1</enaSocket>
        <listenPort>23367</listenPort>
        <oscCursorEna/>
        <lgcMeasure/>
        <toolbarPWM/>
        <chnShowIndex>0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15</chnShowIndex>
        <chnShowMultip>1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1</chnShowMultip>
        <chnShowName0>base MOSI</chnShowName0>
        <chnShowName1>base MISO</chnShowName1>
        <chnShowName2>base SCLK</chnShowName2>
        <chnShowName3>base SCS</chnShowName3>
        <chnShowName4>A</chnShowName4>
        <chnShowName5>B</chnShowName5>
        <chnShowName6>Z</chnShowName6>
        <chnShowName7>sclk</chnShowName7>
        <chnShowName8/>
        <chnShowName9/>
        <chnShowName10/>
        <chnShowName11/>
        <chnShowName12/>
        <chnShowName13/>
        <chnShowName14/>
        <chnShowName15/>
        <analyzerAlias>;</analyzerAlias>
        <recentAnalyzers>CAN.dll,I2C.dll,UART.dll,SPI.dll</recentAnalyzers>
        <disAutoUpdate/>
        <lastUpdate>2024-03-18_11:26:39</lastUpdate>
        <zeroSmpCnt/>
        <lastPath>C:/Users/<USER>/Downloads</lastPath>
    </global>
    <devices>
        <Demo>
            <trgPosition/>
            <chnTrig/>
            <chnEnable/>
            <smpMode/>
            <smpDepth/>
            <smpDepthIndex/>
            <smpFrequ/>
            <smpFrequIndex/>
            <chnLevel/>
        </Demo>
        <LA5016>
            <trgPosition/>
            <chnTrig>0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0</chnTrig>
            <chnEnable/>
            <smpMode/>
            <smpDepth>200000</smpDepth>
            <smpDepthIndex>9</smpDepthIndex>
            <smpFrequ>20000</smpFrequ>
            <smpFrequIndex>4</smpFrequIndex>
            <chnLevel>5.0V TTL</chnLevel>
            <chnVth>1.58</chnVth>
        </LA5016>
    </devices>
    <analyzers>
        <item0>
            <fileName>SPI.dll</fileName>
            <parameters>SpiAnalyzer,0,0,1,0,2,0,3,0,0,8,0,0,0,1,</parameters>
            <format>2</format>
        </item0>
    </analyzers>
</settings>
