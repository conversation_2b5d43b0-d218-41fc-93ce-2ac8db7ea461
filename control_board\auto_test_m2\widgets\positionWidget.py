from PyQt5.QtWidgets import QWidget
from PyQt5.QtCore import QTimer

from .ui.positionWidget import Ui_Form
from ..motion_control_card import mcc
from ..constants import X, Y, Z1, Z2, R


class PositionWidget(QWidget, Ui_Form):
    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setupUi(self)

        self.setWindowTitle("位置调试")
        self.resize(900, 700)

        self.pushButton_x1.pressed.connect(self.on_j_move_pressed)
        self.pushButton_x1.released.connect(self.on_j_move_released)
        self.pushButton_x0.pressed.connect(self.on_j_move_pressed)
        self.pushButton_x0.released.connect(self.on_j_move_released)
        self.pushButton_y1.pressed.connect(self.on_j_move_pressed)
        self.pushButton_y1.released.connect(self.on_j_move_released)
        self.pushButton_y0.pressed.connect(self.on_j_move_pressed)
        self.pushButton_y0.released.connect(self.on_j_move_released)
        self.pushButton_z11.pressed.connect(self.on_j_move_pressed)
        self.pushButton_z11.released.connect(self.on_j_move_released)
        self.pushButton_z10.pressed.connect(self.on_j_move_pressed)
        self.pushButton_z10.released.connect(self.on_j_move_released)
        self.pushButton_z21.pressed.connect(self.on_j_move_pressed)
        self.pushButton_z21.released.connect(self.on_j_move_released)
        self.pushButton_z20.pressed.connect(self.on_j_move_pressed)
        self.pushButton_z20.released.connect(self.on_j_move_released)
        self.pushButton_r1.pressed.connect(self.on_j_move_pressed)
        self.pushButton_r1.released.connect(self.on_j_move_released)
        self.pushButton_r0.pressed.connect(self.on_j_move_pressed)
        self.pushButton_r0.released.connect(self.on_j_move_released)

        self.pushButton_home.clicked.connect(self.on_home)
        self.pushButton_start.clicked.connect(self.on_start)
        self.pushButton_stop.clicked.connect(self.on_stop)

        self.timer1 = QTimer(self)
        self.timer1.timeout.connect(self.update_pos)
        self.timer1.start(100)

    def on_j_move_pressed(self):
        if self.sender().objectName() == "pushButton_x1":
            vel = 50
            acc = 0.05
            dec = 0.05
            smh = 0.9
            mcc.axis_d[X].jog_move(1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_x0":
            vel = 50
            acc = 0.05
            dec = 0.05
            smh = 0.9
            mcc.axis_d[X].jog_move(-1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_y1":
            vel = 50
            acc = 0.05
            dec = 0.05
            smh = 0.9
            mcc.axis_d[Y].jog_move(1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_y0":
            vel = 50
            acc = 0.05
            dec = 0.05
            smh = 0.9
            mcc.axis_d[Y].jog_move(-1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_z11":
            vel = 50
            acc = 0.05
            dec = 0.05
            smh = 0.9
            mcc.axis_d[Z1].jog_move(1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_z10":
            vel = 50
            acc = 0.05
            dec = 0.05
            smh = 0.9
            mcc.axis_d[Z1].jog_move(-1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_z21":
            vel = 50
            acc = 0.05
            dec = 0.05
            smh = 0.9
            mcc.axis_d[Z2].jog_move(1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_z20":
            vel = 50
            acc = 0.05
            dec = 0.05
            smh = 0.9
            mcc.axis_d[Z2].jog_move(-1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_r1":
            vel = 1
            acc = 0.05
            dec = 0.05
            smh = 0.9
            mcc.axis_d[R].jog_move(1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_r0":
            vel = 2
            acc = 0.05
            dec = 0.05
            smh = 0.9
            mcc.axis_d[R].jog_move(-1, vel, acc, dec, smh)

    def on_j_move_released(self):
        if self.sender().objectName() == "pushButton_x1":
            mcc.axis_d[X].jog_move(0)
        elif self.sender().objectName() == "pushButton_x0":
            mcc.axis_d[X].jog_move(0)
        elif self.sender().objectName() == "pushButton_y1":
            mcc.axis_d[Y].jog_move(0)
        elif self.sender().objectName() == "pushButton_y0":
            mcc.axis_d[Y].jog_move(0)
        elif self.sender().objectName() == "pushButton_z11":
            mcc.axis_d[Z1].jog_move(0)
        elif self.sender().objectName() == "pushButton_z10":
            mcc.axis_d[Z1].jog_move(0)
        elif self.sender().objectName() == "pushButton_z21":
            mcc.axis_d[Z2].jog_move(0)
        elif self.sender().objectName() == "pushButton_z20":
            mcc.axis_d[Z2].jog_move(0)
        elif self.sender().objectName() == "pushButton_r1":
            mcc.axis_d[R].jog_move(0)
        elif self.sender().objectName() == "pushButton_r0":
            mcc.axis_d[R].jog_move(0)

    def update_pos(self):
        pos = mcc.axis_d[X].status.axis_prf_pos
        self.lineEdit_x.setText(str(int(pos)))

        pos = mcc.axis_d[Y].status.axis_prf_pos
        self.lineEdit_y.setText(str(int(pos)))

        pos = mcc.axis_d[Z1].status.axis_prf_pos
        self.lineEdit_z1.setText(str(int(pos)))

        pos = mcc.axis_d[Z2].status.axis_prf_pos
        self.lineEdit_z2.setText(str(int(pos)))

        pos = mcc.axis_d[R].status.axis_prf_pos
        self.lineEdit_r.setText(str(int(pos)))

    def on_home(self):
        mcc.home()

    def on_start(self):
        pos_d = {
            X: int(float(self.lineEdit_x_dest.text())),
            Y: int(float(self.lineEdit_y_dest.text())),
            Z1: int(float(self.lineEdit_z1_dest.text())),
            Z2: int(float(self.lineEdit_z2_dest.text())),
            R: int(float(self.lineEdit_r_dest.text()))
        }
        mcc.trap_move(pos_d=pos_d)

    def on_stop(self):
        mcc.stop()
