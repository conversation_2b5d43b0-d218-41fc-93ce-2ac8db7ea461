# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'CameraFrame.ui'
#
# Created by: PyQt5 UI code generator 5.15.6
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_cameraForm(object):
    def setupUi(self, cameraForm):
        cameraForm.setObjectName("cameraForm")
        cameraForm.resize(471, 486)
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(cameraForm)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setSpacing(6)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setContentsMargins(15, 5, 15, 5)
        self.verticalLayout.setSpacing(6)
        self.verticalLayout.setObjectName("verticalLayout")
        self.labelLeft = QtWidgets.QLabel(cameraForm)
        self.labelLeft.setObjectName("labelLeft")
        self.verticalLayout.addWidget(self.labelLeft)
        self.labelRight = QtWidgets.QLabel(cameraForm)
        self.labelRight.setObjectName("labelRight")
        self.verticalLayout.addWidget(self.labelRight)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.label = QtWidgets.QLabel(cameraForm)
        self.label.setObjectName("label")
        self.horizontalLayout_2.addWidget(self.label)
        self.label_coordinate_left = QtWidgets.QLabel(cameraForm)
        self.label_coordinate_left.setText("")
        self.label_coordinate_left.setObjectName("label_coordinate_left")
        self.horizontalLayout_2.addWidget(self.label_coordinate_left)
        self.horizontalLayout_2.setStretch(1, 1)
        self.verticalLayout.addLayout(self.horizontalLayout_2)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.label_2 = QtWidgets.QLabel(cameraForm)
        self.label_2.setObjectName("label_2")
        self.horizontalLayout_3.addWidget(self.label_2)
        self.label_coordinate_right = QtWidgets.QLabel(cameraForm)
        self.label_coordinate_right.setText("")
        self.label_coordinate_right.setObjectName("label_coordinate_right")
        self.horizontalLayout_3.addWidget(self.label_coordinate_right)
        self.horizontalLayout_3.setStretch(1, 1)
        self.verticalLayout.addLayout(self.horizontalLayout_3)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.pushButtonloadIMGLeft = QtWidgets.QPushButton(cameraForm)
        self.pushButtonloadIMGLeft.setObjectName("pushButtonloadIMGLeft")
        self.horizontalLayout.addWidget(self.pushButtonloadIMGLeft)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.pushButtonloadIMGRight = QtWidgets.QPushButton(cameraForm)
        self.pushButtonloadIMGRight.setObjectName("pushButtonloadIMGRight")
        self.horizontalLayout.addWidget(self.pushButtonloadIMGRight)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.comboBox = QtWidgets.QComboBox(cameraForm)
        self.comboBox.setMinimumSize(QtCore.QSize(80, 0))
        self.comboBox.setObjectName("comboBox")
        self.horizontalLayout.addWidget(self.comboBox)
        self.verticalLayout.addLayout(self.horizontalLayout)
        self.verticalLayout.setStretch(0, 99)
        self.verticalLayout.setStretch(1, 99)
        self.verticalLayout_2.addLayout(self.verticalLayout)

        self.retranslateUi(cameraForm)
        QtCore.QMetaObject.connectSlotsByName(cameraForm)

    def retranslateUi(self, cameraForm):
        _translate = QtCore.QCoreApplication.translate
        cameraForm.setWindowTitle(_translate("cameraForm", "Form"))
        self.labelLeft.setText(_translate("cameraForm", "Left"))
        self.labelRight.setText(_translate("cameraForm", "Right"))
        self.label.setText(_translate("cameraForm", "左图坐标："))
        self.label_2.setText(_translate("cameraForm", "右图坐标："))
        self.pushButtonloadIMGLeft.setText(_translate("cameraForm", "左相机"))
        self.pushButtonloadIMGRight.setText(_translate("cameraForm", "右相机"))
