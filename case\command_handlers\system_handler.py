"""
系统相关命令处理器
"""
import logging
import threading
import time
import random
import operator
from typing import Dict, Any
from case.command_handlers.base_handler import BaseCommandHandler

logger = logging.getLogger(__name__)


class SystemCommandHandler(BaseCommandHandler):
    """系统命令处理器"""
    
    def get_supported_commands(self) -> set:
        """返回支持的系统相关命令"""
        return {
            "SetDelayTime", "SetRandomDelayTime", "UpdateFwByRandom", "UpdateFwByStep",
            "ExecuteAdbCmd", "ExecuteBat", "ExecuteBlockBat", "StopCycleCANMsg",
            "ClearCycleCANMsg", "StartAutoCyclePress", "StopAutoCyclePress",
            "StartCustomCyclePress", "StopCustomCyclePress", "DetectMcuLog",
            "DetectSocLog", "DetectOsLog", "DetectVdsAppLog", "SetCanMsgDelayTime",
            "CheckCanMsgThreading", "PushExceptCanMsg", "recordCanMsg",
            "LinSender", "LinStopSender", "MarkDtcNormalMsg"
        }
    
    def execute(self, command: str, step: Dict[str, Any]) -> None:
        """执行系统相关命令"""
        params = self._extract_step_params(step)
        case_number = params['case_number']
        data = params['data']
        
        try:
            if command == "SetDelayTime":
                self._handle_set_delay_time(case_number, command, data)
                
            elif command == "SetRandomDelayTime":
                self._handle_set_random_delay_time(case_number, command, data)
                
            elif command == "UpdateFwByRandom":
                self._handle_update_fw_by_random(case_number, command, data)
                
            elif command == "UpdateFwByStep":
                self._handle_update_fw_by_step(case_number, command, data)
                
            elif command == "ExecuteAdbCmd":
                self._handle_execute_adb_cmd(case_number, command, data)
                
            elif command == "ExecuteBat":
                self._handle_execute_bat(case_number, command, data)
                
            elif command == "ExecuteBlockBat":
                self._handle_execute_block_bat(case_number, command, data)
                
            elif command == "StopCycleCANMsg":
                self._handle_stop_cycle_can_msg(case_number, command, data)
                
            elif command == "ClearCycleCANMsg":
                self._handle_clear_cycle_can_msg(case_number, command)
                
            elif command == "StartAutoCyclePress":
                self._handle_start_auto_cycle_press(case_number, command, data)
                
            elif command == "StopAutoCyclePress":
                self._handle_stop_auto_cycle_press(case_number, command)
                
            elif command == "StartCustomCyclePress":
                self._handle_start_custom_cycle_press(case_number, command, data)
                
            elif command == "StopCustomCyclePress":
                self._handle_stop_custom_cycle_press(case_number, command)
                
            elif command == "DetectMcuLog":
                self._handle_detect_mcu_log(case_number, command, data)
                
            elif command == "DetectSocLog":
                self._handle_detect_soc_log(case_number, command, data)
                
            elif command == "DetectOsLog":
                self._handle_detect_os_log(case_number, command, data)
                
            elif command == "DetectVdsAppLog":
                self._handle_detect_vds_app_log(case_number, command, data)
                
            elif command == "SetCanMsgDelayTime":
                self._handle_set_can_msg_delay_time(case_number, command, data)
                
            elif command == "CheckCanMsgThreading":
                self._handle_check_can_msg_threading(case_number, command)
                
            elif command == "PushExceptCanMsg":
                self._handle_push_except_can_msg(case_number, command, data)
                
            elif command == "recordCanMsg":
                self._handle_record_can_msg(case_number, command, data)
                
            elif command == "LinSender":
                self._handle_lin_sender(case_number, command, data)
                
            elif command == "LinStopSender":
                self._handle_lin_stop_sender(case_number, command, data)
                
            elif command == "MarkDtcNormalMsg":
                self._handle_mark_dtc_normal_msg(case_number, command, data)
                
            else:
                logger.warning(f"未处理的系统命令: {command}")
                self._emit_failure(case_number, command, f"未支持的命令: {command}")
                
        except Exception as e:
            logger.error(f"系统命令执行失败 {command}: {e}")
            self._emit_failure(case_number, command, f"命令执行异常: {str(e)}")
    
    def _handle_set_delay_time(self, case_number: str, command: str, data: str):
        """处理设置延时时间"""
        from utils.TimerManager import timer_manager
        
        time_size = 0
        if "," in data:
            params = self._parse_data_params(data, 2)
            value = self._safe_float(params[0])
            unit = params[1]
            if operator.eq("s", unit):
                time_size = value
            elif operator.eq("ms", unit):
                time_size = value / 1000
        else:
            time_size = self._safe_float(data)
        
        if time_size >= 2:
            timer_manager.set_params(case_number, command, time_size)
            timer_manager.stop_timer()
            timer_manager.start_timer()
        else:
            threading.Timer(interval=time_size, function=self._set_delay_time_callback,
                          args=(case_number, command, time_size)).start()
    
    def _handle_set_random_delay_time(self, case_number: str, command: str, data: str):
        """处理设置随机延时时间"""
        from utils.TimerManager import timer_manager
        
        datas = data.split(",")
        time_size = 0
        
        if len(datas) == 2:
            value = random.uniform(self._safe_float(datas[0]), self._safe_float(datas[1]))
            time_size = round(value, 3)
        elif len(datas) == 3:
            value = random.uniform(self._safe_float(datas[0]), self._safe_float(datas[1]))
            unit = datas[2]
            if operator.eq("s", unit):
                time_size = round(value, 3)
            elif operator.eq("ms", unit):
                time_size = round(value / 1000, 3)
        
        if time_size >= 2:
            timer_manager.set_params(case_number, command, time_size)
            timer_manager.stop_timer()
            timer_manager.start_timer()
        else:
            threading.Timer(interval=time_size, function=self._set_delay_time_callback,
                          args=(case_number, command, time_size)).start()
    
    def _set_delay_time_callback(self, case_number: str, command: str, time_size: float):
        """延时时间回调"""
        from utils.SignalsManager import signals_manager
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", f"设置延时：{time_size}秒")
    
    def _handle_update_fw_by_random(self, case_number: str, command: str, data: str):
        """处理随机固件更新"""
        from bat.BatManager import bat_manager
        bat_manager.handle_update_fw_by_random(case_number, command, data)
    
    def _handle_update_fw_by_step(self, case_number: str, command: str, data: str):
        """处理步进固件更新"""
        from bat.BatManager import bat_manager
        bat_manager.handle_update_fw_by_step(case_number, command, data)
    
    def _handle_execute_adb_cmd(self, case_number: str, command: str, data: str):
        """处理执行ADB命令"""
        from adb.AdbManager import adb_manager
        adb_manager.handle_execute_adb_cmd(case_number, command, data)
    
    def _handle_execute_bat(self, case_number: str, command: str, data: str):
        """处理执行BAT文件"""
        from bat.BatManager import bat_manager
        bat_manager.handle_execute_bat(case_number, command, data)
    
    def _handle_execute_block_bat(self, case_number: str, command: str, data: str):
        """处理执行阻塞BAT文件"""
        from bat.BatManager import bat_manager
        bat_manager.handle_execute_block_bat(case_number, command, data)
    
    def _handle_stop_cycle_can_msg(self, case_number: str, command: str, data: str):
        """处理停止循环CAN消息"""
        from adb.AdbConnectDevice import adb_connect_device
        adb_connect_device.stop_cycle_can_msg(int(data, 16))
        self._emit_success(case_number, command)
    
    def _handle_clear_cycle_can_msg(self, case_number: str, command: str):
        """处理清除循环CAN消息"""
        from adb.AdbConnectDevice import adb_connect_device
        adb_connect_device.clear_cycle_can_msg()
        self._emit_success(case_number, command)
    
    def _handle_start_auto_cycle_press(self, case_number: str, command: str, data: str):
        """处理开始自动循环按压"""
        from touch.AutoKeyPress import auto_key_press
        
        if "," in data:
            params = self._parse_data_params(data, 2)
            delay = params[0]
            counter = params[1]
            auto_key_press.start_auto_cycle_press(delay, counter)
        else:
            auto_key_press.start_auto_cycle_press()
        
        self._emit_success(case_number, command)
    
    def _handle_stop_auto_cycle_press(self, case_number: str, command: str):
        """处理停止自动循环按压"""
        from touch.AutoKeyPress import auto_key_press
        auto_key_press.stop_mb()
        self._emit_success(case_number, command)
    
    def _handle_start_custom_cycle_press(self, case_number: str, command: str, data: str):
        """处理开始自定义循环按压"""
        from touch.AutoKeyPress import auto_key_press
        
        if "," in data:
            params = self._parse_data_params(data, 2)
            repeat = self._safe_int(params[0])
            position = params[1]
            auto_key_press.reset_custom_params()
            auto_key_press.start_custom_cycle_press(repeat=repeat, position=position)
        else:
            auto_key_press.reset_custom_params()
            auto_key_press.start_custom_cycle_press()
        
        self._emit_success(case_number, command)
    
    def _handle_stop_custom_cycle_press(self, case_number: str, command: str):
        """处理停止自定义循环按压"""
        from touch.AutoKeyPress import auto_key_press
        auto_key_press.stop_custom_cycle_press()
        self._emit_success(case_number, command)
    
    def _handle_detect_mcu_log(self, case_number: str, command: str, data: str):
        """处理检测MCU日志"""
        from utils.LogReader import mcu_log_reader
        from utils.LogManager import log_manager
        
        if "," in data:
            params = self._parse_data_params(data, 2)
            mark = params[0]
            delay_time = self._safe_int(params[1])
            mcu_log_reader.delay_stop_time = delay_time
            log_manager.parse_mcu_log(case_number, command, mark)
    
    def _handle_detect_soc_log(self, case_number: str, command: str, data: str):
        """处理检测SOC日志"""
        from utils.LogReader import soc_log_reader
        from utils.LogManager import log_manager
        
        params = self._parse_data_params(data, 2)
        delay_time = self._safe_int(params[1])
        soc_log_reader.delay_stop_time = delay_time
        log_manager.parse_soc_log(case_number, command, data)
    
    def _handle_detect_os_log(self, case_number: str, command: str, data: str):
        """处理检测OS日志"""
        from utils.LogReader import logcat_reader
        from utils.LogManager import log_manager
        
        params = self._parse_data_params(data, 2)
        delay_time = self._safe_int(params[1])
        logcat_reader.delay_stop_time = delay_time
        log_manager.parse_os_log(case_number, command, data)
    
    def _handle_detect_vds_app_log(self, case_number: str, command: str, data: str):
        """处理检测VDS APP日志"""
        from utils.LogReader import logcat_reader
        from utils.LogManager import log_manager
        
        params = self._parse_data_params(data, 2)
        delay_time = self._safe_int(params[1])
        logcat_reader.delay_stop_time = delay_time
        log_manager.parse_vds_app_log(case_number, command, data)
    
    def _handle_set_can_msg_delay_time(self, case_number: str, command: str, data: str):
        """处理设置CAN消息延时时间"""
        from adb.AdbConnectDevice import adb_connect_device
        
        delay_time = self._safe_float(data)
        adb_connect_device.reset_can_msg_delay(delay_time)
        self._emit_success(case_number, command)
    
    def _handle_check_can_msg_threading(self, case_number: str, command: str):
        """处理检查CAN消息线程"""
        from adb.AdbConnectDevice import adb_connect_device
        
        adb_connect_device.check_recv_msg_flag = True
        for thread in threading.enumerate():
            if thread.name == "check_recv_can_msg_t":
                break
        else:
            threading.Thread(target=adb_connect_device.check_recv_can_msg, name="check_recv_can_msg_t").start()
        
        self._emit_success(case_number, command)
    
    def _handle_push_except_can_msg(self, case_number: str, command: str, data: str):
        """处理推送异常CAN消息"""
        from adb.AdbConnectDevice import adb_connect_device
        
        params = self._parse_data_params(data, 2)
        expect_id = params[0]
        expect_content = params[1]
        adb_connect_device.check_recv_msg_except = [expect_id, expect_content]
        self._emit_success(case_number, command)
    
    def _handle_record_can_msg(self, case_number: str, command: str, data: str):
        """处理记录CAN消息"""
        from adb.AdbConnectDevice import adb_connect_device
        
        delay_time = self._safe_int(data.split(",")[0])
        try:
            adb_connect_device.record_can_recv_msg(delay_time)
            self._emit_success(case_number, command, "")
        except Exception as e:
            self._emit_failure(case_number, command, str(e.args))
    
    def _handle_lin_sender(self, case_number: str, command: str, data: str):
        """处理LIN发送"""
        from view.LinDebugWidget import lin_sender
        from adb.AdbConnectDevice import adb_connect_device
        
        try:
            params = self._parse_data_params(data, 4)
            device = self._safe_int(params[0])
            pid = params[1].strip()
            lin_data = params[2].strip()
            interval = self._safe_float(params[3])
        except (ValueError, IndexError):
            self._emit_failure(case_number, command, "输入数据不规范")
            return
        
        if device == 1:
            if not lin_sender.is_connected:
                self._emit_failure(case_number, command, "设备未连接")
                return
            
            if interval > 0:
                res, msg = lin_sender.start_periodic_send(pid, lin_data, interval)
                result = "PASS" if res else "NG"
                self._emit_success(case_number, command, msg) if result == "PASS" else self._emit_failure(case_number, command, msg)
            else:
                res, msg = lin_sender.send_message(pid, lin_data)
                result = "PASS" if res else "NG"
                self._emit_success(case_number, command, msg) if result == "PASS" else self._emit_failure(case_number, command, msg)
                
        elif device == 2:
            if adb_connect_device.tsmaster_lin_bus is None:
                self._emit_failure(case_number, command, "设备未连接")
                return
            
            if interval > 0:
                res, msg = adb_connect_device.tsmaster_lin_bus.start_periodic_send(pid, lin_data, interval)
                result = "PASS" if res else "NG"
                self._emit_success(case_number, command, msg) if result == "PASS" else self._emit_failure(case_number, command, msg)
            else:
                res, msg = adb_connect_device.tsmaster_lin_bus.send_message(pid + " " + lin_data)
                result = "PASS" if (res == 0) else "NG"
                self._emit_success(case_number, command, msg) if result == "PASS" else self._emit_failure(case_number, command, msg)
        else:
            self._emit_failure(case_number, command, "设备选择错误")
    
    def _handle_lin_stop_sender(self, case_number: str, command: str, data: str):
        """处理停止LIN发送"""
        from view.LinDebugWidget import lin_sender
        from adb.AdbConnectDevice import adb_connect_device
        
        try:
            pid = data.split(",")[0]
            if pid == '-':
                lin_sender.stop_all_periodic_send()
                if adb_connect_device.tsmaster_lin_bus is not None:
                    adb_connect_device.tsmaster_lin_bus.stop_all_periodic_send()
            else:
                if pid in lin_sender.periodic_threads:
                    lin_sender.stop_periodic_send(pid)
                if adb_connect_device.tsmaster_lin_bus is not None:
                    if pid in adb_connect_device.tsmaster_lin_bus.periodic_threads:
                        adb_connect_device.tsmaster_lin_bus.stop_periodic_send(pid)
            
            self._emit_success(case_number, command)
        except Exception as e:
            self._emit_failure(case_number, command, str(e.args))
    
    def _handle_mark_dtc_normal_msg(self, case_number: str, command: str, data: str):
        """处理标记DTC正常消息"""
        from utils.DtcDetectManager import dtc_detect_manager
        dtc_detect_manager.handle_dtc_detect(case_number, command, data)
