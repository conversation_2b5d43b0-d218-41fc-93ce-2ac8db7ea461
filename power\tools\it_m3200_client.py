import socket
import traceback

import select
from PyQt5.QtCore import QObject, pyqtSignal

from common.LogUtils import logger
from power.constants import CONF
from .thread_runner import Thread<PERSON><PERSON>ner


def flush_socket(socks, lim=0):
    """remove the data present on the socket"""
    input_socks = [socks]
    cnt = 0
    while True:
        i_socks = select.select(input_socks, input_socks, input_socks, 0.0)[0]
        if len(i_socks) == 0:
            break
        for sock in i_socks:
            sock.recv(1024)
        if lim > 0:
            cnt += 1
            if cnt >= lim:
                # avoid infinite loop due to loss of connection
                raise Exception("flush_socket: maximum number of iterations reached")


def to_data(string_data):
    return bytearray(string_data, 'ascii')


class TcpBase(QObject):
    msg_log_signal = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent=parent)

        self._host = None
        self._port = None
        self.timeout = None

        self._is_opened = False
        self._is_init = False
        self._sock = None

    def open(self, host="*************", port=30000, timeout=1.5):
        if not self._is_opened:

            try:
                self._host = host
                self._port = port
                self.timeout = timeout

                if self._sock:
                    self._sock.close()

                self._sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.set_timeout(self.timeout)
                self._sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                self._sock.connect((self._host, self._port))

                self._is_opened = True
                logger.info(f"open True")
                return True
            except Exception as e:
                logger.error(f"open exception: {str(e.args)}")
                return False
        else:
            return True

    def close(self):
        if self._is_opened:
            if self._sock:
                self._sock.close()
                self._sock = None
                self._is_opened = False
        return True

    def _send(self, msg):
        self.flush()
        self._sock.send(msg)

    def _recv(self, length):
        response = self._sock.recv(length)
        return response

    def flush(self):
        flush_socket(self._sock, 3)

    def set_timeout(self, timeout_in_sec):
        if self._sock:
            self._sock.setblocking(timeout_in_sec > 0)
            if timeout_in_sec:
                self._sock.settimeout(timeout_in_sec)


class ITM3200Client(TcpBase):
    def __init__(self):
        super().__init__()

        self.err_msg = ""

        self.runner = None

    def is_opened(self):
        return self._is_opened

    def is_init(self):
        return self._is_init

    def send(self, cmd):
        cmd += "\n"
        cmd = cmd.encode("ascii")
        self._send(cmd)

    def recv(self):
        response = to_data("")
        while True:
            rcv_byte = self._recv(1)
            if rcv_byte == b"\n":
                break
            if rcv_byte:
                response += rcv_byte
        return response.decode("ascii")

    def query(self, cmd):
        self.send(cmd)
        return self.recv()

    def idn(self):
        cmd = "*IDN?"
        self.send(cmd)
        r = self.recv()
        return r

    def err(self):
        cmd = "SYST:ERR?"
        self.send(cmd)
        r = self.recv()
        return r

    def cls(self):
        cmd = "*CLS"
        self.send(cmd)

    def trigger(self):
        cmd = "*TRG"
        self.cls()
        self.send(cmd)
        self._check_cmd(cmd)

    def pause(self, s):
        cmd = f"LIST:PAUS {s}"
        self.cls()
        self.send(cmd)
        self._check_cmd(cmd)

    def stop(self):
        cmd = "LIST OFF"
        self.cls()
        self.send(cmd)
        self._check_cmd(cmd)
        cmd = "OUTP OFF"
        self.cls()
        self.send(cmd)
        self._check_cmd(cmd)

    def get_output_status(self):
        cmd = "OUTPut[:STATe]?"
        self.send(cmd)
        return self.recv()

    def _check_cmd(self, cmd):
        r = self.err()
        self.msg_log_signal.emit(f"{cmd} {r}")
        if r.split(",")[0] != "0":
            self.msg_log_signal.emit(f"{cmd} 执行失败！")
            return False
        return True

    def _run_list(self, index):
        try:
            conf = CONF.get(index)
            voltage = conf.get("voltage")
            current = conf.get("current")
            func = conf.get("function")
            term = conf.get("terminate")
            repeat = conf.get("repeat")
            steps = conf.get("steps")

            self.cls()
            self.msg_log_signal.emit(f"*CLS {self.err()}")

            cmd = "SYSTem:REMote"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return

            cmd = "LIST?"
            self.send(cmd)
            r = self.recv()
            if r == "1":
                cmd = "LIST OFF"
                self.send(cmd)
                if not self._check_cmd(cmd):
                    return

            cmd = f"CURR {current}"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return

            cmd = f"VOLT {voltage}"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return

            cmd = "TRIG:SOUR BUS"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return

            cmd = f"LIST:REC {index}"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return

            cmd = f"LIST:FUNC {func}"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return

            cmd = f"LIST:TERM {term}"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return

            cmd = f"LIST:REP {repeat}"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return

            cmd = f"LIST:STEP:COUN {len(steps)}"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return

            for i, step in enumerate(steps):
                voltage = step.get("voltage")
                current = step.get("current")
                slew = step.get("slew")
                width = step.get("width")

                if func == "VOLTage":
                    cmd = f"LIST:STEP:VOLT {i + 1},{voltage}"
                    self.send(cmd)
                    if not self._check_cmd(cmd):
                        return
                else:
                    cmd = f"LIST:STEP:CURR {i + 1},{current}"
                    self.send(cmd)
                    if not self._check_cmd(cmd):
                        return

                cmd = f"LIST:STEP:SLEW {i + 1},{slew}"
                self.send(cmd)
                if not self._check_cmd(cmd):
                    return

                cmd = f"LIST:STEP:WIDT {i + 1},{width}"
                self.send(cmd)
                if not self._check_cmd(cmd):
                    return

            cmd = f"LIST:SAVE {index}"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return

            cmd = "LIST ON"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return

            cmd = "OUTP ON"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return

            cmd = "TRIG"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return
            self.msg_log_signal.emit("执行成功！")
        except Exception:
            self.msg_log_signal.emit(traceback.format_exc())

    def run_list(self, index):
        if self.runner:
            self.runner.stop()

        self.runner = ThreadRunner(self._run_list, index)
        self.runner.start()

    def _run_fixed(self, mode, volt, curr):
        try:
            self.cls()
            self.msg_log_signal.emit(f"*CLS {self.err()}")

            cmd = "SYSTem:REMote"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return False

            cmd = f"FUNC:MODE FIXed"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return False

            cmd = f"FUNC:PRI {mode}"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return False

            cmd = f"VOLT {volt}"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return False

            cmd = f"CURR {curr}"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return False

            cmd = "OUTP ON"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return False

            return True
        except Exception:
            self.msg_log_signal.emit(traceback.format_exc())
            return False

    def run_fixed(self, mode, volt, curr):
        if self.runner:
            self.runner.stop()

        self.runner = ThreadRunner(self._run_fixed, mode, volt, curr)
        self.runner.start()

    def run_fixed_s(self, mode, volt, curr):
        return self._run_fixed(mode, volt, curr)

    def f_on(self):
        cmd = "OUTP ON"
        self.cls()
        self.send(cmd)
        self._check_cmd(cmd)

    def f_off(self):
        cmd = "OUTP OFF"
        self.cls()
        self.send(cmd)
        self._check_cmd(cmd)

    def output_init(self, volt=0, curr=5):
        try:
            self.cls()
            self.msg_log_signal.emit(f"*CLS {self.err()}")

            cmd = "SYSTem:REMote"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return False

            cmd = f"FUNC:MODE FIXed"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return False

            cmd = f"FUNC:PRI VOLT"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return False

            cmd = f"VOLT {volt}"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return False

            cmd = f"CURR {curr}"
            self.send(cmd)
            if not self._check_cmd(cmd):
                return False

            self._is_init = True
            return True
        except Exception as e:
            logger.error("output_init exception: {}".format(str(e.args)))
            self.msg_log_signal.emit(traceback.format_exc())
            return False

    def output_on(self):
        logger.info("output_on")
        cmd = "OUTP ON"
        self.send(cmd)

    def output_off(self):
        logger.info("output_off")
        cmd = "OUTP OFF"
        self.send(cmd)

    def set_volt(self, volt):
        cmd = f"VOLT {volt}"
        self.send(cmd)

    def set_remote(self):
        cmd = "SYSTem:REMote"
        self.send(cmd)

    def get_current(self):
        try:
            cmd = "FETC:CURR?"
            r = self.query(cmd)
            return float(r)
        except Exception as e:
            logger.error("get_current exception: {}".format(str(e.args)))
            return 0

    def get_voltage(self):
        cmd = "FETC:VOLT?"
        r = self.query(cmd)
        return float(r)

    def get_vcp(self):
        cmd = "FETC?"
        r = self.query(cmd)
        v, c, p = r.split(",")
        return float(v), float(c), float(p)


it_m3200_client = ITM3200Client()

if __name__ == '__main__':
    it_m3200_client.open()
    it_m3200_client.send("LIST:TERMinate?")
    print(it_m3200_client.recv())
