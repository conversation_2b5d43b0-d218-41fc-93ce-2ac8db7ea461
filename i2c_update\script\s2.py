import csv


def process_rw_data(input_file, output_file):
    # 存储处理后的数据
    processed_data = []
    # 当前分组的数据
    current_group = []

    # 读取CSV文件
    with open(input_file, 'r') as f:
        reader = csv.DictReader(f)
        headers = reader.fieldnames

        # 处理每一行数据
        for row in reader:
            if not current_group:
                # 开始新的分组
                current_group.append(row)
            else:
                # 检查是否应该和当前分组合并
                prev_row = current_group[-1]

                # 检查是否是同一个packet的相同操作
                same_operation = (
                        prev_row['Read/Write'] == row['Read/Write'] and
                        prev_row['Packet ID'] == row['Packet ID']
                )

                if same_operation:
                    # 添加到当前分组
                    current_group.append(row)
                else:
                    # 处理当前分组并开始新的分组
                    process_group(current_group, processed_data)
                    current_group = [row]

        # 处理最后一个分组
        if current_group:
            process_group(current_group, processed_data)

    # 写入处理后的数据到新文件
    with open(output_file, 'w', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=headers)
        writer.writeheader()
        writer.writerows(processed_data)


def process_group(group, processed_data):
    """处理一组数据"""
    if not group:
        return

    # 使用第一行作为基础
    merged_row = group[0].copy()

    # 合并Byte值
    bytes_list = [row['Byte'] for row in group]
    merged_row['Byte'] = ' '.join(bytes_list)

    processed_data.append(merged_row)


# 使用示例
if __name__ == "__main__":
    input_file = "D:\work\HWTreeATE\i2c_update\script\q.csv"
    output_file = "output.csv"
#
#     # 创建示例输入文件
#     sample_data = '''Time [s],Packet ID,Byte,Read/Write,ACK/NAK,Address/Data
# 1.0535242000,1,0xC0,W,NAK,A
# 1.0535574000,1,0x00,W,NAK,D
# 1.0535902000,1,0x13,W,NAK,D
# 1.0536266000,1,0xC1,R,NAK,A
# 1.0536504000,1,0xFF,R,NAK,D
# 1.0536896000,2,0x90,W,ACK,A'''
#
#     # 创建示例输入文件
#     with open(input_file, 'w', newline='') as f:
#         f.write(sample_data)

    # 处理数据
    process_rw_data(input_file, output_file)

    # 打印处理结果
    print("处理后的数据:")
    with open(output_file, 'r') as f:
        print(f.read())