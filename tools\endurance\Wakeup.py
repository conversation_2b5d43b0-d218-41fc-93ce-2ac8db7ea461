from common.modbus.BaseIO import BaseIO
from tools.endurance.Worker import Worker


class Wakeup<PERSON><PERSON>ker(Worker):
    """
    执行休眠、唤醒的耐久测试    
    """

    def __init__(self, client: BaseIO, channel, wake_time, sleep_time, count=0):
        super(Wakeup<PERSON><PERSON>ker, self).__init__(interval=1000)
        self.channel = channel
        self.wake_time = wake_time
        self.sleep_time = sleep_time
        self.count = count
        self.number = 0
        self.duration = 0

        self.client: BaseIO = client

        self.task = 0

    def set_wake_time(self, wake_time):
        self.wake_time = wake_time

    def set_sleep_time(self, sleep_time):
        self.sleep_time = sleep_time

    def set_count(self, count):
        self.count = count

    def process(self):
        # print(f'client process: {self.client.is_open()}')
        if self.client is not None and self.client.is_open():
            if self.count == 0 or self.number < self.count:
                if self.task == 0:
                    print(f'wakeup time: {self.duration}s')
                    if self.duration == 0:
                        self.client.power_on(self.channel)
                    elif self.duration >= self.wake_time:
                        self.task = 1
                        self.duration = 0
                        self.client.power_off(self.channel)
                    else:
                        self.client.read_electric_current()
                else:
                    print(f'sleep time: {self.duration}s')
                    if self.duration == 0:
                        self.client.power_off(self.channel)
                    elif self.duration >= self.sleep_time:
                        self.task = 0
                        self.duration = 0
                        self.client.power_on(self.channel)
                self.duration += 1

    def finish(self):
        print('worker is finishing')
        self.number = 0
        self.task = 0
        self.duration = 0

    def reset(self):
        self.number = 0
