import time

from control_board.auto_test_m.ctr_card import ctr_card


def wait_for_device(timeout=3):
    start_time = int(time.time())
    while ctr_card.crd_move_result == 0 and time.time() - start_time < timeout:
        print("ctr_card.crd_move_result:", ctr_card.crd_move_result)
        time.sleep(0.1)
    print("end ctr_card.crd_move_result:", ctr_card.crd_move_result, time.time() - start_time, timeout,
          time.time() - start_time > timeout)

if __name__ == '__main__':
    start_point = [-91668, -15980]

    end_point = [-205948, -15980]

    ctr_card.init()
    ctr_card.go_home()
    pos = {4: 0, 3: 0, 1: -62369, 2: 0, 5: 12573}
    ctr_card.m_axis_trap_move(pos_d=pos)
    while ctr_card.m_axis_trap_move_result != 1:
        time.sleep(0.1)

    ctr_card.setup_coordinate_system()

    end = [end_point]
    start = [start_point]
    for i in range(10):


        print('start', start)
        ctr_card.crd_move(start)
        # wait_for_device(20)

        s = time.time()
        while time.time() - s < 5:
            print('ctr_card.crd_move_result', ctr_card.crd_move_result)
            # if ctr_card.crd_move_result == 1:
            #     break
            time.sleep(0.1)

        print('end', end)
        ctr_card.crd_move(end)
        # wait_for_device(20)

        s = time.time()
        while time.time() - s < 5:
            print('ctr_card.crd_move_result', ctr_card.crd_move_result)
            # if ctr_card.crd_move_result == 1:
            #     break
            time.sleep(0.1)

        # pos = {4: -91668, 3: -15980, 1: -62369, 2: 0, 5: 12573}
        # ctr_card.m_axis_trap_move(pos_d=pos)
        # while ctr_card.m_axis_trap_move_result != 1:
        #     time.sleep(0.1)
        # ctr_card.setup_coordinate_system()

