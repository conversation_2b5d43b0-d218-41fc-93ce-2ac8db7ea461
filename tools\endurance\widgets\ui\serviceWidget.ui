<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>230</width>
    <height>370</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>6</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="Line" name="line_2">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QComboBox" name="comboBox_delay">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>45</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_close">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string>关闭</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QFormLayout" name="formLayout_2">
     <item row="0" column="0">
      <widget class="QLabel" name="label">
       <property name="text">
        <string>唤醒时长：</string>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="label_25">
       <property name="text">
        <string>休眠时长：</string>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="label_26">
       <property name="text">
        <string>循环次数：</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QSpinBox" name="spinBox_on_time">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>45</height>
        </size>
       </property>
       <property name="minimum">
        <number>1</number>
       </property>
       <property name="maximum">
        <number>1000000</number>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QSpinBox" name="spinBox_off_time">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>45</height>
        </size>
       </property>
       <property name="minimum">
        <number>1</number>
       </property>
       <property name="maximum">
        <number>1000000</number>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QSpinBox" name="spinBox_count">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>45</height>
        </size>
       </property>
       <property name="maximum">
        <number>1000000</number>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_5">
     <item>
      <widget class="QPushButton" name="pushButton_update">
       <property name="text">
        <string>刷新</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_save">
       <property name="text">
        <string>保存</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QFormLayout" name="formLayout_4">
     <item row="0" column="0">
      <widget class="QLabel" name="label_10">
       <property name="text">
        <string>剩余次数：</string>
       </property>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="QLabel" name="label_11">
       <property name="text">
        <string>唤醒：</string>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="label_13">
       <property name="text">
        <string>休眠：</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QLineEdit" name="lineEdit_remain_count">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>45</height>
        </size>
       </property>
       <property name="readOnly">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="QProgressBar" name="progressBar_on">
       <property name="value">
        <number>24</number>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QProgressBar" name="progressBar_off">
       <property name="value">
        <number>24</number>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <item>
      <widget class="QPushButton" name="pushButton_start">
       <property name="text">
        <string>启动</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton_stop">
       <property name="text">
        <string>停止</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="Line" name="line">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
