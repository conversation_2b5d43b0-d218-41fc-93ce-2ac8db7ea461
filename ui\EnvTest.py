# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'EnvTest.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_EnvForm(object):
    def setupUi(self, EnvForm):
        EnvForm.setObjectName("EnvForm")
        EnvForm.resize(1600, 900)
        EnvForm.setMinimumSize(QtCore.QSize(1600, 900))
        EnvForm.setStyleSheet("")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(EnvForm)
        self.verticalLayout_4.setContentsMargins(5, 5, 0, 0)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.widget = QtWidgets.QWidget(EnvForm)
        self.widget.setStyleSheet("")
        self.widget.setObjectName("widget")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.widget)
        self.verticalLayout_3.setContentsMargins(10, 10, 10, 10)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout()
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setObjectName("gridLayout")
        self.label_7 = QtWidgets.QLabel(self.widget)
        self.label_7.setMinimumSize(QtCore.QSize(0, 45))
        self.label_7.setStyleSheet("")
        self.label_7.setObjectName("label_7")
        self.gridLayout.addWidget(self.label_7, 0, 4, 1, 1)
        self.pushButtonStop = QtWidgets.QPushButton(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButtonStop.sizePolicy().hasHeightForWidth())
        self.pushButtonStop.setSizePolicy(sizePolicy)
        self.pushButtonStop.setMinimumSize(QtCore.QSize(0, 45))
        self.pushButtonStop.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButtonStop.setStyleSheet("")
        self.pushButtonStop.setObjectName("pushButtonStop")
        self.gridLayout.addWidget(self.pushButtonStop, 2, 2, 1, 1)
        self.comboBoxScenarios = QtWidgets.QComboBox(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.comboBoxScenarios.sizePolicy().hasHeightForWidth())
        self.comboBoxScenarios.setSizePolicy(sizePolicy)
        self.comboBoxScenarios.setMinimumSize(QtCore.QSize(0, 45))
        self.comboBoxScenarios.setStyleSheet("")
        self.comboBoxScenarios.setObjectName("comboBoxScenarios")
        self.comboBoxScenarios.addItem("")
        self.gridLayout.addWidget(self.comboBoxScenarios, 0, 1, 1, 1)
        self.spinBoxFrequency = QtWidgets.QSpinBox(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinBoxFrequency.sizePolicy().hasHeightForWidth())
        self.spinBoxFrequency.setSizePolicy(sizePolicy)
        self.spinBoxFrequency.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBoxFrequency.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.spinBoxFrequency.setStyleSheet("")
        self.spinBoxFrequency.setMinimum(1)
        self.spinBoxFrequency.setObjectName("spinBoxFrequency")
        self.gridLayout.addWidget(self.spinBoxFrequency, 1, 1, 1, 1)
        self.label_4 = QtWidgets.QLabel(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_4.sizePolicy().hasHeightForWidth())
        self.label_4.setSizePolicy(sizePolicy)
        self.label_4.setMinimumSize(QtCore.QSize(0, 45))
        self.label_4.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.label_4.setStyleSheet("")
        self.label_4.setObjectName("label_4")
        self.gridLayout.addWidget(self.label_4, 1, 0, 1, 1)
        self.label_3 = QtWidgets.QLabel(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_3.sizePolicy().hasHeightForWidth())
        self.label_3.setSizePolicy(sizePolicy)
        self.label_3.setMinimumSize(QtCore.QSize(0, 45))
        self.label_3.setStyleSheet("")
        self.label_3.setObjectName("label_3")
        self.gridLayout.addWidget(self.label_3, 2, 5, 1, 1)
        self.pushButtonDelChl = QtWidgets.QPushButton(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButtonDelChl.sizePolicy().hasHeightForWidth())
        self.pushButtonDelChl.setSizePolicy(sizePolicy)
        self.pushButtonDelChl.setMinimumSize(QtCore.QSize(0, 45))
        self.pushButtonDelChl.setStyleSheet("")
        self.pushButtonDelChl.setObjectName("pushButtonDelChl")
        self.gridLayout.addWidget(self.pushButtonDelChl, 2, 4, 1, 1)
        self.label = QtWidgets.QLabel(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setMinimumSize(QtCore.QSize(0, 45))
        self.label.setStyleSheet("")
        self.label.setObjectName("label")
        self.gridLayout.addWidget(self.label, 0, 0, 1, 1)
        self.comboBoxDevice = QtWidgets.QComboBox(self.widget)
        self.comboBoxDevice.setMinimumSize(QtCore.QSize(0, 45))
        self.comboBoxDevice.setStyleSheet("")
        self.comboBoxDevice.setObjectName("comboBoxDevice")
        self.comboBoxDevice.addItem("")
        self.comboBoxDevice.addItem("")
        self.comboBoxDevice.addItem("")
        self.gridLayout.addWidget(self.comboBoxDevice, 0, 3, 1, 1)
        self.label_5 = QtWidgets.QLabel(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_5.sizePolicy().hasHeightForWidth())
        self.label_5.setSizePolicy(sizePolicy)
        self.label_5.setMinimumSize(QtCore.QSize(0, 45))
        self.label_5.setStyleSheet("")
        self.label_5.setObjectName("label_5")
        self.gridLayout.addWidget(self.label_5, 1, 2, 1, 1)
        self.pushButtonAdd = QtWidgets.QPushButton(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButtonAdd.sizePolicy().hasHeightForWidth())
        self.pushButtonAdd.setSizePolicy(sizePolicy)
        self.pushButtonAdd.setMinimumSize(QtCore.QSize(0, 45))
        self.pushButtonAdd.setStyleSheet("")
        self.pushButtonAdd.setObjectName("pushButtonAdd")
        self.gridLayout.addWidget(self.pushButtonAdd, 1, 4, 1, 1)
        self.spinBoxTestTime = QtWidgets.QSpinBox(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinBoxTestTime.sizePolicy().hasHeightForWidth())
        self.spinBoxTestTime.setSizePolicy(sizePolicy)
        self.spinBoxTestTime.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBoxTestTime.setStyleSheet("")
        self.spinBoxTestTime.setMaximum(99999)
        self.spinBoxTestTime.setProperty("value", 1)
        self.spinBoxTestTime.setObjectName("spinBoxTestTime")
        self.gridLayout.addWidget(self.spinBoxTestTime, 0, 5, 1, 1)
        self.spinBoxChl = QtWidgets.QSpinBox(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinBoxChl.sizePolicy().hasHeightForWidth())
        self.spinBoxChl.setSizePolicy(sizePolicy)
        self.spinBoxChl.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBoxChl.setStyleSheet("")
        self.spinBoxChl.setMinimum(1)
        self.spinBoxChl.setObjectName("spinBoxChl")
        self.gridLayout.addWidget(self.spinBoxChl, 1, 5, 1, 1)
        self.spinBoxTemperatureTime = QtWidgets.QSpinBox(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinBoxTemperatureTime.sizePolicy().hasHeightForWidth())
        self.spinBoxTemperatureTime.setSizePolicy(sizePolicy)
        self.spinBoxTemperatureTime.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBoxTemperatureTime.setStyleSheet("")
        self.spinBoxTemperatureTime.setObjectName("spinBoxTemperatureTime")
        self.gridLayout.addWidget(self.spinBoxTemperatureTime, 1, 3, 1, 1)
        self.label_2 = QtWidgets.QLabel(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_2.sizePolicy().hasHeightForWidth())
        self.label_2.setSizePolicy(sizePolicy)
        self.label_2.setMinimumSize(QtCore.QSize(0, 45))
        self.label_2.setStyleSheet("")
        self.label_2.setObjectName("label_2")
        self.gridLayout.addWidget(self.label_2, 0, 2, 1, 1)
        self.pushButtonPause = QtWidgets.QPushButton(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButtonPause.sizePolicy().hasHeightForWidth())
        self.pushButtonPause.setSizePolicy(sizePolicy)
        self.pushButtonPause.setMinimumSize(QtCore.QSize(0, 45))
        self.pushButtonPause.setStyleSheet("")
        self.pushButtonPause.setObjectName("pushButtonPause")
        self.gridLayout.addWidget(self.pushButtonPause, 2, 1, 1, 1)
        self.pushButtonStart = QtWidgets.QPushButton(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButtonStart.sizePolicy().hasHeightForWidth())
        self.pushButtonStart.setSizePolicy(sizePolicy)
        self.pushButtonStart.setMinimumSize(QtCore.QSize(0, 45))
        self.pushButtonStart.setStyleSheet("")
        self.pushButtonStart.setObjectName("pushButtonStart")
        self.gridLayout.addWidget(self.pushButtonStart, 2, 0, 1, 1)
        self.gridLayout.setColumnMinimumWidth(0, 1)
        self.gridLayout.setColumnMinimumWidth(1, 1)
        self.gridLayout.setColumnMinimumWidth(2, 1)
        self.gridLayout.setColumnMinimumWidth(3, 1)
        self.gridLayout.setColumnMinimumWidth(4, 1)
        self.gridLayout.setColumnMinimumWidth(5, 1)
        self.gridLayout.setRowMinimumHeight(0, 1)
        self.gridLayout.setRowMinimumHeight(1, 1)
        self.gridLayout.setRowMinimumHeight(2, 1)
        self.gridLayout.setColumnStretch(0, 1)
        self.gridLayout.setColumnStretch(1, 1)
        self.gridLayout.setColumnStretch(2, 1)
        self.gridLayout.setColumnStretch(3, 1)
        self.gridLayout.setColumnStretch(4, 1)
        self.gridLayout.setColumnStretch(5, 1)
        self.gridLayout.setRowStretch(0, 1)
        self.gridLayout.setRowStretch(1, 1)
        self.gridLayout.setRowStretch(2, 1)
        self.verticalLayout_2.addLayout(self.gridLayout)
        self.verticalLayout_2.setStretch(0, 2)
        self.horizontalLayout_2.addLayout(self.verticalLayout_2)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setContentsMargins(0, -1, -1, -1)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.plainTextEdit = QtWidgets.QPlainTextEdit(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.plainTextEdit.sizePolicy().hasHeightForWidth())
        self.plainTextEdit.setSizePolicy(sizePolicy)
        self.plainTextEdit.setStyleSheet("")
        self.plainTextEdit.setObjectName("plainTextEdit")
        self.horizontalLayout.addWidget(self.plainTextEdit)
        self.horizontalLayout_2.addLayout(self.horizontalLayout)
        self.verticalLayout_3.addLayout(self.horizontalLayout_2)
        self.tableWidget = QtWidgets.QTableWidget(self.widget)
        self.tableWidget.setStyleSheet("")
        self.tableWidget.setObjectName("tableWidget")
        self.tableWidget.setColumnCount(0)
        self.tableWidget.setRowCount(0)
        self.verticalLayout_3.addWidget(self.tableWidget)
        self.verticalLayout_3.setStretch(0, 1)
        self.verticalLayout_3.setStretch(1, 3)
        self.verticalLayout_4.addWidget(self.widget)

        self.retranslateUi(EnvForm)
        QtCore.QMetaObject.connectSlotsByName(EnvForm)

    def retranslateUi(self, EnvForm):
        _translate = QtCore.QCoreApplication.translate
        EnvForm.setWindowTitle(_translate("EnvForm", "Form"))
        self.label_7.setText(_translate("EnvForm", "测试时间(min):"))
        self.pushButtonStop.setText(_translate("EnvForm", "停止"))
        self.comboBoxScenarios.setItemText(0, _translate("EnvForm", "温升"))
        self.label_4.setText(_translate("EnvForm", "采集频率(s/次):"))
        self.label_3.setText(_translate("EnvForm", "默认最后一个通道为环境通道"))
        self.pushButtonDelChl.setText(_translate("EnvForm", "删除通道"))
        self.label.setText(_translate("EnvForm", "测试场景:"))
        self.comboBoxDevice.setItemText(0, _translate("EnvForm", "TP700"))
        self.comboBoxDevice.setItemText(1, _translate("EnvForm", "TP1000"))
        self.comboBoxDevice.setItemText(2, _translate("EnvForm", "TP710A"))
        self.label_5.setText(_translate("EnvForm", "温差时间(s)"))
        self.pushButtonAdd.setText(_translate("EnvForm", "添加通道"))
        self.label_2.setText(_translate("EnvForm", "测试设备:"))
        self.pushButtonPause.setText(_translate("EnvForm", "暂停"))
        self.pushButtonStart.setText(_translate("EnvForm", "启动"))
        self.plainTextEdit.setPlainText(_translate("EnvForm", "测试方法:"))
