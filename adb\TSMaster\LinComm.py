
import time
from threading import Lock
import datetime # 导入 datetime 模块
try:
    from common.LogUtils import logger
except ImportError:
    # 调试使用的日志记录器
    import logging
    logger = logging.getLogger("LinComm")
    logger.setLevel(logging.INFO)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    
    # 添加处理器到日志记录器
    logger.addHandler(console_handler)
    
    logger.info("使用独立日志记录器")

class LinCommunicator:
    def __init__(self, baudrate=19.2, channel=0):
        import libTSCANAPI as tscan
        self.HwHandle = tscan.c_size_t(0)
        self.baudrate = baudrate
        self.channel = channel
        self.is_connected = False
        self.lock = Lock()
        self.name = ''
        self.status = False
        self.periodic_threads = {}  # 格式: {pid: (thread, stop_event)}

    def connect(self):
        try:
            import libTSCANAPI as tscan
            tscan.initialize_lib_tscan(True, True, False)
            res = tscan.tsapp_connect(b'', self.HwHandle)
            if res == 0:
                self.is_connected = True
                self.status = True
                tscan.tsapp_configure_baudrate_lin(self.HwHandle, self.channel, self.baudrate, 1)
                tscan.tslin_set_node_funtiontype(self.HwHandle, self.channel, tscan.T_LIN_NODE_FUNCTION.T_MASTER_NODE)
            else:
                # 连接失败
                tscan.tsapp_disconnect_by_handle(self.HwHandle)
                logger.error(f"Failed to connect to LIN device, error code: {res}")

            # 注册回调放在连接成功之后
            if self.is_connected:
                if self.register_callback() != 0:
                    # 记录回调注册失败
                    logger.error("Callback registration failed, terminating connection attempt.")
                    self.disconnect() # 尝试断开连接
                    return -1
            
            return res
        except Exception as e:
            # 记录连接过程中的异常
            logger.exception(f"Exception during connect: {str(e)}")
            return -1

    def disconnect(self):
        # 移除尝试断开的打印
        if self.is_connected:
            try:
                import libTSCANAPI as tscan
                #res = tscan.tsapp_unregister_event_lin_whandle (self.HwHandle, self.ONLINEvent)
                res = tscan.tsapp_disconnect_by_handle(self.HwHandle)
                self.is_connected = False
                self.status = False
                if res == 0:
                    # 移除成功断开的打印
                    pass
                else:
                    # 记录断开失败
                    logger.error(f"Failed to disconnect from LIN device, error code: {res}")
                return res
            except Exception as e:
                # 记录断开过程中的异常
                logger.exception(f"Exception during disconnect: {str(e)}")
                return -1
        else:
            # 如果设备未连接时尝试断开，可以记录一个警告或调试信息（如果需要）
            logger.warning("Attempted to disconnect, but device was not connected.")
            return -1 # 表示未执行断开操作

    def send_message(self, lin_msg):
        import libTSCANAPI as tscan
        if not self.is_connected:
            # 记录未连接无法发送的错误
            logger.error("tsmaster device not connected")
            return -1, "Device not connected"
        try:
            with self.lock:
                # 移除准备发送的打印
                idf = int(lin_msg.split(" ")[0], 16)
                data = [int(lin_msg.split(" ")[i], 16) for i in range(1, len(lin_msg.split(" ")))]
                while len(data) < 8:
                    data.append(0x00)
                lin_msg_obj = tscan.TLIBLIN(FIdxChn=self.channel, FDLC=8, FIdentifier=idf, FProperties=1, FData=tuple(data)) # 使用 tuple
                res = tscan.tsapp_transmit_lin_async(self.HwHandle, lin_msg_obj)
                time.sleep(0.01) # 短暂等待，确保API调用完成
                if res == 0:
                    pass
                else:
                    # 记录发送失败
                    logger.error(f"Failed to send message: ID=0x{idf:02X}, Data={data}, error code: {res}")
                return res, lin_msg
        except Exception as e:
            # 记录发送过程中的异常
            logger.exception(f"Exception during send_message for msg='{lin_msg}': {str(e)}")
            return -1, f"NG: {str(e)}"

    def On_LIN_Event(self, obj, msg):
        try:
            # 解析报文信息 (这部分保留，因为如果解析出错也应该记录)
            timestamp_us = msg.contents.FTimeUs
            dt_object = datetime.datetime.fromtimestamp(timestamp_us / 1000000, tz=datetime.timezone.utc)
            formatted_time = dt_object.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

            is_tx = (msg.contents.FProperties & 0x01) != 0
            direction = "SEND" if is_tx else "RECE"
            dlc = msg.contents.FDLC
            pid = msg.contents.FIdentifier
            data = list(msg.contents.FData[:dlc]) # 只取 DLC 长度的数据

            message = f"[{formatted_time}]-{direction}:{dlc} PID:0x{pid:02X}, Data:{['0x{:02X}'.format(b) for b in data]}"
            logger.info(message) # 记录接收到的消息

        except Exception as e:
            # 记录回调处理中的异常
            logger.error(f"处理报文时发生错误: {e}")

    def register_callback(self):
        import libTSCANAPI as tscan
        try:
            # 注意：确保 self.ONLINEvent 在对象生命周期内保持有效
            self.ONLINEvent = tscan.OnTx_RxFUNC_LIN_WHandle(self.On_LIN_Event)
            ret = tscan.tsapp_register_event_lin_whandle(self.HwHandle, self.ONLINEvent)
            if ret != 0:
                logger.error(f"Failed to register LIN event callback, error code: {ret}")
            return ret
        except Exception as e:
            # 记录回调注册过程中的异常
            logger.exception(f"Exception during callback registration: {str(e)}")
            return -1

    def get_device_info(self):
        """获取设备信息 (主要用于调试或状态检查)"""
        try:
            info = {
                'connected': self.is_connected,
                'status': self.status,
                'baudrate': self.baudrate,
                'channel': self.channel,
                'handle_value': self.HwHandle.value # 获取句柄的实际值
            }
            # 移除设备信息的打印，如果需要查看，可以调用此方法并手动打印或记录
            # self.logger.debug(f"Device info: {info}") # 可选：记录为 DEBUG 级别
            return info
        except Exception as e:
            # 记录获取信息时的异常
            logger.exception(f"Exception getting device info: {str(e)}")
            return None

    # 添加周期性发送方法
    def start_periodic_send(self, pid, lin_data, interval):
        """启动周期性发送LIN消息
        
        Args:
            pid: LIN消息ID
            lin_data: LIN消息数据
            interval: 发送间隔(毫秒)
            
        Returns:
            (bool, str): 成功标志和消息
        """
        import threading
        
        # 先停止之前可能运行的相同PID的周期性发送
        self.stop_periodic_send(pid)
        
        # 创建停止事件
        stop_event = threading.Event()
        
        # 创建并启动线程
        def send_task():
            while not stop_event.is_set():
                self.send_message(pid + " " + lin_data)
                if stop_event.wait(float(interval)/1000.0):
                    break
        
        thread = threading.Thread(target=send_task, daemon=True)
        self.periodic_threads[pid] = (thread, stop_event)
        thread.start()
        return True, "周期性发送已启动"

    def stop_periodic_send(self, pid):
        """停止特定PID的周期性发送
        
        Args:
            pid: 要停止发送的LIN消息ID
            
        Returns:
            (bool, str): 成功标志和消息
        """
        if pid in self.periodic_threads:
            thread, stop_event = self.periodic_threads[pid]
            stop_event.set()
            thread.join(1.0)  # 等待最多1秒
            del self.periodic_threads[pid]
            return True, "周期性发送已停止"
        return False, "没有找到对应的周期性发送任务"

    def stop_all_periodic_send(self):
        """停止所有周期性发送
        
        Returns:
            (bool, str): 成功标志和消息
        """
        for pid in list(self.periodic_threads.keys()):
            self.stop_periodic_send(pid)
        return True, "所有周期性发送已停止"



def test_lin_communication():
    """测试LIN通信功能"""

    logger.info("\n=== LIN通信测试开始 ===")
    # 创建LIN通信实例
    lin_comm = LinCommunicator(baudrate=19200, channel=0)

    # 连接设备
    logger.info("尝试连接设备...")
    if lin_comm.connect() != 0:
        logger.error("设备连接失败，测试终止") # connect 方法内部已记录具体错误
        return

    # 获取设备信息 (可选)
    device_info = lin_comm.get_device_info()
    if device_info:
         logger.info(f"设备信息: {device_info}")
    else:
         logger.warning("获取设备信息失败") # get_device_info 内部已记录具体错误


    # 测试消息发送
    test_messages = [
        "0x2E 01 02 03 04 05 06 07 08",  # 标准8字节消息
        "0x2E 01 02 03",                 # 短消息（会自动补零）
        "0x2E FF FF FF FF FF FF FF FF"   # 全1消息
    ]

    logger.info("\n开始发送测试消息...")
    all_sent_ok = True
    for msg in test_messages:
        logger.info(f"发送消息: {msg}")
        if lin_comm.send_message(msg) != 0:
            logger.error(f"消息发送失败: {msg}") # send_message 内部已记录具体错误
            all_sent_ok = False
        time.sleep(1)  # 等待1秒后发送下一条消息

    if all_sent_ok:
        logger.info("所有测试消息已尝试发送。")
    else:
        logger.warning("部分测试消息发送失败。")

    logger.info("\n等待接收消息 (持续10秒)... 按 Ctrl+C 中断")
    try:
        # 保持主线程活动以接收回调消息
        time.sleep(10)
        logger.info("消息接收等待时间结束。")
    except KeyboardInterrupt:
        logger.info("\n用户中断测试")

    # 断开连接
    logger.info("尝试断开连接...")
    lin_comm.disconnect()
    logger.info("\n=== LIN通信测试结束 ===")

if __name__ == "__main__":
    try:
        test_lin_communication()
    except KeyboardInterrupt:
        # Test function logger handles this now
        pass
    except Exception as e:
        # 记录主程序块中的未捕获异常
        logger.critical(f"\n程序发生未预料的严重错误: {str(e)}", exc_info=True)
    finally:
        logger.info("\n程序结束")