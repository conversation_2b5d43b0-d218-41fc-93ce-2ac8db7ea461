[KERNELDLL_VCI_CAN]
COUNT=33
START=1
1=PCI51XXE.dll 1 32 0
2=PCI9810.dll 2 32 0
3=USBCAN.dll 3 32 1
4=USBCAN.dll 4 32 1
5=PCI9820.dll 5 32 0
6=PCI51XXE.dll 7 32 0
7=CANETE.dll 12 8 0
8=PCI9840B.dll 14 32 0
9=PCI9820I.dll 16 32 0
10=CANET_TCP.dll 17 32 0
11=pci50xx_u.dll 19 8 0
12=USBCAN_E_64.dll 20 8 1
13=USBCAN_E_64.dll 21 8 1
14=pci50xx_u.dll 22 8 0
15=topcliff_can.dll 23 8 0
16=pcie9221.dll 24 32 0
17=CANWIFI_TCP.dll 25 8 0
18=CANWIFI_UDP.dll 26 8 0
19=pcie9120.dll 27 32 0
20=pcie9110.dll 28 32 0
21=pcie9140.dll 29 32 0
22=pci5010p.dll 30 32 0
23=USBCAN_4E_U_X64.dll 31 32 1
24=CANDTU_x64.DLL 32 8 0
25=CANDTU_MINI.dll 33 8 0
26=USBCAN_8E_U_x64.dll 34 32 1
27=CAN_REPLAY.dll 35 8 0
28=CANDTU_NET.dll 36 32 0
29=CANDTU_x64.dll 37 8 0
30=zpcfd.dll 38 32 0
31=zpcfd.dll 39 32 0
32=zpcfd.dll 40 32 0
33=CANDTU_NET.dll 47 32 0
[KERNELDLL_ZLG_CAN]
COUNT=32
START=1
1=VirtualUSBCAN.dll 99 32 0
2=USBCANFD.dll 41 32 1
3=USBCANFD.dll 42 32 1
4=USBCANFD.dll 43 32 1
5=ZlgCloud.dll 46 999 0
6=CANFDCOM.dll 44 32 0
7=CANFDNET.dll 48 32 0
8=CANFDNET.dll 49 32 0
9=CANFDNET.dll 50 32 0
10=CANFDNET.dll 51 32 0
11=CANFDNET.dll 52 32 0
12=CANFDNET.dll 53 32 0
13=CANFDBlue.dll 54 32 0
14=CANFDNET.dll 55 32 0
15=CANFDNET.dll 56 32 0
16=CANFDNET.dll 57 32 0
17=CANFDNET.dll 58 32 0
18=USBCANFD800U.dll 59 32 1
19=xpcfd.dll 60 32 1
20=xpcfd.dll 61 32 1
21=xpcfd.dll 62 32 1
22=xpcfd.dll 63 32 1
23=CANFDNET.dll 64 32 0
24=CANFDNET.dll 65 32 0
25=CANFDNET.dll 66 32 0
26=CANFDNET.dll 67 32 0
27=CANFDNET.dll 68 32 0
28=CANFDNET.dll 69 32 0
29=CANFDNET.dll 70 32 0
30=CANFDNET.dll 71 32 0
31=CANFDNET.dll 72 32 0
32=CANFDNET.dll 73 32 0
[KERNELDLL_CANSCOPE]
COUNT=1
START=1
1=CANDevice.dll 45 32 0
