<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PictureWindow</class>
 <widget class="QMainWindow" name="PictureWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1600</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1600</width>
    <height>900</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QLabel" name="label_8">
        <property name="text">
         <string>分辨率:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSpinBox" name="pic_width">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>45</height>
         </size>
        </property>
        <property name="maximum">
         <number>999999999</number>
        </property>
        <property name="value">
         <number>99</number>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_9">
        <property name="text">
         <string>*</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSpinBox" name="pic_height">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>45</height>
         </size>
        </property>
        <property name="maximum">
         <number>999999999</number>
        </property>
        <property name="value">
         <number>99</number>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_5">
      <item>
       <widget class="QLabel" name="label">
        <property name="text">
         <string>类型:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QRadioButton" name="gray_picture">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>灰阶图片</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QRadioButton" name="chess_picture">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>棋盘格</string>
        </property>
        <property name="autoRepeatDelay">
         <number>300</number>
        </property>
        <property name="autoRepeatInterval">
         <number>100</number>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QRadioButton" name="custom_picture">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>自定义</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QRadioButton" name="radioButton">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>渐变图片</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <item>
       <widget class="QLabel" name="label_5">
        <property name="text">
         <string>灰阶范围:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSpinBox" name="gray_star">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>45</height>
         </size>
        </property>
        <property name="maximum">
         <number>255</number>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_6">
        <property name="text">
         <string>~</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSpinBox" name="gray_end">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>45</height>
         </size>
        </property>
        <property name="maximum">
         <number>255</number>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <widget class="QLabel" name="label_11">
        <property name="text">
         <string>色块1</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="color_1_btn">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>50</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_13">
        <property name="text">
         <string>色块2</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="color_2_btn">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>50</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_7">
        <property name="text">
         <string>宽度方格个数:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSpinBox" name="chess_width">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>45</height>
         </size>
        </property>
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>500</number>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_12">
        <property name="text">
         <string>高度方格个数</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSpinBox" name="spinBox">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>45</height>
         </size>
        </property>
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>500</number>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_4">
      <item>
       <widget class="QLabel" name="label_2">
        <property name="text">
         <string>R:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSpinBox" name="color_r">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>45</height>
         </size>
        </property>
        <property name="maximum">
         <number>255</number>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_3">
        <property name="text">
         <string>G:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSpinBox" name="color_g">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>45</height>
         </size>
        </property>
        <property name="maximum">
         <number>255</number>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_4">
        <property name="text">
         <string>B:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSpinBox" name="color_b">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>45</height>
         </size>
        </property>
        <property name="maximum">
         <number>255</number>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_7">
      <item>
       <widget class="QLabel" name="label_14">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>开始:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_16">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>R:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSpinBox" name="spinBox_2">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>45</height>
         </size>
        </property>
        <property name="maximum">
         <number>255</number>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_17">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>G:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSpinBox" name="spinBox_3">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>45</height>
         </size>
        </property>
        <property name="maximum">
         <number>255</number>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_18">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>B:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSpinBox" name="spinBox_4">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>45</height>
         </size>
        </property>
        <property name="maximum">
         <number>255</number>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_15">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>结束:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_19">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>R:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSpinBox" name="spinBox_5">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>45</height>
         </size>
        </property>
        <property name="specialValueText">
         <string>0</string>
        </property>
        <property name="maximum">
         <number>255</number>
        </property>
        <property name="value">
         <number>255</number>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_20">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>G:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSpinBox" name="spinBox_6">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>45</height>
         </size>
        </property>
        <property name="specialValueText">
         <string>0</string>
        </property>
        <property name="maximum">
         <number>255</number>
        </property>
        <property name="value">
         <number>255</number>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_21">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>B:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSpinBox" name="spinBox_7">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>45</height>
         </size>
        </property>
        <property name="specialValueText">
         <string>0</string>
        </property>
        <property name="maximum">
         <number>255</number>
        </property>
        <property name="value">
         <number>255</number>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_6">
      <item>
       <widget class="QLabel" name="label_10">
        <property name="text">
         <string>保存路径:</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLineEdit" name="path_line">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>45</height>
         </size>
        </property>
        <property name="readOnly">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="path_button">
        <property name="text">
         <string>选择</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <widget class="QPushButton" name="create_button">
      <property name="text">
       <string>创建图片</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1600</width>
     <height>18</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
