import os.path
import xml.etree.ElementTree as ET


def get_release_version():
    release_version = ""
    conf_path = os.path.join(os.getcwd(), "res", "project.xml")
    tree = ET.parse(conf_path)
    root = tree.getroot()
    for child in root:
        if child.tag == "version":
            release_version = child.text

    return release_version


def get_release_time():
    release_time = ""
    conf_path = os.path.join(os.getcwd(), "res", "project.xml")
    tree = ET.parse(conf_path)
    root = tree.getroot()
    for child in root:
        if child.tag == "release":
            release_time = child.text

    return release_time
