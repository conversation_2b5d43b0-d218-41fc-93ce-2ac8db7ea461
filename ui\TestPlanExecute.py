# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'TestPlanExecute.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_TestPlanExecuteForm(object):
    def setupUi(self, TestPlanExecuteForm):
        TestPlanExecuteForm.setObjectName("TestPlanExecuteForm")
        TestPlanExecuteForm.resize(1000, 600)
        TestPlanExecuteForm.setMinimumSize(QtCore.QSize(1000, 600))
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(TestPlanExecuteForm)
        self.verticalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_4.setSpacing(6)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.frame_4 = QtWidgets.QFrame(TestPlanExecuteForm)
        self.frame_4.setObjectName("frame_4")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.frame_4)
        self.verticalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setContentsMargins(25, 0, -1, 0)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.start_btn = QtWidgets.QPushButton(self.frame_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.start_btn.sizePolicy().hasHeightForWidth())
        self.start_btn.setSizePolicy(sizePolicy)
        self.start_btn.setMinimumSize(QtCore.QSize(0, 50))
        self.start_btn.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(False)
        font.setWeight(50)
        self.start_btn.setFont(font)
        self.start_btn.setStyleSheet("")
        self.start_btn.setObjectName("start_btn")
        self.horizontalLayout.addWidget(self.start_btn)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.label = QtWidgets.QLabel(self.frame_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.label.setFont(font)
        self.label.setStyleSheet("")
        self.label.setObjectName("label")
        self.horizontalLayout.addWidget(self.label)
        self.version_info_label = QtWidgets.QLabel(self.frame_4)
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.version_info_label.setFont(font)
        self.version_info_label.setStyleSheet("")
        self.version_info_label.setText("")
        self.version_info_label.setObjectName("version_info_label")
        self.horizontalLayout.addWidget(self.version_info_label)
        self.label_3 = QtWidgets.QLabel(self.frame_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_3.sizePolicy().hasHeightForWidth())
        self.label_3.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.label_3.setFont(font)
        self.label_3.setStyleSheet("")
        self.label_3.setObjectName("label_3")
        self.horizontalLayout.addWidget(self.label_3)
        self.case_pass_count_label = QtWidgets.QLabel(self.frame_4)
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.case_pass_count_label.setFont(font)
        self.case_pass_count_label.setStyleSheet("")
        self.case_pass_count_label.setObjectName("case_pass_count_label")
        self.horizontalLayout.addWidget(self.case_pass_count_label)
        self.label_8 = QtWidgets.QLabel(self.frame_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_8.sizePolicy().hasHeightForWidth())
        self.label_8.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.label_8.setFont(font)
        self.label_8.setStyleSheet("")
        self.label_8.setObjectName("label_8")
        self.horizontalLayout.addWidget(self.label_8)
        self.case_ng_count_label = QtWidgets.QLabel(self.frame_4)
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.case_ng_count_label.setFont(font)
        self.case_ng_count_label.setStyleSheet("")
        self.case_ng_count_label.setObjectName("case_ng_count_label")
        self.horizontalLayout.addWidget(self.case_ng_count_label)
        self.label_6 = QtWidgets.QLabel(self.frame_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_6.sizePolicy().hasHeightForWidth())
        self.label_6.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.label_6.setFont(font)
        self.label_6.setStyleSheet("")
        self.label_6.setObjectName("label_6")
        self.horizontalLayout.addWidget(self.label_6)
        self.case_decision_count_label = QtWidgets.QLabel(self.frame_4)
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.case_decision_count_label.setFont(font)
        self.case_decision_count_label.setStyleSheet("")
        self.case_decision_count_label.setObjectName("case_decision_count_label")
        self.horizontalLayout.addWidget(self.case_decision_count_label)
        self.label_5 = QtWidgets.QLabel(self.frame_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_5.sizePolicy().hasHeightForWidth())
        self.label_5.setSizePolicy(sizePolicy)
        self.label_5.setMinimumSize(QtCore.QSize(0, 0))
        self.label_5.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.label_5.setFont(font)
        self.label_5.setStyleSheet("")
        self.label_5.setObjectName("label_5")
        self.horizontalLayout.addWidget(self.label_5)
        self.comboBox = QtWidgets.QComboBox(self.frame_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.comboBox.sizePolicy().hasHeightForWidth())
        self.comboBox.setSizePolicy(sizePolicy)
        self.comboBox.setMinimumSize(QtCore.QSize(600, 45))
        self.comboBox.setMaximumSize(QtCore.QSize(600, 45))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.comboBox.setFont(font)
        self.comboBox.setStyleSheet("")
        self.comboBox.setObjectName("comboBox")
        self.horizontalLayout.addWidget(self.comboBox)
        self.refresh_plan_btn = QtWidgets.QPushButton(self.frame_4)
        self.refresh_plan_btn.setMinimumSize(QtCore.QSize(0, 45))
        self.refresh_plan_btn.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.refresh_plan_btn.setFont(font)
        self.refresh_plan_btn.setObjectName("refresh_plan_btn")
        self.horizontalLayout.addWidget(self.refresh_plan_btn)
        spacerItem1 = QtWidgets.QSpacerItem(30, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.horizontalLayout.setStretch(0, 1)
        self.horizontalLayout.setStretch(1, 1)
        self.horizontalLayout.setStretch(2, 1)
        self.horizontalLayout.setStretch(3, 5)
        self.horizontalLayout.setStretch(4, 1)
        self.horizontalLayout.setStretch(5, 1)
        self.horizontalLayout.setStretch(6, 1)
        self.horizontalLayout.setStretch(7, 1)
        self.horizontalLayout.setStretch(8, 1)
        self.horizontalLayout.setStretch(9, 1)
        self.horizontalLayout.setStretch(10, 1)
        self.horizontalLayout.setStretch(11, 3)
        self.horizontalLayout.setStretch(13, 1)
        self.verticalLayout_3.addLayout(self.horizontalLayout)
        self.verticalLayout_4.addWidget(self.frame_4)
        self.frame = QtWidgets.QFrame(TestPlanExecuteForm)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.frame.sizePolicy().hasHeightForWidth())
        self.frame.setSizePolicy(sizePolicy)
        self.frame.setStyleSheet("")
        self.frame.setObjectName("frame")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.frame)
        self.horizontalLayout_3.setContentsMargins(10, 10, 10, 10)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.widget = QtWidgets.QWidget(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.widget.sizePolicy().hasHeightForWidth())
        self.widget.setSizePolicy(sizePolicy)
        self.widget.setMinimumSize(QtCore.QSize(0, 0))
        self.widget.setStyleSheet("")
        self.widget.setObjectName("widget")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.widget)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setSpacing(6)
        self.verticalLayout.setObjectName("verticalLayout")
        self.frame_2 = QtWidgets.QFrame(self.widget)
        self.frame_2.setMinimumSize(QtCore.QSize(0, 0))
        self.frame_2.setStyleSheet("")
        self.frame_2.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_2.setObjectName("frame_2")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(self.frame_2)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.cases_info_table_widget = GeneralTableWidget(self.frame_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.cases_info_table_widget.sizePolicy().hasHeightForWidth())
        self.cases_info_table_widget.setSizePolicy(sizePolicy)
        self.cases_info_table_widget.setMinimumSize(QtCore.QSize(400, 0))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.cases_info_table_widget.setFont(font)
        self.cases_info_table_widget.setStyleSheet("")
        self.cases_info_table_widget.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.cases_info_table_widget.setObjectName("cases_info_table_widget")
        self.cases_info_table_widget.setColumnCount(8)
        self.cases_info_table_widget.setRowCount(0)
        item = QtWidgets.QTableWidgetItem()
        self.cases_info_table_widget.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.cases_info_table_widget.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.cases_info_table_widget.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.cases_info_table_widget.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.cases_info_table_widget.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.cases_info_table_widget.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.cases_info_table_widget.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.cases_info_table_widget.setHorizontalHeaderItem(7, item)
        self.horizontalLayout_2.addWidget(self.cases_info_table_widget)
        self.verticalLayout.addWidget(self.frame_2)
        self.horizontalLayout_3.addWidget(self.widget)
        self.verticalLayout_2 = QtWidgets.QVBoxLayout()
        self.verticalLayout_2.setContentsMargins(-1, 0, -1, -1)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.monitor_video_label = QtWidgets.QLabel(self.frame)
        self.monitor_video_label.setStyleSheet("")
        self.monitor_video_label.setText("")
        self.monitor_video_label.setObjectName("monitor_video_label")
        self.verticalLayout_2.addWidget(self.monitor_video_label)
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setContentsMargins(0, 10, -1, -1)
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.label_7 = QtWidgets.QLabel(self.frame)
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.label_7.setFont(font)
        self.label_7.setStyleSheet("")
        self.label_7.setObjectName("label_7")
        self.horizontalLayout_4.addWidget(self.label_7)
        self.execute_case_label = QtWidgets.QLabel(self.frame)
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.execute_case_label.setFont(font)
        self.execute_case_label.setStyleSheet("")
        self.execute_case_label.setText("")
        self.execute_case_label.setObjectName("execute_case_label")
        self.horizontalLayout_4.addWidget(self.execute_case_label)
        spacerItem2 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem2)
        self.label_2 = QtWidgets.QLabel(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_2.sizePolicy().hasHeightForWidth())
        self.label_2.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.label_2.setFont(font)
        self.label_2.setStyleSheet("")
        self.label_2.setObjectName("label_2")
        self.horizontalLayout_4.addWidget(self.label_2)
        self.pass_times_label = QtWidgets.QLabel(self.frame)
        self.pass_times_label.setMinimumSize(QtCore.QSize(50, 0))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.pass_times_label.setFont(font)
        self.pass_times_label.setStyleSheet("")
        self.pass_times_label.setObjectName("pass_times_label")
        self.horizontalLayout_4.addWidget(self.pass_times_label)
        self.label_4 = QtWidgets.QLabel(self.frame)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_4.sizePolicy().hasHeightForWidth())
        self.label_4.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.label_4.setFont(font)
        self.label_4.setStyleSheet("")
        self.label_4.setObjectName("label_4")
        self.horizontalLayout_4.addWidget(self.label_4)
        self.ng_times_label = QtWidgets.QLabel(self.frame)
        self.ng_times_label.setMinimumSize(QtCore.QSize(50, 0))
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.ng_times_label.setFont(font)
        self.ng_times_label.setStyleSheet("")
        self.ng_times_label.setObjectName("ng_times_label")
        self.horizontalLayout_4.addWidget(self.ng_times_label)
        spacerItem3 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_4.addItem(spacerItem3)
        self.horizontalLayout_4.setStretch(0, 1)
        self.horizontalLayout_4.setStretch(1, 1)
        self.horizontalLayout_4.setStretch(2, 1)
        self.horizontalLayout_4.setStretch(3, 1)
        self.horizontalLayout_4.setStretch(4, 1)
        self.horizontalLayout_4.setStretch(5, 1)
        self.horizontalLayout_4.setStretch(6, 1)
        self.horizontalLayout_4.setStretch(7, 1)
        self.verticalLayout_2.addLayout(self.horizontalLayout_4)
        self.frame_3 = QtWidgets.QFrame(self.frame)
        self.frame_3.setMinimumSize(QtCore.QSize(0, 0))
        self.frame_3.setStyleSheet("")
        self.frame_3.setFrameShape(QtWidgets.QFrame.StyledPanel)
        self.frame_3.setFrameShadow(QtWidgets.QFrame.Raised)
        self.frame_3.setObjectName("frame_3")
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout(self.frame_3)
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.steps_info_table_widget = GeneralTableWidget(self.frame_3)
        font = QtGui.QFont()
        font.setBold(False)
        font.setWeight(50)
        self.steps_info_table_widget.setFont(font)
        self.steps_info_table_widget.setStyleSheet("border:none; ")
        self.steps_info_table_widget.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOff)
        self.steps_info_table_widget.setObjectName("steps_info_table_widget")
        self.steps_info_table_widget.setColumnCount(5)
        self.steps_info_table_widget.setRowCount(0)
        item = QtWidgets.QTableWidgetItem()
        self.steps_info_table_widget.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.steps_info_table_widget.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.steps_info_table_widget.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.steps_info_table_widget.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.steps_info_table_widget.setHorizontalHeaderItem(4, item)
        self.horizontalLayout_5.addWidget(self.steps_info_table_widget)
        self.verticalLayout_2.addWidget(self.frame_3)
        self.verticalLayout_2.setStretch(0, 7)
        self.verticalLayout_2.setStretch(2, 4)
        self.horizontalLayout_3.addLayout(self.verticalLayout_2)
        self.horizontalLayout_3.setStretch(0, 3)
        self.horizontalLayout_3.setStretch(1, 2)
        self.verticalLayout_4.addWidget(self.frame)
        self.verticalLayout_4.setStretch(0, 1)
        self.verticalLayout_4.setStretch(1, 16)

        self.retranslateUi(TestPlanExecuteForm)
        QtCore.QMetaObject.connectSlotsByName(TestPlanExecuteForm)

    def retranslateUi(self, TestPlanExecuteForm):
        _translate = QtCore.QCoreApplication.translate
        TestPlanExecuteForm.setWindowTitle(_translate("TestPlanExecuteForm", "Form"))
        self.start_btn.setText(_translate("TestPlanExecuteForm", "启动测试"))
        self.label.setText(_translate("TestPlanExecuteForm", "版本信息："))
        self.label_3.setText(_translate("TestPlanExecuteForm", "用例PASS数："))
        self.case_pass_count_label.setText(_translate("TestPlanExecuteForm", "0"))
        self.label_8.setText(_translate("TestPlanExecuteForm", "用例NG数："))
        self.case_ng_count_label.setText(_translate("TestPlanExecuteForm", "0"))
        self.label_6.setText(_translate("TestPlanExecuteForm", "用例待判定数："))
        self.case_decision_count_label.setText(_translate("TestPlanExecuteForm", "0"))
        self.label_5.setText(_translate("TestPlanExecuteForm", "测试计划："))
        self.refresh_plan_btn.setText(_translate("TestPlanExecuteForm", "刷新"))
        item = self.cases_info_table_widget.horizontalHeaderItem(0)
        item.setText(_translate("TestPlanExecuteForm", "编号"))
        item = self.cases_info_table_widget.horizontalHeaderItem(1)
        item.setText(_translate("TestPlanExecuteForm", "用例ID"))
        item = self.cases_info_table_widget.horizontalHeaderItem(2)
        item.setText(_translate("TestPlanExecuteForm", "用例名称"))
        item = self.cases_info_table_widget.horizontalHeaderItem(3)
        item.setText(_translate("TestPlanExecuteForm", "功能模块"))
        item = self.cases_info_table_widget.horizontalHeaderItem(4)
        item.setText(_translate("TestPlanExecuteForm", "用例版本"))
        item = self.cases_info_table_widget.horizontalHeaderItem(5)
        item.setText(_translate("TestPlanExecuteForm", "总次数"))
        item = self.cases_info_table_widget.horizontalHeaderItem(6)
        item.setText(_translate("TestPlanExecuteForm", "执行次数"))
        item = self.cases_info_table_widget.horizontalHeaderItem(7)
        item.setText(_translate("TestPlanExecuteForm", "执行结果"))
        self.label_7.setText(_translate("TestPlanExecuteForm", " 当前执行用例："))
        self.label_2.setText(_translate("TestPlanExecuteForm", "PASS次数："))
        self.pass_times_label.setText(_translate("TestPlanExecuteForm", "0"))
        self.label_4.setText(_translate("TestPlanExecuteForm", "NG次数："))
        self.ng_times_label.setText(_translate("TestPlanExecuteForm", "0"))
        item = self.steps_info_table_widget.horizontalHeaderItem(0)
        item.setText(_translate("TestPlanExecuteForm", "步骤名称"))
        item = self.steps_info_table_widget.horizontalHeaderItem(1)
        item.setText(_translate("TestPlanExecuteForm", "步骤参数"))
        item = self.steps_info_table_widget.horizontalHeaderItem(2)
        item.setText(_translate("TestPlanExecuteForm", "期望值"))
        item = self.steps_info_table_widget.horizontalHeaderItem(3)
        item.setText(_translate("TestPlanExecuteForm", "测试值"))
        item = self.steps_info_table_widget.horizontalHeaderItem(4)
        item.setText(_translate("TestPlanExecuteForm", "执行结果"))
from view.GeneralTableWidget import GeneralTableWidget
