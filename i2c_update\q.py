
import subprocess
import time


def calculate_checksum(data):
    checksum = 0
    for byte in data:
        if byte == '':
            continue
        byte = int(byte, 16)
        checksum ^= byte
    return f"{checksum:#04x}"



def create_i2c_command(brightness):
    # 亮度值
    brightness_byte = f"{brightness:#04x}"
    # print("brightness_byte",brightness_byte)
    # 前5个字节
    # command_bytes = f"0x22 0x00 0x03 0x02 {brightness_byte}"
    # cmd_list = command_bytes.split(" ")
    command_bytes = f"0xF4 0x00 0x24 0xE3 0x43 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 " \
                    f"0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 " \
                    f"0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 " \
                    f"0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x00 0x11 0x00 0x11"
    cmd_list = command_bytes.split(" ")
    # 计算校验和
    checksum = calculate_checksum(cmd_list)
    print("111checksum", checksum)
    # 构造ADB命令
    adb_command = f'adb shell i2ctransfer -f -y 4 w6@0x2b 0x22 0x00 0x03 0x02 {brightness_byte} {checksum}'
    return adb_command


def adjust_brightness():
    """从0x01开始自增调节亮度"""
    subprocess.run("adb root", shell=True)
    brightness = 0
    while brightness <= 100:
        adb_command = create_i2c_command(brightness)
        print(f"Executing command: {adb_command}")
        # 执行ADB命令
        subprocess.run(adb_command, shell=True)
        # 等待时间
        time.sleep(0.002)
        print("当前亮度：", brightness)
        if brightness == 1:
            brightness += 1
        elif brightness == 100:
            brightness = 1  # 当亮度为100时，再从1开始调节
        else:
            brightness += 1


if __name__ == "__main__":
    adjust_brightness()