import time

from common.LogUtils import logger
from utils.SignalsManager import signals_manager


class BrightnessManager:

    def __init__(self):
        super().__init__()

    def handle_switch_step_brightness(self, case_number, command, data):
        mode = data.split(",")[0]
        start_brightness = int(data.split(",")[1])
        end_brightness = int(data.split(",")[2])
        interval = float(data.split(",")[3]) / 1000
        step_brightness = int(data.split(",")[4])
        status, errors = self.set_step_brightness(mode, start_brightness, end_brightness, step_brightness, interval)
        if status:
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "步进亮度设置成功")
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", f"步进亮度设置失败 异常：{errors}")

    @staticmethod
    def set_step_brightness(mode, start_brightness, end_brightness, step_brightness, interval):
        logger.info(f"set_step_brightness mode={mode}, start_brightness={start_brightness}, "
                    f"end_brightness={end_brightness}, step_brightness={step_brightness}, interval={interval}")
        from adb.AdbConnectDevice import adb_connect_device
        status = True
        errors = ""
        try:
            if start_brightness < end_brightness:
                for i in range(int((end_brightness - start_brightness) / step_brightness) + 1):
                    if start_brightness + i * step_brightness >= end_brightness:
                        brightness = f"{mode}:{end_brightness}"
                    else:
                        brightness = f"{mode}:{start_brightness + i * step_brightness}"
                    adb_connect_device.switch_brightness(brightness)
                    time.sleep(interval)
            else:
                for i in range(int((start_brightness - end_brightness) / step_brightness) + 1):
                    if start_brightness - i * step_brightness <= end_brightness:
                        brightness = f"{mode}:{end_brightness}"
                    else:
                        brightness = f"{mode}:{start_brightness - i * step_brightness}"
                    adb_connect_device.switch_brightness(brightness)
                    time.sleep(interval)
        except Exception as e:
            logger.error(f"set_step_brightness exception: {str(e.args)}")
            status = False
            errors = str(e.args)
        return status, errors


brightness_manager: BrightnessManager = BrightnessManager()
