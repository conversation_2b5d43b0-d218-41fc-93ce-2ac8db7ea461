# -*- coding: utf-8 -*-
"""
user:Created by jid on 2021/1/29
email:<EMAIL>
description:
"""

from PyQt5.QtCore import QObject

from common.LogUtils import logger
from photics.color_analyzer_tools.manager import MeasureType
from photics.color_analyzer_tools.manager.SignalCenter import signal_center
from simbox_tools.main import simbox_control


class GammaCurveCollect(QObject):

    def __init__(self):
        super(GammaCurveCollect, self).__init__()
        self.collect_x = []
        self.collect_y = []
        self.collect_brightness = []
        self.gray_index = []
        self.gamma = []
        self.gamma_len = 256
        self.brightness_index = 0
        self.brightness = []
        signal_center.gamma_curve_measure_data_signal.connect(self.measure_data)
        self.create_gamma_brightness()

    def create_gamma_brightness(self):
        for i in range(self.gamma_len):
            pattern = {'B': i, 'G': i, 'R': i}
            self.brightness.append(pattern)

    def reset_params(self):
        self.collect_x.clear()
        self.collect_y.clear()
        self.collect_brightness.clear()
        self.gray_index.clear()
        self.gamma = []
        self.brightness_index = 0

    def measure_data(self, measure_data):
        logger.info(f"measure_data measure_data={measure_data}")
        self.collect_x.append(measure_data[0])
        self.collect_y.append(measure_data[1])
        self.collect_brightness.append(measure_data[2])
        self.gray_index.append(self.brightness_index)
        self.brightness_index += 1
        if self.brightness_index == len(self.brightness):
            signal_center.gamma_measure_event_signal.emit(MeasureType.MEASURE_COMPLETED)
        else:
            signal_center.gamma_measure_event_signal.emit(MeasureType.MEASURE_NEXT)

    def set_pattern_background(self, is_simbox=False):
        logger.info(f"set_pattern_background index={self.brightness_index}, size={len(self.brightness)}")
        if self.brightness_index > len(self.brightness):
            return
        brightness = self.brightness[self.brightness_index]
        if not is_simbox:
            from adb.AdbConnectDevice import adb_connect_device
            adb_connect_device.switch_color("#%02X%02X%02X" % (brightness['R'], brightness['G'], brightness['B']))
        else:
            simbox_control.set_background_color(r=brightness['R'], g=brightness['G'], b=brightness['B'])
