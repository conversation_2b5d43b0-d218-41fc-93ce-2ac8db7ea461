from PyQt5.QtWidgets import QWidget

from .ui.jogWidget import Ui_Form
from ..motion_control_card import mcc


class JogWidget(QWidget, Ui_Form):
    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setupUi(self)

        self.setWindowTitle("jog运动")
        self.resize(900, 700)

        for i in mcc.axis_d:
            getattr(self, f"pushButton_f_move_{i}").pressed.connect(self.on_j_move_pressed)
            getattr(self, f"pushButton_c_move_{i}").pressed.connect(self.on_j_move_pressed)
            getattr(self, f"pushButton_f_move_{i}").released.connect(self.on_j_move_released)
            getattr(self, f"pushButton_c_move_{i}").released.connect(self.on_j_move_released)

    def on_j_move_pressed(self):
        sender = self.sender().objectName()
        index = int(sender.split("_")[-1])

        vel = getattr(self, f"doubleSpinBox_j_vel_{index}").value()
        acc = getattr(self, f"doubleSpinBox_j_acc_{index}").value()
        dec = getattr(self, f"doubleSpinBox_j_dec_{index}").value()
        smh = getattr(self, f"doubleSpinBox_j_smh_{index}").value()

        if self.sender().objectName() == f"pushButton_f_move_{index}":
            mcc.axis_d[index].jog_move(1, vel, acc, dec, smh)
        elif self.sender().objectName() == f"pushButton_c_move_{index}":
            mcc.axis_d[index].jog_move(-1, vel, acc, dec, smh)

    def on_j_move_released(self):
        sender = self.sender().objectName()
        index = int(sender.split("_")[-1])

        mcc.axis_d[index].jog_move(0)
