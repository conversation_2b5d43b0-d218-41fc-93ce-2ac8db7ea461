import json
import os
import queue
import re
import shutil
import threading
import time
import traceback
import xml.etree.ElementTree as ET

import numpy as np
from PIL import Image, ImageDraw
from PyQt5.QtCore import QTimer, pyqtSignal
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QImage, QPixmap
from PyQt5.QtWidgets import QWidget, QGridLayout, QDialog, QListWidgetItem, QListView
from openpyxl import Workbook
from openpyxl.styles import PatternFill, Alignment

from rotationanglemeasurement.UI.StereoCamera import Ui_Stereo
from rotationanglemeasurement.core.camera_params import get_stereo_coefficients
from rotationanglemeasurement.utils.manager import managerQ
from rotationanglemeasurement.utils.threadings import CameraThread, SavePhotoThread
from rotationanglemeasurement.utils.transformation_3d_matrix import load_transformation_matrix, \
    get_3d_transformation_matrix, \
    validate_transformation, save_transformation_matrix
from rotationanglemeasurement.utils.vector import CameraCalculation
from rotationanglemeasurement.view.SingleAngleview import SingleAngleWindows
from rotationanglemeasurement.view.cameraView import <PERSON><PERSON><PERSON>ow
from rotationanglemeasurement.view.messageboxView import MessageDialog
from utils.SignalsManager import signals_manager


class StereoWindow(QWidget, Ui_Stereo):
    camera_thread = globals()
    camera_timer = globals()
    count_camera_num = pyqtSignal(int)
    camera_transformation = globals()

    def __init__(self, parent=None):
        super(StereoWindow, self).__init__(parent=parent)
        self.setupUi(self)
        self.setWindowTitle("角度测试工具")
        self.splitter.setStretchFactor(0, 1)
        self.splitter.setStretchFactor(1, 3)
        self.remove_sample()
        self.coordinate_transformation = {}
        self.init()
        self.start_position = None
        self.end_position = None
        self.angle_result_value = {}
        self.camera_config = {}
        for i in range(4):
            path = os.path.join(os.getcwd(), "configs", f"b500-{i + 1}", "stereo_cam.yml")
            self.camera_config[str(i)] = get_stereo_coefficients(path)
        managerQ.camera_config = self.camera_config
        self.pushButtonConnectCamera.clicked.connect(self.connect_all_camera)
        self.pushButtonDisconnectCamera.clicked.connect(self.disconnect_all_camera)
        self.pushButtonExportData.clicked.connect(self.export_data)
        signals_manager.capture_image_signal.connect(self.captrue_image_calc)
        signals_manager.rotate_xml_parse.connect(self.parse_rotate_xml)
        signals_manager.angle_result_signal.connect(self.set_angle_result)
        self.front_angle = 10  # 初始角度
        self.path_map = {}
        # 去掉dockWidget的标题栏
        self.dockWidget.setTitleBarWidget(QWidget())
        self.dockWidget.titleBarWidget().setVisible(False)
        signals_manager.set_rotate_test_param.connect(self.set_test_case_param)
        self.case_number = 0
        self.command = ""

    def set_test_case_param(self,case_number,command):
        self.case_number = case_number
        self.command = command

    def export_data(self):
        # 创建WorkBook和Sheet
        wb = Workbook()
        ws = wb.active
        ws.append(["设定值", "实际值"])

        for i in range(self.listWidgetTest.count()):
            widget = self.listWidgetTest.item(i)
            if not isinstance(widget, QListWidgetItem):
                continue
            if not widget.lineEditDetect.text().strip():
                continue

            rotate_angle = widget.spinBox.value()  # 标准值
            angle = widget.lineEditDetect.text()  # 测量值
            if not angle:
                angle = "null"
                correct = False
            elif abs(abs(rotate_angle) - abs(float(angle))) <= 3:
                correct = True
            else:
                correct = False
                # 根据测试结果选择颜色填充
            if correct:
                fill = PatternFill(start_color="00FFFF", end_color="00FFFF", fill_type="solid")
            else:
                fill = PatternFill(start_color="FF0000", end_color="FF0000", fill_type="solid")

            # 为测试条目创建新的Excel行
            ws.append([str(rotate_angle), angle])
            # 设置底色
            row = i + 2  # 行号从1开始，因此需要 +1
            for col in range(1, 3):  # 只有两列需要设置底色
                ws.cell(row=row, column=col).fill = fill
                # 居中显示
                ws.cell(row=row, column=col).alignment = Alignment(horizontal='center', vertical='center')

        # 保存工作簿
        excel_name = 'export-report-rotate-%s.xlsx' % (time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime(time.time())))
        export_gamma_folder = os.path.join(os.getcwd(), 'export_report')
        if not os.path.exists(export_gamma_folder):
            os.makedirs(export_gamma_folder)
        result_excel = os.path.join(export_gamma_folder, excel_name)
        wb.save(result_excel)
        MessageDialog.showMessage("提示", f"保存成功\n{result_excel}")

    def set_angle_result(self, angle_result):
        pass

    def save_path_map(self):

        for key, path_info in self.path_map.items():
            timestamp = int(time.time())  # Get current timestamp

            # Prepare the new file names with the given format: {key}_right_{timestamp}.jpg
            right_file_name = "{}_right_{}.jpg".format(key, timestamp)
            left_file_name = "{}_left_{}.jpg".format(key, timestamp)
            # Construct the new paths in the tmp directory
            right_path = os.path.join(os.getcwd(), 'img', right_file_name)
            left_path = os.path.join(os.getcwd(), 'img', left_file_name)
            # Copy image files to tmp directory with the new names
            shutil.copy(path_info['right_path'], right_path)
            shutil.copy(path_info['left_path'], left_path)
            # Process right_path image
            self.draw_points_on_image(right_path, path_info['coordinate_right_list'])

            # Process left_path image
            self.draw_points_on_image(left_path, path_info['coordinate_left_list'])

    def draw_points_on_image(self, image_path, coordinates):
        # Open the image file
        image = Image.open(image_path).convert('RGB')
        draw = ImageDraw.Draw(image)

        # Draw red circles at each coordinate
        for coord in coordinates:
            # Draw a red circle with radius 3
            draw.ellipse([int(coord[0]) - 3, int(coord[1]) - 3, int(coord[0]) + 3, int(coord[1]) + 3], fill="red")

        # Save the modified image
        image.save(image_path)

    def parse_rotate_xml(self, case):
        # 清除所有的单角度
        self.listWidgetTest.clear()
        row_count = self.listWidgetTest.count()
        # 清除所有的单角度
        for i in range(row_count):
            self.listWidgetTest.takeItem(0)

        front_angle = self.front_angle
        is_first = True
        front_angle_pith = 0
        id = case.attrib.get("id")
        if id == "1000":
            rotate_type = "yaw"
        else:
            rotate_type = "pitch"
        self.timer_set_rotate_type = QTimer()
        self.timer_set_rotate_type.setSingleShot(True)  # 只执行一次
        self.timer_set_rotate_type.timeout.connect(lambda: self.set_current_rotate_type(rotate_type))

        # 启动定时器；这里需要一个时间参数，表示多少毫秒后执行
        self.timer_set_rotate_type.start(3000)  # 例如，在1秒后执行

        for step in case[0]:
            if step.text == "CAN":
                msg = step.attrib.get("msg")
                if not msg:
                    continue
                if is_first:
                    is_first = False
                    continue
                # yaw
                data = msg[30:35].replace(" ", "")
                # 12 0C 12 0C 07 03 07 03 00 01 {r_hex} {num_str} {num_str} 00 00
                data_pitch = msg[24:26].replace(" ", "")
                # print("msg:", msg)
                # print("data_pith", data_pitch)
                # data 转换为int 10机制
                data = int(data, 16)
                data_pitch = int(data_pitch, 16)
                angle = data - front_angle
                angle_pitch = data_pitch - front_angle_pith
                # print("angle_pitch:", angle_pitch)
                # 比较
                if id == "1000":
                    self.comboBoxRotateType.setCurrentText("yaw")
                    self.add_single_angle(angle)
                else:
                    self.comboBoxRotateType.setCurrentText("pitch")
                    self.add_single_angle(angle_pitch)

                front_angle = data
                front_angle_pith = data_pitch

    def remove_sample(self):
        imgpath = os.path.join(os.getcwd(), "images")
        path = os.path.join(os.getcwd(), "images", "sample")
        # 判断文件夹是否存在
        if not os.path.exists(imgpath):
            os.mkdir(imgpath)
        else:
            try:
                shutil.rmtree(path)
            except FileNotFoundError:
                pass
            os.mkdir(path)

    def init(self):
        self.init_camera_window(managerQ.num_cameras)
        self.pushButtonTakePhotos.clicked.connect(self.take_photos)
        self.save_photo_thread = SavePhotoThread()
        self.save_photo_thread.start()
        managerQ.photo_path_signal.connect(self.set_photo_path)
        self.pushButtonCalc.clicked.connect(self.calc)
        self.pushButtonCalcAngle.clicked.connect(self.CalcAngle)
        self.load_camera_transformation_thread = threading.Thread(target=self.load_camera_transformation, )
        self.load_camera_transformation_thread.start()
        self.pushButtonCoordinateTransformation.clicked.connect(self.CoordinateTransformation)
        self.pushButtonAdd.clicked.connect(self.add_single_angle)
        self.pushButtonSub.clicked.connect(self.remove_last_single_angle)
        self.pushButtonSaveTest.clicked.connect(self.AutoDetection)
        self.pushButtonClearResult.clicked.connect(self.clear_result)
        self.update()

    def clear_result(self):
        for i in range(self.listWidgetTest.count()):
            widget = self.listWidgetTest.item(i)
            if not isinstance(widget, QListWidgetItem):
                continue
            widget.lineEditDetect.setText("")

    def set_current_rotate_type(self, rotate_type):
        self.comboBoxRotateType.setCurrentText(rotate_type)

    def add_single_angle(self, angle=None):
        if angle is None or angle is False:
            angle = self.spinBoxAngle.value()
        # 创建 SingleAngleWindows 实例
        single_angle_widget_item = SingleAngleWindows()
        single_angle_widget_item.spinBox.setValue(angle)
        single_angle_widget_item.spinBox.setEnabled(False)
        single_angle_widget = single_angle_widget_item.widget
        # 添加 QListWidgetItem 到 QListWidget 中
        self.listWidgetTest.addItem(single_angle_widget_item)
        self.listWidgetTest.setItemWidget(single_angle_widget_item, single_angle_widget)

        for i in range(self.listWidgetTest.count()):
            widget = self.listWidgetTest.item(i)
            if not isinstance(widget, QListWidgetItem):
                continue
            widget.lineEditDetect.setText("")

    def remove_last_single_angle(self):
        # 从 QListWidget 中删除最后一个 QListWidgetItem
        if self.listWidgetTest.count() > 0:
            # QListWidget 的 takeItem 方法会将 item 从列表中移除并返回移除的 item
            item_to_remove = self.listWidgetTest.takeItem(self.listWidgetTest.count() - 1)
            # 删除 QWidget 实例
            item_to_remove.widget.deleteLater()
            # 删除 QListWidgetItem 实例
            del item_to_remove

    def AutoDetection(self):
        # 获取self.listWidgetTest中的所有item
        items = []
        for index in range(self.listWidgetTest.count()):
            items.append(self.listWidgetTest.item(index))
        # 获取所有的单角度
        rotate_type = self.comboBoxRotateType.currentText().strip()
        single_angle_list = []
        for item in items:
            single_angle_list.append(item.spinBox.value())
        if len(single_angle_list) == 0:
            MessageDialog.showMessage("错误", "请添加单角度")
            return
        if sum(single_angle_list) > 354:
            MessageDialog.showMessage("错误", "单角度和不大于354度")
            return
        data = self.generate_test_data(single_angle_list, rotate_type)
        if not data:
            return

        # 解析第一个XML字符串
        tree1 = ET.ElementTree(ET.fromstring(data))
        root1 = tree1.getroot()

        # 解析第二个XML字符串
        tree2 = ET.ElementTree(ET.fromstring(origin_data))
        root2 = tree2.getroot()

        # 创建一个字典，用于存储第二个XML中已有的case元素的id
        existing_ids = {case.get('id'): case for case in root2.findall('case')}

        # 遍历第一个XML的case元素
        for case in root1.findall('case'):
            case_id = case.get('id')

            # 如果第二个XML中已经存在相同id的case元素，则替换它
            if case_id in existing_ids:
                root2.remove(existing_ids[case_id])

            # 将case元素添加到第二个XML的根元素下
            root2.append(case)

        signals_manager.reload_xml.emit()

    def generate_test_data(self, rotate_pos, rotate_type="yaw"):

        num = 1
        # 在rotate_pos中第一个索引位置添加0
        rotate_pos.insert(0, 0)
        id = "1000" if rotate_type == "yaw" else "10001"
        head = f"""<?xml version='1.0' encoding='utf-8'?>
<test-case version="1">
    <case cycle="1" describe="运动角度测试" id="{id}" interval="1000" module="显示功能" version="V0.1" vision_algorithm="True">
        <option type="steps" name="运动角度测试验证">
        """
        tail = """
        </option>
    </case>
</test-case>
        """
        result = ""
        result += head
        rotate = self.front_angle if rotate_type == "yaw" else 0  # 初始的角度
        for r in rotate_pos:
            rotate += r
            num_str = '{:02X}'.format(num)
            if rotate_type == "yaw":
                if rotate > 354 or rotate < 0:
                    MessageDialog.showMessage("错误", "基座Yaw角度设置超出范围!")
                    return None
                r_high, r_low = divmod(rotate, 0x100)
                # 将高低字节转换为指定格式，并确保输出格式为两个字符的大写
                r_hex = '{:02X} {:02X}'.format(r_high, r_low)
                data = f"12 0C 12 0C 07 03 07 03 00 01 {r_hex} {num_str} {num_str} 00 00"
            else:
                if rotate > 40 or rotate < 0:
                    MessageDialog.showMessage("错误", "头部Pitch角度设置超出范围!")
                    return None
                # 将角度转换为 00 的16进制
                r_hex = '{:02x}'.format(rotate).upper()
                data = f"12 0C 12 0C 07 03 07 03 {r_hex} 01 00 3C {num_str} {num_str} 00 00"
            # step = f'<step id="48A" msg="{data}" id_ivibox="" msg_ivibox="00 00 00 00 00 00 00 00" zlg="False" ivibox="False" period="False" period_time="500" uds="False" expect="id: msg:00 00 00 00 00 00 00 00">CAN</step>'
            step = f'<step id="0x48A" msg="{data}" can_type="pcan" data_expect="00 00 00 00 00 00 00 00" recv_id="0x782" uds="1" cycle_period="0" expect="00 00 00 00 00 00 00 00">CAN</step>'
            sp = f"""
        <step wait="5" expect="">WAIT</step>
        <step CustomizeCMD="Mate3BeforeRotate" data="" expect="">CustomizeCMD</step>
        <step wait="5" expect="">WAIT</step>
        {step}
        <step wait="10" expect="">WAIT</step>
        <step CustomizeCMD="Mate3AfterRotate" data="" expect="">CustomizeCMD</step>
        <step wait="5" expect="">WAIT</step>
                """
            if num == 1:
                # print(step)
                result += step
                num += 1
                continue
            # print(sp)
            result += sp
            num += 1
        result += tail
        return result

    def init_camera_window(self, num):
        # 删除
        grid_layout = self.widgetDevices.findChild(QGridLayout)
        if grid_layout:
            for i in reversed(range(grid_layout.count())):
                layout_item = grid_layout.itemAt(i)
                if layout_item.widget() is not None:
                    layout_item.widget().deleteLater()
        else:
            grid_layout = QGridLayout()
        item_list = []
        for i in range(num):
            camera_list = [str(i) for i in range(4)]
            item = CameraWindow()
            item.comboBox.addItems(camera_list)
            item.comboBox.currentText()
            item.comboBox.setCurrentText(str(i))
            item.position_id = i
            # item.label.setText(str(i))
            item_list.append(item)
        for i, widget in enumerate(item_list):
            row = i // 4  # 计算行号
            col = i % 4  # 计算列号
            grid_layout.addWidget(widget, row, col)

        self.widgetDevices.setLayout(grid_layout)

    def CoordinateTransformation(self):

        # 使用
        result = MessageDialog.showMessage("警告", "确定需要转换坐标系吗?")
        if result == QDialog.Rejected:
            return
        elif result == QDialog.Accepted:
            position_list, checked_list = self.get_select_data()
            if not position_list or not checked_list:
                return
            index1 = re.findall(r"(\d)", checked_list[0].objectName())[0]
            index2 = re.findall(r"(\d)", checked_list[1].objectName())[0]

            print("index", index2, index1)
            # points1相机到points2相机的坐标转换
            points1, points2 = position_list[1], position_list[0]
            points1 = np.array(json.loads(points1))
            points2 = np.array(json.loads(points2))

            transformation_matrix = get_3d_transformation_matrix(points2, points1)
            file_path = os.path.join(os.getcwd(), "checkpoints", f"{min(index1, index2)}_{max(index1, index2)}")

            # 验证变换矩阵并计算误差
            mean_error, std_dev_error, max_error = validate_transformation(points2, points1, transformation_matrix)

            # print(f"Mean Error: {mean_error}")
            # print(f"Standard Deviation of Error: {std_dev_error}")
            # print(f"Maximum Error: {max_error}")
            # self.statusBar().showMessage(f"{min(index1,index2)}_{max(index1,index2)}坐标系转换误差:{error}",3)
            self.camera_transformation[f"{min(index1, index2)}_{max(index1, index2)}"] = transformation_matrix
            save_transformation_matrix(transformation_matrix, file_path)
            self.coordinate_transformation[f"{min(index1, index2)}_{max(index1, index2)}"] = {
                str(index1): json.loads((position_list[0])),
                str(index2): json.loads((position_list[1]))
            }
            self.save_camera_transformation()

    def save_camera_transformation(self):
        with open(os.path.join(os.getcwd(), "configs", "camera_transformation.json"), "w") as f:
            f.write(json.dumps(self.coordinate_transformation, indent=2))

    def load_camera_transformation(self):
        base_dir = os.path.join(os.getcwd(), "checkpoints")
        npz_files = os.listdir(base_dir)
        for npz_file in npz_files:
            tmp = npz_file.split(".")[0]
            path = os.path.join(base_dir, npz_file)
            self.camera_transformation[tmp] = load_transformation_matrix(path)
        with open(os.path.join(os.getcwd(), "configs", "camera_transformation.json"), "r") as f:
            data = f.read()
        self.coordinate_transformation = json.loads(data)

    def disconnect_all_camera(self):
        grid_layout = self.widgetDevices.findChild(QGridLayout)
        if grid_layout:
            for i in range(grid_layout.count()):
                layout_item = grid_layout.itemAt(i)
                if layout_item.widget() is not None:
                    layout_item.widget().labelLeft.setPixmap(QPixmap())
                    layout_item.widget().labelRight.setPixmap(QPixmap())
                    self.camera_timer[f"timer_{i}"].stop()
                    self.camera_thread[f"camera_thread_{i}"].run_flag = False
                    self.camera_thread[f"camera_thread_{i}"].imgQ.queue.clear()
                    del self.camera_timer[f"timer_{i}"]
                    del self.camera_thread[f"camera_thread_{i}"]
        self.pushButtonConnectCamera.setEnabled(True)

    def connect_all_camera(self):
        grid_layout = self.widgetDevices.findChild(QGridLayout)
        if grid_layout:
            for i in range(grid_layout.count()):
                layout_item = grid_layout.itemAt(i)
                if layout_item.widget() is not None:
                    camera_num = layout_item.widget().comboBox.currentText()
                    imgQ = queue.Queue(maxsize=10)  # You can adjust maxsize based on your needs
                    self.camera_thread[f"camera_thread_{i}"] = CameraThread(camera_num, imgQ)
                    self.camera_thread[f"camera_thread_{i}"].start()
                    # print(camera_num)
                    if f"timer_{i}" not in self.camera_timer or not self.camera_timer[f"timer_{i}"].isActive():
                        self.camera_timer[f"timer_{i}"] = QTimer()
                        self.camera_timer[f"timer_{i}"].timeout.connect(
                            lambda q=imgQ, l=layout_item.widget().labelLeft,
                                   r=layout_item.widget().labelRight,: self.update_image_from_queue(q, l, r))
                        self.camera_timer[f"timer_{i}"].start(20)  # Refresh rate of 20 FPS. Adjust as needed.
                    self.pushButtonConnectCamera.setEnabled(False)

    def update_image_from_queue(self, imgQ, Llabel, Rlabel):
        label_w = 450
        label_h = 380
        try:
            frame = imgQ.get_nowait()
            # 确保数据是连续的
            if not frame.flags['C_CONTIGUOUS']:
                frame = np.ascontiguousarray(frame)
            frame_left = frame[:, :1920]
            frame_right = frame[:, 1920:]
            frame_left_bytes = frame_left.tobytes()
            frame_right_bytes = frame_right.tobytes()
            # 如果原图像是BGR格式，将其转为RGB格式
            # frame_left = cv2.cvtColor(frame_left, cv2.COLOR_BGR2RGB)
            # frame_right = cv2.cvtColor(frame_right, cv2.COLOR_BGR2RGB)
            h, w, = frame_left.shape
            ch = 1
            bytes_per_line = ch * w
            # 对于灰度图，我们使用QImage.Format_Grayscale8
            convert_to_Qt_format_l = QImage(frame_left_bytes, w, h, bytes_per_line, QImage.Format_Grayscale8)
            convert_to_Qt_format_r = QImage(frame_right_bytes, w, h, bytes_per_line, QImage.Format_Grayscale8)
            l = convert_to_Qt_format_l.scaled(label_w, label_h, Qt.KeepAspectRatio)
            r = convert_to_Qt_format_r.scaled(label_w, label_h, Qt.KeepAspectRatio)
            Llabel.setPixmap(QPixmap.fromImage(l))
            Rlabel.setPixmap(QPixmap.fromImage(r))
        except queue.Empty:
            pass

    def take_photos(self):
        # 获取当前所有的摄像头窗口
        grid_layout = self.widgetDevices.findChild(QGridLayout)
        if grid_layout:
            # 设置所有相机线程的take_photo标志
            for i in range(grid_layout.count()):
                if f"camera_thread_{i}" in self.camera_thread:
                    self.camera_thread[f"camera_thread_{i}"].take_photo = True
                # 重置坐标
                layout_item = grid_layout.itemAt(i)
                if layout_item.widget() is not None:
                    layout_item.widget().label_coordinate_left.setText("")
                    layout_item.widget().label_coordinate_right.setText("")
                    layout_item.widget().coordinate_right_list = []
                    layout_item.widget().coordinate_left_list = []

    def set_photo_path(self, camera_id, Lpath, Rpath):
        grid_layout = self.widgetDevices.findChild(QGridLayout)
        if grid_layout:
            for i in range(grid_layout.count()):
                layout_item = grid_layout.itemAt(i)
                if layout_item.widget() is not None:
                    camera_num = layout_item.widget().comboBox.currentText()
                    if camera_num == camera_id:
                        camera_widget = layout_item.widget()
                        camera_widget.img_Lpath = Lpath
                        camera_widget.img_Rpath = Rpath
                        # num = camera_widget.comboBox.currentText()
                        camera_widget.image_view.load_image(camera_widget.camera_id,
                                                            camera_widget.img_Lpath,
                                                            camera_widget.position_id, dot_num=managerQ.dot_num)
                        camera_widget.image_view.load_image(camera_widget.camera_id,
                                                            camera_widget.img_Rpath,
                                                            camera_widget.position_id, dot_num=managerQ.dot_num)

    def get_camera_map(self):
        grid_layout = self.widgetDevices.findChild(QGridLayout)
        camera_map = {}
        if grid_layout:
            for i in range(grid_layout.count()):
                layout_item = grid_layout.itemAt(i)
                if layout_item.widget() is not None:
                    coordinate_left = layout_item.widget().coordinate_left_list
                    coordinate_right = layout_item.widget().coordinate_right_list
                    img_Lpath = layout_item.widget().img_Lpath
                    img_Rpath = layout_item.widget().img_Rpath
                    if not coordinate_right and not coordinate_left:
                        continue
                    # 判断是第几组相机然后判断左右
                    group_num = i
                    if str(group_num) not in camera_map.keys():
                        camera_map[str(group_num)] = {}
                    if str(group_num) not in self.path_map.keys():
                        self.path_map[str(group_num)] = {}
                    camera_map[str(group_num)]["left"] = coordinate_left
                    camera_map[str(group_num)]["right"] = coordinate_right
                    self.path_map[str(group_num)]["left_path"] = img_Lpath
                    self.path_map[str(group_num)]["right_path"] = img_Rpath
                    self.path_map[str(group_num)]["coordinate_left_list"] = coordinate_left
                    self.path_map[str(group_num)]["coordinate_right_list"] = coordinate_right
                    # 取完之后就要重置
                    layout_item.widget().coordinate_left = []
                    layout_item.widget().coordinate_right = []
            return camera_map

    def calc(self):
        # {'0': {'left': [], 'right': []}, '1': {'left': [], 'right': []}}

        camera_map = self.get_camera_map()

        # camera_map = {'0': {'left': [('966', '551'), ('1090', '541'), ('1016', '631')],
        #                'right': [('573', '566'), ('657', '557'), ('600', '646')]},
        #          '1': {'left': [('1344', '620'), ('1469', '623'), ('1408', '714')],
        #                'right': [('826', '651'), ('979', '645'), ('900', '741')]}}
        # print(camera_map)

        positon_3d = {}
        # positon_3d_ = {}
        for k, v in camera_map.items():
            try:
                f, B = self.camera_config[k]["focal_length"], self.camera_config[k]["baseline"]
                P1, P2, Q = self.camera_config[k]["P1"], self.camera_config[k]["P2"], self.camera_config[k]["Q"]
                cx, cy = self.camera_config[k]["K1"][0][-1], self.camera_config[k]["K1"][1][-1]
                cx_prime, cy_prime = self.camera_config[k]["K2"][0][-1], self.camera_config[k]["K2"][1][-1]
                R, T = self.camera_config[k]["R"], self.camera_config[k]["T"]
                # Extracting left and right points
                if "left" not in v.keys() or "right" not in v.keys():
                    print("left or right lost!!")
                    continue

                left_points = [(float(x), float(y)) for x, y in v["left"]]
                right_points = [(float(x), float(y)) for x, y in v["right"]]

                points_3d = [
                    # CameraCalculation.calc_3d_points3(x_left, y_left, x_right, y_right, f, B)
                    # CameraCalculation.calc_3d_points4(x_left, y_left, x_right, y_right, f, B, cx, cy, cx_prime,cy_prime, R, T)

                    CameraCalculation.calc_3d_points4(x_left, y_left, x_right, y_right, f, B, cx, cy, cx_prime,cy_prime, R, T)

                    for (x_left, y_left), (x_right, y_right) in zip(left_points, right_points)
                ]
                # points_3d_ = [
                #     # CameraCalculation.calc_3d_points3(x_left, y_left, x_right, y_right, f, B)
                #     # CameraCalculation.calc_3d_points4(x_left, y_left, x_right, y_right, f, B, cx, cy, cx_prime,
                #     #                                   cy_prime, R, T)
                #     CameraCalculation.calc_3d_points(x_left, y_left, x_right, y_right, f, B, cx, cy, cx_prime,cy_prime, R, T)
                #     for (x_left, y_left), (x_right, y_right) in zip(left_points, right_points)
                # ]
                # print("points_3d:",points_3d)

                if len(points_3d) != 0:
                    positon_3d[k] = points_3d
                    # positon_3d_[k] =points_3d_
                    self.show_points_3d(index=int(k), points_3d=positon_3d[k])
                # 显示坐标
            except IndexError:
                MessageDialog.showMessage("错误", "确认靶向点是否标记?")
        print("positon_3d:", positon_3d)
        # print("positon_3d_:",positon_3d_)
        return positon_3d

    def show_points_3d(self, index, points_3d):
        lineEdit_start = getattr(self, f"lineEdit{index + 1}Start")
        text = lineEdit_start.text().strip()
        if text:
            lineEdit_end = getattr(self, f"lineEdit{index + 1}End")
            lineEdit_end.setText(str(points_3d))
        else:
            lineEdit_start.setText(str(points_3d))

    def CalcAngle(self, auto=False):
        from scipy.spatial.transform import Rotation as Rot
        def find_rotation_matrix(A, B):
            # Ensure input is numpy arrays
            A = np.array(A)
            B = np.array(B)
            # Calculate centroids
            centroid_A = np.mean(A, axis=0)
            centroid_B = np.mean(B, axis=0)

            # Translate points to the origin
            A_centered = A - centroid_A
            B_centered = B - centroid_B

            # Compute covariance matrix
            H = A_centered.T @ B_centered

            # Compute SVD
            U, S, Vt = np.linalg.svd(H)

            # Compute rotation matrix
            rotation_matrix = Vt.T @ U.T

            # Correct the rotation matrix in case of reflection
            if np.linalg.det(rotation_matrix) < 0:
                Vt[-1, :] *= -1
                rotation_matrix = Vt.T @ U.T

            return rotation_matrix

        def rotation_matrix_to_euler_angles(rotation_matrix):
            # Convert the rotation matrix to Euler angles
            r = Rot.from_matrix(rotation_matrix)
            # return r.as_euler('xyz', degrees=True)
            return r.as_euler('zyx', degrees=True)

        def rotation_matrix_x(theta_degrees):
            # 将角度从度转换为弧度
            theta_radians = np.deg2rad(theta_degrees)

            # 计算余弦和正弦值
            cos_theta = np.cos(theta_radians)
            sin_theta = np.sin(theta_radians)

            # 构建绕x轴旋转的旋转矩阵
            R_x = np.array([[1, 0, 0],
                            [0, cos_theta, -sin_theta],
                            [0, sin_theta, cos_theta]])

            return R_x
        def apply_pre_rotation(point, pre_rot_matrix):
            """
            应用预旋转变换到给定点。
            参数:
            point: 三维点坐标。
            pre_rot_matrix: 预旋转矩阵。
            返回:
            旋转后的点坐标。
            """
            # 将点坐标扩展为4维向量，以应用变换
            point_homogenous = np.append(point, 1)
            rotated_point = pre_rot_matrix.dot(point_homogenous)[:3]
            # 将rotated_point转为list
            rotated_point = list(rotated_point)
            return rotated_point

        def compensation_value(start, end):
            if self.checkBoxYAW.isChecked():
                angle = 8.0
            else:
                angle = 0.1
            pre_rot = rotation_matrix_x(float(angle))
            # 应用旋转矩阵
            A_pre_rot = np.array(start) @ pre_rot
            A_pre_rot = A_pre_rot.tolist()
            B_pre_rot = np.array(end) @ pre_rot
            B_pre_rot = B_pre_rot.tolist()

            return A_pre_rot, B_pre_rot
        if auto:
            ii1, ii2 = list(self.start_position.keys())[0], list(self.end_position.keys())[0]
            position_list, checked_list = [self.start_position[ii1], self.end_position[ii2]], [int(ii1) + 1,
                                                                                               int(ii2) + 1]
            print("position_list", position_list)
            start_position, end_position = position_list[0], position_list[1]
            if isinstance(start_position, str):
                start_position, end_position = json.loads(start_position), json.loads(end_position)
            start_position, end_position = np.array(start_position), np.array(end_position)
            index1 = checked_list[0]
            index2 = checked_list[1]
            if not position_list or not checked_list:
                return
        else:
            position_list, checked_list = self.get_select_data()
            if not position_list or not checked_list:
                return
            # print("position_list", position_list)
            start_position, end_position = position_list[0], position_list[1]
            if isinstance(start_position, str):
                start_position, end_position = json.loads(start_position), json.loads(end_position)
            start_position, end_position = np.array(start_position), np.array(end_position)
            index1 = re.findall(r"(\d)", checked_list[0].objectName())[0]
            index2 = re.findall(r"(\d)", checked_list[1].objectName())[0]

        index1 = int(index1)
        index2 = int(index2)
        if index1 != index2:
            # if abs(index2-index1) == 1:
            # transformation_matrix = self.camera_transformation[f"{min(index1,index2)}_{max(index1,index2)}"]
            # end_position = apply_transformation(end_position, transformation_matrix)
            # rotate=find_rotation_matrix(start_position, end_position)
            # print("坐标转换:{}".format(rotation_matrix_to_euler_angles(rotate)))
            #     upper=self.coordinate_transformation[f"{min(index1,index2)}_{max(index1,index2)}"].get("{}".format(max(index1,index2)),"")
            #     lower = self.coordinate_transformation[f"{min(index1,index2)}_{max(index1,index2)}"].get("{}".format(min(index1,index2)),"")
            center_rotate_angle = 0
            center_rotate_angle2 = 0
            lower = self.coordinate_transformation[f"{min(index1, index2)}_{min(index1, index2) + 1}"].get(
                "{}".format(min(index1, index2)), "")
            upper = self.coordinate_transformation[f"{max(index1, index2) - 1}_{max(index1, index2)}"].get(
                "{}".format(max(index1, index2)), "")
            if abs(index2 - index1) == 2:
                if min(index1, index2) + 1 == 2:
                    A1 = self.coordinate_transformation["1_2"]["2"]
                    A2 = self.coordinate_transformation["2_3"]["2"]

                elif min(index1, index2) + 1 == 3:
                    A1 = self.coordinate_transformation["2_3"]["3"]
                    A2 = self.coordinate_transformation["3_4"]["3"]
                A_pre_rot, B_pre_rot = compensation_value(A1, A2)

                # print("预旋转后点：",A_pre_rot,B_pre_rot)
                # 现在计算预旋转后点的旋转矩阵
                rotation_matrix = find_rotation_matrix(A_pre_rot, B_pre_rot)

                quat = Rot.from_matrix(rotation_matrix).as_quat()
                rotation = Rot.from_quat(quat).as_euler('yxz', degrees=True)
                center_rotate_angle =  abs(rotation.tolist()[0])
                # center_rotate1 = self.coordinate_transformation[f"{min(index1, index2)}_{min(index1, index2) + 1}"].get("{}".format(min(index1, index2) + 1), "")
                # center_rotate2 = self.coordinate_transformation[f"{max(index1, index2) - 1}_{max(index1, index2)}"].get("{}".format(max(index1, index2) - 1), "")
                # rotation_matrix_center_rotate = find_rotation_matrix(center_rotate1, center_rotate2)
                # center_rotate_angle = rotation_matrix_to_euler_angles(rotation_matrix_center_rotate)
                # center_rotate_angle = managerQ.fix_camera_results(min(index1, index2) + 1, center_rotate_angle.tolist()[1])
                # center_rotate_angle = center_rotate_angle.tolist()[1]

            if abs(index2 - index1) == 3:  # 4组 1~4 旋转
                A1 = self.coordinate_transformation["1_2"]["2"]
                A2 = self.coordinate_transformation["2_3"]["2"]
                A_pre_rot, B_pre_rot = compensation_value(A1, A2)

                # print("预旋转后点：",A_pre_rot,B_pre_rot)
                # 现在计算预旋转后点的旋转矩阵
                rotation_matrix = find_rotation_matrix(A_pre_rot, B_pre_rot)

                quat = Rot.from_matrix(rotation_matrix).as_quat()
                rotation = Rot.from_quat(quat).as_euler('yxz', degrees=True)
                center_rotate_angle1 = abs(rotation.tolist()[0])
                A3 = self.coordinate_transformation["2_3"]["3"]
                A4 = self.coordinate_transformation["3_4"]["3"]
                A_pre_rot, B_pre_rot = compensation_value(A3, A4)

                # print("预旋转后点：",A_pre_rot,B_pre_rot)
                # 现在计算预旋转后点的旋转矩阵
                rotation_matrix = find_rotation_matrix(A_pre_rot, B_pre_rot)

                quat = Rot.from_matrix(rotation_matrix).as_quat()
                rotation = Rot.from_quat(quat).as_euler('yxz', degrees=True)
                center_rotate_angle2 =  abs(rotation.tolist()[0])
                center_rotate_angle =center_rotate_angle1 + center_rotate_angle2


            if upper and lower:
                # rotation_matrix1 = find_rotation_matrix(start_position, lower)
                A_pre_rot, B_pre_rot = compensation_value(start_position, lower)

                # print("预旋转后点：",A_pre_rot,B_pre_rot)
                # 现在计算预旋转后点的旋转矩阵
                rotation_matrix1 = find_rotation_matrix(A_pre_rot, B_pre_rot)
                A_pre_rot, B_pre_rot = compensation_value(upper, end_position)

                # rotation_matrix2 = find_rotation_matrix(upper, end_position)
                rotation_matrix2 = find_rotation_matrix(A_pre_rot, B_pre_rot)

                quat1 = Rot.from_matrix(rotation_matrix1).as_quat()
                rotation1 = Rot.from_quat(quat1).as_euler('yxz', degrees=True)
                pitch_rotation1 = rotation1.tolist()[1]
                rotation1 = managerQ.fix_camera_results(min(index1, index2), rotation1.tolist()[0])

                quat2 = Rot.from_matrix(rotation_matrix2).as_quat()
                rotation2 = Rot.from_quat(quat2).as_euler('yxz', degrees=True)
                pitch_rotation2 = rotation2.tolist()[1]
                rotation2 = managerQ.fix_camera_results(max(index1, index2), rotation2.tolist()[0])
                yaw_rotation = abs(rotation1) + abs(rotation2) + abs(center_rotate_angle)
                print("多相机:",rotation1,rotation2,center_rotate_angle)

                # print("间接计算:{}".format(yaw_rotation))
                pitch_rotation = pitch_rotation1 + pitch_rotation2
            else:
                yaw_rotation = "NULL"
                pitch_rotation = "NULL"

        else:
            # rotation_matrix = find_rotation_matrix(start_position, end_position)
            # 头部10度旋转
            # pre_rot =find_rotation_matrix([[-19.918254137251246, 28.36634156071821, 297.0781823225642], [25.84036605446936, 31.001737282936013, 270.0710748386947], [1.0501149351241414, 51.79839568453535, 280.5030047680863]],
            #                          [[-20.191376492037502, 30.278198044222012, 297.0781823225642], [25.592073004663675, 33.4846677809929, 270.0710748386947], [2.3700494354296002, 54.041511751367814, 284.16173961288746]])
            # 头部3度旋转
            # pre_rot = find_rotation_matrix([[23.95466291408019, 64.2049394869158, 265.67967524782165], [75.77887733822749, 65.51598833387345, 258.3288541935341], [49.61273040750457, 90.81218826997035, 259.35396869430207]],
            #                                [[23.95466291408019, 65.42621830913077, 265.67967524782165], [75.71709797096273, 66.6774273889285, 257.3118114604887], [49.81039068801256, 91.8921601521572, 260.38725143810404]])
            # pre_rot = find_rotation_matrix([[23.95466291408019, 65.42621830913077, 265.67967524782165], [75.71709797096273, 66.6774273889285, 257.3118114604887], [49.81039068801256, 91.8921601521572, 260.38725143810404]],
            #                                [[23.95466291408019, 65.42621830913077, 265.67967524782165], [75.71709797096273, 66.6774273889285, 257.3118114604887], [49.81039068801256, 91.8921601521572, 260.38725143810404]])
            # with open(os.path.join(os.getcwd(), "configs", "angle.txt"), "r") as f:
            #     angle = f.read()


            A_pre_rot, B_pre_rot = compensation_value(start_position,end_position)

            # print("预旋转后点：",A_pre_rot,B_pre_rot)
            # 现在计算预旋转后点的旋转矩阵
            rotation_matrix = find_rotation_matrix(A_pre_rot, B_pre_rot)

            quat = Rot.from_matrix(rotation_matrix).as_quat()
            rotation = Rot.from_quat(quat).as_euler('yxz', degrees=True)
            yaw_rotation = managerQ.fix_camera_results(index1, rotation.tolist()[0])
            pitch_rotation = rotation.tolist()[1]
            # TODO rotation需要修正
        self.labelResult.setText(
            "yaw基座电机转动夹角:{}\npitch上下电机转动夹角{}".format(round(yaw_rotation, 3), round(pitch_rotation, 3)))
        return {"yaw": yaw_rotation, "pitch": pitch_rotation}

    def get_select_data(self):
        # 获取选中的2个状态
        # 获取选中的2个状态
        checked_list = []
        position_list = []
        for i in range(4):  # TODO
            checkBoxEnd = getattr(self, f"checkBox{i + 1}end")
            checkBoxStart = getattr(self, f"checkBox{i + 1}start")
            if checkBoxStart.isChecked():
                checked_list.append(checkBoxStart)
                obj = getattr(self, f"lineEdit{i + 1}Start")
                if obj.text():
                    position_list.append(obj.text())
                else:
                    MessageDialog.showMessage("错误", "坐标为空？")
                    return [], []

            if checkBoxEnd.isChecked():
                checked_list.append(checkBoxEnd)
                obj = getattr(self, f"lineEdit{i + 1}End")
                if obj.text():
                    position_list.append(obj.text())
                else:
                    MessageDialog.showMessage("错误", "坐标为空？")
                    return [], []

        if len(checked_list) != 2:
            MessageDialog.showMessage("错误", "选择2个进行计算！")
            return [], []
        return position_list, checked_list

    def get_position3D(self):
        positon_3d = self.calc()
        # if len(positon_3d.keys()) >1:
        #     Mes
        return positon_3d

    def get_calc_result(self, position):
        if position == "before rotate":
            self.path_map = {}  # 重置map
            self.start_position = self.get_position3D()
            # print("self.start_position", self.start_position)
        elif position == "after rotate":
            self.end_position = self.get_position3D()
            # print("self.end_position", self.end_position)
            # 获取字典a和b的键的集合
            if self.start_position and self.end_position:
                if len(self.start_position.keys()) > 1 or len(self.end_position) > 1:
                    keys_start_position = set(self.start_position.keys())
                    keys_end_position = set(self.end_position.keys())
                    common_keys = sorted(list(keys_start_position & keys_end_position))
                    index = common_keys[0]
                    path_map = self.path_map[index]
                    self.path_map = {f"{index}": path_map}
                    start_v = self.start_position[index]
                    end_v = self.end_position[index]
                    self.start_position = {f"{index}": start_v}
                    self.end_position = {f"{index}": end_v}

                    # print("self.start_position after",self.start_position)
                    # print("self.end_position after",self.end_position)
            # 计算
            try:
                self.angle_result_value = self.CalcAngle(auto=True)
            except Exception:
                print(traceback.format_exc())
                self.angle_result_value = {}
            self.start_position = None
            self.end_position = None
            signals_manager.angle_result_signal.emit(self.angle_result_value)
            d = f"yaw:{round(self.angle_result_value['yaw'],2)}\npitch:{round(self.angle_result_value['pitch'],2)}"
            print("self.angle_result_value", self.angle_result_value)
            if 57.0<=round(self.angle_result_value['yaw'],2) <= 63.1:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "PASS",d)
            else:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG",d)


    def captrue_image_calc(self, position):
        threading.Thread(target=self.take_photos).start()
        QTimer.singleShot(2000, lambda: self.get_calc_result(position))

    def get_angle_result_(self):
        return self.angle_result_value

    def onclose(self):
        print("close stereo view")

