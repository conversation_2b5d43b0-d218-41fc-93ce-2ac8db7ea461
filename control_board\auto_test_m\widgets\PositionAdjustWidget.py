from PyQt5.QtWidgets import QWidget

from .ui.positionAdjustWidget import Ui_Form

from ..ctr_card import ctr_card

from ..constants import X, Y, Z1, Z2, R


class PositionAdjustWidget(QWidget, Ui_Form):
    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setupUi(self)

        self.setWindowTitle("位置调试")

        ctr_card.axis_prf_pos_changed_signal.connect(self.on_axis_prf_pos_changed)

        self.pushButton_x1.pressed.connect(self.on_j_move_pressed)
        self.pushButton_x1.released.connect(self.on_j_move_released)
        self.pushButton_x0.pressed.connect(self.on_j_move_pressed)
        self.pushButton_x0.released.connect(self.on_j_move_released)
        self.pushButton_y1.pressed.connect(self.on_j_move_pressed)
        self.pushButton_y1.released.connect(self.on_j_move_released)
        self.pushButton_y0.pressed.connect(self.on_j_move_pressed)
        self.pushButton_y0.released.connect(self.on_j_move_released)
        self.pushButton_z11.pressed.connect(self.on_j_move_pressed)
        self.pushButton_z11.released.connect(self.on_j_move_released)
        self.pushButton_z10.pressed.connect(self.on_j_move_pressed)
        self.pushButton_z10.released.connect(self.on_j_move_released)
        self.pushButton_z21.pressed.connect(self.on_j_move_pressed)
        self.pushButton_z21.released.connect(self.on_j_move_released)
        self.pushButton_z20.pressed.connect(self.on_j_move_pressed)
        self.pushButton_z20.released.connect(self.on_j_move_released)
        self.pushButton_r1.pressed.connect(self.on_j_move_pressed)
        self.pushButton_r1.released.connect(self.on_j_move_released)
        self.pushButton_r0.pressed.connect(self.on_j_move_pressed)
        self.pushButton_r0.released.connect(self.on_j_move_released)

        self.pushButton_home.clicked.connect(self.on_home)
        self.pushButton_start.clicked.connect(self.on_start)
        self.pushButton_stop.clicked.connect(self.on_stop)

    def on_j_move_pressed(self):
        if self.sender().objectName() == "pushButton_x1":
            vel = 50
            acc = 0.05
            dec = 0.05
            smh = 0.9
            ctr_card.axis_jog(X, 1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_x0":
            vel = 50
            acc = 0.05
            dec = 0.05
            smh = 0.9
            ctr_card.axis_jog(X, -1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_y1":
            vel = 50
            acc = 0.05
            dec = 0.05
            smh = 0.9
            ctr_card.axis_jog(Y, 1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_y0":
            vel = 50
            acc = 0.05
            dec = 0.05
            smh = 0.9
            ctr_card.axis_jog(Y, -1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_z11":
            vel = 50
            acc = 0.05
            dec = 0.05
            smh = 0.9
            ctr_card.axis_jog(Z1, 1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_z10":
            vel = 50
            acc = 0.05
            dec = 0.05
            smh = 0.9
            ctr_card.axis_jog(Z1, -1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_z21":
            vel = 50
            acc = 0.05
            dec = 0.05
            smh = 0.9
            ctr_card.axis_jog(Z2, 1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_z20":
            vel = 50
            acc = 0.05
            dec = 0.05
            smh = 0.9
            ctr_card.axis_jog(Z2, -1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_r1":
            vel = 1
            acc = 0.05
            dec = 0.05
            smh = 0.9
            ctr_card.axis_jog(R, 1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_r0":
            vel = 1
            acc = 0.05
            dec = 0.05
            smh = 0.9
            ctr_card.axis_jog(R, -1, vel, acc, dec, smh)

    def on_j_move_released(self):
        if self.sender().objectName() == "pushButton_x1":
            ctr_card.axis_jog(X, 0)
        elif self.sender().objectName() == "pushButton_x0":
            ctr_card.axis_jog(X, 0)
        elif self.sender().objectName() == "pushButton_y1":
            ctr_card.axis_jog(Y, 0)
        elif self.sender().objectName() == "pushButton_y0":
            ctr_card.axis_jog(Y, 0)
        elif self.sender().objectName() == "pushButton_z11":
            ctr_card.axis_jog(Z1, 0)
        elif self.sender().objectName() == "pushButton_z10":
            ctr_card.axis_jog(Z1, 0)
        elif self.sender().objectName() == "pushButton_z21":
            ctr_card.axis_jog(Z2, 0)
        elif self.sender().objectName() == "pushButton_z20":
            ctr_card.axis_jog(Z2, 0)
        elif self.sender().objectName() == "pushButton_r1":
            ctr_card.axis_jog(R, 0)
        elif self.sender().objectName() == "pushButton_r0":
            ctr_card.axis_jog(R, 0)

    def on_axis_prf_pos_changed(self, index, prf_pos):
        if index == X:
            self.lineEdit_x.setText(str(prf_pos))
        elif index == Y:
            self.lineEdit_y.setText(str(prf_pos))
        elif index == Z1:
            self.lineEdit_z1.setText(str(prf_pos))
        elif index == Z2:
            self.lineEdit_z2.setText(str(prf_pos))
        elif index == R:
            self.lineEdit_r.setText(str(prf_pos))

    def on_home(self):
        ctr_card.home()

    def on_start(self):
        pos_d = {
            X: int(float(self.lineEdit_x_dest.text())),
            Y: int(float(self.lineEdit_y_dest.text())),
            Z1: int(float(self.lineEdit_z1_dest.text())),
            Z2: int(float(self.lineEdit_z2_dest.text())),
            R: int(float(self.lineEdit_r_dest.text()))
        }
        ctr_card.m_axis_trap_move(pos_d=pos_d)

    def on_stop(self):
        ctr_card.stop()
