import serial  # 调用串口通信库
import time  # 调用时间库

# 寄存器地址说明，对应仿人五指灵巧手——RH56用户手册11页，2.4寄存器说明
regdict = {
    'ID': 2,  # ID
    'baudrate': 12,  # 波特率设置
    'curLocat': 26,  # 当前位置
    'zeroCalibra': 31,  # 力传感器零位校准设置
    'overCurproSet': 32,  # 过流保护设置
    'tarLocatSet': 55,  # 目标位置设置
    'fSensorDada': 76,  # 力传感器数据
    'fOriginalValue': 78,  # 力传感器原始值
    'forceAct': 98,  # 过温保护设置
    'warmUpSta': 100,  # 回温启动设置

}


# 函数说明：设置串口号和波特率并且打开串口；参数：port为串口号，baudrate为波特率
def openSerial(port, baudrate):
    ser = serial.Serial()  # 调用串口通信函数
    ser.port = port
    ser.baudrate = baudrate
    ser.open()  # 打开串口
    return ser


# 函数说明：写电缸寄存器操作函数；参数：id为电缸ID号，add为控制表索引，num为该帧数据的部分长度，val为所要写入寄存器的数据
def writeRegister(ser, id, add, num, val):
    bytes = [0x55, 0xAA]  # 帧头
    bytes.append(num + 2)  # 帧长度
    bytes.append(id)  # ID号
    bytes.append(0x02)  # CMD_WR 写寄存器命令标志
    bytes.append(add)  # 控制表索引
    for i in range(num):
        bytes.append(val[i])
    checksum = 0x00  # 校验和初始化为0
    for i in range(2, len(bytes)):
        checksum += bytes[i]  # 对数据进行加和处理
    checksum &= 0xFF  # 对校验和取低八位
    bytes.append(checksum)  # 低八位校验和
    ser.write(bytes)  # 向串口写入数据
    time.sleep(0.01)  # 延时10ms
    ser.read_all()  # 把返回帧读掉，不处理


# 函数说明：读电缸寄存器操作；参数：id为电缸ID号，add为起始地址，num为该帧数据的部分长度。
def readRegister(ser, id, add, num, mute=False):
    bytes = [0x55, 0xAA]  # 帧头
    bytes.append(num + 2)  # 帧长度
    bytes.append(id)  # id
    bytes.append(0x01)  # CMD_RD 读寄存器命令标志
    bytes.append(add)  # 控制表索引
    bytes.append(num)
    checksum = 0x00  # 校验和赋值为0
    for i in range(2, len(bytes)):
        checksum += bytes[i]  # 对数据进行加和处理
    checksum &= 0xFF  # 对校验和取低八位
    bytes.append(checksum)  # 低八位校验和
    ser.write(bytes)  # 向串口写入数据
    time.sleep(0.01)  # 延时10ms
    recv = ser.read_all()  # 从端口读字节数据
    if len(recv) == 0:  # 如果返回的数据长度为0，直接返回
        return []
    num = (recv[2] & 0xFF) - 2  # 寄存器数据所返回的数量
    val = []
    for i in range(num):
        val.append(recv[6 + i])
    if not mute:
        print('读到的寄存器值依次为：', end='')
        for i in range(num):
            print(val[i], end=' ')
        print()
    return val


# 函数说明：查询电缸状态信息函数；参数：id为电缸ID号
def control(ser, id, num):
    bytes = [0x55, 0xAA]  # 帧头
    bytes.append(num + 2)  # 帧长度
    bytes.append(id)  # ID号
    bytes.append(0x04)  # CMD_MC 单控指令
    bytes.append(0x00)  # 参数1保留
    bytes.append(0x22)  # 查询电缸状态信息
    checksum = 0x00  # 校验和初始化为0
    for i in range(2, len(bytes)):
        checksum += bytes[i]  # 对数据进行加和处理
    checksum &= 0xFF  # 对校验和取低八位
    bytes.append(checksum)  # 低八位校验和
    ser.write(bytes)  # 向串口写入数据
    time.sleep(0.01)  # 延时10ms
    ser.read_all()  # 把返回帧读掉，不处理
    recv = ser.read_all()  # 从端口读字节数据
    if len(recv) == 0:  # 如果返回的数据长度为0，直接返回
        return []
    num = (recv[2] & 0xFF) - 2  # 寄存器数据所返回的数量
    val = []


# 函数功能：写入电缸位置数据函数，zeroCalibra为力传感器零位校准设置、overCurproSet为过流保护设置、tarLocatSet为目标位置设置、forceAct为过温保护设置、warmUpSta为回温启动设置
def writePosition(ser, id, str, val):
    if str == 'zeroCalibra' or str == 'overCurproSet' or str == 'tarLocatSet' or str == 'forceAct' or str == 'warmUpSta':
        val_reg = []
        for i in range(3):
            val_reg.append(val & 0xFF)
            val_reg.append((val >> 8) & 0xFF)
        writeRegister(ser, id, regdict[str], 6, val_reg)
    else:
        print(
            '函数调用错误，正确方式：str的值为\'zeroCalibra\'/\'overCurproSet\'/\'overCurproSet\'/\'tarLocatSet\'，val为长度为1的list，值为0~1000，允许使用-1作为占位符')


# 函数说明：广播定位模式函数；参数：id为电缸ID号，num为该帧数据的部分长度，val1为ID1电缸位置信息，val2为ID2电缸位置信息，同理valx为IDx电缸位置信息
def broadcast(ser, num, val1, val2, val3, val4, val5, val6):
    bytes = [0x55, 0xAA]  # 帧头
    bytes.append(1 + num * 3)  # 帧长度
    bytes.append(0xff)  # 广播ID
    bytes.append(0xf2)  # 定位标志
    bytes.append(0x01)  # ID1电缸
    bytes.append(val1 & 0XFF)  # 电缸1目标位置
    bytes.append((val1 >> 8) & 0XFF)  # 电缸1目标位置
    bytes.append(0x02)  # ID2电缸
    bytes.append(val2 & 0XFF)  # 电缸2目标位置
    bytes.append((val2 >> 8) & 0XFF)  # 电缸2目标位置
    bytes.append(0x03)  # ID3电缸
    bytes.append(val3 & 0XFF)  # 电缸3目标位置
    bytes.append((val3 >> 8) & 0XFF)  # 电缸3目标位置
    bytes.append(0x04)  # ID4电缸
    bytes.append(val4 & 0XFF)  # 电缸4目标位置
    bytes.append((val4 >> 8) & 0XFF)  # 电缸4目标位置
    bytes.append(0x05)  # ID5电缸
    bytes.append(val5 & 0XFF)  # 电缸5目标位置
    bytes.append((val5 >> 8) & 0XFF)  # 电缸5目标位置
    bytes.append(0x06)  # ID6电缸
    bytes.append(val6 & 0XFF)  # 电缸6目标位置
    bytes.append((val6 >> 8) & 0XFF)  # 电缸6目标位置

    checksum = 0x00  # 校验和初始化为0
    for i in range(2, len(bytes)):
        checksum += bytes[i]  # 对数据进行加和处理
    checksum &= 0xFF  # 对校验和取低八位
    bytes.append(checksum)  # 低八位校验和
    ser.write(bytes)  # 向串口写入数据
    time.sleep(0.01)  # 延时10ms


# ser.read_all()                       # 无返回帧不处理

# 主函数功能：首先打开串口，设置对应的端口和波特率，依次设置电缸运动位置参数
if __name__ == '__main__':
    print('打开串口！')  # 打印提示字符“打开串口”
    ser = openSerial('COM13', 921600)  # 改成自己的串口号和波特率，波特率默认921600
    time.sleep(1)
    print('设置电缸位置信息，-1为不设置该运动速度！')
    writePosition(ser, 1, 'tarLocatSet', 0)  # ID号改为对应电缸的ID号
    time.sleep(1)
    print('广播模式下设置6个电缸位置信息')
    broadcast(ser, 6, 1500, 0, 0, 0, 0, 0)
    time.sleep(1)
    print('读取电缸状态信息')
    control(ser, 1, 1)

    # writeRegister(ser, 1, regdict['forceClb'], 1, [1])
    # time.sleep(10) # 由于力校准时间较长，请不要漏过这个sleep并尝试重新与手通讯，可能导致插件崩溃
