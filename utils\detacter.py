import cv2
import numpy as np


def detect_irregular_red_circles(image_path, min_radius=10):
    """
    检测图像中不规则的红色圆形

    参数:
        image_path: 图像文件路径
        min_radius: 最小圆半径（默认为10）

    返回:
        - 处理过程中的各个阶段图像和最终标记结果
        - 检测到的圆的坐标和半径列表 [{'x': x, 'y': y, 'radius': r, 'method': '检测方法'}, ...]
    """
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        return None

    # 创建图像副本用于结果显示
    output = image.copy()

    # 转换到HSV色彩空间
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

    # 定义红色的HSV范围（两个范围）
    lower_red1 = np.array([0, 100, 100])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([160, 100, 100])
    upper_red2 = np.array([180, 255, 255])

    # 创建红色的掩码
    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    red_mask = cv2.bitwise_or(mask1, mask2)

    # 保存原始掩码用于展示
    original_mask = red_mask.copy()

    # 应用形态学操作来改善掩码
    # 先腐蚀去除小噪点
    kernel = np.ones((3, 3), np.uint8)
    eroded_mask = cv2.erode(red_mask, kernel, iterations=1)

    # 再膨胀恢复主要形状并连接断裂部分
    kernel = np.ones((5, 5), np.uint8)
    dilated_mask = cv2.dilate(eroded_mask, kernel, iterations=2)

    # 闭运算填充内部小孔
    kernel = np.ones((7, 7), np.uint8)
    closed_mask = cv2.morphologyEx(dilated_mask, cv2.MORPH_CLOSE, kernel)

    # 应用高斯模糊以平滑边缘
    blurred_mask = cv2.GaussianBlur(closed_mask, (9, 9), 2)

    # 方法1：使用改进的HoughCircles参数
    # 降低param2参数使其对不完美的圆更宽容
    circles = cv2.HoughCircles(
        blurred_mask,
        cv2.HOUGH_GRADIENT,
        dp=1.5,  # 增加累加器分辨率比率
        minDist=30,  # 适当增加最小距离
        param1=50,  # Canny边缘检测的高阈值
        param2=25,  # 降低累加器阈值，对不规则圆更宽容
        minRadius=min_radius,
        maxRadius=0
    )

    # 在原图上绘制检测到的圆
    result1 = output.copy()
    detected_circles = []  # 存储检测到的圆的信息

    if circles is not None:
        circles = np.uint16(np.around(circles))
        for i in circles[0, :]:
            # 记录圆的信息
            # circle_info = {
            #     'x': int(i[0]),
            #     'y': int(i[1]),
            #     'radius': int(i[2]),
            #     'method': 'hough'
            # }
            detected_circles.append([int(i[0]),int(i[1])])

            # 绘制外圆
            cv2.circle(result1, (i[0], i[1]), i[2], (0, 255, 0), 2)
            # 绘制圆心
            cv2.circle(result1, (i[0], i[1]), 2, (0, 0, 255), 3)
            # 标记半径
            cv2.putText(result1, f"r={i[2]}", (i[0], i[1] - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)

            # 保存图片
            image_path = "center_point_detect.jpg"
            cv2.imwrite(image_path, result1)
    return detected_circles


# 主程序



# 主程序
if __name__ == "__main__":
    # 替换为你的图像路径
    image_path = r"D:\work\HWTreeATE\utils\center_point.jpg"
    min_radius = 5

    # 检测不规则红色圆形
    results = detect_irregular_red_circles(image_path, min_radius)
    print(results)
    # if results is not None:
    #     # 显示各阶段处理结果
    #     # cv2.imshow("Original Image", results['original'])
    #     # cv2.imshow("Original Red Mask", results['original_mask'])
    #     # cv2.imshow("Processed Mask", results['processed_mask'])
    #     # cv2.imshow("HoughCircles Result", results['hough_result'])
    #     cv2.imshow("Contour Method Result", results['contour_result'])
    #     cv2.waitKey(0)
    #     cv2.destroyAllWindows()