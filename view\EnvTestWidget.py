import threading
import time

from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QWidget, QTableWidgetItem, QHeaderView, QListView
from modbus_tk import defines

from common.LogUtils import logger
from common.view.ConfirmDialog import ConfirmDialog
from common.view.MessageDialog import MessageDialog
from environment.TemperatureManager import temperature_manager
from ui.EnvTest import Ui_EnvForm
from utils.ProjectManager import project_manager
from utils.SignalsManager import signals_manager
from environment.TemperatureClient import temperature_client


class EnvTestWidget(Ui_EnvForm, QWidget):

    def __init__(self, parent=None):
        super(EnvTestWidget, self).__init__(parent)
        self.setupUi(self)
        self.setWindowTitle("温升测试工具")
        self.case_id = ""
        self.command = ""
        self.is_stop = False
        self.suspend_flag = False
        self.thread = None
        self.channel_list = []
        self.init()
        self.pushButtonStop.clicked.connect(self.stop)
        self.confirm_dialog = ConfirmDialog(self)
        signals_manager.start_env_temperature_rise_signal.connect(self.start_env_temperature_rise)
        self.tableWidget.horizontalHeader().setFixedHeight(40)
        self.tableWidget.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.tableWidget.setFocusPolicy(Qt.NoFocus)

    def init(self):
        self.update_table_header()
        self.pushButtonStart.clicked.connect(self.start)
        self.pushButtonPause.clicked.connect(self.pause)
        self.pushButtonAdd.clicked.connect(self.add_channel)
        self.pushButtonDelChl.clicked.connect(self.del_channel)
        signals_manager.temp_data_update.connect(self.update_table)

    def add_channel(self):
        logger.info("add_channel")
        channel = self.spinBoxChl.value()
        self.spinBoxChl.setValue(channel + 1)
        if channel in self.channel_list:
            return
        self.channel_list.append(channel)
        self.update_table_header()

    def set_unable(self):
        self.comboBoxDevice.setEnabled(False)
        self.pushButtonAdd.setEnabled(False)
        self.pushButtonDelChl.setEnabled(False)
        self.comboBoxScenarios.setEnabled(False)
        self.pushButtonStart.setEnabled(False)

    def resume(self):
        self.comboBoxDevice.setEnabled(True)
        self.pushButtonAdd.setEnabled(True)
        self.pushButtonDelChl.setEnabled(True)
        self.comboBoxScenarios.setEnabled(True)
        self.pushButtonStart.setEnabled(True)

    def del_channel(self):
        if len(self.channel_list) > 0:
            self.channel_list.pop()
        self.update_table_header()

    def update_table_header(self):
        logger.info("update_table_header")
        head = ["时间"] + ["产品屏幕温度"] + ["通道" + str(chl) for chl in self.channel_list]
        self.tableWidget.setColumnCount(len(head))
        self.tableWidget.setHorizontalHeaderLabels(head)
        self.tableWidget.verticalHeader().hide()
        for i in range(1, len(head)):
            self.tableWidget.horizontalHeader().setSectionResizeMode(i, 1)

    def pause(self):
        logger.info("pause")
        if self.pushButtonPause.text() == "暂停":
            self.suspend_flag = True
            self.pushButtonPause.setText("继续")
        else:
            self.suspend_flag = False
            self.pushButtonPause.setText("暂停")

    def update_table(self, result):
        logger.info(f"update_table result={result}")
        row_position = self.tableWidget.rowCount()
        self.tableWidget.insertRow(row_position)
        for col, value in enumerate(result):
            item = QTableWidgetItem(value)
            item.setTextAlignment(Qt.AlignCenter)
            self.tableWidget.setItem(row_position, col, item)
            self.tableWidget.setRowHeight(row_position, 45)

        # 自动滑动到最后一行
        self.tableWidget.setAutoScroll(True)
        self.tableWidget.scrollToBottom()

    def start(self):
        logger.info("start")
        total_time = self.spinBoxTestTime.value() * 60
        frequency = self.spinBoxFrequency.value()
        if total_time <= 0 or frequency <= 0:
            return MessageDialog.show_message("错误", "测试时间或者采集频率不能为0")

        def callback_result(result: int):
            if result == 0x01:
                self.start_env_temperature_rise()

        self.confirm_dialog.show_dialog(title='请确认是否启动', confirm_text="确认", cancel_text="取消")
        self.confirm_dialog.callback_result = callback_result

    def start_env_temperature_rise(self, case_id="", command=""):
        logger.info("start_env_temperature_rise case_id={}, command={}".format(case_id, command))
        self.case_id = case_id
        self.command = command
        self.is_stop = False
        self.comboBoxDevice.setEnabled(False)
        self.set_unable()
        count = self.tableWidget.rowCount()
        for i in range(count):
            self.tableWidget.removeRow(0)

        self.thread = threading.Thread(target=self.get_data)
        self.thread.start()

    def stop(self):
        self.is_stop = True
        self.resume()
        head = ["时间"] + ["产品屏幕温度"] + [f"通道{i}" for i in self.channel_list]
        result = self.get_table_result()
        signals_manager.temperature_test_finished.emit(head, result)

    def get_data(self):
        self.comboBoxDevice.setEnabled(False)
        total_time = self.spinBoxTestTime.value() * 60
        frequency = self.spinBoxFrequency.value()
        while not self.is_stop:
            if not self.suspend_flag:
                t = QTableWidgetItem(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())) + " ")
                device = self.comboBoxDevice.currentText()
                result = [t]
                temp = temperature_manager.get_screen_temp(project_number=project_manager.get_project_number())
                logger.info(f"get_data temp={temp}")
                result.append(str(temp))
                for num in self.channel_list:
                    if device == "TP1000" or device == "TP710A":
                        tem = temperature_client.execute(defines.READ_HOLDING_REGISTERS,
                                                         starting_address=(int(num) - 1) * 2,
                                                         quantity_of_x=2,
                                                         data_format=">f")[0]
                        tem = str(round(tem, 2))
                        result.append(tem)
                    elif device == "TP700":
                        tem = str(temperature_client.read_hr_one(2 * int(num) - 1))
                        tem = tem[0:2] + "." + tem[2:]
                        result.append(tem)

                signals_manager.temp_data_update.emit(result)
                time.sleep(frequency)
                total_time = total_time - frequency
                if total_time <= 0:
                    break
            else:
                time.sleep(0.2)

        head = ["时间"] + ["产品屏幕温度"] + [f"通道{i}" for i in self.channel_list]
        self.resume()
        result = self.get_table_result()
        signals_manager.temperature_test_finished.emit(head, result)
        signals_manager.step_execute_finish.emit(self.case_id, self.command, "PASS", "PASS")

    def get_table_result(self):
        result = []
        column_count = self.tableWidget.columnCount()
        row_count = self.tableWidget.rowCount()
        for i in range(row_count):
            row_data = []
            for j in range(column_count):
                item = self.tableWidget.item(i, j)
                if item and item.text():
                    row_data.append(item.text())
                else:
                    row_data.append("")
            result.append(row_data)
        return result
