from PyQt5.QtCore import QObject, pyqtSignal

from common.modbus.RTUClient import RTUClient


class BaseIO(QObject):

    state_signal = pyqtSignal(bool)
    error_signal = pyqtSignal(int, str)

    def __init__(self):
        super().__init__()
        self.client: RTUClient = None

    def connect(self, port, baud_rate=9600, bytesize=8, parity="N", stop_bits=1, slave_id=1, timeout=2.0):
        try:
            self.client = RTUClient(port=port, baud_rate=baud_rate, bytesize=bytesize,
                                    parity=parity, stop_bits=stop_bits, slave_id=slave_id,
                                    timeout=timeout)
            self.client.error_tips = lambda code, msg: self.error_signal.emit(code, msg)
            self.client.open()
            self.state_signal.emit(self.is_open())
            return self.is_open()
        except Exception as e:
            self.error_signal.emit(100, u'串口连接异常:%s' % str(e.args))
            return False

    def disconnect(self):
        if self.is_open():
            self.client.close()
            self.state_signal.emit(False)

    def is_open(self):
        if self.client is None:
            return False
        else:
            return self.client.is_open()

    def power_on(self, position):
        """
        输出量闭合
        :param position:
        :return:
        """
        pass

    def power_off(self, position):
        """
        输出量断开
        :param position:
        :return:
        """
        pass

    def read_electric_current(self):
        """
        读取设备电流
        :return:
        """
        pass
