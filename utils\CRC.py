def seed_to_key(level, gkey_array):
    seed_length = 6
    crc_byte = [0] * 7
    seed = [0] * 6
    seed[0] = gkey_array[0]
    seed[1] = gkey_array[1]
    seed[2] = gkey_array[2]
    seed[3] = gkey_array[3]

    if level == 0x02:
        seed[4] = 0x9B
        seed[5] = 0x1D
    elif level == 0x04:
        seed[4] = 0x32
        seed[5] = 0x50
    else:
        return 0
    buf_byte = seed[:]
    crc_byte[0] = crc8(buf_byte, seed_length)
    buf_byte[0] = crc_byte[0]

    crc_byte[1] = crc8(buf_byte, seed_length)
    buf_byte[0] = seed[0]
    buf_byte[1] = crc_byte[1]

    crc_byte[2] = crc8(buf_byte, seed_length)
    buf_byte[1] = seed[1]
    buf_byte[2] = crc_byte[2]

    crc_byte[3] = crc8(buf_byte, seed_length)
    buf_byte[2] = seed[2]
    buf_byte[3] = crc_byte[3]

    crc_byte[4] = crc8(buf_byte, seed_length)
    buf_byte[3] = seed[3]
    buf_byte[4] = crc_byte[4]

    crc_byte[5] = crc8(buf_byte, seed_length)
    buf_byte[4] = seed[4]
    buf_byte[5] = crc_byte[5]

    crc_byte[6] = crc8(buf_byte, seed_length)

    if crc_byte[3] == 0 and crc_byte[4] == 0 and crc_byte[5] == 0 and crc_byte[6] == 0:
        un_lock_key = [crc_byte[1], crc_byte[2], crc_byte[3], crc_byte[4]]
    else:
        un_lock_key = [crc_byte[3], crc_byte[4], crc_byte[5], crc_byte[6]]

    return un_lock_key


def seed_to_key2(level, gkey_array):
    seed_length = 6
    crc_byte = [0] * 7
    seed = [0] * 6
    seed[0] = gkey_array[0]
    seed[1] = gkey_array[1]
    seed[2] = gkey_array[2]
    seed[3] = gkey_array[3]

    if level == 0x02:
        seed[4] = 0x1F
        seed[5] = 0xB3
    elif level == 0x04:
        seed[4] = 0xD4
        seed[5] = 0x82
    else:
        return 0
    buf_byte = seed[:]
    crc_byte[0] = crc8(buf_byte, seed_length)
    buf_byte[0] = crc_byte[0]

    crc_byte[1] = crc8(buf_byte, seed_length)
    buf_byte[0] = seed[0]
    buf_byte[1] = crc_byte[1]

    crc_byte[2] = crc8(buf_byte, seed_length)
    buf_byte[1] = seed[1]
    buf_byte[2] = crc_byte[2]

    crc_byte[3] = crc8(buf_byte, seed_length)
    buf_byte[2] = seed[2]
    buf_byte[3] = crc_byte[3]

    crc_byte[4] = crc8(buf_byte, seed_length)
    buf_byte[3] = seed[3]
    buf_byte[4] = crc_byte[4]

    crc_byte[5] = crc8(buf_byte, seed_length)
    buf_byte[4] = seed[4]
    buf_byte[5] = crc_byte[5]

    crc_byte[6] = crc8(buf_byte, seed_length)

    if crc_byte[3] == 0 and crc_byte[4] == 0 and crc_byte[5] == 0 and crc_byte[6] == 0:
        un_lock_key = [crc_byte[1], crc_byte[2], crc_byte[3], crc_byte[4]]
    else:
        un_lock_key = [crc_byte[3], crc_byte[4], crc_byte[5], crc_byte[6]]

    return un_lock_key

def seed_to_key3(level, gSeedArray,position="right"):
    unLockKey =[0,0,0,0]
    seed = [0] * 6
    Char_Cal = [0] * 4
    # Char_Key = [0] * 4
    Char_Xor = [0] * 4

    # 初始化seed数组
    seed[0] = gSeedArray[0]
    seed[1] = gSeedArray[1]
    seed[2] = gSeedArray[2]
    seed[3] = gSeedArray[3]
    if position == "right":
        # 初始化Char_Xor数组
        Char_Xor[0] = 0xC9
        Char_Xor[1] = 0x7D
        Char_Xor[2] = 0x5C
        Char_Xor[3] = 0x05
    else:
        Char_Xor[0] = 0x25
        Char_Xor[1] = 0xED
        Char_Xor[2] = 0x2F
        Char_Xor[3] = 0xBA

    for i in range(4):
        Char_Cal[i] = seed[i] ^ Char_Xor[i]

    if level == 1:
        unLockKey[0] = ((Char_Cal[0] & 0x0f) << 4) | (Char_Cal[1] & 0xf0)
        unLockKey[1] = ((Char_Cal[1] & 0x0f) << 4) | ((Char_Cal[2] & 0xf0) >> 4)
        unLockKey[2] = (Char_Cal[2] & 0xf0) | ((Char_Cal[3] & 0xf0) >> 4)
        unLockKey[3] = ((Char_Cal[3] & 0x0f) << 4) | (Char_Cal[0] & 0x0f)
    elif level == 4:
        unLockKey[0] = ((Char_Cal[0] & 0x0f) << 4) | (Char_Cal[1] & 0x0f)
        unLockKey[1] = ((Char_Cal[1] & 0xf0) >> 4) | ((Char_Cal[2] & 0x0f) << 4)
        unLockKey[2] = ((Char_Cal[2] & 0xf0) >> 4) | (Char_Cal[3] & 0xf0)
        unLockKey[3] = (Char_Cal[3] & 0x0f) | ((Char_Cal[0] & 0xf0) >> 4)

    return unLockKey
def crc8(data, length):
    t_crc = 0xFF
    for f in range(length):
        t_crc ^= data[f]
        for b in range(8):
            if t_crc & 0x80 != 0:
                t_crc = (t_crc << 1) ^ 0x1D
            else:
                t_crc <<= 1
    return ~t_crc & 0xFF


def seed_to_key4(level, gSeedArray,position="right"):

    seed = bytearray(6)
    Char_Cal = bytearray(4)
    Char_Key = bytearray(4)
    if position == "right":
        Char_Xor = bytearray([0xC9, 0x7D, 0x5C, 0x05])
    else:
        # Char_Xor = bytearray([0x25, 0xED, 0x2F, 0xBA])
        Char_Xor = bytearray([0x84, 0xf4, 0x75, 0x50])

    # Assuming gSeedArray is a global variable in the original code
    # You might need to pass it as an argument or define it elsewhere
    seed[:4] = gSeedArray[:4]

    for i in range(4):
        Char_Cal[i] = seed[i] ^ Char_Xor[i]

    unLockKey = bytearray(4)

    if level == 1:
        unLockKey[0] = ((Char_Cal[0] & 0x0f) << 4) | (Char_Cal[1] & 0xf0)
        unLockKey[1] = ((Char_Cal[1] & 0x0f) << 4) | ((Char_Cal[2] & 0xf0) >> 4)
        unLockKey[2] = (Char_Cal[2] & 0xf0) | ((Char_Cal[3] & 0xf0) >> 4)
        unLockKey[3] = ((Char_Cal[3] & 0x0f) << 4) | (Char_Cal[0] & 0x0f)
    elif level == 4:
        unLockKey[0] = ((Char_Cal[0] & 0x0f) << 4) | (Char_Cal[1] & 0x0f)
        unLockKey[1] = ((Char_Cal[1] & 0xf0) >> 4) | ((Char_Cal[2] & 0x0f) << 4)
        unLockKey[2] = ((Char_Cal[2] & 0xf0) >> 4) | (Char_Cal[3] & 0xf0)
        unLockKey[3] = (Char_Cal[3] & 0x0f) | ((Char_Cal[0] & 0xf0) >> 4)

    return unLockKey

def crc8(data, length):
    t_crc = 0xFF
    for f in range(length):
        t_crc ^= data[f]
        for b in range(8):
            if t_crc & 0x80:
                t_crc = (t_crc << 1) & 0xFF  # Keep it within 8 bits
                t_crc ^= 0x11D
            else:
                t_crc = (t_crc << 1) & 0xFF  # Keep it within 8 bits
    return ~t_crc & 0xFF  # Keep it within 8 bits

def getkey(myseed):
    seedlength = 6
    buf_byte = list(myseed)
    crc_byte = [0] * 7

    crc_byte[0] = crc8(buf_byte, seedlength)
    buf_byte[0] = crc_byte[0]
    crc_byte[1] = crc8(buf_byte, seedlength)

    for i in range(5):
        buf_byte[i] = myseed[i]
        buf_byte[i+1] = crc_byte[i+1]
        crc_byte[i+2] = crc8(buf_byte, seedlength)

    if crc_byte[3] == 0 and crc_byte[4] == 0 and crc_byte[5] == 0 and crc_byte[6] == 0:
        return crc_byte[1:5]
    else:
        return crc_byte[3:7]

def SeedToKey_Mate(ipSeed, level):
    seed = list(ipSeed[:4])

    if level == 0x02:
        seed.extend([0x2D, 0xAE])
    elif level == 0x04:
        seed.extend([0x49, 0xCB])
    else:
        return 1

    opKey = getkey(seed)
    return opKey if opKey[0] != 0 or len(opKey) == 4 else 1



if __name__ == '__main__':

    #

    # g_key_array = [0x55, 0x97, 0x9d,0xbe]# ca 1e bc 0b
    # g_key_array = [0xc5, 0x26, 0xd3,0x2c]
    # result = seed_to_key4(0x01, g_key_array,position ="left")
    # print(result)
    # a = " ".join(hex(i)[2:].zfill(2) for i in result)
    # print(a, type(a))
    #
    # a = "12 22 33"
    #
    # # 将字符串分割,扩展到8个元素,不足的用"00"填充,然后重新组合
    # result = " ".join((a.split() + ["00"] * 8)[:8])
    #
    # print(result)

    # Example usage 6E 44 87 C3
    ipSeed = [0x6e, 0x44, 0x87, 0xc3]
    level = 0x02
    opKey = SeedToKey_Mate(ipSeed, level)
    d = []
    for i in opKey:
        d.append(hex(i))
    print("Generated Key:", " ".join(d).replace("0x","").replace("0X",""))
