# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'UIPicture.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_PictureWindow(object):
    def setupUi(self, PictureWindow):
        PictureWindow.setObjectName("PictureWindow")
        PictureWindow.resize(1600, 900)
        PictureWindow.setMinimumSize(QtCore.QSize(1600, 900))
        self.centralwidget = QtWidgets.QWidget(PictureWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.centralwidget)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.label_8 = QtWidgets.QLabel(self.centralwidget)
        self.label_8.setObjectName("label_8")
        self.horizontalLayout.addWidget(self.label_8)
        self.pic_width = QtWidgets.QSpinBox(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pic_width.sizePolicy().hasHeightForWidth())
        self.pic_width.setSizePolicy(sizePolicy)
        self.pic_width.setMinimumSize(QtCore.QSize(0, 45))
        self.pic_width.setMaximum(999999999)
        self.pic_width.setProperty("value", 99)
        self.pic_width.setObjectName("pic_width")
        self.horizontalLayout.addWidget(self.pic_width)
        self.label_9 = QtWidgets.QLabel(self.centralwidget)
        self.label_9.setAlignment(QtCore.Qt.AlignCenter)
        self.label_9.setObjectName("label_9")
        self.horizontalLayout.addWidget(self.label_9)
        self.pic_height = QtWidgets.QSpinBox(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pic_height.sizePolicy().hasHeightForWidth())
        self.pic_height.setSizePolicy(sizePolicy)
        self.pic_height.setMinimumSize(QtCore.QSize(0, 45))
        self.pic_height.setMaximum(999999999)
        self.pic_height.setProperty("value", 99)
        self.pic_height.setObjectName("pic_height")
        self.horizontalLayout.addWidget(self.pic_height)
        self.verticalLayout.addLayout(self.horizontalLayout)
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.label = QtWidgets.QLabel(self.centralwidget)
        self.label.setObjectName("label")
        self.horizontalLayout_5.addWidget(self.label)
        self.gray_picture = QtWidgets.QRadioButton(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.gray_picture.sizePolicy().hasHeightForWidth())
        self.gray_picture.setSizePolicy(sizePolicy)
        self.gray_picture.setChecked(True)
        self.gray_picture.setObjectName("gray_picture")
        self.horizontalLayout_5.addWidget(self.gray_picture)
        self.chess_picture = QtWidgets.QRadioButton(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.chess_picture.sizePolicy().hasHeightForWidth())
        self.chess_picture.setSizePolicy(sizePolicy)
        self.chess_picture.setAutoRepeatDelay(300)
        self.chess_picture.setAutoRepeatInterval(100)
        self.chess_picture.setObjectName("chess_picture")
        self.horizontalLayout_5.addWidget(self.chess_picture)
        self.custom_picture = QtWidgets.QRadioButton(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.custom_picture.sizePolicy().hasHeightForWidth())
        self.custom_picture.setSizePolicy(sizePolicy)
        self.custom_picture.setObjectName("custom_picture")
        self.horizontalLayout_5.addWidget(self.custom_picture)
        self.radioButton = QtWidgets.QRadioButton(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.radioButton.sizePolicy().hasHeightForWidth())
        self.radioButton.setSizePolicy(sizePolicy)
        self.radioButton.setObjectName("radioButton")
        self.horizontalLayout_5.addWidget(self.radioButton)
        self.verticalLayout.addLayout(self.horizontalLayout_5)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.label_5 = QtWidgets.QLabel(self.centralwidget)
        self.label_5.setObjectName("label_5")
        self.horizontalLayout_3.addWidget(self.label_5)
        self.gray_star = QtWidgets.QSpinBox(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.gray_star.sizePolicy().hasHeightForWidth())
        self.gray_star.setSizePolicy(sizePolicy)
        self.gray_star.setMinimumSize(QtCore.QSize(0, 45))
        self.gray_star.setMaximum(255)
        self.gray_star.setObjectName("gray_star")
        self.horizontalLayout_3.addWidget(self.gray_star)
        self.label_6 = QtWidgets.QLabel(self.centralwidget)
        self.label_6.setAlignment(QtCore.Qt.AlignCenter)
        self.label_6.setObjectName("label_6")
        self.horizontalLayout_3.addWidget(self.label_6)
        self.gray_end = QtWidgets.QSpinBox(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.gray_end.sizePolicy().hasHeightForWidth())
        self.gray_end.setSizePolicy(sizePolicy)
        self.gray_end.setMinimumSize(QtCore.QSize(0, 45))
        self.gray_end.setMaximum(255)
        self.gray_end.setObjectName("gray_end")
        self.horizontalLayout_3.addWidget(self.gray_end)
        self.verticalLayout.addLayout(self.horizontalLayout_3)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.label_11 = QtWidgets.QLabel(self.centralwidget)
        self.label_11.setObjectName("label_11")
        self.horizontalLayout_2.addWidget(self.label_11)
        self.color_1_btn = QtWidgets.QPushButton(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.color_1_btn.sizePolicy().hasHeightForWidth())
        self.color_1_btn.setSizePolicy(sizePolicy)
        self.color_1_btn.setMinimumSize(QtCore.QSize(0, 50))
        self.color_1_btn.setText("")
        self.color_1_btn.setObjectName("color_1_btn")
        self.horizontalLayout_2.addWidget(self.color_1_btn)
        self.label_13 = QtWidgets.QLabel(self.centralwidget)
        self.label_13.setObjectName("label_13")
        self.horizontalLayout_2.addWidget(self.label_13)
        self.color_2_btn = QtWidgets.QPushButton(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.color_2_btn.sizePolicy().hasHeightForWidth())
        self.color_2_btn.setSizePolicy(sizePolicy)
        self.color_2_btn.setMinimumSize(QtCore.QSize(0, 50))
        self.color_2_btn.setText("")
        self.color_2_btn.setObjectName("color_2_btn")
        self.horizontalLayout_2.addWidget(self.color_2_btn)
        self.label_7 = QtWidgets.QLabel(self.centralwidget)
        self.label_7.setObjectName("label_7")
        self.horizontalLayout_2.addWidget(self.label_7)
        self.chess_width = QtWidgets.QSpinBox(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.chess_width.sizePolicy().hasHeightForWidth())
        self.chess_width.setSizePolicy(sizePolicy)
        self.chess_width.setMinimumSize(QtCore.QSize(0, 45))
        self.chess_width.setMinimum(1)
        self.chess_width.setMaximum(500)
        self.chess_width.setObjectName("chess_width")
        self.horizontalLayout_2.addWidget(self.chess_width)
        self.label_12 = QtWidgets.QLabel(self.centralwidget)
        self.label_12.setObjectName("label_12")
        self.horizontalLayout_2.addWidget(self.label_12)
        self.spinBox = QtWidgets.QSpinBox(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinBox.sizePolicy().hasHeightForWidth())
        self.spinBox.setSizePolicy(sizePolicy)
        self.spinBox.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox.setMinimum(1)
        self.spinBox.setMaximum(500)
        self.spinBox.setObjectName("spinBox")
        self.horizontalLayout_2.addWidget(self.spinBox)
        self.verticalLayout.addLayout(self.horizontalLayout_2)
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.label_2 = QtWidgets.QLabel(self.centralwidget)
        self.label_2.setObjectName("label_2")
        self.horizontalLayout_4.addWidget(self.label_2)
        self.color_r = QtWidgets.QSpinBox(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.color_r.sizePolicy().hasHeightForWidth())
        self.color_r.setSizePolicy(sizePolicy)
        self.color_r.setMinimumSize(QtCore.QSize(0, 45))
        self.color_r.setMaximum(255)
        self.color_r.setObjectName("color_r")
        self.horizontalLayout_4.addWidget(self.color_r)
        self.label_3 = QtWidgets.QLabel(self.centralwidget)
        self.label_3.setObjectName("label_3")
        self.horizontalLayout_4.addWidget(self.label_3)
        self.color_g = QtWidgets.QSpinBox(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.color_g.sizePolicy().hasHeightForWidth())
        self.color_g.setSizePolicy(sizePolicy)
        self.color_g.setMinimumSize(QtCore.QSize(0, 45))
        self.color_g.setMaximum(255)
        self.color_g.setObjectName("color_g")
        self.horizontalLayout_4.addWidget(self.color_g)
        self.label_4 = QtWidgets.QLabel(self.centralwidget)
        self.label_4.setObjectName("label_4")
        self.horizontalLayout_4.addWidget(self.label_4)
        self.color_b = QtWidgets.QSpinBox(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.color_b.sizePolicy().hasHeightForWidth())
        self.color_b.setSizePolicy(sizePolicy)
        self.color_b.setMinimumSize(QtCore.QSize(0, 45))
        self.color_b.setMaximum(255)
        self.color_b.setObjectName("color_b")
        self.horizontalLayout_4.addWidget(self.color_b)
        self.verticalLayout.addLayout(self.horizontalLayout_4)
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.label_14 = QtWidgets.QLabel(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_14.sizePolicy().hasHeightForWidth())
        self.label_14.setSizePolicy(sizePolicy)
        self.label_14.setObjectName("label_14")
        self.horizontalLayout_7.addWidget(self.label_14)
        self.label_16 = QtWidgets.QLabel(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_16.sizePolicy().hasHeightForWidth())
        self.label_16.setSizePolicy(sizePolicy)
        self.label_16.setObjectName("label_16")
        self.horizontalLayout_7.addWidget(self.label_16)
        self.spinBox_2 = QtWidgets.QSpinBox(self.centralwidget)
        self.spinBox_2.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_2.setMaximum(255)
        self.spinBox_2.setObjectName("spinBox_2")
        self.horizontalLayout_7.addWidget(self.spinBox_2)
        self.label_17 = QtWidgets.QLabel(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_17.sizePolicy().hasHeightForWidth())
        self.label_17.setSizePolicy(sizePolicy)
        self.label_17.setObjectName("label_17")
        self.horizontalLayout_7.addWidget(self.label_17)
        self.spinBox_3 = QtWidgets.QSpinBox(self.centralwidget)
        self.spinBox_3.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_3.setMaximum(255)
        self.spinBox_3.setObjectName("spinBox_3")
        self.horizontalLayout_7.addWidget(self.spinBox_3)
        self.label_18 = QtWidgets.QLabel(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_18.sizePolicy().hasHeightForWidth())
        self.label_18.setSizePolicy(sizePolicy)
        self.label_18.setObjectName("label_18")
        self.horizontalLayout_7.addWidget(self.label_18)
        self.spinBox_4 = QtWidgets.QSpinBox(self.centralwidget)
        self.spinBox_4.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_4.setMaximum(255)
        self.spinBox_4.setObjectName("spinBox_4")
        self.horizontalLayout_7.addWidget(self.spinBox_4)
        self.label_15 = QtWidgets.QLabel(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_15.sizePolicy().hasHeightForWidth())
        self.label_15.setSizePolicy(sizePolicy)
        self.label_15.setObjectName("label_15")
        self.horizontalLayout_7.addWidget(self.label_15)
        self.label_19 = QtWidgets.QLabel(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_19.sizePolicy().hasHeightForWidth())
        self.label_19.setSizePolicy(sizePolicy)
        self.label_19.setObjectName("label_19")
        self.horizontalLayout_7.addWidget(self.label_19)
        self.spinBox_5 = QtWidgets.QSpinBox(self.centralwidget)
        self.spinBox_5.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_5.setMaximum(255)
        self.spinBox_5.setProperty("value", 255)
        self.spinBox_5.setObjectName("spinBox_5")
        self.horizontalLayout_7.addWidget(self.spinBox_5)
        self.label_20 = QtWidgets.QLabel(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_20.sizePolicy().hasHeightForWidth())
        self.label_20.setSizePolicy(sizePolicy)
        self.label_20.setObjectName("label_20")
        self.horizontalLayout_7.addWidget(self.label_20)
        self.spinBox_6 = QtWidgets.QSpinBox(self.centralwidget)
        self.spinBox_6.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_6.setMaximum(255)
        self.spinBox_6.setProperty("value", 255)
        self.spinBox_6.setObjectName("spinBox_6")
        self.horizontalLayout_7.addWidget(self.spinBox_6)
        self.label_21 = QtWidgets.QLabel(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_21.sizePolicy().hasHeightForWidth())
        self.label_21.setSizePolicy(sizePolicy)
        self.label_21.setObjectName("label_21")
        self.horizontalLayout_7.addWidget(self.label_21)
        self.spinBox_7 = QtWidgets.QSpinBox(self.centralwidget)
        self.spinBox_7.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_7.setMaximum(255)
        self.spinBox_7.setProperty("value", 255)
        self.spinBox_7.setObjectName("spinBox_7")
        self.horizontalLayout_7.addWidget(self.spinBox_7)
        self.verticalLayout.addLayout(self.horizontalLayout_7)
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.label_10 = QtWidgets.QLabel(self.centralwidget)
        self.label_10.setObjectName("label_10")
        self.horizontalLayout_6.addWidget(self.label_10)
        self.path_line = QtWidgets.QLineEdit(self.centralwidget)
        self.path_line.setMinimumSize(QtCore.QSize(0, 45))
        self.path_line.setReadOnly(True)
        self.path_line.setObjectName("path_line")
        self.horizontalLayout_6.addWidget(self.path_line)
        self.path_button = QtWidgets.QPushButton(self.centralwidget)
        self.path_button.setObjectName("path_button")
        self.horizontalLayout_6.addWidget(self.path_button)
        self.verticalLayout.addLayout(self.horizontalLayout_6)
        self.create_button = QtWidgets.QPushButton(self.centralwidget)
        self.create_button.setObjectName("create_button")
        self.verticalLayout.addWidget(self.create_button)
        PictureWindow.setCentralWidget(self.centralwidget)
        self.menubar = QtWidgets.QMenuBar(PictureWindow)
        self.menubar.setGeometry(QtCore.QRect(0, 0, 1600, 18))
        self.menubar.setObjectName("menubar")
        PictureWindow.setMenuBar(self.menubar)
        self.statusbar = QtWidgets.QStatusBar(PictureWindow)
        self.statusbar.setObjectName("statusbar")
        PictureWindow.setStatusBar(self.statusbar)

        self.retranslateUi(PictureWindow)
        QtCore.QMetaObject.connectSlotsByName(PictureWindow)

    def retranslateUi(self, PictureWindow):
        _translate = QtCore.QCoreApplication.translate
        PictureWindow.setWindowTitle(_translate("PictureWindow", "MainWindow"))
        self.label_8.setText(_translate("PictureWindow", "分辨率:"))
        self.label_9.setText(_translate("PictureWindow", "*"))
        self.label.setText(_translate("PictureWindow", "类型:"))
        self.gray_picture.setText(_translate("PictureWindow", "灰阶图片"))
        self.chess_picture.setText(_translate("PictureWindow", "棋盘格"))
        self.custom_picture.setText(_translate("PictureWindow", "自定义"))
        self.radioButton.setText(_translate("PictureWindow", "渐变图片"))
        self.label_5.setText(_translate("PictureWindow", "灰阶范围:"))
        self.label_6.setText(_translate("PictureWindow", "~"))
        self.label_11.setText(_translate("PictureWindow", "色块1"))
        self.label_13.setText(_translate("PictureWindow", "色块2"))
        self.label_7.setText(_translate("PictureWindow", "宽度方格个数:"))
        self.label_12.setText(_translate("PictureWindow", "高度方格个数"))
        self.label_2.setText(_translate("PictureWindow", "R:"))
        self.label_3.setText(_translate("PictureWindow", "G:"))
        self.label_4.setText(_translate("PictureWindow", "B:"))
        self.label_14.setText(_translate("PictureWindow", "开始:"))
        self.label_16.setText(_translate("PictureWindow", "R:"))
        self.label_17.setText(_translate("PictureWindow", "G:"))
        self.label_18.setText(_translate("PictureWindow", "B:"))
        self.label_15.setText(_translate("PictureWindow", "结束:"))
        self.label_19.setText(_translate("PictureWindow", "R:"))
        self.spinBox_5.setSpecialValueText(_translate("PictureWindow", "0"))
        self.label_20.setText(_translate("PictureWindow", "G:"))
        self.spinBox_6.setSpecialValueText(_translate("PictureWindow", "0"))
        self.label_21.setText(_translate("PictureWindow", "B:"))
        self.spinBox_7.setSpecialValueText(_translate("PictureWindow", "0"))
        self.label_10.setText(_translate("PictureWindow", "保存路径:"))
        self.path_button.setText(_translate("PictureWindow", "选择"))
        self.create_button.setText(_translate("PictureWindow", "创建图片"))
