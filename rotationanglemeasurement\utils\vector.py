import cv2
import math
import numpy as np
from scipy.optimize import least_squares

# 定义一个向量类
class Vector:
    def __init__(self, x, y, z):
        self.x = x
        self.y = y
        self.z = z

    # 计算向量的模长
    def magnitude(self):
        return math.sqrt(self.x**2 + self.y**2 + self.z**2)

    # 计算向量与 xy 平面的夹角
    def angle_xy(self):
        return math.degrees(math.acos(self.z / self.magnitude()))

    # 计算向量与 yz 平面的夹角
    def angle_yz(self):
        return math.degrees(math.acos(self.x / self.magnitude()))

# # 创建一个向量对象
# N = Vector(1, 2, 3)
#
# # 打印结果
# print("N 与 xy 平面的夹角度数 =", N.angle_xy())
# print("N 与 yz 平面的夹角度数 =", N.angle_yz())

class CameraCalculation():
    def __init__(self):
        pass
    @staticmethod
    def compute_plane_normal_and_angles(point): # calc_vector
        # 定义三个点的坐标
        x1, y1, z1 = point[0][0],point[0][1],point[0][2]
        x2, y2, z2 = point[1][0],point[1][1],point[1][2]
        x3, y3, z3 = point[2][0],point[2][1],point[2][2],

        # 求出两个向量的坐标
        AB = (x2 - x1, y2 - y1, z2 - z1)
        AC = (x3 - x1, y3 - y1, z3 - z1)

        # 用叉乘求出法向量的坐标
        N = (AB[1] * AC[2] - AB[2] * AC[1], AB[2] * AC[0] - AB[0] * AC[2], AB[0] * AC[1] - AB[1] * AC[0])

        # 如果需要，可以对法向量进行归一化
        import math  # 导入数学模块
        norm = math.sqrt(N[0] ** 2 + N[1] ** 2 + N[2] ** 2)  # 计算法向量的模长
        N = (N[0] / norm, N[1] / norm, N[2] / norm)  # 对法向量进行归一化

        # 打印结果
        print("The normal vector of the plane is:", N)
        N = Vector(N[0], N[1], N[2])
        print("头部平面的夹角度数 =", N.angle_xy())
        print("基座的夹角度数 =", N.angle_yz())
        # self.labelResult.setText("头部平面的夹角度数 = {}\n基座的夹角度数 ={}".format(N.angle_xy(),N.angle_yz()))

    @staticmethod
    def triangulate_points(P1, P2, pts1, pts2):

        homog_3d_point = cv2.triangulatePoints(P1, P2, pts1, pts2)
        return cv2.convertPointsFromHomogeneous(homog_3d_point.T)
    @staticmethod
    def calc_3d_points2(M1,M2,T,points_left, points_right):

        # 计算三维坐标
        points_3d = cv2.triangulatePoints(
            np.hstack((M1, np.zeros((3, 1)))),
            np.hstack((M2, T.reshape(3, 1))),
            points_left.T,
            points_right.T
        )
        points_3d /= points_3d[3]
        # 将结果转换为更易读的格式
        points_3d = points_3d[:3].T.reshape(-1, 3)
        return points_3d

    @staticmethod
    def calc_3d_points3(x1, y1, x1_prime, y1_prime, f, B):
        # 计算视差
        disparity = x1 - x1_prime

        # 计算深度（Z坐标）
        Z = f * B / disparity

        # 计算X和Y坐标（假设图像平面与真实世界坐标系平行）
        X = x1 * Z / f
        Y = y1 * Z / f

        return [X, Y, Z]
    @staticmethod
    def calc_3d_points4(x1, y1, x1_prime, y1_prime, f, B, cx, cy,
                        cx_prime,cy_prime,R,T
                        ):
        # 计算视差
        # print("cx, cy,",cx, cy,)
        # print("x1 x1_prime",x1, x1_prime,)
        # print("cx_prime,cy_prime",cx_prime,cy_prime)
        disparity = x1 - x1_prime

        # 如果视差为0，则返回无穷远点
        if disparity == 0:
            return [float('inf'), float('inf'), float('inf')]

        # 计算深度（Z坐标），这里我们取左相机为参考
        Z = (f * B) / disparity

        # # # 从像素坐标转换到左相机坐标系
        X_left = (x1 - cx) * B / disparity
        Y_left = (y1 - cy) * B / disparity

        # 构造左相机坐标系下的坐标向量
        point_camera_left = np.array([X_left, Y_left, Z, 1])

        # 由于左相机是我们的参考，我们假设它的外参是 [I|0]，即单位矩阵和零向量
        # 因此，左相机坐标系下的点就是世界坐标系下的点
        point_world = point_camera_left

        # # 如果右相机的外参(R|T)是相对于左相机（世界坐标系）的，
        # # 我们需要将右相机的图像坐标转换到右相机坐标系
        # X_right = (x1_prime - cx_prime) * Z / f
        # Y_right = (y1_prime - cy_prime) * Z / f
        # point_camera_right = np.array([X_right, Y_right, Z, 1])
        #
        # # 然后，使用外参矩阵将右相机坐标系下的点转换到世界坐标系
        # # 对于右相机，这个步骤通常不是必须的，因为我们已经有了左相机的世界坐标
        # # 但如果你需要它，这里是计算方式:
        # point_world = R.dot(point_camera_right[:3]) + T.reshape(3)

        # 只返回X, Y, Z坐标
        return [round(num, 4) for num in point_world[:3]]

        # return point_world[:3].tolist()

    @staticmethod
    def calc_3d_points(x1, y1, x1_prime, y1_prime, f, B, cx, cy,
                       cx_prime, cy_prime, R, T
                       ):
        K_left = np.array([[f, 0, cx], [0, f, cy], [0, 0, 1]])  # 左相机的内参矩阵
        K_right = np.array([[f, 0, cx_prime], [0, f, cy_prime], [0, 0, 1]])  # 右相机的内参矩阵

        # 构建相机矩阵
        P_left = np.dot(K_left, np.hstack((np.eye(3), np.zeros((3, 1)))))
        P_right = np.dot(K_right, np.hstack((R, T.reshape(3, 1))))

        # 三角测量法计算3D坐标
        point_2d_left = np.array([x1, y1, 1])
        point_2d_right = np.array([x1_prime, y1_prime, 1])

        # 使用OpenCV的triangulatePoints
        point_3d_hom = cv2.triangulatePoints(P_left, P_right, point_2d_left[:2].reshape(2, 1),
                                             point_2d_right[:2].reshape(2, 1))

        # 将齐次坐标转换为3D坐标
        point_3d = point_3d_hom[:3] / point_3d_hom[3]
        point_3d = [pt[0] for pt in point_3d.tolist()]  # 修改这里以"平铺"结果


        return point_3d

    @staticmethod
    def compute_transformation(src_points, dst_points):
        src_points = np.array(src_points).reshape(-1, 3)
        dst_points = np.array(dst_points).reshape(-1, 3)

        # 使用Procrustes分析来计算最优的旋转和平移
        src_mean = np.mean(src_points, axis=0)
        dst_mean = np.mean(dst_points, axis=0)

        src_centered = src_points - src_mean
        dst_centered = dst_points - dst_mean

        U, S, Vt = np.linalg.svd(np.dot(src_centered.T, dst_centered))
        R = np.dot(Vt.T, U.T)

        if np.linalg.det(R) < 0:
            Vt[-1, :] *= -1
            R = np.dot(Vt.T, U.T)

        T = dst_mean.T - np.dot(R, src_mean.T)

        return R, T
    @staticmethod
    def calculate_3d_coordinates(left_rectified, right_rectified, Q, x1, y1, x2, y2):
        # 计算视差
        disparity = x1 - x2

        # 构造齐次坐标表示的点
        point = np.array([x1, y1, disparity, 1], dtype=np.float32)

        # 计算齐次坐标表示的三维点
        point_3D_homogeneous = np.dot(Q, point)

        # 将齐次坐标表示的三维点转换为笛卡尔坐标
        point_3D = point_3D_homogeneous / point_3D_homogeneous[3]

        return point_3D[:3]
    @staticmethod
    def calc_normal_vector( points_3d):
        # 计算法线向量
        normal = np.cross(points_3d[1] - points_3d[0], points_3d[2] - points_3d[0])
        # 计算单位向量
        normal /= np.linalg.norm(normal)
        return normal

    @staticmethod
    def compute_transformation_matrix(src_points, dst_points):
        # 检查点的数量是否相同
        assert len(src_points) == len(dst_points)

        def optimization(R, T, src_points,dst_points):
            rot_vec = cv2.Rodrigues(R)[0].flatten()

            # 将旋转向量和平移矩阵T连接为一个参数向量
            params = np.hstack((rot_vec, T))
            # 使用最小二乘法优化参数
            res = least_squares(CameraCalculation.objective_function, params, args=(src_points, dst_points))
            # 从优化结果中提取旋转矩阵和平移矩阵
            optimized_rot_vec = res.x[:3]
            optimized_R = cv2.Rodrigues(optimized_rot_vec)[0]
            optimized_T = res.x[3:]
            return optimized_R, optimized_T

        # 计算重心
        src_mean = np.mean(src_points, axis=0)
        dst_mean = np.mean(dst_points, axis=0)

        # 去中心化
        src_demeaned = src_points - src_mean
        dst_demeaned = dst_points - dst_mean

        # 计算H矩阵
        H = np.dot(src_demeaned.T, dst_demeaned)

        # SVD分解
        U, S, Vt = np.linalg.svd(H)

        # 计算旋转矩阵R
        R = np.dot(Vt.T, U.T)

        # 计算平移矩阵T
        T = dst_mean - np.dot(src_mean, R)
        # R, T = optimization(R,T,src_points,dst_points)

        return R, T




    @staticmethod
    def transform_coordinates(R, T, coordinates):
        return np.dot(coordinates, R.T) + T

        # return (np.dot(R, coordinates.T)).T + T

    @staticmethod
    def objective_function(params, src_points, dst_points):
        n_points = len(src_points)
        R = cv2.Rodrigues(np.array(params[:3]))[0]  # 从旋转向量转换为旋转矩阵
        T = np.array(params[3:])

        transformed_points = np.dot(src_points, R.T) + T
        residuals = dst_points - transformed_points

        return residuals.flatten()

    @staticmethod
    def calculate_distance(point1, point2):
        """
        计算空间中两点之间的距离。

        参数:
        point1 -- 第一个点的坐标，一个包含 x, y, z 的元组。
        point2 -- 第二个点的坐标，一个包含 x, y, z 的元组。

        返回:
        两点之间的距离。
        """
        x1, y1, z1 = point1
        x2, y2, z2 = point2
        distance = math.sqrt((x2 - x1)**2 + (y2 - y1)**2 + (z2 - z1)**2)
        return distance

