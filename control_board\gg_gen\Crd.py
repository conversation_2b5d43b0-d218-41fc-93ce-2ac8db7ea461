import ctypes

from .Base import gts


class CrdPrm(ctypes.Structure):
    _fields_ = [
        ("dimension", ctypes.c_short), 
        ("profile", ctypes.c_short * 8), 
        ("synVelMax", ctypes.c_double),
        ("synAccMax", ctypes.c_double), 
        ("evenTime", ctypes.c_short), 
        ("setOriginFlag", ctypes.c_short),
        ("originPos", ctypes.c_long * 8),
    ]


class TCrdData(ctypes.Structure):
    _fields_ = [
        ("dimension", ctypes.c_short), 
        ("profile", ctypes.c_short * 8), 
        ("synVelMax", ctypes.c_double),
        ("synAccMax", ctypes.c_double), 
        ("evenTime", ctypes.c_short), 
        ("setOriginFlag", ctypes.c_short),
        ("originPos", ctypes.c_long * 8),
    ]


null_pointer = ctypes.POINTER(ctypes.c_void_p)()


if gts is not None:
    gts.GTN_SetCrdPrm.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.POINTER(CrdPrm)]
    gts.GTN_SetCrdPrm.restype = ctypes.c_short


def GTN_SetCrdPrm(core, profile, dimension, profile_list, synVelMax, synAccMax, evenTime, setOriginFlag,
                  originPos_list):
    profile_list = (ctypes.c_short * 8)(*profile_list)
    originPos_list = (ctypes.c_long * 8)(*originPos_list)
    prm = CrdPrm(dimension, profile_list, synVelMax, synAccMax, evenTime, setOriginFlag, originPos_list)
    return gts.GTN_SetCrdPrm(core, profile, ctypes.byref(prm))


if gts is not None:
    gts.GTN_GetCrdPrm.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.POINTER(CrdPrm)]
    gts.GTN_GetCrdPrm.restype = ctypes.c_short


def GTN_GetCrdPrm(core, profile):
    prm = CrdPrm()
    r = gts.GTN_GetCrdPrm(core, profile, ctypes.byref(prm))
    profile = [prm.profile[i] for i in range(8)]
    originPos = [prm.originPos[i] for i in range(8)]
    return r, prm.dimension, profile, prm.synVelMax, prm.synAccMax, prm.evenTime, prm.setOriginFlag, originPos


if gts is not None:
    gts.GTN_CrdClear.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_short]
    gts.GTN_CrdClear.restype = ctypes.c_short


def GTN_CrdClear(core, crd, fifo=0):
    return gts.GTN_CrdClear(core, crd, fifo)

if gts is not None:
    gts.GTN_LnXY.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_long, ctypes.c_long, ctypes.c_double, ctypes.c_double, ctypes.c_double, ctypes.c_short]
    gts.GTN_LnXY.restype = ctypes.c_short


def GTN_LnXY(core, crd, x, y, synVel, synAcc, velEnd=0, fifo=0):
    return gts.GTN_LnXY(core, crd, x, y, synVel, synAcc, velEnd, fifo)


if gts is not None:
    gts.GTN_LnXYZ.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_long, ctypes.c_long, ctypes.c_long, ctypes.c_double, ctypes.c_double, ctypes.c_double, ctypes.c_short]
    gts.GTN_LnXYZ.restype = ctypes.c_short


def GTN_LnXYZ(core, crd, x, y, z, synVel, synAcc, velEnd=0, fifo=0):
    return gts.GTN_LnXYZ(core, crd, x, y, z, synVel, synAcc, velEnd, fifo)


if gts is not None:
    gts.GTN_LnXYZA.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_long, ctypes.c_long, ctypes.c_long, ctypes.c_long, ctypes.c_double, ctypes.c_double, ctypes.c_double, ctypes.c_short]
    gts.GTN_LnXYZA.restype = ctypes.c_short


def GTN_LnXYZA(core, crd, x, y, z, a, synVel, synAcc, velEnd=0, fifo=0):
    return gts.GTN_LnXYZA(core, crd, x, y, z, a, synVel, synAcc, velEnd, fifo)


if gts is not None:
    gts.GTN_ArcXYC.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_long, ctypes.c_long, ctypes.c_double, ctypes.c_double, ctypes.c_short, ctypes.c_double, ctypes.c_double, ctypes.c_double, ctypes.c_short]
    gts.GTN_ArcXYC.restype = ctypes.c_short


def GTN_ArcXYC(core, crd, x, y, xCenter, yCenter, circleDir, synVel, synAcc, velEnd=0, fifo=0):
    return gts.GTN_ArcXYC(core, crd, x, y, xCenter, yCenter, circleDir, synVel, synAcc, velEnd, fifo)

if gts is not None:
    gts.GTN_ArcXYR.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_long, ctypes.c_long, ctypes.c_double, ctypes.c_short, ctypes.c_double, ctypes.c_double, ctypes.c_double, ctypes.c_short]
    gts.GTN_ArcXYR.restype = ctypes.c_short


def GTN_ArcXYR(core, crd, x, y, radius, circleDir, synVel, synAcc, velEnd=0, fifo=0):
    return gts.GTN_ArcXYR(core, crd, x, y, radius, circleDir, synVel, synAcc, velEnd, fifo)


if gts is not None:
    gts.GTN_ArcYZC.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_long, ctypes.c_long, ctypes.c_double, ctypes.c_double, ctypes.c_short, ctypes.c_double, ctypes.c_double, ctypes.c_double, ctypes.c_short]
    gts.GTN_ArcYZC.restype = ctypes.c_short


def GTN_ArcYZC(core, crd, y, z, yCenter, zCenter, circleDir, synVel, synAcc, velEnd=0, fifo=0):
    return gts.GTN_ArcYZC(core, crd, y, z, yCenter, zCenter, circleDir, synVel, synAcc, velEnd, fifo)


if gts is not None:
    gts.GTN_ArcYZR.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_long, ctypes.c_long, ctypes.c_double, ctypes.c_short, ctypes.c_double, ctypes.c_double, ctypes.c_double, ctypes.c_short]
    gts.GTN_ArcYZR.restype = ctypes.c_short


def GTN_ArcYZR(core, crd, y, z, radius, circleDir, synVel, synAcc, velEnd=0, fifo=0):
    return gts.GTN_ArcYZR(core, crd, y, z, radius, circleDir, synVel, synAcc, velEnd, fifo)


if gts is not None:
    gts.GTN_BufIO.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_ushort, ctypes.c_ushort, ctypes.c_ushort, ctypes.c_ushort, ctypes.c_short]
    gts.GTN_BufIO.restype = ctypes.c_short


def GTN_BufIO(core, crd, doType, doMask, doValue, fifo=0):
    return gts.GTN_BufIO(core, crd, doType, doMask, doValue, fifo)


if gts is not None:
    gts.GTN_BufDelay.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_ushort, ctypes.c_short]
    gts.GTN_BufDelay.restype = ctypes.c_short


def GTN_BufDelay(core, crd, delayTime, fifo=0):
    return gts.GTN_BufDelay(core, crd, delayTime, fifo)


if gts is not None:
    gts.GTN_CrdSpace.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.POINTER(ctypes.c_long), ctypes.c_short]
    gts.GTN_CrdSpace.restype = ctypes.c_short


def GTN_CrdSpace(core, crd, fifo=0):
    pSpace = ctypes.c_long(0)
    r = gts.GTN_CrdSpace(core, crd, ctypes.byref(pSpace), fifo)
    return r, pSpace.value

if gts is not None:
    gts.GTN_CrdStart.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_short]
    gts.GTN_CrdStart.restype = ctypes.c_short


def GTN_CrdStart(core, mask, option):
    return gts.GTN_CrdStart(core, mask, option)


if gts is not None:
    gts.GTN_CrdStatus.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.POINTER(ctypes.c_short), ctypes.POINTER(ctypes.c_long), ctypes.c_short]
    gts.GTN_CrdStatus.restype = ctypes.c_short


def GTN_CrdStatus(core, crd, fifo=0):
    pRun = ctypes.c_short(0)
    pSegment = ctypes.c_long(0)
    r = gts.GTN_CrdStatus(core, crd, ctypes.byref(pRun), ctypes.byref(pSegment),fifo)
    return r, pRun.value, pSegment.value

if gts is not None:
    gts.GTN_CrdData.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.POINTER(TCrdData), ctypes.c_short]
    gts.GTN_CrdData.restype = ctypes.c_short


def GTN_CrdData(core, crd, fifo=0):
    return gts.GTN_CrdData(core, crd, null_pointer ,fifo)
