# -*-coding:utf-8 -*-
"""
# Author     : jid
# Time       : 2025/4/10 11:18
# Description: 
"""
import operator

from common.LogUtils import logger
from utils.SignalsManager import signals_manager


class DtcDetectManager:

    def __init__(self):
        super().__init__()
        self.case_number = ""
        self.command = ""
        self.dtc_normal_msg_id = ""
        self.dtc_normal_msg_mark = ""
        self.dtc_normal_msg = ""

    def reset_params(self):
        self.case_number = ""
        self.command = ""
        self.dtc_normal_msg_id = ""
        self.dtc_normal_msg_mark = ""
        self.dtc_normal_msg = ""

    def handle_dtc_detect(self, case_number, command, data):
        logger.info(f"handle_dtc_detect case_number={case_number}, command={command}, data={data}")
        self.case_number = case_number
        self.command = command
        self.dtc_normal_msg_id = data.split(",")[0]
        self.dtc_normal_msg_mark = data.split(",")[1]
        self.dtc_normal_msg = data.split(",")[2]
        signals_manager.step_execute_finish.emit(self.case_number, self.command, "PASS", "PASS")

    def handle_dtc_error(self, msg):
        if not operator.eq("", self.dtc_normal_msg_id):
            if msg and msg.arbitration_id == int(self.dtc_normal_msg_id, 16):
                converted = " ".join(format(x, "02X") for x in msg.data)
                logger.info(f"handle_dtc_error recv_msg={msg}, converted={converted}")
                if converted.startswith(self.dtc_normal_msg_mark) and not operator.eq(converted, self.dtc_normal_msg):
                    signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG",
                                                             f"DTC异常消息：{converted}")


dtc_detect_manager: DtcDetectManager = DtcDetectManager()
