# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2023/12/28 10:56
@Desc   : 
"""
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QPixmap


class VisualDetectSignal(QObject):
    video_change = pyqtSignal(QPixmap)
    start_collect = pyqtSignal(bool)
    # 开启关闭算法
    switch_durability = pyqtSignal(bool)
    clear_ui_ng_count = pyqtSignal()
    # adb 继续体系
    adb_title = pyqtSignal()
    # 功能测试清空测试结果
    function_test_clear_result = pyqtSignal()
    save_function_test_result = pyqtSignal(int, dict)
    start_brightness_test = pyqtSignal()


visual_detect_signal: VisualDetectSignal = VisualDetectSignal()
