<EtherCATConfig>
  <Config>
    <Master AdapterIndex="0" IoUpdateFreq="4" StackDebugLevel="0">
      <Info>
        <Name>GOOGOL EtherCAT Master</Name>
        <Destination>FFFFFFFFFFFF</Destination>
        <Source>000000000000</Source>
        <EtherType>A488</EtherType>
      </Info>
    </Master>
    <Slave>
      <Info>
        <Name>SV660_1Axis_00915</Name>
        <VendorId>1048576</VendorId>
        <ProductCode>786701</ProductCode>
        <RevisionNo>65536</RevisionNo>
        <SerialNo>65536</SerialNo>
        <DevType>1</DevType>
        <NChannel>1</NChannel>
      </Info>
      <ProcessData>
        <Sm2>
          <Type>Outputs</Type>
          <DefaultSize>12</DefaultSize>
          <StartAddress>6144</StartAddress>
          <ControlByte>100</ControlByte>
          <Enable>true</Enable>
          <Pdo>5889</Pdo>
        </Sm2>
        <Sm3>
          <Type>Inputs</Type>
          <DefaultSize>28</DefaultSize>
          <StartAddress>7168</StartAddress>
          <ControlByte>32</ControlByte>
          <Enable>true</Enable>
          <Pdo>6913</Pdo>
        </Sm3>
        <RxPdo Fixed="true" Mandatory="false" Sm="2" Virtual="false">
          <Index>#x1701</Index>
          <Name LcId="1033">Outputs</Name>
          <Exclude>#x1600</Exclude>
          <Exclude>#x1702</Exclude>
          <Exclude>#x1703</Exclude>
          <Exclude>#x1704</Exclude>
          <Exclude>#x1705</Exclude>
          <Entry Fixed="false">
            <Index>#x6040</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Controlword</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x607a</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Target position</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60b8</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Touch probe function</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60fe</Index>
            <SubIndex>#x1</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Physical outputs</Name>
            <Comment></Comment>
            <DataType>UDINT</DataType>
            <DefaultValue>2</DefaultValue>
          </Entry>
        </RxPdo>
        <TxPdo Fixed="true" Mandatory="false" Sm="3" Virtual="false">
          <Index>#x1b01</Index>
          <Name LcId="1033">Inputs</Name>
          <Exclude>#x1a00</Exclude>
          <Exclude>#x1b02</Exclude>
          <Exclude>#x1b03</Exclude>
          <Exclude>#x1b04</Exclude>
          <Entry Fixed="false">
            <Index>#x603f</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Error code</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x6041</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Statusword</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x6064</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Position actual value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x6077</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Torque actual value</Name>
            <Comment></Comment>
            <DataType>INT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60f4</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Following error actual value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60b9</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Touch probe status</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60ba</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Touch probe pos1 pos value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60bc</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Touch probe pos2 pos value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60fd</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Digital inputs</Name>
            <Comment></Comment>
            <DataType>UDINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
        </TxPdo>
      </ProcessData>
      <Mailbox>
        <Send>
          <Start>4096</Start>
          <Length>256</Length>
        </Send>
        <Recv>
          <Start>5120</Start>
          <Length>256</Length>
        </Recv>
        <Protocol>CoE</Protocol>
        <CoE>
          <InitCmds>
            <InitCmd Fixed="false">
              <Transition>PS</Transition>
              <Comment>Modes of operation</Comment>
              <Timeout>0</Timeout>
              <Ccs>1</Ccs>
              <Index>24672</Index>
              <SubIndex>0</SubIndex>
              <Data>08</Data>
            </InitCmd>
          </InitCmds>
        </CoE>
      </Mailbox>
      <DC>
        <AssignActivate>768</AssignActivate>
        <CycleTime0>1000000</CycleTime0>
        <CycleTime1>1000000</CycleTime1>
        <ShiftTime0>0</ShiftTime0>
        <ShiftTime1>0</ShiftTime1>
      </DC>
    </Slave>
    <Slave>
      <Info>
        <Name>SV660_1Axis_00915</Name>
        <VendorId>1048576</VendorId>
        <ProductCode>786701</ProductCode>
        <RevisionNo>65536</RevisionNo>
        <SerialNo>65536</SerialNo>
        <DevType>1</DevType>
        <NChannel>1</NChannel>
      </Info>
      <ProcessData>
        <Sm2>
          <Type>Outputs</Type>
          <DefaultSize>12</DefaultSize>
          <StartAddress>6144</StartAddress>
          <ControlByte>100</ControlByte>
          <Enable>true</Enable>
          <Pdo>5889</Pdo>
        </Sm2>
        <Sm3>
          <Type>Inputs</Type>
          <DefaultSize>28</DefaultSize>
          <StartAddress>7168</StartAddress>
          <ControlByte>32</ControlByte>
          <Enable>true</Enable>
          <Pdo>6913</Pdo>
        </Sm3>
        <RxPdo Fixed="true" Mandatory="false" Sm="2" Virtual="false">
          <Index>#x1701</Index>
          <Name LcId="1033">Outputs</Name>
          <Exclude>#x1600</Exclude>
          <Exclude>#x1702</Exclude>
          <Exclude>#x1703</Exclude>
          <Exclude>#x1704</Exclude>
          <Exclude>#x1705</Exclude>
          <Entry Fixed="false">
            <Index>#x6040</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Controlword</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x607a</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Target position</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60b8</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Touch probe function</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60fe</Index>
            <SubIndex>#x1</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Physical outputs</Name>
            <Comment></Comment>
            <DataType>UDINT</DataType>
            <DefaultValue>2</DefaultValue>
          </Entry>
        </RxPdo>
        <TxPdo Fixed="true" Mandatory="false" Sm="3" Virtual="false">
          <Index>#x1b01</Index>
          <Name LcId="1033">Inputs</Name>
          <Exclude>#x1a00</Exclude>
          <Exclude>#x1b02</Exclude>
          <Exclude>#x1b03</Exclude>
          <Exclude>#x1b04</Exclude>
          <Entry Fixed="false">
            <Index>#x603f</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Error code</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x6041</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Statusword</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x6064</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Position actual value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x6077</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Torque actual value</Name>
            <Comment></Comment>
            <DataType>INT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60f4</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Following error actual value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60b9</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Touch probe status</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60ba</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Touch probe pos1 pos value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60bc</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Touch probe pos2 pos value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60fd</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Digital inputs</Name>
            <Comment></Comment>
            <DataType>UDINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
        </TxPdo>
      </ProcessData>
      <Mailbox>
        <Send>
          <Start>4096</Start>
          <Length>256</Length>
        </Send>
        <Recv>
          <Start>5120</Start>
          <Length>256</Length>
        </Recv>
        <Protocol>CoE</Protocol>
        <CoE>
          <InitCmds>
            <InitCmd Fixed="false">
              <Transition>PS</Transition>
              <Comment>Modes of operation</Comment>
              <Timeout>0</Timeout>
              <Ccs>1</Ccs>
              <Index>24672</Index>
              <SubIndex>0</SubIndex>
              <Data>08</Data>
            </InitCmd>
          </InitCmds>
        </CoE>
      </Mailbox>
      <DC>
        <AssignActivate>768</AssignActivate>
        <CycleTime0>1000000</CycleTime0>
        <CycleTime1>1000000</CycleTime1>
        <ShiftTime0>0</ShiftTime0>
        <ShiftTime1>0</ShiftTime1>
      </DC>
    </Slave>
    <Slave>
      <Info>
        <Name>SV660_1Axis_00915</Name>
        <VendorId>1048576</VendorId>
        <ProductCode>786701</ProductCode>
        <RevisionNo>65536</RevisionNo>
        <SerialNo>65536</SerialNo>
        <DevType>1</DevType>
        <NChannel>1</NChannel>
      </Info>
      <ProcessData>
        <Sm2>
          <Type>Outputs</Type>
          <DefaultSize>12</DefaultSize>
          <StartAddress>6144</StartAddress>
          <ControlByte>100</ControlByte>
          <Enable>true</Enable>
          <Pdo>5889</Pdo>
        </Sm2>
        <Sm3>
          <Type>Inputs</Type>
          <DefaultSize>28</DefaultSize>
          <StartAddress>7168</StartAddress>
          <ControlByte>32</ControlByte>
          <Enable>true</Enable>
          <Pdo>6913</Pdo>
        </Sm3>
        <RxPdo Fixed="true" Mandatory="false" Sm="2" Virtual="false">
          <Index>#x1701</Index>
          <Name LcId="1033">Outputs</Name>
          <Exclude>#x1600</Exclude>
          <Exclude>#x1702</Exclude>
          <Exclude>#x1703</Exclude>
          <Exclude>#x1704</Exclude>
          <Exclude>#x1705</Exclude>
          <Entry Fixed="false">
            <Index>#x6040</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Controlword</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x607a</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Target position</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60b8</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Touch probe function</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60fe</Index>
            <SubIndex>#x1</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Physical outputs</Name>
            <Comment></Comment>
            <DataType>UDINT</DataType>
            <DefaultValue>2</DefaultValue>
          </Entry>
        </RxPdo>
        <TxPdo Fixed="true" Mandatory="false" Sm="3" Virtual="false">
          <Index>#x1b01</Index>
          <Name LcId="1033">Inputs</Name>
          <Exclude>#x1a00</Exclude>
          <Exclude>#x1b02</Exclude>
          <Exclude>#x1b03</Exclude>
          <Exclude>#x1b04</Exclude>
          <Entry Fixed="false">
            <Index>#x603f</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Error code</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x6041</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Statusword</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x6064</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Position actual value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x6077</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Torque actual value</Name>
            <Comment></Comment>
            <DataType>INT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60f4</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Following error actual value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60b9</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Touch probe status</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60ba</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Touch probe pos1 pos value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60bc</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Touch probe pos2 pos value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60fd</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Digital inputs</Name>
            <Comment></Comment>
            <DataType>UDINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
        </TxPdo>
      </ProcessData>
      <Mailbox>
        <Send>
          <Start>4096</Start>
          <Length>256</Length>
        </Send>
        <Recv>
          <Start>5120</Start>
          <Length>256</Length>
        </Recv>
        <Protocol>CoE</Protocol>
        <CoE>
          <InitCmds>
            <InitCmd Fixed="false">
              <Transition>PS</Transition>
              <Comment>Modes of operation</Comment>
              <Timeout>0</Timeout>
              <Ccs>1</Ccs>
              <Index>24672</Index>
              <SubIndex>0</SubIndex>
              <Data>08</Data>
            </InitCmd>
          </InitCmds>
        </CoE>
      </Mailbox>
      <DC>
        <AssignActivate>768</AssignActivate>
        <CycleTime0>1000000</CycleTime0>
        <CycleTime1>1000000</CycleTime1>
        <ShiftTime0>0</ShiftTime0>
        <ShiftTime1>0</ShiftTime1>
      </DC>
    </Slave>
    <Slave>
      <Info>
        <Name>SV660_1Axis_00915</Name>
        <VendorId>1048576</VendorId>
        <ProductCode>786701</ProductCode>
        <RevisionNo>65536</RevisionNo>
        <SerialNo>65536</SerialNo>
        <DevType>1</DevType>
        <NChannel>1</NChannel>
      </Info>
      <ProcessData>
        <Sm2>
          <Type>Outputs</Type>
          <DefaultSize>12</DefaultSize>
          <StartAddress>6144</StartAddress>
          <ControlByte>100</ControlByte>
          <Enable>true</Enable>
          <Pdo>5889</Pdo>
        </Sm2>
        <Sm3>
          <Type>Inputs</Type>
          <DefaultSize>28</DefaultSize>
          <StartAddress>7168</StartAddress>
          <ControlByte>32</ControlByte>
          <Enable>true</Enable>
          <Pdo>6913</Pdo>
        </Sm3>
        <RxPdo Fixed="true" Mandatory="false" Sm="2" Virtual="false">
          <Index>#x1701</Index>
          <Name LcId="1033">Outputs</Name>
          <Exclude>#x1600</Exclude>
          <Exclude>#x1702</Exclude>
          <Exclude>#x1703</Exclude>
          <Exclude>#x1704</Exclude>
          <Exclude>#x1705</Exclude>
          <Entry Fixed="false">
            <Index>#x6040</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Controlword</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x607a</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Target position</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60b8</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Touch probe function</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60fe</Index>
            <SubIndex>#x1</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Physical outputs</Name>
            <Comment></Comment>
            <DataType>UDINT</DataType>
            <DefaultValue>2</DefaultValue>
          </Entry>
        </RxPdo>
        <TxPdo Fixed="true" Mandatory="false" Sm="3" Virtual="false">
          <Index>#x1b01</Index>
          <Name LcId="1033">Inputs</Name>
          <Exclude>#x1a00</Exclude>
          <Exclude>#x1b02</Exclude>
          <Exclude>#x1b03</Exclude>
          <Exclude>#x1b04</Exclude>
          <Entry Fixed="false">
            <Index>#x603f</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Error code</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x6041</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Statusword</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x6064</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Position actual value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x6077</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Torque actual value</Name>
            <Comment></Comment>
            <DataType>INT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60f4</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Following error actual value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60b9</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Touch probe status</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60ba</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Touch probe pos1 pos value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60bc</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Touch probe pos2 pos value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60fd</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Digital inputs</Name>
            <Comment></Comment>
            <DataType>UDINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
        </TxPdo>
      </ProcessData>
      <Mailbox>
        <Send>
          <Start>4096</Start>
          <Length>256</Length>
        </Send>
        <Recv>
          <Start>5120</Start>
          <Length>256</Length>
        </Recv>
        <Protocol>CoE</Protocol>
        <CoE>
          <InitCmds>
            <InitCmd Fixed="false">
              <Transition>PS</Transition>
              <Comment>Modes of operation</Comment>
              <Timeout>0</Timeout>
              <Ccs>1</Ccs>
              <Index>24672</Index>
              <SubIndex>0</SubIndex>
              <Data>08</Data>
            </InitCmd>
          </InitCmds>
        </CoE>
      </Mailbox>
      <DC>
        <AssignActivate>768</AssignActivate>
        <CycleTime0>1000000</CycleTime0>
        <CycleTime1>1000000</CycleTime1>
        <ShiftTime0>0</ShiftTime0>
        <ShiftTime1>0</ShiftTime1>
      </DC>
    </Slave>
    <Slave>
      <Info>
        <Name>SV660_1Axis_00915</Name>
        <VendorId>1048576</VendorId>
        <ProductCode>786701</ProductCode>
        <RevisionNo>65536</RevisionNo>
        <SerialNo>65536</SerialNo>
        <DevType>1</DevType>
        <NChannel>1</NChannel>
      </Info>
      <ProcessData>
        <Sm2>
          <Type>Outputs</Type>
          <DefaultSize>12</DefaultSize>
          <StartAddress>6144</StartAddress>
          <ControlByte>100</ControlByte>
          <Enable>true</Enable>
          <Pdo>5889</Pdo>
        </Sm2>
        <Sm3>
          <Type>Inputs</Type>
          <DefaultSize>28</DefaultSize>
          <StartAddress>7168</StartAddress>
          <ControlByte>32</ControlByte>
          <Enable>true</Enable>
          <Pdo>6913</Pdo>
        </Sm3>
        <RxPdo Fixed="true" Mandatory="false" Sm="2" Virtual="false">
          <Index>#x1701</Index>
          <Name LcId="1033">Outputs</Name>
          <Exclude>#x1600</Exclude>
          <Exclude>#x1702</Exclude>
          <Exclude>#x1703</Exclude>
          <Exclude>#x1704</Exclude>
          <Exclude>#x1705</Exclude>
          <Entry Fixed="false">
            <Index>#x6040</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Controlword</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x607a</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Target position</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60b8</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Touch probe function</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60fe</Index>
            <SubIndex>#x1</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Physical outputs</Name>
            <Comment></Comment>
            <DataType>UDINT</DataType>
            <DefaultValue>2</DefaultValue>
          </Entry>
        </RxPdo>
        <TxPdo Fixed="true" Mandatory="false" Sm="3" Virtual="false">
          <Index>#x1b01</Index>
          <Name LcId="1033">Inputs</Name>
          <Exclude>#x1a00</Exclude>
          <Exclude>#x1b02</Exclude>
          <Exclude>#x1b03</Exclude>
          <Exclude>#x1b04</Exclude>
          <Entry Fixed="false">
            <Index>#x603f</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Error code</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x6041</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Statusword</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x6064</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Position actual value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x6077</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Torque actual value</Name>
            <Comment></Comment>
            <DataType>INT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60f4</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Following error actual value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60b9</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>16</BitLen>
            <Name LcId="1033">Touch probe status</Name>
            <Comment></Comment>
            <DataType>UINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60ba</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Touch probe pos1 pos value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60bc</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Touch probe pos2 pos value</Name>
            <Comment></Comment>
            <DataType>DINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
          <Entry Fixed="false">
            <Index>#x60fd</Index>
            <SubIndex>#x0</SubIndex>
            <BitLen>32</BitLen>
            <Name LcId="1033">Digital inputs</Name>
            <Comment></Comment>
            <DataType>UDINT</DataType>
            <DefaultValue>0</DefaultValue>
          </Entry>
        </TxPdo>
      </ProcessData>
      <Mailbox>
        <Send>
          <Start>4096</Start>
          <Length>256</Length>
        </Send>
        <Recv>
          <Start>5120</Start>
          <Length>256</Length>
        </Recv>
        <Protocol>CoE</Protocol>
        <CoE>
          <InitCmds>
            <InitCmd Fixed="false">
              <Transition>PS</Transition>
              <Comment>Modes of operation</Comment>
              <Timeout>0</Timeout>
              <Ccs>1</Ccs>
              <Index>24672</Index>
              <SubIndex>0</SubIndex>
              <Data>08</Data>
            </InitCmd>
          </InitCmds>
        </CoE>
      </Mailbox>
      <DC>
        <AssignActivate>768</AssignActivate>
        <CycleTime0>1000000</CycleTime0>
        <CycleTime1>1000000</CycleTime1>
        <ShiftTime0>0</ShiftTime0>
        <ShiftTime1>0</ShiftTime1>
      </DC>
    </Slave>
  </Config>
</EtherCATConfig>