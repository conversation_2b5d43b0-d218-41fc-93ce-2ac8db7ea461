# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/5/6 10:52
@Desc   : 触摸管理模块
"""
import threading
import time
import traceback

from common.LogUtils import logger
from adb.AdbConnectDevice import adb_connect_device
from control_board.inspire_robots.uart_client import inspire_client
from utils.SignalsManager import signals_manager


class TouchManager:

    def __init__(self):
        super().__init__()
        self.error_coordinates = []
        self.up_event_coordinates = []
        self.inspire_touch = {}

    def append_inspire_touch_header(self, index, touch_header):
        self.inspire_touch.update({index: touch_header})

    def append_error_coordinates(self, coordinates):
        logger.info(f"append_error_coordinates coordinates={coordinates}")
        self.error_coordinates.append(coordinates)

    def append_up_event_coordinates(self, coordinates):
        logger.info(f"append_up_event_coordinates coordinates={coordinates}")
        self.up_event_coordinates.append(coordinates)

    def calculate_touch_still_result(self, case_number, command):
        result = True
        if len(self.error_coordinates) > 0:
            result = False
        logger.info(f"calculate_touch_still_result result={result}, error_coordinates={self.error_coordinates}")
        if result:
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", str(self.error_coordinates))
        self.clear_error_coordinates()

    def calculate_touch_resp_times(self, case_number, command):
        logger.info(f"calculate_touch_resp_times case_number={case_number}, command={command}")
        result = True
        if result:
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", str(self.error_coordinates))

    def calculate_touch_report_rate(self, case_number, command):
        logger.info(f"calculate_touch_report_rate case_number={case_number}, command={command}")
        result = True
        if result:
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", str(self.error_coordinates))

    def calculate_touch_resp_time(self, case_number, command):
        logger.info(f"calculate_touch_resp_time case_number={case_number}, command={command}")
        result = True
        if result:
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", str(self.error_coordinates))

    def calculate(self, case_number, command):
        result = True
        if len(self.up_event_coordinates) != 1:
            result = False
        logger.info(f"calculate_touch_result result={result}, up_event_coordinates={self.up_event_coordinates}")
        if result:
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        else:
            signals_manager.step_execute_finish.emit(case_number, command, "NG", str(self.up_event_coordinates))
        self.clear_up_event_coordinates()

    def clear_error_coordinates(self):
        logger.info("clear_error_coordinates")
        self.error_coordinates.clear()

    def clear_up_event_coordinates(self):
        logger.info("clear_up_event_coordinates")
        self.up_event_coordinates.clear()

    def handle_touch_still_test(self, case_number, command, test_time):
        logger.info(f"handle_touch_still_test case_number={case_number}, command={command}, interval={test_time}")
        threading.Timer(interval=test_time,
                        function=self.calculate_touch_still_result,
                        args=(case_number, command)).start()

    def handle_touch_resp_times_test(self, case_number, command, interval_time, calibrate_resp_times):
        # logger.info(f"handle_touch_resp_times_test case_number={case_number}, command={command}, "
        #             f"interval_time={interval_time}, calibrate_resp_times={calibrate_resp_times}")
        if not inspire_client.is_open():
            return signals_manager.step_execute_finish.emit(case_number, command, "NG", "触摸电机设备未连接")
        # 第1个触摸头按下
        adb_connect_device.test_touch_resp_times_start()
        point_id = 2
        for i in range(calibrate_resp_times):
            try:
                time.sleep(interval_time / 2)
                inspire_client.move_to(point_id, 1550)
                time.sleep(interval_time / 2)
                # 触摸头抬起
                inspire_client.move_to(point_id, 0)
            except Exception:
                logger.info(traceback.format_exc())
        adb_connect_device.test_touch_resp_times_end()
        # 触摸头抬起之后等待1秒计算识别到的触摸响应次数
        # threading.Timer(interval=1, function=self.calculate_touch_resp_times, args=(case_number, command)).start()

    def handle_touch_report_rate_test(self, case_number, command, calibrate_report_rate):
        logger.info(f"handle_touch_report_rate_test case_number={case_number}, command={command}, "
                    f"calibrate_report_rate={calibrate_report_rate}")
        threading.Timer(interval=1, function=self.calculate_touch_report_rate, args=(case_number, command)).start()

    def handle_touch_resp_time_test(self, case_number, command, interval_time, calibrate_resp_time):
        from case.VdsDetectManager import vds_detect_manager
        logger.info(f"handle_touch_resp_time_test case_number={case_number}, command={command}, "
                    f"interval_time={interval_time}, calibrate_resp_time={calibrate_resp_time}")
        if not inspire_client.is_open():
            return signals_manager.step_execute_finish.emit(case_number, command, "NG", "触摸电机设备未连接")

        # 第1个触摸头按下
        point_id = 2
        calibrate_resp_time = 1000 * calibrate_resp_time
        # vds_detect_manager.set_expect_TouchRespTimes(case_number, command, data)  # action 返回结果，用于判断
        T = int(time.time() * 1000)
        vds_detect_manager.set_expect_test_touch_resp_time(case_number, command, T, calibrate_resp_time)
        adb_connect_device.test_touch_resp_time()
        inspire_client.move_to(point_id, 1500)

        time.sleep(interval_time)
        # 第1个触摸头抬起
        inspire_client.move_to(point_id, 0)
        # # 触摸头抬起之后等待1秒计算识别到的触摸响应次数
        # threading.Timer(interval=1, function=self.calculate_touch_resp_time, args=(case_number, command)).start()


touch_manager: TouchManager = TouchManager()
