import logging
import threading

from PyQt5 import QtWidgets, QtGui, QtCore
from PyQt5.QtCore import QRect

from common.LogUtils import logger


class HWToast(QtWidgets.QWidget):
    background_color = QtGui.QColor("#778899")
    text_color = QtCore.Qt.white
    font = QtGui.QFont('Microsoft YaHei UI Light', 14)
    text = ''
    times = 3
    parent = None
    min_height = 10
    min_width = 10

    def __init__(self, parent=None):
        super(HWToast, self).__init__(parent)
        self.parent = parent
        self.setWindowFlags(QtCore.Qt.FramelessWindowHint)
        self.setAttribute(QtCore.Qt.WA_TranslucentBackground)

        self.width = 0
        self.height = 0

    def init_view(self):
        # 计算气泡长宽及移动气泡到指定位置
        self.height = self.get_font_size() * 4
        self.width = len(self.text) * self.height * 0.5
        if self.height < self.min_height:
            self.height = self.min_height
        if self.width < self.min_width:
            self.width = self.min_width
        self.resize(self.width, self.height)
        rect: QRect = self.parent.geometry()
        x = rect.width() / 2
        y = rect.height() * 0.8
        logger.info('toast point: (%d, %d)', x, y)
        self.move(x - self.width / 2, y - self.height / 2)

    def paintEvent(self, event):
        painter = QtGui.QPainter(self)
        painter.setRenderHints(QtGui.QPainter.Antialiasing | QtGui.QPainter.TextAntialiasing)
        rect_line_path = QtGui.QPainterPath()
        rectangle = QtCore.QRectF(0, 0, self.width, self.height)
        rect_line_path.addRoundedRect(rectangle, self.height / 2, self.height / 2, QtCore.Qt.AbsoluteSize)
        painter.fillPath(rect_line_path, QtGui.QColor(self.background_color))

        pen = QtGui.QPen(QtGui.QColor(self.text_color))
        painter.setPen(pen)
        painter.setFont(self.font)
        self.draw_text(painter)

    def get_font_size(self):
        return self.font.pointSizeF()

    def draw_text(self, painter):
        painter.drawText(QtCore.QRectF(0, 0, self.width, self.height), QtCore.Qt.AlignCenter, self.text)

    def make_text(self, text, times=2, background_color=None):
        if text:
            self.text = text
        if times:
            self.times = times
        if background_color:
            self.background_color = background_color
        self.init_view()
        self.show()

        toast_timer = threading.Timer(self.times, self.toast_timeout)
        toast_timer.start()

    def toast_timeout(self):
        self.close()
