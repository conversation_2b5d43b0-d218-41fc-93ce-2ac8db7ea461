<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>singleAngle</class>
 <widget class="QWidget" name="singleAngle">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>182</width>
    <height>51</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_2">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>2</number>
   </property>
   <property name="rightMargin">
    <number>2</number>
   </property>
   <property name="bottomMargin">
    <number>2</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>旋转</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QSpinBox" name="spinBox">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>45</height>
        </size>
       </property>
       <property name="minimum">
        <number>-354</number>
       </property>
       <property name="maximum">
        <number>354</number>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="label_2">
       <property name="text">
        <string>检测角度</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditDetect">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>45</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>100</width>
         <height>16777215</height>
        </size>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
