<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FormPower</class>
 <widget class="QWidget" name="FormPower">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1600</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1600</width>
    <height>900</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Tommens程控电源</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>上下电控制</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_5">
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_4">
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_3">
           <item>
            <widget class="QGroupBox" name="groupBox">
             <property name="title">
              <string>配置</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_3">
              <item>
               <layout class="QGridLayout" name="gridLayout" columnstretch="1,1,1" columnminimumwidth="0,0,0">
                <item row="1" column="0" colspan="2">
                 <widget class="QLabel" name="label_2">
                  <property name="text">
                   <string>上电时间(s)</string>
                  </property>
                 </widget>
                </item>
                <item row="4" column="0" colspan="3">
                 <widget class="QLabel" name="label_11">
                  <property name="text">
                   <string>上电时间范围(s)</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="2">
                 <widget class="QSpinBox" name="spinBoxVoltage">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="value">
                   <number>13</number>
                  </property>
                 </widget>
                </item>
                <item row="6" column="0" colspan="3">
                 <widget class="QLabel" name="label_12">
                  <property name="text">
                   <string>下电时间范围</string>
                  </property>
                 </widget>
                </item>
                <item row="2" column="2">
                 <widget class="QSpinBox" name="spinBoxPowerOff">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="maximum">
                   <number>9999</number>
                  </property>
                 </widget>
                </item>
                <item row="2" column="0" colspan="2">
                 <widget class="QLabel" name="label_3">
                  <property name="text">
                   <string>下电时间(s)</string>
                  </property>
                 </widget>
                </item>
                <item row="5" column="1">
                 <widget class="QLabel" name="label_9">
                  <property name="text">
                   <string> ~</string>
                  </property>
                 </widget>
                </item>
                <item row="1" column="2">
                 <widget class="QSpinBox" name="spinBoxPowerOn">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="maximum">
                   <number>9999</number>
                  </property>
                 </widget>
                </item>
                <item row="3" column="2">
                 <widget class="QCheckBox" name="checkBoxRandomTest">
                  <property name="text">
                   <string>是</string>
                  </property>
                 </widget>
                </item>
                <item row="3" column="0" colspan="2">
                 <widget class="QLabel" name="label_10">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>时间随机</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="0" colspan="2">
                 <widget class="QLabel" name="label_4">
                  <property name="text">
                   <string>电压(V)</string>
                  </property>
                 </widget>
                </item>
                <item row="7" column="2">
                 <widget class="QDoubleSpinBox" name="doubleSpinBoxMaxOff">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="decimals">
                   <number>3</number>
                  </property>
                 </widget>
                </item>
                <item row="7" column="1">
                 <widget class="QLabel" name="label_13">
                  <property name="text">
                   <string> ~</string>
                  </property>
                 </widget>
                </item>
                <item row="5" column="2">
                 <widget class="QDoubleSpinBox" name="doubleSpinBoxMaxOn">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="decimals">
                   <number>3</number>
                  </property>
                 </widget>
                </item>
                <item row="5" column="0">
                 <widget class="QDoubleSpinBox" name="doubleSpinBoxMinOn">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="decimals">
                   <number>3</number>
                  </property>
                 </widget>
                </item>
                <item row="7" column="0">
                 <widget class="QDoubleSpinBox" name="doubleSpinBoxMinOff">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="decimals">
                   <number>3</number>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_2">
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_14">
               <property name="leftMargin">
                <number>0</number>
               </property>
               <item>
                <widget class="QPushButton" name="pushButtonStrart">
                 <property name="minimumSize">
                  <size>
                   <width>200</width>
                   <height>100</height>
                  </size>
                 </property>
                 <property name="text">
                  <string>开始</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="pushButtonEnd">
                 <property name="minimumSize">
                  <size>
                   <width>200</width>
                   <height>100</height>
                  </size>
                 </property>
                 <property name="text">
                  <string>结束</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QLabel" name="label_process">
           <property name="text">
            <string>执行进度</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_2">
      <attribute name="title">
       <string>高低压控制</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_7">
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_6">
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_4">
           <item>
            <widget class="QGroupBox" name="groupBox_2">
             <property name="title">
              <string>配置</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_2">
              <item>
               <layout class="QGridLayout" name="gridLayout_2">
                <item row="1" column="1">
                 <widget class="QDoubleSpinBox" name="doubleSpinBoxV2">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="value">
                   <double>13.000000000000000</double>
                  </property>
                 </widget>
                </item>
                <item row="1" column="0">
                 <widget class="QLabel" name="label_5">
                  <property name="text">
                   <string>电压2(V)</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="0">
                 <widget class="QLabel" name="label">
                  <property name="text">
                   <string>电压1(V)</string>
                  </property>
                 </widget>
                </item>
                <item row="2" column="0">
                 <widget class="QLabel" name="label_6">
                  <property name="text">
                   <string>切换时间(s)</string>
                  </property>
                 </widget>
                </item>
                <item row="3" column="0">
                 <widget class="QLabel" name="label_7">
                  <property name="text">
                   <string>循环次数</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="1">
                 <widget class="QDoubleSpinBox" name="doubleSpinBoxV1">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="value">
                   <double>13.000000000000000</double>
                  </property>
                 </widget>
                </item>
                <item row="3" column="1">
                 <widget class="QSpinBox" name="spinBoxSwitchNum">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="maximum">
                   <number>99999</number>
                  </property>
                  <property name="value">
                   <number>10</number>
                  </property>
                 </widget>
                </item>
                <item row="2" column="1">
                 <widget class="QDoubleSpinBox" name="doubleSpinBoxSwitchTime">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>45</height>
                   </size>
                  </property>
                  <property name="decimals">
                   <number>3</number>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout">
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_13">
               <property name="leftMargin">
                <number>0</number>
               </property>
               <item>
                <widget class="QPushButton" name="pushButtonStrartSwitchVoltage">
                 <property name="minimumSize">
                  <size>
                   <width>200</width>
                   <height>100</height>
                  </size>
                 </property>
                 <property name="text">
                  <string>开始</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="pushButtonEndSwitchVoltage">
                 <property name="minimumSize">
                  <size>
                   <width>200</width>
                   <height>100</height>
                  </size>
                 </property>
                 <property name="text">
                  <string>结束</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QLabel" name="label_process_v_switch">
           <property name="text">
            <string>执行进度</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_3">
      <attribute name="title">
       <string>随机电压</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_11">
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_10" stretch="99,1">
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_7">
           <item>
            <widget class="QGroupBox" name="groupBox_3">
             <property name="title">
              <string>配置</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_9">
              <item>
               <layout class="QVBoxLayout" name="verticalLayout_8" stretch="1,1,99">
                <item>
                 <layout class="QHBoxLayout" name="horizontalLayout_6">
                  <item>
                   <spacer name="horizontalSpacer">
                    <property name="orientation">
                     <enum>Qt::Horizontal</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>40</width>
                      <height>20</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                  <item>
                   <widget class="QToolButton" name="toolButtonAdd">
                    <property name="text">
                     <string>+</string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QToolButton" name="toolButtonReduce">
                    <property name="text">
                     <string>-</string>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </item>
                <item>
                 <layout class="QHBoxLayout" name="horizontalLayout_8">
                  <item>
                   <widget class="QLabel" name="label_8">
                    <property name="text">
                     <string>间隔时间(s) </string>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="QDoubleSpinBox" name="doubleSpinBoxRandomTime">
                    <property name="minimumSize">
                     <size>
                      <width>0</width>
                      <height>45</height>
                     </size>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <spacer name="horizontalSpacer_3">
                    <property name="orientation">
                     <enum>Qt::Horizontal</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>40</width>
                      <height>20</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                 </layout>
                </item>
                <item>
                 <widget class="QWidget" name="widgetVoltage" native="true"/>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_5">
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_12">
               <property name="leftMargin">
                <number>0</number>
               </property>
               <item>
                <widget class="QPushButton" name="pushButtonStartRandomVoltage">
                 <property name="minimumSize">
                  <size>
                   <width>200</width>
                   <height>100</height>
                  </size>
                 </property>
                 <property name="text">
                  <string>开始</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="pushButtonEndRandomVoltage">
                 <property name="minimumSize">
                  <size>
                   <width>200</width>
                   <height>100</height>
                  </size>
                 </property>
                 <property name="text">
                  <string>结束</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </item>
          </layout>
         </item>
         <item>
          <widget class="QLabel" name="label_process_random_voltage">
           <property name="text">
            <string>执行进度</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
