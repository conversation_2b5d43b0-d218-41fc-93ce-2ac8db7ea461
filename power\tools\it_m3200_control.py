import random
import threading
import time

from PyQt5.QtCore import QObject, pyqtSignal

from common.LogUtils import logger
from . import power_manager
from .it_m3200_client import it_m3200_client
from .thread_runner import ThreadRunner


class ITM3200Control(QObject):
    remain_count_signal = pyqtSignal(int)
    remain_count2_signal = pyqtSignal(int)

    def __init__(self):
        super().__init__(parent=None)
        self.runner = None
        self.power_on_interval = 10

    @staticmethod
    def is_connect():
        return it_m3200_client.is_opened() and it_m3200_client.is_init()

    def set_power_on_interval(self, interval):
        logger.info("set_power_on_interval interval={}".format(interval))
        self.power_on_interval = interval

    def _start_on_off_c(self, volt, on_time, off_time):
        logger.info("_start_on_off_c volt={}, on_time={}, off_time={}".format(volt, on_time, off_time))
        if not it_m3200_client.output_init(volt):
            return
        while True:
            it_m3200_client.output_on()
            logger.info("_start_on_off_c output_on")
            time.sleep(self.power_on_interval)
            time.sleep(on_time - self.power_on_interval)
            it_m3200_client.output_off()
            logger.info("_start_on_off_c output_off")
            time.sleep(off_time)

    def start_on_off_c(self, volt, on_time, off_time):
        if self.runner:
            self.runner.stop()

        self.runner = ThreadRunner(self._start_on_off_c, volt, on_time, off_time)
        self.runner.start()

    def _start_on_off_c2(self, volt, on_time_s, on_time_e, off_time_s, off_time_e):
        if not it_m3200_client.output_init(volt):
            return
        random.seed()
        while True:
            it_m3200_client.output_on()

            on_time = random.uniform(on_time_s, on_time_e)
            time.sleep(on_time)

            it_m3200_client.output_off()

            off_time = random.uniform(off_time_s, off_time_e)
            time.sleep(off_time)

    def start_on_off_c2(self, volt, on_time_s, on_time_e, off_time_s, off_time_e):
        if self.runner:
            self.runner.stop()

        self.runner = ThreadRunner(self._start_on_off_c2, volt, on_time_s, on_time_e, off_time_s, off_time_e)
        self.runner.start()

    def _start_high_low_volt_c(self, volt1, volt2, interval, repeat):
        if not it_m3200_client.output_init():
            return
        it_m3200_client.output_on()
        if repeat == 0:
            self.remain_count_signal.emit(-1)
            while True:
                it_m3200_client.set_volt(volt1)

                time.sleep(interval)

                it_m3200_client.set_volt(volt2)

                time.sleep(interval)
        else:
            self.remain_count_signal.emit(repeat)
            for i in range(repeat):
                it_m3200_client.set_volt(volt1)

                time.sleep(interval)

                it_m3200_client.set_volt(volt2)

                time.sleep(interval)

                self.remain_count_signal.emit(repeat - i - 1)

    def start_high_low_volt_c(self, volt1, volt2, interval, repeat):
        if self.runner:
            self.runner.stop()

        self.runner = ThreadRunner(self._start_high_low_volt_c, volt1, volt2, interval, repeat)
        self.runner.start()

    @staticmethod
    def _start_random_volt_c(volt_s, volt_e, interval):
        if not it_m3200_client.output_init():
            return
        it_m3200_client.output_on()
        random.seed()
        while True:
            volt = random.uniform(volt_s, volt_e)
            it_m3200_client.set_volt(volt)

            time.sleep(interval)

    def start_random_volt_c(self, volt_s, volt_e, interval):
        if self.runner:
            self.runner.stop()

        self.runner = ThreadRunner(self._start_random_volt_c, volt_s, volt_e, interval)
        self.runner.start()

    @staticmethod
    def _start_random_volt_c2(volt_list):
        if not it_m3200_client.output_init():
            return
        it_m3200_client.output_on()
        random.seed()

        while True:
            i = random.choice(volt_list)
            volt = i.get("volt")
            interval = i.get("time")

            it_m3200_client.set_volt(volt)
            time.sleep(interval)

    def start_random_volt_c2(self, volt_list):
        if self.runner:
            self.runner.stop()

        self.runner = ThreadRunner(self._start_random_volt_c2, volt_list)
        self.runner.start()

    # <<<<<<< HEAD
    #     def _start_step_volt_c(self, s_volt, e_volt, step, stay_time, repeat):
    #         if not it_m3200_client.output_init(0, 5):
    #             print("*********")

    def _start_step_volt_c(self, s_volt, e_volt, step, stay_time, repeat, mode):
        if not it_m3200_client.output_init():
            return
        it_m3200_client.output_on()
        if repeat == 0:
            self.remain_count2_signal.emit(-1)
            while True:
                if mode == "模式1":
                    volt = s_volt
                    if e_volt >= s_volt:
                        while True:
                            it_m3200_client.set_volt(volt)

                            time.sleep(stay_time)

                            if volt >= e_volt:
                                break

                            volt += step
                    else:
                        while True:
                            it_m3200_client.set_volt(volt)

                            time.sleep(stay_time)

                            if volt <= e_volt:
                                break

                            volt -= step

                elif mode == "模式2":
                    volt = s_volt
                    if e_volt >= s_volt:
                        while True:
                            it_m3200_client.set_volt(volt)

                            time.sleep(stay_time)

                            if volt >= e_volt:
                                break

                            volt += step

                        volt -= step
                        while True:
                            it_m3200_client.set_volt(volt)

                            time.sleep(stay_time)

                            if volt <= s_volt:
                                break

                            volt -= step
                    else:
                        while True:
                            it_m3200_client.set_volt(volt)

                            time.sleep(stay_time)

                            if volt <= e_volt:
                                break

                            volt -= step

                        volt += step
                        while True:
                            it_m3200_client.set_volt(volt)

                            time.sleep(stay_time)

                            if volt >= e_volt:
                                break

                            volt += step

                else:
                    break
        else:
            self.remain_count2_signal.emit(repeat)
            for i in range(repeat):
                if mode == "模式1":
                    volt = s_volt
                    if e_volt >= s_volt:
                        while True:
                            it_m3200_client.set_volt(volt)

                            time.sleep(stay_time)

                            if volt >= e_volt:
                                break

                            volt += step
                    else:
                        while True:
                            it_m3200_client.set_volt(volt)

                            time.sleep(stay_time)

                            if volt <= e_volt:
                                break

                            volt -= step

                elif mode == "模式2":
                    volt = s_volt
                    if e_volt >= s_volt:
                        while True:
                            it_m3200_client.set_volt(volt)

                            time.sleep(stay_time)

                            if volt >= e_volt:
                                break

                            volt += step

                        volt -= step
                        while True:
                            it_m3200_client.set_volt(volt)

                            time.sleep(stay_time)

                            if volt <= s_volt:
                                break

                            volt -= step
                    else:
                        while True:
                            it_m3200_client.set_volt(volt)

                            time.sleep(stay_time)

                            if volt <= e_volt:
                                break

                            volt -= step

                        volt += step
                        while True:
                            it_m3200_client.set_volt(volt)

                            time.sleep(stay_time)

                            if volt >= e_volt:
                                break

                            volt += step

                self.remain_count2_signal.emit(repeat - i - 1)

    def start_step_volt_c(self, s_volt, e_volt, step, stay_time, repeat, mode):
        if self.runner:
            self.runner.stop()

        self.runner = ThreadRunner(self._start_step_volt_c, s_volt, e_volt, step, stay_time, repeat, mode)
        self.runner.start()

    def stop(self):
        if self.runner:
            self.runner.stop()

    @staticmethod
    def set_volt(volt):
        logger.info("set_volt volt={}".format(volt))
        status = True, ''
        try:
            it_m3200_client.set_volt(volt)
        except Exception as e:
            logger.error("set_volt exception: {}".format(str(e.args)))
            status = False, 'm3200_client设置失败'
        return status

    @staticmethod
    def set_step_volt(start_volt, end_volt, interval, step):
        status = True
        try:
            it_m3200_client.set_volt(start_volt)
            time.sleep(interval)

            if start_volt < end_volt:
                for i in range(int((end_volt - start_volt) / step)):
                    if start_volt + i * step >= end_volt:
                        it_m3200_client.set_volt(end_volt)
                    else:
                        it_m3200_client.set_volt(start_volt + i * step)
                    time.sleep(interval)
            else:
                for i in range(int((start_volt - end_volt) / step)):
                    if start_volt - i * step <= end_volt:
                        it_m3200_client.set_volt(end_volt)
                    else:
                        it_m3200_client.set_volt(start_volt - i * step)
                    time.sleep(interval)
        except Exception as e:
            logger.error(f"set_step_voltage exception: {str(e.args)}")
            status = False
        return status

    def read_period_work_current(self, read_interval, read_time, min_current, max_current):
        logger.info(f"read_period_work_current read_interval={read_interval}, read_time={read_time}, "
                    f"min_current={min_current}, max_current={max_current}")
        state = True
        error_work_current = 0
        try:
            for i in range(int(read_time / (read_interval + 0.75))):
                work_current = self.read_work_current()
                work_current = round(work_current, 3)
                if min_current < work_current < max_current:
                    # 读取的电流在标定范围内，等待读取间隔时间后再次读取工作电流
                    time.sleep(read_interval)
                else:
                    # 读取的电流不在标定范围内，返回结果False，如果关联耐久测试异常停止，则直接停止测试
                    state = False
                    error_work_current = work_current
                    break
        except Exception as e:
            logger.error(f"read_period_work_current exception: {str(e.args)}")
            state = False
        return state, error_work_current

    @staticmethod
    def set_remote():
        logger.info("set_remote")
        it_m3200_client.set_remote()

    @staticmethod
    def power_init(volt=14):
        return it_m3200_client.output_init(volt=volt)

    @staticmethod
    def open():
        return it_m3200_client.open()

    @staticmethod
    def close():
        return it_m3200_client.close()

    @staticmethod
    def power_on():
        it_m3200_client.output_on()

    @staticmethod
    def power_off():
        it_m3200_client.output_off()

    @staticmethod
    def start_power_on_time_detect(interval_time=30):
        threading.Timer(interval=interval_time, function=power_manager.set_power_on_ready, args=(True,)).start()

    def read_work_current(self, times=5, interval=0.1):
        """
        读取电流
        @return:
        """
        sum_work_current = 0
        for i in range(times):
            instant_current = self.read_instant_current()
            sum_work_current += instant_current
            time.sleep(interval)
        average_work_current = sum_work_current / times
        logger.info(f"read_work_current average_work_current={average_work_current}")
        return average_work_current

    @staticmethod
    def read_instant_current():
        """
        读取瞬时电流
        @return:
        """
        instant_current = it_m3200_client.get_current()
        if instant_current is None:
            instant_current = 0.0

        logger.info(f"read_instant_current instant_current={instant_current}")
        return instant_current


it_m3200_control: ITM3200Control = ITM3200Control()
