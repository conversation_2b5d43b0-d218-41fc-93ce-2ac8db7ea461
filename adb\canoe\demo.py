import can
import time
import threading

# 步骤2: 设置CAN总线
def setup_can_bus(channel='0', bitrate=500000):
    """设置CAN总线，连接到CANoe或其他CAN硬件设备"""
    try:
        bus = can.interface.Bus(bustype='vector', app_name='CANoe', channel=channel, bitrate=bitrate,fd=True, recv_own_messages=True)
        print("CAN总线已设置。")
        return bus
    except Exception as e:
        print(f"设置CAN总线时发生错误: {e}")
        return None

# 步骤3: 发送周期性报文
def send_periodic_message(bus, arbitration_id, data, cycle_period):
    """发送周期性报文"""
    message = can.Message(arbitration_id=arbitration_id, data=data,is_fd=True, is_extended_id=False)
    cyclic_task = bus.send_periodic(message, cycle_period / 1000)  # cycle_period is in ms, convert to seconds
    if isinstance(cyclic_task, can.ModifiableCyclicTaskABC):
        print(f"周期性报文 {hex(arbitration_id)} 已启动，周期为 {cycle_period}ms。")
        return cyclic_task
    else:
        print(f"Failed to start cyclic task for {hex(arbitration_id)}.")
        return None

# 新增功能: 停止所有周期性任务
def stop_all_periodic_tasks(tasks):
    """停止所有周期性发送任务"""
    for task in tasks:
        if task:
            task.stop()
            print(f"周期性任务 {task} 已停止。")

# 步骤4: 接收响应
def receive_responses(bus, duration):
    """接收来自设备的响应报文"""
    start_time = time.time()
    while time.time() - start_time < duration:
        message = bus.recv(timeout=0.001)  # 设置超时为1秒
        if message:
            print(f"收到响应报文: ID={hex(message.arbitration_id)}, 数据={message.data}")

# 主函数
def main():
    bus = setup_can_bus()
    if bus:
        # Define messages and their cycles
        messages = [
            (0x505, bytes.fromhex('05 00 00 FF FF FF FF FF'), 640),
            # (0x13C, bytes.fromhex('FF 0F 00 01 00 00 00 00'), 20),
            # (0x225, bytes.fromhex(
            #     '40 00 64 04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00'),
            #  20),
            # (0x63D, bytes.fromhex('02 10 03 00 00 00 00 00'), 0),
        ]
        # Start sending each message periodically
        tasks = [send_periodic_message(bus, *msg) for msg in messages if msg[2] > 0]

        # Start a thread to receive responses
        # receiver_thread = threading.Thread(target=receive_responses, args=(bus, 60))
        # receiver_thread.start()
        receive_responses(bus, 60)
        # print("sleep")
        # time.sleep(5)
        # print("end")




        try:
            # Wait for the receiver thread to finish
            # receiver_thread.join()
            pass
        finally:
            # Stop all tasks
            stop_all_periodic_tasks(tasks)  # 使用新增的函数停止所有周期性任务
            bus.shutdown()
            print("CAN总线已关闭和资源已清理。")

if __name__ == "__main__":
    main()
