import copy
import json
import os
import socket
import subprocess
import threading
import traceback

import time

import psutil

from common.LogUtils import logger


class RecordImgAlgorithm(object):
    def __init__(self):
        self.img_list = []
        self.img_list_index = 0
        self.ip = "127.0.0.1"
        self.port = 2001
        self.conn = None
        self.case_number, self.command = 0, ""
        self.test_result = None
        self.results = None
    def set_info(self, case_number,command):
        self.command = command
        self.case_number = case_number

    def connect(self):
        try:
            self.conn = socket.socket(socket.AF_INET, socket.SOCK_STREAM)  # 创建 TCP socket
            self.conn.connect((self.ip, self.port))  # 连接服务器
            logger.info(f"成功连接到 {self.ip}:{self.port}")
        except ConnectionRefusedError:
            logger.info(f"连接 {self.ip}:{self.port} 被拒绝，请检查服务器是否已启动。")
            return False  # 连接失败
        except Exception as e:
            logger.info(f"连接错误: {e}")
            return False # 连接失败
        return True # 连接成功
    def disconnect(self):
        if self.conn is not None:
            self.conn.close()
            self.conn = None
            print("已断开连接。")

    def execute_comand(self,cmd):
        working_directory = os.path.join(os.getcwd(), "algorithm_server")
        logger.info("execute_comand cmd={}".format(cmd))
        out = subprocess.Popen(cmd, shell=True, stderr=subprocess.STDOUT,
                               stdin=subprocess.PIPE,
                               bufsize=0,
                               stdout=subprocess.PIPE,
                               cwd=working_directory,
                               encoding='utf8'
                               )
        output, error = out.communicate()
        logger.info("execute_comand output={}".format(output))
        return output
    def check_server(self):
        # 检查server服务
        for proc in psutil.process_iter(['pid', 'name']):
            # 如果进程名匹配
            if proc.info['name'] == "algorithm_server.exe":
                break
        else:
            # 开启进程
            path = os.path.join(os.getcwd(), "algorithm_server", "algorithm_server.exe")
            threading.Thread(target=self.execute_comand, args=(path,)).start()
            logger.info("start orbbec_server...")
            time.sleep(.2)
    def recv(self):
        if self.conn is None:
            logger.info("未建立连接，无法接收数据。")
            return None
        result = []
        delay = 60*10

        data = b""  # 使用字节串存储接收的数据
        while delay > 0:
            try:
                chunk = self.conn.recv(1024)  # 接收数据块
                if not chunk:  # 连接断开
                    time.sleep(.1)
                    delay -= .1
                    logger.info(f"delay msg delay is {delay}")
                    continue
                data += chunk
                if b"\n" in data:  # 检查是否包含换行符
                    delay = 60*10
                    message = data.split(b"\n")[0] # 分割消息，取第一个
                    data = data[len(message)+1:] # 剩余数据
                    try:
                        msg = message.decode("utf-8") #尝试解码
                        logger.info(f"RecordImageAlgorithm recv:{msg}")
                        if msg and msg == "end":
                            break
                        else:
                            # logger.info(f"RecordImageAlgorithm recv:{msg}")
                            msg_json = json.loads(msg)
                            time_ = msg_json["time"]
                            algorithm = msg_json["algorithm"]
                            producttype = msg_json["producttype"]
                            paths = msg_json["paths"]
                            status = msg_json["status"]
                            tmp = [time_, algorithm, paths, status,producttype]
                            result.append(copy.deepcopy(tmp))

                    except UnicodeDecodeError:
                        logger.info("接收到无法解码的数据")
                        return None

            except ConnectionResetError:
                print(traceback.format_exc())
                logger.info("连接被重置。")
                # self.conn.close()
                # self.conn = None
                return result
            except Exception as e: # 捕获其他异常
                logger.info(f"接收数据出错：{e}")
                # self.conn.close()
                # self.conn = None
                return result

        logger.info(f"end test delay is {delay}s")
        return result  # 返回解码后的字符串
    def send(self, message):
        if self.conn is None:
            logger.info("未建立连接，无法发送数据。")
            return False
        try:

            self.conn.sendall(message.encode("utf-8")) # 发送数据，编码为utf-8
            logger.info(f"RecordImageAlgorithm send:{message}")
            return True
        except Exception as e:
            logger.info(f"发送数据出错：{e}")
            self.conn.close()
            self.conn = None
            return False

    def start(self):
        if self.conn:
            self.send("start\n") # 添加换行符
    def end(self):
        if self.conn:
            self.send("end\n") # 添加换行符

alg_recorder = RecordImgAlgorithm()