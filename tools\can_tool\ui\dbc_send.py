# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'dbc_send.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(1600, 900)
        Form.setMinimumSize(QtCore.QSize(1600, 900))
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout(Form)
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.groupBox = QtWidgets.QGroupBox(Form)
        self.groupBox.setMinimumSize(QtCore.QSize(0, 0))
        self.groupBox.setObjectName("groupBox")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.groupBox)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.choice_dbc_button = QtWidgets.QPushButton(self.groupBox)
        self.choice_dbc_button.setMinimumSize(QtCore.QSize(0, 45))
        self.choice_dbc_button.setObjectName("choice_dbc_button")
        self.horizontalLayout.addWidget(self.choice_dbc_button)
        self.dbc_file_path = QtWidgets.QLabel(self.groupBox)
        self.dbc_file_path.setObjectName("dbc_file_path")
        self.horizontalLayout.addWidget(self.dbc_file_path)
        self.load_dbc_button = QtWidgets.QPushButton(self.groupBox)
        self.load_dbc_button.setMinimumSize(QtCore.QSize(0, 45))
        self.load_dbc_button.setObjectName("load_dbc_button")
        self.horizontalLayout.addWidget(self.load_dbc_button)
        self.verticalLayout.addLayout(self.horizontalLayout)
        self.treeWidget = QtWidgets.QTreeWidget(self.groupBox)
        self.treeWidget.setObjectName("treeWidget")
        self.verticalLayout.addWidget(self.treeWidget)
        self.horizontalLayout_2.addWidget(self.groupBox)
        self.groupBox_2 = QtWidgets.QGroupBox(Form)
        self.groupBox_2.setObjectName("groupBox_2")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.groupBox_2)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout()
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.delete_message = QtWidgets.QPushButton(self.groupBox_2)
        self.delete_message.setMinimumSize(QtCore.QSize(0, 45))
        self.delete_message.setObjectName("delete_message")
        self.verticalLayout_2.addWidget(self.delete_message)
        self.clear_message = QtWidgets.QPushButton(self.groupBox_2)
        self.clear_message.setMinimumSize(QtCore.QSize(0, 45))
        self.clear_message.setObjectName("clear_message")
        self.verticalLayout_2.addWidget(self.clear_message)
        self.horizontalLayout_3.addLayout(self.verticalLayout_2)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem)
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setObjectName("gridLayout")
        self.send_type_box = QtWidgets.QComboBox(self.groupBox_2)
        self.send_type_box.setMinimumSize(QtCore.QSize(0, 45))
        self.send_type_box.setObjectName("send_type_box")
        self.send_type_box.addItem("")
        self.send_type_box.addItem("")
        self.gridLayout.addWidget(self.send_type_box, 1, 1, 1, 1)
        self.list_send_message = QtWidgets.QPushButton(self.groupBox_2)
        self.list_send_message.setMinimumSize(QtCore.QSize(0, 45))
        self.list_send_message.setObjectName("list_send_message")
        self.gridLayout.addWidget(self.list_send_message, 1, 2, 1, 1)
        self.stop_message = QtWidgets.QPushButton(self.groupBox_2)
        self.stop_message.setMinimumSize(QtCore.QSize(0, 45))
        self.stop_message.setObjectName("stop_message")
        self.gridLayout.addWidget(self.stop_message, 0, 2, 1, 1)
        self.send_message = QtWidgets.QPushButton(self.groupBox_2)
        self.send_message.setMinimumSize(QtCore.QSize(0, 45))
        self.send_message.setObjectName("send_message")
        self.gridLayout.addWidget(self.send_message, 0, 1, 1, 1)
        self.connect_type = QtWidgets.QComboBox(self.groupBox_2)
        self.connect_type.setMinimumSize(QtCore.QSize(0, 45))
        self.connect_type.setObjectName("connect_type")
        self.connect_type.addItem("")
        self.connect_type.addItem("")
        self.connect_type.addItem("")
        self.connect_type.addItem("")
        self.connect_type.addItem("")
        self.gridLayout.addWidget(self.connect_type, 0, 0, 1, 1)
        self.gridLayout.setColumnMinimumWidth(0, 3)
        self.gridLayout.setColumnMinimumWidth(1, 1)
        self.gridLayout.setColumnMinimumWidth(2, 1)
        self.horizontalLayout_3.addLayout(self.gridLayout)
        self.horizontalLayout_3.setStretch(0, 1)
        self.horizontalLayout_3.setStretch(1, 1)
        self.horizontalLayout_3.setStretch(2, 3)
        self.verticalLayout_3.addLayout(self.horizontalLayout_3)
        self.tableWidget_Message = QtWidgets.QTableWidget(self.groupBox_2)
        self.tableWidget_Message.setObjectName("tableWidget_Message")
        self.tableWidget_Message.setColumnCount(15)
        self.tableWidget_Message.setRowCount(0)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_Message.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_Message.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_Message.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_Message.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_Message.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_Message.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_Message.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_Message.setHorizontalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_Message.setHorizontalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_Message.setHorizontalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_Message.setHorizontalHeaderItem(10, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_Message.setHorizontalHeaderItem(11, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_Message.setHorizontalHeaderItem(12, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_Message.setHorizontalHeaderItem(13, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_Message.setHorizontalHeaderItem(14, item)
        self.verticalLayout_3.addWidget(self.tableWidget_Message)
        self.tableWidget_signal = QtWidgets.QTableWidget(self.groupBox_2)
        self.tableWidget_signal.setObjectName("tableWidget_signal")
        self.tableWidget_signal.setColumnCount(10)
        self.tableWidget_signal.setRowCount(0)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_signal.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_signal.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_signal.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_signal.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_signal.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_signal.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_signal.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_signal.setHorizontalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_signal.setHorizontalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_signal.setHorizontalHeaderItem(9, item)
        self.verticalLayout_3.addWidget(self.tableWidget_signal)
        self.verticalLayout_3.setStretch(0, 1)
        self.verticalLayout_3.setStretch(1, 4)
        self.verticalLayout_3.setStretch(2, 4)
        self.horizontalLayout_2.addWidget(self.groupBox_2)
        self.horizontalLayout_2.setStretch(0, 1)
        self.horizontalLayout_2.setStretch(1, 1)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.groupBox.setTitle(_translate("Form", "DBC视图"))
        self.choice_dbc_button.setText(_translate("Form", "选择文件"))
        self.dbc_file_path.setText(_translate("Form", "文件地址：等待导入中...."))
        self.load_dbc_button.setText(_translate("Form", "导入"))
        self.treeWidget.headerItem().setText(0, _translate("Form", "消息名"))
        self.treeWidget.headerItem().setText(1, _translate("Form", "ID"))
        self.treeWidget.headerItem().setText(2, _translate("Form", "DLC"))
        self.treeWidget.headerItem().setText(3, _translate("Form", "注释"))
        self.groupBox_2.setTitle(_translate("Form", "CAN视图"))
        self.delete_message.setText(_translate("Form", "删除"))
        self.clear_message.setText(_translate("Form", "清空"))
        self.send_type_box.setItemText(0, _translate("Form", "串型发送"))
        self.send_type_box.setItemText(1, _translate("Form", "并型发送"))
        self.list_send_message.setText(_translate("Form", "列表发送"))
        self.stop_message.setText(_translate("Form", "停止"))
        self.send_message.setText(_translate("Form", "发送"))
        self.connect_type.setItemText(0, _translate("Form", "请选择通讯方式"))
        self.connect_type.setItemText(1, _translate("Form", "PCAN"))
        self.connect_type.setItemText(2, _translate("Form", "周立功"))
        self.connect_type.setItemText(3, _translate("Form", "CANOE"))
        self.connect_type.setItemText(4, _translate("Form", "LIN"))
        item = self.tableWidget_Message.horizontalHeaderItem(0)
        item.setText(_translate("Form", "消息名"))
        item = self.tableWidget_Message.horizontalHeaderItem(1)
        item.setText(_translate("Form", "ID(Hex)"))
        item = self.tableWidget_Message.horizontalHeaderItem(2)
        item.setText(_translate("Form", "发送次数"))
        item = self.tableWidget_Message.horizontalHeaderItem(3)
        item.setText(_translate("Form", "间隔(ms)"))
        item = self.tableWidget_Message.horizontalHeaderItem(4)
        item.setText(_translate("Form", "状态"))
        item = self.tableWidget_Message.horizontalHeaderItem(5)
        item.setText(_translate("Form", "已发"))
        item = self.tableWidget_Message.horizontalHeaderItem(6)
        item.setText(_translate("Form", "DLC"))
        item = self.tableWidget_Message.horizontalHeaderItem(7)
        item.setText(_translate("Form", "B1"))
        item = self.tableWidget_Message.horizontalHeaderItem(8)
        item.setText(_translate("Form", "B2"))
        item = self.tableWidget_Message.horizontalHeaderItem(9)
        item.setText(_translate("Form", "B3"))
        item = self.tableWidget_Message.horizontalHeaderItem(10)
        item.setText(_translate("Form", "B4"))
        item = self.tableWidget_Message.horizontalHeaderItem(11)
        item.setText(_translate("Form", "B5"))
        item = self.tableWidget_Message.horizontalHeaderItem(12)
        item.setText(_translate("Form", "B6"))
        item = self.tableWidget_Message.horizontalHeaderItem(13)
        item.setText(_translate("Form", "B7"))
        item = self.tableWidget_Message.horizontalHeaderItem(14)
        item.setText(_translate("Form", "B8"))
        item = self.tableWidget_signal.horizontalHeaderItem(0)
        item.setText(_translate("Form", "信号名"))
        item = self.tableWidget_signal.horizontalHeaderItem(1)
        item.setText(_translate("Form", "原始值(Hex)"))
        item = self.tableWidget_signal.horizontalHeaderItem(2)
        item.setText(_translate("Form", "实际值"))
        item = self.tableWidget_signal.horizontalHeaderItem(3)
        item.setText(_translate("Form", "值描述"))
        item = self.tableWidget_signal.horizontalHeaderItem(4)
        item.setText(_translate("Form", "单位"))
        item = self.tableWidget_signal.horizontalHeaderItem(5)
        item.setText(_translate("Form", "变化比例"))
        item = self.tableWidget_signal.horizontalHeaderItem(6)
        item.setText(_translate("Form", "变化偏移"))
        item = self.tableWidget_signal.horizontalHeaderItem(7)
        item.setText(_translate("Form", "起始位"))
        item = self.tableWidget_signal.horizontalHeaderItem(8)
        item.setText(_translate("Form", "位宽"))
        item = self.tableWidget_signal.horizontalHeaderItem(9)
        item.setText(_translate("Form", "注释"))
