# import sys
# import os
# import traceback
from ctypes import *
import traceback
from adb.lin import PLinApi


class PLinApiView:

    def __init__(self):
        self.initialize()
        self.status = False

    def initialize(self):
        self.plink_api = PLinApi.PLinApi()
        if not self.plink_api.isLoaded():
            raise Exception("PLin-API could not be loaded ! Exiting...")
        self.m_h_client = PLinApi.HLINCLIENT(0)
        self.m_h_hw = PLinApi.HLINHW(0)
        self.m_HwMode = PLinApi.TLIN_HARDWAREMODE_NONE
        self.m_HwBaud_rate = c_ushort(0)
        self.FRAME_FILTER_MASK = c_uint64(0xFFFFFFFFFFFFFFFF)
        self.m_lMask = self.FRAME_FILTER_MASK
        self.PIDs = {}
        for i in range(64):
            nPID = c_ubyte(i)
            self.plink_api.GetPID(nPID)
            self.PIDs[nPID.value] = i

    def uninitialize(self):
        if self.m_h_client.value != PLinApi.HLINCLIENT(0).value:
            status = self.do_lin_disconnect()
            self.m_h_hw = PLinApi.HLINHW(0)
            self.plink_api.RemoveClient(self.m_h_client)
            self.m_h_client = PLinApi.HLINCLIENT(0)
            return status

    def display_available_connection(self):
        """
        显示可用的Lin硬件列表
        :return: None
        """
        hWInfoList = self.get_available_hardware()
        if len(hWInfoList) == 0:
            return "No hardware found!"
        else:
            for hWInfo in hWInfoList:
                if self.m_h_hw.value == hWInfo[3]:
                    if self.m_HwMode.value == PLinApi.TLIN_HARDWAREMODE_MASTER.value:
                        hwType = "master"
                    else:
                        hwType = "slave"
                    isConnected = str.format(
                        "(connected as {0}, {1})", hwType, self.m_HwBaud_rate.value)
                else:
                    isConnected = ""
                return str.format('{0} - dev. {1}, chan. {2} {4}', hWInfo[
                    0], hWInfo[1], hWInfo[2], hWInfo[3], isConnected)

    def get_available_hardware(self):
        """
        返回可用硬件的2D列表。
        每一行都按顺序描述LIN硬件及其句柄、类型、设备和通道号
        返回:
        一个LIN硬件信息的列表，混杂的列表是这样组成的[handle, type, device_number, channel_number]
        """
        res = []
        lwCount = c_ushort(0)
        availableHWs = (PLinApi.HLINHW * 0)()
        self.plink_api.GetAvailableHardware(availableHWs, 0, lwCount)
        if lwCount == 0:
            lwCount = c_ushort(16)
        availableHWs = (PLinApi.HLINHW * lwCount.value)()
        lwBuffSize = c_ushort(lwCount.value * 2)
        linResult = self.plink_api.GetAvailableHardware(
            availableHWs, lwBuffSize, lwCount)
        if linResult == PLinApi.TLIN_ERROR_OK:
            lnHwType = c_int(0)
            lnDevNo = c_int(0)
            lnChannel = c_int(0)
            lnMode = c_int(0)
            for i in range(lwCount.value):
                lwHw = availableHWs[i]
                self.plink_api.GetHardwareParam(
                    lwHw, PLinApi.TLIN_HARDWAREPARAM_TYPE, lnHwType, 0)
                self.plink_api.GetHardwareParam(
                    lwHw, PLinApi.TLIN_HARDWAREPARAM_DEVICE_NUMBER, lnDevNo, 0)
                self.plink_api.GetHardwareParam(
                    lwHw, PLinApi.TLIN_HARDWAREPARAM_CHANNEL_NUMBER, lnChannel, 0)
                self.plink_api.GetHardwareParam(
                    lwHw, PLinApi.TLIN_HARDWAREPARAM_MODE, lnMode, 0)
                if lnHwType.value == PLinApi.LIN_HW_TYPE_USB_PRO.value:
                    strName = "PCAN-USB Pro"
                elif lnHwType.value == PLinApi.LIN_HW_TYPE_USB_PRO_FD.value:
                    strName = "PCAN-USB Pro FD"
                elif lnHwType.value == PLinApi.LIN_HW_TYPE_PLIN_USB.value:
                    strName = "PLIN-USB"
                else:
                    strName = "Unknown"
                res.append([strName, lnDevNo.value, lnChannel.value, lwHw])
        return res

    def connect(self, choice_model, choice_bit):
        try:
            lHw = PLinApi.HLINHW(1)
            if choice_model == "Master":
                lHwMode = PLinApi.TLIN_HARDWAREMODE_MASTER
            elif choice_model == "Slave":
                lHwMode = PLinApi.TLIN_HARDWAREMODE_SLAVE
            else:
                return "Master or Slave can not be empty"
            if choice_bit == "":
                lHwBaudrate = c_ushort(19200)
            else:
                lHwBaudrate = c_ushort(int(choice_bit))
            if self.do_lin_connect(lHw, lHwMode, lHwBaudrate):
                self.status = True
                return self.display_notification("Connection successful")
            else:
                return self.display_notification("Connection failed")
        except Exception as e:
            return self.display_notification(str(e))

    def disconnect(self):
        self.uninitialize()
        if self.do_lin_disconnect():
            return self.display_notification("Disconnection successful")
        else:
            return self.display_notification("Disconnection failed")

    def do_lin_connect(self, hw_handle, hw_mode, hw_baud_rate):
        result = False
        if self.m_h_hw.value != 0:
            if not self.do_lin_disconnect():
                return result
        if self.m_h_client.value == 0:
            self.plink_api.RegisterClient(
                "PLINK-API Console", None, self.m_h_client)
        linResult = self.plink_api.ConnectClient(self.m_h_client, hw_handle)
        if linResult == PLinApi.TLIN_ERROR_OK:
            self.m_h_hw = hw_handle
            lnMode = c_int(0)
            lnCurrBaud = c_int(0)
            linResult = self.plink_api.GetHardwareParam(
                hw_handle, PLinApi.TLIN_HARDWAREPARAM_BAUDRATE, lnCurrBaud, 0)
            if lnMode.value == PLinApi.TLIN_HARDWAREMODE_NONE.value or lnCurrBaud.value != hw_baud_rate.value:
                linResult = self.plink_api.InitializeHardware(
                    self.m_h_client, self.m_h_hw, hw_mode, hw_baud_rate)
            if linResult == PLinApi.TLIN_ERROR_OK:
                self.m_HwMode = hw_mode
                self.m_HwBaud_rate = hw_baud_rate
                linResult = self.plink_api.SetClientFilter(self.m_h_client, self.m_h_hw, self.m_lMask)
                self.readFrameTableFromHw()
                linResult = PLinApi.TLIN_ERROR_OK
                result = True
            else:
                self.m_h_hw = PLinApi.HLINHW(0)
                result = False
        else:
            self.m_h_hw = PLinApi.HLINHW(0)
            result = False
        if linResult != PLinApi.TLIN_ERROR_OK:
            self.display_error(linResult)
        return result

    def display_error(self, lin_error):
        pTextBuff = create_string_buffer(255)
        linResult = self.plink_api.GetErrorText(
            lin_error, 0x09, pTextBuff, 255)
        if linResult == PLinApi.TLIN_ERROR_OK and len(pTextBuff.value) != 0:
            return self.display_notification(str.format("** Error ** - {0} ", bytes.decode(pTextBuff.value)))
        else:
            return self.display_notification(str.format("** Error ** - code={0}", lin_error))

    def do_lin_disconnect(self):
        if self.m_h_hw.value != 0:
            lfOtherClient = False
            lfOwnClient = False
            lhClientsSize = c_ushort(255)
            lhClients = (PLinApi.HLINCLIENT * lhClientsSize.value)()
            linResult = self.plink_api.GetHardwareParam(
                self.m_h_hw, PLinApi.TLIN_HARDWAREPARAM_CONNECTED_CLIENTS, lhClients, lhClientsSize)
            if linResult == PLinApi.TLIN_ERROR_OK:
                for i in range(1, lhClientsSize.value):
                    if lhClients[i] == 0:
                        continue
                    lfOtherClient = lfOtherClient | (
                        lhClients[i] != self.m_h_client.value)
                    lfOwnClient = lfOwnClient | (
                        lhClients[i] == self.m_h_client.value)

            if lfOtherClient is False:
                self.plink_api.ResetHardwareConfig(
                    self.m_h_client, self.m_h_hw)
            if lfOwnClient is True:
                linResult = self.plink_api.DisconnectClient(
                    self.m_h_client, self.m_h_hw)
                if linResult == PLinApi.TLIN_ERROR_OK:
                    self.m_h_hw = PLinApi.HLINHW(0)
                    return True
                else:
                    self.display_error(linResult)
                    return False
            else:
                return True
        else:
            return True

    @staticmethod
    def display_notification(text="** Invalid choice **"):
        return text

    def write_message(self, choice, data, direction_value, checksum_value, length):
        """
        Displays menu for writing messages

        Returns:
            None
        """
        checksum = ["Classic", "Enhanced", "Automatic"]
        direction = ["Disabled", "Publisher", "Subscriber", "Subscriber Automatic Length"]
        try:
            frameId = int(choice, 16)
            lFrameEntry = PLinApi.TLINFrameEntry()
            lFrameEntry.FrameId = c_ubyte(frameId)
            self.plink_api.GetFrameEntry(self.m_h_hw, lFrameEntry)
            # initialize LIN message to sent
            pMsg = PLinApi.TLINMsg()
            num = direction.index(direction_value) + 1
            # print(lFrameEntry.ChecksumType, direction.index(direction_value) + 1, direction.index(direction_value))
            pMsg.Direction = PLinApi.TLINDirection(
                direction.index(direction_value))
            pMsg.ChecksumType = PLinApi.TLINChecksumType(num)
            # pMsg.Direction = PLinApi.TLINDirection(
            #     lFrameEntry.Direction)
            # pMsg.ChecksumType = PLinApi.TLINChecksumType(
            #     lFrameEntry.ChecksumType)
            pMsg.Length = c_ubyte(int(length))
            # pMsg.Length = lFrameEntry.Length
            if pMsg.Direction == PLinApi.TLIN_DIRECTION_PUBLISHER.value:
                # for i in range(lFrameEntry.Length):
                if not isinstance(data, list):
                    data_list = data.split(' ')
                else:
                    data_list = data
                if len(data_list) < lFrameEntry.Length:
                    return "数据长度不能小于8"
                for i in range(int(length)):
                    pMsg.Data[i] = c_ubyte(int(data_list[i], 16))
            # Check if the hardware is initialized as master
            if self.m_HwMode.value == PLinApi.TLIN_HARDWAREMODE_MASTER.value:
                # set frame id to Protected ID
                nPID = c_ubyte(frameId)
                self.plink_api.GetPID(nPID)
                pMsg.FrameId = c_ubyte(nPID.value)
                # set checksum
                self.plink_api.CalculateChecksum(pMsg)
                # write LIN message
                linResult = self.plink_api.Write(
                    self.m_h_client, self.m_h_hw, pMsg)
            else:
                linResult = self.plink_api.UpdateByteArray(
                    self.m_h_client, self.m_h_hw, c_ubyte(frameId), c_ubyte(0), c_ubyte(pMsg.Length), pMsg.Data)
            if linResult == PLinApi.TLIN_ERROR_OK:
                # self.display_notification("Message successfully written")
                print("Message successfully written")
                return True
            else:
                self.display_error(linResult)
                # self.display_notification("Failed to write message")
                print("Failed to write message")
                return False
        except Exception as e:
            print(traceback.format_exc())
            return str(e)

    def read_message(self):
        """
        Displays menu for reading messages
        """

        if not self.plink_api.isLoaded():
            raise Exception("PLin-API could not be loaded ! Exiting...")
        try:
            pRcvMsg = PLinApi.TLINRcvMsg()
            # print(self.m_h_client, pRcvMsg)
            linResult = self.plink_api.Read(self.m_h_client, pRcvMsg)
            if linResult == PLinApi.TLIN_ERROR_OK:
                # listMsg.append(self.getFormattedRcvMsg(pRcvMsg))
                return self.getFormattedRcvMsg(pRcvMsg)
            elif linResult == PLinApi.TLIN_ERROR_RCVQUEUE_EMPTY:
                self.display_error(linResult)
            else:
                self.display_error(linResult)
        except Exception as e:
            return self.display_notification(str(e))

    def getFormattedRcvMsg(self, msg):
        """
        Returns a string formatted LIN receive message

        Parameters:
            msg a Lin receive message (TLINRcvMsg)

        Returns:
            a string formatted LIN message
        """
        # Check if the received frame is a standard type.
        # If it is not a standard type then ignore it.
        if msg.Type != PLinApi.TLIN_MSGTYPE_STANDARD.value:
            if msg.Type == PLinApi.TLIN_MSGTYPE_BUS_SLEEP.value:
                strTemp = 'Bus Sleep status message'
            elif msg.Type == PLinApi.TLIN_MSGTYPE_BUS_WAKEUP.value:
                strTemp = 'Bus WakeUp status message'
            elif msg.Type == PLinApi.TLIN_MSGTYPE_AUTOBAUDRATE_TIMEOUT.value:
                strTemp = 'Auto-baudrate Timeout status message'
            elif msg.Type == PLinApi.TLIN_MSGTYPE_AUTOBAUDRATE_REPLY.value:
                strTemp = 'Auto-baudrate Reply status message'
            elif msg.Type == PLinApi.TLIN_MSGTYPE_OVERRUN.value:
                strTemp = 'Bus Overrun status message'
            elif msg.Type == PLinApi.TLIN_MSGTYPE_QUEUE_OVERRUN.value:
                strTemp = 'Queue Overrun status message'
            else:
                strTemp = 'Non standard message'
            return strTemp
        # format Data field as string
        dataStr = ""
        for i in range(msg.Length):
            dataStr = str.format("{0}{1} ", dataStr,
                                 hex(msg.Data[i])[2:].upper() if hex(msg.Data[i])[2:].upper() != "0" else "00")
        # remove ending space
        dataStr = dataStr[:-1]
        # format Error field as string
        error = ""
        if msg.ErrorFlags & PLinApi.TLIN_MSGERROR_CHECKSUM:
            error = error + 'Checksum,'
        if msg.ErrorFlags & PLinApi.TLIN_MSGERROR_GROUND_SHORT:
            error = error + 'GroundShort,'
        if msg.ErrorFlags & PLinApi.TLIN_MSGERROR_ID_PARITY_BIT_0:
            error = error + 'IdParityBit0,'
        if msg.ErrorFlags & PLinApi.TLIN_MSGERROR_ID_PARITY_BIT_1:
            error = error + 'IdParityBit1,'
        if msg.ErrorFlags & PLinApi.TLIN_MSGERROR_INCONSISTENT_SYNCH:
            error = error + 'InconsistentSynch,'
        if msg.ErrorFlags & PLinApi.TLIN_MSGERROR_OTHER_RESPONSE:
            error = error + 'OtherResponse,'
        if msg.ErrorFlags & PLinApi.TLIN_MSGERROR_SLAVE_NOT_RESPONDING:
            error = error + 'SlaveNotResponding,'
        if msg.ErrorFlags & PLinApi.TLIN_MSGERROR_SLOT_DELAY:
            error = error + 'SlotDelay,'
        if msg.ErrorFlags & PLinApi.TLIN_MSGERROR_TIMEOUT:
            error = error + 'Timeout,'
        if msg.ErrorFlags & PLinApi.TLIN_MSGERROR_VBAT_SHORT:
            error = error + 'VBatShort,'
        if msg.ErrorFlags == 0:
            error = 'O.k. '
        # remove ending comma
        error = error[:-1]
        # # format message
        # print(hex(self.PIDs[msg.FrameId]), msg.Length, dataStr,
        #       self.getFrameDirectionAsString(msg.Direction), self.getFrameDirectionAsString(msg.ChecksumType),
        #       msg.Checksum, self.getFrameDirectionAsString(msg.Type),
        #       self.getFrameDirectionAsString(msg.hHw), error)
        # print(self.PIDs)
        return (
            hex(self.PIDs[msg.FrameId])[2:] + 'h',
            msg.Length,
            dataStr,
            msg.TimeStamp,
            self.getFrameDirectionAsString(msg.Direction),
            self.getFrameDirectionAsString(msg.ChecksumType),
            hex(msg.Checksum)[2:].upper() + 'h',
            error
            )

    def getFrameDirectionAsString(self, direction):
        """
        Returns the string name of a PLinApi.TLINDirection value

        Parameters:
            value   a PLinApi.TLINDirection value (or a number)

        Returns:
            a string name of the direction value
        """
        # check given parameter
        if isinstance(direction, PLinApi.TLINDirection):
            value = direction.value
        else:
            value = int(direction)
        # translate value to string
        if value == PLinApi.TLIN_DIRECTION_DISABLED.value:
            return 'Disabled'
        elif value == PLinApi.TLIN_DIRECTION_PUBLISHER.value:
            return 'Publisher'
        elif value == PLinApi.TLIN_DIRECTION_SUBSCRIBER.value:
            return 'Subscriber'
        elif value == PLinApi.TLIN_DIRECTION_SUBSCRIBER_AUTOLENGTH.value:
            return 'Subscriber Automatic Length'

    def readFrameTableFromHw(self):
        """
        Reads all values from the frame table of the hardware.

        Returns:
            A global frame List of frame entry retrieved from the hardware
        """
        # Initialize result
        result = []
        # Initialize the member attribute for the
        # client mask with 0.
        self.m_lMask = c_uint64(0x0)
        llMask = c_uint64(0x0)
        for i in range(64):
            lFrameEntry = PLinApi.TLINFrameEntry()
            lFrameEntry.FrameId = c_ubyte(i)
            lFrameEntry.ChecksumType = PLinApi.TLIN_CHECKSUMTYPE_AUTO
            lFrameEntry.Direction = PLinApi.TLIN_DIRECTION_SUBSCRIBER_AUTOLENGTH
            # length values is set to LIN 1.2.
            if (i >= 0x00) and (i <= 0x1F):
                lFrameEntry.Length = c_ubyte(2)
            elif (i >= 0x20) and (i <= 0x2F):
                lFrameEntry.Length = c_ubyte(4)
            elif (i >= 0x30) and (i <= 0x3F):
                lFrameEntry.Length = c_ubyte(8)
            linResult = self.plink_api.GetFrameEntry(
                self.m_h_hw, lFrameEntry)
            if linResult == PLinApi.TLIN_ERROR_OK:
                result.append(lFrameEntry)
                if lFrameEntry.Direction != PLinApi.TLIN_DIRECTION_DISABLED.value:
                    llMask = c_uint64((1 << i) & self.FRAME_FILTER_MASK.value)
                    self.m_lMask = c_uint64(self.m_lMask.value | llMask.value)
            # If the Client and Hardware handles are valid.
            if (self.m_h_client.value != 0) and (self.m_h_hw.value != 0):
                # Set the client filter.
                self.plink_api.SetClientFilter(
                    self.m_h_client, self.m_h_hw, self.m_lMask)
        return result


if __name__ == '__main__':
    plink = PLinApiView()
    print(plink.display_available_connection())
    # while True:
    a = plink.connect('Slave', '19200')
    print(a)
    # data = {"data": "00,00,00,00,", "pid": "E2h", "checksum": "Enhanced", "direction": "Publisher", "length": "4"}
    while True:
        msg = plink.write_message("22" ,"00 00 00 00 00 00 00 00" ,"Publisher", "Enhanced", 8)
        # recv_msg = plink.read_message()
        # print("recv_msg",recv_msg)
        print(msg)

