<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PushImgForm</class>
 <widget class="QWidget" name="PushImgForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1600</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1600</width>
    <height>900</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_5">
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_4">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QToolButton" name="toolButtonOpen">
         <property name="text">
          <string>...</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="lineEdit">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>45</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButtonPushImg">
         <property name="text">
          <string>推图</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Fixed</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pushButtonPushVideo">
         <property name="text">
          <string>推视频</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Fixed</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pushButtonWB">
         <property name="text">
          <string>黑白切换</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_3">
       <item>
        <widget class="QLabel" name="labelImg">
         <property name="font">
          <font>
           <pointsize>40</pointsize>
          </font>
         </property>
         <property name="text">
          <string>拖拽图片到此处</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_3">
         <item>
          <widget class="QGroupBox" name="groupBox">
           <property name="title">
            <string>mate2转动控制</string>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout">
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_2">
              <item>
               <widget class="QSpinBox" name="spinBox">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>45</height>
                 </size>
                </property>
                <property name="minimum">
                 <number>-110</number>
                </property>
                <property name="maximum">
                 <number>360</number>
                </property>
                <property name="value">
                 <number>40</number>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="pushButtonRotate">
                <property name="text">
                 <string>执行</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="groupBox_2">
           <property name="title">
            <string>mate3转动控制</string>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_2">
            <item>
             <layout class="QGridLayout" name="gridLayout" rowminimumheight="45,45">
              <item row="0" column="0">
               <widget class="QLabel" name="label_2">
                <property name="text">
                 <string>Yaw位置</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QSpinBox" name="spinBoxYaw">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>45</height>
                 </size>
                </property>
                <property name="minimum">
                 <number>-177</number>
                </property>
                <property name="maximum">
                 <number>177</number>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="label_3">
                <property name="text">
                 <string>Pitch位置</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QSpinBox" name="spinBoxPitch">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>45</height>
                 </size>
                </property>
                <property name="minimum">
                 <number>0</number>
                </property>
                <property name="maximum">
                 <number>360</number>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QPushButton" name="pushButtonMate3Rotate">
              <property name="text">
               <string>执行</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
