from PyQt5.QtCore import QTimer, Qt
from PyQt5.QtWidgets import QDialog

from common.ui.UiMessagDialog import Ui_DialogMessage


class MessageDialog(Ui_DialogMessage, QDialog):

    def __init__(self, title, info, timeout=None, confirm="确认", cancel="取消"):
        super(MessageDialog, self).__init__()
        self.setupUi(self)
        self.setWindowTitle(title)
        self.setWindowFlags(Qt.WindowCloseButtonHint)
        self.label.setText(info)
        self.label.setWordWrap(True)

        if timeout is not None:
            # 设置定时器
            self.timer = QTimer(self)
            # 设置为单次定时器
            self.timer.setSingleShot(True)
            # 当时间到达时接受对话框，等同于点击OK
            self.timer.timeout.connect(self.accept)
            # 启动定时器
            self.timer.start(timeout)

        self.confirm_btn.setText(confirm)
        self.cancel_btn.setText(cancel)
        self.confirm_btn.clicked.connect(self.confirm)
        self.cancel_btn.clicked.connect(self.cancel)
        self.callback_result = None

    def closeEvent(self, a0):
        if self.callback_result is not None:
            self.callback_result(0x02)
        self.close()

    @staticmethod
    def show_message(title, info):
        dialog = MessageDialog(title, info)
        result = dialog.exec_()
        return result

    @staticmethod
    def show_auto_close_message(title, info, timeout=3000):
        dialog = MessageDialog(title=title, info=info, timeout=timeout)
        result = dialog.exec_()
        return result

    def set_callback(self, callback=None):
        self.callback_result = callback

    def confirm(self):
        self.close()
        if self.callback_result is not None:
            self.callback_result(0x01)
        self.accept()

    def cancel(self):
        self.close()
        if self.callback_result is not None:
            self.callback_result(0x02)
        self.reject()

    def close_dialog(self):
        self.close()
