from libTSCANAPI import *


# configs = [{'FChannel': 0, 'rate_baudrate': 500, 'data_baudrate': 2000, 'enable_120hm': True, 'is_fd': True},
#            {'FChannel': 1, 'rate_baudrate': 500, 'data_baudrate': 2000, 'enable_120hm': True, 'is_fd': True},
#            {'FChannel': 2, 'rate_baudrate': 500, 'data_baudrate': 2000, 'enable_120hm': True, 'is_fd': True},
#            {'FChannel': 3, 'rate_baudrate': 500, 'data_baudrate': 2000, 'enable_120hm': True, 'is_fd': True}]
#
# hwhandle = TSMasterDevice(configs=configs, is_include_tx=True, hwserial=b"")
#
# print(hwhandle)
# msg = TLIBCAN(FIdxChn=0, FDLC=8, FIdentifier=0X1, FProperties=1, FData=[1, 2, 3, 4, 5, 6, 7, 8])
#
# hwhandle.send_msg(msg)

class TSMaster:

    def __init__(self, cantype, channel, baudrate):
        self.bus = None
        self.channel = channel
        self.is_fd = True if cantype.lower() == "canfd" else False
        baudrate = int(baudrate / 1000)
        self.name = "tsmaster"
        self.configs = [
            {'FChannel': 0, 'rate_baudrate': baudrate, 'data_baudrate': 2000, 'enable_120hm': True,
             'is_fd': self.is_fd},
            {'FChannel': 1, 'rate_baudrate': baudrate, 'data_baudrate': 2000, 'enable_120hm': True,
             'is_fd': self.is_fd},
            {'FChannel': 2, 'rate_baudrate': baudrate, 'data_baudrate': 2000, 'enable_120hm': True,
             'is_fd': self.is_fd},
            {'FChannel': 3, 'rate_baudrate': baudrate, 'data_baudrate': 2000, 'enable_120hm': True,
             'is_fd': self.is_fd}
        ]
        self.status = False
        self.init_device()

    def init_device(self):
        self.bus = TSMasterDevice(configs=self.configs, is_include_tx=True, hwserial=b"")
        if self.bus._TSMasterDevice__hw_isconnect:
            self.status = True
        else:
            self.status = False

    def _recv_internal(self, timeout=3):
        return self.bus.recv(channel=self.channel, timeout=1), False

    def send(self, msg: can.Message, timeout: Optional[float] = 0.1, sync: bool = False,
             is_cyclic: bool = False) -> None:
        self.bus.send_msg(msg, timeout, sync, is_cyclic)

    def shutdown(self) -> None:
        finalize_lib_tscan()
