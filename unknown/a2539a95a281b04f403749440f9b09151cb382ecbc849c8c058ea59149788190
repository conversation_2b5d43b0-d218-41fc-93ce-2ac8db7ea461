# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'BaseHomePage.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_BasePage(object):
    def setupUi(self, BasePage):
        BasePage.setObjectName("BasePage")
        BasePage.resize(852, 556)
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(BasePage)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setSpacing(0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName("verticalLayout")
        self.stackedWidget = QtWidgets.QStackedWidget(BasePage)
        self.stackedWidget.setObjectName("stackedWidget")
        self.page = QtWidgets.QWidget()
        self.page.setObjectName("page")
        self.stackedWidget.addWidget(self.page)
        self.page_2 = QtWidgets.QWidget()
        self.page_2.setObjectName("page_2")
        self.stackedWidget.addWidget(self.page_2)
        self.page_3 = QtWidgets.QWidget()
        self.page_3.setObjectName("page_3")
        self.stackedWidget.addWidget(self.page_3)
        self.page_4 = QtWidgets.QWidget()
        self.page_4.setObjectName("page_4")
        self.stackedWidget.addWidget(self.page_4)
        self.verticalLayout.addWidget(self.stackedWidget)
        self.verticalLayout.setStretch(0, 99)
        self.verticalLayout_2.addLayout(self.verticalLayout)

        self.retranslateUi(BasePage)
        QtCore.QMetaObject.connectSlotsByName(BasePage)

    def retranslateUi(self, BasePage):
        _translate = QtCore.QCoreApplication.translate
        BasePage.setWindowTitle(_translate("BasePage", "Form"))
