%YAML:1.0
---
size: !!opencv-matrix
   rows: 2
   cols: 1
   dt: d
   data: [ 1920., 1080. ]
K1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 1.0898684528675460e+03, 0., 9.9751966489928202e+02, 0.,
       1.0895085367198747e+03, 5.6181657861264648e+02, 0., 0., 1. ]
D1: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ 8.2920626187311788e-02, -1.1877144136303881e-01,
       7.4384645956123688e-04, -1.1479132642596543e-04,
       5.2933186885836508e-02 ]
K2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 1.0881127357162345e+03, 0., 1.0217829301976955e+03, 0.,
       1.0880094050074063e+03, 5.5578970368913349e+02, 0., 0., 1. ]
D2: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ 8.3395864330604960e-02, -1.4439354330239054e-01,
       -2.1387671855591258e-04, 1.6143227992706353e-04,
       9.9607557972923366e-02 ]
R: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.9997448850630200e-01, -1.1845161195355654e-04,
       -7.1420099254574619e-03, 1.3193268687620858e-04,
       9.9999821066021022e-01, 1.8871327838515654e-03,
       7.1417736120547061e-03, -1.8880269048346049e-03,
       9.9997271483980044e-01 ]
T: !!opencv-matrix
   rows: 3
   cols: 1
   dt: d
   data: [ -6.0036380468871002e+01, -1.6810689890627820e-01,
       -8.5772734695495700e-01 ]
E: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -1.0874191411222364e-03, 8.5804320253730970e-01,
       -1.6648366668656617e-01, -4.2893922725329109e-01,
       -1.1324870240725178e-01, 6.0040868263837396e+01,
       1.6018184926260567e-01, -6.0036292955919748e+01,
       -1.1449724294711880e-01 ]
F: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -2.4987266318104095e-09, 1.9723063443593273e-06,
       -1.5225160405462328e-03, -9.8573183188816582e-07,
       -2.6033927743820803e-07, 1.5150746585838698e-01,
       9.5091870576495064e-04, -1.5203013200967902e-01, 1. ]
R1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.9997092320366310e-01, 2.6543720842893376e-03,
       7.1489199220534320e-03, -2.6611503625858499e-03,
       9.9999601845044628e-01, 9.3880882099294102e-04,
       -7.1463995103475779e-03, -9.5780587428272417e-04,
       9.9997400545311455e-01 ]
R2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.9989404038006591e-01, 2.7997871465661323e-03,
       1.4285279288875593e-02, -2.7862385949153808e-03,
       9.9999564968196730e-01, -9.6824151555533053e-04,
       -1.4287928013517522e-02, 9.2833672455852633e-04,
       9.9989749139799644e-01 ]
P1: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ 1.0968508447242093e+03, 0., 9.8504021453857422e+02, 0., 0.,
       1.0968508447242093e+03, 5.5840150451660156e+02, 0., 0., 0., 1.,
       0. ]
P2: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ 1.0968508447242093e+03, 0., 9.8504021453857422e+02,
       -6.5857932913006283e+04, 0., 1.0968508447242093e+03,
       5.5840150451660156e+02, 0., 0., 0., 1., 0. ]
Q: !!opencv-matrix
   rows: 4
   cols: 4
   dt: d
   data: [ 1., 0., 0., -9.8504021453857422e+02, 0., 1., 0.,
       -5.5840150451660156e+02, 0., 0., 0., 1.0968508447242093e+03, 0.,
       0., 1.6654802181129375e-02, 0. ]
