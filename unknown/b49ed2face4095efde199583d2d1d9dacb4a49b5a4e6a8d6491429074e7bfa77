import operator

from PyQt5.QtCore import QTimer, Qt
from PyQt5.QtWidgets import QDialog

from common.ui.UiCaseDecisionDialog import Ui_CaseDecisionDialog
# from res import custom_cancel_btn_stylesheet, custom_confirm_btn_stylesheet


class CaseDecisionDialog(Ui_CaseDecisionDialog, QDialog):

    def __init__(self, title, info, timeout=None, confirm="PASS", cancel="NG"):
        super(CaseDecisionDialog, self).__init__()
        self.setupUi(self)
        self.setWindowTitle(title)
        self.setWindowFlags(Qt.WindowCloseButtonHint)
        self.label.setText(info)

        if timeout is not None:
            # 设置定时器
            self.timer = QTimer(self)
            # 设置为单次定时器
            self.timer.setSingleShot(True)
            # 当时间到达时接受对话框，等同于点击OK
            self.timer.timeout.connect(self.accept)
            # 启动定时器
            self.timer.start(timeout)

        self.confirm_btn.setText(confirm)
        self.cancel_btn.setText(cancel)
        self.confirm_btn.clicked.connect(self.confirm)
        self.cancel_btn.clicked.connect(self.cancel)
        self.callback_result = None

    @staticmethod
    def show_message(title, info):
        dialog = CaseDecisionDialog(title, info)
        result = dialog.exec_()
        return result

    @staticmethod
    def show_auto_close_message(title, info, timeout=3000):
        dialog = CaseDecisionDialog(title=title, info=info, timeout=timeout)
        result = dialog.exec_()
        return result

    def set_callback(self, callback=None):
        self.callback_result = callback

    def confirm(self):
        step_actual = self.step_actual_text_edit.toPlainText()
        if operator.eq("", step_actual):
            return self.step_actual_text_edit.setPlaceholderText("请输入实测值")

        self.close()
        if self.callback_result is not None:
            self.callback_result(0x00, step_actual)

    def cancel(self):
        step_actual = self.step_actual_text_edit.toPlainText()
        if operator.eq("", step_actual):
            return self.step_actual_text_edit.setPlaceholderText("请输入实测值")

        self.close()
        if self.callback_result is not None:
            self.callback_result(0x01, step_actual)

    def close_dialog(self):
        self.close()
