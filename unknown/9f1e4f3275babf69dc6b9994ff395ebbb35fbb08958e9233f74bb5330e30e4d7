%YAML:1.0
---
size: !!opencv-matrix
   rows: 2
   cols: 1
   dt: d
   data: [ 1.920000000000000e+03, 1.080000000000000e+03 ]
K1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 1.083939996147169e+03, 0.000000000000000e+00, 9.268806626923167e+02, 0.000000000000000e+00, 1.083702620503894e+03, 4.903191533207971e+02, 0.000000000000000e+00, 0.000000000000000e+00, 1.000000000000000e+00 ]
K2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 1.086152077310374e+03, 0.000000000000000e+00, 9.766865651154884e+02, 0.000000000000000e+00, 1.086416092848762e+03, 4.940822445908638e+02, 0.000000000000000e+00, 0.000000000000000e+00, 1.000000000000000e+00 ]
D1: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ 7.873939238678374e-02, -9.118318957266447e-02, 4.838934790406887e-04, 2.717976757973002e-04, -1.212239318873361e-02 ]
D2: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ 7.670490285106728e-02, -1.183731305740599e-01, 6.599422144290037e-04, -4.516092471601827e-04, 6.452969072884439e-02 ]
R: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.999716626007573e-01, 1.459885493161458e-03, 7.385304991943797e-03, -1.508446276505206e-03, 9.999772530749654e-01, 6.574033957733931e-03, -7.375539662158344e-03, -6.584988002524986e-03, 9.999511184791477e-01 ]
T: !!opencv-matrix
   rows: 3
   cols: 1
   dt: d
   data: [ -5.992920307324828e+01, -1.904307325011906e-01, -2.280754258789331e-03 ]
E: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -1.403059399304684e-03, 1.028804276584210e-03, -1.904364426967997e-01, 4.403147529919287e-01, 3.939800564587361e-01, 5.992629046445209e+01, 1.029355620061334e-01, -5.992812712268797e+01, 3.932285538177671e-01 ]
F: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -1.191736329552288e-09, 8.740413923702153e-10, -1.746552498932454e-04, 3.739054585325345e-07, 3.346323508822828e-07, 5.464896989736542e-02, -8.861183315157854e-05, -5.546561026544635e-02, 5.890708759008589e-01 ]
Q: !!opencv-matrix
   rows: 4
   cols: 4
   dt: d
   data: [ 1.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 1.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, -1.668627145829074e-02, -9.268806626923167e+02, -4.903191533207971e+02, 1.083939996147169e+03, -8.310748080581832e-01 ]
R1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.804065364806358e-03, 9.988758794822855e-01, -4.637734027316912e-02, -9.996937646511027e-01, 8.737132045221533e-03, -2.315252559290121e-02, -2.272129441797248e-02, 4.659012676646278e-02, 9.986556477924994e-01 ]
R2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 8.639094854605915e-03, 9.991728651696241e-01, -3.973602331408573e-02, -9.995078530532848e-01, 7.429953980972925e-03, -3.047699900997331e-02, -3.015655359795631e-02, 3.997976103686799e-02, 9.987453133719971e-01 ]
P1: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ -3.235924131254727e+01, 1.059744709937074e+03, -4.637734027316912e-02, -1.105067683668814e+03, -1.883673852854288e+00, -2.315252559290121e-02, 9.010060888434609e+02, 5.401498341510749e+02, 9.986556477924994e-01, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00 ]
P2: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ -2.942626929957178e+01, 1.065884616667948e+03, -3.973602331408573e-02, -1.115384006359936e+03, -6.986122505186437e+00, -3.047699900997331e-02, 9.427065262075461e+02, 5.368969819841433e+02, 9.987453133719971e-01, -5.993125396657107e+01, -1.915145189068888e-01, -2.099847992223278e-06 ]
