import struct

from PyQt5.QtCore import pyqtSignal, QThread

from common.modbus.BaseIO import BaseIO


class TASIO(BaseIO):

    device_info_signal = pyqtSignal(str, str, str)

    def __init__(self):
        super().__init__()
        self.thread: QThread = None

    def connect(self, port, baud_rate=9600, bytesize=8, parity="N", stop_bits=1, slave_id=1, timeout=2.0):
        super(TASIO, self).connect(port, baud_rate, bytesize, parity, stop_bits, slave_id, timeout)

        self.thread = TASIOThread(self)
        self.thread.start()

    def power_on(self, position):
        try:
            starting_address = position
            value = 0xFF00
            self.client.write_coil_one(starting_address, value)
        except Exception as e:
            print(e.args)

    def power_off(self, position):
        try:
            starting_address = position
            value = 0x0000
            self.client.write_coil_one(starting_address, value)
        except Exception as e:
            print(e.args)

    def read_device_info(self):
        """
        读取设备信息
        :return:
        """
        device_name = self.read_device_name()
        software_version = self.read_software_version()
        hardware_version = self.read_hardware_version()
        self.device_info_signal.emit(device_name, software_version, hardware_version)

    def read_device_name(self) -> str:
        try:
            r = self.client.read_hr_many(0x1200, 0x05)
            if r is not None and len(r) > 0:
                device_name = f"{struct.pack('>hhhhh', r[0], r[1], r[2], r[3], r[4]).decode()}"
            else:
                device_name = '--'
            print(f"device name: {device_name}")
        except Exception as e:
            print(e.args)
            device_name = '--'
        return device_name

    def read_software_version(self) -> str:
        try:
            r = self.client.read_hr_many(0x0360, 0x04)
            if r is not None and len(r) > 0:
                version = f"{struct.pack('>hhh', r[0], r[1], r[2]).decode()}"
            else:
                version = '--'
            print(f'software version: {version}')
        except Exception as e:
            print('read_software_version', e.args)
            version = '--'
        return version

    def read_hardware_version(self) -> str:
        try:
            r = self.client.read_hr_many(0x0370, 0x04)
            if r is not None and len(r) > 0:
                version = f"{struct.pack('>hhh', r[0], r[1], r[2]).decode()}"
            else:
                version = '--'
            print(f'hardware version: {version}')
        except Exception as e:
            print(e.args)
            version = '--'
        return version

    def read_electric_current(self):
        try:
            r = self.client.read_hr_one(0x00C0)
            print(f"electric: {r}")
        except Exception as e:
            print(e.args)


class TASIOThread(QThread):

    def __init__(self, client: TASIO):
        super().__init__()
        self.client = client

    def run(self) -> None:
        self.client.read_device_info()
