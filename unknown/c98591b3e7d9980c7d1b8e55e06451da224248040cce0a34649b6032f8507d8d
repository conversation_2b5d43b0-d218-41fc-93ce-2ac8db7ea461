%YAML:1.0
---
size: !!opencv-matrix
   rows: 2
   cols: 1
   dt: d
   data: [ 1.920000000000000e+03, 1.080000000000000e+03 ]
K1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 1.084835050136418e+03, 0.000000000000000e+00, 9.389343672840890e+02, 0.000000000000000e+00, 1.084770066811669e+03, 4.764246820186756e+02, 0.000000000000000e+00, 0.000000000000000e+00, 1.000000000000000e+00 ]
K2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 1.085947387351901e+03, 0.000000000000000e+00, 9.711312731259916e+02, 0.000000000000000e+00, 1.086170018658918e+03, 4.954196946774913e+02, 0.000000000000000e+00, 0.000000000000000e+00, 1.000000000000000e+00 ]
D1: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ 7.737518048867807e-02, -1.097997714985676e-01, 6.837099271517154e-04, 4.304675959726023e-04, 4.201025423599768e-02 ]
D2: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ 9.264907750490435e-02, -2.165214628673079e-01, -1.599300564682116e-04, -1.298603831095394e-04, 2.461737075941244e-01 ]
R: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.999700335998069e-01, -1.732635887839724e-03, 7.545188883077754e-03, 1.737403174053684e-03, 9.999982951995445e-01, -6.253224891662625e-04, -7.544092563850105e-03, 6.384127856166086e-04, 9.999713391375291e-01 ]
T: !!opencv-matrix
   rows: 3
   cols: 1
   dt: d
   data: [ -6.001798812730685e+01, -3.238501818169844e-02, -2.146575124980787e-01 ]
E: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -6.162743889113972e-04, 2.146773976300347e-01, -3.224704989863879e-02, 2.381959768176936e-01, -3.790354437406734e-02, 6.001788735614716e+01, 1.363733678645936e-01, -6.001782954277998e+01, -3.856056656230322e-02 ]
F: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -5.231203639176158e-10, 1.822383793165569e-07, -1.160265409474267e-04, 2.021496075572452e-07, -3.216950062355102e-08, 5.508196010859361e-02, 2.606797286258528e-05, -5.548873695891839e-02, -9.731233464533169e-01 ]
Q: !!opencv-matrix
   rows: 4
   cols: 4
   dt: d
   data: [ 1.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 1.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, -1.666156247189857e-02, -9.389343672840890e+02, -4.764246820186756e+02, 1.084835050136418e+03, -5.364507580866954e-01 ]
R1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -3.772443302343892e-03, -9.998915557809006e-01, 1.423535561835500e-02, 9.999395972209185e-01, -3.918818018170873e-03, -1.026863062964972e-02, 1.032330282411214e-02, 1.419575793647137e-02, 9.998459430709369e-01 ]
R2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -5.616937858985211e-03, -9.998742268616357e-01, 1.483173850007581e-02, 9.999802915638899e-01, -5.657898194269457e-03, -2.721152664101321e-03, 2.804726882668244e-03, 1.481616164428612e-02, 9.998863009668871e-01 ]
P1: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ 9.273585901549126e+00, -1.077870354994973e+03, 1.423535561835500e-02, 1.075127952881421e+03, -9.143245565892087e+00, -1.026863062964972e-02, 9.499887986756426e+02, 4.917504187804223e+02, 9.998459430709369e-01, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00 ]
P2: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ 8.303866099367440e+00, -1.078685472287630e+03, 1.483173850007581e-02, 1.083283388576140e+03, -7.493552009259830e+00, -2.721152664101321e-03, 9.740666422696806e+02, 5.114562365068492e+02, 9.998863009668871e-01, -6.020995011904446e+01, -1.303204895126773e-01, -1.976684275851745e-04 ]
