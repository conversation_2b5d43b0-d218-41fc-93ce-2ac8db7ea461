%YAML:1.0
---
size: !!opencv-matrix
   rows: 2
   cols: 1
   dt: d
   data: [ 1.920000000000000e+03, 1.080000000000000e+03 ]
K1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 1.086521613517773e+03, 0.000000000000000e+00, 9.979279525758302e+02, 0.000000000000000e+00, 1.086280111622965e+03, 5.631405353182554e+02, 0.000000000000000e+00, 0.000000000000000e+00, 1.000000000000000e+00 ]
K2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 1.087830811914427e+03, 0.000000000000000e+00, 1.022735115111295e+03, 0.000000000000000e+00, 1.087689438219634e+03, 5.579178007906687e+02, 0.000000000000000e+00, 0.000000000000000e+00, 1.000000000000000e+00 ]
D1: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ 8.391729426446469e-02, -1.249264890815240e-01, 6.673429711160796e-04, -2.392418252507933e-04, 5.999713853146724e-02 ]
D2: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ 8.064391607867136e-02, -1.355423008758884e-01, 3.011144644538485e-04, 2.370396166403242e-04, 1.019614819880730e-01 ]
R: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.999726194414709e-01, 1.682667921418782e-04, 7.398111492133179e-03, -1.600708240756168e-04, 9.999993728864314e-01, -1.108424140560493e-03, -7.398293363651544e-03, 1.107209569485288e-03, 9.999720192796769e-01 ]
T: !!opencv-matrix
   rows: 3
   cols: 1
   dt: d
   data: [ -6.008610970318878e+01, -2.102292710818801e-01, -2.300988810672225e-01 ]
E: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -1.516581585781021e-03, 2.303317599682116e-01, -2.099686210323967e-01, 2.144311578813939e-01, -6.656406238986923e-02, 6.008613078958265e+01, 2.001130179549781e-01, -6.008610567394676e+01, -6.808325347760383e-02 ]
F: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -1.283116391220497e-09, 1.949174145536754e-07, -3.015013190749603e-04, 1.814448346884579e-07, -5.633691732318882e-08, 5.509264831104711e-02, 8.425865392623676e-05, -5.548155626385194e-02, 4.686596385605100e-01 ]
Q: !!opencv-matrix
   rows: 4
   cols: 4
   dt: d
   data: [ 1.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 1.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, -1.664255768815325e-02, -9.979279525758302e+02, -5.631405353182554e+02, 1.086521613517773e+03, -4.128546335758601e-01 ]
R1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 5.988752712686304e-03, 9.999819950935350e-01, -3.799074886210538e-04, -9.998729081638874e-01, 5.993711898771079e-03, 1.477304767355489e-02, 1.477505874224830e-02, 2.913870761511285e-04, 9.998908004040920e-01 ]
R2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 5.831331462328956e-03, 9.999819550622582e-01, -1.443995581756110e-03, -9.999557858441706e-01, 5.841819592981690e-03, 7.368819485519483e-03, 7.377122077312318e-03, 1.400961707804309e-03, 9.999718072906604e-01 ]
P1: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ 6.127788958059028e+00, 1.086046611944647e+03, -3.799074886210538e-04, -1.071641088272759e+03, 1.483015200560075e+01, 1.477304767355489e-02, 1.013872399911111e+03, 5.633955685849662e+02, 9.998908004040920e-01, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00 ]
P2: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ 4.866677051679915e+00, 1.086864180092115e+03, -1.443995581756110e-03, -1.080246363948636e+03, 1.046528103305516e+01, 7.368819485519483e-03, 1.030731342136417e+03, 5.594258828292045e+02, 9.999718072906604e-01, -6.030243948814965e+01, -3.282131886128981e-01, -2.115208344414159e-04 ]
