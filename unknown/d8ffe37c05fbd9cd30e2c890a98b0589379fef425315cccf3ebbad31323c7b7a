# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'axisWidget.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(2400, 1200)
        Form.setMinimumSize(QtCore.QSize(2400, 1200))
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(Form)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.tabWidget = QtWidgets.QTabWidget(Form)
        self.tabWidget.setObjectName("tabWidget")
        self.tab = QtWidgets.QWidget()
        self.tab.setObjectName("tab")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.tab)
        self.verticalLayout.setObjectName("verticalLayout")
        self.groupBox_3 = QtWidgets.QGroupBox(self.tab)
        self.groupBox_3.setTitle("")
        self.groupBox_3.setObjectName("groupBox_3")
        self.gridLayout_3 = QtWidgets.QGridLayout(self.groupBox_3)
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.label_46 = QtWidgets.QLabel(self.groupBox_3)
        self.label_46.setObjectName("label_46")
        self.gridLayout_3.addWidget(self.label_46, 5, 0, 1, 1)
        self.label_50 = QtWidgets.QLabel(self.groupBox_3)
        self.label_50.setObjectName("label_50")
        self.gridLayout_3.addWidget(self.label_50, 1, 1, 1, 1)
        self.label_52 = QtWidgets.QLabel(self.groupBox_3)
        self.label_52.setObjectName("label_52")
        self.gridLayout_3.addWidget(self.label_52, 3, 1, 1, 1)
        self.label_48 = QtWidgets.QLabel(self.groupBox_3)
        self.label_48.setObjectName("label_48")
        self.gridLayout_3.addWidget(self.label_48, 0, 1, 1, 1)
        self.label_44 = QtWidgets.QLabel(self.groupBox_3)
        self.label_44.setObjectName("label_44")
        self.gridLayout_3.addWidget(self.label_44, 4, 0, 1, 1)
        self.label_49 = QtWidgets.QLabel(self.groupBox_3)
        self.label_49.setObjectName("label_49")
        self.gridLayout_3.addWidget(self.label_49, 2, 1, 1, 1)
        self.label_43 = QtWidgets.QLabel(self.groupBox_3)
        self.label_43.setObjectName("label_43")
        self.gridLayout_3.addWidget(self.label_43, 3, 0, 1, 1)
        self.label_47 = QtWidgets.QLabel(self.groupBox_3)
        self.label_47.setObjectName("label_47")
        self.gridLayout_3.addWidget(self.label_47, 0, 0, 1, 1)
        self.label_51 = QtWidgets.QLabel(self.groupBox_3)
        self.label_51.setObjectName("label_51")
        self.gridLayout_3.addWidget(self.label_51, 2, 0, 1, 1)
        self.label_45 = QtWidgets.QLabel(self.groupBox_3)
        self.label_45.setObjectName("label_45")
        self.gridLayout_3.addWidget(self.label_45, 4, 1, 1, 1)
        self.label_53 = QtWidgets.QLabel(self.groupBox_3)
        self.label_53.setObjectName("label_53")
        self.gridLayout_3.addWidget(self.label_53, 5, 1, 1, 1)
        self.label_54 = QtWidgets.QLabel(self.groupBox_3)
        self.label_54.setObjectName("label_54")
        self.gridLayout_3.addWidget(self.label_54, 1, 0, 1, 1)
        self.pushButton_home_1 = QtWidgets.QPushButton(self.groupBox_3)
        self.pushButton_home_1.setObjectName("pushButton_home_1")
        self.gridLayout_3.addWidget(self.pushButton_home_1, 1, 2, 1, 1)
        self.pushButton_home_2 = QtWidgets.QPushButton(self.groupBox_3)
        self.pushButton_home_2.setObjectName("pushButton_home_2")
        self.gridLayout_3.addWidget(self.pushButton_home_2, 2, 2, 1, 1)
        self.pushButton_home_3 = QtWidgets.QPushButton(self.groupBox_3)
        self.pushButton_home_3.setObjectName("pushButton_home_3")
        self.gridLayout_3.addWidget(self.pushButton_home_3, 3, 2, 1, 1)
        self.pushButton_home_4 = QtWidgets.QPushButton(self.groupBox_3)
        self.pushButton_home_4.setObjectName("pushButton_home_4")
        self.gridLayout_3.addWidget(self.pushButton_home_4, 4, 2, 1, 1)
        self.pushButton_home_5 = QtWidgets.QPushButton(self.groupBox_3)
        self.pushButton_home_5.setObjectName("pushButton_home_5")
        self.gridLayout_3.addWidget(self.pushButton_home_5, 5, 2, 1, 1)
        self.label_55 = QtWidgets.QLabel(self.groupBox_3)
        self.label_55.setObjectName("label_55")
        self.gridLayout_3.addWidget(self.label_55, 0, 2, 1, 1)
        self.verticalLayout.addWidget(self.groupBox_3)
        self.tabWidget.addTab(self.tab, "")
        self.tab_2 = QtWidgets.QWidget()
        self.tab_2.setObjectName("tab_2")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.tab_2)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.groupBox_4 = QtWidgets.QGroupBox(self.tab_2)
        self.groupBox_4.setTitle("")
        self.groupBox_4.setObjectName("groupBox_4")
        self.gridLayout_4 = QtWidgets.QGridLayout(self.groupBox_4)
        self.gridLayout_4.setObjectName("gridLayout_4")
        self.label_57 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_57.sizePolicy().hasHeightForWidth())
        self.label_57.setSizePolicy(sizePolicy)
        self.label_57.setObjectName("label_57")
        self.gridLayout_4.addWidget(self.label_57, 0, 0, 1, 1)
        self.label_63 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_63.sizePolicy().hasHeightForWidth())
        self.label_63.setSizePolicy(sizePolicy)
        self.label_63.setObjectName("label_63")
        self.gridLayout_4.addWidget(self.label_63, 0, 1, 1, 1)
        self.label_68 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_68.sizePolicy().hasHeightForWidth())
        self.label_68.setSizePolicy(sizePolicy)
        self.label_68.setObjectName("label_68")
        self.gridLayout_4.addWidget(self.label_68, 0, 2, 1, 1)
        self.label_69 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_69.sizePolicy().hasHeightForWidth())
        self.label_69.setSizePolicy(sizePolicy)
        self.label_69.setObjectName("label_69")
        self.gridLayout_4.addWidget(self.label_69, 0, 3, 1, 1)
        self.label_70 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_70.sizePolicy().hasHeightForWidth())
        self.label_70.setSizePolicy(sizePolicy)
        self.label_70.setObjectName("label_70")
        self.gridLayout_4.addWidget(self.label_70, 0, 4, 1, 1)
        self.label_71 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_71.sizePolicy().hasHeightForWidth())
        self.label_71.setSizePolicy(sizePolicy)
        self.label_71.setObjectName("label_71")
        self.gridLayout_4.addWidget(self.label_71, 0, 5, 1, 1)
        self.label_72 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_72.sizePolicy().hasHeightForWidth())
        self.label_72.setSizePolicy(sizePolicy)
        self.label_72.setObjectName("label_72")
        self.gridLayout_4.addWidget(self.label_72, 0, 6, 1, 1)
        self.label_73 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_73.sizePolicy().hasHeightForWidth())
        self.label_73.setSizePolicy(sizePolicy)
        self.label_73.setObjectName("label_73")
        self.gridLayout_4.addWidget(self.label_73, 0, 7, 1, 1)
        self.label_74 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_74.sizePolicy().hasHeightForWidth())
        self.label_74.setSizePolicy(sizePolicy)
        self.label_74.setObjectName("label_74")
        self.gridLayout_4.addWidget(self.label_74, 0, 8, 1, 1)
        self.label_75 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_75.sizePolicy().hasHeightForWidth())
        self.label_75.setSizePolicy(sizePolicy)
        self.label_75.setObjectName("label_75")
        self.gridLayout_4.addWidget(self.label_75, 0, 9, 1, 1)
        self.label_76 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_76.sizePolicy().hasHeightForWidth())
        self.label_76.setSizePolicy(sizePolicy)
        self.label_76.setObjectName("label_76")
        self.gridLayout_4.addWidget(self.label_76, 0, 10, 1, 1)
        self.label_77 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_77.sizePolicy().hasHeightForWidth())
        self.label_77.setSizePolicy(sizePolicy)
        self.label_77.setObjectName("label_77")
        self.gridLayout_4.addWidget(self.label_77, 0, 11, 1, 1)
        self.label_61 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_61.sizePolicy().hasHeightForWidth())
        self.label_61.setSizePolicy(sizePolicy)
        self.label_61.setObjectName("label_61")
        self.gridLayout_4.addWidget(self.label_61, 1, 0, 1, 1)
        self.label_65 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_65.sizePolicy().hasHeightForWidth())
        self.label_65.setSizePolicy(sizePolicy)
        self.label_65.setObjectName("label_65")
        self.gridLayout_4.addWidget(self.label_65, 1, 1, 1, 1)
        self.comboBox_trap_mode_1 = QtWidgets.QComboBox(self.groupBox_4)
        self.comboBox_trap_mode_1.setMinimumSize(QtCore.QSize(0, 45))
        self.comboBox_trap_mode_1.setObjectName("comboBox_trap_mode_1")
        self.comboBox_trap_mode_1.addItem("")
        self.comboBox_trap_mode_1.addItem("")
        self.gridLayout_4.addWidget(self.comboBox_trap_mode_1, 1, 2, 1, 1)
        self.doubleSpinBox_trap_vel_1 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_1.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_vel_1.setDecimals(3)
        self.doubleSpinBox_trap_vel_1.setObjectName("doubleSpinBox_trap_vel_1")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_1, 1, 4, 1, 1)
        self.doubleSpinBox_trap_acc_1 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_acc_1.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_acc_1.setDecimals(3)
        self.doubleSpinBox_trap_acc_1.setObjectName("doubleSpinBox_trap_acc_1")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_acc_1, 1, 5, 1, 1)
        self.doubleSpinBox_trap_dec_1 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_dec_1.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_dec_1.setDecimals(3)
        self.doubleSpinBox_trap_dec_1.setObjectName("doubleSpinBox_trap_dec_1")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_dec_1, 1, 6, 1, 1)
        self.doubleSpinBox_trap_vel_start_1 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_start_1.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_vel_start_1.setDecimals(3)
        self.doubleSpinBox_trap_vel_start_1.setObjectName("doubleSpinBox_trap_vel_start_1")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_start_1, 1, 7, 1, 1)
        self.spinBox_trap_smooth_time_1 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_smooth_time_1.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_trap_smooth_time_1.setObjectName("spinBox_trap_smooth_time_1")
        self.gridLayout_4.addWidget(self.spinBox_trap_smooth_time_1, 1, 8, 1, 1)
        self.spinBox = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox.setObjectName("spinBox")
        self.gridLayout_4.addWidget(self.spinBox, 1, 9, 1, 1)
        self.spinBox_2 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_2.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_2.setObjectName("spinBox_2")
        self.gridLayout_4.addWidget(self.spinBox_2, 1, 10, 1, 1)
        self.pushButton_trap_start_1 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_trap_start_1.setObjectName("pushButton_trap_start_1")
        self.gridLayout_4.addWidget(self.pushButton_trap_start_1, 1, 11, 1, 1)
        self.label_67 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_67.sizePolicy().hasHeightForWidth())
        self.label_67.setSizePolicy(sizePolicy)
        self.label_67.setObjectName("label_67")
        self.gridLayout_4.addWidget(self.label_67, 2, 0, 1, 1)
        self.label_58 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_58.sizePolicy().hasHeightForWidth())
        self.label_58.setSizePolicy(sizePolicy)
        self.label_58.setObjectName("label_58")
        self.gridLayout_4.addWidget(self.label_58, 2, 1, 1, 1)
        self.comboBox_trap_mode_2 = QtWidgets.QComboBox(self.groupBox_4)
        self.comboBox_trap_mode_2.setMinimumSize(QtCore.QSize(0, 45))
        self.comboBox_trap_mode_2.setObjectName("comboBox_trap_mode_2")
        self.comboBox_trap_mode_2.addItem("")
        self.comboBox_trap_mode_2.addItem("")
        self.gridLayout_4.addWidget(self.comboBox_trap_mode_2, 2, 2, 1, 1)
        self.doubleSpinBox_trap_vel_2 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_2.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_vel_2.setDecimals(3)
        self.doubleSpinBox_trap_vel_2.setObjectName("doubleSpinBox_trap_vel_2")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_2, 2, 4, 1, 1)
        self.doubleSpinBox_trap_acc_2 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_acc_2.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_acc_2.setDecimals(3)
        self.doubleSpinBox_trap_acc_2.setObjectName("doubleSpinBox_trap_acc_2")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_acc_2, 2, 5, 1, 1)
        self.doubleSpinBox_trap_dec_2 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_dec_2.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_dec_2.setDecimals(3)
        self.doubleSpinBox_trap_dec_2.setObjectName("doubleSpinBox_trap_dec_2")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_dec_2, 2, 6, 1, 1)
        self.doubleSpinBox_trap_vel_start_2 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_start_2.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_vel_start_2.setDecimals(3)
        self.doubleSpinBox_trap_vel_start_2.setObjectName("doubleSpinBox_trap_vel_start_2")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_start_2, 2, 7, 1, 1)
        self.spinBox_trap_smooth_time_2 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_smooth_time_2.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_trap_smooth_time_2.setObjectName("spinBox_trap_smooth_time_2")
        self.gridLayout_4.addWidget(self.spinBox_trap_smooth_time_2, 2, 8, 1, 1)
        self.spinBox_6 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_6.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_6.setObjectName("spinBox_6")
        self.gridLayout_4.addWidget(self.spinBox_6, 2, 9, 1, 1)
        self.spinBox_5 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_5.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_5.setObjectName("spinBox_5")
        self.gridLayout_4.addWidget(self.spinBox_5, 2, 10, 1, 1)
        self.pushButton_trap_start_2 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_trap_start_2.setObjectName("pushButton_trap_start_2")
        self.gridLayout_4.addWidget(self.pushButton_trap_start_2, 2, 11, 1, 1)
        self.label_66 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_66.sizePolicy().hasHeightForWidth())
        self.label_66.setSizePolicy(sizePolicy)
        self.label_66.setObjectName("label_66")
        self.gridLayout_4.addWidget(self.label_66, 3, 0, 1, 1)
        self.label_56 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_56.sizePolicy().hasHeightForWidth())
        self.label_56.setSizePolicy(sizePolicy)
        self.label_56.setObjectName("label_56")
        self.gridLayout_4.addWidget(self.label_56, 3, 1, 1, 1)
        self.comboBox_trap_mode_3 = QtWidgets.QComboBox(self.groupBox_4)
        self.comboBox_trap_mode_3.setMinimumSize(QtCore.QSize(0, 45))
        self.comboBox_trap_mode_3.setObjectName("comboBox_trap_mode_3")
        self.comboBox_trap_mode_3.addItem("")
        self.comboBox_trap_mode_3.addItem("")
        self.gridLayout_4.addWidget(self.comboBox_trap_mode_3, 3, 2, 1, 1)
        self.doubleSpinBox_trap_vel_3 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_3.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_vel_3.setDecimals(3)
        self.doubleSpinBox_trap_vel_3.setObjectName("doubleSpinBox_trap_vel_3")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_3, 3, 4, 1, 1)
        self.doubleSpinBox_trap_acc_3 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_acc_3.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_acc_3.setDecimals(3)
        self.doubleSpinBox_trap_acc_3.setObjectName("doubleSpinBox_trap_acc_3")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_acc_3, 3, 5, 1, 1)
        self.doubleSpinBox_trap_dec_3 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_dec_3.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_dec_3.setDecimals(3)
        self.doubleSpinBox_trap_dec_3.setObjectName("doubleSpinBox_trap_dec_3")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_dec_3, 3, 6, 1, 1)
        self.doubleSpinBox_trap_vel_start_3 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_start_3.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_vel_start_3.setDecimals(3)
        self.doubleSpinBox_trap_vel_start_3.setObjectName("doubleSpinBox_trap_vel_start_3")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_start_3, 3, 7, 1, 1)
        self.spinBox_trap_smooth_time_3 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_smooth_time_3.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_trap_smooth_time_3.setObjectName("spinBox_trap_smooth_time_3")
        self.gridLayout_4.addWidget(self.spinBox_trap_smooth_time_3, 3, 8, 1, 1)
        self.spinBox_9 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_9.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_9.setObjectName("spinBox_9")
        self.gridLayout_4.addWidget(self.spinBox_9, 3, 9, 1, 1)
        self.spinBox_8 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_8.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_8.setObjectName("spinBox_8")
        self.gridLayout_4.addWidget(self.spinBox_8, 3, 10, 1, 1)
        self.pushButton_trap_start_3 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_trap_start_3.setObjectName("pushButton_trap_start_3")
        self.gridLayout_4.addWidget(self.pushButton_trap_start_3, 3, 11, 1, 1)
        self.label_59 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_59.sizePolicy().hasHeightForWidth())
        self.label_59.setSizePolicy(sizePolicy)
        self.label_59.setObjectName("label_59")
        self.gridLayout_4.addWidget(self.label_59, 4, 0, 1, 1)
        self.label_62 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_62.sizePolicy().hasHeightForWidth())
        self.label_62.setSizePolicy(sizePolicy)
        self.label_62.setObjectName("label_62")
        self.gridLayout_4.addWidget(self.label_62, 4, 1, 1, 1)
        self.comboBox_trap_mode_4 = QtWidgets.QComboBox(self.groupBox_4)
        self.comboBox_trap_mode_4.setMinimumSize(QtCore.QSize(0, 45))
        self.comboBox_trap_mode_4.setObjectName("comboBox_trap_mode_4")
        self.comboBox_trap_mode_4.addItem("")
        self.comboBox_trap_mode_4.addItem("")
        self.gridLayout_4.addWidget(self.comboBox_trap_mode_4, 4, 2, 1, 1)
        self.doubleSpinBox_trap_vel_4 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_4.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_vel_4.setDecimals(3)
        self.doubleSpinBox_trap_vel_4.setObjectName("doubleSpinBox_trap_vel_4")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_4, 4, 4, 1, 1)
        self.doubleSpinBox_trap_acc_4 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_acc_4.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_acc_4.setDecimals(3)
        self.doubleSpinBox_trap_acc_4.setObjectName("doubleSpinBox_trap_acc_4")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_acc_4, 4, 5, 1, 1)
        self.doubleSpinBox_trap_dec_4 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_dec_4.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_dec_4.setDecimals(3)
        self.doubleSpinBox_trap_dec_4.setObjectName("doubleSpinBox_trap_dec_4")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_dec_4, 4, 6, 1, 1)
        self.doubleSpinBox_trap_vel_start_4 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_start_4.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_vel_start_4.setDecimals(3)
        self.doubleSpinBox_trap_vel_start_4.setObjectName("doubleSpinBox_trap_vel_start_4")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_start_4, 4, 7, 1, 1)
        self.spinBox_trap_smooth_time_4 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_smooth_time_4.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_trap_smooth_time_4.setObjectName("spinBox_trap_smooth_time_4")
        self.gridLayout_4.addWidget(self.spinBox_trap_smooth_time_4, 4, 8, 1, 1)
        self.spinBox_12 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_12.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_12.setObjectName("spinBox_12")
        self.gridLayout_4.addWidget(self.spinBox_12, 4, 9, 1, 1)
        self.spinBox_11 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_11.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_11.setObjectName("spinBox_11")
        self.gridLayout_4.addWidget(self.spinBox_11, 4, 10, 1, 1)
        self.pushButton_trap_start_4 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_trap_start_4.setObjectName("pushButton_trap_start_4")
        self.gridLayout_4.addWidget(self.pushButton_trap_start_4, 4, 11, 1, 1)
        self.label_60 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_60.sizePolicy().hasHeightForWidth())
        self.label_60.setSizePolicy(sizePolicy)
        self.label_60.setObjectName("label_60")
        self.gridLayout_4.addWidget(self.label_60, 5, 0, 1, 1)
        self.label_64 = QtWidgets.QLabel(self.groupBox_4)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_64.sizePolicy().hasHeightForWidth())
        self.label_64.setSizePolicy(sizePolicy)
        self.label_64.setObjectName("label_64")
        self.gridLayout_4.addWidget(self.label_64, 5, 1, 1, 1)
        self.comboBox_trap_mode_5 = QtWidgets.QComboBox(self.groupBox_4)
        self.comboBox_trap_mode_5.setMinimumSize(QtCore.QSize(0, 45))
        self.comboBox_trap_mode_5.setObjectName("comboBox_trap_mode_5")
        self.comboBox_trap_mode_5.addItem("")
        self.comboBox_trap_mode_5.addItem("")
        self.gridLayout_4.addWidget(self.comboBox_trap_mode_5, 5, 2, 1, 1)
        self.doubleSpinBox_trap_vel_5 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_5.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_vel_5.setDecimals(3)
        self.doubleSpinBox_trap_vel_5.setObjectName("doubleSpinBox_trap_vel_5")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_5, 5, 4, 1, 1)
        self.doubleSpinBox_trap_acc_5 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_acc_5.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_acc_5.setDecimals(3)
        self.doubleSpinBox_trap_acc_5.setObjectName("doubleSpinBox_trap_acc_5")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_acc_5, 5, 5, 1, 1)
        self.doubleSpinBox_trap_dec_5 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_dec_5.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_dec_5.setDecimals(3)
        self.doubleSpinBox_trap_dec_5.setObjectName("doubleSpinBox_trap_dec_5")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_dec_5, 5, 6, 1, 1)
        self.doubleSpinBox_trap_vel_start_5 = QtWidgets.QDoubleSpinBox(self.groupBox_4)
        self.doubleSpinBox_trap_vel_start_5.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_trap_vel_start_5.setDecimals(3)
        self.doubleSpinBox_trap_vel_start_5.setObjectName("doubleSpinBox_trap_vel_start_5")
        self.gridLayout_4.addWidget(self.doubleSpinBox_trap_vel_start_5, 5, 7, 1, 1)
        self.spinBox_trap_smooth_time_5 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_smooth_time_5.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_trap_smooth_time_5.setObjectName("spinBox_trap_smooth_time_5")
        self.gridLayout_4.addWidget(self.spinBox_trap_smooth_time_5, 5, 8, 1, 1)
        self.spinBox_15 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_15.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_15.setObjectName("spinBox_15")
        self.gridLayout_4.addWidget(self.spinBox_15, 5, 9, 1, 1)
        self.spinBox_14 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_14.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_14.setObjectName("spinBox_14")
        self.gridLayout_4.addWidget(self.spinBox_14, 5, 10, 1, 1)
        self.pushButton_trap_start_5 = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_trap_start_5.setObjectName("pushButton_trap_start_5")
        self.gridLayout_4.addWidget(self.pushButton_trap_start_5, 5, 11, 1, 1)
        self.spinBox_trap_step_1 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_step_1.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_trap_step_1.setMinimum(-999999999)
        self.spinBox_trap_step_1.setMaximum(999999999)
        self.spinBox_trap_step_1.setObjectName("spinBox_trap_step_1")
        self.gridLayout_4.addWidget(self.spinBox_trap_step_1, 1, 3, 1, 1)
        self.spinBox_trap_step_2 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_step_2.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_trap_step_2.setMinimum(-999999999)
        self.spinBox_trap_step_2.setMaximum(999999999)
        self.spinBox_trap_step_2.setObjectName("spinBox_trap_step_2")
        self.gridLayout_4.addWidget(self.spinBox_trap_step_2, 2, 3, 1, 1)
        self.spinBox_trap_step_3 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_step_3.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_trap_step_3.setMinimum(-999999999)
        self.spinBox_trap_step_3.setMaximum(999999999)
        self.spinBox_trap_step_3.setObjectName("spinBox_trap_step_3")
        self.gridLayout_4.addWidget(self.spinBox_trap_step_3, 3, 3, 1, 1)
        self.spinBox_trap_step_4 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_step_4.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_trap_step_4.setMinimum(-999999999)
        self.spinBox_trap_step_4.setMaximum(999999999)
        self.spinBox_trap_step_4.setProperty("value", 0)
        self.spinBox_trap_step_4.setObjectName("spinBox_trap_step_4")
        self.gridLayout_4.addWidget(self.spinBox_trap_step_4, 4, 3, 1, 1)
        self.spinBox_trap_step_5 = QtWidgets.QSpinBox(self.groupBox_4)
        self.spinBox_trap_step_5.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_trap_step_5.setMinimum(-999999999)
        self.spinBox_trap_step_5.setMaximum(999999999)
        self.spinBox_trap_step_5.setObjectName("spinBox_trap_step_5")
        self.gridLayout_4.addWidget(self.spinBox_trap_step_5, 5, 3, 1, 1)
        self.verticalLayout_2.addWidget(self.groupBox_4)
        self.tabWidget.addTab(self.tab_2, "")
        self.tab_3 = QtWidgets.QWidget()
        self.tab_3.setObjectName("tab_3")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.tab_3)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.groupBox_2 = QtWidgets.QGroupBox(self.tab_3)
        self.groupBox_2.setTitle("")
        self.groupBox_2.setObjectName("groupBox_2")
        self.gridLayout = QtWidgets.QGridLayout(self.groupBox_2)
        self.gridLayout.setObjectName("gridLayout")
        self.label_4 = QtWidgets.QLabel(self.groupBox_2)
        self.label_4.setObjectName("label_4")
        self.gridLayout.addWidget(self.label_4, 0, 3, 1, 1)
        self.label_8 = QtWidgets.QLabel(self.groupBox_2)
        self.label_8.setObjectName("label_8")
        self.gridLayout.addWidget(self.label_8, 0, 7, 1, 1)
        self.label_10 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_10.sizePolicy().hasHeightForWidth())
        self.label_10.setSizePolicy(sizePolicy)
        self.label_10.setObjectName("label_10")
        self.gridLayout.addWidget(self.label_10, 2, 0, 1, 1)
        self.label_2 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_2.sizePolicy().hasHeightForWidth())
        self.label_2.setSizePolicy(sizePolicy)
        self.label_2.setObjectName("label_2")
        self.gridLayout.addWidget(self.label_2, 0, 1, 1, 1)
        self.label_6 = QtWidgets.QLabel(self.groupBox_2)
        self.label_6.setObjectName("label_6")
        self.gridLayout.addWidget(self.label_6, 0, 5, 1, 1)
        self.label_3 = QtWidgets.QLabel(self.groupBox_2)
        self.label_3.setObjectName("label_3")
        self.gridLayout.addWidget(self.label_3, 0, 2, 1, 1)
        self.label_11 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_11.sizePolicy().hasHeightForWidth())
        self.label_11.setSizePolicy(sizePolicy)
        self.label_11.setObjectName("label_11")
        self.gridLayout.addWidget(self.label_11, 3, 0, 1, 1)
        self.label_9 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_9.sizePolicy().hasHeightForWidth())
        self.label_9.setSizePolicy(sizePolicy)
        self.label_9.setObjectName("label_9")
        self.gridLayout.addWidget(self.label_9, 1, 0, 1, 1)
        self.label_12 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_12.sizePolicy().hasHeightForWidth())
        self.label_12.setSizePolicy(sizePolicy)
        self.label_12.setObjectName("label_12")
        self.gridLayout.addWidget(self.label_12, 4, 0, 1, 1)
        self.label = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setObjectName("label")
        self.gridLayout.addWidget(self.label, 0, 0, 1, 1)
        self.label_5 = QtWidgets.QLabel(self.groupBox_2)
        self.label_5.setObjectName("label_5")
        self.gridLayout.addWidget(self.label_5, 0, 4, 1, 1)
        self.label_7 = QtWidgets.QLabel(self.groupBox_2)
        self.label_7.setObjectName("label_7")
        self.gridLayout.addWidget(self.label_7, 0, 6, 1, 1)
        self.label_13 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_13.sizePolicy().hasHeightForWidth())
        self.label_13.setSizePolicy(sizePolicy)
        self.label_13.setObjectName("label_13")
        self.gridLayout.addWidget(self.label_13, 5, 0, 1, 1)
        self.label_14 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_14.sizePolicy().hasHeightForWidth())
        self.label_14.setSizePolicy(sizePolicy)
        self.label_14.setObjectName("label_14")
        self.gridLayout.addWidget(self.label_14, 1, 1, 1, 1)
        self.label_15 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_15.sizePolicy().hasHeightForWidth())
        self.label_15.setSizePolicy(sizePolicy)
        self.label_15.setObjectName("label_15")
        self.gridLayout.addWidget(self.label_15, 2, 1, 1, 1)
        self.label_16 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_16.sizePolicy().hasHeightForWidth())
        self.label_16.setSizePolicy(sizePolicy)
        self.label_16.setObjectName("label_16")
        self.gridLayout.addWidget(self.label_16, 3, 1, 1, 1)
        self.label_17 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_17.sizePolicy().hasHeightForWidth())
        self.label_17.setSizePolicy(sizePolicy)
        self.label_17.setObjectName("label_17")
        self.gridLayout.addWidget(self.label_17, 4, 1, 1, 1)
        self.label_18 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_18.sizePolicy().hasHeightForWidth())
        self.label_18.setSizePolicy(sizePolicy)
        self.label_18.setObjectName("label_18")
        self.gridLayout.addWidget(self.label_18, 5, 1, 1, 1)
        self.doubleSpinBox_j_vel_1 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_vel_1.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_vel_1.setDecimals(3)
        self.doubleSpinBox_j_vel_1.setMaximum(1000.0)
        self.doubleSpinBox_j_vel_1.setProperty("value", 100.0)
        self.doubleSpinBox_j_vel_1.setObjectName("doubleSpinBox_j_vel_1")
        self.gridLayout.addWidget(self.doubleSpinBox_j_vel_1, 1, 2, 1, 1)
        self.doubleSpinBox_j_vel_2 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_vel_2.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_vel_2.setDecimals(3)
        self.doubleSpinBox_j_vel_2.setMaximum(1000.0)
        self.doubleSpinBox_j_vel_2.setProperty("value", 100.0)
        self.doubleSpinBox_j_vel_2.setObjectName("doubleSpinBox_j_vel_2")
        self.gridLayout.addWidget(self.doubleSpinBox_j_vel_2, 2, 2, 1, 1)
        self.doubleSpinBox_j_vel_3 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_vel_3.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_vel_3.setDecimals(3)
        self.doubleSpinBox_j_vel_3.setMaximum(1000.0)
        self.doubleSpinBox_j_vel_3.setProperty("value", 100.0)
        self.doubleSpinBox_j_vel_3.setObjectName("doubleSpinBox_j_vel_3")
        self.gridLayout.addWidget(self.doubleSpinBox_j_vel_3, 3, 2, 1, 1)
        self.doubleSpinBox_j_vel_4 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_vel_4.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_vel_4.setDecimals(3)
        self.doubleSpinBox_j_vel_4.setMaximum(1000.0)
        self.doubleSpinBox_j_vel_4.setProperty("value", 100.0)
        self.doubleSpinBox_j_vel_4.setObjectName("doubleSpinBox_j_vel_4")
        self.gridLayout.addWidget(self.doubleSpinBox_j_vel_4, 4, 2, 1, 1)
        self.doubleSpinBox_j_vel_5 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_vel_5.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_vel_5.setDecimals(3)
        self.doubleSpinBox_j_vel_5.setMaximum(100.0)
        self.doubleSpinBox_j_vel_5.setProperty("value", 2.0)
        self.doubleSpinBox_j_vel_5.setObjectName("doubleSpinBox_j_vel_5")
        self.gridLayout.addWidget(self.doubleSpinBox_j_vel_5, 5, 2, 1, 1)
        self.doubleSpinBox_j_acc_1 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_acc_1.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_acc_1.setDecimals(3)
        self.doubleSpinBox_j_acc_1.setProperty("value", 0.06)
        self.doubleSpinBox_j_acc_1.setObjectName("doubleSpinBox_j_acc_1")
        self.gridLayout.addWidget(self.doubleSpinBox_j_acc_1, 1, 3, 1, 1)
        self.doubleSpinBox_j_acc_2 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_acc_2.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_acc_2.setDecimals(3)
        self.doubleSpinBox_j_acc_2.setProperty("value", 0.06)
        self.doubleSpinBox_j_acc_2.setObjectName("doubleSpinBox_j_acc_2")
        self.gridLayout.addWidget(self.doubleSpinBox_j_acc_2, 2, 3, 1, 1)
        self.doubleSpinBox_j_acc_3 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_acc_3.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_acc_3.setDecimals(3)
        self.doubleSpinBox_j_acc_3.setProperty("value", 0.06)
        self.doubleSpinBox_j_acc_3.setObjectName("doubleSpinBox_j_acc_3")
        self.gridLayout.addWidget(self.doubleSpinBox_j_acc_3, 3, 3, 1, 1)
        self.doubleSpinBox_j_acc_4 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_acc_4.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_acc_4.setDecimals(3)
        self.doubleSpinBox_j_acc_4.setProperty("value", 0.06)
        self.doubleSpinBox_j_acc_4.setObjectName("doubleSpinBox_j_acc_4")
        self.gridLayout.addWidget(self.doubleSpinBox_j_acc_4, 4, 3, 1, 1)
        self.doubleSpinBox_j_acc_5 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_acc_5.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_acc_5.setDecimals(3)
        self.doubleSpinBox_j_acc_5.setProperty("value", 0.06)
        self.doubleSpinBox_j_acc_5.setObjectName("doubleSpinBox_j_acc_5")
        self.gridLayout.addWidget(self.doubleSpinBox_j_acc_5, 5, 3, 1, 1)
        self.doubleSpinBox_j_dec_1 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_dec_1.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_dec_1.setDecimals(3)
        self.doubleSpinBox_j_dec_1.setProperty("value", 0.06)
        self.doubleSpinBox_j_dec_1.setObjectName("doubleSpinBox_j_dec_1")
        self.gridLayout.addWidget(self.doubleSpinBox_j_dec_1, 1, 4, 1, 1)
        self.doubleSpinBox_j_smh_1 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_smh_1.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_smh_1.setDecimals(3)
        self.doubleSpinBox_j_smh_1.setMinimum(0.0)
        self.doubleSpinBox_j_smh_1.setMaximum(0.999)
        self.doubleSpinBox_j_smh_1.setSingleStep(0.01)
        self.doubleSpinBox_j_smh_1.setProperty("value", 0.5)
        self.doubleSpinBox_j_smh_1.setObjectName("doubleSpinBox_j_smh_1")
        self.gridLayout.addWidget(self.doubleSpinBox_j_smh_1, 1, 5, 1, 1)
        self.doubleSpinBox_j_dec_2 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_dec_2.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_dec_2.setDecimals(3)
        self.doubleSpinBox_j_dec_2.setProperty("value", 0.06)
        self.doubleSpinBox_j_dec_2.setObjectName("doubleSpinBox_j_dec_2")
        self.gridLayout.addWidget(self.doubleSpinBox_j_dec_2, 2, 4, 1, 1)
        self.doubleSpinBox_j_dec_3 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_dec_3.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_dec_3.setDecimals(3)
        self.doubleSpinBox_j_dec_3.setProperty("value", 0.06)
        self.doubleSpinBox_j_dec_3.setObjectName("doubleSpinBox_j_dec_3")
        self.gridLayout.addWidget(self.doubleSpinBox_j_dec_3, 3, 4, 1, 1)
        self.doubleSpinBox_j_dec_4 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_dec_4.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_dec_4.setDecimals(3)
        self.doubleSpinBox_j_dec_4.setProperty("value", 0.06)
        self.doubleSpinBox_j_dec_4.setObjectName("doubleSpinBox_j_dec_4")
        self.gridLayout.addWidget(self.doubleSpinBox_j_dec_4, 4, 4, 1, 1)
        self.doubleSpinBox_j_dec_5 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_dec_5.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_dec_5.setDecimals(3)
        self.doubleSpinBox_j_dec_5.setProperty("value", 0.06)
        self.doubleSpinBox_j_dec_5.setObjectName("doubleSpinBox_j_dec_5")
        self.gridLayout.addWidget(self.doubleSpinBox_j_dec_5, 5, 4, 1, 1)
        self.doubleSpinBox_j_smh_2 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_smh_2.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_smh_2.setDecimals(3)
        self.doubleSpinBox_j_smh_2.setMaximum(0.999)
        self.doubleSpinBox_j_smh_2.setSingleStep(0.01)
        self.doubleSpinBox_j_smh_2.setProperty("value", 0.5)
        self.doubleSpinBox_j_smh_2.setObjectName("doubleSpinBox_j_smh_2")
        self.gridLayout.addWidget(self.doubleSpinBox_j_smh_2, 2, 5, 1, 1)
        self.doubleSpinBox_j_smh_3 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_smh_3.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_smh_3.setDecimals(3)
        self.doubleSpinBox_j_smh_3.setMaximum(0.999)
        self.doubleSpinBox_j_smh_3.setSingleStep(0.01)
        self.doubleSpinBox_j_smh_3.setProperty("value", 0.5)
        self.doubleSpinBox_j_smh_3.setObjectName("doubleSpinBox_j_smh_3")
        self.gridLayout.addWidget(self.doubleSpinBox_j_smh_3, 3, 5, 1, 1)
        self.doubleSpinBox_j_smh_4 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_smh_4.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_smh_4.setDecimals(3)
        self.doubleSpinBox_j_smh_4.setMaximum(0.999)
        self.doubleSpinBox_j_smh_4.setSingleStep(0.01)
        self.doubleSpinBox_j_smh_4.setProperty("value", 0.5)
        self.doubleSpinBox_j_smh_4.setObjectName("doubleSpinBox_j_smh_4")
        self.gridLayout.addWidget(self.doubleSpinBox_j_smh_4, 4, 5, 1, 1)
        self.doubleSpinBox_j_smh_5 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_smh_5.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_j_smh_5.setDecimals(3)
        self.doubleSpinBox_j_smh_5.setMaximum(0.999)
        self.doubleSpinBox_j_smh_5.setSingleStep(0.01)
        self.doubleSpinBox_j_smh_5.setProperty("value", 0.5)
        self.doubleSpinBox_j_smh_5.setObjectName("doubleSpinBox_j_smh_5")
        self.gridLayout.addWidget(self.doubleSpinBox_j_smh_5, 5, 5, 1, 1)
        self.pushButton_f_move_1 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_f_move_1.setObjectName("pushButton_f_move_1")
        self.gridLayout.addWidget(self.pushButton_f_move_1, 1, 6, 1, 1)
        self.pushButton_f_move_2 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_f_move_2.setObjectName("pushButton_f_move_2")
        self.gridLayout.addWidget(self.pushButton_f_move_2, 2, 6, 1, 1)
        self.pushButton_f_move_3 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_f_move_3.setObjectName("pushButton_f_move_3")
        self.gridLayout.addWidget(self.pushButton_f_move_3, 3, 6, 1, 1)
        self.pushButton_f_move_4 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_f_move_4.setObjectName("pushButton_f_move_4")
        self.gridLayout.addWidget(self.pushButton_f_move_4, 4, 6, 1, 1)
        self.pushButton_f_move_5 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_f_move_5.setObjectName("pushButton_f_move_5")
        self.gridLayout.addWidget(self.pushButton_f_move_5, 5, 6, 1, 1)
        self.pushButton_c_move_1 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_c_move_1.setObjectName("pushButton_c_move_1")
        self.gridLayout.addWidget(self.pushButton_c_move_1, 1, 7, 1, 1)
        self.pushButton_c_move_2 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_c_move_2.setObjectName("pushButton_c_move_2")
        self.gridLayout.addWidget(self.pushButton_c_move_2, 2, 7, 1, 1)
        self.pushButton_c_move_3 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_c_move_3.setObjectName("pushButton_c_move_3")
        self.gridLayout.addWidget(self.pushButton_c_move_3, 3, 7, 1, 1)
        self.pushButton_c_move_4 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_c_move_4.setObjectName("pushButton_c_move_4")
        self.gridLayout.addWidget(self.pushButton_c_move_4, 4, 7, 1, 1)
        self.pushButton_c_move_5 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_c_move_5.setObjectName("pushButton_c_move_5")
        self.gridLayout.addWidget(self.pushButton_c_move_5, 5, 7, 1, 1)
        self.verticalLayout_3.addWidget(self.groupBox_2)
        self.tabWidget.addTab(self.tab_3, "")
        self.tab_4 = QtWidgets.QWidget()
        self.tab_4.setObjectName("tab_4")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.tab_4)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.groupBox_5 = QtWidgets.QGroupBox(self.tab_4)
        self.groupBox_5.setObjectName("groupBox_5")
        self.gridLayout_5 = QtWidgets.QGridLayout(self.groupBox_5)
        self.gridLayout_5.setObjectName("gridLayout_5")
        self.label_93 = QtWidgets.QLabel(self.groupBox_5)
        self.label_93.setObjectName("label_93")
        self.gridLayout_5.addWidget(self.label_93, 0, 0, 1, 1)
        self.label_in_start_s = QtWidgets.QLabel(self.groupBox_5)
        self.label_in_start_s.setObjectName("label_in_start_s")
        self.gridLayout_5.addWidget(self.label_in_start_s, 0, 1, 1, 1)
        self.label_119 = QtWidgets.QLabel(self.groupBox_5)
        self.label_119.setObjectName("label_119")
        self.gridLayout_5.addWidget(self.label_119, 0, 2, 1, 1)
        self.label_in_figer3_s = QtWidgets.QLabel(self.groupBox_5)
        self.label_in_figer3_s.setObjectName("label_in_figer3_s")
        self.gridLayout_5.addWidget(self.label_in_figer3_s, 0, 3, 1, 1)
        self.label_94 = QtWidgets.QLabel(self.groupBox_5)
        self.label_94.setObjectName("label_94")
        self.gridLayout_5.addWidget(self.label_94, 1, 0, 1, 1)
        self.label_in_pause_s = QtWidgets.QLabel(self.groupBox_5)
        self.label_in_pause_s.setObjectName("label_in_pause_s")
        self.gridLayout_5.addWidget(self.label_in_pause_s, 1, 1, 1, 1)
        self.label_115 = QtWidgets.QLabel(self.groupBox_5)
        self.label_115.setObjectName("label_115")
        self.gridLayout_5.addWidget(self.label_115, 1, 2, 1, 1)
        self.label_in_figer4_s = QtWidgets.QLabel(self.groupBox_5)
        self.label_in_figer4_s.setObjectName("label_in_figer4_s")
        self.gridLayout_5.addWidget(self.label_in_figer4_s, 1, 3, 1, 1)
        self.label_95 = QtWidgets.QLabel(self.groupBox_5)
        self.label_95.setObjectName("label_95")
        self.gridLayout_5.addWidget(self.label_95, 2, 0, 1, 1)
        self.label_in_reset_s = QtWidgets.QLabel(self.groupBox_5)
        self.label_in_reset_s.setObjectName("label_in_reset_s")
        self.gridLayout_5.addWidget(self.label_in_reset_s, 2, 1, 1, 1)
        self.label_120 = QtWidgets.QLabel(self.groupBox_5)
        self.label_120.setObjectName("label_120")
        self.gridLayout_5.addWidget(self.label_120, 2, 2, 1, 1)
        self.label_in_figer5_s = QtWidgets.QLabel(self.groupBox_5)
        self.label_in_figer5_s.setObjectName("label_in_figer5_s")
        self.gridLayout_5.addWidget(self.label_in_figer5_s, 2, 3, 1, 1)
        self.label_96 = QtWidgets.QLabel(self.groupBox_5)
        self.label_96.setObjectName("label_96")
        self.gridLayout_5.addWidget(self.label_96, 3, 0, 1, 1)
        self.label_in_e_stop_s = QtWidgets.QLabel(self.groupBox_5)
        self.label_in_e_stop_s.setObjectName("label_in_e_stop_s")
        self.gridLayout_5.addWidget(self.label_in_e_stop_s, 3, 1, 1, 1)
        self.label_121 = QtWidgets.QLabel(self.groupBox_5)
        self.label_121.setObjectName("label_121")
        self.gridLayout_5.addWidget(self.label_121, 3, 2, 1, 1)
        self.label_in_figer6_s = QtWidgets.QLabel(self.groupBox_5)
        self.label_in_figer6_s.setObjectName("label_in_figer6_s")
        self.gridLayout_5.addWidget(self.label_in_figer6_s, 3, 3, 1, 1)
        self.label_97 = QtWidgets.QLabel(self.groupBox_5)
        self.label_97.setObjectName("label_97")
        self.gridLayout_5.addWidget(self.label_97, 4, 0, 1, 1)
        self.label_in_vacuum1_s = QtWidgets.QLabel(self.groupBox_5)
        self.label_in_vacuum1_s.setObjectName("label_in_vacuum1_s")
        self.gridLayout_5.addWidget(self.label_in_vacuum1_s, 4, 1, 1, 1)
        self.label_124 = QtWidgets.QLabel(self.groupBox_5)
        self.label_124.setObjectName("label_124")
        self.gridLayout_5.addWidget(self.label_124, 4, 2, 1, 1)
        self.label_in_figer7_s = QtWidgets.QLabel(self.groupBox_5)
        self.label_in_figer7_s.setObjectName("label_in_figer7_s")
        self.gridLayout_5.addWidget(self.label_in_figer7_s, 4, 3, 1, 1)
        self.label_98 = QtWidgets.QLabel(self.groupBox_5)
        self.label_98.setObjectName("label_98")
        self.gridLayout_5.addWidget(self.label_98, 5, 0, 1, 1)
        self.label_in_a_pressure_s = QtWidgets.QLabel(self.groupBox_5)
        self.label_in_a_pressure_s.setObjectName("label_in_a_pressure_s")
        self.gridLayout_5.addWidget(self.label_in_a_pressure_s, 5, 1, 1, 1)
        self.label_114 = QtWidgets.QLabel(self.groupBox_5)
        self.label_114.setObjectName("label_114")
        self.gridLayout_5.addWidget(self.label_114, 5, 2, 1, 1)
        self.label_in_figer8_s = QtWidgets.QLabel(self.groupBox_5)
        self.label_in_figer8_s.setObjectName("label_in_figer8_s")
        self.gridLayout_5.addWidget(self.label_in_figer8_s, 5, 3, 1, 1)
        self.label_99 = QtWidgets.QLabel(self.groupBox_5)
        self.label_99.setObjectName("label_99")
        self.gridLayout_5.addWidget(self.label_99, 6, 0, 1, 1)
        self.label_in_figer1_s = QtWidgets.QLabel(self.groupBox_5)
        self.label_in_figer1_s.setObjectName("label_in_figer1_s")
        self.gridLayout_5.addWidget(self.label_in_figer1_s, 6, 1, 1, 1)
        self.label_123 = QtWidgets.QLabel(self.groupBox_5)
        self.label_123.setObjectName("label_123")
        self.gridLayout_5.addWidget(self.label_123, 6, 2, 1, 1)
        self.label_in_figer9_s = QtWidgets.QLabel(self.groupBox_5)
        self.label_in_figer9_s.setObjectName("label_in_figer9_s")
        self.gridLayout_5.addWidget(self.label_in_figer9_s, 6, 3, 1, 1)
        self.label_100 = QtWidgets.QLabel(self.groupBox_5)
        self.label_100.setObjectName("label_100")
        self.gridLayout_5.addWidget(self.label_100, 7, 0, 1, 1)
        self.label_in_figer2_s = QtWidgets.QLabel(self.groupBox_5)
        self.label_in_figer2_s.setObjectName("label_in_figer2_s")
        self.gridLayout_5.addWidget(self.label_in_figer2_s, 7, 1, 1, 1)
        self.label_118 = QtWidgets.QLabel(self.groupBox_5)
        self.label_118.setObjectName("label_118")
        self.gridLayout_5.addWidget(self.label_118, 7, 2, 1, 1)
        self.label_in_vacuum2_s = QtWidgets.QLabel(self.groupBox_5)
        self.label_in_vacuum2_s.setObjectName("label_in_vacuum2_s")
        self.gridLayout_5.addWidget(self.label_in_vacuum2_s, 7, 3, 1, 1)
        self.horizontalLayout.addWidget(self.groupBox_5)
        self.groupBox_6 = QtWidgets.QGroupBox(self.tab_4)
        self.groupBox_6.setObjectName("groupBox_6")
        self.gridLayout_6 = QtWidgets.QGridLayout(self.groupBox_6)
        self.gridLayout_6.setObjectName("gridLayout_6")
        self.label_out_vacuum1_o_s = QtWidgets.QLabel(self.groupBox_6)
        self.label_out_vacuum1_o_s.setObjectName("label_out_vacuum1_o_s")
        self.gridLayout_6.addWidget(self.label_out_vacuum1_o_s, 5, 1, 1, 1)
        self.label_out_figer6_s = QtWidgets.QLabel(self.groupBox_6)
        self.label_out_figer6_s.setObjectName("label_out_figer6_s")
        self.gridLayout_6.addWidget(self.label_out_figer6_s, 2, 4, 1, 1)
        self.pushButton_out_figer8_s = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_out_figer8_s.setObjectName("pushButton_out_figer8_s")
        self.gridLayout_6.addWidget(self.pushButton_out_figer8_s, 4, 5, 1, 1)
        self.pushButton_out_figer6_s = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_out_figer6_s.setObjectName("pushButton_out_figer6_s")
        self.gridLayout_6.addWidget(self.pushButton_out_figer6_s, 2, 5, 1, 1)
        self.pushButton_out_buzzer_s = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_out_buzzer_s.setObjectName("pushButton_out_buzzer_s")
        self.gridLayout_6.addWidget(self.pushButton_out_buzzer_s, 0, 5, 1, 1)
        self.label_153 = QtWidgets.QLabel(self.groupBox_6)
        self.label_153.setObjectName("label_153")
        self.gridLayout_6.addWidget(self.label_153, 7, 0, 1, 1)
        self.pushButton_out_figer1_s = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_out_figer1_s.setObjectName("pushButton_out_figer1_s")
        self.gridLayout_6.addWidget(self.pushButton_out_figer1_s, 0, 2, 1, 1)
        self.label_143 = QtWidgets.QLabel(self.groupBox_6)
        self.label_143.setObjectName("label_143")
        self.gridLayout_6.addWidget(self.label_143, 4, 3, 1, 1)
        self.label_out_vacuum2_i_s = QtWidgets.QLabel(self.groupBox_6)
        self.label_out_vacuum2_i_s.setObjectName("label_out_vacuum2_i_s")
        self.gridLayout_6.addWidget(self.label_out_vacuum2_i_s, 6, 1, 1, 1)
        self.label_out_pause_light_s = QtWidgets.QLabel(self.groupBox_6)
        self.label_out_pause_light_s.setObjectName("label_out_pause_light_s")
        self.gridLayout_6.addWidget(self.label_out_pause_light_s, 6, 4, 1, 1)
        self.label_out_figer4_s = QtWidgets.QLabel(self.groupBox_6)
        self.label_out_figer4_s.setObjectName("label_out_figer4_s")
        self.gridLayout_6.addWidget(self.label_out_figer4_s, 3, 1, 1, 1)
        self.label_out_figer2_s = QtWidgets.QLabel(self.groupBox_6)
        self.label_out_figer2_s.setObjectName("label_out_figer2_s")
        self.gridLayout_6.addWidget(self.label_out_figer2_s, 1, 1, 1, 1)
        self.pushButton_out_figer7_s = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_out_figer7_s.setObjectName("pushButton_out_figer7_s")
        self.gridLayout_6.addWidget(self.pushButton_out_figer7_s, 3, 5, 1, 1)
        self.label_137 = QtWidgets.QLabel(self.groupBox_6)
        self.label_137.setObjectName("label_137")
        self.gridLayout_6.addWidget(self.label_137, 3, 0, 1, 1)
        self.label_151 = QtWidgets.QLabel(self.groupBox_6)
        self.label_151.setObjectName("label_151")
        self.gridLayout_6.addWidget(self.label_151, 6, 3, 1, 1)
        self.label_out_vacuum1_i_s = QtWidgets.QLabel(self.groupBox_6)
        self.label_out_vacuum1_i_s.setObjectName("label_out_vacuum1_i_s")
        self.gridLayout_6.addWidget(self.label_out_vacuum1_i_s, 4, 1, 1, 1)
        self.label_139 = QtWidgets.QLabel(self.groupBox_6)
        self.label_139.setObjectName("label_139")
        self.gridLayout_6.addWidget(self.label_139, 3, 3, 1, 1)
        self.label_out_buzzer_s = QtWidgets.QLabel(self.groupBox_6)
        self.label_out_buzzer_s.setObjectName("label_out_buzzer_s")
        self.gridLayout_6.addWidget(self.label_out_buzzer_s, 0, 4, 1, 1)
        self.label_145 = QtWidgets.QLabel(self.groupBox_6)
        self.label_145.setObjectName("label_145")
        self.gridLayout_6.addWidget(self.label_145, 5, 0, 1, 1)
        self.pushButton_out_vacuum2_o_s = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_out_vacuum2_o_s.setObjectName("pushButton_out_vacuum2_o_s")
        self.gridLayout_6.addWidget(self.pushButton_out_vacuum2_o_s, 7, 5, 1, 1)
        self.label_out_figer1_s = QtWidgets.QLabel(self.groupBox_6)
        self.label_out_figer1_s.setObjectName("label_out_figer1_s")
        self.gridLayout_6.addWidget(self.label_out_figer1_s, 0, 1, 1, 1)
        self.pushButton_out_vacuum1_i_s = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_out_vacuum1_i_s.setObjectName("pushButton_out_vacuum1_i_s")
        self.gridLayout_6.addWidget(self.pushButton_out_vacuum1_i_s, 4, 2, 1, 1)
        self.label_147 = QtWidgets.QLabel(self.groupBox_6)
        self.label_147.setObjectName("label_147")
        self.gridLayout_6.addWidget(self.label_147, 5, 3, 1, 1)
        self.label_out_red_s = QtWidgets.QLabel(self.groupBox_6)
        self.label_out_red_s.setObjectName("label_out_red_s")
        self.gridLayout_6.addWidget(self.label_out_red_s, 7, 1, 1, 1)
        self.label_135 = QtWidgets.QLabel(self.groupBox_6)
        self.label_135.setObjectName("label_135")
        self.gridLayout_6.addWidget(self.label_135, 2, 3, 1, 1)
        self.label_out_figer7_s = QtWidgets.QLabel(self.groupBox_6)
        self.label_out_figer7_s.setObjectName("label_out_figer7_s")
        self.gridLayout_6.addWidget(self.label_out_figer7_s, 3, 4, 1, 1)
        self.pushButton_out_figer9_s = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_out_figer9_s.setObjectName("pushButton_out_figer9_s")
        self.gridLayout_6.addWidget(self.pushButton_out_figer9_s, 5, 5, 1, 1)
        self.pushButton_out_figer5_s = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_out_figer5_s.setObjectName("pushButton_out_figer5_s")
        self.gridLayout_6.addWidget(self.pushButton_out_figer5_s, 1, 5, 1, 1)
        self.pushButton_out_vacuum2_i_s = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_out_vacuum2_i_s.setObjectName("pushButton_out_vacuum2_i_s")
        self.gridLayout_6.addWidget(self.pushButton_out_vacuum2_i_s, 6, 2, 1, 1)
        self.label_out_figer9_s = QtWidgets.QLabel(self.groupBox_6)
        self.label_out_figer9_s.setObjectName("label_out_figer9_s")
        self.gridLayout_6.addWidget(self.label_out_figer9_s, 5, 4, 1, 1)
        self.label_out_figer8_s = QtWidgets.QLabel(self.groupBox_6)
        self.label_out_figer8_s.setObjectName("label_out_figer8_s")
        self.gridLayout_6.addWidget(self.label_out_figer8_s, 4, 4, 1, 1)
        self.label_131 = QtWidgets.QLabel(self.groupBox_6)
        self.label_131.setObjectName("label_131")
        self.gridLayout_6.addWidget(self.label_131, 1, 3, 1, 1)
        self.label_127 = QtWidgets.QLabel(self.groupBox_6)
        self.label_127.setObjectName("label_127")
        self.gridLayout_6.addWidget(self.label_127, 0, 3, 1, 1)
        self.pushButton_out_vacuum1_o_s = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_out_vacuum1_o_s.setObjectName("pushButton_out_vacuum1_o_s")
        self.gridLayout_6.addWidget(self.pushButton_out_vacuum1_o_s, 5, 2, 1, 1)
        self.label_out_vacuum2_o_s = QtWidgets.QLabel(self.groupBox_6)
        self.label_out_vacuum2_o_s.setObjectName("label_out_vacuum2_o_s")
        self.gridLayout_6.addWidget(self.label_out_vacuum2_o_s, 7, 4, 1, 1)
        self.pushButton_out_figer4_s = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_out_figer4_s.setObjectName("pushButton_out_figer4_s")
        self.gridLayout_6.addWidget(self.pushButton_out_figer4_s, 3, 2, 1, 1)
        self.label_out_figer5_s = QtWidgets.QLabel(self.groupBox_6)
        self.label_out_figer5_s.setObjectName("label_out_figer5_s")
        self.gridLayout_6.addWidget(self.label_out_figer5_s, 1, 4, 1, 1)
        self.pushButton_out_figer3_s = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_out_figer3_s.setObjectName("pushButton_out_figer3_s")
        self.gridLayout_6.addWidget(self.pushButton_out_figer3_s, 2, 2, 1, 1)
        self.pushButton_out_pause_light_s = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_out_pause_light_s.setObjectName("pushButton_out_pause_light_s")
        self.gridLayout_6.addWidget(self.pushButton_out_pause_light_s, 6, 5, 1, 1)
        self.label_133 = QtWidgets.QLabel(self.groupBox_6)
        self.label_133.setObjectName("label_133")
        self.gridLayout_6.addWidget(self.label_133, 2, 0, 1, 1)
        self.label_149 = QtWidgets.QLabel(self.groupBox_6)
        self.label_149.setObjectName("label_149")
        self.gridLayout_6.addWidget(self.label_149, 6, 0, 1, 1)
        self.label_141 = QtWidgets.QLabel(self.groupBox_6)
        self.label_141.setObjectName("label_141")
        self.gridLayout_6.addWidget(self.label_141, 4, 0, 1, 1)
        self.label_129 = QtWidgets.QLabel(self.groupBox_6)
        self.label_129.setObjectName("label_129")
        self.gridLayout_6.addWidget(self.label_129, 1, 0, 1, 1)
        self.pushButton_out_red_s = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_out_red_s.setObjectName("pushButton_out_red_s")
        self.gridLayout_6.addWidget(self.pushButton_out_red_s, 7, 2, 1, 1)
        self.label_out_figer3_s = QtWidgets.QLabel(self.groupBox_6)
        self.label_out_figer3_s.setObjectName("label_out_figer3_s")
        self.gridLayout_6.addWidget(self.label_out_figer3_s, 2, 1, 1, 1)
        self.pushButton_out_figer2_s = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_out_figer2_s.setObjectName("pushButton_out_figer2_s")
        self.gridLayout_6.addWidget(self.pushButton_out_figer2_s, 1, 2, 1, 1)
        self.label_155 = QtWidgets.QLabel(self.groupBox_6)
        self.label_155.setObjectName("label_155")
        self.gridLayout_6.addWidget(self.label_155, 7, 3, 1, 1)
        self.label_125 = QtWidgets.QLabel(self.groupBox_6)
        self.label_125.setObjectName("label_125")
        self.gridLayout_6.addWidget(self.label_125, 0, 0, 1, 1)
        self.label_101 = QtWidgets.QLabel(self.groupBox_6)
        self.label_101.setObjectName("label_101")
        self.gridLayout_6.addWidget(self.label_101, 8, 0, 1, 1)
        self.label_102 = QtWidgets.QLabel(self.groupBox_6)
        self.label_102.setObjectName("label_102")
        self.gridLayout_6.addWidget(self.label_102, 8, 3, 1, 1)
        self.label_out_green_s = QtWidgets.QLabel(self.groupBox_6)
        self.label_out_green_s.setObjectName("label_out_green_s")
        self.gridLayout_6.addWidget(self.label_out_green_s, 8, 1, 1, 1)
        self.label_out_yellow_s = QtWidgets.QLabel(self.groupBox_6)
        self.label_out_yellow_s.setObjectName("label_out_yellow_s")
        self.gridLayout_6.addWidget(self.label_out_yellow_s, 8, 4, 1, 1)
        self.pushButton_out_green_s = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_out_green_s.setObjectName("pushButton_out_green_s")
        self.gridLayout_6.addWidget(self.pushButton_out_green_s, 8, 2, 1, 1)
        self.pushButton_out_yellow_s = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_out_yellow_s.setObjectName("pushButton_out_yellow_s")
        self.gridLayout_6.addWidget(self.pushButton_out_yellow_s, 8, 5, 1, 1)
        self.horizontalLayout.addWidget(self.groupBox_6)
        self.tabWidget.addTab(self.tab_4, "")
        self.verticalLayout_4.addWidget(self.tabWidget)
        self.groupBox = QtWidgets.QGroupBox(Form)
        self.groupBox.setObjectName("groupBox")
        self.gridLayout_2 = QtWidgets.QGridLayout(self.groupBox)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.label_28 = QtWidgets.QLabel(self.groupBox)
        self.label_28.setAlignment(QtCore.Qt.AlignCenter)
        self.label_28.setObjectName("label_28")
        self.gridLayout_2.addWidget(self.label_28, 1, 13, 1, 1)
        self.lineEdit_prf_pos_1 = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_prf_pos_1.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_prf_pos_1.setObjectName("lineEdit_prf_pos_1")
        self.gridLayout_2.addWidget(self.lineEdit_prf_pos_1, 3, 5, 1, 1)
        self.label_82 = QtWidgets.QLabel(self.groupBox)
        self.label_82.setAlignment(QtCore.Qt.AlignCenter)
        self.label_82.setObjectName("label_82")
        self.gridLayout_2.addWidget(self.label_82, 4, 6, 1, 1)
        self.label_19 = QtWidgets.QLabel(self.groupBox)
        self.label_19.setObjectName("label_19")
        self.gridLayout_2.addWidget(self.label_19, 0, 0, 2, 1)
        self.label_38 = QtWidgets.QLabel(self.groupBox)
        self.label_38.setObjectName("label_38")
        self.gridLayout_2.addWidget(self.label_38, 6, 1, 1, 1)
        self.lineEdit_enc_pos_5 = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_enc_pos_5.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_enc_pos_5.setObjectName("lineEdit_enc_pos_5")
        self.gridLayout_2.addWidget(self.lineEdit_enc_pos_5, 7, 4, 1, 1)
        self.label_23 = QtWidgets.QLabel(self.groupBox)
        self.label_23.setObjectName("label_23")
        self.gridLayout_2.addWidget(self.label_23, 0, 6, 2, 1)
        self.pushButton_axis_clr_sts_1 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_clr_sts_1.setObjectName("pushButton_axis_clr_sts_1")
        self.gridLayout_2.addWidget(self.pushButton_axis_clr_sts_1, 3, 12, 1, 1)
        self.label_42 = QtWidgets.QLabel(self.groupBox)
        self.label_42.setText("")
        self.label_42.setObjectName("label_42")
        self.gridLayout_2.addWidget(self.label_42, 1, 10, 1, 1)
        self.pushButton_axis_clr_sts_4 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_clr_sts_4.setObjectName("pushButton_axis_clr_sts_4")
        self.gridLayout_2.addWidget(self.pushButton_axis_clr_sts_4, 6, 12, 1, 1)
        self.lineEdit_e_enc_pos_1 = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_e_enc_pos_1.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_e_enc_pos_1.setObjectName("lineEdit_e_enc_pos_1")
        self.gridLayout_2.addWidget(self.lineEdit_e_enc_pos_1, 3, 3, 1, 1)
        self.label_37 = QtWidgets.QLabel(self.groupBox)
        self.label_37.setObjectName("label_37")
        self.gridLayout_2.addWidget(self.label_37, 5, 1, 1, 1)
        self.label_24 = QtWidgets.QLabel(self.groupBox)
        self.label_24.setObjectName("label_24")
        self.gridLayout_2.addWidget(self.label_24, 0, 7, 2, 1)
        self.pushButton_axis_clr_sts_3 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_clr_sts_3.setObjectName("pushButton_axis_clr_sts_3")
        self.gridLayout_2.addWidget(self.pushButton_axis_clr_sts_3, 5, 12, 1, 1)
        self.label_21 = QtWidgets.QLabel(self.groupBox)
        self.label_21.setObjectName("label_21")
        self.gridLayout_2.addWidget(self.label_21, 0, 4, 2, 1)
        self.label_29 = QtWidgets.QLabel(self.groupBox)
        self.label_29.setAlignment(QtCore.Qt.AlignCenter)
        self.label_29.setObjectName("label_29")
        self.gridLayout_2.addWidget(self.label_29, 1, 14, 1, 1)
        self.pushButton_axis_zero_pos_4 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_zero_pos_4.setObjectName("pushButton_axis_zero_pos_4")
        self.gridLayout_2.addWidget(self.pushButton_axis_zero_pos_4, 6, 11, 1, 1)
        self.label_91 = QtWidgets.QLabel(self.groupBox)
        self.label_91.setAlignment(QtCore.Qt.AlignCenter)
        self.label_91.setObjectName("label_91")
        self.gridLayout_2.addWidget(self.label_91, 7, 6, 1, 1)
        self.pushButton_axis_zero_pos_3 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_zero_pos_3.setObjectName("pushButton_axis_zero_pos_3")
        self.gridLayout_2.addWidget(self.pushButton_axis_zero_pos_3, 5, 11, 1, 1)
        self.pushButton_axis_zero_pos_1 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_zero_pos_1.setObjectName("pushButton_axis_zero_pos_1")
        self.gridLayout_2.addWidget(self.pushButton_axis_zero_pos_1, 3, 11, 1, 1)
        self.label_78 = QtWidgets.QLabel(self.groupBox)
        self.label_78.setAlignment(QtCore.Qt.AlignCenter)
        self.label_78.setObjectName("label_78")
        self.gridLayout_2.addWidget(self.label_78, 3, 6, 1, 1)
        self.label_85 = QtWidgets.QLabel(self.groupBox)
        self.label_85.setAlignment(QtCore.Qt.AlignCenter)
        self.label_85.setObjectName("label_85")
        self.gridLayout_2.addWidget(self.label_85, 5, 6, 1, 1)
        self.lineEdit_enc_pos_2 = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_enc_pos_2.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_enc_pos_2.setObjectName("lineEdit_enc_pos_2")
        self.gridLayout_2.addWidget(self.lineEdit_enc_pos_2, 4, 4, 1, 1)
        self.label_84 = QtWidgets.QLabel(self.groupBox)
        self.label_84.setAlignment(QtCore.Qt.AlignCenter)
        self.label_84.setObjectName("label_84")
        self.gridLayout_2.addWidget(self.label_84, 5, 7, 1, 1)
        self.lineEdit_prf_pos_4 = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_prf_pos_4.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_prf_pos_4.setObjectName("lineEdit_prf_pos_4")
        self.gridLayout_2.addWidget(self.lineEdit_prf_pos_4, 6, 5, 1, 1)
        self.lineEdit_prf_pos_5 = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_prf_pos_5.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_prf_pos_5.setObjectName("lineEdit_prf_pos_5")
        self.gridLayout_2.addWidget(self.lineEdit_prf_pos_5, 7, 5, 1, 1)
        self.label_35 = QtWidgets.QLabel(self.groupBox)
        self.label_35.setObjectName("label_35")
        self.gridLayout_2.addWidget(self.label_35, 3, 1, 1, 1)
        self.pushButton_axis_stop_5 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_stop_5.setObjectName("pushButton_axis_stop_5")
        self.gridLayout_2.addWidget(self.pushButton_axis_stop_5, 7, 13, 1, 1)
        self.label_36 = QtWidgets.QLabel(self.groupBox)
        self.label_36.setObjectName("label_36")
        self.gridLayout_2.addWidget(self.label_36, 4, 1, 1, 1)
        self.pushButton_axis_switch_4 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_switch_4.setObjectName("pushButton_axis_switch_4")
        self.gridLayout_2.addWidget(self.pushButton_axis_switch_4, 6, 14, 1, 1)
        self.label_20 = QtWidgets.QLabel(self.groupBox)
        self.label_20.setObjectName("label_20")
        self.gridLayout_2.addWidget(self.label_20, 0, 1, 2, 1)
        self.label_25 = QtWidgets.QLabel(self.groupBox)
        self.label_25.setObjectName("label_25")
        self.gridLayout_2.addWidget(self.label_25, 0, 8, 2, 2)
        self.pushButton_axis_stop_3 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_stop_3.setObjectName("pushButton_axis_stop_3")
        self.gridLayout_2.addWidget(self.pushButton_axis_stop_3, 5, 13, 1, 1)
        self.pushButton_axis_switch_5 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_switch_5.setObjectName("pushButton_axis_switch_5")
        self.gridLayout_2.addWidget(self.pushButton_axis_switch_5, 7, 14, 1, 1)
        self.pushButton_axis_switch_1 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_switch_1.setObjectName("pushButton_axis_switch_1")
        self.gridLayout_2.addWidget(self.pushButton_axis_switch_1, 3, 14, 1, 1)
        self.lineEdit_e_enc_pos_5 = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_e_enc_pos_5.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_e_enc_pos_5.setObjectName("lineEdit_e_enc_pos_5")
        self.gridLayout_2.addWidget(self.lineEdit_e_enc_pos_5, 7, 3, 1, 1)
        self.pushButton_axis_zero_pos_5 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_zero_pos_5.setObjectName("pushButton_axis_zero_pos_5")
        self.gridLayout_2.addWidget(self.pushButton_axis_zero_pos_5, 7, 11, 1, 1)
        self.label_81 = QtWidgets.QLabel(self.groupBox)
        self.label_81.setAlignment(QtCore.Qt.AlignCenter)
        self.label_81.setObjectName("label_81")
        self.gridLayout_2.addWidget(self.label_81, 3, 9, 1, 1)
        self.label_86 = QtWidgets.QLabel(self.groupBox)
        self.label_86.setAlignment(QtCore.Qt.AlignCenter)
        self.label_86.setObjectName("label_86")
        self.gridLayout_2.addWidget(self.label_86, 5, 9, 1, 1)
        self.label_89 = QtWidgets.QLabel(self.groupBox)
        self.label_89.setAlignment(QtCore.Qt.AlignCenter)
        self.label_89.setObjectName("label_89")
        self.gridLayout_2.addWidget(self.label_89, 6, 9, 1, 1)
        self.pushButton_axis_stop_1 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_stop_1.setObjectName("pushButton_axis_stop_1")
        self.gridLayout_2.addWidget(self.pushButton_axis_stop_1, 3, 13, 1, 1)
        self.lineEdit_e_enc_pos_2 = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_e_enc_pos_2.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_e_enc_pos_2.setObjectName("lineEdit_e_enc_pos_2")
        self.gridLayout_2.addWidget(self.lineEdit_e_enc_pos_2, 4, 3, 1, 1)
        self.pushButton_axis_stop_4 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_stop_4.setObjectName("pushButton_axis_stop_4")
        self.gridLayout_2.addWidget(self.pushButton_axis_stop_4, 6, 13, 1, 1)
        self.label_87 = QtWidgets.QLabel(self.groupBox)
        self.label_87.setAlignment(QtCore.Qt.AlignCenter)
        self.label_87.setObjectName("label_87")
        self.gridLayout_2.addWidget(self.label_87, 6, 7, 1, 1)
        self.pushButton_axis_zero_pos_2 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_zero_pos_2.setObjectName("pushButton_axis_zero_pos_2")
        self.gridLayout_2.addWidget(self.pushButton_axis_zero_pos_2, 4, 11, 1, 1)
        self.pushButton_axis_stop_2 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_stop_2.setObjectName("pushButton_axis_stop_2")
        self.gridLayout_2.addWidget(self.pushButton_axis_stop_2, 4, 13, 1, 1)
        self.label_88 = QtWidgets.QLabel(self.groupBox)
        self.label_88.setAlignment(QtCore.Qt.AlignCenter)
        self.label_88.setObjectName("label_88")
        self.gridLayout_2.addWidget(self.label_88, 6, 6, 1, 1)
        self.pushButton_axis_switch_3 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_switch_3.setObjectName("pushButton_axis_switch_3")
        self.gridLayout_2.addWidget(self.pushButton_axis_switch_3, 5, 14, 1, 1)
        self.label_90 = QtWidgets.QLabel(self.groupBox)
        self.label_90.setAlignment(QtCore.Qt.AlignCenter)
        self.label_90.setObjectName("label_90")
        self.gridLayout_2.addWidget(self.label_90, 7, 7, 1, 1)
        self.lineEdit_enc_pos_1 = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_enc_pos_1.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_enc_pos_1.setObjectName("lineEdit_enc_pos_1")
        self.gridLayout_2.addWidget(self.lineEdit_enc_pos_1, 3, 4, 1, 1)
        self.label_92 = QtWidgets.QLabel(self.groupBox)
        self.label_92.setAlignment(QtCore.Qt.AlignCenter)
        self.label_92.setObjectName("label_92")
        self.gridLayout_2.addWidget(self.label_92, 7, 9, 1, 1)
        self.label_80 = QtWidgets.QLabel(self.groupBox)
        self.label_80.setAlignment(QtCore.Qt.AlignCenter)
        self.label_80.setObjectName("label_80")
        self.gridLayout_2.addWidget(self.label_80, 4, 7, 1, 1)
        self.lineEdit_enc_pos_3 = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_enc_pos_3.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_enc_pos_3.setObjectName("lineEdit_enc_pos_3")
        self.gridLayout_2.addWidget(self.lineEdit_enc_pos_3, 5, 4, 1, 1)
        self.lineEdit_prf_pos_3 = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_prf_pos_3.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_prf_pos_3.setObjectName("lineEdit_prf_pos_3")
        self.gridLayout_2.addWidget(self.lineEdit_prf_pos_3, 5, 5, 1, 1)
        self.pushButton_axis_switch_2 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_switch_2.setObjectName("pushButton_axis_switch_2")
        self.gridLayout_2.addWidget(self.pushButton_axis_switch_2, 4, 14, 1, 1)
        self.lineEdit_e_enc_pos_3 = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_e_enc_pos_3.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_e_enc_pos_3.setObjectName("lineEdit_e_enc_pos_3")
        self.gridLayout_2.addWidget(self.lineEdit_e_enc_pos_3, 5, 3, 1, 1)
        self.pushButton_axis_clr_sts_2 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_clr_sts_2.setObjectName("pushButton_axis_clr_sts_2")
        self.gridLayout_2.addWidget(self.pushButton_axis_clr_sts_2, 4, 12, 1, 1)
        self.lineEdit_prf_pos_2 = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_prf_pos_2.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_prf_pos_2.setObjectName("lineEdit_prf_pos_2")
        self.gridLayout_2.addWidget(self.lineEdit_prf_pos_2, 4, 5, 1, 1)
        self.label_79 = QtWidgets.QLabel(self.groupBox)
        self.label_79.setAlignment(QtCore.Qt.AlignCenter)
        self.label_79.setObjectName("label_79")
        self.gridLayout_2.addWidget(self.label_79, 3, 7, 1, 1)
        self.label_39 = QtWidgets.QLabel(self.groupBox)
        self.label_39.setObjectName("label_39")
        self.gridLayout_2.addWidget(self.label_39, 7, 1, 1, 1)
        self.label_31 = QtWidgets.QLabel(self.groupBox)
        self.label_31.setObjectName("label_31")
        self.gridLayout_2.addWidget(self.label_31, 4, 0, 1, 1)
        self.label_41 = QtWidgets.QLabel(self.groupBox)
        self.label_41.setText("")
        self.label_41.setObjectName("label_41")
        self.gridLayout_2.addWidget(self.label_41, 1, 9, 1, 1)
        self.label_27 = QtWidgets.QLabel(self.groupBox)
        self.label_27.setAlignment(QtCore.Qt.AlignCenter)
        self.label_27.setObjectName("label_27")
        self.gridLayout_2.addWidget(self.label_27, 1, 12, 1, 1)
        self.pushButton_axis_clr_sts_5 = QtWidgets.QPushButton(self.groupBox)
        self.pushButton_axis_clr_sts_5.setObjectName("pushButton_axis_clr_sts_5")
        self.gridLayout_2.addWidget(self.pushButton_axis_clr_sts_5, 7, 12, 1, 1)
        self.lineEdit_enc_pos_4 = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_enc_pos_4.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_enc_pos_4.setObjectName("lineEdit_enc_pos_4")
        self.gridLayout_2.addWidget(self.lineEdit_enc_pos_4, 6, 4, 1, 1)
        self.label_22 = QtWidgets.QLabel(self.groupBox)
        self.label_22.setObjectName("label_22")
        self.gridLayout_2.addWidget(self.label_22, 0, 5, 2, 1)
        self.lineEdit_e_enc_pos_4 = QtWidgets.QLineEdit(self.groupBox)
        self.lineEdit_e_enc_pos_4.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_e_enc_pos_4.setObjectName("lineEdit_e_enc_pos_4")
        self.gridLayout_2.addWidget(self.lineEdit_e_enc_pos_4, 6, 3, 1, 1)
        self.label_30 = QtWidgets.QLabel(self.groupBox)
        self.label_30.setObjectName("label_30")
        self.gridLayout_2.addWidget(self.label_30, 3, 0, 1, 1)
        self.label_34 = QtWidgets.QLabel(self.groupBox)
        self.label_34.setObjectName("label_34")
        self.gridLayout_2.addWidget(self.label_34, 7, 0, 1, 1)
        self.label_40 = QtWidgets.QLabel(self.groupBox)
        self.label_40.setObjectName("label_40")
        self.gridLayout_2.addWidget(self.label_40, 1, 3, 1, 1)
        self.label_33 = QtWidgets.QLabel(self.groupBox)
        self.label_33.setObjectName("label_33")
        self.gridLayout_2.addWidget(self.label_33, 6, 0, 1, 1)
        self.label_32 = QtWidgets.QLabel(self.groupBox)
        self.label_32.setObjectName("label_32")
        self.gridLayout_2.addWidget(self.label_32, 5, 0, 1, 1)
        self.label_83 = QtWidgets.QLabel(self.groupBox)
        self.label_83.setAlignment(QtCore.Qt.AlignCenter)
        self.label_83.setObjectName("label_83")
        self.gridLayout_2.addWidget(self.label_83, 4, 9, 1, 1)
        self.label_26 = QtWidgets.QLabel(self.groupBox)
        self.label_26.setAlignment(QtCore.Qt.AlignCenter)
        self.label_26.setObjectName("label_26")
        self.gridLayout_2.addWidget(self.label_26, 1, 11, 1, 1)
        self.verticalLayout_4.addWidget(self.groupBox)

        self.retranslateUi(Form)
        self.tabWidget.setCurrentIndex(1)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.label_46.setText(_translate("Form", "1"))
        self.label_50.setText(_translate("Form", "1"))
        self.label_52.setText(_translate("Form", "3"))
        self.label_48.setText(_translate("Form", "轴"))
        self.label_44.setText(_translate("Form", "1"))
        self.label_49.setText(_translate("Form", "2"))
        self.label_43.setText(_translate("Form", "1"))
        self.label_47.setText(_translate("Form", "核"))
        self.label_51.setText(_translate("Form", "1"))
        self.label_45.setText(_translate("Form", "4"))
        self.label_53.setText(_translate("Form", "5"))
        self.label_54.setText(_translate("Form", "1"))
        self.pushButton_home_1.setText(_translate("Form", "回零"))
        self.pushButton_home_2.setText(_translate("Form", "回零"))
        self.pushButton_home_3.setText(_translate("Form", "回零"))
        self.pushButton_home_4.setText(_translate("Form", "回零"))
        self.pushButton_home_5.setText(_translate("Form", "回零"))
        self.label_55.setText(_translate("Form", "回零"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab), _translate("Form", "回零"))
        self.label_57.setText(_translate("Form", "核"))
        self.label_63.setText(_translate("Form", "轴"))
        self.label_68.setText(_translate("Form", "模式"))
        self.label_69.setText(_translate("Form", "步长（pulse）"))
        self.label_70.setText(_translate("Form", "速度（pulse/ms）"))
        self.label_71.setText(_translate("Form", "加速度（pulse/ms^2）"))
        self.label_72.setText(_translate("Form", "减速度（pulse/ms^2）"))
        self.label_73.setText(_translate("Form", "起跳速度（pulse/ms）"))
        self.label_74.setText(_translate("Form", "平滑时间（ms）"))
        self.label_75.setText(_translate("Form", "循环次数"))
        self.label_76.setText(_translate("Form", "循环间隔（ms）"))
        self.label_77.setText(_translate("Form", "操作"))
        self.label_61.setText(_translate("Form", "1"))
        self.label_65.setText(_translate("Form", "1"))
        self.comboBox_trap_mode_1.setItemText(0, _translate("Form", "绝对运动"))
        self.comboBox_trap_mode_1.setItemText(1, _translate("Form", "相对运动"))
        self.pushButton_trap_start_1.setText(_translate("Form", "启动"))
        self.label_67.setText(_translate("Form", "1"))
        self.label_58.setText(_translate("Form", "2"))
        self.comboBox_trap_mode_2.setItemText(0, _translate("Form", "绝对运动"))
        self.comboBox_trap_mode_2.setItemText(1, _translate("Form", "相对运动"))
        self.pushButton_trap_start_2.setText(_translate("Form", "启动"))
        self.label_66.setText(_translate("Form", "1"))
        self.label_56.setText(_translate("Form", "3"))
        self.comboBox_trap_mode_3.setItemText(0, _translate("Form", "绝对运动"))
        self.comboBox_trap_mode_3.setItemText(1, _translate("Form", "相对运动"))
        self.pushButton_trap_start_3.setText(_translate("Form", "启动"))
        self.label_59.setText(_translate("Form", "1"))
        self.label_62.setText(_translate("Form", "4"))
        self.comboBox_trap_mode_4.setItemText(0, _translate("Form", "绝对运动"))
        self.comboBox_trap_mode_4.setItemText(1, _translate("Form", "相对运动"))
        self.pushButton_trap_start_4.setText(_translate("Form", "启动"))
        self.label_60.setText(_translate("Form", "1"))
        self.label_64.setText(_translate("Form", "5"))
        self.comboBox_trap_mode_5.setItemText(0, _translate("Form", "绝对运动"))
        self.comboBox_trap_mode_5.setItemText(1, _translate("Form", "相对运动"))
        self.pushButton_trap_start_5.setText(_translate("Form", "启动"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_2), _translate("Form", "点位运动"))
        self.label_4.setText(_translate("Form", "加速度（pulse/m^2）"))
        self.label_8.setText(_translate("Form", "反向运动"))
        self.label_10.setText(_translate("Form", "1"))
        self.label_2.setText(_translate("Form", "轴"))
        self.label_6.setText(_translate("Form", "平滑系数"))
        self.label_3.setText(_translate("Form", "速度（pulse/ms）"))
        self.label_11.setText(_translate("Form", "1"))
        self.label_9.setText(_translate("Form", "1"))
        self.label_12.setText(_translate("Form", "1"))
        self.label.setText(_translate("Form", "核"))
        self.label_5.setText(_translate("Form", "减速度（pulse/ms^2）"))
        self.label_7.setText(_translate("Form", "正向运动"))
        self.label_13.setText(_translate("Form", "1"))
        self.label_14.setText(_translate("Form", "1"))
        self.label_15.setText(_translate("Form", "2"))
        self.label_16.setText(_translate("Form", "3"))
        self.label_17.setText(_translate("Form", "4"))
        self.label_18.setText(_translate("Form", "5"))
        self.pushButton_f_move_1.setText(_translate("Form", "正向"))
        self.pushButton_f_move_2.setText(_translate("Form", "正向"))
        self.pushButton_f_move_3.setText(_translate("Form", "正向"))
        self.pushButton_f_move_4.setText(_translate("Form", "正向"))
        self.pushButton_f_move_5.setText(_translate("Form", "正向"))
        self.pushButton_c_move_1.setText(_translate("Form", "反向"))
        self.pushButton_c_move_2.setText(_translate("Form", "反向"))
        self.pushButton_c_move_3.setText(_translate("Form", "反向"))
        self.pushButton_c_move_4.setText(_translate("Form", "反向"))
        self.pushButton_c_move_5.setText(_translate("Form", "反向"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_3), _translate("Form", "Jog运动"))
        self.groupBox_5.setTitle(_translate("Form", "输入"))
        self.label_93.setText(_translate("Form", "启动按钮"))
        self.label_in_start_s.setText(_translate("Form", "NA"))
        self.label_119.setText(_translate("Form", "指头3伸出"))
        self.label_in_figer3_s.setText(_translate("Form", "NA"))
        self.label_94.setText(_translate("Form", "暂停按钮"))
        self.label_in_pause_s.setText(_translate("Form", "NA"))
        self.label_115.setText(_translate("Form", "指头4伸出"))
        self.label_in_figer4_s.setText(_translate("Form", "NA"))
        self.label_95.setText(_translate("Form", "复位按钮"))
        self.label_in_reset_s.setText(_translate("Form", "NA"))
        self.label_120.setText(_translate("Form", "指头5伸出"))
        self.label_in_figer5_s.setText(_translate("Form", "NA"))
        self.label_96.setText(_translate("Form", "急停按钮"))
        self.label_in_e_stop_s.setText(_translate("Form", "NA"))
        self.label_121.setText(_translate("Form", "指头6伸出"))
        self.label_in_figer6_s.setText(_translate("Form", "NA"))
        self.label_97.setText(_translate("Form", "真空1反馈"))
        self.label_in_vacuum1_s.setText(_translate("Form", "NA"))
        self.label_124.setText(_translate("Form", "指头7伸出"))
        self.label_in_figer7_s.setText(_translate("Form", "NA"))
        self.label_98.setText(_translate("Form", "气压检测"))
        self.label_in_a_pressure_s.setText(_translate("Form", "NA"))
        self.label_114.setText(_translate("Form", "指头8伸出"))
        self.label_in_figer8_s.setText(_translate("Form", "NA"))
        self.label_99.setText(_translate("Form", "指头1伸出"))
        self.label_in_figer1_s.setText(_translate("Form", "NA"))
        self.label_123.setText(_translate("Form", "指头9伸出"))
        self.label_in_figer9_s.setText(_translate("Form", "NA"))
        self.label_100.setText(_translate("Form", "指头2伸出"))
        self.label_in_figer2_s.setText(_translate("Form", "NA"))
        self.label_118.setText(_translate("Form", "真空2伸出"))
        self.label_in_vacuum2_s.setText(_translate("Form", "NA"))
        self.groupBox_6.setTitle(_translate("Form", "输出"))
        self.label_out_vacuum1_o_s.setText(_translate("Form", "NA"))
        self.label_out_figer6_s.setText(_translate("Form", "NA"))
        self.pushButton_out_figer8_s.setText(_translate("Form", "转换"))
        self.pushButton_out_figer6_s.setText(_translate("Form", "转换"))
        self.pushButton_out_buzzer_s.setText(_translate("Form", "转换"))
        self.label_153.setText(_translate("Form", "三色灯-红"))
        self.pushButton_out_figer1_s.setText(_translate("Form", "转换"))
        self.label_143.setText(_translate("Form", "指头8下降"))
        self.label_out_vacuum2_i_s.setText(_translate("Form", "NA"))
        self.label_out_pause_light_s.setText(_translate("Form", "NA"))
        self.label_out_figer4_s.setText(_translate("Form", "NA"))
        self.label_out_figer2_s.setText(_translate("Form", "NA"))
        self.pushButton_out_figer7_s.setText(_translate("Form", "转换"))
        self.label_137.setText(_translate("Form", "指头4下降"))
        self.label_151.setText(_translate("Form", "暂停按钮灯"))
        self.label_out_vacuum1_i_s.setText(_translate("Form", "NA"))
        self.label_139.setText(_translate("Form", "指头7下降"))
        self.label_out_buzzer_s.setText(_translate("Form", "NA"))
        self.label_145.setText(_translate("Form", "真空1破"))
        self.pushButton_out_vacuum2_o_s.setText(_translate("Form", "转换"))
        self.label_out_figer1_s.setText(_translate("Form", "NA"))
        self.pushButton_out_vacuum1_i_s.setText(_translate("Form", "转换"))
        self.label_147.setText(_translate("Form", "指头9伸出"))
        self.label_out_red_s.setText(_translate("Form", "NA"))
        self.label_135.setText(_translate("Form", "指头6下降"))
        self.label_out_figer7_s.setText(_translate("Form", "NA"))
        self.pushButton_out_figer9_s.setText(_translate("Form", "转换"))
        self.pushButton_out_figer5_s.setText(_translate("Form", "转换"))
        self.pushButton_out_vacuum2_i_s.setText(_translate("Form", "转换"))
        self.label_out_figer9_s.setText(_translate("Form", "NA"))
        self.label_out_figer8_s.setText(_translate("Form", "NA"))
        self.label_131.setText(_translate("Form", "指头5下降"))
        self.label_127.setText(_translate("Form", "蜂鸣器"))
        self.pushButton_out_vacuum1_o_s.setText(_translate("Form", "转换"))
        self.label_out_vacuum2_o_s.setText(_translate("Form", "NA"))
        self.pushButton_out_figer4_s.setText(_translate("Form", "转换"))
        self.label_out_figer5_s.setText(_translate("Form", "NA"))
        self.pushButton_out_figer3_s.setText(_translate("Form", "转换"))
        self.pushButton_out_pause_light_s.setText(_translate("Form", "转换"))
        self.label_133.setText(_translate("Form", "指头3下降"))
        self.label_149.setText(_translate("Form", "真空2吸"))
        self.label_141.setText(_translate("Form", "真空1吸"))
        self.label_129.setText(_translate("Form", "指头2下降"))
        self.pushButton_out_red_s.setText(_translate("Form", "转换"))
        self.label_out_figer3_s.setText(_translate("Form", "NA"))
        self.pushButton_out_figer2_s.setText(_translate("Form", "转换"))
        self.label_155.setText(_translate("Form", "真空2破"))
        self.label_125.setText(_translate("Form", "指头1下降"))
        self.label_101.setText(_translate("Form", "三色灯-绿"))
        self.label_102.setText(_translate("Form", "三色灯-黄"))
        self.label_out_green_s.setText(_translate("Form", "NA"))
        self.label_out_yellow_s.setText(_translate("Form", "NA"))
        self.pushButton_out_green_s.setText(_translate("Form", "转换"))
        self.pushButton_out_yellow_s.setText(_translate("Form", "转换"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_4), _translate("Form", "io"))
        self.groupBox.setTitle(_translate("Form", "轴运动状态"))
        self.label_28.setText(_translate("Form", "停止"))
        self.lineEdit_prf_pos_1.setText(_translate("Form", "0.0"))
        self.label_82.setText(_translate("Form", "NA"))
        self.label_19.setText(_translate("Form", "核"))
        self.label_38.setText(_translate("Form", "4"))
        self.lineEdit_enc_pos_5.setText(_translate("Form", "0.0"))
        self.label_23.setText(_translate("Form", "轴报警"))
        self.pushButton_axis_clr_sts_1.setText(_translate("Form", "清除"))
        self.pushButton_axis_clr_sts_4.setText(_translate("Form", "清除"))
        self.label_37.setText(_translate("Form", "3"))
        self.label_24.setText(_translate("Form", "正限位报警"))
        self.pushButton_axis_clr_sts_3.setText(_translate("Form", "清除"))
        self.label_21.setText(_translate("Form", "实际位置"))
        self.label_29.setText(_translate("Form", "轴使能状态"))
        self.pushButton_axis_zero_pos_4.setText(_translate("Form", "清零"))
        self.label_91.setText(_translate("Form", "NA"))
        self.pushButton_axis_zero_pos_3.setText(_translate("Form", "清零"))
        self.pushButton_axis_zero_pos_1.setText(_translate("Form", "清零"))
        self.label_78.setText(_translate("Form", "NA"))
        self.label_85.setText(_translate("Form", "NA"))
        self.lineEdit_enc_pos_2.setText(_translate("Form", "0.0"))
        self.label_84.setText(_translate("Form", "NA"))
        self.lineEdit_prf_pos_4.setText(_translate("Form", "0.0"))
        self.lineEdit_prf_pos_5.setText(_translate("Form", "0.0"))
        self.label_35.setText(_translate("Form", "1"))
        self.pushButton_axis_stop_5.setText(_translate("Form", "停止"))
        self.label_36.setText(_translate("Form", "2"))
        self.pushButton_axis_switch_4.setText(_translate("Form", "使能开启"))
        self.label_20.setText(_translate("Form", "轴"))
        self.label_25.setText(_translate("Form", "负限位报警"))
        self.pushButton_axis_stop_3.setText(_translate("Form", "停止"))
        self.pushButton_axis_switch_5.setText(_translate("Form", "使能开启"))
        self.pushButton_axis_switch_1.setText(_translate("Form", "使能开启"))
        self.pushButton_axis_zero_pos_5.setText(_translate("Form", "清零"))
        self.label_81.setText(_translate("Form", "NA"))
        self.label_86.setText(_translate("Form", "NA"))
        self.label_89.setText(_translate("Form", "NA"))
        self.pushButton_axis_stop_1.setText(_translate("Form", "停止"))
        self.pushButton_axis_stop_4.setText(_translate("Form", "停止"))
        self.label_87.setText(_translate("Form", "NA"))
        self.pushButton_axis_zero_pos_2.setText(_translate("Form", "清零"))
        self.pushButton_axis_stop_2.setText(_translate("Form", "停止"))
        self.label_88.setText(_translate("Form", "NA"))
        self.pushButton_axis_switch_3.setText(_translate("Form", "使能开启"))
        self.label_90.setText(_translate("Form", "NA"))
        self.lineEdit_enc_pos_1.setText(_translate("Form", "0.0"))
        self.label_92.setText(_translate("Form", "NA"))
        self.label_80.setText(_translate("Form", "NA"))
        self.lineEdit_enc_pos_3.setText(_translate("Form", "0.0"))
        self.lineEdit_prf_pos_3.setText(_translate("Form", "0.0"))
        self.pushButton_axis_switch_2.setText(_translate("Form", "使能开启"))
        self.pushButton_axis_clr_sts_2.setText(_translate("Form", "清除"))
        self.lineEdit_prf_pos_2.setText(_translate("Form", "0.0"))
        self.label_79.setText(_translate("Form", "NA"))
        self.label_39.setText(_translate("Form", "5"))
        self.label_31.setText(_translate("Form", "1"))
        self.label_27.setText(_translate("Form", "清除状态"))
        self.pushButton_axis_clr_sts_5.setText(_translate("Form", "清除"))
        self.lineEdit_enc_pos_4.setText(_translate("Form", "0.0"))
        self.label_22.setText(_translate("Form", "规划位置"))
        self.label_30.setText(_translate("Form", "1"))
        self.label_34.setText(_translate("Form", "1"))
        self.label_40.setText(_translate("Form", "编码器位置"))
        self.label_33.setText(_translate("Form", "1"))
        self.label_32.setText(_translate("Form", "1"))
        self.label_83.setText(_translate("Form", "NA"))
        self.label_26.setText(_translate("Form", "位置清零"))
