import threading
import time
import traceback

from can import Message

from common.LogUtils import logger
from adb.zlgcan.zlgcan import ZCAN_TransmitFD_Data, ZCAN_Transmit_Data, ZCAN, INVALID_DEVICE_HANDLE, \
    INVALID_CHANNEL_HANDLE, ZCAN_TYPE_CAN, ZCAN_TYPE_CANFD, ZCAN_CHANNEL_INIT_CONFIG, ZCAN_STATUS_OK

GRPBOX_WIDTH = 200
MSGCNT_WIDTH = 50
MSGID_WIDTH = 80
MSGDIR_WIDTH = 60
MSGINFO_WIDTH = 100
MSGLEN_WIDTH = 60
MSGDATA_WIDTH = 200
MSGVIEW_WIDTH = MSGCNT_WIDTH + MSGID_WIDTH + MSGDIR_WIDTH + <PERSON><PERSON>NFO_WIDTH + MSGLEN_WIDTH + MSGDATA_WIDTH
MSGVIEW_HEIGHT = 500
SENDVIEW_HEIGHT = 125

WIDGHT_WIDTH = GRPBOX_WIDTH + MSGVIEW_WIDTH + 40
WIDGHT_HEIGHT = MSGVIEW_HEIGHT + SENDVIEW_HEIGHT + 20

MAX_DISPLAY = 1000
MAX_RCV_NUM = 10

USBCANFD_TYPE = (41, 42, 43)
USBCAN_XE_U_TYPE = (20, 21, 31)
USBCAN_I_II_TYPE = (3, 4)


class PeriodSendThread(object):
    def __init__(self, period_func, args=[], kwargs={}):
        self._thread = threading.Thread(target=self._run)
        self._function = period_func
        self._args = args
        self._kwargs = kwargs
        self._period = 0
        self._event = threading.Event()
        self._period_event = threading.Event()
        self._terminated = False

    def start(self):
        self._thread.start()

    def stop(self):
        self._terminated = True
        self._event.set()
        self._thread.join()

    def send_start(self, period):
        self._period = period
        self._event.set()

    def send_stop(self):
        self._period_event.set()

    def _run(self):
        while True:
            self._event.wait()
            self._event.clear()
            if self._terminated:
                break
            self._function(*self._args, **self._kwargs)
            while not self._period_event.wait(self._period):
                self._function(*self._args, **self._kwargs)
            self._period_event.clear()


class ZCAN_LIB():
    def __init__(self, ratio, can_type):
        super().__init__()
        self._dev_info = {
            "USBCANFD-200U": {
                "dev_type": 41,
                "chn_num": 2,
                "chn_info": {
                    "is_canfd": True,
                    "sf_res": True,

                    "baudrate": {
                        "50K": 12696558,
                        "100K": 4307950,
                        "125K": 4304830,
                        "250K": 110526,
                        "500K": 104286,
                        "800K": 101946,
                        "1000K": 101166
                    },

                    "data_baudrate": {
                        "1M": 8487694,
                        "2M": 4260362,
                        "4M": 66058,
                        "5M": 66055
                    }
                }
            },

            "USBCANFD-100U": {
                "dev_type": 42,
                "chn_num": 1,
                "chn_info": {
                    "is_canfd": True,
                    "sf_res": True,

                    "baudrate": {
                        "50K": 12696558,
                        "100K": 4307950,
                        "125K": 4304830,
                        "250K": 110526,
                        "500K": 104286,
                        "800K": 101946,
                        "1M": 101166
                    },

                    "data_baudrate": {
                        "1M": 8487694,
                        "2M": 4260362,
                        "4M": 66058,
                        "5M": 66055
                    }
                }
            },

            "USBCANFD-MINI": {
                "dev_type": 43,
                "chn_num": 1,
                "chn_info": {
                    "is_canfd": True,
                    "sf_res": True,

                    "baudrate": {
                        "50K": 12696558,
                        "100K": 4307950,
                        "125K": 4304830,
                        "250K": 110526,
                        "500K": 104286,
                        "800K": 101946,
                        "1M": 101166
                    },

                    "data_baudrate": {
                        "1M": 8487694,
                        "2M": 4260362,
                        "4M": 66058,
                        "5M": 66055
                    }
                }
            },

            "USBCAN-E-U": {
                "dev_type": 20,
                "chn_num": 1,
                "chn_info": {
                    "is_canfd": False,
                    "sf_res": False,

                    "baudrate": {
                        "50K": "50000",
                        "100K": "100000",
                        "125K": "125000",
                        "250K": "250000",
                        "500K": "500000",
                        "800K": "800000",
                        "1M": "1000000"
                    }
                }
            },

            "USBCAN-2E-U": {
                "dev_type": 21,
                "chn_num": 2,
                "chn_info": {
                    "is_canfd": False,
                    "sf_res": False,

                    "baudrate": {
                        "50K": "50000",
                        "100K": "100000",
                        "125K": "125000",
                        "250K": "250000",
                        "500K": "500000",
                        "800K": "800000",
                        "1M": "1000000"
                    }
                }
            },

            "USBCAN-I": {
                "dev_type": 3,
                "chn_num": 1,
                "chn_info": {
                    "is_canfd": False,
                    "sf_res": False,

                    "baudrate": {
                        "50K": {
                            "timing0": 9,
                            "timing1": 28
                        },
                        "100K": {
                            "timing0": 4,
                            "timing1": 28
                        },

                        "125K": {
                            "timing0": 3,
                            "timing1": 28
                        },

                        "250K": {
                            "timing0": 1,
                            "timing1": 28
                        },

                        "500K": {
                            "timing0": 0,
                            "timing1": 28
                        },

                        "800K": {
                            "timing0": 0,
                            "timing1": 22
                        },

                        "1M": {
                            "timing0": 191,
                            "timing1": 255
                        }
                    }
                }
            },

            "USBCAN-II": {
                "dev_type": 4,
                "chn_num": 2,
                "chn_info": {
                    "is_canfd": False,
                    "sf_res": False,

                    "baudrate": {
                        "50K": {
                            "timing0": 9,
                            "timing1": 28
                        },
                        "100K": {
                            "timing0": 4,
                            "timing1": 28
                        },

                        "125K": {
                            "timing0": 3,
                            "timing1": 28
                        },

                        "250K": {
                            "timing0": 1,
                            "timing1": 28
                        },

                        "500K": {
                            "timing0": 0,
                            "timing1": 28
                        },

                        "800K": {
                            "timing0": 0,
                            "timing1": 22
                        },

                        "1M": {
                            "timing0": 191,
                            "timing1": 255
                        }
                    }
                }
            }
        }
        self.can_type = can_type
        self.ratio = str(int(int(ratio) / 1000)) + "K"
        self.status = False
        self.handle = None
        self.init_device()

    def init_device(self):
        self._zcan = ZCAN()
        self._dev_handle = INVALID_DEVICE_HANDLE
        self._can_handle = INVALID_CHANNEL_HANDLE
        self._can_handle2 = INVALID_CHANNEL_HANDLE

        self._isOpen = False
        self._isChnOpen = False

        # current device info
        self._is_canfd = True
        self._res_support = False

        # Transmit and receive count display
        self._tx_cnt = 0
        self._rx_cnt = 0
        self._view_cnt = 0

        # read can/canfd message thread
        self._read_thread = None
        self._terminated = False
        self._lock = threading.RLock()

        # period send var
        self._is_sending = False
        self._id_increase = False
        self._send_num = 1
        self._send_cnt = 1
        self._is_canfd_msg = False
        self._send_msgs = None
        self._send_thread = None
        self.OpenDevice()
        self.OpenCAN()

    def set_channel(self, chl):
        self.handle = chl

    def __dlc2len(self, dlc):
        if dlc <= 8:
            return dlc
        elif dlc == 9:
            return 12
        elif dlc == 10:
            return 16
        elif dlc == 11:
            return 20
        elif dlc == 12:
            return 24
        elif dlc == 13:
            return 32
        elif dlc == 14:
            return 48
        else:
            return 64

    def CANMsg2View(self, msg, is_transmit=True):
        view = []
        view.append(str(self._view_cnt))
        self._view_cnt += 1
        view.append(hex(msg.can_id)[2:])
        view.append("发送" if is_transmit else "接收")

        str_info = ''
        str_info += 'EXT' if msg.eff else 'STD'
        if msg.rtr:
            str_info += ' RTR'
        view.append(str_info)
        view.append(str(msg.can_dlc))
        if msg.rtr:
            view.append('')
        else:
            view.append(''.join(hex(msg.data[i])[2:] + ' ' for i in range(msg.can_dlc)))
        return view

    def CANFDMsg2View(self, msg, is_transmit=True):
        view = []
        view.append(str(self._view_cnt))
        self._view_cnt += 1

        view.append(hex(msg.can_id)[2:])
        view.append("发送" if is_transmit else "接收")

        str_info = ''
        str_info += 'EXT' if msg.eff else 'STD'
        if msg.rtr:
            str_info += ' RTR'
        else:
            str_info += ' FD'
            if msg.brs:
                str_info += ' BRS'
            if msg.esi:
                str_info += ' ESI'
        view.append(str_info)
        view.append(str(msg.len))
        if msg.rtr:
            view.append('')
        else:
            view.append(''.join(hex(msg.data[i])[2:] + ' ' for i in range(msg.len)))
        return view

    # def MsgReadThreadFunc(self):
    #     try:
    #         while not self._terminated:
    #             can_num = self._zcan.GetReceiveNum(self._can_handle, ZCAN_TYPE_CAN)
    #             canfd_num = self._zcan.GetReceiveNum(self._can_handle, ZCAN_TYPE_CANFD)
    #             if not can_num and not canfd_num:
    #                 time.sleep(0.005)  # wait 5ms
    #                 continue
    #
    #             if can_num:
    #                 while can_num and not self._terminated:
    #                     read_cnt = MAX_RCV_NUM if can_num >= MAX_RCV_NUM else can_num
    #                     can_msgs, act_num = self._zcan.Receive(self._can_handle, read_cnt, MAX_RCV_NUM)
    #                     if act_num:
    #                         # bat data
    #                         self._rx_cnt += act_num
    #                         self.ViewDataUpdate(can_msgs, act_num, False, False)
    #                     else:
    #                         break
    #                     can_num -= act_num
    #             if canfd_num:
    #                 while canfd_num and not self._terminated:
    #                     read_cnt = MAX_RCV_NUM if canfd_num >= MAX_RCV_NUM else canfd_num
    #                     canfd_msgs, act_num = self._zcan.ReceiveFD(self._can_handle, read_cnt, MAX_RCV_NUM)
    #                     if act_num:
    #                         # bat data
    #                         self._rx_cnt += act_num
    #                         # self.strvRxCnt.set(str(self._rx_cnt))
    #                         self.ViewDataUpdate(canfd_msgs, act_num, True, False)
    #                     else:
    #                         break
    #                     canfd_num -= act_num
    #     except:
    #         print(traceback.format_exc())
    #         print("Error occurred while read CAN(FD) data!")

    def ViewDataUpdate(self, msgs, msgs_num, is_canfd=False, is_send=True):
        with self._lock:
            if is_canfd:
                for i in range(msgs_num):
                    values = self.CANFDMsg2View(msgs[i].frame, is_send)
                    print("is_canfd values:", values)
            else:
                for i in range(msgs_num):
                    print("is_can values:", self.CANMsg2View(msgs[i].frame, is_send))

    def PeriodSendIdUpdate(self, is_ext):
        self._cur_id += 1
        if is_ext:
            if self._cur_id > 0x1FFFFFFF:
                self._cur_id = 0
        else:
            if self._cur_id > 0x7FF:
                self._cur_id = 0

    def PeriodSendComplete(self):
        self._is_sending = False
        # self.strvSend.set("发送")
        # self._send_thread.send_stop()

    def PeriodSend(self):
        if self._is_canfd_msg:
            ret = self._zcan.TransmitFD(self.handle, self._send_msgs, self._send_num)
        else:
            ret = self._zcan.Transmit(self.handle, self._send_msgs, self._send_num)

        # bat transmit display
        # self._tx_cnt += ret

        if ret != self._send_num:
            self.PeriodSendComplete()
            print("发送失败！")
            return

        self._send_cnt -= 1
        if self._send_cnt:
            if self._id_increase:
                for i in range(self._send_num):
                    self._send_msgs[i].frame.can_id = self._cur_id
                    self.PeriodSendIdUpdate(self._send_msgs[i].frame.eff)
        else:
            self.PeriodSendComplete()

    def send(self, msg_dict):
        is_canfd_msg = True if self.can_type.lower() == "canfd" else False

        if is_canfd_msg:
            msg = ZCAN_TransmitFD_Data()
        else:
            msg = ZCAN_Transmit_Data()

        # print("msg_dict:",msg_dict)
        msg.transmit_type = 0  # 正常发送 1 单次发送 2自发自收
        try:
            msg.frame.can_id = int(msg_dict["id"], 16)
        except:
            msg.frame.can_id = 0
        msg.frame.rtr = 0  # ("数据帧", "远程帧")
        msg.frame.eff = 0  # ("标准帧", "扩展帧")
        data = msg_dict["msg"].strip().split(' ')
        msg_len = len(data)
        if not is_canfd_msg:
            msg.frame.can_dlc = msg_len
            msg_len = msg.frame.can_dlc
        else:
            msg.frame.brs = 0  # can canfd 0，"CANFD BRS" 1
            # msg.frame.len = self.__dlc2len(msg_len)
            msg.frame.len = msg_len
            msg_len = msg.frame.len

        for i in range(msg_len):
            if i < len(data):
                try:
                    msg.frame.data[i] = int(data[i], 16)
                except:
                    msg.frame.data[i] = 0
            else:
                msg.frame.data[i] = 0
        self._id_increase = 0
        self._send_num = 1
        self._send_cnt = 1
        self._is_canfd_msg = is_canfd_msg

        if is_canfd_msg:
            self._send_msgs = (ZCAN_TransmitFD_Data * self._send_num)()
        else:
            self._send_msgs = (ZCAN_Transmit_Data * self._send_num)()

        self._cur_id = msg.frame.can_id
        for i in range(self._send_num):
            self._send_msgs[i] = msg
            self._send_msgs[i].frame.can_id = self._cur_id
            self.PeriodSendIdUpdate(self._send_msgs[i].frame.eff)

        self._is_sending = True
        # self._send_thread.send_start(period * 0.001)
        self.PeriodSend()
        # print("send daa ",msg)

    def DevInfoRead(self):
        info = self._zcan.GetDeviceInf(self._dev_handle)
        if info != None:
            print("info:", info)

    def OpenDevice(self):
        if self._isOpen:
            # Close Device
            self._zcan.CloseDevice(self._dev_handle)
            self._isOpen = False
        else:
            self._cur_dev_info = self._dev_info["USBCANFD-200U"]

            # Open Device
            self._dev_handle = self._zcan.OpenDevice(41, 0, 0)
            print("self._dev_handle", self._dev_handle)
            if self._dev_handle == INVALID_DEVICE_HANDLE:
                print("打开设备失败")
                return

                # Update Device Info Display
            self.DevInfoRead()
            self._is_canfd = self._cur_dev_info["chn_info"]["is_canfd"]
            self._res_support = self._cur_dev_info["chn_info"]["sf_res"]
            print("OpenDevice", self._dev_handle)
            self._isOpen = True

    def OpenCAN(self):
        if self._isChnOpen:
            # wait read_thread exit
            self._terminated = True
            # self._read_thread.join(0.1)
            # stop send thread
            # self._send_thread.stop()
            # Close channel
            self._zcan.ResetCAN(self._can_handle)
            self._zcan.ResetCAN(self._can_handle2)

            self._isChnOpen = False
        else:
            # Initial channel
            if self._res_support:  # resistance enable
                ip = self._zcan.GetIProperty(self._dev_handle)
                for chl in range(2):
                    self._zcan.SetValue(ip, str(chl) + "/initenal_resistance", '1')  # 使能 1 失能 0
                self._zcan.ReleaseIProperty(ip)

            # # set usbcan-e-u baudrate
            # if self._cur_dev_info["dev_type"] in USBCAN_XE_U_TYPE:
            #     ip = self._zcan.GetIProperty(self._dev_handle)
            #     self._zcan.SetValue(ip,
            #                         str(chl) + "/baud_rate",
            #                         self._cur_dev_info["chn_info"]["baudrate"][1]) # 波特率 2M
            #     self._zcan.ReleaseIProperty(ip)

            # set usbcanfd clock
            if 41 in USBCANFD_TYPE:  # usbcanfd
                ip = self._zcan.GetIProperty(self._dev_handle)
                for chl in range(2):
                    self._zcan.SetValue(ip, str(chl) + "/clock", "60000000")
                self._zcan.ReleaseIProperty(ip)

            chn_cfg = ZCAN_CHANNEL_INIT_CONFIG()
            chn_cfg.can_type = ZCAN_TYPE_CANFD if self._is_canfd else ZCAN_TYPE_CAN
            if self._is_canfd:
                chn_cfg.config.canfd.mode = 0
                chn_cfg.config.canfd.abit_timing = self._cur_dev_info["chn_info"]["baudrate"][self.ratio]
                chn_cfg.config.canfd.dbit_timing = self._cur_dev_info["chn_info"]["data_baudrate"]["2M"]
            else:
                chn_cfg.config.can.mode = 0
                if 41 in USBCAN_I_II_TYPE:
                    brt = self._cur_dev_info["chn_info"]["baudrate"][self.ratio]
                    chn_cfg.config.can.timing0 = brt["timing0"]
                    chn_cfg.config.can.timing1 = brt["timing1"]
                    chn_cfg.config.can.acc_code = 0
                    chn_cfg.config.can.acc_mask = 0xFFFFFFFF

            self._can_handle = self._zcan.InitCAN(self._dev_handle, 0, chn_cfg)
            self._can_handle2 = self._zcan.InitCAN(self._dev_handle, 1, chn_cfg)
            if self._can_handle == INVALID_CHANNEL_HANDLE:
                print("初始化通道失败!")
                return

            ret = self._zcan.StartCAN(self._can_handle)
            ret1 = self._zcan.StartCAN(self._can_handle2)
            if ret != ZCAN_STATUS_OK:
                print("打开通道失败!")
                return
            if ret1 != ZCAN_STATUS_OK:
                print("打开2通道失败!")
                # return
                # start send thread
            # self._send_thread = PeriodSendThread(self.PeriodSend)
            # self._send_thread.start()
            # start receive thread
            self._terminated = False
            # self._read_thread = threading.Thread(None, target=self.MsgReadThreadFunc)
            # self._read_thread.start()
            self._isChnOpen = True

            self.status = self._isChnOpen

    def _recv_internal(self, timeout=3):
        try:
            # canfd_num = 0
            # can_num = 0
            # if self.can_type.lower()=="canfd":
            #     canfd_num = self._zcan.GetReceiveNum(self._can_handle, ZCAN_TYPE_CANFD)
            # else:
            #     can_num = self._zcan.GetReceiveNum(self._can_handle, ZCAN_TYPE_CAN)
            msg_list = []
            can_num = self._zcan.GetReceiveNum(self.handle, ZCAN_TYPE_CAN)
            canfd_num = self._zcan.GetReceiveNum(self.handle, ZCAN_TYPE_CANFD)
            if can_num:
                read_cnt = MAX_RCV_NUM if can_num >= MAX_RCV_NUM else can_num
                rcv_msg, rcv_num = self._zcan.Receive(self.handle, read_cnt, MAX_RCV_NUM)
                for i in range(rcv_num):
                    msg = Message(
                        timestamp=rcv_msg[i].timestamp,
                        arbitration_id=rcv_msg[i].frame.can_id,
                        is_extended_id=False,
                        is_remote_frame=False,
                        is_error_frame=False,
                        data=rcv_msg[i].frame.data,
                        is_fd=False
                    )
                    print("can msg is ,msg={}".format(msg))
                    msg_list.append(msg)
                    # return msg, True
            if canfd_num:
                read_cnt = MAX_RCV_NUM if canfd_num >= MAX_RCV_NUM else canfd_num
                rcv_msg, rcv_num = self._zcan.ReceiveFD(self.handle, read_cnt, MAX_RCV_NUM)
                for i in range(rcv_num):
                    msg = Message(
                        timestamp=rcv_msg[i].timestamp,
                        arbitration_id=rcv_msg[i].frame.can_id,
                        is_extended_id=False,
                        is_remote_frame=False,
                        is_error_frame=False,
                        data=rcv_msg[i].frame.data,
                        is_fd=True
                    )
                    # print("canfd msg is ,msg={}".format(msg))
                    msg_list.append(msg)
                    # return msg, True
            return msg_list, True
        except:
            print(f"Error occurred while read CAN(FD) data! {traceback.format_exc()}")
            return [], True

    def close_channel_device(self):
        close_channel = self._zcan.ResetCAN(self._can_handle)
        close_device = self._zcan.CloseDevice(self._dev_handle)
        if close_channel and close_device:
            logger.info("close_channel_device")

    def send_periodic_message(self, canbus, id, data, period):
        """
        Sends a periodic message on the CAN bus.

        :param canbus: Object of ZCAN_LIB.
        :param id: Message arbitration ID.
        :param data: Data bytes to send.
        :param period: Period in milliseconds for sending the message.
        """
        # Construct message dict as per your current sending method requirements
        msg_dict = {
            "id": hex(id),
            "msg": ' '.join(format(byte, '02X') for byte in data)
        }

        def _send():
            canbus.send(msg_dict)

        # Use PeriodSendThread for periodic sending
        if period == 0:
            _send()
            return
        send_thread = PeriodSendThread(period_func=_send)
        send_thread.send_start(period / 1000.0)  # Convert to seconds if needed
        return send_thread

    def receive_responses(self, canbus, duration):
        """
        Listens for responses on the CAN bus for a specific duration.

        :param canbus: An instance of ZCAN_LIB, already initialized and configured.
        :param duration: Duration in seconds for which to listen to incoming messages.
        """

        def listener():
            end_time = time.time() + duration
            while time.time() < end_time:
                msg_list = canbus._recv_internal(1)  # Assuming this call is blocking but quick.
                for msg in msg_list:
                    print("Received message:", msg)

        # Start the listener in a separate thread
        listener_thread = threading.Thread(target=listener)
        listener_thread.start()
        listener_thread.join()  # Wait for the thread to complete if you want to keep the main thread blocked.


if __name__ == "__main__":
    chl, ratio, can_type = 0, "500000", "canfd"
    canbus = ZCAN_LIB(ratio, can_type)
    # canbus2 = ZCAN_LIB(ratio, can_type)
    # print(canbus._zcan.GetVersion())
    messages = [
        (0x4E7, bytes.fromhex('05 00 00 FF FF FF FF FF'), 640),
        (0x13C, bytes.fromhex('00 00 00 02 00 00 00 00'), 20),
        (0x288, bytes.fromhex(
            '00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 C6 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00'),
         20),
        (0x636, bytes.fromhex('06 50 01 00 32 00 C8 AA'), 0),
    ]
    # tasks = [canbus.send_periodic_message(canbus, *msg) for msg in messages if msg[2] > 0]

    # canbus.receive_responses(canbus, 60)
