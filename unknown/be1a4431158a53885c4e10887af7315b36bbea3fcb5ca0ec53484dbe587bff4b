%YAML:1.0
---
size: !!opencv-matrix
   rows: 2
   cols: 1
   dt: d
   data: [ 640., 480. ]
K1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 7.6159209686584518e+02, 0., 3.2031427422505453e+02, 0.,
       7.6167321445963728e+02, 2.2467546927337131e+02, 0., 0., 1. ]
D1: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ 3.4834574885170888e-02, -5.5261651661983137e-02,
       5.7491952731614823e-04, -4.2764224824172658e-05,
       1.8477350140315381e-02 ]
K2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 7.6327773941976670e+02, 0., 2.8768149948082271e+02, 0.,
       7.6350419442870850e+02, 2.1897333598636970e+02, 0., 0., 1. ]
D2: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ 3.5020972475517692e-02, -4.0770660841280497e-02,
       -4.4231087565750534e-04, -1.0552562170995372e-03,
       -9.7749906830348537e-02 ]
R: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.9999370552351063e-01, 7.8563885326366346e-04,
       3.4600122760633780e-03, -7.9503151737356746e-04,
       9.9999600079883766e-01, 2.7140949167922721e-03,
       -3.4578661403601796e-03, -2.7168286517956050e-03,
       9.9999033095517087e-01 ]
T: !!opencv-matrix
   rows: 3
   cols: 1
   dt: d
   data: [ -6.0005833133148414e+01, 1.7047017063672587e-01,
       6.0300223404957642e-01 ]
E: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -1.1005724987007073e-04, -6.0346296076620343e-01,
       1.6883191705475561e-01, 3.9550629985097430e-01,
       -1.6255182474732952e-01, 6.0007339329190145e+01,
       -1.2276256904913259e-01, -6.0005727085740176e+01,
       -1.6345135556766910e-01 ]
F: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -6.7250769136371160e-10, -3.6870834234286016e-06,
       1.6143104894409041e-03, 2.4160347372858321e-06,
       -9.9287680075344234e-07, 2.7862421257891157e-01,
       -1.1014218394645766e-03, -2.7856049650040260e-01, 1. ]
R1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.9997618806974742e-01, -2.0278309638726887e-03,
       -6.5963016213173775e-03, 2.0367881225372914e-03,
       9.9999701250432615e-01, 1.3514719999064883e-03,
       6.5935413581266105e-03, -1.3648750875444691e-03,
       9.9997733090723306e-01 ]
R2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.9994547731576255e-01, -2.8407384289991728e-03,
       -1.0048512373976153e-02, 2.8270879178959596e-03,
       9.9999506202764499e-01, -1.3724045434755307e-03,
       1.0052361397026631e-02, 1.3439216883706559e-03,
       9.9994857062992937e-01 ]
P1: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ 7.3741438842621210e+02, 0., 3.1126281356811523e+02, 0., 0.,
       7.3741438842621210e+02, 2.2189782714843750e+02, 0., 0., 0., 1.,
       0. ]
P2: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ 7.3741438842621210e+02, 0., 3.1126281356811523e+02,
       -4.4251577456670653e+04, 0., 7.3741438842621210e+02,
       2.2189782714843750e+02, 0., 0., 0., 1., 0. ]
Q: !!opencv-matrix
   rows: 4
   cols: 4
   dt: d
   data: [ 1., 0., 0., -3.1126281356811523e+02, 0., 1., 0.,
       -2.2189782714843750e+02, 0., 0., 0., 7.3741438842621210e+02, 0.,
       0., 1.6664137886344466e-02, 0. ]
