"""
user: Created by jid on 2019/10/18.
email: <EMAIL>
description:
"""

from common.LogUtils import logger
from common.ui.ConfirmDialog import Ui_ConfirmDialog
from common.view.HWDialog import BaseDialog
# from res import custom_confirm_btn_stylesheet, custom_cancel_btn_stylesheet


class ConfirmDialog(BaseDialog, Ui_ConfirmDialog):

    def __init__(self, parent=None, callback=None):
        super(ConfirmDialog, self).__init__(parent)
        self.setupUi(self)
        self.confirm_btn.clicked.connect(self.confirm)
        self.cancel_btn.clicked.connect(self.cancel)
        self.callback_result = callback

    def show_dialog(self, title=None, confirm_text=None, cancel_text=None):
        logger.info('show_dialog title=%s', title)
        self.title_label.setText(title)
        if confirm_text is not None:
            self.confirm_btn.setText(confirm_text)
        if cancel_text is not None:
            self.cancel_btn.setText(cancel_text)
        super(ConfirmDialog, self).show_dialog()

    def confirm(self):
        self.callback_result(0x01)
        self.close()

    def cancel(self):
        self.callback_result(0x02)
        self.close()

    def close_dialog(self):
        self.close()
