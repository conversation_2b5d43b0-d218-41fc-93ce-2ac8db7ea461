import ctypes
import logging
import os
import re
import traceback
import urllib.parse

import requests
from urllib3 import disable_warnings

from common import is_windows_platform
from hw_file_system.NextcloudHelper import NEXTCLOUD_USERNAME, NEXTCLOUD_PASSWORD, NEXTCLOUD_UID, NEXTCLOUD_URL, \
    AUTOTEST_ROOT

disable_warnings()


def update():
    try:
        s = login()
        url = f'http://hq.hwauto.com.cn:10011/programs/versions/last?program_number=WPTSN11_27SecureServer_DLL'
        response = s.get(url, verify=False, timeout=20)
        path = response.json()['data']['path']

        download(s, path)
    except Exception as e:
        logging.error(str(e.args))
        logging.error("update  GenerateKeyExOptImpl.dll failed: " + traceback.format_exc())


def login():
    s = requests.Session()
    url = f'{NEXTCLOUD_URL}'
    headers = {
        # "host": "**********:8081",
        "connection": "keep-alive",
        "upgrade-insecure-requests": "1",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        # "accept-encoding": "gzip, deflate",
        "accept-language": "zh-CN,zh;q=0.9"
    }

    response = s.get(url, headers=headers, verify=False, timeout=20)
    token = re.findall('data-requesttoken="(.*?)"', response.text)[0]
    token = urllib.parse.quote(token)
    url = f'{NEXTCLOUD_URL}/login'
    headers = {
        # "host": f"{NEXTCLOUD_URL}",
        "connection": "keep-alive",
        "content-length": "201",
        "cache-control": "max-age=0",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"99\", \"Google Chrome\";v=\"127\", \"Chromium\";v=\"127\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "upgrade-insecure-requests": "1",
        "origin": "null",
        "content-type": "application/x-www-form-urlencoded",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "sec-fetch-site": "same-origin",
        "sec-fetch-mode": "navigate",
        "sec-fetch-user": "?1",
        "sec-fetch-dest": "document",
        "accept-language": "zh-CN,zh;q=0.9",
    }

    payload = f'user={NEXTCLOUD_USERNAME}&password={NEXTCLOUD_PASSWORD}&timezone=Asia%2FShanghai&timezone_offset=8&requesttoken={token}'
    response = s.post(url, headers=headers, data=payload, verify=False, timeout=20)
    if response.status_code == 200:
        return s
    return None


def download(s, url):
    # url = "http://**********:8081/f/168824"
    response = s.get(url, verify=False, timeout=20)
    print(response.url)
    date = re.findall(r'27SecureServer/(.*?)&', response.url)[0]
    # date = response.url.split('/')[-1]
    url = f'{NEXTCLOUD_URL}/remote.php/dav/files/{NEXTCLOUD_UID}/{AUTOTEST_ROOT}/WPTSN11/27SecureServer/{date}/GenerateKeyExOptImpl.dll'
    # "http://**********:8081/remote.php/dav/files/D6A25366-9A66-4BA4-8AB9-AB918E763DC7/automated_test/WPTSN11/27SecureServer/A3-2024-10-24/GenerateKeyExOptImpl.dll"
    # "http://**********:8081/remote.php/dav/files/D6A25366-9A66-4BA4-8AB9-AB918E763DC7/automated_test/WPTSN11/27SecureServer/A3-2024-10-24/GenerateKeyExOptImpl.dll"
    print("download dll from:{url}".format(url=url))
    headers = {
        # "host": "**********:8081",
        "connection": "keep-alive",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
        "accept-encoding": "gzip, deflate",
        "accept-language": "zh-CN,zh;q=0.9",
        # "cookie": "oc_sessionPassphrase=5ClU8ceby4KJmEYxu2Qxh02IvozfYrhOsI8CzLabfDgMQbHzJBzeVapGfVVd%2FsD4lgfjpIhS9UdXkdsAZfkHkFptv11tuxcJEMRnYNnXUqJ5AfTpJL2KhFdVBWxx6zZs; nc_sameSiteCookielax=true; nc_sameSiteCookiestrict=true; ocaef7kdgag8=b1dd581d22bad3e4845413ac353e80cb; nc_username=D6A25366-9A66-4BA4-8AB9-AB918E763DC7; nc_token=OxEGf7Ny6GSbty%2Bnyv0eEiSYn4Cgb%2FwA; nc_session_id=b1dd581d22bad3e4845413ac353e80cb"
    }

    response = s.get(url, headers=headers, verify=False, timeout=20)

    if response.status_code == 200:
        # 获取文件总大小
        total_size = int(response.headers.get('content-length', 0))
        # 已下载的大小
        downloaded_size = 0

        # 打开一个本地文件用于保存下载的数据
        file_path = os.path.join(os.getcwd(), "adb/dll", "GenerateKeyExOptImpl.dll")
        # file_path ="GenerateKeyExOptImpl.dll"
        with open(file_path, 'wb') as file:
            # 逐块写入文件
            for chunk in response.iter_content(chunk_size=100):
                file.write(chunk)
                downloaded_size += len(chunk)
                # 计算下载的百分比
                progress = (downloaded_size / total_size) * 100
                # 打印下载进度
                print(f"\r已下载: {downloaded_size} 字节，总大小: {total_size} 字节，进度: {progress:.2f}%", end="")
        print("\n文件下载成功，并保存到", file_path)
    else:
        print("文件下载失败，状态码：", response.status_code)


# 定义BYTE类型
BYTE = ctypes.c_ubyte


class Service27:
    instance = None
    dll = None

    def __new__(cls, *args, **kwargs):

        if cls.instance is None:
            cls.instance = super().__new__(cls)
            cls.instance.initialize()
        return cls.instance

    def __init__(self):
        pass

    def initialize(self):
        # 更新dll
        update()
        dll_path = os.path.join(os.getcwd(), "adb/dll", 'GenerateKeyExOptImpl.dll')
        # dll_path = r'D:\work\HWTreeATE\adb\dll\GenerateKeyExOptImpl.dll'
        # ctype load dll
        if is_windows_platform():
            self.dll = ctypes.cdll.LoadLibrary(dll_path)
        else:
            self.dll = None

    def XCP_ComputeKeyFromSeed_All(self, level, seed_values, projectname, position):
        XCP_ComputeKeyFromSeed_All = self.dll.XCP_ComputeKeyFromSeed_All
        XCP_ComputeKeyFromSeed_All.restype = ctypes.c_int
        XCP_ComputeKeyFromSeed_All.argtypes = [
            BYTE,  # privilege
            BYTE,  # byteLenSeed
            ctypes.POINTER(BYTE),  # seed
            ctypes.POINTER(BYTE),  # byteLenKey
            ctypes.POINTER(BYTE),  # key
            ctypes.c_char_p,  # projectname
            ctypes.c_char_p  # position
        ]
        seed_length = len(seed_values)
        byteLenSeed = BYTE(seed_length)
        seed = (BYTE * seed_length)(*seed_values)
        byte_len_key = BYTE(16)  # 假设key长度
        key = (BYTE * 16)()  # 假设最大长度为16
        position = position.encode('utf-8')  # 字符串需要编码为bytes
        projectname = projectname.encode('utf-8')  # 字符串需要编码为bytes
        # 调用函数
        result = XCP_ComputeKeyFromSeed_All(BYTE(level), byteLenSeed, seed, ctypes.byref(byte_len_key), key,
                                            projectname,
                                            position)
        # 转为无符号
        key = [k & 0xFF for k in key[:byte_len_key.value]]
        # print(f"Key: {[hex(k) for k in key[:byte_len_key.value]]}")
        return key[:4]
if __name__ == '__main__':
    Service27().XCP_ComputeKeyFromSeed_All(1, [0, 1, 76, 116], 'ICSCN07', "none")
# def XCP_ComputeKeyFromSeedopt(level, seed_values, position):
#     # input [0x55, 0x97, 0x9d, 0xbe] output ['0xca', '0x1e', '0xbc', '0xb']
#     # 定义函数参数和返回类型
#     XCP_ComputeKeyFromSeedopt = dll.XCP_ComputeKeyFromSeedopt
#     XCP_ComputeKeyFromSeedopt.restype = ctypes.c_int
#     XCP_ComputeKeyFromSeedopt.argtypes = [
#         BYTE,  # privilege
#         BYTE,  # byteLenSeed
#         ctypes.POINTER(BYTE),  # seed
#         ctypes.POINTER(BYTE),  # byteLenKey
#         ctypes.POINTER(BYTE),  # key
#         ctypes.c_char_p  # position
#     ]
#     seed_length = len(seed_values)
#     byteLenSeed = BYTE(seed_length)
#     seed = (BYTE * seed_length)(*seed_values)
#     byte_len_key = BYTE(16)  # 假设key长度
#     key = (BYTE * 16)()  # 假设最大长度为16
#     position = position.encode('utf-8')  # 字符串需要编码为bytes
#     # 调用函数
#     result = XCP_ComputeKeyFromSeedopt(BYTE(level), byteLenSeed, seed, ctypes.byref(byte_len_key), key, position)
#     # 转为无符号
#     key = [k & 0xFF for k in key[:byte_len_key.value]]
#     print(f"Key: {[hex(k) for k in key[:byte_len_key.value]]}")
#     return key
#
#
# def XCP_ComputeKeyFromSeed(level, seed_values):
#     XCP_ComputeKeyFromSeed = dll.XCP_ComputeKeyFromSeed
#     XCP_ComputeKeyFromSeed.restype = ctypes.c_int
#     XCP_ComputeKeyFromSeed.argtypes = [
#         BYTE,  # privilege
#         BYTE,  # byteLenSeed
#         ctypes.POINTER(BYTE),  # seed
#         ctypes.POINTER(BYTE),  # byteLenKey
#         ctypes.POINTER(BYTE),  # key
#         # ctypes.c_char_p  # position
#     ]
#     seed_length = len(seed_values)
#     byteLenSeed = BYTE(seed_length)
#     seed = (BYTE * seed_length)(*seed_values)
#     byte_len_key = BYTE(16)  # 假设key长度
#     key = (BYTE * 16)()  # 假设最大长度为16
#     # position = position.encode('utf-8')  # 字符串需要编码为bytes
#     # 调用函数
#     result = XCP_ComputeKeyFromSeed(BYTE(level), byteLenSeed, seed, ctypes.byref(byte_len_key), key)
#     # 转为无符号
#     key = [k & 0xFF for k in key[:byte_len_key.value]]
#     print(f"Key: {[hex(k) for k in key[:byte_len_key.value]]}")
#     return key
#


# if __name__ == '__main__':
#     XCP_ComputeKeyFromSeed_All(4, [0x55, 0x97, 0x9d, 0xbe], "ILTCF07", "right")
