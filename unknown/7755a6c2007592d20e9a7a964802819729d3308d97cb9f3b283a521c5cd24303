from PyQt5.QtWidgets import QWidget

from .ui.axisWidget import Ui_Form

from ..ctr_card import ctr_card

from ..constants import X, Y, Z1, Z2, R


class AxisWidget(QWidget, Ui_Form):
    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setupUi(self)

        self.setWindowTitle("轴控制")
        self.resize(900, 700)

        self.spinBox_trap_step_2.setValue(1000)
        self.doubleSpinBox_trap_vel_2.setValue(10)
        self.doubleSpinBox_trap_acc_2.setValue(1)
        self.doubleSpinBox_trap_dec_2.setValue(1)
        self.doubleSpinBox_trap_vel_start_2.setValue(0)
        self.spinBox_trap_smooth_time_2.setValue(10)

        axis_1_pos = ctr_card.axis_d[1].get_ecat_enc_pos()
        self.lineEdit_e_enc_pos_1.setText(str(axis_1_pos))
        axis_2_pos = ctr_card.axis_d[2].get_ecat_enc_pos()
        self.lineEdit_e_enc_pos_2.setText(str(axis_2_pos))
        axis_3_pos = ctr_card.axis_d[3].get_ecat_enc_pos()
        self.lineEdit_e_enc_pos_3.setText(str(axis_3_pos))
        axis_4_pos = ctr_card.axis_d[4].get_ecat_enc_pos()
        self.lineEdit_e_enc_pos_4.setText(str(axis_4_pos))
        axis_5_pos = ctr_card.axis_d[5].get_ecat_enc_pos()
        self.lineEdit_e_enc_pos_5.setText(str(axis_5_pos))

        self.pushButton_f_move_1.pressed.connect(self.on_j_move_pressed)
        self.pushButton_c_move_1.pressed.connect(self.on_j_move_pressed)
        self.pushButton_f_move_1.released.connect(self.on_j_move_released)
        self.pushButton_c_move_1.released.connect(self.on_j_move_released)

        self.pushButton_f_move_2.pressed.connect(self.on_j_move_pressed)
        self.pushButton_c_move_2.pressed.connect(self.on_j_move_pressed)
        self.pushButton_f_move_2.released.connect(self.on_j_move_released)
        self.pushButton_c_move_2.released.connect(self.on_j_move_released)

        self.pushButton_f_move_3.pressed.connect(self.on_j_move_pressed)
        self.pushButton_c_move_3.pressed.connect(self.on_j_move_pressed)
        self.pushButton_f_move_3.released.connect(self.on_j_move_released)
        self.pushButton_c_move_3.released.connect(self.on_j_move_released)

        self.pushButton_f_move_4.pressed.connect(self.on_j_move_pressed)
        self.pushButton_c_move_4.pressed.connect(self.on_j_move_pressed)
        self.pushButton_f_move_4.released.connect(self.on_j_move_released)
        self.pushButton_c_move_4.released.connect(self.on_j_move_released)

        self.pushButton_f_move_5.pressed.connect(self.on_j_move_pressed)
        self.pushButton_c_move_5.pressed.connect(self.on_j_move_pressed)
        self.pushButton_f_move_5.released.connect(self.on_j_move_released)
        self.pushButton_c_move_5.released.connect(self.on_j_move_released)

        self.pushButton_trap_start_1.clicked.connect(self.on_trap_start)
        self.pushButton_trap_start_2.clicked.connect(self.on_trap_start)
        self.pushButton_trap_start_3.clicked.connect(self.on_trap_start)
        self.pushButton_trap_start_4.clicked.connect(self.on_trap_start)
        self.pushButton_trap_start_5.clicked.connect(self.on_trap_start)

        self.pushButton_home_1.clicked.connect(self.on_home_clicked)
        self.pushButton_home_2.clicked.connect(self.on_home_clicked)
        self.pushButton_home_3.clicked.connect(self.on_home_clicked)
        self.pushButton_home_4.clicked.connect(self.on_home_clicked)
        self.pushButton_home_5.clicked.connect(self.on_home_clicked)

        self.pushButton_axis_zero_pos_1.clicked.connect(self.on_axis_zero_pos_clicked)
        self.pushButton_axis_zero_pos_2.clicked.connect(self.on_axis_zero_pos_clicked)
        self.pushButton_axis_zero_pos_3.clicked.connect(self.on_axis_zero_pos_clicked)
        self.pushButton_axis_zero_pos_4.clicked.connect(self.on_axis_zero_pos_clicked)
        self.pushButton_axis_zero_pos_5.clicked.connect(self.on_axis_zero_pos_clicked)

        self.pushButton_axis_stop_1.clicked.connect(self.on_axis_stop_clicked)
        self.pushButton_axis_stop_2.clicked.connect(self.on_axis_stop_clicked)
        self.pushButton_axis_stop_3.clicked.connect(self.on_axis_stop_clicked)
        self.pushButton_axis_stop_4.clicked.connect(self.on_axis_stop_clicked)
        self.pushButton_axis_stop_5.clicked.connect(self.on_axis_stop_clicked)

        self.pushButton_out_figer1_s.clicked.connect(self.on_out_value_clicked)
        self.pushButton_out_figer2_s.clicked.connect(self.on_out_value_clicked)
        self.pushButton_out_figer3_s.clicked.connect(self.on_out_value_clicked)
        self.pushButton_out_figer4_s.clicked.connect(self.on_out_value_clicked)
        self.pushButton_out_vacuum1_i_s.clicked.connect(self.on_out_value_clicked)
        self.pushButton_out_vacuum1_o_s.clicked.connect(self.on_out_value_clicked)
        self.pushButton_out_vacuum2_i_s.clicked.connect(self.on_out_value_clicked)
        self.pushButton_out_figer5_s.clicked.connect(self.on_out_value_clicked)
        self.pushButton_out_figer6_s.clicked.connect(self.on_out_value_clicked)
        self.pushButton_out_figer7_s.clicked.connect(self.on_out_value_clicked)
        self.pushButton_out_figer8_s.clicked.connect(self.on_out_value_clicked)
        self.pushButton_out_figer9_s.clicked.connect(self.on_out_value_clicked)
        self.pushButton_out_pause_light_s.clicked.connect(self.on_out_value_clicked)
        self.pushButton_out_vacuum2_o_s.clicked.connect(self.on_out_value_clicked)
        self.pushButton_out_buzzer_s.clicked.connect(self.on_out_value_clicked)
        self.pushButton_out_red_s.clicked.connect(self.on_out_value_clicked)
        self.pushButton_out_green_s.clicked.connect(self.on_out_value_clicked)
        self.pushButton_out_yellow_s.clicked.connect(self.on_out_value_clicked)

        ctr_card.axis_status_changed_signal.connect(self.on_axis_status_changed)
        ctr_card.axis_prf_pos_changed_signal.connect(self.on_axis_prf_pos_changed)
        ctr_card.axis_enc_pos_changed_signal.connect(self.on_axis_enc_pos_changed)
        ctr_card.axis_e_enc_pos_changed_signal.connect(self.on_axis_e_enc_pos_changed)

        ctr_card.io.in_values_changed_signal.connect(self.on_in_values_changed)
        ctr_card.io.out_values_changed_signal.connect(self.on_out_values_changed)

        self.on_in_values_changed([], ctr_card.io.in_values)
        self.on_out_values_changed([], ctr_card.io.out_values)

    def on_j_move_pressed(self):
        if self.sender().objectName() == "pushButton_f_move_1":
            vel = self.doubleSpinBox_j_vel_1.value()
            acc = self.doubleSpinBox_j_acc_1.value()
            dec = self.doubleSpinBox_j_dec_1.value()
            smh = self.doubleSpinBox_j_smh_1.value()
            ctr_card.axis_jog(1, 1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_c_move_1":
            vel = self.doubleSpinBox_j_vel_1.value()
            acc = self.doubleSpinBox_j_acc_1.value()
            dec = self.doubleSpinBox_j_dec_1.value()
            smh = self.doubleSpinBox_j_smh_1.value()
            ctr_card.axis_jog(1, -1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_f_move_2":
            vel = self.doubleSpinBox_j_vel_2.value()
            acc = self.doubleSpinBox_j_acc_2.value()
            dec = self.doubleSpinBox_j_dec_2.value()
            smh = self.doubleSpinBox_j_smh_2.value()
            ctr_card.axis_jog(2, 1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_c_move_2":
            vel = self.doubleSpinBox_j_vel_2.value()
            acc = self.doubleSpinBox_j_acc_2.value()
            dec = self.doubleSpinBox_j_dec_2.value()
            smh = self.doubleSpinBox_j_smh_2.value()
            ctr_card.axis_jog(2, -1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_f_move_3":
            vel = self.doubleSpinBox_j_vel_3.value()
            acc = self.doubleSpinBox_j_acc_3.value()
            dec = self.doubleSpinBox_j_dec_3.value()
            smh = self.doubleSpinBox_j_smh_3.value()
            ctr_card.axis_jog(3, 1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_c_move_3":
            vel = self.doubleSpinBox_j_vel_3.value()
            acc = self.doubleSpinBox_j_acc_3.value()
            dec = self.doubleSpinBox_j_dec_3.value()
            smh = self.doubleSpinBox_j_smh_3.value()
            ctr_card.axis_jog(3, -1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_f_move_4":
            vel = self.doubleSpinBox_j_vel_4.value()
            acc = self.doubleSpinBox_j_acc_4.value()
            dec = self.doubleSpinBox_j_dec_4.value()
            smh = self.doubleSpinBox_j_smh_4.value()
            ctr_card.axis_jog(4, 1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_c_move_4":
            vel = self.doubleSpinBox_j_vel_4.value()
            acc = self.doubleSpinBox_j_acc_4.value()
            dec = self.doubleSpinBox_j_dec_4.value()
            smh = self.doubleSpinBox_j_smh_4.value()
            ctr_card.axis_jog(4, -1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_f_move_5":
            vel = self.doubleSpinBox_j_vel_5.value()
            acc = self.doubleSpinBox_j_acc_5.value()
            dec = self.doubleSpinBox_j_dec_5.value()
            smh = self.doubleSpinBox_j_smh_5.value()
            ctr_card.axis_jog(5, 1, vel, acc, dec, smh)
        elif self.sender().objectName() == "pushButton_c_move_5":
            vel = self.doubleSpinBox_j_vel_5.value()
            acc = self.doubleSpinBox_j_acc_5.value()
            dec = self.doubleSpinBox_j_dec_5.value()
            smh = self.doubleSpinBox_j_smh_5.value()
            ctr_card.axis_jog(5, -1, vel, acc, dec, smh)

    def on_j_move_released(self):
        if self.sender().objectName() == "pushButton_f_move_1":
            ctr_card.axis_jog(1, 0)
        elif self.sender().objectName() == "pushButton_c_move_1":
            ctr_card.axis_jog(1, 0)
        elif self.sender().objectName() == "pushButton_f_move_2":
            ctr_card.axis_jog(2, 0)
        elif self.sender().objectName() == "pushButton_c_move_2":
            ctr_card.axis_jog(2, 0)
        elif self.sender().objectName() == "pushButton_f_move_3":
            ctr_card.axis_jog(3, 0)
        elif self.sender().objectName() == "pushButton_c_move_3":
            ctr_card.axis_jog(3, 0)
        elif self.sender().objectName() == "pushButton_f_move_4":
            ctr_card.axis_jog(4, 0)
        elif self.sender().objectName() == "pushButton_c_move_4":
            ctr_card.axis_jog(4, 0)
        elif self.sender().objectName() == "pushButton_f_move_5":
            ctr_card.axis_jog(5, 0)
        elif self.sender().objectName() == "pushButton_c_move_5":
            ctr_card.axis_jog(5, 0)

    def on_trap_start(self):
        if self.sender().objectName() == "pushButton_trap_start_1":
            pos = self.spinBox_trap_step_1.value()
            vel = self.doubleSpinBox_trap_vel_1.value()
            acc = self.doubleSpinBox_trap_acc_1.value()
            dec = self.doubleSpinBox_trap_dec_1.value()
            vel_start = self.doubleSpinBox_trap_vel_start_1.value()
            smooth_time = self.spinBox_trap_smooth_time_1.value()
            ctr_card.axis_trap_move(1, pos=pos, vel=vel, acc=acc, dec=dec, velStart=vel_start, smoothTime=smooth_time)
        elif self.sender().objectName() == "pushButton_trap_start_2":
            pos = self.spinBox_trap_step_2.value()
            vel = self.doubleSpinBox_trap_vel_2.value()
            acc = self.doubleSpinBox_trap_acc_2.value()
            dec = self.doubleSpinBox_trap_dec_2.value()
            vel_start = self.doubleSpinBox_trap_vel_start_2.value()
            smooth_time = self.spinBox_trap_smooth_time_2.value()
            ctr_card.axis_trap_move(2, pos=pos, vel=vel, acc=acc, dec=dec, velStart=vel_start, smoothTime=smooth_time)
        elif self.sender().objectName() == "pushButton_trap_start_3":
            pos = self.spinBox_trap_step_3.value()
            vel = self.doubleSpinBox_trap_vel_3.value()
            acc = self.doubleSpinBox_trap_acc_3.value()
            dec = self.doubleSpinBox_trap_dec_3.value()
            vel_start = self.doubleSpinBox_trap_vel_start_3.value()
            smooth_time = self.spinBox_trap_smooth_time_3.value()
            ctr_card.axis_trap_move(3, pos=pos, vel=vel, acc=acc, dec=dec, velStart=vel_start, smoothTime=smooth_time)
        elif self.sender().objectName() == "pushButton_trap_start_4":
            pos = self.spinBox_trap_step_4.value()
            vel = self.doubleSpinBox_trap_vel_4.value()
            acc = self.doubleSpinBox_trap_acc_4.value()
            dec = self.doubleSpinBox_trap_dec_4.value()
            vel_start = self.doubleSpinBox_trap_vel_start_4.value()
            smooth_time = self.spinBox_trap_smooth_time_4.value()
            ctr_card.axis_trap_move(4, pos=pos, vel=vel, acc=acc, dec=dec, velStart=vel_start, smoothTime=smooth_time)
        elif self.sender().objectName() == "pushButton_trap_start_5":
            pos = self.spinBox_trap_step_5.value()
            vel = self.doubleSpinBox_trap_vel_5.value()
            acc = self.doubleSpinBox_trap_acc_5.value()
            dec = self.doubleSpinBox_trap_dec_5.value()
            vel_start = self.doubleSpinBox_trap_vel_start_5.value()
            smooth_time = self.spinBox_trap_smooth_time_5.value()
            ctr_card.axis_trap_move(5, pos=pos, vel=vel, acc=acc, dec=dec, velStart=vel_start, smoothTime=smooth_time)

    def on_home_clicked(self):
        if self.sender().objectName() == "pushButton_home_1":
            ctr_card.axis_home(1)
        elif self.sender().objectName() == "pushButton_home_2":
            ctr_card.axis_home(2)
        elif self.sender().objectName() == "pushButton_home_3":
            ctr_card.axis_home(3)
        elif self.sender().objectName() == "pushButton_home_4":
            ctr_card.axis_home(4)
        elif self.sender().objectName() == "pushButton_home_5":
            ctr_card.axis_home(5)

    def on_axis_status_changed(self, index, sts):
        pass

    def on_axis_zero_pos_clicked(self):
        if self.sender().objectName() == "pushButton_axis_zero_pos_1":
            ctr_card.axis_zero_pos(1)
        elif self.sender().objectName() == "pushButton_axis_zero_pos_2":
            ctr_card.axis_zero_pos(2)
        elif self.sender().objectName() == "pushButton_axis_zero_pos_3":
            ctr_card.axis_zero_pos(3)
        elif self.sender().objectName() == "pushButton_axis_zero_pos_4":
            ctr_card.axis_zero_pos(4)
        elif self.sender().objectName() == "pushButton_axis_zero_pos_5":
            ctr_card.axis_zero_pos(5)

    def on_axis_stop_clicked(self):
        if self.sender().objectName() == "pushButton_axis_stop_1":
            ctr_card.axis_stop(1)
        elif self.sender().objectName() == "pushButton_axis_stop_2":
            ctr_card.axis_stop(2)
        elif self.sender().objectName() == "pushButton_axis_stop_3":
            ctr_card.axis_stop(3)
        elif self.sender().objectName() == "pushButton_axis_stop_4":
            ctr_card.axis_stop(4)
        elif self.sender().objectName() == "pushButton_axis_stop_5":
            ctr_card.axis_stop(5)

    def on_axis_prf_pos_changed(self, index, prf_pos):
        if index == 1:
            self.lineEdit_prf_pos_1.setText(str(prf_pos))
        elif index == 2:
            self.lineEdit_prf_pos_2.setText(str(prf_pos))
        elif index == 3:
            self.lineEdit_prf_pos_3.setText(str(prf_pos))
        elif index == 4:
            self.lineEdit_prf_pos_4.setText(str(prf_pos))
        elif index == 5:
            self.lineEdit_prf_pos_5.setText(str(prf_pos))

    def on_axis_enc_pos_changed(self, index, enc_pos):
        if index == 1:
            self.lineEdit_enc_pos_1.setText(str(enc_pos))
        elif index == 2:
            self.lineEdit_enc_pos_2.setText(str(enc_pos))
        elif index == 3:
            self.lineEdit_enc_pos_3.setText(str(enc_pos))
        elif index == 4:
            self.lineEdit_enc_pos_4.setText(str(enc_pos))
        elif index == 5:
            self.lineEdit_enc_pos_5.setText(str(enc_pos))

    def on_axis_e_enc_pos_changed(self, index, e_enc_pos):
        if index == 1:
            self.lineEdit_e_enc_pos_1.setText(str(e_enc_pos))
        elif index == 2:
            self.lineEdit_e_enc_pos_2.setText(str(e_enc_pos))
        elif index == 3:
            self.lineEdit_e_enc_pos_3.setText(str(e_enc_pos))
        elif index == 4:
            self.lineEdit_e_enc_pos_4.setText(str(e_enc_pos))
        elif index == 5:
            self.lineEdit_e_enc_pos_5.setText(str(e_enc_pos))

    def on_in_values_changed(self, old_values, new_values):
        self.label_in_start_s.setText(str(new_values[0]))
        self.label_in_pause_s.setText(str(new_values[1]))
        self.label_in_reset_s.setText(str(new_values[2]))
        self.label_in_e_stop_s.setText(str(new_values[3]))
        self.label_in_vacuum1_s.setText(str(new_values[4]))
        self.label_in_a_pressure_s.setText(str(new_values[5]))
        self.label_in_figer1_s.setText(str(new_values[6]))
        self.label_in_figer2_s.setText(str(new_values[7]))
        self.label_in_figer3_s.setText(str(new_values[8]))
        self.label_in_figer4_s.setText(str(new_values[9]))
        self.label_in_figer5_s.setText(str(new_values[10]))
        self.label_in_figer6_s.setText(str(new_values[11]))
        self.label_in_figer7_s.setText(str(new_values[12]))
        self.label_in_figer8_s.setText(str(new_values[13]))
        self.label_in_figer9_s.setText(str(new_values[14]))
        self.label_in_vacuum2_s.setText(str(new_values[15]))

    def on_out_values_changed(self, old_values, new_values):
        self.label_out_figer1_s.setText(str(new_values[0]))
        self.label_out_figer2_s.setText(str(new_values[1]))
        self.label_out_figer3_s.setText(str(new_values[2]))
        self.label_out_figer4_s.setText(str(new_values[3]))
        self.label_out_vacuum1_i_s.setText(str(new_values[4]))
        self.label_out_vacuum1_o_s.setText(str(new_values[5]))
        self.label_out_vacuum2_i_s.setText(str(new_values[6]))
        self.label_out_figer5_s.setText(str(new_values[9]))
        self.label_out_figer6_s.setText(str(new_values[10]))
        self.label_out_figer7_s.setText(str(new_values[11]))
        self.label_out_figer8_s.setText(str(new_values[12]))
        self.label_out_figer9_s.setText(str(new_values[13]))
        self.label_out_pause_light_s.setText(str(new_values[14]))
        self.label_out_vacuum2_o_s.setText(str(new_values[15]))

        self.label_out_buzzer_s.setText(str(new_values[19]))
        self.label_out_red_s.setText(str(new_values[16]))
        self.label_out_green_s.setText(str(new_values[17]))
        self.label_out_yellow_s.setText(str(new_values[18]))

    def on_out_value_clicked(self):
        if self.sender().objectName() == "pushButton_out_figer1_s":
            if self.label_out_figer1_s.text() == "0":
                ctr_card.io.set_output_by_bit(0, 0, 1)
            elif self.label_out_figer1_s.text() == "1":
                ctr_card.io.set_output_by_bit(0, 0, 0)
        elif self.sender().objectName() == "pushButton_out_figer2_s":
            if self.label_out_figer2_s.text() == "0":
                ctr_card.io.set_output_by_bit(0, 1, 1)
            elif self.label_out_figer2_s.text() == "1":
                ctr_card.io.set_output_by_bit(0, 1, 0)
        elif self.sender().objectName() == "pushButton_out_figer3_s":
            if self.label_out_figer3_s.text() == "0":
                ctr_card.io.set_output_by_bit(0, 2, 1)
            elif self.label_out_figer3_s.text() == "1":
                ctr_card.io.set_output_by_bit(0, 2, 0)
        elif self.sender().objectName() == "pushButton_out_figer4_s":
            if self.label_out_figer4_s.text() == "0":
                ctr_card.io.set_output_by_bit(0, 3, 1)
            elif self.label_out_figer4_s.text() == "1":
                ctr_card.io.set_output_by_bit(0, 3, 0)
        elif self.sender().objectName() == "pushButton_out_vacuum1_i_s":
            if self.label_out_vacuum1_i_s.text() == "0":
                ctr_card.io.set_output_by_bit(0, 4, 1)
            elif self.label_out_vacuum1_i_s.text() == "1":
                ctr_card.io.set_output_by_bit(0, 4, 0)
        elif self.sender().objectName() == "pushButton_out_vacuum1_o_s":
            if self.label_out_vacuum1_o_s.text() == "0":
                ctr_card.io.set_output_by_bit(0, 5, 1)
            elif self.label_out_vacuum1_o_s.text() == "1":
                ctr_card.io.set_output_by_bit(0, 5, 0)
        elif self.sender().objectName() == "pushButton_out_vacuum2_i_s":
            if self.label_out_vacuum2_i_s.text() == "0":
                ctr_card.io.set_output_by_bit(0, 6, 1)
            elif self.label_out_vacuum2_i_s.text() == "1":
                ctr_card.io.set_output_by_bit(0, 6, 0)
        elif self.sender().objectName() == "pushButton_out_figer5_s":
            if self.label_out_figer5_s.text() == "0":
                ctr_card.io.set_output_by_bit(0, 9, 1)
            elif self.label_out_figer5_s.text() == "1":
                ctr_card.io.set_output_by_bit(0, 9, 0)
        elif self.sender().objectName() == "pushButton_out_figer6_s":
            if self.label_out_figer6_s.text() == "0":
                ctr_card.io.set_output_by_bit(0, 10, 1)
            elif self.label_out_figer6_s.text() == "1":
                ctr_card.io.set_output_by_bit(0, 10, 0)
        elif self.sender().objectName() == "pushButton_out_figer7_s":
            if self.label_out_figer7_s.text() == "0":
                ctr_card.io.set_output_by_bit(0, 11, 1)
            elif self.label_out_figer7_s.text() == "1":
                ctr_card.io.set_output_by_bit(0, 11, 0)
        elif self.sender().objectName() == "pushButton_out_figer8_s":
            if self.label_out_figer8_s.text() == "0":
                ctr_card.io.set_output_by_bit(0, 12, 1)
            elif self.label_out_figer8_s.text() == "1":
                ctr_card.io.set_output_by_bit(0, 12, 0)
        elif self.sender().objectName() == "pushButton_out_figer9_s":
            if self.label_out_figer9_s.text() == "0":
                ctr_card.io.set_output_by_bit(0, 13, 1)
            elif self.label_out_figer9_s.text() == "1":
                ctr_card.io.set_output_by_bit(0, 13, 0)
        elif self.sender().objectName() == "pushButton_out_pause_light_s":
            if self.label_out_pause_light_s.text() == "0":
                ctr_card.io.set_output_by_bit(0, 14, 1)
            elif self.label_out_pause_light_s.text() == "1":
                ctr_card.io.set_output_by_bit(0, 14, 0)
        elif self.sender().objectName() == "pushButton_out_vacuum2_o_s":
            if self.label_out_vacuum2_o_s.text() == "0":
                ctr_card.io.set_output_by_bit(0, 15, 1)
            elif self.label_out_vacuum2_o_s.text() == "1":
                ctr_card.io.set_output_by_bit(0, 15, 0)

        elif self.sender().objectName() == "pushButton_out_buzzer_s":
            if self.label_out_buzzer_s.text() == "0":
                ctr_card.io.set_output_by_bit(1, 3, 1)
            elif self.label_out_buzzer_s.text() == "1":
                ctr_card.io.set_output_by_bit(1, 3, 0)
        elif self.sender().objectName() == "pushButton_out_red_s":
            if self.label_out_red_s.text() == "0":
                ctr_card.io.set_output_by_bit(1, 0, 1)
            elif self.label_out_red_s.text() == "1":
                ctr_card.io.set_output_by_bit(1, 0, 0)
        elif self.sender().objectName() == "pushButton_out_green_s":
            if self.label_out_green_s.text() == "0":
                ctr_card.io.set_output_by_bit(1, 1, 1)
            elif self.label_out_green_s.text() == "1":
                ctr_card.io.set_output_by_bit(1, 1, 0)
        elif self.sender().objectName() == "pushButton_out_yellow_s":
            if self.label_out_yellow_s.text() == "0":
                ctr_card.io.set_output_by_bit(1, 2, 1)
            elif self.label_out_yellow_s.text() == "1":
                ctr_card.io.set_output_by_bit(1, 2, 0)
