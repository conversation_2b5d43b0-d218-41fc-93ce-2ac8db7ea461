from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QDialog


class BaseDialog(QDialog):

    def __init__(self, parent=None):
        super(BaseDialog, self).__init__(parent)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog | Qt.SubWindow)
        # self.setWindowModality(Qt.ApplicationModal)
        self.setAttribute(Qt.WA_TranslucentBackground, True)

    def show_dialog(self):
        if not self.isVisible():
            self.show()

    def dismiss_dialog(self):
        if self.isVisible():
            self.hide()

    def close_dialog(self):
        self.close()
