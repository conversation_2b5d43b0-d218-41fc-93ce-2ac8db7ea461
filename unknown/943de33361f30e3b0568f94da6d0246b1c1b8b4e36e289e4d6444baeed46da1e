import time
import traceback

import can

from common.LogUtils import logger
from utils.CRC import seed_to_key4
from adb.AdbConnectDevice import adb_connect_device
from adb.util.dllFunction import Service27
from adb.zlgcan.zlgcan import ZCAN_TransmitFD_Data, ZCAN_Transmit_Data


class canAdvancedTool():
    def __init__(self):
        self.device = adb_connect_device
        self.step_data = "00 00 00 00"

    @staticmethod
    def dlc2len(dlc):
        if dlc <= 8:
            return dlc
        elif dlc == 9:
            return 12
        elif dlc == 10:
            return 16
        elif dlc == 11:
            return 20
        elif dlc == 12:
            return 24
        elif dlc == 13:
            return 32
        elif dlc == 14:
            return 48
        else:
            return 64

    def set_message(self, can_dict):
        try:
            if self.device.can_bus.name == "zlg":
                is_canfd_msg = True if self.device.can_bus.can_type.lower() == "canfd" else False
                if is_canfd_msg:
                    msg = ZCAN_TransmitFD_Data()
                else:
                    msg = ZCAN_Transmit_Data()
                msg.transmit_type = 0  # 正常发送 1 单次发送 2自发自收
                try:
                    msg.frame.can_id = int(can_dict["id"], 16)
                except Exception as e:
                    print(str(e.args))
                    msg.frame.can_id = 0
                msg.frame.rtr = 0  # ("数据帧", "远程帧")
                msg.frame.eff = 0  # ("标准帧", "扩展帧")
                data = can_dict["msg"].split(' ')
                msg_len = len(data)
                if not is_canfd_msg:
                    msg.frame.can_dlc = msg_len
                    msg_len = msg.frame.can_dlc
                else:
                    msg.frame.brs = 0  # can canfd 0，"CANFD BRS" 1
                    msg.frame.len = canAdvancedTool.dlc2len(msg_len)
                    msg_len = msg.frame.len

                for i in range(msg_len):
                    if i < len(data):
                        try:
                            msg.frame.data[i] = int(data[i], 16)
                        except Exception as e:
                            print(str(e.args))
                            msg.frame.data[i] = 0
                    else:
                        msg.frame.data[i] = 0
                return msg
            elif self.device.can_bus.name == "canoe":
                if self.device.can_bus.can_type.lower() == "can":
                    msg = can.Message(
                        arbitration_id=int(can_dict["id"], 16),
                        is_extended_id=False,
                        dlc=len(can_dict["msg"].strip().split(" ")),
                        data=[int(i, 16) for i in can_dict["msg"].strip().split(" ")]
                    )
                    return msg
                else:
                    msg = can.Message(
                        arbitration_id=int(can_dict["id"], 16),
                        is_extended_id=False,
                        is_fd=True,
                        data=[int(i, 16) for i in can_dict["msg"].strip().split(" ")]
                    )
                    return msg
            elif self.device.can_bus.name.lower() == "tsmaster":
                msg = can.Message(
                    arbitration_id=int(can_dict["id"], 16),
                    is_extended_id=False,
                    is_fd=self.device.can_bus.is_fd,
                    channel=self.device.can_bus.channel,
                    data=[int(i, 16) for i in can_dict["msg"].strip().split(" ")]
                )
                return msg
            else:
                # print("can_dict:{}".format(can_dict))
                msg = can.Message(
                    arbitration_id=int(can_dict["id"], 16),
                    is_extended_id=False,
                    dlc=len(can_dict["msg"].strip().split(" ")),
                    data=[int(i, 16) for i in can_dict["msg"].strip().split(" ")]
                )
                return msg
        except Exception as e:
            print(str(e.args))
            logger.error("set_message exception: {}".format(traceback.format_exc()))

    # def run_security_level(self, project_code, position,level,send_id,send_msg,recv_id,recv_msg):
    #
    #     return self.lantu(project_code, position,level,send_id,send_msg,recv_id,recv_msg)

    def parse(self, project_code, can_dict):
        start_time = time.time()
        timeout = 3
        if not self.device.can_bus:
            logger.info("no canbus!")
            return
        while time.time() - start_time < timeout:
            recv_msg, success = self.device.can_bus._recv_internal(timeout=3)
            # if recv_msg:
                # logging.info(f"canoe_send_and_read_msg recv_msg={recv_msg}")
            messages = recv_msg if isinstance(recv_msg, list) else [recv_msg]

            for msg in messages:
                self.device.add_can_msgs(msg, "recv")
                if msg and msg.arbitration_id == int(can_dict["recv_id"], 16):
                    converted = ' '.join(format(x, '02X') for x in msg.data)
                    logger.info("converted:{}".format(converted))
                    logger.info(f"cc recv_msg={converted} from {hex(msg.arbitration_id)}")
                    if converted.lower().startswith("03 7f 27 78"):
                        continue
                    if converted.lower().startswith("03 7f 27"):
                        return msg

                    if converted.lower().startswith(can_dict["expect"].lower()):
                        # return "20 03 63 03 00 03 02 00"
                        return msg

    def run_security_level(self, project_code, position, level, send_id, send_msg, recv_id, recv_msg, channel):

        m = send_msg
        send_msg = " ".join((m.split() + ["00"] * 8)[:8])
        can_dict = {
            "id": send_id,
            "msg": send_msg,
            "can_type": "",
            "cycle_period": 0,
            "uds": 0,
            "recv_id": recv_id,
            "expect": recv_msg,
            "projectNo": project_code,
            "channel": channel
        }

        self.send(can_dict)
        print("run_security_level send can_dict:", can_dict)
        logger.info("run_security_level send can_dict:", can_dict)
        msg = self.parse(project_code, can_dict)
        print("recv msg:", msg)
        logger.info(f"run_security_level recv msg:{msg}")
        if not msg:
            return False, "None"
        ret_data = ' '.join(format(x, '02X') for x in msg.data)
        if ret_data.lower().startswith("03 7f 27"):
            return False, ret_data
        if ret_data.startswith(recv_msg.upper()):
            seed = [msg.data[3], msg.data[4], msg.data[5], msg.data[6]]
            # result = []
            project_code = project_code.upper()
            service27 = Service27()
            result = service27.XCP_ComputeKeyFromSeed_All(int(level), seed, project_code, position)

            tmp = " ".join([format(x, '02X') for x in result]).replace("0x", "").replace("0X", "")
            self.step_data = tmp
            logger.info("project_code {} seed is {} self.step_data if {} ".format(project_code,seed, self.step_data))
            # print(" ret_data {} tmp is {}".format(ret_data,tmp))
            return True, ret_data
        else:
            return False, ret_data
        # result = seed_to_key4(0x4, msg.data[3:7], position=position)
        # # crc_value = crc32(result)

        # data = f'06 50 03 00 32 01 F4 CC'

        # return False, "result"
        # can_dict["msg"] = data

    def send(self, can_dict):
        msg = self.set_message(can_dict)
        if not self.device.can_bus:
            logger.info("no canbus!")
            return
        print("send msg is {}".format(msg))
        self.device.add_can_msgs(can_dict)
        if self.device.can_bus.name.lower() == "zlg":
            self.device.can_bus.send(can_dict)
        else:
            self.device.can_bus.send(msg)

    def enter_level(self, project_code, position, level, send_id, send_msg, recv_id, recv_msg, channel):
        # can_dict = self.step_data
        m = send_msg + " " + self.step_data
        send_msg = " ".join((m.split() + ["00"] * 8)[:8])

        can_dict = {
            "id": send_id,
            "msg": send_msg,
            "can_type": "",
            "cycle_period": 0,
            "uds": 0,
            "recv_id": recv_id,
            "expect": recv_msg,
            "projectNo": project_code,
            "channel": channel
        }

        self.send(can_dict)
        logger.info("enter_level send can_dict:", can_dict)
        msg = self.parse(project_code, can_dict)
        if not msg:
            return False, "NO Response"
        converted = ' '.join(format(x, '02X') for x in msg.data)
        if converted.lower().startswith(recv_msg.lower()):
            return True, converted
        return False, converted


can_tool = canAdvancedTool()
if __name__ == '__main__':

    # can = canAdvancedTool()
    # can.run_security_level('ILTCF07')
    position = "left"


    def crc32(buf):
        if position.lower() == "left":
            Poly = 0xC97D5C05
        else:
            Poly = 0x25ED2FBA

        crc = 0xFFFFFFFF
        length = len(buf)
        for i in range(length):
            crc = crc ^ buf[i]
            for j in range(7, -1, -1):
                mask = -(crc & 1)
                crc = ((crc >> 1) ^ (Poly & mask)) % (1 << 32)
        return ~crc % (1 << 32)


    seed = [0x55, 0x97, 0x9d, 0xbe]
    result = seed_to_key4(4, seed, position="right")
    # crc_value = crc32(result)
    tmp = " ".join([hex(x) for x in result]).replace("0x", "").replace("0X", "")

    data = f'{tmp}'
