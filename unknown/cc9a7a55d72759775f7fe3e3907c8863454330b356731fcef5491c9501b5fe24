# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/9/29 15:36
@Desc   : 
"""
from enum import Enum


class TouchHeaderStatus(Enum):
    UP = "抬起"
    DOWN = "按下"
    ERROR = "异常"


class InspireTouchHeader:

    def __init__(self, header_index):
        super().__init__()
        self.header_status = TouchHeaderStatus.UP
        self.header_index = header_index

    def set_header_status(self, status):
        self.header_status = status

    def set_header_index(self, index):
        self.header_index = index
