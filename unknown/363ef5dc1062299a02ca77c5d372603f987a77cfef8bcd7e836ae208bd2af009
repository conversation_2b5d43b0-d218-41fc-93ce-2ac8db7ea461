# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/2/21 10:27
@Desc   : 测试工站管理模块
"""
import threading

from common.LogUtils import logger
from control_board.auto_test_m.station import station
from utils.SignalsManager import signals_manager


class StationManager:

    def __init__(self):
        super().__init__()
        self.test_timer = None
        self.reset_timer = None
        self.pause_timer = None
        self.test_position_timer = None

    def init_station(self):
        """
        初始化测试工站，控制载具运动到上料位
        """
        logger.info(f"init_station")
        try:
            threading.Thread(target=station.start).start()
        except Exception as e:
            logger.error("init_station exception: {}".format(str(e.args)))

        self.detect_station_start()
        self.detect_station_reset()

    def detect_station_start(self):
        """
        开启开始测试信号监测
        """
        if self.test_timer is not None and self.test_timer.is_alive():
            self.test_timer.cancel()

        self.test_timer = threading.Timer(interval=1, function=self.detect_station_start)
        self.test_timer.start()
        logger.debug("detect_station_start test_triggered_flag={}".format(station.test_triggered_flag))
        if station.test_triggered_flag == 1:
            logger.info(" signals_manager.test_state_signal detect_station_start")
            # 开始测试
            signals_manager.test_state_signal.emit("start_test")
            # 重置开始测试标识符
            station.test_triggered_flag = 0


            # self.adjust_test_position(0, 0, 0, 0, 0)

    # def detect_station_pause(self):
    #     """
    #     开启开始测试信号监测
    #     """
    #     if self.pause_timer is not None and self.pause_timer.is_alive():
    #         self.pause_timer.cancel()
    #
    #     self.pause_timer = threading.Timer(interval=1, function=self.detect_station_pause)
    #     self.pause_timer.start()
    #     logger.debug("detect_station_pause _test_pause_flag={}".format(station._test_pause_flag))
    #     if station._test_pause_flag == 1:
    #         logger.info(" signals_manager.test_state_signal detect_station_start")
    #         # 开始测试
    #         signals_manager.test_state_signal.emit("pause_test")
    #         # 重置开始测试标识符
    #         station._test_pause_flag = 0

    def detect_station_reset(self):
        """
        开启复位测试信号监测
        """
        if self.reset_timer is not None and self.reset_timer.is_alive():
            self.reset_timer.cancel()

        self.reset_timer = threading.Timer(interval=1, function=self.detect_station_reset)
        self.reset_timer.start()

        if station.reset_result_flag == 1:
            # 复位成功
            signals_manager.test_state_signal.emit("reset_success")
            logger.info(" signals_manager.test_state_signal reset_success")

            # 重置复位标识符
            station.reset_result_flag = 0
            self.adjust_test_position(0, 0, 0, 0, 0)

    def detect_test_position_adjust(self):
        """
        开始测试点位调整信号监测
        """
        if self.test_position_timer is not None and self.test_position_timer.is_alive():
            self.test_position_timer.cancel()

        self.test_position_timer = threading.Timer(interval=1, function=self.detect_test_position_adjust)
        self.test_position_timer.start()

        if station.p_adjust_result_flag == 1:
            # 测试点位调整成功
            signals_manager.test_state_signal.emit("test_position_adjust_success")
            # 重置测试点位调整标识符
            station.p_adjust_result_flag = 0

    @staticmethod
    def trigger_test_over():
        """
        通知运动控制模块测试结束，将载具回退到上料位
        """
        logger.info("trigger_test_over")
        station.test_result_flag = 1

    @staticmethod
    def trigger_start():
        """
        通知运动控制模块开始
        """
        logger.info("trigger_start")
        station.test_triggered_flag2 = 1

    @staticmethod
    def trigger_stop():
        """
        通知运动控制模块停止
        """
        logger.info("trigger_stop")

    @staticmethod
    def trigger_reset():
        """
        通知运动控制模块复位
        """
        logger.info("trigger_reset")
        station.reset_triggered_flag = 1

    @staticmethod
    def adjust_test_position(x, y, z1, z2, r):
        """
        调整测试点位
        @param x:
        @param y:
        @param z1:
        @param z2:
        @param r:
        """
        station.x = x
        station.y = y
        station.z1 = z1
        station.z2 = z2
        station.r = r
        station.p_adjust_triggered_flag = 1


station_manager: StationManager = StationManager()

if __name__ == '__main__':
    station_manager.detect_station_start()
    station_manager.detect_station_reset()
