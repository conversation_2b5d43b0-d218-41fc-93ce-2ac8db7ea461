import binascii
import traceback

import isotp
from can import BusABC
from can import Message
from udsoncan.connections import PythonIsoTpConnection

from adb.zlgcan.zlgcan import ZCAN, ZCAN_USBCANFD_200U, INVALID_DEVICE_HANDLE, ZCAN_STATUS_OK, ZCAN_CHANNEL_INIT_CONFIG, \
    ZCAN_TYPE_CANFD, ZCAN_Transmit_Data, ZCAN_TYPE_CAN


class Zlgcanlib(BusABC):

    def __init__(self):

        self.zcanlib = ZCAN()
        self.open_device()
        self.open_channel()

    def open_device(self):
        self.device_handle = self.zcanlib.OpenDevice(ZCAN_USBCANFD_200U, 0, 0)
        if self.device_handle == INVALID_DEVICE_HANDLE:
            print("Open Device failed!")
            # exit(0)
        print("device handle 33:%d." % (self.device_handle))
        # info = zcanlib.GetDeviceInf(device_handle)
        # print("Device Information:\n%s" %(info))
        # return device_handle

    def open_channel(self):
        ip = self.zcanlib.GetIProperty(self.device_handle)
        ret = self.zcanlib.SetValue(ip, str(0) + "/canfd_abit_baud_rate", "500000")  # 设置仲裁波特率
        # # ret = zcanlib.SetValue(ip, str(chn) + "/canfd_dbit_baud_rate", "2000000")  # 设置数据波特率
        if ret != ZCAN_STATUS_OK:
            print("Set CH%d baud failed!" % (0))

        chn_init_cfg = ZCAN_CHANNEL_INIT_CONFIG()
        chn_init_cfg.can_type = ZCAN_TYPE_CANFD

        self.chn_handle = self.zcanlib.InitCAN(self.device_handle, 0, chn_init_cfg)
        if self.chn_handle is None:
            return None

        self.zcanlib.StartCAN(self.chn_handle)
        # return chn_handle

    def transmit_can(self, chn_handle, msg):
        msgs = ZCAN_Transmit_Data()
        msgs.transmit_type = 0  # 0-正常发送，2-自发自收
        msgs.frame.eff = 0  # 0-标准帧，1-扩展帧
        msgs.frame.rtr = 0  # 0-数据帧，1-远程帧
        msgs.frame.can_id = msg.arbitration_id
        msgs.frame.can_dlc = msg.dlc
        for j in range(msg.dlc):
            msgs.frame.data[j] = msg.data[j]
        print("发送的数据：", msg.data)
        ret = self.zcanlib.Transmit(chn_handle, msgs, 1)
        print("Tranmit Num: %d." % ret)

    def receive_can(self, chn_handle):
        rcv_num = self.zcanlib.GetReceiveNum(chn_handle, ZCAN_TYPE_CAN)
        # rcv_num = self.zcanlib.GetReceiveNum(chn_handle, ZCAN_TYPE_CANFD)
        # print("rcv_num",rcv_num)
        if rcv_num:
            print("Receive CAN message number:%d" % rcv_num)
            rcv_msg, rcv_num = self.zcanlib.Receive(chn_handle, rcv_num)
            for i in range(rcv_num):
                print("[%d]:ts:%d, id:0x%x, dlc:%d, eff:%d, rtr:%d, data:%s" % (i, rcv_msg[i].timestamp,
                                                                                rcv_msg[i].frame.can_id,
                                                                                rcv_msg[i].frame.can_dlc,
                                                                                rcv_msg[i].frame.eff,
                                                                                rcv_msg[i].frame.rtr,
                                                                                ''.join(hex(rcv_msg[i].frame.data[j])[
                                                                                        2:] + ' ' for j in range(
                                                                                    rcv_msg[i].frame.can_dlc))))

                msg = Message(
                    timestamp=rcv_msg[i].timestamp,
                    arbitration_id=rcv_msg[i].frame.can_id,
                    is_extended_id=False,
                    is_remote_frame=False,
                    is_error_frame=False,
                    channel=0,
                    dlc=rcv_msg[i].frame.can_dlc,
                    data=rcv_msg[i].frame.data[:rcv_msg[i].frame.can_dlc],
                    is_fd=True
                )
                # clear_buffer = zcanlib.ClearBuffer(chn_handle)
                return msg

    def send(self, msg, timeout=None):
        # print("send: ",msg)
        # msg.is_rx = False
        self.transmit_can(self.chn_handle, msg)

    def _recv_internal(self, timeout):
        msg = self.receive_can(self.chn_handle)
        return msg, True

    def close_channel_device(self, dev_handle, chn_handle):
        close_channel = self.zcanlib.ResetCAN(chn_handle)
        if close_channel:
            print("关闭通道成功")
        close_device = self.zcanlib.CloseDevice(dev_handle)
        if close_device:
            print("关闭设备成功")


class ZlgCanBus:

    def __init__(self):

        self.can_bus = Zlgcanlib()

    def open_uds(self, tx_id=int("636", 16), rx_id=int("6b6", 16)):
        """can总线相关配置"""
        if isinstance(tx_id, str) or isinstance(rx_id, str):
            tx_id = eval(tx_id)
            rx_id = eval(rx_id)
        iso_tp_params = {
            'stmin': 32,
            # Will request the sender to wait 32ms between consecutive frame. 0-127ms or 100-900ns with values from 0xF1-0xF9
            'blocksize': 0,
            # Request the sender to send 8 consecutives frames before sending a new flow control message
            'wftmax': 0,  # Number of wait frame allowed before triggering an error
            'tx_data_length': 8,  # Link layer (CAN layer) works with 8 byte payload (CAN 2.0)
            'tx_data_min_length': None,
            # Minimum length of CAN messages. When different from None, messages are padded to meet this length. Works with CAN 2.0 and CAN FD.
            'tx_padding': 0,  # Will pad all transmitted CAN messages with byte 0x00.
            'rx_flowcontrol_timeout': 1000,
            # Triggers a timeout if a flow control is awaited for more than 1000 milliseconds
            'rx_consecutive_frame_timeout': 1000,
            # Triggers a timeout if a consecutive frame is awaited for more than 1000 milliseconds
            'squash_stmin_requirement': False,
            # When sending, respect the stmin requirement of the receiver. If set to True, go as fast as possible.
            'max_frame_size': 4095  # Limit the size of receive frame.
        }

        try:
            tp_addr = isotp.Address(isotp.AddressingMode.Normal_11bits, txid=tx_id, rxid=rx_id)  # 网络层寻址方法
            tp_stack = isotp.CanStack(bus=self.can_bus, address=tp_addr, params=iso_tp_params)  # 网络/传输层（IsoTP 协议）
            self.uds_conn = PythonIsoTpConnection(tp_stack)  # 应用层和传输层之间建立连接
            self.uds_conn.mtu = 4098
            self.uds_conn.open()
            print(f'open uds {hex(tx_id)}, {hex(rx_id)}')
        except Exception as e:
            print(e.args)
            return False
        else:
            return True

    def close_uds(self):
        if self.uds_conn.is_open():
            self.uds_conn.close()

    def uds_request_respond(self, command):
        """发送uds请求和接收uds响应"""
        if self.uds_conn is None or not self.uds_conn.is_open():
            return False, 'uds is not open'
        if not isinstance(command, str):  # 判断command数据类型
            command = str(int(command))
        request_pdu = binascii.a2b_hex(command.replace(' ', ''))  # 处理command
        # 耗时统计
        try:
            # rs = self.uds_conn.specific_send(request_pdu)  # 发送uds请求
            rs = self.uds_conn.send(request_pdu)  # 发送uds请求
            # payload = self.uds_conn.wait_frame(timeout=3)
            # print("pay'load:",payload)

        except Exception as e:
            print("发送请求失败", str(e.args))
        else:
            print('UDS发送请求：%s' % request_pdu)
        try:
            resp_pdu = self.uds_conn.specific_wait_frame(timeout=1)  # 接收uds响应
        except Exception as e:
            print(traceback.format_exc())
            print('响应数据失败', str(e.args))
            return False, str(e.args)
        else:
            res = resp_pdu.hex().upper()
            respond = ''
            for i in range(len(res)):
                if i % 2 == 0:
                    respond += res[i]
                else:
                    respond += res[i] + ' '
            print('UDS响应结果：%s' % respond)
            return True, respond.strip()

