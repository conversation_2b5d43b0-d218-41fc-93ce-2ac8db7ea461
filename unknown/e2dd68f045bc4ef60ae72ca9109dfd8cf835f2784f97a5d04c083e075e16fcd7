import time
import traceback
import logging

from PyQt5.QtCore import QObject, pyqtSignal

from utils.SignalsManager import signals_manager
from ..common.worker_thread import WorkerThread
from ..common.exceptions import ThreadStopException
from ..common.log import get_logger
from .ctr_card import ctr_card
from .constants import X, Y, Z1, Z2, R

logger = get_logger("auto_test_m")
logger_d = get_logger("auto_test_m_debug", logging.DEBUG)


class Station(QObject):
    test_triggered_signal = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent=parent)

        self.test_triggered_flag = 0
        self.test_result_flag = 0

        self.test_triggered_flag2 = 0

        self.p_adjust_triggered_flag = 0
        self.p_adjust_result_flag = 0

        self.reset_triggered_flag = 0
        self.reset_result_flag = 0

        self.x = None
        self.y = None
        self.z1 = None
        self.z2 = None
        self.r = None

        self.home_x = 0
        self.home_y = 83270
        self.home_z1 = 0
        self.home_z2 = 0
        self.home_r = 0

        self._test_start_flag = 0
        self._test_pause_flag = 0
        self._test_stop_flag = 0
        self._reset_flag = 0

        self.worker = None

        self._is_start = False

        ctr_card.io.in_value_changed_signal.connect(self.on_in_value_changed)
        ctr_card.io.out_value_changed_signal.connect(self.on_out_value_changed)

    def on_in_value_changed(self, slave, index, old_value, new_value):
        print(slave, index, old_value, new_value)
        logger.info("%s, %s, %s, %s", slave, index, old_value, new_value)

        if slave == 0 and index == 0:
            if old_value == 0 and new_value == 1:
                if self._test_start_flag == 0:
                    self._test_start_flag = 1
        elif slave == 0 and index == 1:
            if old_value == 0 and new_value == 1:
                self._test_pause_flag = 1
                signals_manager.test_state_signal.emit("pause_test")
        elif slave == 0 and index == 2:
            if old_value == 0 and new_value == 1:
                self._reset_flag = 1
        elif slave == 0 and index == 3:
            if old_value == 0 and new_value == 1:
                self._test_stop_flag = 1

    def on_out_value_changed(self, slave, index, old_value, new_value):
        print(slave, index, old_value, new_value)

        if slave == 0 and index == 0:
            pass
        elif slave == 0 and index == 1:
            pass

    def start(self):
        if self._is_start:
            return

        ctr_card.init()

        ctr_card.home()
        while True:
            if ctr_card.home_result == 1:
                break
            time.sleep(0.1)

        self._reset()

        self.worker = WorkerThread(target=self._work)
        self.worker.start()

        self._is_start = True

    def stop(self):
        if self.worker:
            self.worker.stop()
            self.worker = None

        self._is_start = False

    def _reset(self):
        ctr_card.m_axis_trap_move({
            X: self.home_x,
            Y: self.home_y,
            Z1: self.home_z1,
            Z2: self.home_z2,
            R: self.home_r
        })

        while True:
            if ctr_card.m_axis_trap_move_result == 1:
                break
            time.sleep(0.1)

    def _position_adjust(self):
        ctr_card.m_axis_trap_move({
            X: self.x,
            Y: self.y,
            Z1: self.z1,
            Z2: self.z2,
            R: self.r
        })

        while True:
            if ctr_card.m_axis_trap_move_result == 1:
                break
            time.sleep(0.1)

    def clear_flags(self):
        self.test_triggered_flag = 0
        self.test_result_flag = 0

        self.test_triggered_flag2 = 0

        self.p_adjust_triggered_flag = 0
        self.p_adjust_result_flag = 0

        self.reset_triggered_flag = 0
        self.reset_result_flag = 0

        self._test_start_flag = 0
        self._test_pause_flag = 0
        self._test_stop_flag = 0
        self._reset_flag = 0

    def _work(self):
        try:
            logger.info("worker started")
            while True:
                self.clear_flags()

                while True:
                    # logger_d.debug("_test_start_flag %s test_triggered_flag2 %s", self._test_start_flag,
                    #                self.test_triggered_flag2)
                    if self._test_start_flag == 1:
                        logger.info("测试开始被触发")
                        break
                    if self.test_triggered_flag2 == 1:
                        self.test_triggered_flag2 = 0
                        self._test_start_flag = 1
                        logger.info("测试开始被触发")
                        break
                    time.sleep(0.1)

                self.test_triggered_flag = 1

                while True:
                    if self.test_result_flag != 0:
                        self.test_result_flag = 0
                        logger.info("测试结束")
                        self._reset()
                        break

                    if self.reset_triggered_flag != 0:
                        self.reset_triggered_flag = 0
                        logger.info("复位")
                        #
                        self._reset()
                        self.reset_result_flag = 1
                        break

                    if self._reset_flag != 0:
                        self._reset_flag = 0
                        logger.info("复位(按钮)")
                        #
                        self._reset()
                        break

                    if self.p_adjust_triggered_flag != 0:
                        self.p_adjust_triggered_flag = 0
                        logger.info("位置调整")
                        #
                        self._position_adjust()
                        self.p_adjust_result_flag = 1
                        continue

                    time.sleep(0.1)

        except ThreadStopException:
            logger.info("worker stopped")
        except Exception:
            logger.error("worker 工作线程异常！\n%s", traceback.format_exc())

        logger.info("worker finished")


station = Station()
