import serial
from modbus_tk import defines
from modbus_tk.modbus_rtu import RtuMaster


class RTUClient(RtuMaster):
    def __init__(self, port, baud_rate=115200, bytesize=8, parity="N", stop_bits=1, slave_id=1, timeout=2.0):
        self.port = port
        self.baud_rate = baud_rate
        self.bytesize = bytesize
        self.parity = parity
        self.stop_bits = stop_bits
        self.slave_id = slave_id
        self.timeout = timeout

        super().__init__(serial.Serial(port=self.port,
                                       baudrate=self.baud_rate,
                                       bytesize=self.bytesize,
                                       parity=self.parity,
                                       stopbits=self.stop_bits))

        self.set_timeout(self.timeout)

        self.error_tips = None

    def is_open(self):
        return self._is_opened

    def read_hr_one(self, starting_address):
        """保持寄存器单个读取"""
        try:
            data = self.execute(self.slave_id, defines.READ_HOLDING_REGISTERS, starting_address, 1)
            return data[0]
        except Exception as e:
            if self.error_tips is not None:
                self.error_tips(101, u'继电器操作异常:%s' % str(e.args))
            return None

    def read_hr_many(self, starting_address, quantity_of_x):
        """保持寄存器多个读取"""
        try:
            data = self.execute(self.slave_id, defines.READ_HOLDING_REGISTERS, starting_address, quantity_of_x)
            return data
        except Exception as e:
            if self.error_tips is not None:
                self.error_tips(101, u'继电器操作异常:%s' % str(e.args))
            return None

    def read_coil_one(self, starting_address):
        """保持寄存器单个读取"""
        try:
            data = self.execute(self.slave_id, defines.READ_COILS, starting_address, 1)
            return data[0]
        except Exception as e:
            if self.error_tips is not None:
                self.error_tips(101, u'继电器操作异常:%s' % str(e.args))
            return None

    def read_coil_many(self, starting_address, quantity_of_x):
        """保持寄存器多个读取"""
        try:
            data = self.execute(self.slave_id, defines.READ_COILS, starting_address, quantity_of_x)
            return data
        except Exception as e:
            if self.error_tips is not None:
                self.error_tips(101, u'继电器操作异常:%s' % str(e.args))
            return None

    def read_discrete_input_one(self, starting_address):
        """保持寄存器单个读取"""
        try:
            data = self.execute(self.slave_id, defines.READ_DISCRETE_INPUTS, starting_address, 1)
            return data[0]
        except Exception as e:
            if self.error_tips is not None:
                self.error_tips(101, u'继电器操作异常:%s' % str(e.args))
            return None

    def read_discrete_input_many(self, starting_address, quantity_of_x):
        """保持寄存器多个读取"""
        try:
            data = self.execute(self.slave_id, defines.READ_DISCRETE_INPUTS, starting_address, quantity_of_x)
            return data
        except Exception as e:
            if self.error_tips is not None:
                self.error_tips(101, u'继电器操作异常:%s' % str(e.args))
            return None

    def write_hr_one(self, starting_address, value):
        """保持寄存器单个写入"""
        try:
            data = self.execute(self.slave_id, defines.WRITE_SINGLE_REGISTER, starting_address, 1, value)
            return data
        except Exception as e:
            if self.error_tips is not None:
                self.error_tips(101, u'继电器操作异常:%s' % str(e.args))
            return None

    def write_hr_many(self, starting_address, value, data_format=""):
        """保持寄存器多个写入"""
        try:
            data = self.execute(self.slave_id, defines.WRITE_MULTIPLE_REGISTERS,
                                starting_address=starting_address,
                                output_value=value,
                                data_format=data_format)
            return data
        except Exception as e:
            if self.error_tips is not None:
                self.error_tips(101, u'继电器操作异常:%s' % str(e.args))
            return None

    def write_coil_one(self, starting_address, value):
        try:
            data = self.execute(self.slave_id, defines.WRITE_SINGLE_COIL, starting_address, 1, value)
            return data
        except Exception as e:
            if self.error_tips is not None:
                self.error_tips(101, u'继电器操作异常:%s' % str(e.args))
            return None

    def write_coil_many(self, starting_address, value, data_format=""):
        try:
            data = self.execute(self.slave_id, defines.WRITE_MULTIPLE_COILS,
                                starting_address=starting_address,
                                output_value=value,
                                data_format=data_format)
            return data
        except Exception as e:
            if self.error_tips is not None:
                self.error_tips(101, u'继电器操作异常:%s' % str(e.args))
            return None
