%YAML:1.0
---
size: !!opencv-matrix
   rows: 2
   cols: 1
   dt: d
   data: [ 1920., 1080. ]
K1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 1.0863581186669649e+03, 0., 9.2546996659259844e+02, 0.,
       1.0858564623727050e+03, 4.8950851023896388e+02, 0., 0., 1. ]
D1: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ 8.8863669058367220e-02, -1.5119685643687558e-01,
       4.2051606445248503e-04, 2.6694295457013127e-04,
       9.5322223500024172e-02 ]
K2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 1.0886768723500618e+03, 0., 9.7528757602356927e+02, 0.,
       1.0884006225021624e+03, 4.9373113558788128e+02, 0., 0., 1. ]
D2: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ 8.2749096408227404e-02, -1.3594284194125891e-01,
       2.6302331232580674e-04, -1.2789804915713516e-04,
       7.9973882174305733e-02 ]
R: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.9997109100813830e-01, -1.4922153783352196e-03,
       -7.4558997618093910e-03, 1.4453262367694274e-03,
       9.9997917136426051e-01, -6.2902996523440495e-03,
       7.4651309474647121e-03, 6.2793415985781201e-03,
       9.9995241971257087e-01 ]
T: !!opencv-matrix
   rows: 3
   cols: 1
   dt: d
   data: [ -5.9919295597005764e+01, -3.7892511310122795e-01,
       3.2071650902024976e-02 ]
E: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -2.8750795870887496e-03, -3.4450383118733233e-02,
       -3.7870534344091339e-01, 4.7937611165442384e-01,
       3.7620586758909247e-01, 5.9916205496684384e+01,
       2.9231122874312182e-01, -5.9918612997705011e+01,
       3.7408509660203065e-01 ]
F: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -4.4837159226377465e-09, -5.3750544456700561e-08,
       -6.1113660955646148e-04, 7.4778168084425584e-07,
       5.8711691406211230e-07, 1.0055554617108241e-01,
       1.3145660143028406e-04, -1.0201443574255178e-01, 1. ]
R1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.9995609728028090e-01, 4.8281193619991091e-03,
       -8.0307394065338190e-03, -4.8532358130744828e-03,
       9.9998338376193097e-01, -3.1109969754699570e-03,
       8.0155857011235780e-03, 3.1498354663349608e-03,
       9.9996291377350743e-01 ]
R2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.9997986135167061e-01, 6.3237973391720922e-03,
       -5.3523668298529562e-04, -6.3220908126394070e-03,
       9.9997511006642448e-01, 3.1321582653346292e-03,
       5.5503049508399223e-04, -3.1287113729847158e-03,
       9.9999495154040363e-01 ]
P1: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ 1.0966206731018387e+03, 0., 9.5953915405273438e+02, 0., 0.,
       1.0966206731018387e+03, 4.9449611663818359e+02, 0., 0., 0., 1.,
       0. ]
P2: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ 1.0966206731018387e+03, 0., 9.5953915405273438e+02,
       -6.5710061581198403e+04, 0., 1.0966206731018387e+03,
       4.9449611663818359e+02, 0., 0., 0., 1., 0. ]
Q: !!opencv-matrix
   rows: 4
   cols: 4
   dt: d
   data: [ 1., 0., 0., -9.5953915405273438e+02, 0., 1., 0.,
       -4.9449611663818359e+02, 0., 0., 0., 1.0966206731018387e+03, 0.,
       0., 1.6688778654494741e-02, 0. ]
