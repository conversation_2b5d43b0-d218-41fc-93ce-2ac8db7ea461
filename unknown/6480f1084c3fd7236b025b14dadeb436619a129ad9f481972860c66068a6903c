{
    "device":
    {
        "tcp_server":
        [
            {
                "name":"调试tcp",
                "port":7930,
                "time":1000
            }
        ]
        /*
        "s7_client":
        [
            {
                "name":"左PLC",
                "ip":"*************",
                "time":1000,
                "heartbeat":
                [
                    {
                        "type":"01",
                        "db":520,
                        "offset":154,
                        "time":1000
                    }
                ]
            },
            {
                "name":"右PLC",
                "ip":"*************",
                "time":1000,
                "heartbeat":
                [
                    {
                        "type":"01",
                        "db":520,
                        "offset":154,
                        "time":1000
                    }
                ]
            }
        ],*/
        /*"modbus_master":
        [
            {
                "name":"点胶控制卡",
                "ip":"*************",
                "port":502,
                "id":1,
                "time":1000
            }
        ],*/
        /*
        "camera":
        [
            {
                "name":"左拍后壳相机",
                "mac":"34:BD:20:2F:1B:59",
                "time":1000,
                "param":
                [
                    {
                        "name":"GevHeartbeatTimeout",
                        "value":"500",
                        "type":"integer"
                    },
                    {
                        "name":"AcquisitionMode",
                        "value":"Continuous",
                        "type":"enum"
                    },
                    {
                        "name":"TriggerMode",
                        "value":"On",
                        "type":"enum"
                    },
                    {
                        "name":"TriggerSource",
                        "value":"Software",
                        "type":"enum"
                    },
                    {
                        "name":"ExposureAuto",
                        "value":"Off",
                        "type":"enum"
                    }
                ]
            },
            {
                "name":"右拍后壳相机",
                "mac":"34:BD:20:2F:1B:41",
                "time":1000,
                "param":
                [
                    {
                        "name":"GevHeartbeatTimeout",
                        "value":"500",
                        "type":"integer"
                    },
                    {
                        "name":"AcquisitionMode",
                        "value":"Continuous",
                        "type":"enum"
                    },
                    {
                        "name":"TriggerMode",
                        "value":"On",
                        "type":"enum"
                    },
                    {
                        "name":"TriggerSource",
                        "value":"Software",
                        "type":"enum"
                    },
                    {
                        "name":"ExposureAuto",
                        "value":"Off",
                        "type":"enum"
                    }
                ]
            },
            {
                "name":"左拍屏幕相机",
                "mac":"34:BD:20:25:4E:2F",
                "time":1000,
                "param":
                [
                    {
                        "name":"GevHeartbeatTimeout",
                        "value":"500",
                        "type":"integer"
                    },
                    {
                        "name":"AcquisitionMode",
                        "value":"Continuous",
                        "type":"enum"
                    },
                    {
                        "name":"TriggerMode",
                        "value":"On",
                        "type":"enum"
                    },
                    {
                        "name":"TriggerSource",
                        "value":"Software",
                        "type":"enum"
                    },
                    {
                        "name":"ExposureAuto",
                        "value":"Off",
                        "type":"enum"
                    }
                ]
            },
            {
                "name":"右拍屏幕相机",
                "mac":"34:BD:20:20:B4:F0",
                "time":1000,
                "param":
                [
                    {
                        "name":"GevHeartbeatTimeout",
                        "value":"500",
                        "type":"integer"
                    },
                    {
                        "name":"AcquisitionMode",
                        "value":"Continuous",
                        "type":"enum"
                    },
                    {
                        "name":"TriggerMode",
                        "value":"On",
                        "type":"enum"
                    },
                    {
                        "name":"TriggerSource",
                        "value":"Software",
                        "type":"enum"
                    },
                    {
                        "name":"ExposureAuto",
                        "value":"Off",
                        "type":"enum"
                    }
                ]
            },
            {
                "name":"左拍排线相机",
                "mac":"34:BD:20:33:DE:E1",
                "time":1000,
                "param":
                [
                    {
                        "name":"GevHeartbeatTimeout",
                        "value":"500",
                        "type":"integer"
                    },
                    {
                        "name":"AcquisitionMode",
                        "value":"Continuous",
                        "type":"enum"
                    },
                    {
                        "name":"TriggerMode",
                        "value":"On",
                        "type":"enum"
                    },
                    {
                        "name":"TriggerSource",
                        "value":"Software",
                        "type":"enum"
                    },
                    {
                        "name":"ExposureAuto",
                        "value":"Off",
                        "type":"enum"
                    },
                    {
                        "name":"BalanceWhiteAuto",
                        "value":"Off",
                        "type":"enum"
                    },
                    {
                        "name":"BalanceRatioSelector",
                        "value":"Red",
                        "type":"enum"
                    },
                    {
                        "name":"BalanceRatio",
                        "value":"1906",
                        "type":"integer"
                    },
                    {
                        "name":"BalanceRatioSelector",
                        "value":"Green",
                        "type":"enum"
                    },
                    {
                        "name":"BalanceRatio",
                        "value":"1024",
                        "type":"integer"
                    },
                    {
                        "name":"BalanceRatioSelector",
                        "value":"Blue",
                        "type":"enum"
                    },
                    {
                        "name":"BalanceRatio",
                        "value":"1473",
                        "type":"integer"
                    }
                ]
            },
            {
                "name":"右拍排线相机",
                "mac":"34:BD:20:34:8B:BC",
                "time":1000,
                "param":
                [
                    {
                        "name":"GevHeartbeatTimeout",
                        "value":"500",
                        "type":"integer"
                    },
                    {
                        "name":"AcquisitionMode",
                        "value":"Continuous",
                        "type":"enum"
                    },
                    {
                        "name":"TriggerMode",
                        "value":"On",
                        "type":"enum"
                    },
                    {
                        "name":"TriggerSource",
                        "value":"Software",
                        "type":"enum"
                    },
                    {
                        "name":"ExposureAuto",
                        "value":"Off",
                        "type":"enum"
                    },
                    {
                        "name":"BalanceWhiteAuto",
                        "value":"Off",
                        "type":"enum"
                    },
                    {
                        "name":"BalanceRatioSelector",
                        "value":"Red",
                        "type":"enum"
                    },
                    {
                        "name":"BalanceRatio",
                        "value":"2156",
                        "type":"integer"
                    },
                    {
                        "name":"BalanceRatioSelector",
                        "value":"Green",
                        "type":"enum"
                    },
                    {
                        "name":"BalanceRatio",
                        "value":"1024",
                        "type":"integer"
                    },
                    {
                        "name":"BalanceRatioSelector",
                        "value":"Blue",
                        "type":"enum"
                    },
                    {
                        "name":"BalanceRatio",
                        "value":"1551",
                        "type":"integer"
                    }
                ]
            }
        ]*/
    }
}
