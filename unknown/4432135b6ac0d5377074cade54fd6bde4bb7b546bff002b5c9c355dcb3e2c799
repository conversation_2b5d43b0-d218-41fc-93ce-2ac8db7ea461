import os
import string
import sys
import time

from serial.tools import list_ports


def scan_serial_list():
    plist = list_ports.comports()
    serial_list = {}
    for port in plist:
        if port.serial_number is not None:
            serial_list.update({port.serial_number: port.device})
        else:
            serial_list.update({port.device: port.device})

    return serial_list


def get_sign_folder(sign_file):
    file_path = None
    sign_folder = None
    disk_list = get_disk_list()
    for disk in disk_list:
        file_path = disk + sign_file
        if os.path.exists(file_path):
            sign_folder = disk
            break
        else:
            file_path = None

    return file_path, sign_folder


# 获取盘符路径
def get_disk_list():
    disk_list = []
    for c in string.ascii_uppercase:
        disk = c + ':/'
        if os.path.isdir(disk):
            disk_list.append(disk)
    return disk_list


def iterable_str_to_hex(data):
    """
    将str数组转化为byte数组
    :param data: 原始str
    :return: byte数组
    """
    return [int(i, 16) for i in data]


def iterable_str_to_ord(data):
    """
    将str字符串转化为ord数组
    :param data: 原始str
    :return: ord数组
    """
    return [ord(i) for i in data]


def is_windows_platform():
    return 'win' in sys.platform.lower()


def is_linux_platform():
    return 'linux' in sys.platform.lower()


def get_timestamp():
    timestamp = int(round(time.time() * 1000))
    return timestamp
