# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'BaseHead.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_BaseHeadForm(object):
    def setupUi(self, BaseHeadForm):
        BaseHeadForm.setObjectName("BaseHeadForm")
        BaseHeadForm.resize(266, 77)
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(BaseHeadForm)
        self.verticalLayout_2.setContentsMargins(-1, 9, -1, -1)
        self.verticalLayout_2.setSpacing(6)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setObjectName("verticalLayout")
        self.labelIcon = QtWidgets.QLabel(BaseHeadForm)
        self.labelIcon.setAlignment(QtCore.Qt.AlignCenter)
        self.labelIcon.setObjectName("labelIcon")
        self.verticalLayout.addWidget(self.labelIcon)
        self.labelName = QtWidgets.QLabel(BaseHeadForm)
        self.labelName.setAlignment(QtCore.Qt.AlignCenter)
        self.labelName.setObjectName("labelName")
        self.verticalLayout.addWidget(self.labelName)
        self.verticalLayout_2.addLayout(self.verticalLayout)

        self.retranslateUi(BaseHeadForm)
        QtCore.QMetaObject.connectSlotsByName(BaseHeadForm)

    def retranslateUi(self, BaseHeadForm):
        _translate = QtCore.QCoreApplication.translate
        BaseHeadForm.setWindowTitle(_translate("BaseHeadForm", "Form"))
        self.labelIcon.setText(_translate("BaseHeadForm", "labelIcon"))
        self.labelName.setText(_translate("BaseHeadForm", "TextLabel"))
