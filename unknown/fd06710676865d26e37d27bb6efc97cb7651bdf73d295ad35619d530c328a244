%YAML:1.0
---
size: !!opencv-matrix
   rows: 2
   cols: 1
   dt: d
   data: [ 1920., 1080. ]
K1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.7499057605216876e+02, 0., 9.3497906688199055e+02, 0.,
       9.7466542460380060e+02, 5.1616792968936898e+02, 0., 0., 1. ]
D1: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ -7.7962994800752248e-03, 3.8944355885825094e-02,
       -8.9491430730604505e-05, -4.2034158066240083e-04,
       -3.0590597344210286e-02 ]
K2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.7078069524662703e+02, 0., 9.3849122517175545e+02, 0.,
       9.7022164089134446e+02, 5.2243656610630205e+02, 0., 0., 1. ]
D2: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ -7.1160442256919610e-03, 4.0208808140124286e-02,
       -7.9467093195691551e-05, 6.1906466269072901e-04,
       -3.2057283782363062e-02 ]
R: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.9966144165815873e-01, -1.9663734722016699e-02,
       1.7039354410129492e-02, 1.9247631113269423e-02,
       9.9952063305135364e-01, 2.4249387644820531e-02,
       -1.7508019832618064e-02, -2.3913210604254432e-02,
       9.9956071731543017e-01 ]
T: !!opencv-matrix
   rows: 3
   cols: 1
   dt: d
   data: [ -1.4153241363077777e+02, 4.4760035363261608e-01,
       -1.0867027735505799e+00 ]
E: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 1.3079858246581616e-02, 1.0754782826349818e+00,
       4.7375560735818756e-01, -3.5642871660674529e+00,
       -3.3631257794204608e+00, 1.4145172417846737e+02,
       -3.1716125029349582e+00, -1.4145576617490570e+02,
       -3.4397011834994564e+00 ]
F: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -3.1783180676570479e-09, -2.6142121106309106e-07,
       2.5668533312867276e-05, 8.6659699809775820e-07,
       8.1796074411348373e-07, -3.4763968693042893e-02,
       2.9840273618628269e-04, 3.3197596302328151e-02, 1. ]
R1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.9943168443530062e-01, -2.3007562903097165e-02,
       2.4636562180585294e-02, 2.2709737982276254e-02,
       9.9966641996182337e-01, 1.2301081313762669e-02,
       -2.4911361817335033e-02, -1.1734600545884297e-02,
       9.9962078970099177e-01 ]
R2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.9996552422940332e-01, -3.1624199063964857e-03,
       7.6778547101650201e-03, 3.2544766025377355e-03,
       9.9992261526901183e-01, -1.2007158515794298e-02,
       -7.6392888843340723e-03, 1.2031731958263684e-02,
       9.9989843418790603e-01 ]
P1: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ 8.8456493173403499e+02, 0., 9.0190047454833984e+02, 0., 0.,
       8.8456493173403499e+02, 5.1700495529174805e+02, 0., 0., 0., 1.,
       0. ]
P2: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ 8.8456493173403499e+02, 0., 9.0190047454833984e+02,
       -1.2519892613091841e+05, 0., 8.8456493173403499e+02,
       5.1700495529174805e+02, 0., 0., 0., 1., 0. ]
Q: !!opencv-matrix
   rows: 4
   cols: 4
   dt: d
   data: [ 1., 0., 0., -9.0190047454833984e+02, 0., 1., 0.,
       -5.1700495529174805e+02, 0., 0., 0., 8.8456493173403499e+02, 0.,
       0., 7.0652757101850889e-03, 0. ]
