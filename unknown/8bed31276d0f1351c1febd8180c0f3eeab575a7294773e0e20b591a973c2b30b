%YAML:1.0
---
size: !!opencv-matrix
   rows: 2
   cols: 1
   dt: d
   data: [ 1920., 1080. ]
K1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.7121631107817655e+02, 0., 9.7931079492767731e+02, 0.,
       9.7088424036971662e+02, 5.6206085192352725e+02, 0., 0., 1. ]
D1: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ -6.9459539592393524e-03, 3.9327854957498673e-02,
       -2.8133022965708550e-04, 2.8847942342816391e-04,
       -3.3046902700061699e-02 ]
K2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.7225866502783958e+02, 0., 9.6270707115872437e+02, 0.,
       9.7155464813075787e+02, 5.9631519827284683e+02, 0., 0., 1. ]
D2: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ -4.8566083474354061e-03, 3.2383496416076595e-02,
       1.1527617067263618e-04, -3.7166414405849355e-04,
       -2.6529351106867501e-02 ]
R: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.9966020851102722e-01, -2.5767137910656067e-02,
       -3.9398126329717072e-03, 2.5661615371598695e-02,
       9.9936235871707468e-01, -2.4826547806030646e-02,
       4.5770095269534027e-03, 2.4717009999962101e-02,
       9.9968401027547293e-01 ]
T: !!opencv-matrix
   rows: 3
   cols: 1
   dt: d
   data: [ -1.4376404511398113e+02, -3.7410365030342070e+00,
       4.3329591243742235e-02 ]
E: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -1.8234667019773509e-02, -1.3576919916331268e-01,
       -3.7387786497717390e+00, 7.0132427233746730e-01,
       3.5523008611640843e+00, 1.4371844644249771e+02,
       5.0547700690393160e-02, -1.4376877102731859e+02,
       3.5544259559355362e+00 ]
F: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -5.0483606711321410e-09, -3.7601252356629162e-08,
       -9.7922785822321997e-04, 1.9430590356117363e-07,
       9.8452190462800198e-07, 3.7928261598147078e-02,
       -9.7401304891656747e-05, -3.9263049251327883e-02, 1. ]
R1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.9998803926300728e-01, 2.3080092750955738e-04,
       -4.8854950474079986e-03, -2.9115209319287798e-04,
       9.9992361898802895e-01, -1.2356027692659539e-02,
       4.8822701057004616e-03, 1.2357302327571373e-02,
       9.9991172636278247e-01 ]
R2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.9966155269396828e-01, 2.6013252175424464e-02,
       -3.0129178979478423e-04, -2.6007542518776978e-02,
       9.9958531248796456e-01, 1.2361666168945891e-02,
       6.2273398721324289e-04, -1.2349646537299292e-02,
       9.9992354629380786e-01 ]
P1: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ 9.2860518020667587e+02, 0., 9.7938813018798828e+02, 0., 0.,
       9.2860518020667587e+02, 5.8150356292724609e+02, 0., 0., 0., 1.,
       0. ]
P2: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ 9.2860518020667587e+02, 0., 9.7938813018798828e+02,
       -1.3354523504534361e+05, 0., 9.2860518020667587e+02,
       5.8150356292724609e+02, 0., 0., 0., 1., 0. ]
Q: !!opencv-matrix
   rows: 4
   cols: 4
   dt: d
   data: [ 1., 0., 0., -9.7938813018798828e+02, 0., 1., 0.,
       -5.8150356292724609e+02, 0., 0., 0., 9.2860518020667587e+02, 0.,
       0., 6.9534879315714983e-03, 0. ]
