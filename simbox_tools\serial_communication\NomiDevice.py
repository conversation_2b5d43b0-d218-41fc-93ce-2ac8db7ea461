"""
user: Created by jid on 2019/5/23.
email: <EMAIL>
description:Nomi设备通信模块
"""
import time
from PyQt5.QtCore import QTimer

from simbox_tools.Logger import Logger
from simbox_tools.AppData import AppData
from simbox_tools.BytesTool import BytesTool
from simbox_tools.CommonMethod import CommonMethod
from simbox_tools.CommunicationManager import communication_manager
from simbox_tools.ConfigManger import NomiConfig
from simbox_tools.DiagServer import DiagServer

from simbox_tools.serial_communication.AppComm import AppComm, CanMessage, EN_READ_REQ, EN_WRITE_REQ, SIMBOX_TIMEOUT
# communication_manager
# NomiConfig

# default_port_name = "GD32"
default_port_name = "Virtual Com Port"


class NomiDevice():
    _log_tag = "NomiDevice"
    __data = None
    __comm = None
    _port = None
    _msg_id = 0
    _brightness_percent = NomiConfig.DefaultBrightnessPercent
    _calibrate_status = NomiConfig.CalibrateNotActive
    _pitch_frame_count = 0
    _yaw_frame_count = 0
    pitch_rotate_time = 0
    yaw_rotate_time = 0
    pitch_timer = None
    yaw_timer = None
    rotate_pitch_timer = None
    rotate_yaw_timer = None
    pitch_timeout = None
    yaw_timeout = None
    pitch_not_completed = None
    yaw_not_completed = None
    check_rotate_pitch_count = 0
    check_rotate_yaw_count = 0

    def __init__(self):
        super().__init__()
        self.__data = AppData()
        self.__comm = AppComm(sim_data=self.__data)
        self.__diag1_server = DiagServer(self.__comm.diag1_send)
        self.__diag2_server = DiagServer(self.__comm.diag2_send)

    def get_port_list(self):
        return self.__comm.get_port_list()

    def refresh_port(self):
        self._port = self.__comm.get_default_port(default_port_name)
        Logger.console(self._log_tag, "refresh_port -> %s" % self._port)

    def connect(self, port):
        self._port = port
        return self.__comm.connect(self._port, self.__diag1_server.msg_protocol, self.__diag2_server.msg_protocol)

    def disconnect(self):
        self.__comm.disconnect()

    def get_port(self):
        return self._port

    def get_diag1_data(self):
        return self.__diag1_server.get_data()

    def get_diag2_data(self):
        return self.__diag2_server.get_data()

    def send_diag1_data(self, data):
        self.__diag1_server.request(data)

    def send_diag2_data(self, data):
        self.__diag2_server.request(data)

    def get_hardware_version(self):
        bt = BytesTool("get_hardware_version")
        bt.append_int(0x22)
        bt.append_int(0xF1)
        bt.append_int(0x10)
        self.__diag2_server.request(bt.get_byte_list())
        self.__diag2_server.wait_response(500)
        data = self.get_diag2_data()
        Logger.console(self._log_tag, "get_hardware_version -> %s" % data)
        if data is not None and len(data) >= 4:
            if data[0] == 0x62:
                hardware_version_data = data[3:]
                return CommonMethod.iterable_byte_to_char(hardware_version_data)

        return ""

    def get_software_version(self):
        bt = BytesTool("get_software_version")
        bt.append_int(0x22)
        bt.append_int(0xF1)
        bt.append_int(0x18)
        self.__diag2_server.request(bt.get_byte_list())
        self.__diag2_server.wait_response(500)
        data = self.get_diag2_data()
        Logger.console(self._log_tag, "get_software_version -> %s" % data)
        if data is not None and len(data) >= 4:
            if data[0] == 0x62:
                software_version_data = data[3:]
                return CommonMethod.iterable_byte_to_char(software_version_data)

        return ""

    def get_ecu_serial(self):
        bt = BytesTool("get_ecu_serial")
        bt.append_int(0x22)
        bt.append_int(0xF1)
        bt.append_int(0x8C)
        self.__diag2_server.request(bt.get_byte_list())
        self.__diag2_server.wait_response(500)
        data = self.get_diag2_data()
        Logger.console(self._log_tag, "get_ecu_serial -> %s" % data)
        if data is not None and len(data) >= 4:
            if data[0] == 0x62:
                ecu_serial_data = data[3:]
                return CommonMethod.iterable_byte_to_char(ecu_serial_data)

        return ""

    def send_diag_msg(self, data):
        Logger.console(self._log_tag, "send_diag_msg -> %s" % data)
        bt = BytesTool("%s send_diag_msg" % self._log_tag)
        for item in data:
            bt.append_int(item)

        self.__diag2_server.request(bt.get_byte_list())

    def get_simbox_version(self):
        self.__comm.simbox_obj(EN_READ_REQ, self.__data.version)
        time.sleep(0.1)
        simbox_version = self.__data.version.get_string()
        return simbox_version

    def get_play_state(self):
        return self.__data.play_state.get_value()

    def get_play_frame(self):
        return self.__data.play_frame.get_value()

    def replay_animation(self):
        Logger.console(self._log_tag, "replay_animation")
        self.__comm.simbox_obj(EN_WRITE_REQ, self.__data.replay_animation)

    def play_animation(self):
        Logger.console(self._log_tag, "play_animation")
        self.__comm.simbox_obj(EN_WRITE_REQ, self.__data.play_animation)

    def pause_animation(self):
        Logger.console(self._log_tag, "pause_animation")
        self.__comm.simbox_obj(EN_WRITE_REQ, self.__data.pause_animation)

    def get_simbox_state(self):
        self.__comm.simbox_obj(EN_READ_REQ, self.__data.simbox_version)

    # 等待pitch_rotate_time之后开启一个timer,重试100次,每次50ms,如果有一次回复的frame_ack(mate回复的运动状态)为1,
    # 则结束超时检测timer,否则一直重试直至达到100次,100次以后认为timeout
    def start_pitch_timer_delay_time(self, pitch_rotate_time=0):
        Logger.console(self._log_tag, "start_pitch_timer_delay_time -> %s" % pitch_rotate_time)
        if self.pitch_timer is None:
            self.pitch_timer = QTimer()
            self.pitch_timer.timeout.connect(self.start_pitch_timer_by_times)
            self.pitch_timer.start(pitch_rotate_time)

    # 等待yaw_rotate_time之后开启一个timer,重试100次,每次50ms,如果有一次回复的frame_ack(mate回复的运动状态)为1,
    # 则结束超时检测timer,否则一直重试直至达到100次,100次以后认为timeout
    def start_yaw_timer_delay_time(self, yaw_rotate_time=0):
        Logger.console(self._log_tag, "start_yaw_timer_delay_time -> %s" % yaw_rotate_time)
        if self.yaw_timer is None:
            self.yaw_timer = QTimer()
            self.yaw_timer.timeout.connect(self.start_yaw_timer_by_times)
            self.yaw_timer.start(yaw_rotate_time)

    def stop_pitch_timer(self):
        if self.rotate_pitch_timer is not None:
            self.rotate_pitch_timer.stop()
            self.rotate_pitch_timer = None
            self.check_rotate_pitch_count = 0

    def stop_yaw_timer(self):
        if self.rotate_yaw_timer is not None:
            self.rotate_yaw_timer.stop()
            self.rotate_yaw_timer = None
            self.check_rotate_yaw_count = 0

    def start_pitch_timer_by_times(self):
        Logger.console(self._log_tag, "start_pitch_timer_by_times")
        if self.pitch_timer is not None:
            self.pitch_timer.stop()
            self.pitch_timer = None

        if self.rotate_pitch_timer is None:
            self.rotate_pitch_timer = QTimer()
            self.rotate_pitch_timer.timeout.connect(self.check_rotate_pitch_timeout)
            self.rotate_pitch_timer.start(50)

    def start_yaw_timer_by_times(self):
        Logger.console(self._log_tag, "start_yaw_timer_by_times")
        if self.yaw_timer is not None:
            self.yaw_timer.stop()
            self.yaw_timer = None

        if self.rotate_yaw_timer is None:
            self.rotate_yaw_timer = QTimer()
            self.rotate_yaw_timer.timeout.connect(self.check_rotate_yaw_timeout)
            self.rotate_yaw_timer.start(50)

    def check_rotate_pitch_timeout(self):
        # Logger.console(self._log_tag, "check_rotate_pitch_timeout")
        self.check_rotate_pitch_count += 1
        if communication_manager.pitch_rotate_state == NomiConfig.KineticRotateStateNotActivated:
            if self.check_rotate_pitch_count >= 20:
                # 说明rotate_pitch指令执行超时
                self.pitch_timeout()
                self.stop_pitch_timer()
        elif communication_manager.pitch_rotate_state == NomiConfig.KineticRotateStateActivated:
            # 说明rotate_pitch指令执行完成
            self.stop_pitch_timer()
        elif communication_manager.pitch_rotate_state == NomiConfig.KineticRotateStateNotCompleted:
            # 说明rotate_pitch指令没有执行到位
            # self.pitch_not_completed()
            self.stop_pitch_timer()

    def check_rotate_yaw_timeout(self):
        # Logger.console(self._log_tag, "check_rotate_yaw_timeout")
        self.check_rotate_yaw_count += 1
        if communication_manager.yaw_rotate_state == NomiConfig.KineticRotateStateNotActivated:
            if self.check_rotate_yaw_count >= 40:
                # 说明rotate_yaw指令回复超时
                self.yaw_timeout()
                self.stop_yaw_timer()
        elif communication_manager.yaw_rotate_state == NomiConfig.KineticRotateStateActivated:
            # 说明rotate_yaw指令执行完成
            self.stop_yaw_timer()
        elif communication_manager.yaw_rotate_state == NomiConfig.KineticRotateStateNotCompleted:
            # 说明rotate_yaw指令没有执行到位
            # self.yaw_not_completed()
            print("yaw_not_completed")
            self.stop_yaw_timer()

    def set_pitch_timeout(self, timeout):
        self.pitch_timeout = timeout

    def set_yaw_timeout(self, timeout):
        self.yaw_timeout = timeout

    def set_pitch_not_completed(self, not_completed):
        self.pitch_not_completed = not_completed

    def set_yaw_not_completed(self, not_completed):
        self.yaw_not_completed = not_completed

    def rotate_degree(self, pitch_offset=0, yaw_offset=0):
        Logger.console(self._log_tag, "rotate_degree -> %s,%s" % (pitch_offset, yaw_offset))
        if pitch_offset + 50 < 0 or yaw_offset + 110 < 0:
            return Logger.console(self._log_tag, "rotate_degree -> rotate degree data is invalid")

        # 需要在pitch转动指令转动完成之后才能响应下一条运动指令
        if communication_manager.pitch_rotate_state == NomiConfig.KineticRotateStateActivated and pitch_offset != 0:
            if self._pitch_frame_count >= 50:
                self._pitch_frame_count = 0
            else:
                self._pitch_frame_count += 1

            # 以1°/40ms的速度运行,运行的时间=运动的度数*40
            pitch_rotate_time = abs(pitch_offset) * 40
            pitch_high_byte = pitch_rotate_time >> 8
            pitch_low_byte = pitch_rotate_time & 0xFF
            data = [0x00, self._pitch_frame_count, pitch_offset + 50, pitch_high_byte, pitch_low_byte, 0x30, 0x30, 0xAA]
            self.__comm.append(CanMessage(id=NomiConfig.KineticPitchId, data=data, ch=1, console=True))
            self.start_pitch_timer_delay_time(pitch_rotate_time=pitch_rotate_time)

        # 需要在yaw转动指令转动完成之后才能响应下一条运动指令
        if communication_manager.yaw_rotate_state == NomiConfig.KineticRotateStateActivated and yaw_offset != 0:
            if self._yaw_frame_count >= 50:
                self._yaw_frame_count = 0
            else:
                self._yaw_frame_count += 1

            # 以1°/40ms的速度运行,运行的时间=运动的度数*40
            yaw_rotate_time = abs(yaw_offset) * 40
            yaw_high_byte = yaw_rotate_time >> 8
            yaw_low_byte = yaw_rotate_time & 0xFF
            data = [0x00, self._yaw_frame_count, yaw_offset + 110, yaw_high_byte, yaw_low_byte, 0x30, 0x30, 0xAA]
            self.__comm.append(CanMessage(id=NomiConfig.KineticYawId, data=data, ch=1, console=True))
            self.start_yaw_timer_delay_time(yaw_rotate_time=yaw_rotate_time)

    def rotate_degree_movement(self, pitch_offset=0, yaw_offset=0,pitch_rotate_time=100,yaw_rotate_time =100,conv0=45,conv1=55):
        Logger.console(self._log_tag, "rotate_degree -> %s,%s" % (pitch_offset, yaw_offset))
        if pitch_offset + 50 < 0 or yaw_offset + 110 < 0:
            return Logger.console(self._log_tag, "rotate_degree -> rotate degree data is invalid")

        # 需要在pitch转动指令转动完成之后才能响应下一条运动指令
        if communication_manager.pitch_rotate_state == NomiConfig.KineticRotateStateActivated and pitch_offset != 0:
            if self._pitch_frame_count >= 50:
                self._pitch_frame_count = 0
            else:
                self._pitch_frame_count += 1

            # 以1°/40ms的速度运行,运行的时间=运动的度数*40
            # pitch_rotate_time = abs(pitch_offset) * 40
            pitch_high_byte = pitch_rotate_time >> 8
            pitch_low_byte = pitch_rotate_time & 0xFF
            # data = [0x00, self._pitch_frame_count, pitch_offset + 50, pitch_high_byte, pitch_low_byte, 0x30, 0x30, 0xAA]
            data = [0x00, self._pitch_frame_count, pitch_offset + 50, pitch_high_byte, pitch_low_byte, conv0, conv1, 0xAA]
            self.__comm.append(CanMessage(id=NomiConfig.KineticPitchId, data=data, ch=1, console=True))
            self.start_pitch_timer_delay_time(pitch_rotate_time=pitch_rotate_time)

        # 需要在yaw转动指令转动完成之后才能响应下一条运动指令
        # if communication_manager.yaw_rotate_state == NomiConfig.KineticRotateStateActivated and yaw_offset != 0:
        if communication_manager.yaw_rotate_state == NomiConfig.KineticRotateStateActivated and yaw_offset != 0:
            if self._yaw_frame_count >= 50:
                self._yaw_frame_count = 0
            else:
                self._yaw_frame_count += 1

            # 以1°/40ms的速度运行,运行的时间=运动的度数*40
            # yaw_rotate_time = abs(yaw_offset) * 40 # 毫秒
            yaw_high_byte = yaw_rotate_time >> 8
            yaw_low_byte = yaw_rotate_time & 0xFF
            data = [0x00, self._yaw_frame_count, yaw_offset + 110, yaw_high_byte, yaw_low_byte, conv0, conv1, 0xAA]
            self.__comm.append(CanMessage(id=NomiConfig.KineticYawId, data=data, ch=1, console=True))
            self.__comm.append(CanMessage(id=NomiConfig.KineticYawId, data=data, ch=1, console=True))
            self.start_yaw_timer_delay_time(yaw_rotate_time=yaw_rotate_time)


    def rotate_degree_by_time(self, pitch_offset=0, yaw_offset=0,
                              pitch_rotate_time=0,
                              yaw_rotate_time=0):
        self._pitch_frame_count += 1
        self._yaw_frame_count += 1

        pitch_high_byte = pitch_rotate_time >> 8
        pitch_low_byte = pitch_rotate_time & 0xFF

        yaw_high_byte = yaw_rotate_time >> 8
        yaw_low_byte = yaw_rotate_time & 0xFF

        pitch_data = [0x00, self._pitch_frame_count, pitch_offset + 50, pitch_high_byte, pitch_low_byte, 0x30, 0x30]
        self.__comm.append(CanMessage(NomiConfig.KineticPitchId, pitch_data, ch=1, console=True))
        yaw_data = [0x00, self._yaw_frame_count, yaw_offset + 110, yaw_high_byte, yaw_low_byte, 0x30, 0x30]
        self.__comm.append(CanMessage(NomiConfig.KineticYawId, yaw_data, ch=1, console=True))

    def check_simbox_timeout(self):
        if self.__comm.simbox_recv_time == 0:
            return

        timeout = int(time.time()) - self.__comm.simbox_recv_time
        if timeout > SIMBOX_TIMEOUT:
            communication_manager.timeout_signal.emit()

    def on_close(self):
        # self.__comm.get_usb().set_usb_null()
        self.__comm.disconnect()
        return True


