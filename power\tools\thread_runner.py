import ctypes
import inspect
import threading
import time


class ThreadStopException(SystemExit):
    pass


class ThreadRunner:
    def __init__(self, func, *args, **kwargs):
        self.func = func
        self.args = args
        self.kwargs = kwargs

        self.thread = threading.Thread(target=self.run, daemon=True)

    def run(self):
        try:

            self.func(*self.args, **self.kwargs)

        except ThreadStopException:
            pass

        except Exception:
            pass

    @staticmethod
    def _async_raise(tid, exctype):
        """raises the exception, performs cleanup if needed"""
        tid = ctypes.c_long(tid)
        if not inspect.isclass(exctype):
            exctype = type(exctype)
        res = ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, ctypes.py_object(exctype))
        if res == 0:
            raise ValueError("invalid thread id")
        elif res != 1:
            # """if it returns a number greater than one, you're in trouble,
            # and you should call it again with exc=NULL to revert the effect"""
            ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, None)
            raise SystemError("PyThreadState_SetAsyncExc failed")

    def stop(self):
        while self.thread.is_alive():
            self._async_raise(self.thread.ident, ThreadStopException)
            time.sleep(0.1)

    def start(self):
        self.thread.start()

    def is_alive(self):
        return self.thread.is_alive()
