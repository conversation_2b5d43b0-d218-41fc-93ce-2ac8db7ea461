# -*- coding: utf-8 -*-
"""
user:Created by jid on 2021/1/29
email:<EMAIL>
description:
"""

from PyQt5.QtCore import QObject

from common.LogUtils import logger
from photics.color_analyzer_tools.manager import MeasureType
from photics.color_analyzer_tools.manager.SignalCenter import signal_center


class BrightnessCurveCollect(QObject):

    def __init__(self):
        super(BrightnessCurveCollect, self).__init__()
        self.collect_x = []
        self.collect_y = []
        self.collect_brightness = []
        self.gray_index = []
        self.brightness_index = 0
        self.brightness_size = 101
        signal_center.brightness_curve_measure_data_signal.connect(self.measure_data)

    def reset_params(self):
        self.collect_x.clear()
        self.collect_y.clear()
        self.collect_brightness.clear()
        self.gray_index.clear()
        self.brightness_index = 0

    def measure_data(self, measure_data):
        logger.info('measure_data measure_data=%s', measure_data)
        self.collect_x.append(measure_data[0])
        self.collect_y.append(measure_data[1])
        self.collect_brightness.append(measure_data[2])
        self.gray_index.append(self.brightness_index)
        self.brightness_index += 1

        if self.brightness_index == self.brightness_size:
            signal_center.brightness_measure_event_signal.emit(MeasureType.MEASURE_COMPLETED)
        else:
            signal_center.brightness_measure_event_signal.emit(MeasureType.MEASURE_NEXT)
