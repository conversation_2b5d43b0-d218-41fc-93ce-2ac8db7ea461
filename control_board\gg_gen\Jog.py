import ctypes

from .Base import gts


class JogPrm(ctypes.Structure):
    _fields_ = [("acc", ctypes.c_double), ("dec", ctypes.c_double), ("smooth", ctypes.c_double)]


if gts is not None:
    gts.GTN_PrfJog.argtypes = [ctypes.c_short, ctypes.c_short]
    gts.GTN_PrfJog.restype = ctypes.c_short


def GTN_PrfJog(core, profile):
    return gts.GTN_PrfJog(core, profile)


if gts is not None:
    gts.GTN_SetJogPrm.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.POINTER(JogPrm)]
    gts.GTN_SetJogPrm.restype = ctypes.c_short


def GTN_SetJogPrm(core, profile, acc=0, dec=0, smooth=0):
    prm = JogPrm(acc, dec, smooth)
    return gts.GTN_SetJogPrm(core, profile, ctypes.byref(prm))


if gts is not None:
    gts.GTN_GetJogPrm.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.POINTER(JogPrm)]
    gts.GTN_GetJogPrm.restype = ctypes.c_short


def GTN_GetJogPrm(core, profile):
    prm = JogPrm()
    r = gts.GTN_GetJogPrm(core, profile, ctypes.byref(prm))
    return r, prm.acc, prm.dec, prm.smooth


if gts is not None:
    gts.GTN_SetVel.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_double]
    gts.GTN_SetVel.restype = ctypes.c_short


def GTN_SetVel(core, profile, vel):
    r = gts.GTN_SetVel(core, profile, vel)
    return r


if gts is not None:
    gts.GTN_GetVel.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.POINTER(ctypes.c_double)]
    gts.GTN_GetVel.restype = ctypes.c_short


def GTN_GetVel(core, profile):
    vel = ctypes.c_double(0)
    r = gts.GTN_GetVel(core, profile, ctypes.byref(vel))
    return r, vel


if gts is not None:
    gts.GTN_Update.argtypes = [ctypes.c_short, ctypes.c_long]
    gts.GTN_Update.restype = ctypes.c_short


def GTN_Update(core, mask):
    r = gts.GTN_Update(core, mask)
    return r
