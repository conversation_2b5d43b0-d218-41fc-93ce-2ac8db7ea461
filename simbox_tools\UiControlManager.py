"""
user: Created by jid on 2019/5/16.
email: <EMAIL>
description:用于一级界面和二级界面的串联
"""

from PyQt5.QtCore import QObject, pyqtSignal


from simbox_tools.ConfigManger import appConfig, NomiConfig
from simbox_tools.Logger import Logger



class UiControlManager(QObject):
    control_signal = pyqtSignal(object, object, object, object, object)
    update_kinetic_signal = pyqtSignal(object, object)

    def __init__(self):
        super().__init__()
        self._log_tag = "UiControlManager"
        self._import_folder_path = ""
        self.kinetic_pitch_start_position = 0
        self.kinetic_yaw_start_position = 0
        self.calibrate_timeout = 0
        self.kinetic_pitch_min_degree = 0
        self.kinetic_pitch_max_degree = 0
        self.kinetic_yaw_min_degree = 0
        self.kinetic_yaw_max_degree = 0

    def update_params(self):
        self.kinetic_pitch_start_position = int(appConfig.get_calibrate_pitch_degree())
        self.kinetic_yaw_start_position = int(appConfig.get_calibrate_yaw_degree())
        self.calibrate_timeout = int(appConfig.get_calibrate_timeout())
        self.kinetic_pitch_min_degree = int(appConfig.get_kinetic_pitch_min_degree())
        self.kinetic_pitch_max_degree = int(appConfig.get_kinetic_pitch_max_degree())
        self.kinetic_yaw_min_degree = int(appConfig.get_kinetic_yaw_min_degree())
        self.kinetic_yaw_max_degree = int(appConfig.get_kinetic_yaw_max_degree())
        Logger.console('UiControlManager', '_kinetic_pitch_start_position -> %s' % self.kinetic_pitch_start_position)
        Logger.console('UiControlManager', '_kinetic_yaw_start_position -> %s' % self.kinetic_yaw_start_position)
        Logger.console('UiControlManager', '_calibrate_timeout -> %s' % self.calibrate_timeout)
        Logger.console('UiControlManager', '_kinetic_pitch_min_degree -> %s' % self.kinetic_pitch_min_degree)
        Logger.console('UiControlManager', '_kinetic_pitch_max_degree -> %s' % self.kinetic_pitch_max_degree)
        Logger.console('UiControlManager', '_kinetic_yaw_min_degree -> %s' % self.kinetic_yaw_min_degree)
        Logger.console('UiControlManager', '_kinetic_yaw_max_degree -> %s' % self.kinetic_yaw_max_degree)

    def transfer_params(self, element_type, element_index, element_command, data, time):
        self.control_signal.emit(element_type, element_index, element_command, data, time)

    def get_emoji_name_list(self):
        emoji_name_list = []
        project_data = appConfig.get_project_file()
        emoji_list = project_data["data"][NomiConfig.EditorTypeEmoji]
        for emoji in emoji_list:
            emoji_name_list.append(emoji["name"])

        Logger.console(self._log_tag, "get_emoji_name_list -> %s" % emoji_name_list)
        return emoji_name_list

    def update_kinetic_current_position(self, kinetic_type):
        project_data = appConfig.get_project_file()
        if project_data is None:
            return

        kinetic_list = project_data["data"][kinetic_type]
        kinetic_list_size = len(kinetic_list)
        if kinetic_list_size > 0:
            first_kinetic = kinetic_list[0]
            first_kinetic_motion_object = first_kinetic["motion"]
            first_kinetic_motion_type = first_kinetic_motion_object["type"]
            if kinetic_type == NomiConfig.EditorTypePitch:
                first_kinetic_motion_object['current_position'] = self.get_kinetic_start_position()[0]
            elif kinetic_type == NomiConfig.EditorTypeYaw:
                first_kinetic_motion_object['current_position'] = self.get_kinetic_start_position()[1]

            if first_kinetic_motion_type == NomiConfig.KineticMotionTypeGoTo:
                first_kinetic_motion_object['move_range'] = first_kinetic_motion_object['target_position'] - \
                                                            first_kinetic_motion_object['current_position']
            else:
                first_kinetic_motion_object['target_position'] = first_kinetic_motion_object['current_position'] + \
                                                                 first_kinetic_motion_object['move_range']

            for i in range(kinetic_list_size):
                if i + 1 < kinetic_list_size:
                    kinetic = kinetic_list[i]
                    kinetic_next = kinetic_list[i + 1]
                    kinetic_motion_object = kinetic["motion"]
                    kinetic_next_motion_object = kinetic_next["motion"]
                    kinetic_next_motion_type = kinetic_next_motion_object["type"]
                    kinetic_next_motion_object["current_position"] = kinetic_motion_object["target_position"]
                    if kinetic_next_motion_type == NomiConfig.KineticMotionTypeGoTo:
                        kinetic_next_motion_object["move_range"] = kinetic_next_motion_object["target_position"] - \
                                                                   kinetic_next_motion_object["current_position"]
                    else:
                        kinetic_next_motion_object["target_position"] = kinetic_next_motion_object["current_position"] + \
                                                                        kinetic_next_motion_object["move_range"]

        self.update_kinetic_signal.emit(kinetic_list, kinetic_type)
        return kinetic_list

    def set_kinetic_start_position(self, pitch_position, yaw_position):
        Logger.console(self._log_tag, "set_kinetic_start_position -> %s,%s" % (pitch_position, yaw_position))
        self.kinetic_pitch_start_position = pitch_position
        self.kinetic_yaw_start_position = yaw_position

    def get_kinetic_start_position(self):
        return self.kinetic_pitch_start_position, self.kinetic_yaw_start_position

    # 只会在新添加KineticEditor的时候用到
    def get_last_target_position(self, kinetic_type, kinetic_index):
        current_position = 0
        if kinetic_index == 0:
            # 说明是新添加的第一个元素
            if kinetic_type == NomiConfig.EditorTypePitch:
                current_position = self.get_kinetic_start_position()[0]
            elif kinetic_type == NomiConfig.EditorTypeYaw:
                current_position = self.get_kinetic_start_position()[1]
        elif kinetic_index >= 1:
            project_data = appConfig.get_project_file()
            kinetic_list = project_data["data"][kinetic_type]
            kinetic_info = kinetic_list[kinetic_index - 1]
            motion_object = kinetic_info["motion"]
            current_position = motion_object["target_position"]

        return current_position

    def get_start_position(self, kinetic_type):
        start_position = 0
        if kinetic_type == NomiConfig.EditorTypePitch:
            start_position = 20
        elif kinetic_type == NomiConfig.EditorTypeYaw:
            start_position = 0

        project_data = appConfig.get_project_file()
        kinetic_list = project_data["data"][kinetic_type]
        if len(kinetic_list) > 0:
            kinetic_info = kinetic_list[0]
            motion_object = kinetic_info["motion"]
            start_position = motion_object["current_position"]

        return start_position

    def get_import_folder_path(self):
        return self._import_folder_path

    def set_import_folder_path(self, path):
        self._import_folder_path = path


uiControlManager = UiControlManager()
