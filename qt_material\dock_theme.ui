<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DockWidget</class>
 <widget class="QDockWidget" name="DockWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>231</width>
    <height>391</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Change Theme</string>
  </property>
  <widget class="QWidget" name="dockWidgetContents">
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="0">
     <layout class="QFormLayout" name="formLayout">
      <item row="0" column="1">
       <widget class="QPushButton" name="pushButton_primaryColor">
        <property name="text">
         <string>primaryColor</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QPushButton" name="pushButton_primaryLightColor">
        <property name="text">
         <string>primaryLightColor</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QPushButton" name="pushButton_secondaryColor">
        <property name="text">
         <string>secondaryColor</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QPushButton" name="pushButton_secondaryLightColor">
        <property name="text">
         <string>secondaryLightColor</string>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="QPushButton" name="pushButton_secondaryDarkColor">
        <property name="text">
         <string>secondaryDarkColor</string>
        </property>
       </widget>
      </item>
      <item row="5" column="1">
       <widget class="QPushButton" name="pushButton_primaryTextColor">
        <property name="text">
         <string>primaryTextColor</string>
        </property>
       </widget>
      </item>
      <item row="6" column="1">
       <widget class="QPushButton" name="pushButton_secondaryTextColor">
        <property name="text">
         <string>secondaryTextColor</string>
        </property>
       </widget>
      </item>
      <item row="7" column="1">
       <widget class="QCheckBox" name="checkBox_ligh_theme">
        <property name="text">
         <string>Invert secondary colors</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
