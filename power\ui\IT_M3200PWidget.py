# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'IT_M3200PWidget.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(1600, 900)
        Form.setMinimumSize(QtCore.QSize(1600, 900))
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(Form)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.groupBox = QtWidgets.QGroupBox(Form)
        self.groupBox.setTitle("")
        self.groupBox.setObjectName("groupBox")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.groupBox)
        self.verticalLayout.setObjectName("verticalLayout")
        self.tabWidget = QtWidgets.QTabWidget(self.groupBox)
        self.tabWidget.setObjectName("tabWidget")
        self.tab_5 = QtWidgets.QWidget()
        self.tab_5.setObjectName("tab_5")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.tab_5)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.groupBox_7 = QtWidgets.QGroupBox(self.tab_5)
        self.groupBox_7.setTitle("")
        self.groupBox_7.setObjectName("groupBox_7")
        self.verticalLayout_8 = QtWidgets.QVBoxLayout(self.groupBox_7)
        self.verticalLayout_8.setObjectName("verticalLayout_8")
        self.formLayout_3 = QtWidgets.QFormLayout()
        self.formLayout_3.setObjectName("formLayout_3")
        self.label_11 = QtWidgets.QLabel(self.groupBox_7)
        self.label_11.setObjectName("label_11")
        self.formLayout_3.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_11)
        self.doubleSpinBox_1_volt = QtWidgets.QDoubleSpinBox(self.groupBox_7)
        self.doubleSpinBox_1_volt.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_1_volt.setDecimals(3)
        self.doubleSpinBox_1_volt.setObjectName("doubleSpinBox_1_volt")
        self.formLayout_3.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_1_volt)
        self.label_12 = QtWidgets.QLabel(self.groupBox_7)
        self.label_12.setObjectName("label_12")
        self.formLayout_3.setWidget(1, QtWidgets.QFormLayout.LabelRole, self.label_12)
        self.doubleSpinBox_1_on_time = QtWidgets.QDoubleSpinBox(self.groupBox_7)
        self.doubleSpinBox_1_on_time.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_1_on_time.setMaximum(9999.99)
        self.doubleSpinBox_1_on_time.setObjectName("doubleSpinBox_1_on_time")
        self.formLayout_3.setWidget(1, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_1_on_time)
        self.label_13 = QtWidgets.QLabel(self.groupBox_7)
        self.label_13.setObjectName("label_13")
        self.formLayout_3.setWidget(2, QtWidgets.QFormLayout.LabelRole, self.label_13)
        self.doubleSpinBox_1_off_time = QtWidgets.QDoubleSpinBox(self.groupBox_7)
        self.doubleSpinBox_1_off_time.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_1_off_time.setMaximum(9999.99)
        self.doubleSpinBox_1_off_time.setObjectName("doubleSpinBox_1_off_time")
        self.formLayout_3.setWidget(2, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_1_off_time)
        self.verticalLayout_8.addLayout(self.formLayout_3)
        self.checkBox_1_random_time_enable = QtWidgets.QCheckBox(self.groupBox_7)
        self.checkBox_1_random_time_enable.setMinimumSize(QtCore.QSize(0, 45))
        self.checkBox_1_random_time_enable.setObjectName("checkBox_1_random_time_enable")
        self.verticalLayout_8.addWidget(self.checkBox_1_random_time_enable)
        self.label_17 = QtWidgets.QLabel(self.groupBox_7)
        self.label_17.setObjectName("label_17")
        self.verticalLayout_8.addWidget(self.label_17)
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.doubleSpinBox_1_on_time_s = QtWidgets.QDoubleSpinBox(self.groupBox_7)
        self.doubleSpinBox_1_on_time_s.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_1_on_time_s.setMaximum(9999.99)
        self.doubleSpinBox_1_on_time_s.setObjectName("doubleSpinBox_1_on_time_s")
        self.horizontalLayout_4.addWidget(self.doubleSpinBox_1_on_time_s)
        self.label_15 = QtWidgets.QLabel(self.groupBox_7)
        self.label_15.setAlignment(QtCore.Qt.AlignCenter)
        self.label_15.setObjectName("label_15")
        self.horizontalLayout_4.addWidget(self.label_15)
        self.doubleSpinBox_1_on_time_e = QtWidgets.QDoubleSpinBox(self.groupBox_7)
        self.doubleSpinBox_1_on_time_e.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_1_on_time_e.setMaximum(9999.99)
        self.doubleSpinBox_1_on_time_e.setObjectName("doubleSpinBox_1_on_time_e")
        self.horizontalLayout_4.addWidget(self.doubleSpinBox_1_on_time_e)
        self.verticalLayout_8.addLayout(self.horizontalLayout_4)
        self.label_18 = QtWidgets.QLabel(self.groupBox_7)
        self.label_18.setObjectName("label_18")
        self.verticalLayout_8.addWidget(self.label_18)
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.doubleSpinBox_1_off_time_s = QtWidgets.QDoubleSpinBox(self.groupBox_7)
        self.doubleSpinBox_1_off_time_s.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_1_off_time_s.setMaximum(9999.99)
        self.doubleSpinBox_1_off_time_s.setObjectName("doubleSpinBox_1_off_time_s")
        self.horizontalLayout_5.addWidget(self.doubleSpinBox_1_off_time_s)
        self.label_16 = QtWidgets.QLabel(self.groupBox_7)
        self.label_16.setAlignment(QtCore.Qt.AlignCenter)
        self.label_16.setObjectName("label_16")
        self.horizontalLayout_5.addWidget(self.label_16)
        self.doubleSpinBox_1_off_time_e = QtWidgets.QDoubleSpinBox(self.groupBox_7)
        self.doubleSpinBox_1_off_time_e.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_1_off_time_e.setMaximum(9999.99)
        self.doubleSpinBox_1_off_time_e.setObjectName("doubleSpinBox_1_off_time_e")
        self.horizontalLayout_5.addWidget(self.doubleSpinBox_1_off_time_e)
        self.verticalLayout_8.addLayout(self.horizontalLayout_5)
        spacerItem = QtWidgets.QSpacerItem(20, 66, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_8.addItem(spacerItem)
        self.horizontalLayout_6.addWidget(self.groupBox_7)
        self.groupBox_8 = QtWidgets.QGroupBox(self.tab_5)
        self.groupBox_8.setTitle("")
        self.groupBox_8.setObjectName("groupBox_8")
        self.verticalLayout_9 = QtWidgets.QVBoxLayout(self.groupBox_8)
        self.verticalLayout_9.setObjectName("verticalLayout_9")
        self.pushButton_1_start = QtWidgets.QPushButton(self.groupBox_8)
        self.pushButton_1_start.setObjectName("pushButton_1_start")
        self.verticalLayout_9.addWidget(self.pushButton_1_start)
        self.pushButton_1_stop = QtWidgets.QPushButton(self.groupBox_8)
        self.pushButton_1_stop.setObjectName("pushButton_1_stop")
        self.verticalLayout_9.addWidget(self.pushButton_1_stop)
        spacerItem1 = QtWidgets.QSpacerItem(20, 197, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_9.addItem(spacerItem1)
        self.horizontalLayout_6.addWidget(self.groupBox_8)
        self.tabWidget.addTab(self.tab_5, "")
        self.tab_3 = QtWidgets.QWidget()
        self.tab_3.setObjectName("tab_3")
        self.horizontalLayout_9 = QtWidgets.QHBoxLayout(self.tab_3)
        self.horizontalLayout_9.setObjectName("horizontalLayout_9")
        self.groupBox_11 = QtWidgets.QGroupBox(self.tab_3)
        self.groupBox_11.setTitle("")
        self.groupBox_11.setObjectName("groupBox_11")
        self.verticalLayout_12 = QtWidgets.QVBoxLayout(self.groupBox_11)
        self.verticalLayout_12.setObjectName("verticalLayout_12")
        self.label_26 = QtWidgets.QLabel(self.groupBox_11)
        self.label_26.setObjectName("label_26")
        self.verticalLayout_12.addWidget(self.label_26)
        self.horizontalLayout_8 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_8.setObjectName("horizontalLayout_8")
        self.doubleSpinBox_3_volt_s = QtWidgets.QDoubleSpinBox(self.groupBox_11)
        self.doubleSpinBox_3_volt_s.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_3_volt_s.setMaximum(9999.99)
        self.doubleSpinBox_3_volt_s.setObjectName("doubleSpinBox_3_volt_s")
        self.horizontalLayout_8.addWidget(self.doubleSpinBox_3_volt_s)
        self.label_27 = QtWidgets.QLabel(self.groupBox_11)
        self.label_27.setAlignment(QtCore.Qt.AlignCenter)
        self.label_27.setObjectName("label_27")
        self.horizontalLayout_8.addWidget(self.label_27)
        self.doubleSpinBox_3_volt_e = QtWidgets.QDoubleSpinBox(self.groupBox_11)
        self.doubleSpinBox_3_volt_e.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_3_volt_e.setMaximum(9999.99)
        self.doubleSpinBox_3_volt_e.setObjectName("doubleSpinBox_3_volt_e")
        self.horizontalLayout_8.addWidget(self.doubleSpinBox_3_volt_e)
        self.verticalLayout_12.addLayout(self.horizontalLayout_8)
        self.formLayout_6 = QtWidgets.QFormLayout()
        self.formLayout_6.setObjectName("formLayout_6")
        self.label_25 = QtWidgets.QLabel(self.groupBox_11)
        self.label_25.setObjectName("label_25")
        self.formLayout_6.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_25)
        self.doubleSpinBox_3_internal = QtWidgets.QDoubleSpinBox(self.groupBox_11)
        self.doubleSpinBox_3_internal.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_3_internal.setMaximum(9999.99)
        self.doubleSpinBox_3_internal.setObjectName("doubleSpinBox_3_internal")
        self.formLayout_6.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_3_internal)
        self.verticalLayout_12.addLayout(self.formLayout_6)
        self.checkBox_3_volt_list_enable = QtWidgets.QCheckBox(self.groupBox_11)
        self.checkBox_3_volt_list_enable.setObjectName("checkBox_3_volt_list_enable")
        self.verticalLayout_12.addWidget(self.checkBox_3_volt_list_enable)
        self.scrollArea_3_s = QtWidgets.QScrollArea(self.groupBox_11)
        self.scrollArea_3_s.setWidgetResizable(True)
        self.scrollArea_3_s.setObjectName("scrollArea_3_s")
        self.scrollAreaWidgetContents_2 = QtWidgets.QWidget()
        self.scrollAreaWidgetContents_2.setGeometry(QtCore.QRect(0, 0, 744, 362))
        self.scrollAreaWidgetContents_2.setObjectName("scrollAreaWidgetContents_2")
        self.scrollArea_3_s.setWidget(self.scrollAreaWidgetContents_2)
        self.verticalLayout_12.addWidget(self.scrollArea_3_s)
        self.horizontalLayout_9.addWidget(self.groupBox_11)
        self.groupBox_12 = QtWidgets.QGroupBox(self.tab_3)
        self.groupBox_12.setTitle("")
        self.groupBox_12.setObjectName("groupBox_12")
        self.verticalLayout_13 = QtWidgets.QVBoxLayout(self.groupBox_12)
        self.verticalLayout_13.setObjectName("verticalLayout_13")
        self.pushButton_3_start = QtWidgets.QPushButton(self.groupBox_12)
        self.pushButton_3_start.setObjectName("pushButton_3_start")
        self.verticalLayout_13.addWidget(self.pushButton_3_start)
        self.pushButton_3_stop = QtWidgets.QPushButton(self.groupBox_12)
        self.pushButton_3_stop.setObjectName("pushButton_3_stop")
        self.verticalLayout_13.addWidget(self.pushButton_3_stop)
        spacerItem2 = QtWidgets.QSpacerItem(20, 197, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_13.addItem(spacerItem2)
        self.horizontalLayout_9.addWidget(self.groupBox_12)
        self.horizontalLayout_9.setStretch(0, 1)
        self.horizontalLayout_9.setStretch(1, 1)
        self.tabWidget.addTab(self.tab_3, "")
        self.tab_4 = QtWidgets.QWidget()
        self.tab_4.setObjectName("tab_4")
        self.horizontalLayout_7 = QtWidgets.QHBoxLayout(self.tab_4)
        self.horizontalLayout_7.setObjectName("horizontalLayout_7")
        self.groupBox_9 = QtWidgets.QGroupBox(self.tab_4)
        self.groupBox_9.setTitle("")
        self.groupBox_9.setObjectName("groupBox_9")
        self.verticalLayout_10 = QtWidgets.QVBoxLayout(self.groupBox_9)
        self.verticalLayout_10.setObjectName("verticalLayout_10")
        self.formLayout_4 = QtWidgets.QFormLayout()
        self.formLayout_4.setObjectName("formLayout_4")
        self.label_19 = QtWidgets.QLabel(self.groupBox_9)
        self.label_19.setObjectName("label_19")
        self.formLayout_4.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_19)
        self.label_20 = QtWidgets.QLabel(self.groupBox_9)
        self.label_20.setObjectName("label_20")
        self.formLayout_4.setWidget(1, QtWidgets.QFormLayout.LabelRole, self.label_20)
        self.label_21 = QtWidgets.QLabel(self.groupBox_9)
        self.label_21.setObjectName("label_21")
        self.formLayout_4.setWidget(2, QtWidgets.QFormLayout.LabelRole, self.label_21)
        self.label_22 = QtWidgets.QLabel(self.groupBox_9)
        self.label_22.setObjectName("label_22")
        self.formLayout_4.setWidget(3, QtWidgets.QFormLayout.LabelRole, self.label_22)
        self.doubleSpinBox_2_volt1 = QtWidgets.QDoubleSpinBox(self.groupBox_9)
        self.doubleSpinBox_2_volt1.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_2_volt1.setMaximum(9999.99)
        self.doubleSpinBox_2_volt1.setObjectName("doubleSpinBox_2_volt1")
        self.formLayout_4.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_2_volt1)
        self.doubleSpinBox_2_volt2 = QtWidgets.QDoubleSpinBox(self.groupBox_9)
        self.doubleSpinBox_2_volt2.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_2_volt2.setMaximum(9999.99)
        self.doubleSpinBox_2_volt2.setObjectName("doubleSpinBox_2_volt2")
        self.formLayout_4.setWidget(1, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_2_volt2)
        self.doubleSpinBox_2_interval = QtWidgets.QDoubleSpinBox(self.groupBox_9)
        self.doubleSpinBox_2_interval.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_2_interval.setMaximum(9999.99)
        self.doubleSpinBox_2_interval.setObjectName("doubleSpinBox_2_interval")
        self.formLayout_4.setWidget(2, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_2_interval)
        self.spinBox_2_repeat = QtWidgets.QSpinBox(self.groupBox_9)
        self.spinBox_2_repeat.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_2_repeat.setMaximum(9999)
        self.spinBox_2_repeat.setObjectName("spinBox_2_repeat")
        self.formLayout_4.setWidget(3, QtWidgets.QFormLayout.FieldRole, self.spinBox_2_repeat)
        self.verticalLayout_10.addLayout(self.formLayout_4)
        spacerItem3 = QtWidgets.QSpacerItem(20, 123, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_10.addItem(spacerItem3)
        self.verticalLayout_10.setStretch(0, 1)
        self.verticalLayout_10.setStretch(1, 1)
        self.horizontalLayout_7.addWidget(self.groupBox_9)
        self.groupBox_10 = QtWidgets.QGroupBox(self.tab_4)
        self.groupBox_10.setTitle("")
        self.groupBox_10.setObjectName("groupBox_10")
        self.verticalLayout_11 = QtWidgets.QVBoxLayout(self.groupBox_10)
        self.verticalLayout_11.setObjectName("verticalLayout_11")
        self.pushButton_2_start = QtWidgets.QPushButton(self.groupBox_10)
        self.pushButton_2_start.setObjectName("pushButton_2_start")
        self.verticalLayout_11.addWidget(self.pushButton_2_start)
        self.pushButton_2_stop = QtWidgets.QPushButton(self.groupBox_10)
        self.pushButton_2_stop.setObjectName("pushButton_2_stop")
        self.verticalLayout_11.addWidget(self.pushButton_2_stop)
        spacerItem4 = QtWidgets.QSpacerItem(20, 170, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_11.addItem(spacerItem4)
        self.formLayout_5 = QtWidgets.QFormLayout()
        self.formLayout_5.setObjectName("formLayout_5")
        self.label_23 = QtWidgets.QLabel(self.groupBox_10)
        self.label_23.setObjectName("label_23")
        self.formLayout_5.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_23)
        self.lineEdit_2_remain_repeat = QtWidgets.QLineEdit(self.groupBox_10)
        self.lineEdit_2_remain_repeat.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_2_remain_repeat.setObjectName("lineEdit_2_remain_repeat")
        self.formLayout_5.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.lineEdit_2_remain_repeat)
        self.verticalLayout_11.addLayout(self.formLayout_5)
        self.horizontalLayout_7.addWidget(self.groupBox_10)
        self.horizontalLayout_7.setStretch(0, 1)
        self.horizontalLayout_7.setStretch(1, 1)
        self.tabWidget.addTab(self.tab_4, "")
        self.tab_6 = QtWidgets.QWidget()
        self.tab_6.setObjectName("tab_6")
        self.horizontalLayout_10 = QtWidgets.QHBoxLayout(self.tab_6)
        self.horizontalLayout_10.setObjectName("horizontalLayout_10")
        self.groupBox_13 = QtWidgets.QGroupBox(self.tab_6)
        self.groupBox_13.setTitle("")
        self.groupBox_13.setObjectName("groupBox_13")
        self.verticalLayout_14 = QtWidgets.QVBoxLayout(self.groupBox_13)
        self.verticalLayout_14.setObjectName("verticalLayout_14")
        self.formLayout_7 = QtWidgets.QFormLayout()
        self.formLayout_7.setObjectName("formLayout_7")
        self.label_14 = QtWidgets.QLabel(self.groupBox_13)
        self.label_14.setObjectName("label_14")
        self.formLayout_7.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_14)
        self.doubleSpinBox_4_volt_s = QtWidgets.QDoubleSpinBox(self.groupBox_13)
        self.doubleSpinBox_4_volt_s.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_4_volt_s.setDecimals(3)
        self.doubleSpinBox_4_volt_s.setMaximum(9999.99)
        self.doubleSpinBox_4_volt_s.setObjectName("doubleSpinBox_4_volt_s")
        self.formLayout_7.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_4_volt_s)
        self.label_24 = QtWidgets.QLabel(self.groupBox_13)
        self.label_24.setObjectName("label_24")
        self.formLayout_7.setWidget(1, QtWidgets.QFormLayout.LabelRole, self.label_24)
        self.doubleSpinBox_4_volt_e = QtWidgets.QDoubleSpinBox(self.groupBox_13)
        self.doubleSpinBox_4_volt_e.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_4_volt_e.setDecimals(3)
        self.doubleSpinBox_4_volt_e.setMaximum(9999.99)
        self.doubleSpinBox_4_volt_e.setObjectName("doubleSpinBox_4_volt_e")
        self.formLayout_7.setWidget(1, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_4_volt_e)
        self.label_28 = QtWidgets.QLabel(self.groupBox_13)
        self.label_28.setObjectName("label_28")
        self.formLayout_7.setWidget(2, QtWidgets.QFormLayout.LabelRole, self.label_28)
        self.doubleSpinBox_4_step = QtWidgets.QDoubleSpinBox(self.groupBox_13)
        self.doubleSpinBox_4_step.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_4_step.setDecimals(3)
        self.doubleSpinBox_4_step.setMaximum(9999.99)
        self.doubleSpinBox_4_step.setObjectName("doubleSpinBox_4_step")
        self.formLayout_7.setWidget(2, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_4_step)
        self.label_29 = QtWidgets.QLabel(self.groupBox_13)
        self.label_29.setObjectName("label_29")
        self.formLayout_7.setWidget(3, QtWidgets.QFormLayout.LabelRole, self.label_29)
        self.doubleSpinBox_4_time = QtWidgets.QDoubleSpinBox(self.groupBox_13)
        self.doubleSpinBox_4_time.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_4_time.setDecimals(3)
        self.doubleSpinBox_4_time.setMaximum(9999.99)
        self.doubleSpinBox_4_time.setObjectName("doubleSpinBox_4_time")
        self.formLayout_7.setWidget(3, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_4_time)
        self.label_30 = QtWidgets.QLabel(self.groupBox_13)
        self.label_30.setObjectName("label_30")
        self.formLayout_7.setWidget(4, QtWidgets.QFormLayout.LabelRole, self.label_30)
        self.spinBox_4_repeat = QtWidgets.QSpinBox(self.groupBox_13)
        self.spinBox_4_repeat.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_4_repeat.setMaximum(9999999)
        self.spinBox_4_repeat.setObjectName("spinBox_4_repeat")
        self.formLayout_7.setWidget(4, QtWidgets.QFormLayout.FieldRole, self.spinBox_4_repeat)
        self.label_32 = QtWidgets.QLabel(self.groupBox_13)
        self.label_32.setObjectName("label_32")
        self.formLayout_7.setWidget(5, QtWidgets.QFormLayout.LabelRole, self.label_32)
        self.comboBox_4_mode = QtWidgets.QComboBox(self.groupBox_13)
        self.comboBox_4_mode.setMinimumSize(QtCore.QSize(0, 45))
        self.comboBox_4_mode.setObjectName("comboBox_4_mode")
        self.comboBox_4_mode.addItem("")
        self.comboBox_4_mode.addItem("")
        self.formLayout_7.setWidget(5, QtWidgets.QFormLayout.FieldRole, self.comboBox_4_mode)
        self.verticalLayout_14.addLayout(self.formLayout_7)
        spacerItem5 = QtWidgets.QSpacerItem(20, 143, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_14.addItem(spacerItem5)
        self.horizontalLayout_10.addWidget(self.groupBox_13)
        self.groupBox_14 = QtWidgets.QGroupBox(self.tab_6)
        self.groupBox_14.setTitle("")
        self.groupBox_14.setObjectName("groupBox_14")
        self.verticalLayout_15 = QtWidgets.QVBoxLayout(self.groupBox_14)
        self.verticalLayout_15.setObjectName("verticalLayout_15")
        self.pushButton_4_start = QtWidgets.QPushButton(self.groupBox_14)
        self.pushButton_4_start.setObjectName("pushButton_4_start")
        self.verticalLayout_15.addWidget(self.pushButton_4_start)
        self.pushButton_4_stop = QtWidgets.QPushButton(self.groupBox_14)
        self.pushButton_4_stop.setObjectName("pushButton_4_stop")
        self.verticalLayout_15.addWidget(self.pushButton_4_stop)
        spacerItem6 = QtWidgets.QSpacerItem(20, 210, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_15.addItem(spacerItem6)
        self.formLayout_8 = QtWidgets.QFormLayout()
        self.formLayout_8.setObjectName("formLayout_8")
        self.label_31 = QtWidgets.QLabel(self.groupBox_14)
        self.label_31.setObjectName("label_31")
        self.formLayout_8.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_31)
        self.lineEdit_4_remain_repeat = QtWidgets.QLineEdit(self.groupBox_14)
        self.lineEdit_4_remain_repeat.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_4_remain_repeat.setObjectName("lineEdit_4_remain_repeat")
        self.formLayout_8.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.lineEdit_4_remain_repeat)
        self.verticalLayout_15.addLayout(self.formLayout_8)
        self.horizontalLayout_10.addWidget(self.groupBox_14)
        self.horizontalLayout_10.setStretch(0, 1)
        self.horizontalLayout_10.setStretch(1, 1)
        self.tabWidget.addTab(self.tab_6, "")
        self.tab_2 = QtWidgets.QWidget()
        self.tab_2.setObjectName("tab_2")
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout(self.tab_2)
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.groupBox_5 = QtWidgets.QGroupBox(self.tab_2)
        self.groupBox_5.setTitle("")
        self.groupBox_5.setObjectName("groupBox_5")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.groupBox_5)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.formLayout = QtWidgets.QFormLayout()
        self.formLayout.setObjectName("formLayout")
        self.label_8 = QtWidgets.QLabel(self.groupBox_5)
        self.label_8.setObjectName("label_8")
        self.formLayout.setWidget(2, QtWidgets.QFormLayout.LabelRole, self.label_8)
        self.doubleSpinBox_f_curr = QtWidgets.QDoubleSpinBox(self.groupBox_5)
        self.doubleSpinBox_f_curr.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_f_curr.setDecimals(3)
        self.doubleSpinBox_f_curr.setObjectName("doubleSpinBox_f_curr")
        self.formLayout.setWidget(2, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_f_curr)
        self.label_9 = QtWidgets.QLabel(self.groupBox_5)
        self.label_9.setObjectName("label_9")
        self.formLayout.setWidget(1, QtWidgets.QFormLayout.LabelRole, self.label_9)
        self.doubleSpinBox_f_volt = QtWidgets.QDoubleSpinBox(self.groupBox_5)
        self.doubleSpinBox_f_volt.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_f_volt.setDecimals(3)
        self.doubleSpinBox_f_volt.setObjectName("doubleSpinBox_f_volt")
        self.formLayout.setWidget(1, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_f_volt)
        self.label_10 = QtWidgets.QLabel(self.groupBox_5)
        self.label_10.setObjectName("label_10")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_10)
        self.comboBox_f_mode = QtWidgets.QComboBox(self.groupBox_5)
        self.comboBox_f_mode.setMinimumSize(QtCore.QSize(0, 45))
        self.comboBox_f_mode.setObjectName("comboBox_f_mode")
        self.comboBox_f_mode.addItem("")
        self.comboBox_f_mode.addItem("")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.comboBox_f_mode)
        self.verticalLayout_6.addLayout(self.formLayout)
        self.horizontalLayout_3.addWidget(self.groupBox_5)
        self.groupBox_6 = QtWidgets.QGroupBox(self.tab_2)
        self.groupBox_6.setTitle("")
        self.groupBox_6.setObjectName("groupBox_6")
        self.verticalLayout_7 = QtWidgets.QVBoxLayout(self.groupBox_6)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.pushButton_f_exec = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_f_exec.setObjectName("pushButton_f_exec")
        self.verticalLayout_7.addWidget(self.pushButton_f_exec)
        spacerItem7 = QtWidgets.QSpacerItem(20, 277, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_7.addItem(spacerItem7)
        self.pushButton_f_off = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_f_off.setObjectName("pushButton_f_off")
        self.verticalLayout_7.addWidget(self.pushButton_f_off)
        self.pushButton_f_on = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_f_on.setObjectName("pushButton_f_on")
        self.verticalLayout_7.addWidget(self.pushButton_f_on)
        self.horizontalLayout_3.addWidget(self.groupBox_6)
        self.tabWidget.addTab(self.tab_2, "")
        self.tab = QtWidgets.QWidget()
        self.tab.setObjectName("tab")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.tab)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.groupBox_2 = QtWidgets.QGroupBox(self.tab)
        self.groupBox_2.setTitle("")
        self.groupBox_2.setObjectName("groupBox_2")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.groupBox_2)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.formLayout_2 = QtWidgets.QFormLayout()
        self.formLayout_2.setObjectName("formLayout_2")
        self.label = QtWidgets.QLabel(self.groupBox_2)
        self.label.setObjectName("label")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label)
        self.label_2 = QtWidgets.QLabel(self.groupBox_2)
        self.label_2.setObjectName("label_2")
        self.formLayout_2.setWidget(3, QtWidgets.QFormLayout.LabelRole, self.label_2)
        self.label_3 = QtWidgets.QLabel(self.groupBox_2)
        self.label_3.setObjectName("label_3")
        self.formLayout_2.setWidget(4, QtWidgets.QFormLayout.LabelRole, self.label_3)
        self.label_4 = QtWidgets.QLabel(self.groupBox_2)
        self.label_4.setObjectName("label_4")
        self.formLayout_2.setWidget(5, QtWidgets.QFormLayout.LabelRole, self.label_4)
        self.comboBox_list_index = QtWidgets.QComboBox(self.groupBox_2)
        self.comboBox_list_index.setObjectName("comboBox_list_index")
        self.comboBox_list_index.addItem("")
        self.comboBox_list_index.addItem("")
        self.comboBox_list_index.addItem("")
        self.comboBox_list_index.addItem("")
        self.comboBox_list_index.addItem("")
        self.comboBox_list_index.addItem("")
        self.comboBox_list_index.addItem("")
        self.comboBox_list_index.addItem("")
        self.comboBox_list_index.addItem("")
        self.comboBox_list_index.addItem("")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.comboBox_list_index)
        self.comboBox_list_func = QtWidgets.QComboBox(self.groupBox_2)
        self.comboBox_list_func.setMinimumSize(QtCore.QSize(0, 45))
        self.comboBox_list_func.setObjectName("comboBox_list_func")
        self.comboBox_list_func.addItem("")
        self.comboBox_list_func.addItem("")
        self.formLayout_2.setWidget(3, QtWidgets.QFormLayout.FieldRole, self.comboBox_list_func)
        self.comboBox_list_term = QtWidgets.QComboBox(self.groupBox_2)
        self.comboBox_list_term.setMinimumSize(QtCore.QSize(0, 45))
        self.comboBox_list_term.setObjectName("comboBox_list_term")
        self.comboBox_list_term.addItem("")
        self.comboBox_list_term.addItem("")
        self.formLayout_2.setWidget(4, QtWidgets.QFormLayout.FieldRole, self.comboBox_list_term)
        self.spinBox_list_repeat = QtWidgets.QSpinBox(self.groupBox_2)
        self.spinBox_list_repeat.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_list_repeat.setMinimum(1)
        self.spinBox_list_repeat.setMaximum(65535)
        self.spinBox_list_repeat.setObjectName("spinBox_list_repeat")
        self.formLayout_2.setWidget(5, QtWidgets.QFormLayout.FieldRole, self.spinBox_list_repeat)
        self.label_6 = QtWidgets.QLabel(self.groupBox_2)
        self.label_6.setObjectName("label_6")
        self.formLayout_2.setWidget(1, QtWidgets.QFormLayout.LabelRole, self.label_6)
        self.label_7 = QtWidgets.QLabel(self.groupBox_2)
        self.label_7.setObjectName("label_7")
        self.formLayout_2.setWidget(2, QtWidgets.QFormLayout.LabelRole, self.label_7)
        self.doubleSpinBox_volt = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_volt.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_volt.setObjectName("doubleSpinBox_volt")
        self.formLayout_2.setWidget(1, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_volt)
        self.doubleSpinBox_curr = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_curr.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_curr.setObjectName("doubleSpinBox_curr")
        self.formLayout_2.setWidget(2, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_curr)
        self.verticalLayout_3.addLayout(self.formLayout_2)
        self.label_5 = QtWidgets.QLabel(self.groupBox_2)
        self.label_5.setObjectName("label_5")
        self.verticalLayout_3.addWidget(self.label_5)
        self.scrollArea = QtWidgets.QScrollArea(self.groupBox_2)
        self.scrollArea.setWidgetResizable(True)
        self.scrollArea.setObjectName("scrollArea")
        self.scrollAreaWidgetContents = QtWidgets.QWidget()
        self.scrollAreaWidgetContents.setGeometry(QtCore.QRect(0, 0, 1415, 205))
        self.scrollAreaWidgetContents.setObjectName("scrollAreaWidgetContents")
        self.scrollArea.setWidget(self.scrollAreaWidgetContents)
        self.verticalLayout_3.addWidget(self.scrollArea)
        self.horizontalLayout.addWidget(self.groupBox_2)
        self.groupBox_4 = QtWidgets.QGroupBox(self.tab)
        self.groupBox_4.setTitle("")
        self.groupBox_4.setObjectName("groupBox_4")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.groupBox_4)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.pushButton_save = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_save.setObjectName("pushButton_save")
        self.verticalLayout_4.addWidget(self.pushButton_save)
        self.pushButton_save_exec = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_save_exec.setObjectName("pushButton_save_exec")
        self.verticalLayout_4.addWidget(self.pushButton_save_exec)
        self.pushButton_exec = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_exec.setObjectName("pushButton_exec")
        self.verticalLayout_4.addWidget(self.pushButton_exec)
        spacerItem8 = QtWidgets.QSpacerItem(20, 161, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_4.addItem(spacerItem8)
        self.pushButton_trigger = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_trigger.setObjectName("pushButton_trigger")
        self.verticalLayout_4.addWidget(self.pushButton_trigger)
        self.pushButton_pause = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_pause.setObjectName("pushButton_pause")
        self.verticalLayout_4.addWidget(self.pushButton_pause)
        self.pushButton_pause_release = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_pause_release.setObjectName("pushButton_pause_release")
        self.verticalLayout_4.addWidget(self.pushButton_pause_release)
        self.pushButton_stop = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_stop.setObjectName("pushButton_stop")
        self.verticalLayout_4.addWidget(self.pushButton_stop)
        self.horizontalLayout.addWidget(self.groupBox_4)
        self.tabWidget.addTab(self.tab, "")
        self.verticalLayout.addWidget(self.tabWidget)
        self.verticalLayout_2.addWidget(self.groupBox)
        self.groupBox_3 = QtWidgets.QGroupBox(Form)
        self.groupBox_3.setTitle("")
        self.groupBox_3.setObjectName("groupBox_3")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.groupBox_3)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.pushButton_connect_device = QtWidgets.QPushButton(self.groupBox_3)
        self.pushButton_connect_device.setObjectName("pushButton_connect_device")
        self.verticalLayout_5.addWidget(self.pushButton_connect_device)
        self.plainTextEdit = QtWidgets.QPlainTextEdit(self.groupBox_3)
        self.plainTextEdit.setObjectName("plainTextEdit")
        self.verticalLayout_5.addWidget(self.plainTextEdit)
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        spacerItem9 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_2.addItem(spacerItem9)
        self.pushButton_clear = QtWidgets.QPushButton(self.groupBox_3)
        self.pushButton_clear.setObjectName("pushButton_clear")
        self.horizontalLayout_2.addWidget(self.pushButton_clear)
        self.verticalLayout_5.addLayout(self.horizontalLayout_2)
        self.verticalLayout_2.addWidget(self.groupBox_3)
        self.verticalLayout_2.setStretch(0, 2)
        self.verticalLayout_2.setStretch(1, 1)

        self.retranslateUi(Form)
        self.tabWidget.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.label_11.setText(_translate("Form", "电压(V)："))
        self.label_12.setText(_translate("Form", "上电时间(S)："))
        self.label_13.setText(_translate("Form", "下电时间(S)："))
        self.checkBox_1_random_time_enable.setText(_translate("Form", "是否使用随机时间"))
        self.label_17.setText(_translate("Form", "上电时间范围(S)："))
        self.label_15.setText(_translate("Form", "~"))
        self.label_18.setText(_translate("Form", "下点时间范围(S)："))
        self.label_16.setText(_translate("Form", "~"))
        self.pushButton_1_start.setText(_translate("Form", "开始"))
        self.pushButton_1_stop.setText(_translate("Form", "结束"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_5), _translate("Form", "上下电控制"))
        self.label_26.setText(_translate("Form", "电压范围(V)："))
        self.label_27.setText(_translate("Form", "~"))
        self.label_25.setText(_translate("Form", "间隔时间(S)："))
        self.checkBox_3_volt_list_enable.setText(_translate("Form", "启用电压列表"))
        self.pushButton_3_start.setText(_translate("Form", "开始"))
        self.pushButton_3_stop.setText(_translate("Form", "结束"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_3), _translate("Form", "随机电压"))
        self.label_19.setText(_translate("Form", "电压1(V)："))
        self.label_20.setText(_translate("Form", "电压2(V)："))
        self.label_21.setText(_translate("Form", "切换时间(S)："))
        self.label_22.setText(_translate("Form", "循环次数："))
        self.pushButton_2_start.setText(_translate("Form", "开始"))
        self.pushButton_2_stop.setText(_translate("Form", "结束"))
        self.label_23.setText(_translate("Form", "剩余次数："))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_4), _translate("Form", "高低压控制"))
        self.label_14.setText(_translate("Form", "起始电压(V)："))
        self.label_24.setText(_translate("Form", "终点电压(V)："))
        self.label_28.setText(_translate("Form", "步进值(V)："))
        self.label_29.setText(_translate("Form", "停留时间(S)："))
        self.label_30.setText(_translate("Form", "循环次数："))
        self.label_32.setText(_translate("Form", "模式："))
        self.comboBox_4_mode.setItemText(0, _translate("Form", "模式1"))
        self.comboBox_4_mode.setItemText(1, _translate("Form", "模式2"))
        self.pushButton_4_start.setText(_translate("Form", "开始"))
        self.pushButton_4_stop.setText(_translate("Form", "结束"))
        self.label_31.setText(_translate("Form", "剩余次数："))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_6), _translate("Form", "步进电压控制"))
        self.label_8.setText(_translate("Form", "电流(A)："))
        self.label_9.setText(_translate("Form", "电压(V)："))
        self.label_10.setText(_translate("Form", "输出模式："))
        self.comboBox_f_mode.setItemText(0, _translate("Form", "VOLTage"))
        self.comboBox_f_mode.setItemText(1, _translate("Form", "CURRent"))
        self.pushButton_f_exec.setText(_translate("Form", "执行"))
        self.pushButton_f_off.setText(_translate("Form", "停止输出"))
        self.pushButton_f_on.setText(_translate("Form", "开启输出"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_2), _translate("Form", "Fixed模式"))
        self.label.setText(_translate("Form", "文件:"))
        self.label_2.setText(_translate("Form", "功能模式："))
        self.label_3.setText(_translate("Form", "结束方式："))
        self.label_4.setText(_translate("Form", "重复次数："))
        self.comboBox_list_index.setItemText(0, _translate("Form", "1"))
        self.comboBox_list_index.setItemText(1, _translate("Form", "2"))
        self.comboBox_list_index.setItemText(2, _translate("Form", "3"))
        self.comboBox_list_index.setItemText(3, _translate("Form", "4"))
        self.comboBox_list_index.setItemText(4, _translate("Form", "5"))
        self.comboBox_list_index.setItemText(5, _translate("Form", "6"))
        self.comboBox_list_index.setItemText(6, _translate("Form", "7"))
        self.comboBox_list_index.setItemText(7, _translate("Form", "8"))
        self.comboBox_list_index.setItemText(8, _translate("Form", "9"))
        self.comboBox_list_index.setItemText(9, _translate("Form", "10"))
        self.comboBox_list_func.setItemText(0, _translate("Form", "VOLTage"))
        self.comboBox_list_func.setItemText(1, _translate("Form", "CURRent"))
        self.comboBox_list_term.setItemText(0, _translate("Form", "NORMal"))
        self.comboBox_list_term.setItemText(1, _translate("Form", "LAST"))
        self.label_6.setText(_translate("Form", "电压(V)："))
        self.label_7.setText(_translate("Form", "电流(A)："))
        self.label_5.setText(_translate("Form", "步骤（1-100）："))
        self.pushButton_save.setText(_translate("Form", "保存配置"))
        self.pushButton_save_exec.setText(_translate("Form", "保存并执行"))
        self.pushButton_exec.setText(_translate("Form", "不保存仅执行"))
        self.pushButton_trigger.setText(_translate("Form", "触发"))
        self.pushButton_pause.setText(_translate("Form", "暂停"))
        self.pushButton_pause_release.setText(_translate("Form", "解除暂停"))
        self.pushButton_stop.setText(_translate("Form", "停止"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab), _translate("Form", "List模式"))
        self.pushButton_connect_device.setText(_translate("Form", "连接设备"))
        self.pushButton_clear.setText(_translate("Form", "清除"))
