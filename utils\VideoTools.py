import os
import shutil
import subprocess

import cv2
import numpy as np
from PIL import Image
from PyQt5.QtCore import QObject

pic_num = 13


class VideoCombine(QObject):
    __logTag = "VideoCombiner"
    img_dir = None
    target_file = None
    img = None

    def __init__(self):
        super().__init__()
        self.video_shape = None

    def get_video_shape(self, img_dir, target_file, fps):
        self.img_dir = img_dir
        self.target_file = target_file
        print("self.img_dir:", self.img_dir)
        sample_img = np.random.choice(self.img_dir)
        if os.path.exists(sample_img):
            self.img = cv2.imdecode(np.fromfile(sample_img, dtype=np.uint8), -1)
            self.video_shape = self.img.shape
            # self.combine(fps)
        else:
            print(self.__logTag, '=> Error: ' + '{} not found or open failed, try again.'.format(sample_img))
            exit(0)

    def combine(self, fps):
        size = (self.video_shape[1], self.video_shape[0])
        print(self.__logTag, '=> all {} frames to solve.'.format(len(self.img_dir)))
        video_writer = cv2.VideoWriter(self.target_file, cv2.VideoWriter_fourcc('m', 'p', '4', 'v'), fps, size)
        n = 0
        m = 0
        work_folder = os.path.join(os.path.expanduser('~'), "mate_nomi1.5", "transpose_img")
        if not os.path.exists(work_folder):
            os.makedirs(work_folder)
        else:
            for i in os.listdir(work_folder):
                path = os.path.join(work_folder, i)
                if os.path.exists(path):
                    os.remove(path)

        # self.title_signal.emit('Pictures are being combined to video')
        for item in self.img_dir:
            current_img = Image.open(item)
            trans_img = current_img.transpose(Image.ROTATE_270)
            if 0 <= m < 10:
                trans_img.save(os.path.join(work_folder, '000{}.png'.format(m)))
            elif 10 <= m < 100:
                trans_img.save(os.path.join(work_folder, '00{}.png'.format(m)))
            elif 100 <= m < 1000:
                trans_img.save(os.path.join(work_folder, '0{}.png'.format(m)))
            elif 1000 <= m < 10000:
                trans_img.save(os.path.join(work_folder, '{}.png'.format(m)))
            m += 1
            progress = int(m / len(self.img_dir) * 100)

        # self.title_signal.emit('Video is being compressed')
        for img in os.listdir(work_folder):
            img = cv2.imdecode(np.fromfile(os.path.join(work_folder,img), dtype=np.uint8), cv2.COLOR_BGR2RGB)
            n += 1
            # progress = int(n / len(os.listdir(work_folder)) * 100)
            video_writer.write(img)

        video_writer.release()
        print("done!")

    @staticmethod
    def copy_and_rename_image(img_path, copies=pic_num):
        file_path = img_path.strip(os.path.basename(img_path))
        video_dir = os.path.join(file_path, "video")
        if not os.path.exists(video_dir):
            os.mkdir(video_dir)
        else:
            # 删除video_dir下所有文件
            for file in os.listdir(video_dir):
                os.remove(os.path.join(video_dir, file))
        for i in range(copies):
            new_name = "images" + str(i).zfill(5) + ".png"
            new_path = os.path.join(video_dir, new_name)
            shutil.copy(img_path, new_path)
        return video_dir

    def create_h264(self, img_path, fps=pic_num):
        """
        输入一张图片 创建他的h264文件 输出他的h164文件地址
        :param img_path: 输入图片地址
        :param fps: 帧率
        :return: h164文件地址
        """
        path = self.copy_and_rename_image(img_path)
        target_file = os.path.join(img_path.strip(os.path.basename(img_path)), "images.mp4")
        h264_video_path = os.path.join(img_path.strip(os.path.basename(img_path)), "images.h264")
        print("h264_video_path:", h264_video_path)
        dir = [os.path.join(path, i) for i in os.listdir(path)]
        self.get_video_shape(img_dir=dir, target_file=target_file, fps=fps)
        self.combine(fps)
        ffmpeg_path = os.path.join(os.getcwd(), r"ffmpeg\bin\ffmpeg.exe")
        # print("ffmpeg_path:",ffmpeg_path)
        video_transfor = r'{} -y -i {} -vcodec h264 -preset fast -b:v 2000k {}'.format(
            ffmpeg_path, target_file, h264_video_path)
        out = subprocess.Popen(video_transfor, stderr=subprocess.DEVNULL,
                               stdin=subprocess.DEVNULL,
                               shell=True,
                               stdout=subprocess.DEVNULL,
                               encoding="utf8")
        out.communicate()
        return h264_video_path

    def create_h264_from_folder(self, img_path, fps=10):
        target_file = os.path.join(os.path.dirname(img_path.strip(os.path.basename(img_path))), "images.mp4")
        h264_video_path = os.path.join(os.path.dirname(img_path.strip(os.path.basename(img_path))), "images.h264")
        print("h264_video_path:", h264_video_path)
        path = img_path
        dir = [os.path.join(path, i) for i in os.listdir(path)]
        self.get_video_shape(img_dir=dir, target_file=target_file, fps=fps)
        self.combine(fps)
        ffmpeg_path = os.path.join(os.getcwd(), r"ffmpeg\bin\ffmpeg.exe")
        # print("ffmpeg_path:",ffmpeg_path)
        video_transfor = r'{} -y -i {} -vcodec h264 -preset fast -b:v 2000k {}'.format(
            ffmpeg_path, target_file, h264_video_path)
        out = subprocess.Popen(video_transfor, stderr=subprocess.DEVNULL,
                               stdin=subprocess.DEVNULL,
                               shell=True,
                               stdout=subprocess.DEVNULL,
                               encoding="utf8")
        out.communicate()
        return h264_video_path


if __name__ == '__main__':
    img_path = r"C:\Users\<USER>\Downloads\2799201_jigsaw_processing_business_information_piece_icon\1.png"
    v = VideoCombine()
    h164_file = v.create_h264(img_path)
    print(h164_file)
