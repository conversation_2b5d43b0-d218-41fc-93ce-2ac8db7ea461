import threading
import time

import serial

from common.LogUtils import logger
from photics import photics_manager, PhoticsFunction
from photics.color_analyzer_tools.manager.SignalCenter import signal_center


class ColorAnalyzerSerial(object):

    def __init__(self, port, baudrate, read_serial_interval):
        super(ColorAnalyzerSerial).__init__()
        self.port = port
        self.baudrate = baudrate
        self.serial = serial.Serial(self.port, self.baudrate, timeout=1)
        self.read_serial_timer = None
        self.read_serial_interval = read_serial_interval

    def start_read_serial(self):
        self.read_serial_data()
        self.read_serial_timer = threading.Timer(interval=self.read_serial_interval, function=self.start_read_serial)
        self.read_serial_timer.start()

    def stop_read_serial(self):
        logger.info('stop_read_serial')
        if self.read_serial_timer is not None:
            self.read_serial_timer.cancel()

    def read_serial_data(self):
        try:
            data = self.serial.readlines()
            if len(data) > 0:
                logger.info(f"read_serial_data data={data}")
                self.analysis_measure_data(data)
        except Exception as e:
            logger.error('read_serial_data exception: %s' % str(e.args))

    def get_serial_status(self):
        return self.serial.is_open

    def write_to_serial(self, cmd):
        logger.info("write_to_serial cmd={}".format(cmd))
        try:
            if self.serial is not None:
                self.serial.write(cmd)
        except Exception as e:
            logger.error("write_to_serial exception: {}".format(e.args))

    def read_from_serial(self):
        try:
            if self.serial is None:
                return None
            line = self.serial.readlines()
            logger.info(f"read_from_serial line={line}")
        except Exception as e:
            logger.error(f"read_from_serial exception={str(e.args)}")
            return None
        return line

    def switch_channel(self, channel):
        logger.info(f"switch_channel channel={channel}")
        # 通道设置指令
        cmd = b'COM,' + str(channel).encode() + b'\r'
        self.write_to_serial(cmd)
        ret = self.read_from_serial()
        logger.info(f"switch_channel ret={ret}")

    def set_xyLv_mode(self, channel):
        # 数据类型指令
        cmd = b'STR,0\r'
        self.write_to_serial(cmd)
        ret = self.read_from_serial()
        logger.info('set_xyLv_mode ret_STR=%s', ret)
        # 数据返回格式指令
        cmd = b'MDS,0\r'
        self.write_to_serial(cmd)
        ret = self.read_from_serial()
        logger.info('set_xyLv_mode ret_MDS=%s', ret)
        # 数据采集模式指令
        # 0:SLOW 1:FAST 2:LTDAUTO 3:AUTO
        cmd = b'FSC,[0]\r'
        self.write_to_serial(cmd)
        ret = self.read_from_serial()
        logger.info('set_xyLv_mode ret_FSC=%s', ret)
        # 通道设置指令
        cmd = b'COM,' + str(channel).encode() + b'\r'
        self.write_to_serial(cmd)
        ret = self.read_from_serial()
        logger.info('set_xyLv_mode ret_COM=%s', ret)
        #  校准指令
        cmd = b'ZRC\r'
        self.write_to_serial(cmd)
        ret = self.read_from_serial()
        logger.info('set_xyLv_mode ret_ZRC=%s', ret)

        threading.Timer(interval=5, function=self.read_from_serial).start()

    def set_XYZ_mode(self):
        # 数据返回格式指令
        cmd = b'MDS,7\r'
        self.write_to_serial(cmd)
        ret = self.read_from_serial()
        logger.info('set_XYZ_mode ret_MDS=%s', ret)

        self.measure()

    def set_flicker_mode(self,channel=0,freq=2,sample=10):
        # cmd = b'MDS,8\r'
        # self.write_to_serial(cmd)
        # ret = self.read_from_serial()
        # logger.info('set_flicker_mode ret_MDS=%s', ret)
        # 数据类型指令
        cmd = b'ZRC\r'
        self.write_to_serial(cmd)
        time.sleep(5)
        ret = self.read_from_serial()
        logger.info('set_flicker_mode ret_ZRC=%s', ret)

        cmd = b'MCH,0\r'
        self.write_to_serial(cmd)
        ret = self.read_from_serial()
        logger.info('set_flicker_mode ret_MCH=%s', ret)
        cmd = b'SCS,4,60\r'
        self.write_to_serial(cmd)
        ret = self.read_from_serial()
        logger.info('set_flicker_mode ret_SCS=%s', ret)
        # 0:SLOW 1:FAST 2:LTDAUTO 3:AUTO
        cmd = b'FSC,[1]\r'
        self.write_to_serial(cmd)
        ret = self.read_from_serial()
        logger.info('set_flicker_mode ret_FSC=%s', ret)
        # 数据返回格式指令
        cmd = b'MDS,0\r'
        self.write_to_serial(cmd)
        ret = self.read_from_serial()
        logger.info('set_flicker_mode ret_MDS=%s', ret)
        # 数据采集模式指令
        cmd = b'FCS,1,0\r'
        self.write_to_serial(cmd)
        ret = self.read_from_serial()
        logger.info('set_flicker_mode ret_FCS=%s', ret)
        # 数据采集模式指令
        cmd = f'ACS,{freq},{sample}\r'.encode()
        self.write_to_serial(cmd)
        ret = self.read_from_serial()
        logger.info('set_flicker_mode ret_ACS=%s', ret)
        # 数据采集模式指令
        # 通道设置指令
        # cmd = b'COM,' + str(channel).encode() + b'\r'
        # self.write_to_serial(cmd)
        # ret = self.read_from_serial()
        # logger.info('set_flicker_mode ret_COM=%s', ret)
        # #  校准指令


    def set_response_test_mode(self,flicker_method = 2):
        cmd = b'ZRC\r'
        self.write_to_serial(cmd)
        time.sleep(5)
        ret = self.read_from_serial()
        logger.info('set_flicker_mode ret_ZRC=%s', ret)

        cmd = b'MCH,0\r'
        self.write_to_serial(cmd)
        ret = self.read_from_serial()
        logger.info('set_flicker_mode ret_MCH=%s', ret)
        cmd = b'SCS,4,60\r'
        self.write_to_serial(cmd)
        ret = self.read_from_serial()
        logger.info('set_flicker_mode ret_SCS=%s', ret)
        # 0:SLOW 1:FAST 2:LTDAUTO 3:AUTO
        cmd = b'FSC,[1]\r'
        self.write_to_serial(cmd)
        ret = self.read_from_serial()
        logger.info('set_flicker_mode ret_FSC=%s', ret)
        # 数据返回格式指令
        cmd = b'MDS,0\r'
        self.write_to_serial(cmd)
        ret = self.read_from_serial()
        logger.info('set_flicker_mode ret_MDS=%s', ret)
        # 数据采集模式指令
        cmd = f'FCS,{flicker_method},0\r'.encode()
        self.write_to_serial(cmd)
        ret = self.read_from_serial()
        logger.info('set_flicker_mode ret_FCS=%s', ret)
        if flicker_method == 1:
        # 数据采集模式指令
            cmd = f'ACS,1,10\r'.encode()
            self.write_to_serial(cmd)
            ret = self.read_from_serial()
            logger.info('set_flicker_mode ret_ACS=%s', ret)


    def measure(self):
        cmd = b'MES,0\r'
        self.write_to_serial(cmd)

    def analysis_measure_data(self, data):
        try:
            data = data[0]
            data = data.decode("utf-8")
            data = data.split("P1 ")[1]
            data = data.split(";")
            x = float(data[0].strip()) / 1000
            y = float(data[1].strip()) / 1000
            lv = float(data[2].strip())
            if photics_manager.get_current_analyzer_func() == PhoticsFunction.GAMMA_CURVE:
                signal_center.gamma_curve_measure_data_signal.emit((x, y, lv))
            elif photics_manager.get_current_analyzer_func() == PhoticsFunction.BRIGHTNESS_CURVE:
                signal_center.brightness_curve_measure_data_signal.emit((x, y, lv))
            logger.info(f"analysis_measure_data data={(x, y, lv)}")
        except Exception as e:
            logger.error(f"analysis_measure_data exception: {str(e.args)}", )
            self.measure()

    def close_serial(self):
        self.serial.close()
        self.serial = None
