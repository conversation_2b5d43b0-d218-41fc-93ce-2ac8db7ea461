{
    "sequence":
    [
        //测试流程
        {
            "name":"test_align_L",
            "command":
            [
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"\\\\|/"
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割",
                        "length":"分割长度"
                    }
                },
                {
                    "name":"推送最后一个分割公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@分割长度\",\"-1\"]"
                    },
                    "out":
                    {
                        "result":"推送最后一个分割公式结果",
                        "combine":"最后一个分割公式"
                    }
                },
                {
                    "name":"计算最后一个分割",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@最后一个分割公式" 
                    },
                    "out":
                    {
                        "result":"计算最后一个分割",
                        "value":"最后一个分割"
                    }
                },
                {
                    "name":"计算最后一个分割整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@最后一个分割"                
                    },
                    "out":
                    {
                        "result":"计算最后一个分割整型结果",
                        "integer":"最后一个分割整型"
                    }
                },
                {
                    "name":"获取最后一个分割字符串",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"@最后一个分割整型"
                    },
                    "out":
                    {
                        "result":"获取最后一个分割字符串结果",
                        "string":"最后一个分割字符串"
                    }
                },
                {
                    "name":"判断获取最后一个分割字符串结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"启动左开始流程",
                        "condition":"@获取最后一个分割字符串结果"
                    }
                },
                {
                    "name":"结束测试流程",
                    "type":"end"
                },
                {
                    "name":"启动左开始流程",
                    "type":"call_sequence",
                    "in":
                    {
                        "name":"左开始流程",
                        "para":"@最后一个分割字符串"
                    },
                    "out":
                    {
                        "result":"启动左开始流程结果",
                        "run_result":"左开始流程结果"
                    }
                },
                {
                    "name":"枚举文件",
                    "type":"enum_files",
                    "in":
                    {
                        "wait":"@启动左开始流程结果",
                        "path":"@TRIGGER_STRING"             
                    },
                    "out":
                    {
                        "result":"枚举文件结果",
                        "files":"枚举文件名"
                    }
                },
                {
                    "name":"调用流程",
                    "type":"multi_call_sequence",
                    "in":
                    {
                        "name":"左对位测试流程",
                        "para":"@枚举文件名"
                    },
                    "out":
                    {
                        "result":"调用流程结果",
                        "run_result":"流程结果"
                    }
                },
                {
                    "name":"设置返回结果",
                    "type":"set_result",
                    "in":
                    {
                        "wait":"@调用流程结果",
                        "input":"OK"           
                    }
                }
            ]
        },
        {
            "name":"test_align_R",
            "command":
            [
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"\\\\|/"
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割",
                        "length":"分割长度"
                    }
                },
                {
                    "name":"推送最后一个分割公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@分割长度\",\"-1\"]"
                    },
                    "out":
                    {
                        "result":"推送最后一个分割公式结果",
                        "combine":"最后一个分割公式"
                    }
                },
                {
                    "name":"计算最后一个分割",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@最后一个分割公式" 
                    },
                    "out":
                    {
                        "result":"计算最后一个分割",
                        "value":"最后一个分割"
                    }
                },
                {
                    "name":"计算最后一个分割整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@最后一个分割"                
                    },
                    "out":
                    {
                        "result":"计算最后一个分割整型结果",
                        "integer":"最后一个分割整型"
                    }
                },
                {
                    "name":"获取最后一个分割字符串",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"@最后一个分割整型"
                    },
                    "out":
                    {
                        "result":"获取最后一个分割字符串结果",
                        "string":"最后一个分割字符串"
                    }
                },
                {
                    "name":"判断获取最后一个分割字符串结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"启动右开始流程",
                        "condition":"@获取最后一个分割字符串结果"
                    }
                },
                {
                    "name":"结束测试流程",
                    "type":"end"
                },
                {
                    "name":"启动右开始流程",
                    "type":"call_sequence",
                    "in":
                    {
                        "name":"右开始流程",
                        "para":"@最后一个分割字符串"
                    },
                    "out":
                    {
                        "result":"启动右开始流程结果",
                        "run_result":"右开始流程结果"
                    }
                },
                {
                    "name":"枚举文件",
                    "type":"enum_files",
                    "in":
                    {
                        "wait":"@启动右开始流程结果",
                        "path":"@TRIGGER_STRING"             
                    },
                    "out":
                    {
                        "result":"枚举文件结果",
                        "files":"枚举文件名"
                    }
                },
                {
                    "name":"调用流程",
                    "type":"multi_call_sequence",
                    "in":
                    {
                        "name":"右对位测试流程",
                        "para":"@枚举文件名"
                    },
                    "out":
                    {
                        "result":"调用流程结果",
                        "run_result":"流程结果"
                    }
                },
                {
                    "name":"设置返回结果",
                    "type":"set_result",
                    "in":
                    {
                        "wait":"@调用流程结果",
                        "input":"OK"           
                    }
                }
            ]
        },
        {
            "name":"左对位测试流程",
            "command":
            [
                {
                    "name":"后壳正则匹配",
                    "type":"regex_match",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "pattern":"^(.+|)back+.+\\.(jpg|png|bmp)$"
                    },
                    "out":
                    {
                        "result":"后壳正则匹配结果",
                        "match":"后壳匹配结果"
                    }
                },  
                {
                    "name":"判读后壳匹配结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"调用后壳检测",
                        "condition":"@后壳匹配结果"
                    }
                },
                {
                    "name":"跳转屏幕正则匹配",
                    "type":"jump",
                    "in":
                    {
                        "tag":"屏幕正则匹配"
                    }
                },
                {
                    "name":"调用后壳检测",
                    "type":"call_sequence",
                    "in":
                    {
                        "name":"左对位流程",
                        "para":"@TRIGGER_STRING"
                    }
                },
                {
                    "name":"结束后壳检测",
                    "type":"end"
                },
                {
                    "name":"屏幕正则匹配",
                    "type":"regex_match",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "pattern":"^(.+|)screen+.+\\.(jpg|png|bmp)$"
                    },
                    "out":
                    {
                        "result":"屏幕正则匹配结果",
                        "match":"屏幕匹配结果"
                    }
                },  
                {
                    "name":"判读屏幕匹配结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"调用屏幕检测",
                        "condition":"@屏幕匹配结果"
                    }
                },
                {
                    "name":"跳转角度检测正则匹配",
                    "type":"jump",
                    "in":
                    {
                        "tag":"角度检测正则匹配"
                    }
                },
                {
                    "name":"调用屏幕检测",
                    "type":"call_sequence",
                    "in":
                    {
                        "name":"左拍屏幕流程",
                        "para":"@TRIGGER_STRING"
                    }
                },
                {
                    "name":"结束屏幕检测",
                    "type":"end"
                },
                {
                    "name":"角度检测正则匹配",
                    "type":"regex_match",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "pattern":"^(.+|)cb+.+\\.(jpg|png|bmp)$"
                    },
                    "out":
                    {
                        "result":"角度检测正则匹配结果",
                        "match":"角度检测匹配结果"
                    }
                },  
                {
                    "name":"判读角度检测匹配结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"调用角度检测检测",
                        "condition":"@角度检测匹配结果"
                    }
                },
                {
                    "name":"跳转结束",
                    "type":"jump",
                    "in":
                    {
                        "tag":"无匹配结束"
                    }
                },
                {
                    "name":"调用角度检测检测",
                    "type":"call_sequence",
                    "in":
                    {
                        "name":"左角度检测流程",
                        "para":"@TRIGGER_STRING"
                    }
                },
                {
                    "name":"结束角度检测",
                    "type":"end"
                },
                {
                    "name":"无匹配结束",
                    "type":"end"
                }
            ]
        },
        {
            "name":"右对位测试流程",
            "command":
            [
                {
                    "name":"后壳正则匹配",
                    "type":"regex_match",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "pattern":"^(.+|)back+.+\\.(jpg|png|bmp)$"
                    },
                    "out":
                    {
                        "result":"后壳正则匹配结果",
                        "match":"后壳匹配结果"
                    }
                },  
                {
                    "name":"判读后壳匹配结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"调用后壳检测",
                        "condition":"@后壳匹配结果"
                    }
                },
                {
                    "name":"跳转屏幕正则匹配",
                    "type":"jump",
                    "in":
                    {
                        "tag":"屏幕正则匹配"
                    }
                },
                {
                    "name":"调用后壳检测",
                    "type":"call_sequence",
                    "in":
                    {
                        "name":"右对位流程",
                        "para":"@TRIGGER_STRING"
                    }
                },
                {
                    "name":"结束后壳检测",
                    "type":"end"
                },
                {
                    "name":"屏幕正则匹配",
                    "type":"regex_match",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "pattern":"^(.+|)screen+.+\\.(jpg|png|bmp)$"
                    },
                    "out":
                    {
                        "result":"屏幕正则匹配结果",
                        "match":"屏幕匹配结果"
                    }
                },  
                {
                    "name":"判读屏幕匹配结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"调用屏幕检测",
                        "condition":"@屏幕匹配结果"
                    }
                },
                {
                    "name":"跳转角度检测正则匹配",
                    "type":"jump",
                    "in":
                    {
                        "tag":"角度检测正则匹配"
                    }
                },
                {
                    "name":"调用屏幕检测",
                    "type":"call_sequence",
                    "in":
                    {
                        "name":"右拍屏幕流程",
                        "para":"@TRIGGER_STRING"
                    }
                },
                {
                    "name":"结束屏幕检测",
                    "type":"end"
                },
                {
                    "name":"角度检测正则匹配",
                    "type":"regex_match",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "pattern":"^(.+|)cb+.+\\.(jpg|png|bmp)$"
                    },
                    "out":
                    {
                        "result":"角度检测正则匹配结果",
                        "match":"角度检测匹配结果"
                    }
                },  
                {
                    "name":"判读角度检测匹配结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"调用角度检测检测",
                        "condition":"@角度检测匹配结果"
                    }
                },
                {
                    "name":"跳转结束",
                    "type":"jump",
                    "in":
                    {
                        "tag":"无匹配结束"
                    }
                },
                {
                    "name":"调用角度检测检测",
                    "type":"call_sequence",
                    "in":
                    {
                        "name":"右角度检测流程",
                        "para":"@TRIGGER_STRING"
                    }
                },
                {
                    "name":"结束角度检测",
                    "type":"end"
                },
                {
                    "name":"无匹配结束",
                    "type":"end"
                }
            ]
        }
    ]
}
