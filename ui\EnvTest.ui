<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>EnvForm</class>
 <widget class="QWidget" name="EnvForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1600</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1600</width>
    <height>900</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_4">
   <property name="leftMargin">
    <number>5</number>
   </property>
   <property name="topMargin">
    <number>5</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widget" native="true">
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3" stretch="1,3">
      <property name="leftMargin">
       <number>10</number>
      </property>
      <property name="topMargin">
       <number>10</number>
      </property>
      <property name="rightMargin">
       <number>10</number>
      </property>
      <property name="bottomMargin">
       <number>10</number>
      </property>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_2" stretch="2">
          <item>
           <layout class="QGridLayout" name="gridLayout" rowstretch="1,1,1" columnstretch="1,1,1,1,1,1" rowminimumheight="1,1,1" columnminimumwidth="1,1,1,1,1,1">
            <item row="0" column="4">
             <widget class="QLabel" name="label_7">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>测试时间(min):</string>
              </property>
             </widget>
            </item>
            <item row="2" column="2">
             <widget class="QPushButton" name="pushButtonStop">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>停止</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QComboBox" name="comboBoxScenarios">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <item>
               <property name="text">
                <string>温升</string>
               </property>
              </item>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QSpinBox" name="spinBoxFrequency">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="minimum">
               <number>1</number>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="label_4">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>采集频率(s/次):</string>
              </property>
             </widget>
            </item>
            <item row="2" column="5">
             <widget class="QLabel" name="label_3">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>默认最后一个通道为环境通道</string>
              </property>
             </widget>
            </item>
            <item row="2" column="4">
             <widget class="QPushButton" name="pushButtonDelChl">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>删除通道</string>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="label">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>测试场景:</string>
              </property>
             </widget>
            </item>
            <item row="0" column="3">
             <widget class="QComboBox" name="comboBoxDevice">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <item>
               <property name="text">
                <string>TP700</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>TP1000</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>TP710A</string>
               </property>
              </item>
             </widget>
            </item>
            <item row="1" column="2">
             <widget class="QLabel" name="label_5">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>温差时间(s)</string>
              </property>
             </widget>
            </item>
            <item row="1" column="4">
             <widget class="QPushButton" name="pushButtonAdd">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>添加通道</string>
              </property>
             </widget>
            </item>
            <item row="0" column="5">
             <widget class="QSpinBox" name="spinBoxTestTime">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="maximum">
               <number>99999</number>
              </property>
              <property name="value">
               <number>1</number>
              </property>
             </widget>
            </item>
            <item row="1" column="5">
             <widget class="QSpinBox" name="spinBoxChl">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="minimum">
               <number>1</number>
              </property>
             </widget>
            </item>
            <item row="1" column="3">
             <widget class="QSpinBox" name="spinBoxTemperatureTime">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
             </widget>
            </item>
            <item row="0" column="2">
             <widget class="QLabel" name="label_2">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>测试设备:</string>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QPushButton" name="pushButtonPause">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>暂停</string>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QPushButton" name="pushButtonStart">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>45</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="text">
               <string>启动</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout" stretch="0">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QPlainTextEdit" name="plainTextEdit">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="plainText">
             <string>测试方法:</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QTableWidget" name="tableWidget">
        <property name="styleSheet">
         <string notr="true"/>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
