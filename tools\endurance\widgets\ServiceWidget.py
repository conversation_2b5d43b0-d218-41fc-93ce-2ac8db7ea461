from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QWidget

from common.LogUtils import logger
from .ui.serviceWidget import Ui_Form
from ..constants import CONF, update_conf
from ..service import Service1


class ServiceWidget(QWidget, Ui_Form):
    def __init__(self, endurance_w, parent=None):
        super().__init__(parent=parent)
        self.setupUi(self)

        self.setAttribute(Qt.WA_DeleteOnClose)

        self.endurance_w = endurance_w
        self.comboBox_delay.addItems([f"Y{i}" for i in range(1, 17)])

        self.progressBar_on.setEnabled(False)
        self.progressBar_off.setEnabled(False)

        self.progressBar_on.setValue(0)
        self.progressBar_off.setValue(0)

        self.service1 = Service1()

        self.service1.power_on_time_changed_signal.connect(self.progressBar_on.setValue)
        self.service1.power_off_time_changed_signal.connect(self.progressBar_off.setValue)
        self.service1.power_remain_count_changed_signal.connect(self.update_remain_count)

        self.pushButton_update.clicked.connect(self.update_conf)
        self.pushButton_save.clicked.connect(self.save_conf)

        self.pushButton_start.clicked.connect(self.on_start)
        self.pushButton_stop.clicked.connect(self.on_stop)

        self.update_conf()

        self.comboBox_delay.currentIndexChanged.connect(self.update_conf)
        self.pushButton_close.clicked.connect(self.on_close)

    def update_conf(self):
        delay = self.comboBox_delay.currentText()
        conf = CONF.get(delay, {})
        on_time = conf.get("on_time")
        off_time = conf.get("off_time")
        count = conf.get("count")

        self.spinBox_on_time.setValue(on_time)
        self.spinBox_off_time.setValue(off_time)
        self.spinBox_count.setValue(count)

    def save_conf(self):
        delay = self.comboBox_delay.currentText()
        on_time = self.spinBox_on_time.value()
        off_time = self.spinBox_off_time.value()
        count = self.spinBox_count.value()

        update_conf({
            delay: {
                "on_time": on_time,
                "off_time": off_time,
                "count": count,
            }
        })

    def on_close(self):
        self.endurance_w.services.remove(self)
        self.close()

    def update_remain_count(self, value):
        logger.info("update_remain_count value={}".format(value))
        self.lineEdit_remain_count.setText(str(value))

    def on_start(self):
        logger.info("on_start")
        delay = self.comboBox_delay.currentText()
        on_time = self.spinBox_on_time.value()
        off_time = self.spinBox_off_time.value()
        count = self.spinBox_count.value()

        if count == 0:
            self.update_remain_count("无限")
        else:
            self.update_remain_count(count)
        self.service1.start(on_time=on_time, off_time=off_time, count=count, delay=delay)

    def on_stop(self):
        logger.info("on_stop")
        self.service1.stop()
