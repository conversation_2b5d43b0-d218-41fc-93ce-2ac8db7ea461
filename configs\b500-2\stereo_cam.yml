%YAML:1.0
---
size: !!opencv-matrix
   rows: 2
   cols: 1
   dt: d
   data: [ 1.920000000000000e+03, 1.080000000000000e+03 ]
K1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 1.080542745180650e+03, 0.000000000000000e+00, 9.348068259673191e+02, 0.000000000000000e+00, 1.080435282589111e+03, 4.926309173158955e+02, 0.000000000000000e+00, 0.000000000000000e+00, 1.000000000000000e+00 ]
K2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 1.082947557803269e+03, 0.000000000000000e+00, 9.570821400999338e+02, 0.000000000000000e+00, 1.083357169705195e+03, 4.960110787188617e+02, 0.000000000000000e+00, 0.000000000000000e+00, 1.000000000000000e+00 ]
D1: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ 9.687718494694225e-02, -2.402933962509623e-01, 7.701544178070280e-04, 4.325846369988996e-04, 2.823892213638144e-01 ]
D2: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ 8.679696824473479e-02, -1.718514679575732e-01, 4.654318083530730e-04, -5.240722016164790e-04, 1.960923165505679e-01 ]
R: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.999962117643940e-01, -2.744421873690212e-03, -2.112000010231077e-04, 2.744380908752780e-03, 9.999962153598532e-01, -1.940087571252739e-04, 2.117316435838501e-04, 1.934284089236522e-04, 9.999999588775800e-01 ]
T: !!opencv-matrix
   rows: 3
   cols: 1
   dt: d
   data: [ -5.986940586177013e+01, -2.194124325691163e-01, 1.646232713257523e-01 ]
E: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 4.981356127279134e-04, -1.645800803525700e-01, -2.194442663637905e-01, 1.519782291147556e-01, -1.116340005810778e-02, 5.986943825575108e+01, 3.837185083950058e-01, -5.986857712632205e+01, -1.153398736407599e-02 ]
F: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 4.256946708358335e-10, -1.406601530927796e-07, -1.337404990314333e-04, 1.298278203976073e-07, -9.537313725274082e-09, 5.514621535598244e-02, 2.903129922966248e-04, -5.527218074234677e-02, -2.711972385134693e-01 ]
Q: !!opencv-matrix
   rows: 4
   cols: 4
   dt: d
   data: [ 1.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 1.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00, -1.670284663367100e-02, -9.348068259673191e+02, -4.926309173158955e+02, 1.080542745180650e+03, -3.720611556739067e-01 ]
R1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -1.812424476824516e-02, -9.996980897572598e-01, 1.659039141389337e-02, 9.993546662131800e-01, -1.862770542639498e-02, -3.071253341049538e-02, 3.101230190624486e-02, 1.602304360079269e-02, 9.993905638959389e-01 ]
R2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -2.086421575054000e-02, -9.996413566329342e-01, 1.678816875606423e-02, 9.992932560879270e-01, -2.137622640923415e-02, -3.091998191607048e-02, 3.126776036597255e-02, 1.613118264630133e-02, 9.993808643896120e-01 ]
P1: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ -4.075210057028523e+00, -1.071936148369823e+03, 1.659039141389337e-02, 1.051135148564201e+03, -3.525597368346120e+01, -3.071253341049538e-02, 9.677472387733968e+02, 5.096425518896666e+02, 9.993905638959389e-01, 0.000000000000000e+00, 0.000000000000000e+00, 0.000000000000000e+00 ]
P2: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ -6.527195011114994e+00, -1.074641513147707e+03, 1.678816875606423e-02, 1.052589228745613e+03, -3.849474172584317e+01, -3.091998191607048e-02, 9.903509211912394e+02, 5.131798129725745e+02, 9.993808643896120e-01, -5.972391590549717e+01, -1.440947573400812e-01, 1.520140750487368e-04 ]
