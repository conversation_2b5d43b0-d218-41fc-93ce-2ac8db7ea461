from PyQt5.QtWidgets import QWidget

from .ui.homeWidget import Ui_Form
from ..motion_control_card import mcc


class HomeWidget(QWidget, Ui_Form):
    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setupUi(self)

        self.setWindowTitle("回零")
        self.resize(900, 700)

        for i in mcc.axis_d:
            getattr(self, f"pushButton_home_{i}").clicked.connect(self.on_home)

    def on_home(self):
        sender = self.sender().objectName()
        index = int(sender.split("_")[-1])

        mcc.axis_d[index].home()
