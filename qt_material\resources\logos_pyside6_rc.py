# Resource object code (Python 3)
# Created by: object code
# Created by: The Resource Compiler for Qt version 6.0.0
# WARNING! All changes made in this file will be lost!

from PySide6 import QtCore

qt_resource_data = b"\
\x00\x00\x152\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22 standalone=\x22\
no\x22?>\x0a<!-- Creat\
ed with Inkscape\
 (http://www.ink\
scape.org/) -->\x0a\
\x0a<svg\x0a   xmlns:d\
c=\x22http://purl.o\
rg/dc/elements/1\
.1/\x22\x0a   xmlns:cc\
=\x22http://creativ\
ecommons.org/ns#\
\x22\x0a   xmlns:rdf=\x22\
http://www.w3.or\
g/1999/02/22-rdf\
-syntax-ns#\x22\x0a   \
xmlns:svg=\x22http:\
//www.w3.org/200\
0/svg\x22\x0a   xmlns=\
\x22http://www.w3.o\
rg/2000/svg\x22\x0a   \
xmlns:sodipodi=\x22\
http://sodipodi.\
sourceforge.net/\
DTD/sodipodi-0.d\
td\x22\x0a   xmlns:ink\
scape=\x22http://ww\
w.inkscape.org/n\
amespaces/inksca\
pe\x22\x0a   width=\x2251\
2\x22\x0a   height=\x2251\
2\x22\x0a   viewBox=\x220\
 0 135.46667 135\
.46667\x22\x0a   versi\
on=\x221.1\x22\x0a   id=\x22\
svg8\x22\x0a   inkscap\
e:version=\x220.92.\
4 5da689c313, 20\
19-01-14\x22\x0a   sod\
ipodi:docname=\x22l\
ogo_frame.svg\x22\x0a \
  inkscape:expor\
t-filename=\x22/hom\
e/yeison/Develop\
ment/gcpds/bci-u\
nscented/assets/\
logo.png\x22\x0a   ink\
scape:export-xdp\
i=\x2296\x22\x0a   inksca\
pe:export-ydpi=\x22\
96\x22>\x0a  <defs\x0a   \
  id=\x22defs2\x22 />\x0a\
  <sodipodi:name\
dview\x0a     id=\x22b\
ase\x22\x0a     pageco\
lor=\x22#ffffff\x22\x0a  \
   bordercolor=\x22\
#666666\x22\x0a     bo\
rderopacity=\x221.0\
\x22\x0a     inkscape:\
pageopacity=\x220.0\
\x22\x0a     inkscape:\
pageshadow=\x222\x22\x0a \
    inkscape:zoo\
m=\x220.96260433\x22\x0a \
    inkscape:cx=\
\x22263.56863\x22\x0a    \
 inkscape:cy=\x2215\
4.89649\x22\x0a     in\
kscape:document-\
units=\x22px\x22\x0a     \
inkscape:current\
-layer=\x22layer1\x22\x0a\
     showgrid=\x22f\
alse\x22\x0a     inksc\
ape:window-width\
=\x221920\x22\x0a     ink\
scape:window-hei\
ght=\x221004\x22\x0a     \
inkscape:window-\
x=\x220\x22\x0a     inksc\
ape:window-y=\x220\x22\
\x0a     inkscape:w\
indow-maximized=\
\x221\x22\x0a     units=\x22\
px\x22\x0a     inkscap\
e:showpageshadow\
=\x22false\x22\x0a     sh\
owguides=\x22false\x22\
\x0a     inkscape:s\
nap-bbox=\x22true\x22\x0a\
     inkscape:bb\
ox-paths=\x22true\x22\x0a\
     inkscape:bb\
ox-nodes=\x22true\x22\x0a\
     inkscape:sn\
ap-bbox-midpoint\
s=\x22true\x22\x0a     in\
kscape:snap-bbox\
-edge-midpoints=\
\x22true\x22\x0a     inks\
cape:object-path\
s=\x22true\x22\x0a     in\
kscape:snap-inte\
rsection-paths=\x22\
true\x22\x0a     inksc\
ape:snap-midpoin\
ts=\x22true\x22\x0a     i\
nkscape:snap-smo\
oth-nodes=\x22true\x22\
\x0a     inkscape:s\
nap-object-midpo\
ints=\x22true\x22\x0a    \
 inkscape:snap-c\
enter=\x22true\x22\x0a   \
  fit-margin-top\
=\x220\x22\x0a     fit-ma\
rgin-left=\x220\x22\x0a  \
   fit-margin-ri\
ght=\x220\x22\x0a     fit\
-margin-bottom=\x22\
0\x22 />\x0a  <metadat\
a\x0a     id=\x22metad\
ata5\x22>\x0a    <rdf:\
RDF>\x0a      <cc:W\
ork\x0a         rdf\
:about=\x22\x22>\x0a     \
   <dc:format>im\
age/svg+xml</dc:\
format>\x0a        \
<dc:type\x0a       \
    rdf:resource\
=\x22http://purl.or\
g/dc/dcmitype/St\
illImage\x22 />\x0a   \
     <dc:title /\
>\x0a      </cc:Wor\
k>\x0a    </rdf:RDF\
>\x0a  </metadata>\x0a\
  <g\x0a     inksca\
pe:label=\x22Capa 1\
\x22\x0a     inkscape:\
groupmode=\x22layer\
\x22\x0a     id=\x22layer\
1\x22\x0a     transfor\
m=\x22translate(21.\
700237,-922.5195\
4)\x22>\x0a    <path\x0a \
      sodipodi:t\
ype=\x22star\x22\x0a     \
  style=\x22opacity\
:0.33;fill:#0e97\
98;fill-opacity:\
0.99607843;strok\
e:none;stroke-wi\
dth:1.16275263;s\
troke-linecap:ro\
und;stroke-linej\
oin:round;stroke\
-miterlimit:4;st\
roke-dasharray:n\
one;stroke-dasho\
ffset:0;stroke-o\
pacity:0.1415525\
\x22\x0a       id=\x22pat\
h1578\x22\x0a       so\
dipodi:sides=\x224\x22\
\x0a       sodipodi\
:cx=\x2246.033096\x22\x0a\
       sodipodi:\
cy=\x22990.25287\x22\x0a \
      sodipodi:r\
1=\x2275.712967\x22\x0a  \
     sodipodi:r2\
=\x2253.537148\x22\x0a   \
    sodipodi:arg\
1=\x220.78539816\x22\x0a \
      sodipodi:a\
rg2=\x221.5707963\x22\x0a\
       inkscape:\
flatsided=\x22true\x22\
\x0a       inkscape\
:rounded=\x220.25\x22\x0a\
       inkscape:\
randomized=\x220\x22\x0a \
      d=\x22m 99.57\
0249,1043.79 c -\
18.928242,18.928\
3 -88.146063,18.\
9283 -107.074304\
8,0 -18.9282422,\
-18.9282 -18.928\
2422,-88.14604 -\
4e-7,-107.07428 \
18.9282412,-18.9\
2825 88.1460632,\
-18.92825 107.07\
43042,0 18.92824\
2,18.92824 18.92\
8242,88.14608 10\
e-7,107.07428 z\x22\
\x0a       inkscape\
:transform-cente\
r-y=\x22-2.0420979e\
-05\x22 />\x0a    <cir\
cle\x0a       cy=\x221\
39.7252\x22\x0a       \
cx=\x22-591.70111\x22\x0a\
       id=\x22circl\
e1545\x22\x0a       st\
yle=\x22opacity:0.6\
7299996;fill:#aa\
aaff;fill-opacit\
y:0.30136988;str\
oke:#000000;stro\
ke-width:0.30404\
934;stroke-miter\
limit:4;stroke-d\
asharray:none;st\
roke-opacity:1\x22\x0a\
       r=\x220\x22 />\x0a\
    <circle\x0a    \
   r=\x220\x22\x0a       \
style=\x22opacity:0\
.67299996;fill:#\
aaaaff;fill-opac\
ity:0.30136988;s\
troke:#000000;st\
roke-width:0.304\
04934;stroke-mit\
erlimit:4;stroke\
-dasharray:none;\
stroke-opacity:1\
\x22\x0a       id=\x22ell\
ipse1617\x22\x0a      \
 cx=\x22-993.51282\x22\
\x0a       cy=\x22168.\
13614\x22 />\x0a    <c\
ircle\x0a       r=\x22\
0\x22\x0a       cy=\x2273\
3.2077\x22\x0a       c\
x=\x22-958.14404\x22\x0a \
      id=\x22ellips\
e1906\x22\x0a       st\
yle=\x22opacity:0.6\
7299996;fill:non\
e;fill-opacity:0\
.30136988;stroke\
:#000000;stroke-\
width:0.79374999\
;stroke-miterlim\
it:4;stroke-dash\
array:none;strok\
e-opacity:1\x22 />\x0a\
    <circle\x0a    \
   r=\x220\x22\x0a       \
cy=\x22582.33099\x22\x0a \
      cx=\x22-1500.\
9722\x22\x0a       id=\
\x22ellipse1926\x22\x0a  \
     style=\x22opac\
ity:0.67299996;f\
ill:none;fill-op\
acity:0.30136988\
;stroke:#000000;\
stroke-width:0.7\
9374999;stroke-m\
iterlimit:4;stro\
ke-dasharray:non\
e;stroke-opacity\
:1\x22 />\x0a    <circ\
le\x0a       r=\x220\x22\x0a\
       cy=\x22255.9\
781\x22\x0a       cx=\x22\
-958.14404\x22\x0a    \
   id=\x22ellipse19\
46\x22\x0a       style\
=\x22opacity:0.6729\
9996;fill:none;f\
ill-opacity:0.30\
136988;stroke:#0\
00000;stroke-wid\
th:0.79374999;st\
roke-miterlimit:\
4;stroke-dasharr\
ay:none;stroke-o\
pacity:1\x22 />\x0a   \
 <g\x0a       trans\
form=\x22matrix(0.5\
8796298,0,0,0.58\
796298,-29.22616\
3,980.84547)\x22\x0a  \
     id=\x22g4554\x22>\
\x0a      <g\x0a      \
   id=\x22g10\x22>\x0a   \
     <polygon\x0a  \
         class=\x22\
st0\x22\x0a           \
points=\x2256,-56 2\
00,-56 176,-8 80\
,-8 \x22\x0a          \
 id=\x22polygon4\x22\x0a \
          style=\
\x22fill:#41cd5f;fi\
ll-opacity:0.941\
17647\x22 />\x0a      \
  <rect\x0a        \
   x=\x2256\x22\x0a      \
     y=\x22-8\x22\x0a    \
       class=\x22st\
1\x22\x0a           wi\
dth=\x22144\x22\x0a      \
     height=\x2296\x22\
\x0a           id=\x22\
rect6\x22\x0a         \
  style=\x22fill:#f\
fd80f;fill-opaci\
ty:0.94117647\x22 /\
>\x0a        <polyg\
on\x0a           cl\
ass=\x22st2\x22\x0a      \
     points=\x22128\
,88 176,-8 80,-8\
 \x22\x0a           id\
=\x22polygon8\x22\x0a    \
       style=\x22fi\
ll:#00939f;fill-\
opacity:0.941176\
47\x22 />\x0a      </g\
>\x0a    </g>\x0a  </g\
>\x0a  <style\x0a     \
id=\x22style2\x22\x0a    \
 type=\x22text/css\x22\
>\x0a\x09.st0{fill:#21\
2121;}\x0a\x09.st1{fil\
l:#FF80AB;}\x0a\x09.st\
2{fill:#FF1744;}\
\x0a</style>\x0a</svg>\
\x0a\
\x00\x00\x09{\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22 standalone=\x22\
no\x22?>\x0a<!-- Gener\
ator: Adobe Illu\
strator 19.0.0, \
SVG Export Plug-\
In . SVG Version\
: 6.00 Build 0) \
 -->\x0a\x0a<svg\x0a   xm\
lns:dc=\x22http://p\
url.org/dc/eleme\
nts/1.1/\x22\x0a   xml\
ns:cc=\x22http://cr\
eativecommons.or\
g/ns#\x22\x0a   xmlns:\
rdf=\x22http://www.\
w3.org/1999/02/2\
2-rdf-syntax-ns#\
\x22\x0a   xmlns:svg=\x22\
http://www.w3.or\
g/2000/svg\x22\x0a   x\
mlns=\x22http://www\
.w3.org/2000/svg\
\x22\x0a   xmlns:sodip\
odi=\x22http://sodi\
podi.sourceforge\
.net/DTD/sodipod\
i-0.dtd\x22\x0a   xmln\
s:inkscape=\x22http\
://www.inkscape.\
org/namespaces/i\
nkscape\x22\x0a   vers\
ion=\x221.1\x22\x0a   id=\
\x22content\x22\x0a   x=\x22\
0px\x22\x0a   y=\x220px\x22\x0a\
   viewBox=\x2256 -\
56 512 512\x22\x0a   x\
ml:space=\x22preser\
ve\x22\x0a   sodipodi:\
docname=\x22logo.sv\
g\x22\x0a   width=\x22512\
\x22\x0a   height=\x22512\
\x22\x0a   inkscape:ex\
port-filename=\x22/\
home/yeison/Deve\
lopment/gcpds/py\
side-material/do\
cs/source/_stati\
c/logo.png\x22\x0a   i\
nkscape:export-x\
dpi=\x2296\x22\x0a   inks\
cape:export-ydpi\
=\x2296\x22\x0a   inkscap\
e:version=\x220.92.\
4 5da689c313, 20\
19-01-14\x22><metad\
ata\x0a   id=\x22metad\
ata17\x22><rdf:RDF>\
<cc:Work\x0a       \
rdf:about=\x22\x22><dc\
:format>image/sv\
g+xml</dc:format\
><dc:type\x0a      \
   rdf:resource=\
\x22http://purl.org\
/dc/dcmitype/Sti\
llImage\x22 /><dc:t\
itle></dc:title>\
</cc:Work></rdf:\
RDF></metadata><\
defs\x0a   id=\x22defs\
15\x22 /><sodipodi:\
namedview\x0a   pag\
ecolor=\x22#ffffff\x22\
\x0a   bordercolor=\
\x22#666666\x22\x0a   bor\
deropacity=\x221\x22\x0a \
  objecttoleranc\
e=\x2210\x22\x0a   gridto\
lerance=\x2210\x22\x0a   \
guidetolerance=\x22\
10\x22\x0a   inkscape:\
pageopacity=\x220\x22\x0a\
   inkscape:page\
shadow=\x222\x22\x0a   in\
kscape:window-wi\
dth=\x221920\x22\x0a   in\
kscape:window-he\
ight=\x221015\x22\x0a   i\
d=\x22namedview13\x22\x0a\
   showgrid=\x22tru\
e\x22\x0a   inkscape:z\
oom=\x220.61631945\x22\
\x0a   inkscape:cx=\
\x22156.74186\x22\x0a   i\
nkscape:cy=\x2210.3\
16892\x22\x0a   inksca\
pe:window-x=\x220\x22\x0a\
   inkscape:wind\
ow-y=\x220\x22\x0a   inks\
cape:window-maxi\
mized=\x221\x22\x0a   ink\
scape:current-la\
yer=\x22content\x22\x0a  \
 showguides=\x22fal\
se\x22\x0a   fit-margi\
n-top=\x220\x22\x0a   fit\
-margin-left=\x220\x22\
\x0a   fit-margin-r\
ight=\x220\x22\x0a   fit-\
margin-bottom=\x220\
\x22 />\x0a<style\x0a   t\
ype=\x22text/css\x22\x0a \
  id=\x22style2\x22>\x0a\x09\
.st0{fill:#21212\
1;}\x0a\x09.st1{fill:#\
FF80AB;}\x0a\x09.st2{f\
ill:#FF1744;}\x0a</\
style>\x0a<g\x0a   id=\
\x22g4554\x22\x0a   trans\
form=\x22matrix(3.5\
555556,0,0,3.555\
5556,-143.11111,\
143.11111)\x22><g\x0a \
    id=\x22g10\x22>\x0a\x09<\
polygon\x0a   style\
=\x22fill:#41cd5f;f\
ill-opacity:0.94\
117647\x22\x0a   id=\x22p\
olygon4\x22\x0a   poin\
ts=\x22176,-8 80,-8\
 56,-56 200,-56 \
\x22\x0a   class=\x22st0\x22\
 />\x0a\x09<rect\x0a   st\
yle=\x22fill:#ffd80\
f;fill-opacity:0\
.94117647\x22\x0a   id\
=\x22rect6\x22\x0a   heig\
ht=\x2296\x22\x0a   width\
=\x22144\x22\x0a   class=\
\x22st1\x22\x0a   y=\x22-8\x22\x0a\
   x=\x2256\x22 />\x0a\x09<p\
olygon\x0a   style=\
\x22fill:#00939f;fi\
ll-opacity:0.941\
17647\x22\x0a   id=\x22po\
lygon8\x22\x0a   point\
s=\x22176,-8 80,-8 \
128,88 \x22\x0a   clas\
s=\x22st2\x22 />\x0a</g><\
/g>\x0a</svg>\
"

qt_resource_name = b"\
\x00\x04\
\x00\x06\xfa^\
\x00i\
\x00c\x00o\x00n\
\x00\x04\
\x00\x075\xdf\
\x00l\
\x00o\x00g\x00o\
\x00\x0e\
\x0f\xfe\xfd\x07\
\x00l\
\x00o\x00g\x00o\x00_\x00f\x00r\x00a\x00m\x00e\x00.\x00s\x00v\x00g\
\x00\x08\
\x05\xe2T\xa7\
\x00l\
\x00o\x00g\x00o\x00.\x00s\x00v\x00g\
"

qt_resource_struct = b"\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x01\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x02\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00\x0e\x00\x02\x00\x00\x00\x02\x00\x00\x00\x03\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00>\x00\x00\x00\x00\x00\x01\x00\x00\x156\
\x00\x00\x01lo\x01\x16p\
\x00\x00\x00\x1c\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\
\x00\x00\x01o!\x98\xaf\x1b\
"

def qInitResources():
    QtCore.qRegisterResourceData(0x03, qt_resource_struct, qt_resource_name, qt_resource_data)

def qCleanupResources():
    QtCore.qUnregisterResourceData(0x03, qt_resource_struct, qt_resource_name, qt_resource_data)

qInitResources()
