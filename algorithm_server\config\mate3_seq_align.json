{
    "sequence":
    [
        //左拍屏幕
        {
            "name":"左拍屏幕流程",
            "command":
            [
                {
                    "name":"读取模板图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"temp/screen_temp.jpg"
                    },
                    "out":
                    {
                        "image":"屏幕模板图片",
                        "result":"屏幕模板结果"
                    }
                },
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"."
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取字符串长度",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取字符串长度结果",
                        "string":"获取字符串长度"
                    }
                },
                {
                    "name":"判断获取字符串长度结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"读取屏幕图片",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"屏幕拍照",
                    "type":"get_camera_frame",
                    "in":
                    {
                        "camera":"左拍屏幕相机",
                        "exposure":"20000",
                        "gain":"4.0"
                    },
                    "out":
                    {
                        "result":"屏幕拍照结果",
                        "image":"屏幕拍照图片"
                    }
                },
                {
                    "name":"跳转获取时间",
                    "type":"jump",
                    "in":
                    {
                        "tag":"获取当前时间"
                    }
                },
                {
                    "name":"读取屏幕图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "image":"屏幕拍照图片",
                        "result":"屏幕拍照结果"
                    }
                },
                {
                    "name":"获取当前时间",
                    "type":"get_date_time",
                    "out":
                    {
                        "result":"获取当前时间结果",
                        "date_time":"当前时间"  
                    }
                    
                },
                {
                    "name":"生成图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"screen_\",\"@当前时间\",\"_\",\"$左SN码\",\".png\"]"
                    },
                    "out":
                    {
                        "result":"生成图片名称结果",
                        "combine":"图片名称"
                    }
                },
                {
                    "name":"生成xml名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"screen_\",\"@当前时间\",\"_\",\"$左SN码\",\".xml\"]"
                    },
                    "out":
                    {
                        "result":"生成xml名称结果",
                        "combine":"xml名称"
                    }
                },
                {
                    "name":"获取屏幕图片信息",
                    "type":"get_image_info",
                    "in":
                    {
                        "image":"@屏幕拍照图片"
                    },
                    "out":
                    {
                        "result":"获取屏幕图片信息结果",
                        "width":"获取屏幕图片信息宽",
                        "height":"获取屏幕图片信息高",
                        "channel":"获取屏幕图片信息通道"
                    }
                },
                {
                    "name":"离线模式不保存图片",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"判断拍屏幕结果",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"保存屏幕图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@屏幕拍照图片",
                        "path":"$左图片文件夹",
                        "name":"@图片名称"
                    },
                    "out":
                    {
                        "result":"保存图片结果",
                        "full_name":"图片路径"
                    }
                },
                {
                    "name":"判断拍屏幕结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"跳转算法流程",
                        "condition":"@屏幕拍照结果"
                    }
                },
                {
                    "name":"跳转拍屏幕失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示拍屏幕失败"
                    }
                },
                {
                    "name":"跳转算法流程",
                    "type":"jump",
                    "in":
                    {
                        "tag":"屏幕向下采样"
                    }
                },
                {
                    "name":"显示拍屏幕失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"左拍屏幕失败",
                        "level":"error"
                    }
                },
                {
                    "name":"拍屏幕失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送拍屏幕NG结果"
                    }
                },
                {
                    "name":"屏幕向下采样",
                    "type":"pyramid",
                    "in":
                    {
                        "image":"@屏幕拍照图片",
                        "type":"down",
                        "count":"2"
                    },
                    "out":
                    {
                        "result":"屏幕向下采样结果",
                        "image_result":"屏幕向下采样图片"
                    }
                },
                {
                    "name":"屏幕模板向下采样",
                    "type":"pyramid",
                    "in":
                    {
                        "image":"@屏幕模板图片",
                        "type":"down",
                        "count":"2"
                    },
                    "out":
                    {
                        "result":"屏幕模板向下采样结果",
                        "image_result":"屏幕模板向下采样图片"
                    }
                },
                {
                    "name":"屏幕模板匹配",
                    "type":"calc_sharp_match_image",
                    "in":
                    {
                        "image":"@屏幕向下采样图片",
                        "template":"@屏幕模板向下采样图片",
                        "num_features":"120",
                        "pyramid_levels_h":"8",
                        "pyramid_levels_l":"4",
                        "angle_begin":"-5",
                        "angle_end":"5",
                        "angle_step":"0.1",
                        "scale_begin":"0.9",
                        "scale_end":"1.1",
                        "scale_step":"0.1",
                        "threshold":"70"
                    },
                    "out":
                    {
                        "result":"屏幕模板匹配结果",
                        "debug_img":"屏幕模板匹配图片",
                        "score":"屏幕模板匹配分数",
                        "scale":"屏幕模板匹配缩放",
                        "angle":"屏幕模板匹配角度",
                        "x":"屏幕模板匹配X",
                        "y":"屏幕模板匹配Y",
                        "xmin":"屏幕模板矩形xmin",
                        "ymin":"屏幕模板矩形ymin",
                        "xmax":"屏幕模板矩形xmax",
                        "ymax":"屏幕模板矩形ymax"
                    }
                },
                {
                    "name":"屏幕找圆",
                    "type":"find_circle",
                    "in":
                    {
                        "image":"@屏幕拍照图片",
                        "center_x":"2650",
                        "center_y":"1800",
                        "radius_min":"1200",
                        "radius_max":"1700",
                        "count":"100",
                        "width":"100",
                        "ksize":"17",
                        "type":"-1",
                        "find_type":"0",
                        "iterations":"50000",
                        "strength":"100",
                        "thresh":"0.3",
                        "sigma":"10",
                        "end":"0.9",
                        "minimum":"0.3",
                        "use_fit":"1"
                    },
                    "out":
                    {
                        "result":"屏幕找圆结果",
                        "x":"circle_X",
                        "y":"circle_Y",
                        "radius":"circle_R",
                        "debug_img":"屏幕找圆图片"
                    }
                },    
                {
                    "name":"判断找圆结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"判断模板匹配结果",
                        "condition":"@屏幕找圆结果"
                    }
                },
                {
                    "name":"显示找圆失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"左屏幕找圆失败",
                        "level":"error"
                    }
                },
                {
                    "name":"找圆失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送拍屏幕NG结果"
                    }
                },
                {
                    "name":"判断模板匹配结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"推送计算圆直径公式",
                        "condition":"@屏幕模板匹配结果"
                    }
                },
                {
                    "name":"显示匹配失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"左屏幕匹配失败",
                        "level":"error"
                    }
                },
                {
                    "name":"匹配失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送拍屏幕NG结果"
                    }
                },
                {
                    "name":"推送计算圆直径公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@circle_R\",\"*2\"]"
                    },
                    "out":
                    {
                        "result":"推送计算圆直径结果",
                        "combine":"推送计算圆直径"
                    }
                },
                {
                    "name":"计算圆直径",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算圆直径" 
                    },
                    "out":
                    {
                        "result":"计算圆直径结果",
                        "value":"圆直径"
                    }
                },
                {
                    "name":"计算椭圆矩形",
                    "type":"bounding_box",
                    "in":
                    {
                        "x":"@circle_X",
                        "y":"@circle_Y",
                        "w":"@圆直径",
                        "h":"@圆直径",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"计算椭圆矩形结果",
                        "xmin":"椭圆矩形xmin",
                        "ymin":"椭圆矩形ymin",
                        "xmax":"椭圆矩形xmax",
                        "ymax":"椭圆矩形ymax"
                    }
                },
                {
                    "name":"模板矩形还原",
                    "type":"bounding_box_scale",
                    "in":
                    {
                        "xmin_in":"@屏幕模板矩形xmin",
                        "ymin_in":"@屏幕模板矩形ymin",
                        "xmax_in":"@屏幕模板矩形xmax",
                        "ymax_in":"@屏幕模板矩形ymax",
                        "scale":"4"
                    },
                    "out":
                    {
                        "result":"模板矩形还原",
                        "xmin_out":"模板矩形还原xmin",
                        "ymin_out":"模板矩形还原ymin",
                        "xmax_out":"模板矩形还原xmax",
                        "ymax_out":"模板矩形还原ymax"
                    }
                },
                {
                    "name":"离线模式XML名字有变化",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"生成xml名称2",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"保存XML",
                    "type":"save_xml",
                    "in":
                    {
                        "path":"$左图片文件夹",
                        "name":"@xml名称",
                        "product":"mate3.0",
                        "sn":"$左SN码",
                        "station":"Align",
                        "mac":"NULL",
                        "detection_type":"screen",
                        "result":"1",
                        "folder":"$左图片文件夹",
                        "image_name":"@图片名称",
                        "width":"@获取屏幕图片信息宽",
                        "height":"@获取屏幕图片信息高",
                        "depth":"@获取屏幕图片信息通道",
                        "name_b":"[\"Ellipse\",\"Match\"]",
                        "label":"[\"1\",\"1\"]",
                        "standard":"[\"1\",\"1\"]",
                        "xmin":"[\"@椭圆矩形xmin\",\"@模板矩形还原xmin\"]",
                        "ymin":"[\"@椭圆矩形ymin\",\"@模板矩形还原ymin\"]",
                        "xmax":"[\"@椭圆矩形xmax\",\"@模板矩形还原xmax\"]",
                        "ymax":"[\"@椭圆矩形ymax\",\"@模板矩形还原ymax\"]"
                    },
                    "out":
                    {
                        "result":"保存XML结果",
                        "full_name":"保存XML全称"
                    }
                },
                {
                    "name":"保存筛选圆图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@屏幕找圆图片",
                        "path":"$左调试文件夹",
                        "name":"screen_ellipse.jpg"
                    },
                    "out":
                    {
                        "result":"保存筛选圆图片结果",
                        "full_name":"保存筛选圆图片路径"
                    }
                },
                {
                    "name":"保存模板匹配图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@屏幕模板匹配图片",
                        "path":"$左调试文件夹",
                        "name":"screen_match.jpg"
                    },
                    "out":
                    {
                        "result":"保存模板匹配图片结果",
                        "full_name":"保存模板匹配图片路径"
                    }
                },
                {
                    "name":"保存圆心x",
                    "type":"set_global_float",
                    "in":
                    {
                        "input":"@circle_X",
                        "name":"左屏幕X"
                    },
                    "out":
                    {
                        "result":"保存圆心x结果"
                    }
                },
                {
                    "name":"保存圆心y",
                    "type":"set_global_float",
                    "in":
                    {
                        "input":"@circle_Y",
                        "name":"左屏幕Y"
                    },
                    "out":
                    {
                        "result":"保存圆心y结果"
                    }
                },
                {
                    "name":"推送模板x公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@屏幕模板匹配X\",\"*4\"]"
                    },
                    "out":
                    {
                        "result":"推送模板x公式结果",
                        "combine":"推送模板x"
                    }
                },
                {
                    "name":"计算模板x",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送模板x" 
                    },
                    "out":
                    {
                        "result":"计算模板x结果",
                        "value":"模板X"
                    }
                },
                {
                    "name":"推送模板y公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@屏幕模板匹配Y\",\"*4\"]"
                    },
                    "out":
                    {
                        "result":"推送模板y公式结果",
                        "combine":"推送模板y"
                    }
                },
                {
                    "name":"计算模板y",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送模板y" 
                    },
                    "out":
                    {
                        "result":"计算模板y结果",
                        "value":"模板Y"
                    }
                },
                {
                    "name":"保存模板x",
                    "type":"set_global_float",
                    "in":
                    {
                        "input":"@模板X",
                        "name":"左屏幕模板X"
                    },
                    "out":
                    {
                        "result":"保存模板x结果"
                    }
                },
                {
                    "name":"保存模板y",
                    "type":"set_global_float",
                    "in":
                    {
                        "input":"@模板Y",
                        "name":"左屏幕模板Y"
                    },
                    "out":
                    {
                        "result":"保存模板y结果"
                    }
                },
                {
                    "name":"推送保存公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@保存圆心x结果\",\"&\",\"@保存圆心y结果\",\"&\",\"@保存模板x结果\",\"&\",\"@保存模板y结果\"]"
                    },
                    "out":
                    {
                        "result":"推送保存公式结果",
                        "combine":"保存公式"
                    }
                },
                {
                    "name":"发送拍屏幕OK结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "wait":"@保存公式",
                        "client":"左PLC",
                        "db":"520",
                        "offset":"170",
                        "type":"short",
                        "content":"1"
                    }
                },
                {
                    "name":"结束拍屏幕OK",
                    "type":"end"
                },
                {
                    "name":"发送拍屏幕NG结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"170",
                        "type":"short",
                        "content":"2"
                    }
                },
                {
                    "name":"结束拍屏幕NG",
                    "type":"end"
                },
                {
                    "name":"生成xml名称2",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"screen.xml\"]"
                    },
                    "out":
                    {
                        "result":"生成xml名称2结果",
                        "combine":"xml名称2"
                    }
                },
                {
                    "name":"保存XML2",
                    "type":"save_xml",
                    "in":
                    {
                        "path":"$左图片文件夹",
                        "name":"@xml名称2",
                        "product":"mate3.0",
                        "sn":"$左SN码",
                        "station":"Align",
                        "mac":"NULL",
                        "detection_type":"screen",
                        "result":"1",
                        "folder":"$左图片文件夹",
                        "image_name":"@TRIGGER_STRING",
                        "width":"@获取屏幕图片信息宽",
                        "height":"@获取屏幕图片信息高",
                        "depth":"@获取屏幕图片信息通道",
                        "name_b":"[\"Ellipse\",\"Match\"]",
                        "label":"[\"1\",\"1\"]",
                        "standard":"[\"1\",\"1\"]",
                        "xmin":"[\"@椭圆矩形xmin\",\"@模板矩形还原xmin\"]",
                        "ymin":"[\"@椭圆矩形ymin\",\"@模板矩形还原ymin\"]",
                        "xmax":"[\"@椭圆矩形xmax\",\"@模板矩形还原xmax\"]",
                        "ymax":"[\"@椭圆矩形ymax\",\"@模板矩形还原ymax\"]"
                    },
                    "out":
                    {
                        "result":"保存XML结果",
                        "full_name":"保存XML全称"
                    }
                },
                {
                    "name":"生成筛选圆图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"screen_ellipse.jpg\"]"
                    },
                    "out":
                    {
                        "result":"生成筛选圆图片名称结果",
                        "combine":"筛选圆图片名称"
                    }
                },
                {
                    "name":"生成模板匹配图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"screen_temp.jpg\"]"
                    },
                    "out":
                    {
                        "result":"生成模板匹配图片名称结果",
                        "combine":"模板匹配图片名称"
                    }
                },
                {
                    "name":"保存筛选圆图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@屏幕找圆图片",
                        "path":"$左图片文件夹",
                        "name":"@筛选圆图片名称"
                    },
                    "out":
                    {
                        "result":"保存筛选圆图片结果",
                        "full_name":"保存筛选圆图片路径"
                    }
                },
                {
                    "name":"保存模板匹配图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@屏幕模板匹配图片",
                        "path":"$左图片文件夹",
                        "name":"@模板匹配图片名称"
                    },
                    "out":
                    {
                        "result":"保存模板匹配图片结果",
                        "full_name":"保存模板匹配图片路径"
                    }
                },
                {
                    "name":"结束离线模式",
                    "type":"end"
                }
            ] 
        },
        //右拍屏幕
        {
            "name":"右拍屏幕流程",
            "command":
            [
                {
                    "name":"读取模板图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"temp/screen_temp.jpg"
                    },
                    "out":
                    {
                        "image":"屏幕模板图片",
                        "result":"屏幕模板结果"
                    }
                },
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"."
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取字符串长度",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取字符串长度结果",
                        "string":"获取字符串长度"
                    }
                },
                {
                    "name":"判断获取字符串长度结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"读取屏幕图片",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"屏幕拍照",
                    "type":"get_camera_frame",
                    "in":
                    {
                        "camera":"右拍屏幕相机",
                        "exposure":"20000",
                        "gain":"4.0"
                    },
                    "out":
                    {
                        "result":"屏幕拍照结果",
                        "image":"屏幕拍照图片"
                    }
                },
                {
                    "name":"跳转获取时间",
                    "type":"jump",
                    "in":
                    {
                        "tag":"获取当前时间"
                    }
                },
                {
                    "name":"读取屏幕图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "image":"屏幕拍照图片",
                        "result":"屏幕拍照结果"
                    }
                },
                {
                    "name":"获取当前时间",
                    "type":"get_date_time",
                    "out":
                    {
                        "result":"获取当前时间结果",
                        "date_time":"当前时间"  
                    }
                    
                },
                {
                    "name":"生成图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"screen_\",\"@当前时间\",\"_\",\"$右SN码\",\".png\"]"
                    },
                    "out":
                    {
                        "result":"生成图片名称结果",
                        "combine":"图片名称"
                    }
                },
                {
                    "name":"生成xml名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"screen_\",\"@当前时间\",\"_\",\"$右SN码\",\".xml\"]"
                    },
                    "out":
                    {
                        "result":"生成xml名称结果",
                        "combine":"xml名称"
                    }
                },
                {
                    "name":"获取屏幕图片信息",
                    "type":"get_image_info",
                    "in":
                    {
                        "image":"@屏幕拍照图片"
                    },
                    "out":
                    {
                        "result":"获取屏幕图片信息结果",
                        "width":"获取屏幕图片信息宽",
                        "height":"获取屏幕图片信息高",
                        "channel":"获取屏幕图片信息通道"
                    }
                },
                {
                    "name":"离线模式不保存图片",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"判断拍屏幕结果",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"保存屏幕图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@屏幕拍照图片",
                        "path":"$右图片文件夹",
                        "name":"@图片名称"
                    },
                    "out":
                    {
                        "result":"保存图片结果",
                        "full_name":"图片路径"
                    }
                },
                {
                    "name":"判断拍屏幕结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"跳转算法流程",
                        "condition":"@屏幕拍照结果"
                    }
                },
                {
                    "name":"跳转拍屏幕失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示拍屏幕失败"
                    }
                },
                {
                    "name":"跳转算法流程",
                    "type":"jump",
                    "in":
                    {
                        "tag":"屏幕向下采样"
                    }
                },
                {
                    "name":"显示拍屏幕失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右拍屏幕失败",
                        "level":"error"
                    }
                },
                {
                    "name":"拍屏幕失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送拍屏幕NG结果"
                    }
                },
                {
                    "name":"屏幕向下采样",
                    "type":"pyramid",
                    "in":
                    {
                        "image":"@屏幕拍照图片",
                        "type":"down",
                        "count":"2"
                    },
                    "out":
                    {
                        "result":"屏幕向下采样结果",
                        "image_result":"屏幕向下采样图片"
                    }
                },
                {
                    "name":"屏幕模板向下采样",
                    "type":"pyramid",
                    "in":
                    {
                        "image":"@屏幕模板图片",
                        "type":"down",
                        "count":"2"
                    },
                    "out":
                    {
                        "result":"屏幕模板向下采样结果",
                        "image_result":"屏幕模板向下采样图片"
                    }
                },
                {
                    "name":"屏幕模板匹配",
                    "type":"calc_sharp_match_image",
                    "in":
                    {
                        "image":"@屏幕向下采样图片",
                        "template":"@屏幕模板向下采样图片",
                        "num_features":"120",
                        "pyramid_levels_h":"8",
                        "pyramid_levels_l":"4",
                        "angle_begin":"-5",
                        "angle_end":"5",
                        "angle_step":"0.1",
                        "scale_begin":"0.9",
                        "scale_end":"1.1",
                        "scale_step":"0.1",
                        "threshold":"70"
                    },
                    "out":
                    {
                        "result":"屏幕模板匹配结果",
                        "debug_img":"屏幕模板匹配图片",
                        "score":"屏幕模板匹配分数",
                        "scale":"屏幕模板匹配缩放",
                        "angle":"屏幕模板匹配角度",
                        "x":"屏幕模板匹配X",
                        "y":"屏幕模板匹配Y",
                        "xmin":"屏幕模板矩形xmin",
                        "ymin":"屏幕模板矩形ymin",
                        "xmax":"屏幕模板矩形xmax",
                        "ymax":"屏幕模板矩形ymax"
                    }
                },
                {
                    "name":"屏幕找圆",
                    "type":"find_circle",
                    "in":
                    {
                        "image":"@屏幕拍照图片",
                        "center_x":"2650",
                        "center_y":"1800",
                        "radius_min":"1200",
                        "radius_max":"1700",
                        "count":"100",
                        "width":"100",
                        "ksize":"17",
                        "type":"-1",
                        "find_type":"0",
                        "iterations":"50000",
                        "strength":"100",
                        "thresh":"0.3",
                        "sigma":"10",
                        "end":"0.9",
                        "minimum":"0.3",
                        "use_fit":"1"
                    },
                    "out":
                    {
                        "result":"屏幕找圆结果",
                        "x":"circle_X",
                        "y":"circle_Y",
                        "radius":"circle_R",
                        "debug_img":"屏幕找圆图片"
                    }
                },      
                {
                    "name":"判断找圆结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"判断模板匹配结果",
                        "condition":"@屏幕找圆结果"
                    }
                },
                {
                    "name":"显示找圆失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右屏幕找圆失败",
                        "level":"error"
                    }
                },
                {
                    "name":"找圆失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送拍屏幕NG结果"
                    }
                },
                {
                    "name":"判断模板匹配结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"推送计算圆直径公式",
                        "condition":"@屏幕模板匹配结果"
                    }
                },
                {
                    "name":"显示匹配失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右屏幕匹配失败",
                        "level":"error"
                    }
                },
                {
                    "name":"匹配失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送拍屏幕NG结果"
                    }
                },
                {
                    "name":"推送计算圆直径公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@circle_R\",\"*2\"]"
                    },
                    "out":
                    {
                        "result":"推送计算圆直径公式结果",
                        "combine":"推送计算圆直径"
                    }
                },
                {
                    "name":"计算圆直径",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算圆直径" 
                    },
                    "out":
                    {
                        "result":"计算圆直径结果",
                        "value":"圆直径"
                    }
                },
                {
                    "name":"计算椭圆矩形",
                    "type":"bounding_box",
                    "in":
                    {
                        "x":"@circle_X",
                        "y":"@circle_Y",
                        "w":"@圆直径",
                        "h":"@圆直径",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"计算椭圆矩形结果",
                        "xmin":"椭圆矩形xmin",
                        "ymin":"椭圆矩形ymin",
                        "xmax":"椭圆矩形xmax",
                        "ymax":"椭圆矩形ymax"
                    }
                },
                {
                    "name":"模板矩形还原",
                    "type":"bounding_box_scale",
                    "in":
                    {
                        "xmin_in":"@屏幕模板矩形xmin",
                        "ymin_in":"@屏幕模板矩形ymin",
                        "xmax_in":"@屏幕模板矩形xmax",
                        "ymax_in":"@屏幕模板矩形ymax",
                        "scale":"4"
                    },
                    "out":
                    {
                        "result":"模板矩形还原",
                        "xmin_out":"模板矩形还原xmin",
                        "ymin_out":"模板矩形还原ymin",
                        "xmax_out":"模板矩形还原xmax",
                        "ymax_out":"模板矩形还原ymax"
                    }
                },
                {
                    "name":"离线模式XML名字有变化",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"生成xml名称2",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"保存XML",
                    "type":"save_xml",
                    "in":
                    {
                        "path":"$右图片文件夹",
                        "name":"@xml名称",
                        "product":"mate3.0",
                        "sn":"$右SN码",
                        "station":"Align",
                        "mac":"NULL",
                        "detection_type":"screen",
                        "result":"1",
                        "folder":"$右图片文件夹",
                        "image_name":"@图片名称",
                        "width":"@获取屏幕图片信息宽",
                        "height":"@获取屏幕图片信息高",
                        "depth":"@获取屏幕图片信息通道",
                        "name_b":"[\"Ellipse\",\"Match\"]",
                        "label":"[\"1\",\"1\"]",
                        "standard":"[\"1\",\"1\"]",
                        "xmin":"[\"@椭圆矩形xmin\",\"@模板矩形还原xmin\"]",
                        "ymin":"[\"@椭圆矩形ymin\",\"@模板矩形还原ymin\"]",
                        "xmax":"[\"@椭圆矩形xmax\",\"@模板矩形还原xmax\"]",
                        "ymax":"[\"@椭圆矩形ymax\",\"@模板矩形还原ymax\"]"
                    },
                    "out":
                    {
                        "result":"保存XML结果",
                        "full_name":"保存XML全称"
                    }
                },
                {
                    "name":"保存筛选圆图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@屏幕找圆图片",
                        "path":"$右调试文件夹",
                        "name":"screen_ellipse.jpg"
                    },
                    "out":
                    {
                        "result":"保存筛选圆图片结果",
                        "full_name":"保存筛选圆图片路径"
                    }
                },
                {
                    "name":"保存模板匹配图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@屏幕模板匹配图片",
                        "path":"$右调试文件夹",
                        "name":"screen_match.jpg"
                    },
                    "out":
                    {
                        "result":"保存模板匹配图片结果",
                        "full_name":"保存模板匹配图片路径"
                    }
                },
                {
                    "name":"保存圆心x",
                    "type":"set_global_float",
                    "in":
                    {
                        "input":"@circle_X",
                        "name":"右屏幕X"
                    },
                    "out":
                    {
                        "result":"保存圆心x结果"
                    }
                },
                {
                    "name":"保存圆心y",
                    "type":"set_global_float",
                    "in":
                    {
                        "input":"@circle_Y",
                        "name":"右屏幕Y"
                    },
                    "out":
                    {
                        "result":"保存圆心y结果"
                    }
                },
                {
                    "name":"推送模板x公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@屏幕模板匹配X\",\"*4\"]"
                    },
                    "out":
                    {
                        "result":"推送模板x公式结果",
                        "combine":"推送模板x"
                    }
                },
                {
                    "name":"计算模板x",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送模板x" 
                    },
                    "out":
                    {
                        "result":"计算模板x结果",
                        "value":"模板X"
                    }
                },
                {
                    "name":"推送模板y公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@屏幕模板匹配Y\",\"*4\"]"
                    },
                    "out":
                    {
                        "result":"推送模板y公式结果",
                        "combine":"推送模板y"
                    }
                },
                {
                    "name":"计算模板y",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送模板y" 
                    },
                    "out":
                    {
                        "result":"计算模板y结果",
                        "value":"模板Y"
                    }
                },
                {
                    "name":"保存模板x",
                    "type":"set_global_float",
                    "in":
                    {
                        "input":"@模板X",
                        "name":"右屏幕模板X"
                    },
                    "out":
                    {
                        "result":"保存模板x结果"
                    }
                },
                {
                    "name":"保存模板y",
                    "type":"set_global_float",
                    "in":
                    {
                        "input":"@模板Y",
                        "name":"右屏幕模板Y"
                    },
                    "out":
                    {
                        "result":"保存模板y结果"
                    }
                },
                {
                    "name":"推送保存公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@保存圆心x结果\",\"&\",\"@保存圆心y结果\",\"&\",\"@保存模板x结果\",\"&\",\"@保存模板y结果\"]"
                    },
                    "out":
                    {
                        "result":"推送保存公式结果",
                        "combine":"保存公式"
                    }
                },
                {
                    "name":"发送拍屏幕OK结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "wait":"@保存公式",
                        "client":"右PLC",
                        "db":"520",
                        "offset":"170",
                        "type":"short",
                        "content":"1"
                    }
                },
                {
                    "name":"结束拍屏幕OK",
                    "type":"end"
                },
                {
                    "name":"发送拍屏幕NG结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"170",
                        "type":"short",
                        "content":"2"
                    }
                },
                {
                    "name":"结束拍屏幕NG",
                    "type":"end"
                },
                {
                    "name":"生成xml名称2",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"screen.xml\"]"
                    },
                    "out":
                    {
                        "result":"生成xml名称2结果",
                        "combine":"xml名称2"
                    }
                },
                {
                    "name":"保存XML2",
                    "type":"save_xml",
                    "in":
                    {
                        "path":"$右图片文件夹",
                        "name":"@xml名称2",
                        "product":"mate3.0",
                        "sn":"$右SN码",
                        "station":"Align",
                        "mac":"NULL",
                        "detection_type":"screen",
                        "result":"1",
                        "folder":"$右图片文件夹",
                        "image_name":"@TRIGGER_STRING",
                        "width":"@获取屏幕图片信息宽",
                        "height":"@获取屏幕图片信息高",
                        "depth":"@获取屏幕图片信息通道",
                        "name_b":"[\"Ellipse\",\"Match\"]",
                        "label":"[\"1\",\"1\"]",
                        "standard":"[\"1\",\"1\"]",
                        "xmin":"[\"@椭圆矩形xmin\",\"@模板矩形还原xmin\"]",
                        "ymin":"[\"@椭圆矩形ymin\",\"@模板矩形还原ymin\"]",
                        "xmax":"[\"@椭圆矩形xmax\",\"@模板矩形还原xmax\"]",
                        "ymax":"[\"@椭圆矩形ymax\",\"@模板矩形还原ymax\"]"
                    },
                    "out":
                    {
                        "result":"保存XML结果",
                        "full_name":"保存XML全称"
                    }
                },
                {
                    "name":"生成筛选圆图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"screen_ellipse.jpg\"]"
                    },
                    "out":
                    {
                        "result":"生成筛选圆图片名称结果",
                        "combine":"筛选圆图片名称"
                    }
                },
                {
                    "name":"生成模板匹配图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"screen_temp.jpg\"]"
                    },
                    "out":
                    {
                        "result":"生成模板匹配图片名称结果",
                        "combine":"模板匹配图片名称"
                    }
                },
                {
                    "name":"保存筛选圆图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@屏幕找圆图片",
                        "path":"$右图片文件夹",
                        "name":"@筛选圆图片名称"
                    },
                    "out":
                    {
                        "result":"保存筛选圆图片结果",
                        "full_name":"保存筛选圆图片路径"
                    }
                },
                {
                    "name":"保存模板匹配图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@屏幕模板匹配图片",
                        "path":"$右图片文件夹",
                        "name":"@模板匹配图片名称"
                    },
                    "out":
                    {
                        "result":"保存模板匹配图片结果",
                        "full_name":"保存模板匹配图片路径"
                    }
                },
                {
                    "name":"结束离线流程",
                    "type":"end"
                }
            ] 
        },
        //左拍屏幕盖板
        {
            "name":"左拍屏幕盖板流程",
            "command":
            [
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"."
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取字符串长度",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取字符串长度结果",
                        "string":"获取字符串长度"
                    }
                },
                {
                    "name":"判断获取字符串长度结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"读取屏幕图片",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"屏幕拍照",
                    "type":"get_camera_frame",
                    "in":
                    {
                        "camera":"左拍屏幕相机",
                        "exposure":"20000",
                        "gain":"4.0"
                    },
                    "out":
                    {
                        "result":"屏幕拍照结果",
                        "image":"屏幕拍照图片"
                    }
                },
                {
                    "name":"跳转获取时间",
                    "type":"jump",
                    "in":
                    {
                        "tag":"获取当前时间"
                    }
                },
                {
                    "name":"读取屏幕图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "image":"屏幕拍照图片",
                        "result":"屏幕拍照结果"
                    }
                },
                {
                    "name":"获取当前时间",
                    "type":"get_date_time",
                    "out":
                    {
                        "result":"获取当前时间结果",
                        "date_time":"当前时间"  
                    }
                    
                },
                {
                    "name":"生成图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"screen_\",\"@当前时间\",\"_\",\"$左SN码\",\".png\"]"
                    },
                    "out":
                    {
                        "result":"生成图片名称结果",
                        "combine":"图片名称"
                    }
                },
                {
                    "name":"生成xml名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"screen_\",\"@当前时间\",\"_\",\"$左SN码\",\".xml\"]"
                    },
                    "out":
                    {
                        "result":"生成xml名称结果",
                        "combine":"xml名称"
                    }
                },
                {
                    "name":"获取屏幕图片信息",
                    "type":"get_image_info",
                    "in":
                    {
                        "image":"@屏幕拍照图片"
                    },
                    "out":
                    {
                        "result":"获取屏幕图片信息结果",
                        "width":"获取屏幕图片信息宽",
                        "height":"获取屏幕图片信息高",
                        "channel":"获取屏幕图片信息通道"
                    }
                },
                {
                    "name":"保存屏幕图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@屏幕拍照图片",
                        "path":"$左图片文件夹",
                        "name":"@图片名称"
                    },
                    "out":
                    {
                        "result":"保存图片结果",
                        "full_name":"图片路径"
                    }
                },
                {
                    "name":"判断拍屏幕结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"跳转算法流程",
                        "condition":"@屏幕拍照结果"
                    }
                },
                {
                    "name":"跳转拍屏幕失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示拍屏幕失败"
                    }
                },
                {
                    "name":"跳转算法流程",
                    "type":"jump",
                    "in":
                    {
                        "tag":"屏幕向下采样"
                    }
                },
                {
                    "name":"显示拍屏幕失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"左拍屏幕失败",
                        "level":"error"
                    }
                },
                {
                    "name":"拍屏幕失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送拍屏幕NG结果"
                    }
                },
                {
                    "name":"屏幕向下采样",
                    "type":"pyramid",
                    "in":
                    {
                        "image":"@屏幕拍照图片",
                        "type":"down",
                        "count":"2"
                    },
                    "out":
                    {
                        "result":"屏幕向下采样结果",
                        "image_result":"屏幕向下采样图片"
                    }
                },
                {
                    "name":"屏幕找圆",
                    "type":"find_circle",
                    "in":
                    {
                        "image":"@屏幕拍照图片",
                        "center_x":"2650",
                        "center_y":"1800",
                        "radius_min":"1300",
                        "radius_max":"1700",
                        "count":"100",
                        "width":"100",
                        "ksize":"17",
                        "type":"-1",
                        "find_type":"0",
                        "iterations":"50000",
                        "strength":"100",
                        "thresh":"0.3",
                        "sigma":"10",
                        "end":"0.9",
                        "minimum":"0.3",
                        "use_fit":"1"
                    },
                    "out":
                    {
                        "result":"屏幕找圆结果",
                        "x":"circle_X",
                        "y":"circle_Y",
                        "radius":"circle_R",
                        "debug_img":"屏幕找圆图片"
                    }
                },    
                {
                    "name":"判断找圆结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"推送计算圆直径公式",
                        "condition":"@屏幕找圆结果"
                    }
                },
                {
                    "name":"显示找圆失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"左屏幕找圆失败",
                        "level":"error"
                    }
                },
                {
                    "name":"找圆失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送拍屏幕NG结果"
                    }
                },
                {
                    "name":"推送计算圆直径公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@circle_R\",\"*2\"]"
                    },
                    "out":
                    {
                        "result":"推送计算圆直径公式结果",
                        "combine":"推送计算圆直径"
                    }
                },
                {
                    "name":"计算圆直径",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算圆直径" 
                    },
                    "out":
                    {
                        "result":"计算圆直径结果",
                        "value":"圆直径"
                    }
                },
                {
                    "name":"计算椭圆矩形",
                    "type":"bounding_box",
                    "in":
                    {
                        "x":"@circle_X",
                        "y":"@circle_Y",
                        "w":"@圆直径",
                        "h":"@圆直径",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"计算椭圆矩形结果",
                        "xmin":"椭圆矩形xmin",
                        "ymin":"椭圆矩形ymin",
                        "xmax":"椭圆矩形xmax",
                        "ymax":"椭圆矩形ymax"
                    }
                },
                {
                    "name":"保存XML",
                    "type":"save_xml",
                    "in":
                    {
                        "path":"$左图片文件夹",
                        "name":"@xml名称",
                        "product":"mate3.0",
                        "sn":"$左SN码",
                        "station":"Align",
                        "mac":"NULL",
                        "detection_type":"screen",
                        "result":"1",
                        "folder":"$左图片文件夹",
                        "image_name":"@图片名称",
                        "width":"@获取屏幕图片信息宽",
                        "height":"@获取屏幕图片信息高",
                        "depth":"@获取屏幕图片信息通道",
                        "name_b":"[\"Ellipse\"]",
                        "label":"[\"1\"]",
                        "standard":"[\"1\"]",
                        "xmin":"[\"@椭圆矩形xmin\"]",
                        "ymin":"[\"@椭圆矩形ymin\"]",
                        "xmax":"[\"@椭圆矩形xmax\"]",
                        "ymax":"[\"@椭圆矩形ymax\"]"
                    },
                    "out":
                    {
                        "result":"保存XML结果",
                        "full_name":"保存XML全称"
                    }
                },
                {
                    "name":"保存筛选圆图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@屏幕找圆图片",
                        "path":"$左调试文件夹",
                        "name":"screen_ellipse.jpg"
                    },
                    "out":
                    {
                        "result":"保存筛选圆图片结果",
                        "full_name":"保存筛选圆图片路径"
                    }
                },
                {
                    "name":"保存圆心x",
                    "type":"set_global_float",
                    "in":
                    {
                        "input":"@circle_X",
                        "name":"左屏幕X"
                    },
                    "out":
                    {
                        "result":"保存圆心x结果"
                    }
                },
                {
                    "name":"保存圆心y",
                    "type":"set_global_float",
                    "in":
                    {
                        "input":"@circle_Y",
                        "name":"左屏幕Y"
                    },
                    "out":
                    {
                        "result":"保存圆心y结果"
                    }
                },
                {
                    "name":"推送模板x公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"0\",\"*4\"]"
                    },
                    "out":
                    {
                        "result":"推送模板x公式结果",
                        "combine":"推送模板x"
                    }
                },
                {
                    "name":"计算模板x",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送模板x" 
                    },
                    "out":
                    {
                        "result":"计算模板x结果",
                        "value":"模板X"
                    }
                },
                {
                    "name":"推送模板y公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"0\",\"*4\"]"
                    },
                    "out":
                    {
                        "result":"推送模板y公式结果",
                        "combine":"推送模板y"
                    }
                },
                {
                    "name":"计算模板y",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送模板y" 
                    },
                    "out":
                    {
                        "result":"计算模板y结果",
                        "value":"模板Y"
                    }
                },
                {
                    "name":"保存模板x",
                    "type":"set_global_float",
                    "in":
                    {
                        "input":"@模板X",
                        "name":"左屏幕模板X"
                    },
                    "out":
                    {
                        "result":"保存模板x结果"
                    }
                },
                {
                    "name":"保存模板y",
                    "type":"set_global_float",
                    "in":
                    {
                        "input":"@模板Y",
                        "name":"左屏幕模板Y"
                    },
                    "out":
                    {
                        "result":"保存模板y结果"
                    }
                },
                {
                    "name":"推送保存公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@保存圆心x结果\",\"&\",\"@保存圆心y结果\",\"&\",\"@保存模板x结果\",\"&\",\"@保存模板y结果\"]"
                    },
                    "out":
                    {
                        "result":"推送保存公式结果",
                        "combine":"保存公式"
                    }
                },
                {
                    "name":"发送拍屏幕OK结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "wait":"@保存公式",
                        "client":"左PLC",
                        "db":"520",
                        "offset":"170",
                        "type":"short",
                        "content":"1"
                    }
                },
                {
                    "name":"结束拍屏幕OK",
                    "type":"end"
                },
                {
                    "name":"发送拍屏幕NG结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"170",
                        "type":"short",
                        "content":"2"
                    }
                },
                {
                    "name":"结束拍屏幕NG",
                    "type":"end"
                }
            ] 
        },
        //右拍屏幕盖板
        {
            "name":"右拍屏幕盖板流程",
            "command":
            [
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"."
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取字符串长度",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取字符串长度结果",
                        "string":"获取字符串长度"
                    }
                },
                {
                    "name":"判断获取字符串长度结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"读取屏幕图片",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"屏幕拍照",
                    "type":"get_camera_frame",
                    "in":
                    {
                        "camera":"右拍屏幕相机",
                        "exposure":"20000",
                        "gain":"4.0"
                    },
                    "out":
                    {
                        "result":"屏幕拍照结果",
                        "image":"屏幕拍照图片"
                    }
                },
                {
                    "name":"跳转获取时间",
                    "type":"jump",
                    "in":
                    {
                        "tag":"获取当前时间"
                    }
                },
                {
                    "name":"读取屏幕图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "image":"屏幕拍照图片",
                        "result":"屏幕拍照结果"
                    }
                },
                {
                    "name":"获取当前时间",
                    "type":"get_date_time",
                    "out":
                    {
                        "result":"获取当前时间结果",
                        "date_time":"当前时间"  
                    }
                    
                },
                {
                    "name":"生成图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"screen_\",\"@当前时间\",\"_\",\"$右SN码\",\".png\"]"
                    },
                    "out":
                    {
                        "result":"生成图片名称结果",
                        "combine":"图片名称"
                    }
                },
                {
                    "name":"生成xml名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"screen_\",\"@当前时间\",\"_\",\"$右SN码\",\".xml\"]"
                    },
                    "out":
                    {
                        "result":"生成xml名称结果",
                        "combine":"xml名称"
                    }
                },
                {
                    "name":"获取屏幕图片信息",
                    "type":"get_image_info",
                    "in":
                    {
                        "image":"@屏幕拍照图片"
                    },
                    "out":
                    {
                        "result":"获取屏幕图片信息结果",
                        "width":"获取屏幕图片信息宽",
                        "height":"获取屏幕图片信息高",
                        "channel":"获取屏幕图片信息通道"
                    }
                },
                {
                    "name":"保存屏幕图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@屏幕拍照图片",
                        "path":"$右图片文件夹",
                        "name":"@图片名称"
                    },
                    "out":
                    {
                        "result":"保存图片结果",
                        "full_name":"图片路径"
                    }
                },
                {
                    "name":"判断拍屏幕结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"跳转算法流程",
                        "condition":"@屏幕拍照结果"
                    }
                },
                {
                    "name":"跳转拍屏幕失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示拍屏幕失败"
                    }
                },
                {
                    "name":"跳转算法流程",
                    "type":"jump",
                    "in":
                    {
                        "tag":"屏幕向下采样"
                    }
                },
                {
                    "name":"显示拍屏幕失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右拍屏幕失败",
                        "level":"error"
                    }
                },
                {
                    "name":"拍屏幕失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送拍屏幕NG结果"
                    }
                },
                {
                    "name":"屏幕向下采样",
                    "type":"pyramid",
                    "in":
                    {
                        "image":"@屏幕拍照图片",
                        "type":"down",
                        "count":"2"
                    },
                    "out":
                    {
                        "result":"屏幕向下采样结果",
                        "image_result":"屏幕向下采样图片"
                    }
                },
                {
                    "name":"屏幕找圆",
                    "type":"find_circle",
                    "in":
                    {
                        "image":"@屏幕拍照图片",
                        "center_x":"2650",
                        "center_y":"1800",
                        "radius_min":"1300",
                        "radius_max":"1700",
                        "count":"100",
                        "width":"100",
                        "ksize":"17",
                        "type":"-1",
                        "find_type":"0",
                        "iterations":"50000",
                        "strength":"100",
                        "thresh":"0.3",
                        "sigma":"10",
                        "end":"0.9",
                        "minimum":"0.3",
                        "use_fit":"1"
                    },
                    "out":
                    {
                        "result":"屏幕找圆结果",
                        "x":"circle_X",
                        "y":"circle_Y",
                        "radius":"circle_R",
                        "debug_img":"屏幕找圆图片"
                    }
                },
                {
                    "name":"判断找圆结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"推送计算圆直径公式",
                        "condition":"@屏幕找圆结果"
                    }
                },
                {
                    "name":"显示找圆失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右屏幕找圆失败",
                        "level":"error"
                    }
                },
                {
                    "name":"找圆失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送拍屏幕NG结果"
                    }
                },
                {
                    "name":"推送计算圆直径公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@circle_R\",\"*2\"]"
                    },
                    "out":
                    {
                        "result":"推送计算圆直径结果",
                        "combine":"推送计算圆直径"
                    }
                },
                {
                    "name":"计算圆直径",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算圆直径" 
                    },
                    "out":
                    {
                        "result":"计算圆直径结果",
                        "value":"圆直径"
                    }
                },
                {
                    "name":"计算椭圆矩形",
                    "type":"bounding_box",
                    "in":
                    {
                        "x":"@circle_X",
                        "y":"@circle_Y",
                        "w":"@圆直径",
                        "h":"@圆直径",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"计算椭圆矩形结果",
                        "xmin":"椭圆矩形xmin",
                        "ymin":"椭圆矩形ymin",
                        "xmax":"椭圆矩形xmax",
                        "ymax":"椭圆矩形ymax"
                    }
                },
                {
                    "name":"保存XML",
                    "type":"save_xml",
                    "in":
                    {
                        "path":"$右图片文件夹",
                        "name":"@xml名称",
                        "product":"mate3.0",
                        "sn":"$右SN码",
                        "station":"Align",
                        "mac":"NULL",
                        "detection_type":"screen",
                        "result":"1",
                        "folder":"$右图片文件夹",
                        "image_name":"@图片名称",
                        "width":"@获取屏幕图片信息宽",
                        "height":"@获取屏幕图片信息高",
                        "depth":"@获取屏幕图片信息通道",
                        "name_b":"[\"Ellipse\"]",
                        "label":"[\"1\"]",
                        "standard":"[\"1\"]",
                        "xmin":"[\"@椭圆矩形xmin\"]",
                        "ymin":"[\"@椭圆矩形ymin\"]",
                        "xmax":"[\"@椭圆矩形xmax\"]",
                        "ymax":"[\"@椭圆矩形ymax\"]"
                    },
                    "out":
                    {
                        "result":"保存XML结果",
                        "full_name":"保存XML全称"
                    }
                },
                {
                    "name":"保存筛选圆图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@屏幕找圆图片",
                        "path":"$右调试文件夹",
                        "name":"screen_ellipse.jpg"
                    },
                    "out":
                    {
                        "result":"保存筛选圆图片结果",
                        "full_name":"保存筛选圆图片路径"
                    }
                },
                {
                    "name":"保存圆心x",
                    "type":"set_global_float",
                    "in":
                    {
                        "input":"@circle_X",
                        "name":"右屏幕X"
                    },
                    "out":
                    {
                        "result":"保存圆心x结果"
                    }
                },
                {
                    "name":"保存圆心y",
                    "type":"set_global_float",
                    "in":
                    {
                        "input":"@circle_Y",
                        "name":"右屏幕Y"
                    },
                    "out":
                    {
                        "result":"保存圆心y结果"
                    }
                },
                {
                    "name":"推送模板x公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"0\",\"*4\"]"
                    },
                    "out":
                    {
                        "result":"推送模板x公式结果",
                        "combine":"推送模板x"
                    }
                },
                {
                    "name":"计算模板x",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送模板x" 
                    },
                    "out":
                    {
                        "result":"计算模板x结果",
                        "value":"模板X"
                    }
                },
                {
                    "name":"推送模板y公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"0\",\"*4\"]"
                    },
                    "out":
                    {
                        "result":"推送模板y公式结果",
                        "combine":"推送模板y"
                    }
                },
                {
                    "name":"计算模板y",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送模板y" 
                    },
                    "out":
                    {
                        "result":"计算模板y结果",
                        "value":"模板Y"
                    }
                },
                {
                    "name":"保存模板x",
                    "type":"set_global_float",
                    "in":
                    {
                        "input":"@模板X",
                        "name":"右屏幕模板X"
                    },
                    "out":
                    {
                        "result":"保存模板x结果"
                    }
                },
                {
                    "name":"保存模板y",
                    "type":"set_global_float",
                    "in":
                    {
                        "input":"@模板Y",
                        "name":"右屏幕模板Y"
                    },
                    "out":
                    {
                        "result":"保存模板y结果"
                    }
                },
                {
                    "name":"推送保存公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@保存圆心x结果\",\"&\",\"@保存圆心y结果\",\"&\",\"@保存模板x结果\",\"&\",\"@保存模板y结果\"]"
                    },
                    "out":
                    {
                        "result":"推送保存公式结果",
                        "combine":"保存公式"
                    }
                },
                {
                    "name":"发送拍屏幕OK结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "wait":"@保存公式",
                        "client":"右PLC",
                        "db":"520",
                        "offset":"170",
                        "type":"short",
                        "content":"1"
                    }
                },
                {
                    "name":"结束拍屏幕OK",
                    "type":"end"
                },
                {
                    "name":"发送拍屏幕NG结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"170",
                        "type":"short",
                        "content":"2"
                    }
                },
                {
                    "name":"结束拍屏幕NG",
                    "type":"end"
                }
            ] 
        },
        //左对位
        {
            "name":"左对位流程",
            "command":
            [
                {
                    "name":"读取模板图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"temp/back_temp.jpg"
                    },
                    "out":
                    {
                        "image":"后壳模板图片",
                        "result":"后壳模板结果"
                    }
                },
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"."
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取字符串长度",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取字符串长度结果",
                        "string":"获取字符串长度"
                    }
                },
                {
                    "name":"判断获取字符串长度结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"读取后壳图片",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"后壳拍照",
                    "type":"get_camera_frame",
                    "in":
                    {
                        "camera":"左拍后壳相机",
                        "exposure":"300000",
                        "gain":"8.0"
                    },
                    "out":
                    {
                        "result":"后壳拍照结果",
                        "image":"后壳拍照图片"
                    }
                },
                {
                    "name":"跳转获取时间",
                    "type":"jump",
                    "in":
                    {
                        "tag":"获取当前时间"
                    }
                },
                {
                    "name":"读取后壳图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "image":"后壳拍照图片",
                        "result":"后壳拍照结果"
                    }
                },
                {
                    "name":"获取当前时间",
                    "type":"get_date_time",
                    "out":
                    {
                        "result":"获取当前时间结果",
                        "date_time":"当前时间"  
                    }
                    
                },
                {
                    "name":"生成图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"back_\",\"$左对位次数\",\"_\",\"@当前时间\",\"_\",\"$左SN码\",\".png\"]"
                    },
                    "out":
                    {
                        "result":"生成后壳名称结果",
                        "combine":"图片名称"
                    }
                },
                {
                    "name":"生成xml名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"back_\",\"$左对位次数\",\"_\",\"@当前时间\",\"_\",\"$左SN码\",\".xml\"]"
                    },
                    "out":
                    {
                        "result":"生成xml名称结果",
                        "combine":"生成xml名称"
                    }
                },
                {
                    "name":"获取后壳图片信息",
                    "type":"get_image_info",
                    "in":
                    {
                        "image":"@后壳拍照图片"
                    },
                    "out":
                    {
                        "result":"获取后壳图片信息结果",
                        "width":"获取后壳图片信息宽",
                        "height":"获取后壳图片信息高",
                        "channel":"获取后壳图片信息通道"
                    }
                },
                {
                    "name":"离线模式不保存图片",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"判断拍后壳结果",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"保存后壳图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳拍照图片",
                        "path":"$左图片文件夹",
                        "name":"@图片名称"
                    },
                    "out":
                    {
                        "result":"保存图片结果",
                        "full_name":"图片路径"
                    }
                },
                {
                    "name":"判断拍后壳结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"跳转算法流程",
                        "condition":"@后壳拍照结果"
                    }
                },
                {
                    "name":"跳转拍后壳失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示拍后壳失败"
                    }
                },
                {
                    "name":"跳转算法流程",
                    "type":"jump",
                    "in":
                    {
                        "tag":"后壳向下采样"
                    }
                },
                {
                    "name":"显示拍后壳失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"左拍后壳失败",
                        "level":"error"
                    }
                },
                {
                    "name":"拍后壳失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送对位NG结果"
                    }
                },
                {
                    "name":"后壳向下采样",
                    "type":"pyramid",
                    "in":
                    {
                        "image":"@后壳拍照图片",
                        "type":"down",
                        "count":"2"
                    },
                    "out":
                    {
                        "result":"后壳向下采样结果",
                        "image_result":"后壳向下采样图片"
                    }
                },
                {
                    "name":"后壳模板向下采样",
                    "type":"pyramid",
                    "in":
                    {
                        "image":"@后壳模板图片",
                        "type":"down",
                        "count":"2"
                    },
                    "out":
                    {
                        "result":"后壳模板向下采样结果",
                        "image_result":"后壳模板向下采样图片"
                    }
                },
                {
                    "name":"后壳模板匹配",
                    "type":"calc_sharp_match_image",
                    "in":
                    {
                        "image":"@后壳向下采样图片",
                        "template":"@后壳模板向下采样图片",
                        "num_features":"100",
                        "pyramid_levels_h":"8",
                        "pyramid_levels_l":"4",
                        "angle_begin":"-5",
                        "angle_end":"5",
                        "angle_step":"0.1",
                        "scale_begin":"0.9",
                        "scale_end":"1.1",
                        "scale_step":"0.1",
                        "threshold":"60"
                    },
                    "out":
                    {
                        "result":"后壳模板匹配结果",
                        "debug_img":"后壳模板匹配图片",
                        "score":"后壳模板匹配分数",
                        "scale":"后壳模板匹配缩放",
                        "angle":"后壳模板匹配角度",
                        "x":"后壳模板匹配X",
                        "y":"后壳模板匹配Y",
                        "xmin":"后壳模板矩形xmin",
                        "ymin":"后壳模板矩形ymin",
                        "xmax":"后壳模板矩形xmax",
                        "ymax":"后壳模板矩形ymax"
                    }
                },
                {
                    "name":"后壳找圆图像增强",
                    "type":"multiply",
                    "in":
                    {
                        "image_in":"@后壳拍照图片",
                        "mul":"3"
                    },
                    "out":
                    {
                        "result":"后壳找圆图像增强结果",
                        "image_out":"后壳向下采样增强图片"
                    }
                },
                {
                    "name":"后壳找圆",
                    "type":"find_circle",
                    "in":
                    {
                        "image":"@后壳向下采样增强图片",
                        "center_x":"2600",
                        "center_y":"2700",
                        "radius_min":"1500",
                        "radius_max":"2200",
                        "count":"200",
                        "width":"100",
                        "ksize":"17",
                        "type":"1",
                        "find_type":"1",
                        "iterations":"50000",
                        "strength":"100",
                        "thresh":"0.3",
                        "sigma":"10",
                        "end":"0.9",
                        "minimum":"0.3",
                        "use_fit":"1"
                    },
                    "out":
                    {
                        "result":"后壳找圆结果",
                        "x":"circle_X",
                        "y":"circle_Y",
                        "radius":"circle_R",
                        "debug_img":"后壳找圆图片"
                    }
                },
                {
                    "name":"判断拍找圆结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"判断模板匹配结果",
                        "condition":"@后壳找圆结果"
                    }
                },
                {
                    "name":"显示找圆失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"左后壳找圆失败",
                        "level":"error"
                    }
                },
                {
                    "name":"找圆失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送对位NG结果"
                    }
                },
                {
                    "name":"判断模板匹配结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"保存筛选圆图片",
                        "condition":"@后壳模板匹配结果"
                    }
                },
                {
                    "name":"显示匹配失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"左后壳匹配失败",
                        "level":"error"
                    }
                },
                {
                    "name":"匹配失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送对位NG结果"
                    }
                },
                {
                    "name":"保存筛选圆图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳找圆图片",
                        "path":"$左调试文件夹",
                        "name":"back_ellipse.jpg"
                    },
                    "out":
                    {
                        "result":"保存筛选圆图片结果",
                        "full_name":"保存筛选圆图片路径"
                    }
                },
                {
                    "name":"保存模板匹配图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳模板匹配图片",
                        "path":"$左调试文件夹",
                        "name":"back_match.jpg"
                    },
                    "out":
                    {
                        "result":"保存模板匹配图片结果",
                        "full_name":"保存模板匹配图片路径"
                    }
                },
                {
                    "name":"推送计算圆直径公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@circle_R\",\"*2\"]"
                    },
                    "out":
                    {
                        "result":"推送计算圆直径公式结果",
                        "combine":"推送计算圆直径"
                    }
                },
                {
                    "name":"计算圆直径",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算圆直径" 
                    },
                    "out":
                    {
                        "result":"计算圆直径结果",
                        "value":"圆直径"
                    }
                },
                {
                    "name":"计算椭圆矩形",
                    "type":"bounding_box",
                    "in":
                    {
                        "x":"@circle_X",
                        "y":"@circle_Y",
                        "w":"@圆直径",
                        "h":"@圆直径",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"计算椭圆矩形结果",
                        "xmin":"椭圆矩形xmin",
                        "ymin":"椭圆矩形ymin",
                        "xmax":"椭圆矩形xmax",
                        "ymax":"椭圆矩形ymax"
                    }
                },
                {
                    "name":"模板矩形还原",
                    "type":"bounding_box_scale",
                    "in":
                    {
                        "xmin_in":"@后壳模板矩形xmin",
                        "ymin_in":"@后壳模板矩形ymin",
                        "xmax_in":"@后壳模板矩形xmax",
                        "ymax_in":"@后壳模板矩形ymax",
                        "scale":"4"
                    },
                    "out":
                    {
                        "result":"模板矩形还原",
                        "xmin_out":"模板矩形还原xmin",
                        "ymin_out":"模板矩形还原ymin",
                        "xmax_out":"模板矩形还原xmax",
                        "ymax_out":"模板矩形还原ymax"
                    }
                },
                {
                    "name":"离线模式XML名字有变化",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"生成xml名称2",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"保存XML",
                    "type":"save_xml",
                    "in":
                    {
                        "path":"$左图片文件夹",
                        "name":"@生成xml名称",
                        "product":"mate3.0",
                        "sn":"$左SN码",
                        "station":"Align",
                        "mac":"NULL",
                        "detection_type":"back",
                        "result":"1",
                        "folder":"$左图片文件夹",
                        "image_name":"@图片名称",
                        "width":"@获取后壳图片信息宽",
                        "height":"@获取后壳图片信息高",
                        "depth":"@获取后壳图片信息通道",
                        "name_b":"[\"Ellipse\",\"Match\"]",
                        "label":"[\"1\",\"1\"]",
                        "standard":"[\"1\",\"1\"]",
                        "xmin":"[\"@椭圆矩形xmin\",\"@模板矩形还原xmin\"]",
                        "ymin":"[\"@椭圆矩形ymin\",\"@模板矩形还原ymin\"]",
                        "xmax":"[\"@椭圆矩形xmax\",\"@模板矩形还原xmax\"]",
                        "ymax":"[\"@椭圆矩形ymax\",\"@模板矩形还原ymax\"]"
                    },
                    "out":
                    {
                        "result":"保存XML结果",
                        "full_name":"保存XML全称"
                    }
                },
                {
                    "name":"推送模板x公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@后壳模板匹配X\",\"*4\"]"
                    },
                    "out":
                    {
                        "result":"推送模板x公式结果",
                        "combine":"推送模板x"
                    }
                },
                {
                    "name":"计算模板x",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送模板x" 
                    },
                    "out":
                    {
                        "result":"计算模板x结果",
                        "value":"模板X"
                    }
                },
                {
                    "name":"推送模板y公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@后壳模板匹配Y\",\"*4\"]"
                    },
                    "out":
                    {
                        "result":"推送模板y公式结果",
                        "combine":"推送模板y"
                    }
                },
                {
                    "name":"计算模板y",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送模板y" 
                    },
                    "out":
                    {
                        "result":"计算模板y结果",
                        "value":"模板Y"
                    }
                },
                {
                    "name":"计算屏幕圆心到后壳像素坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"$左屏幕X",
                        "y_in":"$左屏幕Y",
                        "A":"$左屏幕相机矩阵A",
                        "B":"$左屏幕相机矩阵B",
                        "C":"$左屏幕相机矩阵C",
                        "D":"$左屏幕相机矩阵D",
                        "E":"$左屏幕相机矩阵E",
                        "F":"$左屏幕相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算屏幕圆心到后壳像素坐标结果",
                        "x_out":"屏幕像素圆心x",
                        "y_out":"屏幕像素圆心y"
                    }
                },
                {
                    "name":"计算屏幕模板到后壳像素坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"$左屏幕模板X",
                        "y_in":"$左屏幕模板Y",
                        "A":"$左屏幕相机矩阵A",
                        "B":"$左屏幕相机矩阵B",
                        "C":"$左屏幕相机矩阵C",
                        "D":"$左屏幕相机矩阵D",
                        "E":"$左屏幕相机矩阵E",
                        "F":"$左屏幕相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算屏幕模板到后壳像素坐标结果",
                        "x_out":"屏幕像素模板x",
                        "y_out":"屏幕像素模板y"
                    }
                },
                {
                    "name":"计算屏幕圆心物理坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"@屏幕像素圆心x",
                        "y_in":"@屏幕像素圆心y",
                        "A":"$左对位相机矩阵A",
                        "B":"$左对位相机矩阵B",
                        "C":"$左对位相机矩阵C",
                        "D":"$左对位相机矩阵D",
                        "E":"$左对位相机矩阵E",
                        "F":"$左对位相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算屏幕圆心物理坐标结果",
                        "x_out":"屏幕物理圆心x",
                        "y_out":"屏幕物理圆心y"
                    }
                },
                {
                    "name":"计算屏幕模板物理坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"@屏幕像素模板x",
                        "y_in":"@屏幕像素模板y",
                        "A":"$左对位相机矩阵A",
                        "B":"$左对位相机矩阵B",
                        "C":"$左对位相机矩阵C",
                        "D":"$左对位相机矩阵D",
                        "E":"$左对位相机矩阵E",
                        "F":"$左对位相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算屏幕模板物理坐标结果",
                        "x_out":"屏幕物理模板x",
                        "y_out":"屏幕物理模板y"
                    }
                },
                {
                    "name":"计算后壳圆心物理坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"@circle_X",
                        "y_in":"@circle_Y",
                        "A":"$左对位相机矩阵A",
                        "B":"$左对位相机矩阵B",
                        "C":"$左对位相机矩阵C",
                        "D":"$左对位相机矩阵D",
                        "E":"$左对位相机矩阵E",
                        "F":"$左对位相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算后壳圆心物理坐标结果",
                        "x_out":"后壳物理圆心x",
                        "y_out":"后壳物理圆心y"
                    }
                },
                {
                    "name":"计算后壳模板物理坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"@模板X",
                        "y_in":"@模板Y",
                        "A":"$左对位相机矩阵A",
                        "B":"$左对位相机矩阵B",
                        "C":"$左对位相机矩阵C",
                        "D":"$左对位相机矩阵D",
                        "E":"$左对位相机矩阵E",
                        "F":"$左对位相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算后壳模板物理坐标结果",
                        "x_out":"后壳物理模板x",
                        "y_out":"后壳物理模板y"
                    }
                },
                {
                    "name":"画后壳中心",
                    "type":"draw_ellipse",
                    "in":
                    {
                        "image":"@后壳拍照图片",
                        "x":"@circle_X",
                        "y":"@circle_Y",
                        "a":"100",
                        "b":"100",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"画后壳中心结果",
                        "image":"后壳调试图片1"
                    }
                },
                {
                    "name":"画屏幕中心",
                    "type":"draw_ellipse",
                    "in":
                    {
                        "image":"@后壳调试图片1",
                        "x":"@屏幕像素圆心x",
                        "y":"@屏幕像素圆心y",
                        "a":"40",
                        "b":"40",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"画屏幕中心结果",
                        "image":"后壳调试图片2"
                    }
                },
                {
                    "name":"画后壳直线",
                    "type":"draw_line",
                    "in":
                    {
                        "image":"@后壳调试图片2",
                        "x1":"@circle_X",
                        "y1":"@circle_Y",
                        "x2":"@模板X",
                        "y2":"@模板Y"
                    },
                    "out":
                    {
                        "result":"画后壳直线结果",
                        "image":"后壳调试图片3"
                    }
                },
                {
                    "name":"画屏幕直线",
                    "type":"draw_line",
                    "in":
                    {
                        "image":"@后壳调试图片3",
                        "x1":"@屏幕像素圆心x",
                        "y1":"@屏幕像素圆心y",
                        "x2":"@屏幕像素模板x",
                        "y2":"@屏幕像素模板y"
                    },
                    "out":
                    {
                        "result":"画后壳直线结果",
                        "image":"后壳调试图片4"
                    }
                },
                {
                    "name":"保存对位调试图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳调试图片4",
                        "path":"$左调试文件夹",
                        "name":"align.jpg"
                    },
                    "out":
                    {
                        "result":"保存对位调试图片结果",
                        "full_name":"保存对位调试图片路径"
                    }
                },
                {
                    "name":"推送计算x偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@屏幕物理圆心x\",\"-\",\"@后壳物理圆心x\"]"
                    },
                    "out":
                    {
                        "result":"推送计算x偏移量结果",
                        "combine":"推送计算x偏移量"
                    }
                },
                {
                    "name":"推送计算y偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@屏幕物理圆心y\",\"-\",\"@后壳物理圆心y\"]"
                    },
                    "out":
                    {
                        "result":"推送计算y偏移量结果",
                        "combine":"推送计算y偏移量"
                    }
                },
                {
                    "name":"计算x偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算x偏移量" 
                    },
                    "out":
                    {
                        "result":"计算x偏移量结果",
                        "value":"X偏移量"
                    }
                },
                {
                    "name":"计算y偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算y偏移量" 
                    },
                    "out":
                    {
                        "result":"计算y偏移量结果",
                        "value":"Y偏移量"
                    }
                },
                {
                    "name":"生成屏幕匹配直线",
                    "type":"creat_line",
                    "in":
                    {
                        "x_start":"@屏幕物理圆心x",
                        "y_start":"@屏幕物理圆心y",
                        "x_end":"@屏幕物理模板x",
                        "y_end":"@屏幕物理模板y"
                    },
                    "out":
                    {
                        "result":"生成屏幕匹配直线结果",
                        "line":"屏幕匹配直线"
                    }    
                },
                {
                    "name":"生成后壳匹配直线",
                    "type":"creat_line",
                    "in":
                    {
                        "x_start":"@后壳物理圆心x",
                        "y_start":"@后壳物理圆心y",
                        "x_end":"@后壳物理模板x",
                        "y_end":"@后壳物理模板y"
                    },
                    "out":
                    {
                        "result":"生成后壳匹配直线结果",
                        "line":"后壳匹配直线"
                    }    
                },
                {
                    "name":"计算直线夹角",
                    "type":"calc_line_angle",
                    "in":
                    {
                        "line_1":"@屏幕匹配直线",
                        "line_2":"@后壳匹配直线"
                    },
                    "out":
                    {
                        "result":"计算直线夹角结果",
                        "angle":"直线夹角"
                    }
                },
                {
                    "name":"推送计算X偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@X偏移量\",\"*0.8\"]"
                    },
                    "out":
                    {
                        "result":"推送计算X偏移量结果",
                        "combine":"推送计算X偏移量"
                    }
                },
                {
                    "name":"计算X偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算X偏移量" 
                    },
                    "out":
                    {
                        "result":"计算X偏移量结果",
                        "value":"x偏移量"
                    }
                },
                {
                    "name":"推送计算Y偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@Y偏移量\",\"*0.8+0.1\"]"
                    },
                    "out":
                    {
                        "result":"推送计算Y偏移量结果",
                        "combine":"推送计算Y偏移量"
                    }
                },
                {
                    "name":"计算Y偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算Y偏移量" 
                    },
                    "out":
                    {
                        "result":"计算Y偏移量结果",
                        "value":"y偏移量"
                    }
                },
                {
                    "name":"推送计算角度偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@直线夹角\",\"*(-1)+0\"]"
                    },
                    "out":
                    {
                        "result":"推送计算角度偏移量结果",
                        "combine":"推送计算角度偏移量"
                    }
                },
                {
                    "name":"计算角度偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算角度偏移量" 
                    },
                    "out":
                    {
                        "result":"计算角度偏移量结果",
                        "value":"角度偏移量"
                    }
                },
                {
                    "name":"推送显示偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"次数:\",\"$左对位次数\",\",x偏移量:\",\"@x偏移量\",\",y偏移量:\",\"@y偏移量\",\",角度偏移量:\",\"@角度偏移量\"]"
                    },
                    "out":
                    {
                        "result":"推送显示偏移量结果",
                        "combine":"显示偏移量字符"
                    }
                },
                {
                    "name":"显示偏移量",
                    "type":"log_output",
                    "in":
                    {
                        "input":"@显示偏移量字符",
                        "level":"info"
                    }
                },
                {
                    "name":"推送计算偏移量范围小",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"(\",\"@x偏移量\",\">-0.15)&(\",\"@x偏移量\",\"<0.15)&(\",\"@y偏移量\",\">-0.15)&(\",\"@y偏移量\",\"<0.15)&(\",\"@角度偏移量\",\">-0.5)&(\",\"@角度偏移量\",\"<0.5)\"]"
                    },
                    "out":
                    {
                        "result":"推送计算偏移量范围小结果",
                        "combine":"推送计算偏移量范围小"
                    }
                },
                {
                    "name":"计算偏移量范围小",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算偏移量范围小" 
                    },
                    "out":
                    {
                        "result":"计算偏移量范围结果小",
                        "value":"偏移量范围小"
                    }
                },
                {
                    "name":"计算偏移量范围小整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@偏移量范围小"                
                    },
                    "out":
                    {
                        "result":"计算偏移量范围小整型结果",
                        "integer":"偏移量范围小整型"
                    }
                },
                {
                    "name":"判断偏移量范围小",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"发送对位OK结果",
                        "condition":"@偏移量范围小整型"
                    }
                },
                {
                    "name":"推送计算偏移量范围大",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"(\",\"@x偏移量\",\">-5)&(\",\"@x偏移量\",\"<5)&(\",\"@y偏移量\",\">-5)&(\",\"@y偏移量\",\"<5)&(\",\"@角度偏移量\",\">-5)&(\",\"@角度偏移量\",\"<5)\"]"
                    },
                    "out":
                    {
                        "result":"推送计算偏移量范围大结果",
                        "combine":"推送计算偏移量范围大"
                    }
                },
                {
                    "name":"计算偏移量范围大",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算偏移量范围大" 
                    },
                    "out":
                    {
                        "result":"计算偏移量范围结果大",
                        "value":"偏移量范围大"
                    }
                },
                {
                    "name":"计算偏移量范围大整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@偏移量范围大"                
                    },
                    "out":
                    {
                        "result":"计算偏移量范围大整型结果",
                        "integer":"偏移量范围大整型"
                    }
                },
                {
                    "name":"判断偏移量范围大",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"推送计算对位次数",
                        "condition":"@偏移量范围大整型"
                    }
                },
                {
                    "name":"显示偏移量大",
                    "type":"log_output",
                    "in":
                    {
                        "input":"左对位偏移量超范围",
                        "level":"error"
                    }
                },
                {
                    "name":"偏移量范围大跳转对位失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送对位NG结果"
                    }
                },
                {
                    "name":"推送计算对位次数",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"$左对位次数\",\"+1\"]"
                    },
                    "out":
                    {
                        "result":"推送计算对位次数结果",
                        "combine":"推送计算对位次数"
                    }
                },
                {
                    "name":"计算对位次数",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算对位次数" 
                    },
                    "out":
                    {
                        "result":"计算对位次数结果",
                        "value":"对位次数"
                    }
                },
                {
                    "name":"计算对位次数整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@对位次数"                
                    },
                    "out":
                    {
                        "result":"计算对位次数整型结果",
                        "integer":"对位次数整型"
                    }
                },
                {
                    "name":"保存对位次数",
                    "type":"set_global_integer",
                    "in":
                    {
                        "input":"@对位次数整型",
                        "name":"左对位次数"
                    },
                    "out":
                    {
                        "result":"保存对位次数结果"
                    }
                },
                {
                    "name":"推送判断对位次数",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@对位次数\",\"<8\"]"
                    },
                    "out":
                    {
                        "result":"推送判断对位次数结果",
                        "combine":"推送判断对位次数"
                    }
                },
                {
                    "name":"计算判断对位次数",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送判断对位次数" 
                    },
                    "out":
                    {
                        "result":"计算判断对位次数结果",
                        "value":"判断对位次数"
                    }
                },
                {
                    "name":"判断对位次数整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@判断对位次数"                
                    },
                    "out":
                    {
                        "result":"计算判断对位次数整型结果",
                        "integer":"判断对位次数整型"
                    }
                },
                {
                    "name":"判断对位次数大",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"发送X偏移量",
                        "condition":"@判断对位次数整型"
                    }
                },
                {
                    "name":"显示对位次数大",
                    "type":"log_output",
                    "in":
                    {
                        "input":"左对位次数超范围",
                        "level":"error"
                    }
                },
                {
                    "name":"对位次数大跳转对位失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送对位NG结果"
                    }
                },
                {
                    "name":"发送X偏移量",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"204",
                        "type":"float",
                        "content":"@x偏移量"
                    },
                    "out":
                    {
                        "result":"发送X偏移量结果"
                    }
                },
                {
                    "name":"发送Y偏移量",
                    "type":"send_s7_client",
                    "in":
                    {
                        "wait":"@发送X偏移量结果",
                        "client":"左PLC",
                        "db":"520",
                        "offset":"208",
                        "type":"float",
                        "content":"@y偏移量"
                    },
                    "out":
                    {
                        "result":"发送Y偏移量结果"
                    }
                },
                {
                    "name":"发送角度偏移量",
                    "type":"send_s7_client",
                    "in":
                    {
                        "wait":"@发送Y偏移量结果",
                        "client":"左PLC",
                        "db":"520",
                        "offset":"212",
                        "type":"float",
                        "content":"@角度偏移量"
                    },
                    "out":
                    {
                        "result":"发送角度偏移量结果"
                    }
                },
                {
                    "name":"延时1",
                    "type":"sleep",
                    "in":
                    {
                        "wait":"@发送角度偏移量结果",
                        "time":"50"
                    },
                    "out":
                    {
                        "result":"延时1结果"
                    }
                },
                {
                    "name":"跳转继续对位",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送继续对位结果"
                    }
                },
                {
                    "name":"发送对位OK结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"174",
                        "type":"short",
                        "content":"1"
                    }
                },
                {
                    "name":"结束对位OK",
                    "type":"end"
                },
                {
                    "name":"发送对位NG结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"174",
                        "type":"short",
                        "content":"2"
                    }
                },
                {
                    "name":"结束对位NG",
                    "type":"end"
                },
                {
                    "name":"发送继续对位结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "wait":"@延时1结果",
                        "client":"左PLC",
                        "db":"520",
                        "offset":"174",
                        "type":"short",
                        "content":"3"
                    }
                },
                {
                    "name":"结束继续对位",
                    "type":"end"
                },
                {
                    "name":"生成xml名称2",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"back_\",\"$左对位次数\",\".xml\"]"
                    },
                    "out":
                    {
                        "result":"生成xml名称2结果",
                        "combine":"生成xml名称2"
                    }
                },
                {
                    "name":"保存XML2",
                    "type":"save_xml",
                    "in":
                    {
                        "path":"$左图片文件夹",
                        "name":"@生成xml名称2",
                        "product":"mate3.0",
                        "sn":"$左SN码",
                        "station":"Align",
                        "mac":"NULL",
                        "detection_type":"back",
                        "result":"1",
                        "folder":"$左图片文件夹",
                        "image_name":"@TRIGGER_STRING",
                        "width":"@获取后壳图片信息宽",
                        "height":"@获取后壳图片信息高",
                        "depth":"@获取后壳图片信息通道",
                        "name_b":"[\"Ellipse\",\"Match\"]",
                        "label":"[\"1\",\"1\"]",
                        "standard":"[\"1\",\"1\"]",
                        "xmin":"[\"@椭圆矩形xmin\",\"@模板矩形还原xmin\"]",
                        "ymin":"[\"@椭圆矩形ymin\",\"@模板矩形还原ymin\"]",
                        "xmax":"[\"@椭圆矩形xmax\",\"@模板矩形还原xmax\"]",
                        "ymax":"[\"@椭圆矩形ymax\",\"@模板矩形还原ymax\"]"
                    },
                    "out":
                    {
                        "result":"保存XML结果",
                        "full_name":"保存XML全称"
                    }
                },
                {
                    "name":"生成筛选圆图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"back_\",\"$左对位次数\",\"_ellipse.jpg\"]"
                    },
                    "out":
                    {
                        "result":"生成筛选圆图片名称结果",
                        "combine":"筛选圆图片名称"
                    }
                },
                {
                    "name":"生成模板匹配图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"back_\",\"$左对位次数\",\"_temp.jpg\"]"
                    },
                    "out":
                    {
                        "result":"生成模板匹配图片名称结果",
                        "combine":"模板匹配图片名称"
                    }
                },
                {
                    "name":"保存筛选圆图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳找圆图片",
                        "path":"$左图片文件夹",
                        "name":"@筛选圆图片名称"
                    },
                    "out":
                    {
                        "result":"保存筛选圆图片结果",
                        "full_name":"保存筛选圆图片路径"
                    }
                },
                {
                    "name":"保存模板匹配图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳模板匹配图片",
                        "path":"$左图片文件夹",
                        "name":"@模板匹配图片名称"
                    },
                    "out":
                    {
                        "result":"保存模板匹配图片结果",
                        "full_name":"保存模板匹配图片路径"
                    }
                },
                {
                    "name":"推送计算对位次数",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"$左对位次数\",\"+1\"]"
                    },
                    "out":
                    {
                        "result":"推送计算对位次数结果",
                        "combine":"推送计算对位次数"
                    }
                },
                {
                    "name":"计算对位次数",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算对位次数" 
                    },
                    "out":
                    {
                        "result":"计算对位次数结果",
                        "value":"对位次数"
                    }
                },
                {
                    "name":"计算对位次数整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@对位次数"                
                    },
                    "out":
                    {
                        "result":"计算对位次数整型结果",
                        "integer":"对位次数整型"
                    }
                },
                {
                    "name":"保存对位次数",
                    "type":"set_global_integer",
                    "in":
                    {
                        "input":"@对位次数整型",
                        "name":"左对位次数"
                    },
                    "out":
                    {
                        "result":"保存对位次数结果"
                    }
                },
                {
                    "name":"结束离线流程",
                    "type":"end"
                }
            ] 
        },
        //右对位
        {
            "name":"右对位流程",
            "command":
            [
                {
                    "name":"读取模板图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"temp/back_temp.jpg"
                    },
                    "out":
                    {
                        "image":"后壳模板图片",
                        "result":"后壳模板结果"
                    }
                },
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"."
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取字符串长度",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取字符串长度结果",
                        "string":"获取字符串长度"
                    }
                },
                {
                    "name":"判断获取字符串长度结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"读取后壳图片",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"后壳拍照",
                    "type":"get_camera_frame",
                    "in":
                    {
                        "camera":"右拍后壳相机",
                        "exposure":"300000",
                        "gain":"8.0"
                    },
                    "out":
                    {
                        "result":"后壳拍照结果",
                        "image":"后壳拍照图片"
                    }
                },
                {
                    "name":"跳转获取时间",
                    "type":"jump",
                    "in":
                    {
                        "tag":"获取当前时间"
                    }
                },
                {
                    "name":"读取后壳图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "image":"后壳拍照图片",
                        "result":"后壳拍照结果"
                    }
                },
                {
                    "name":"获取当前时间",
                    "type":"get_date_time",
                    "out":
                    {
                        "result":"获取当前时间结果",
                        "date_time":"当前时间"  
                    }
                    
                },
                {
                    "name":"生成图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"back_\",\"$右对位次数\",\"_\",\"@当前时间\",\"_\",\"$右SN码\",\".png\"]"
                    },
                    "out":
                    {
                        "result":"生成后壳名称结果",
                        "combine":"图片名称"
                    }
                },
                {
                    "name":"生成xml名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"back_\",\"$右对位次数\",\"_\",\"@当前时间\",\"_\",\"$右SN码\",\".xml\"]"
                    },
                    "out":
                    {
                        "result":"生成xml名称结果",
                        "combine":"生成xml名称"
                    }
                },
                {
                    "name":"获取后壳图片信息",
                    "type":"get_image_info",
                    "in":
                    {
                        "image":"@后壳拍照图片"
                    },
                    "out":
                    {
                        "result":"获取后壳图片信息结果",
                        "width":"获取后壳图片信息宽",
                        "height":"获取后壳图片信息高",
                        "channel":"获取后壳图片信息通道"
                    }
                },
                {
                    "name":"离线模式不保存图片",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"判断拍后壳结果",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"保存后壳图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳拍照图片",
                        "path":"$右图片文件夹",
                        "name":"@图片名称"
                    },
                    "out":
                    {
                        "result":"保存图片结果",
                        "full_name":"图片路径"
                    }
                },
                {
                    "name":"判断拍后壳结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"跳转算法流程",
                        "condition":"@后壳拍照结果"
                    }
                },
                {
                    "name":"跳转拍后壳失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示拍后壳失败"
                    }
                },
                {
                    "name":"跳转算法流程",
                    "type":"jump",
                    "in":
                    {
                        "tag":"后壳向下采样"
                    }
                },
                {
                    "name":"显示拍后壳失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右拍后壳失败",
                        "level":"error"
                    }
                },
                {
                    "name":"拍后壳失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送对位NG结果"
                    }
                },
                {
                    "name":"后壳向下采样",
                    "type":"pyramid",
                    "in":
                    {
                        "image":"@后壳拍照图片",
                        "type":"down",
                        "count":"2"
                    },
                    "out":
                    {
                        "result":"后壳向下采样结果",
                        "image_result":"后壳向下采样图片"
                    }
                },
                {
                    "name":"后壳模板向下采样",
                    "type":"pyramid",
                    "in":
                    {
                        "image":"@后壳模板图片",
                        "type":"down",
                        "count":"2"
                    },
                    "out":
                    {
                        "result":"后壳模板向下采样结果",
                        "image_result":"后壳模板向下采样图片"
                    }
                },
                {
                    "name":"后壳模板匹配",
                    "type":"calc_sharp_match_image",
                    "in":
                    {
                        "image":"@后壳向下采样图片",
                        "template":"@后壳模板向下采样图片",
                        "num_features":"100",
                        "pyramid_levels_h":"8",
                        "pyramid_levels_l":"4",
                        "angle_begin":"-5",
                        "angle_end":"5",
                        "angle_step":"0.1",
                        "scale_begin":"0.9",
                        "scale_end":"1.1",
                        "scale_step":"0.1",
                        "threshold":"60"
                    },
                    "out":
                    {
                        "result":"后壳模板匹配结果",
                        "debug_img":"后壳模板匹配图片",
                        "score":"后壳模板匹配分数",
                        "scale":"后壳模板匹配缩放",
                        "angle":"后壳模板匹配角度",
                        "x":"后壳模板匹配X",
                        "y":"后壳模板匹配Y",
                        "xmin":"后壳模板矩形xmin",
                        "ymin":"后壳模板矩形ymin",
                        "xmax":"后壳模板矩形xmax",
                        "ymax":"后壳模板矩形ymax"
                    }
                },
                {
                    "name":"后壳找圆图像增强",
                    "type":"multiply",
                    "in":
                    {
                        "image_in":"@后壳拍照图片",
                        "mul":"3"
                    },
                    "out":
                    {
                        "result":"后壳找圆图像增强结果",
                        "image_out":"后壳向下采样增强图片"
                    }
                },
                {
                    "name":"后壳找圆",
                    "type":"find_circle",
                    "in":
                    {
                        "image":"@后壳向下采样增强图片",
                        "center_x":"2600",
                        "center_y":"2700",
                        "radius_min":"1500",
                        "radius_max":"2200",
                        "count":"200",
                        "width":"100",
                        "ksize":"17",
                        "type":"1",
                        "find_type":"1",
                        "iterations":"50000",
                        "strength":"100",
                        "thresh":"0.3",
                        "sigma":"10",
                        "end":"0.9",
                        "minimum":"0.3",
                        "use_fit":"1"
                    },
                    "out":
                    {
                        "result":"后壳找圆结果",
                        "x":"circle_X",
                        "y":"circle_Y",
                        "radius":"circle_R",
                        "debug_img":"后壳找圆图片"
                    }
                },
                {
                    "name":"判断后壳找圆1结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"判断模板匹配结果",
                        "condition":"@后壳找圆结果"
                    }
                },
                {
                    "name":"显示找圆失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右后壳找圆失败",
                        "level":"error"
                    }
                },
                {
                    "name":"找圆失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送对位NG结果"
                    }
                },
                {
                    "name":"判断模板匹配结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"保存筛选圆图片",
                        "condition":"@后壳模板匹配结果"
                    }
                },
                {
                    "name":"显示匹配失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右后壳匹配失败",
                        "level":"error"
                    }
                },
                {
                    "name":"匹配失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送对位NG结果"
                    }
                },
                {
                    "name":"保存筛选圆图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳找圆图片",
                        "path":"$右调试文件夹",
                        "name":"back_ellipse.jpg"
                    },
                    "out":
                    {
                        "result":"保存筛选圆图片结果",
                        "full_name":"保存筛选圆图片路径"
                    }
                },
                {
                    "name":"保存模板匹配图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳模板匹配图片",
                        "path":"$右调试文件夹",
                        "name":"back_match.jpg"
                    },
                    "out":
                    {
                        "result":"保存模板匹配图片结果",
                        "full_name":"保存模板匹配图片路径"
                    }
                },
                {
                    "name":"推送计算圆直径公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@circle_R\",\"*2\"]"
                    },
                    "out":
                    {
                        "result":"推送计算圆直径公式结果",
                        "combine":"推送计算圆直径"
                    }
                },
                {
                    "name":"计算圆直径",
                    "type":"calc_formula_v",
                    "in":
                    {
                        "formula":"[\"@circle_R\",\"*2\"]"
                    },
                    "out":
                    {
                        "result":"计算圆直径结果",
                        "value":"圆直径"
                    }
                },
                {
                    "name":"计算椭圆矩形",
                    "type":"bounding_box",
                    "in":
                    {
                        "x":"@circle_X",
                        "y":"@circle_Y",
                        "w":"@圆直径",
                        "h":"@圆直径",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"计算椭圆矩形结果",
                        "xmin":"椭圆矩形xmin",
                        "ymin":"椭圆矩形ymin",
                        "xmax":"椭圆矩形xmax",
                        "ymax":"椭圆矩形ymax"
                    }
                },
                {
                    "name":"模板矩形还原",
                    "type":"bounding_box_scale",
                    "in":
                    {
                        "xmin_in":"@后壳模板矩形xmin",
                        "ymin_in":"@后壳模板矩形ymin",
                        "xmax_in":"@后壳模板矩形xmax",
                        "ymax_in":"@后壳模板矩形ymax",
                        "scale":"4"
                    },
                    "out":
                    {
                        "result":"模板矩形还原",
                        "xmin_out":"模板矩形还原xmin",
                        "ymin_out":"模板矩形还原ymin",
                        "xmax_out":"模板矩形还原xmax",
                        "ymax_out":"模板矩形还原ymax"
                    }
                },
                {
                    "name":"离线模式XML名字有变化",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"生成xml名称2",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"保存XML",
                    "type":"save_xml",
                    "in":
                    {
                        "path":"$右图片文件夹",
                        "name":"@生成xml名称",
                        "product":"mate3.0",
                        "sn":"$右SN码",
                        "station":"Align",
                        "mac":"NULL",
                        "detection_type":"back",
                        "result":"1",
                        "folder":"$右图片文件夹",
                        "image_name":"@图片名称",
                        "width":"@获取后壳图片信息宽",
                        "height":"@获取后壳图片信息高",
                        "depth":"@获取后壳图片信息通道",
                        "name_b":"[\"Ellipse\",\"Match\"]",
                        "label":"[\"1\",\"1\"]",
                        "standard":"[\"1\",\"1\"]",
                        "xmin":"[\"@椭圆矩形xmin\",\"@模板矩形还原xmin\"]",
                        "ymin":"[\"@椭圆矩形ymin\",\"@模板矩形还原ymin\"]",
                        "xmax":"[\"@椭圆矩形xmax\",\"@模板矩形还原xmax\"]",
                        "ymax":"[\"@椭圆矩形ymax\",\"@模板矩形还原ymax\"]"
                    },
                    "out":
                    {
                        "result":"保存XML结果",
                        "full_name":"保存XML全称"
                    }
                },
                {
                    "name":"推送模板x公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@后壳模板匹配X\",\"*4\"]"
                    },
                    "out":
                    {
                        "result":"推送模板x公式结果",
                        "combine":"推送模板x"
                    }
                },
                {
                    "name":"计算模板x",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送模板x" 
                    },
                    "out":
                    {
                        "result":"计算模板x结果",
                        "value":"模板X"
                    }
                },
                {
                    "name":"推送模板y公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@后壳模板匹配Y\",\"*4\"]"
                    },
                    "out":
                    {
                        "result":"推送模板y公式结果",
                        "combine":"推送模板y"
                    }
                },
                {
                    "name":"计算模板y",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送模板y" 
                    },
                    "out":
                    {
                        "result":"计算模板y结果",
                        "value":"模板Y"
                    }
                },
                {
                    "name":"计算屏幕圆心到后壳像素坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"$右屏幕X",
                        "y_in":"$右屏幕Y",
                        "A":"$右屏幕相机矩阵A",
                        "B":"$右屏幕相机矩阵B",
                        "C":"$右屏幕相机矩阵C",
                        "D":"$右屏幕相机矩阵D",
                        "E":"$右屏幕相机矩阵E",
                        "F":"$右屏幕相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算屏幕圆心到后壳像素坐标结果",
                        "x_out":"屏幕像素圆心x",
                        "y_out":"屏幕像素圆心y"
                    }
                },
                {
                    "name":"计算屏幕模板到后壳像素坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"$右屏幕模板X",
                        "y_in":"$右屏幕模板Y",
                        "A":"$右屏幕相机矩阵A",
                        "B":"$右屏幕相机矩阵B",
                        "C":"$右屏幕相机矩阵C",
                        "D":"$右屏幕相机矩阵D",
                        "E":"$右屏幕相机矩阵E",
                        "F":"$右屏幕相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算屏幕模板到后壳像素坐标结果",
                        "x_out":"屏幕像素模板x",
                        "y_out":"屏幕像素模板y"
                    }
                },
                {
                    "name":"计算屏幕圆心物理坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"@屏幕像素圆心x",
                        "y_in":"@屏幕像素圆心y",
                        "A":"$右对位相机矩阵A",
                        "B":"$右对位相机矩阵B",
                        "C":"$右对位相机矩阵C",
                        "D":"$右对位相机矩阵D",
                        "E":"$右对位相机矩阵E",
                        "F":"$右对位相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算屏幕圆心物理坐标结果",
                        "x_out":"屏幕物理圆心x",
                        "y_out":"屏幕物理圆心y"
                    }
                },
                {
                    "name":"计算屏幕模板物理坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"@屏幕像素模板x",
                        "y_in":"@屏幕像素模板y",
                        "A":"$右对位相机矩阵A",
                        "B":"$右对位相机矩阵B",
                        "C":"$右对位相机矩阵C",
                        "D":"$右对位相机矩阵D",
                        "E":"$右对位相机矩阵E",
                        "F":"$右对位相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算屏幕模板物理坐标结果",
                        "x_out":"屏幕物理模板x",
                        "y_out":"屏幕物理模板y"
                    }
                },
                {
                    "name":"计算后壳圆心物理坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"@circle_X",
                        "y_in":"@circle_Y",
                        "A":"$右对位相机矩阵A",
                        "B":"$右对位相机矩阵B",
                        "C":"$右对位相机矩阵C",
                        "D":"$右对位相机矩阵D",
                        "E":"$右对位相机矩阵E",
                        "F":"$右对位相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算后壳圆心物理坐标结果",
                        "x_out":"后壳物理圆心x",
                        "y_out":"后壳物理圆心y"
                    }
                },
                {
                    "name":"计算后壳模板物理坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"@模板X",
                        "y_in":"@模板Y",
                        "A":"$右对位相机矩阵A",
                        "B":"$右对位相机矩阵B",
                        "C":"$右对位相机矩阵C",
                        "D":"$右对位相机矩阵D",
                        "E":"$右对位相机矩阵E",
                        "F":"$右对位相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算后壳模板物理坐标结果",
                        "x_out":"后壳物理模板x",
                        "y_out":"后壳物理模板y"
                    }
                },
                {
                    "name":"画后壳中心",
                    "type":"draw_ellipse",
                    "in":
                    {
                        "image":"@后壳拍照图片",
                        "x":"@circle_X",
                        "y":"@circle_Y",
                        "a":"100",
                        "b":"100",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"画后壳中心结果",
                        "image":"后壳调试图片1"
                    }
                },
                {
                    "name":"画屏幕中心",
                    "type":"draw_ellipse",
                    "in":
                    {
                        "image":"@后壳调试图片1",
                        "x":"@屏幕像素圆心x",
                        "y":"@屏幕像素圆心y",
                        "a":"40",
                        "b":"40",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"画屏幕中心结果",
                        "image":"后壳调试图片2"
                    }
                },
                {
                    "name":"画后壳直线",
                    "type":"draw_line",
                    "in":
                    {
                        "image":"@后壳调试图片2",
                        "x1":"@circle_X",
                        "y1":"@circle_X",
                        "x2":"@模板X",
                        "y2":"@模板Y"
                    },
                    "out":
                    {
                        "result":"画后壳直线结果",
                        "image":"后壳调试图片3"
                    }
                },
                {
                    "name":"画屏幕直线",
                    "type":"draw_line",
                    "in":
                    {
                        "image":"@后壳调试图片3",
                        "x1":"@屏幕像素圆心x",
                        "y1":"@屏幕像素圆心y",
                        "x2":"@屏幕像素模板x",
                        "y2":"@屏幕像素模板y"
                    },
                    "out":
                    {
                        "result":"画后壳直线结果",
                        "image":"后壳调试图片4"
                    }
                },
                {
                    "name":"保存对位调试图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳调试图片4",
                        "path":"$右调试文件夹",
                        "name":"align.jpg"
                    },
                    "out":
                    {
                        "result":"保存对位调试图片结果",
                        "full_name":"保存对位调试图片路径"
                    }
                },
                {
                    "name":"推送计算x偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@屏幕物理圆心x\",\"-\",\"@后壳物理圆心x\"]"
                    },
                    "out":
                    {
                        "result":"推送计算x偏移量结果",
                        "combine":"推送计算x偏移量"
                    }
                },
                {
                    "name":"推送计算y偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@屏幕物理圆心y\",\"-\",\"@后壳物理圆心y\"]"
                    },
                    "out":
                    {
                        "result":"推送计算y偏移量结果",
                        "combine":"推送计算y偏移量"
                    }
                },
                {
                    "name":"计算x偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算x偏移量" 
                    },
                    "out":
                    {
                        "result":"计算x偏移量结果",
                        "value":"X偏移量"
                    }
                },
                {
                    "name":"计算y偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算y偏移量" 
                    },
                    "out":
                    {
                        "result":"计算y偏移量结果",
                        "value":"Y偏移量"
                    }
                },
                {
                    "name":"生成屏幕匹配直线",
                    "type":"creat_line",
                    "in":
                    {
                        "x_start":"@屏幕物理圆心x",
                        "y_start":"@屏幕物理圆心y",
                        "x_end":"@屏幕物理模板x",
                        "y_end":"@屏幕物理模板y"
                    },
                    "out":
                    {
                        "result":"生成屏幕匹配直线结果",
                        "line":"屏幕匹配直线"
                    }    
                },
                {
                    "name":"生成后壳匹配直线",
                    "type":"creat_line",
                    "in":
                    {
                        "x_start":"@后壳物理圆心x",
                        "y_start":"@后壳物理圆心y",
                        "x_end":"@后壳物理模板x",
                        "y_end":"@后壳物理模板y"
                    },
                    "out":
                    {
                        "result":"生成后壳匹配直线结果",
                        "line":"后壳匹配直线"
                    }    
                },
                {
                    "name":"计算直线夹角",
                    "type":"calc_line_angle",
                    "in":
                    {
                        "line_1":"@屏幕匹配直线",
                        "line_2":"@后壳匹配直线"
                    },
                    "out":
                    {
                        "result":"计算直线夹角结果",
                        "angle":"直线夹角"
                    }
                },
                {
                    "name":"推送计算X偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@X偏移量\",\"*0.8+0.1\"]"
                    },
                    "out":
                    {
                        "result":"推送计算X偏移量结果",
                        "combine":"推送计算X偏移量"
                    }
                },
                {
                    "name":"计算X偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算X偏移量" 
                    },
                    "out":
                    {
                        "result":"计算X偏移量结果",
                        "value":"x偏移量"
                    }
                },
                {
                    "name":"推送计算Y偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@Y偏移量\",\"*0.8\"]"
                    },
                    "out":
                    {
                        "result":"推送计算Y偏移量结果",
                        "combine":"推送计算Y偏移量"
                    }
                },
                {
                    "name":"计算Y偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算Y偏移量" 
                    },
                    "out":
                    {
                        "result":"计算Y偏移量结果",
                        "value":"y偏移量"
                    }
                },
                {
                    "name":"推送计算角度偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@直线夹角\",\"+0\"]"
                    },
                    "out":
                    {
                        "result":"推送计算角度偏移量结果",
                        "combine":"推送计算角度偏移量"
                    }
                },
                {
                    "name":"计算角度偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算角度偏移量" 
                    },
                    "out":
                    {
                        "result":"计算角度偏移量结果",
                        "value":"角度偏移量"
                    }
                },
                {
                    "name":"推送显示偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"次数:\",\"$右对位次数\",\",x偏移量:\",\"@x偏移量\",\",y偏移量:\",\"@y偏移量\",\",角度偏移量:\",\"@角度偏移量\"]"
                    },
                    "out":
                    {
                        "result":"推送显示偏移量结果",
                        "combine":"显示偏移量字符"
                    }
                },
                {
                    "name":"显示偏移量",
                    "type":"log_output",
                    "in":
                    {
                        "input":"@显示偏移量字符",
                        "level":"info"
                    }
                },
                {
                    "name":"推送计算偏移量范围小",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"(\",\"@x偏移量\",\">-0.15)&(\",\"@x偏移量\",\"<0.15)&(\",\"@y偏移量\",\">-0.15)&(\",\"@y偏移量\",\"<0.15)&(\",\"@角度偏移量\",\">-0.5)&(\",\"@角度偏移量\",\"<0.5)\"]"
                    },
                    "out":
                    {
                        "result":"推送计算偏移量范围小结果",
                        "combine":"推送计算偏移量范围小"
                    }
                },
                {
                    "name":"计算偏移量范围小",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算偏移量范围小" 
                    },
                    "out":
                    {
                        "result":"计算偏移量范围结果小",
                        "value":"偏移量范围小"
                    }
                },
                {
                    "name":"计算偏移量范围小整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@偏移量范围小"                
                    },
                    "out":
                    {
                        "result":"计算偏移量范围小整型结果",
                        "integer":"偏移量范围小整型"
                    }
                },
                {
                    "name":"判断偏移量范围小",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"发送对位OK结果",
                        "condition":"@偏移量范围小整型"
                    }
                },
                {
                    "name":"推送计算偏移量范围大",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"(\",\"@x偏移量\",\">-5)&(\",\"@x偏移量\",\"<5)&(\",\"@y偏移量\",\">-5)&(\",\"@y偏移量\",\"<5)&(\",\"@角度偏移量\",\">-5)&(\",\"@角度偏移量\",\"<5)\"]"
                    },
                    "out":
                    {
                        "result":"推送计算偏移量范围大结果",
                        "combine":"推送计算偏移量范围大"
                    }
                },
                {
                    "name":"计算偏移量范围大",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算偏移量范围大" 
                    },
                    "out":
                    {
                        "result":"计算偏移量范围结果大",
                        "value":"偏移量范围大"
                    }
                },
                {
                    "name":"计算偏移量范围大整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@偏移量范围大"                
                    },
                    "out":
                    {
                        "result":"计算偏移量范围大整型结果",
                        "integer":"偏移量范围大整型"
                    }
                },
                {
                    "name":"判断偏移量范围大",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"推送计算对位次数",
                        "condition":"@偏移量范围大整型"
                    }
                },
                {
                    "name":"显示偏移量大",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右对位偏移量超范围",
                        "level":"error"
                    }
                },
                {
                    "name":"偏移量范围大跳转对位失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送对位NG结果"
                    }
                },
                {
                    "name":"推送计算对位次数",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"$右对位次数\",\"+1\"]"
                    },
                    "out":
                    {
                        "result":"推送计算对位次数结果",
                        "combine":"推送计算对位次数"
                    }
                },
                {
                    "name":"计算对位次数",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算对位次数" 
                    },
                    "out":
                    {
                        "result":"计算对位次数结果",
                        "value":"对位次数"
                    }
                },
                {
                    "name":"计算对位次数整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@对位次数"                
                    },
                    "out":
                    {
                        "result":"计算对位次数整型结果",
                        "integer":"对位次数整型"
                    }
                },
                {
                    "name":"保存对位次数",
                    "type":"set_global_integer",
                    "in":
                    {
                        "input":"@对位次数整型",
                        "name":"右对位次数"
                    },
                    "out":
                    {
                        "result":"保存对位次数结果"
                    }
                },
                {
                    "name":"推送判断对位次数",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@对位次数\",\"<8\"]"
                    },
                    "out":
                    {
                        "result":"推送判断对位次数结果",
                        "combine":"推送判断对位次数"
                    }
                },
                {
                    "name":"计算判断对位次数",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送判断对位次数" 
                    },
                    "out":
                    {
                        "result":"计算判断对位次数结果",
                        "value":"判断对位次数"
                    }
                },
                {
                    "name":"判断对位次数整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@判断对位次数"                
                    },
                    "out":
                    {
                        "result":"计算判断对位次数整型结果",
                        "integer":"判断对位次数整型"
                    }
                },
                {
                    "name":"判断对位次数大",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"发送X偏移量",
                        "condition":"@判断对位次数整型"
                    }
                },
                {
                    "name":"显示对位次数大",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右对位次数超范围",
                        "level":"error"
                    }
                },
                {
                    "name":"对位次数大跳转对位失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送对位NG结果"
                    }
                },
                {
                    "name":"发送X偏移量",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"204",
                        "type":"float",
                        "content":"@x偏移量"
                    },
                    "out":
                    {
                        "result":"发送X偏移量结果"
                    }
                },
                {
                    "name":"发送Y偏移量",
                    "type":"send_s7_client",
                    "in":
                    {
                        "wait":"@发送X偏移量结果",
                        "client":"右PLC",
                        "db":"520",
                        "offset":"208",
                        "type":"float",
                        "content":"@y偏移量"
                    },
                    "out":
                    {
                        "result":"发送Y偏移量结果"
                    }
                },
                {
                    "name":"发送角度偏移量",
                    "type":"send_s7_client",
                    "in":
                    {
                        "wait":"@发送Y偏移量结果",
                        "client":"右PLC",
                        "db":"520",
                        "offset":"212",
                        "type":"float",
                        "content":"@角度偏移量"
                    },
                    "out":
                    {
                        "result":"发送角度偏移量结果"
                    }
                },
                {
                    "name":"延时1",
                    "type":"sleep",
                    "in":
                    {
                        "wait":"@发送角度偏移量结果",
                        "time":"50"
                    },
                    "out":
                    {
                        "result":"延时1结果"
                    }
                },
                {
                    "name":"跳转继续对位",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送继续对位结果"
                    }
                },
                {
                    "name":"发送对位OK结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"174",
                        "type":"short",
                        "content":"1"
                    }
                },
                {
                    "name":"结束对位OK",
                    "type":"end"
                },
                {
                    "name":"发送对位NG结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"174",
                        "type":"short",
                        "content":"2"
                    }
                },
                {
                    "name":"结束对位NG",
                    "type":"end"
                },
                {
                    "name":"发送继续对位结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "wait":"@延时1结果",
                        "client":"右PLC",
                        "db":"520",
                        "offset":"174",
                        "type":"short",
                        "content":"3"
                    }
                },
                {
                    "name":"结束继续对位",
                    "type":"end"
                },
                {
                    "name":"生成xml名称2",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"back_\",\"$右对位次数\",\".xml\"]"
                    },
                    "out":
                    {
                        "result":"生成xml名称2结果",
                        "combine":"生成xml名称2"
                    }
                },
                {
                    "name":"保存XML2",
                    "type":"save_xml",
                    "in":
                    {
                        "path":"$右图片文件夹",
                        "name":"@生成xml名称2",
                        "product":"mate3.0",
                        "sn":"$右SN码",
                        "station":"Align",
                        "mac":"NULL",
                        "detection_type":"back",
                        "result":"1",
                        "folder":"$右图片文件夹",
                        "image_name":"@TRIGGER_STRING",
                        "width":"@获取后壳图片信息宽",
                        "height":"@获取后壳图片信息高",
                        "depth":"@获取后壳图片信息通道",
                        "name_b":"[\"Ellipse\",\"Match\"]",
                        "label":"[\"1\",\"1\"]",
                        "standard":"[\"1\",\"1\"]",
                        "xmin":"[\"@椭圆矩形xmin\",\"@模板矩形还原xmin\"]",
                        "ymin":"[\"@椭圆矩形ymin\",\"@模板矩形还原ymin\"]",
                        "xmax":"[\"@椭圆矩形xmax\",\"@模板矩形还原xmax\"]",
                        "ymax":"[\"@椭圆矩形ymax\",\"@模板矩形还原ymax\"]"
                    },
                    "out":
                    {
                        "result":"保存XML结果",
                        "full_name":"保存XML全称"
                    }
                },
                {
                    "name":"生成筛选圆图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"back_\",\"$右对位次数\",\"_ellipse.jpg\"]"
                    },
                    "out":
                    {
                        "result":"生成筛选圆图片名称结果",
                        "combine":"筛选圆图片名称"
                    }
                },
                {
                    "name":"生成模板匹配图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"back_\",\"$右对位次数\",\"_temp.jpg\"]"
                    },
                    "out":
                    {
                        "result":"生成模板匹配图片名称结果",
                        "combine":"模板匹配图片名称"
                    }
                },
                {
                    "name":"保存筛选圆图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳找圆图片",
                        "path":"$右图片文件夹",
                        "name":"@筛选圆图片名称"
                    },
                    "out":
                    {
                        "result":"保存筛选圆图片结果",
                        "full_name":"保存筛选圆图片路径"
                    }
                },
                {
                    "name":"保存模板匹配图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳模板匹配图片",
                        "path":"$右图片文件夹",
                        "name":"@模板匹配图片名称"
                    },
                    "out":
                    {
                        "result":"保存模板匹配图片结果",
                        "full_name":"保存模板匹配图片路径"
                    }
                },
                {
                    "name":"推送计算对位次数",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"$右对位次数\",\"+1\"]"
                    },
                    "out":
                    {
                        "result":"推送计算对位次数结果",
                        "combine":"推送计算对位次数"
                    }
                },
                {
                    "name":"计算对位次数",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算对位次数" 
                    },
                    "out":
                    {
                        "result":"计算对位次数结果",
                        "value":"对位次数"
                    }
                },
                {
                    "name":"计算对位次数整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@对位次数"                
                    },
                    "out":
                    {
                        "result":"计算对位次数整型结果",
                        "integer":"对位次数整型"
                    }
                },
                {
                    "name":"保存对位次数",
                    "type":"set_global_integer",
                    "in":
                    {
                        "input":"@对位次数整型",
                        "name":"右对位次数"
                    },
                    "out":
                    {
                        "result":"保存对位次数结果"
                    }
                },
                {
                    "name":"结束离线测试",
                    "type":"end"
                }
            ] 
        },
        //左对位盖板
        {
            "name":"左对位盖板流程",
            "command":
            [
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"."
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取字符串长度",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取字符串长度结果",
                        "string":"获取字符串长度"
                    }
                },
                {
                    "name":"判断获取字符串长度结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"读取后壳图片",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"后壳拍照",
                    "type":"get_camera_frame",
                    "in":
                    {
                        "camera":"左拍后壳相机",
                        "exposure":"300000",
                        "gain":"8.0"
                    },
                    "out":
                    {
                        "result":"后壳拍照结果",
                        "image":"后壳拍照图片"
                    }
                },
                {
                    "name":"跳转获取时间",
                    "type":"jump",
                    "in":
                    {
                        "tag":"获取当前时间"
                    }
                },
                {
                    "name":"读取后壳图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "image":"后壳拍照图片",
                        "result":"后壳拍照结果"
                    }
                },
                {
                    "name":"获取当前时间",
                    "type":"get_date_time",
                    "out":
                    {
                        "result":"获取当前时间结果",
                        "date_time":"当前时间"  
                    }
                    
                },
                {
                    "name":"生成图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"back_\",\"$左对位次数\",\"_\",\"@当前时间\",\"_\",\"$左SN码\",\".png\"]"
                    },
                    "out":
                    {
                        "result":"生成后壳名称结果",
                        "combine":"图片名称"
                    }
                },
                {
                    "name":"生成xml名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"back_\",\"$左对位次数\",\"_\",\"@当前时间\",\"_\",\"$左SN码\",\".xml\"]"
                    },
                    "out":
                    {
                        "result":"生成xml名称结果",
                        "combine":"生成xml名称"
                    }
                },
                {
                    "name":"获取后壳图片信息",
                    "type":"get_image_info",
                    "in":
                    {
                        "image":"@后壳拍照图片"
                    },
                    "out":
                    {
                        "result":"获取后壳图片信息结果",
                        "width":"获取后壳图片信息宽",
                        "height":"获取后壳图片信息高",
                        "channel":"获取后壳图片信息通道"
                    }
                },
                {
                    "name":"保存后壳图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳拍照图片",
                        "path":"$左图片文件夹",
                        "name":"@图片名称"
                    },
                    "out":
                    {
                        "result":"保存图片结果",
                        "full_name":"图片路径"
                    }
                },
                {
                    "name":"判断拍后壳结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"跳转算法流程",
                        "condition":"@后壳拍照结果"
                    }
                },
                {
                    "name":"跳转拍后壳失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示拍后壳失败"
                    }
                },
                {
                    "name":"跳转算法流程",
                    "type":"jump",
                    "in":
                    {
                        "tag":"后壳向下采样"
                    }
                },
                {
                    "name":"显示拍后壳失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右拍后壳失败",
                        "level":"error"
                    }
                },
                {
                    "name":"拍后壳失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送对位NG结果"
                    }
                },
                {
                    "name":"后壳向下采样",
                    "type":"pyramid",
                    "in":
                    {
                        "image":"@后壳拍照图片",
                        "type":"down",
                        "count":"2"
                    },
                    "out":
                    {
                        "result":"后壳向下采样结果",
                        "image_result":"后壳向下采样图片"
                    }
                },
                {
                    "name":"后壳找圆图像增强",
                    "type":"multiply",
                    "in":
                    {
                        "image_in":"@后壳拍照图片",
                        "mul":"3"
                    },
                    "out":
                    {
                        "result":"后壳找圆图像增强结果",
                        "image_out":"后壳向下采样增强图片"
                    }
                },
                {
                    "name":"后壳找圆",
                    "type":"find_circle",
                    "in":
                    {
                        "image":"@后壳向下采样增强图片",
                        "center_x":"2600",
                        "center_y":"2700",
                        "radius_min":"1500",
                        "radius_max":"2200",
                        "count":"200",
                        "width":"100",
                        "ksize":"17",
                        "type":"1",
                        "find_type":"1",
                        "iterations":"50000",
                        "strength":"100",
                        "thresh":"0.3",
                        "sigma":"10",
                        "end":"0.9",
                        "minimum":"0.3",
                        "use_fit":"1"
                    },
                    "out":
                    {
                        "result":"后壳找圆结果",
                        "x":"circle_X",
                        "y":"circle_Y",
                        "radius":"circle_R",
                        "debug_img":"后壳找圆图片"
                    }
                },
                {
                    "name":"判断拍找圆结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"保存筛选圆图片",
                        "condition":"@后壳找圆结果"
                    }
                },
                {
                    "name":"显示找圆失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"左后壳找圆失败",
                        "level":"error"
                    }
                },
                {
                    "name":"找圆失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送对位NG结果"
                    }
                },
                {
                    "name":"保存筛选圆图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳找圆图片",
                        "path":"$左调试文件夹",
                        "name":"back_ellipse.jpg"
                    },
                    "out":
                    {
                        "result":"保存筛选圆图片结果",
                        "full_name":"保存筛选圆图片路径"
                    }
                },
                {
                    "name":"推送计算圆直径公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@circle_R\",\"*2\"]"
                    },
                    "out":
                    {
                        "result":"推送计算圆直径公式结果",
                        "combine":"推送计算圆直径"
                    }
                },
                {
                    "name":"计算圆直径",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算圆直径" 
                    },
                    "out":
                    {
                        "result":"计算圆直径结果",
                        "value":"圆直径"
                    }
                },
                {
                    "name":"计算椭圆矩形",
                    "type":"bounding_box",
                    "in":
                    {
                        "x":"@circle_X",
                        "y":"@circle_Y",
                        "w":"@圆直径",
                        "h":"@圆直径",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"计算椭圆矩形结果",
                        "xmin":"椭圆矩形xmin",
                        "ymin":"椭圆矩形ymin",
                        "xmax":"椭圆矩形xmax",
                        "ymax":"椭圆矩形ymax"
                    }
                },
                {
                    "name":"保存XML",
                    "type":"save_xml",
                    "in":
                    {
                        "path":"$左图片文件夹",
                        "name":"@生成xml名称",
                        "product":"mate3.0",
                        "sn":"$左SN码",
                        "station":"Align",
                        "mac":"NULL",
                        "detection_type":"back",
                        "result":"1",
                        "folder":"$左图片文件夹",
                        "image_name":"@图片名称",
                        "width":"@获取后壳图片信息宽",
                        "height":"@获取后壳图片信息高",
                        "depth":"@获取后壳图片信息通道",
                        "name_b":"[\"Ellipse\"]",
                        "label":"[\"1\"]",
                        "standard":"[\"1\"]",
                        "xmin":"[\"@椭圆矩形xmin\"]",
                        "ymin":"[\"@椭圆矩形ymin\"]",
                        "xmax":"[\"@椭圆矩形xmax\"]",
                        "ymax":"[\"@椭圆矩形ymax\"]"
                    },
                    "out":
                    {
                        "result":"保存XML结果",
                        "full_name":"保存XML全称"
                    }
                },
                {
                    "name":"推送模板x公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"0\",\"*4\"]"
                    },
                    "out":
                    {
                        "result":"推送模板x公式结果",
                        "combine":"推送模板x"
                    }
                },
                {
                    "name":"计算模板x",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送模板x" 
                    },
                    "out":
                    {
                        "result":"计算模板x结果",
                        "value":"模板X"
                    }
                },
                {
                    "name":"推送模板y公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"0\",\"*4\"]"
                    },
                    "out":
                    {
                        "result":"推送模板y公式结果",
                        "combine":"推送模板y"
                    }
                },
                {
                    "name":"计算模板y",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送模板y" 
                    },
                    "out":
                    {
                        "result":"计算模板y结果",
                        "value":"模板Y"
                    }
                },
                {
                    "name":"计算屏幕圆心到后壳像素坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"$左屏幕X",
                        "y_in":"$左屏幕Y",
                        "A":"$左屏幕相机矩阵A",
                        "B":"$左屏幕相机矩阵B",
                        "C":"$左屏幕相机矩阵C",
                        "D":"$左屏幕相机矩阵D",
                        "E":"$左屏幕相机矩阵E",
                        "F":"$左屏幕相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算屏幕圆心到后壳像素坐标结果",
                        "x_out":"屏幕像素圆心x",
                        "y_out":"屏幕像素圆心y"
                    }
                },
                {
                    "name":"计算屏幕模板到后壳像素坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"$左屏幕模板X",
                        "y_in":"$左屏幕模板Y",
                        "A":"$左屏幕相机矩阵A",
                        "B":"$左屏幕相机矩阵B",
                        "C":"$左屏幕相机矩阵C",
                        "D":"$左屏幕相机矩阵D",
                        "E":"$左屏幕相机矩阵E",
                        "F":"$左屏幕相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算屏幕模板到后壳像素坐标结果",
                        "x_out":"屏幕像素模板x",
                        "y_out":"屏幕像素模板y"
                    }
                },
                {
                    "name":"计算屏幕圆心物理坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"@屏幕像素圆心x",
                        "y_in":"@屏幕像素圆心y",
                        "A":"$左对位相机矩阵A",
                        "B":"$左对位相机矩阵B",
                        "C":"$左对位相机矩阵C",
                        "D":"$左对位相机矩阵D",
                        "E":"$左对位相机矩阵E",
                        "F":"$左对位相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算屏幕圆心物理坐标结果",
                        "x_out":"屏幕物理圆心x",
                        "y_out":"屏幕物理圆心y"
                    }
                },
                {
                    "name":"计算屏幕模板物理坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"@屏幕像素模板x",
                        "y_in":"@屏幕像素模板y",
                        "A":"$左对位相机矩阵A",
                        "B":"$左对位相机矩阵B",
                        "C":"$左对位相机矩阵C",
                        "D":"$左对位相机矩阵D",
                        "E":"$左对位相机矩阵E",
                        "F":"$左对位相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算屏幕模板物理坐标结果",
                        "x_out":"屏幕物理模板x",
                        "y_out":"屏幕物理模板y"
                    }
                },
                {
                    "name":"计算后壳圆心物理坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"@circle_X",
                        "y_in":"@circle_Y",
                        "A":"$左对位相机矩阵A",
                        "B":"$左对位相机矩阵B",
                        "C":"$左对位相机矩阵C",
                        "D":"$左对位相机矩阵D",
                        "E":"$左对位相机矩阵E",
                        "F":"$左对位相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算后壳圆心物理坐标结果",
                        "x_out":"后壳物理圆心x",
                        "y_out":"后壳物理圆心y"
                    }
                },
                {
                    "name":"计算后壳模板物理坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"@模板X",
                        "y_in":"@模板Y",
                        "A":"$左对位相机矩阵A",
                        "B":"$左对位相机矩阵B",
                        "C":"$左对位相机矩阵C",
                        "D":"$左对位相机矩阵D",
                        "E":"$左对位相机矩阵E",
                        "F":"$左对位相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算后壳模板物理坐标结果",
                        "x_out":"后壳物理模板x",
                        "y_out":"后壳物理模板y"
                    }
                },
                {
                    "name":"画后壳中心",
                    "type":"draw_ellipse",
                    "in":
                    {
                        "image":"@后壳拍照图片",
                        "x":"@circle_X",
                        "y":"@circle_Y",
                        "a":"100",
                        "b":"100",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"画后壳中心结果",
                        "image":"后壳调试图片1"
                    }
                },
                {
                    "name":"画屏幕中心",
                    "type":"draw_ellipse",
                    "in":
                    {
                        "image":"@后壳调试图片1",
                        "x":"@屏幕像素圆心x",
                        "y":"@屏幕像素圆心y",
                        "a":"40",
                        "b":"40",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"画屏幕中心结果",
                        "image":"后壳调试图片2"
                    }
                },
                {
                    "name":"画后壳直线",
                    "type":"draw_line",
                    "in":
                    {
                        "image":"@后壳调试图片2",
                        "x1":"@circle_X",
                        "y1":"@circle_Y",
                        "x2":"@模板X",
                        "y2":"@模板Y"
                    },
                    "out":
                    {
                        "result":"画后壳直线结果",
                        "image":"后壳调试图片3"
                    }
                },
                {
                    "name":"画屏幕直线",
                    "type":"draw_line",
                    "in":
                    {
                        "image":"@后壳调试图片3",
                        "x1":"@屏幕像素圆心x",
                        "y1":"@屏幕像素圆心y",
                        "x2":"@屏幕像素模板x",
                        "y2":"@屏幕像素模板y"
                    },
                    "out":
                    {
                        "result":"画后壳直线结果",
                        "image":"后壳调试图片4"
                    }
                },
                {
                    "name":"保存对位调试图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳调试图片4",
                        "path":"$左调试文件夹",
                        "name":"align.jpg"
                    },
                    "out":
                    {
                        "result":"保存对位调试图片结果",
                        "full_name":"保存对位调试图片路径"
                    }
                },
                {
                    "name":"推送计算x偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@屏幕物理圆心x\",\"-\",\"@后壳物理圆心x\"]"
                    },
                    "out":
                    {
                        "result":"推送计算x偏移量结果",
                        "combine":"推送计算x偏移量"
                    }
                },
                {
                    "name":"推送计算y偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@屏幕物理圆心y\",\"-\",\"@后壳物理圆心y\"]"
                    },
                    "out":
                    {
                        "result":"推送计算y偏移量结果",
                        "combine":"推送计算y偏移量"
                    }
                },
                {
                    "name":"计算x偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算x偏移量" 
                    },
                    "out":
                    {
                        "result":"计算x偏移量结果",
                        "value":"X偏移量"
                    }
                },
                {
                    "name":"计算y偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算y偏移量" 
                    },
                    "out":
                    {
                        "result":"计算y偏移量结果",
                        "value":"Y偏移量"
                    }
                },
                {
                    "name":"生成屏幕匹配直线",
                    "type":"creat_line",
                    "in":
                    {
                        "x_start":"@屏幕物理圆心x",
                        "y_start":"@屏幕物理圆心y",
                        "x_end":"@屏幕物理模板x",
                        "y_end":"@屏幕物理模板y"
                    },
                    "out":
                    {
                        "result":"生成屏幕匹配直线结果",
                        "line":"屏幕匹配直线"
                    }    
                },
                {
                    "name":"生成后壳匹配直线",
                    "type":"creat_line",
                    "in":
                    {
                        "x_start":"@后壳物理圆心x",
                        "y_start":"@后壳物理圆心y",
                        "x_end":"@后壳物理模板x",
                        "y_end":"@后壳物理模板y"
                    },
                    "out":
                    {
                        "result":"生成后壳匹配直线结果",
                        "line":"后壳匹配直线"
                    }    
                },
                {
                    "name":"计算直线夹角",
                    "type":"calc_line_angle",
                    "in":
                    {
                        "line_1":"@屏幕匹配直线",
                        "line_2":"@后壳匹配直线"
                    },
                    "out":
                    {
                        "result":"计算直线夹角结果",
                        "angle":"直线夹角"
                    }
                },
                {
                    "name":"推送计算X偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@X偏移量\",\"*0.8\"]"
                    },
                    "out":
                    {
                        "result":"推送计算X偏移量结果",
                        "combine":"推送计算X偏移量"
                    }
                },
                {
                    "name":"计算X偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算X偏移量" 
                    },
                    "out":
                    {
                        "result":"计算X偏移量结果",
                        "value":"x偏移量"
                    }
                },
                {
                    "name":"推送计算Y偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@Y偏移量\",\"*0.8\"]"
                    },
                    "out":
                    {
                        "result":"推送计算Y偏移量结果",
                        "combine":"推送计算Y偏移量"
                    }
                },
                {
                    "name":"计算Y偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算Y偏移量" 
                    },
                    "out":
                    {
                        "result":"计算Y偏移量结果",
                        "value":"y偏移量"
                    }
                },
                {
                    "name":"推送计算角度偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"0\",\"+0\"]"
                    },
                    "out":
                    {
                        "result":"推送计算角度偏移量结果",
                        "combine":"推送计算角度偏移量"
                    }
                },
                {
                    "name":"计算角度偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算角度偏移量" 
                    },
                    "out":
                    {
                        "result":"计算角度偏移量结果",
                        "value":"角度偏移量"
                    }
                },
                {
                    "name":"推送显示偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"次数:\",\"$左对位次数\",\",x偏移量:\",\"@x偏移量\",\",y偏移量:\",\"@y偏移量\",\",角度偏移量:\",\"@角度偏移量\"]"
                    },
                    "out":
                    {
                        "result":"推送显示偏移量结果",
                        "combine":"显示偏移量字符"
                    }
                },
                {
                    "name":"显示偏移量",
                    "type":"log_output",
                    "in":
                    {
                        "input":"@显示偏移量字符",
                        "level":"info"
                    }
                },
                {
                    "name":"推送计算偏移量范围小",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"(\",\"@x偏移量\",\">-0.15)&(\",\"@x偏移量\",\"<0.15)&(\",\"@y偏移量\",\">-0.15)&(\",\"@y偏移量\",\"<0.15)&(\",\"@角度偏移量\",\">-0.5)&(\",\"@角度偏移量\",\"<0.5)\"]"
                    },
                    "out":
                    {
                        "result":"推送计算偏移量范围小结果",
                        "combine":"推送计算偏移量范围小"
                    }
                },
                {
                    "name":"计算偏移量范围小",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算偏移量范围小" 
                    },
                    "out":
                    {
                        "result":"计算偏移量范围结果小",
                        "value":"偏移量范围小"
                    }
                },
                {
                    "name":"计算偏移量范围小整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@偏移量范围小"                
                    },
                    "out":
                    {
                        "result":"计算偏移量范围小整型结果",
                        "integer":"偏移量范围小整型"
                    }
                },
                {
                    "name":"判断偏移量范围小",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"发送对位OK结果",
                        "condition":"@偏移量范围小整型"
                    }
                },
                {
                    "name":"推送计算偏移量范围大",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"(\",\"@x偏移量\",\">-5)&(\",\"@x偏移量\",\"<5)&(\",\"@y偏移量\",\">-5)&(\",\"@y偏移量\",\"<5)&(\",\"@角度偏移量\",\">-5)&(\",\"@角度偏移量\",\"<5)\"]"
                    },
                    "out":
                    {
                        "result":"推送计算偏移量范围大结果",
                        "combine":"推送计算偏移量范围大"
                    }
                },
                {
                    "name":"计算偏移量范围大",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算偏移量范围大" 
                    },
                    "out":
                    {
                        "result":"计算偏移量范围结果大",
                        "value":"偏移量范围大"
                    }
                },
                {
                    "name":"计算偏移量范围大整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@偏移量范围大"                
                    },
                    "out":
                    {
                        "result":"计算偏移量范围大整型结果",
                        "integer":"偏移量范围大整型"
                    }
                },
                {
                    "name":"判断偏移量范围大",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"推送计算对位次数",
                        "condition":"@偏移量范围大整型"
                    }
                },
                {
                    "name":"显示偏移量大",
                    "type":"log_output",
                    "in":
                    {
                        "input":"左对位偏移量超范围",
                        "level":"error"
                    }
                },
                {
                    "name":"偏移量范围大跳转对位失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送对位NG结果"
                    }
                },
                {
                    "name":"推送计算对位次数",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"$左对位次数\",\"+1\"]"
                    },
                    "out":
                    {
                        "result":"推送计算对位次数结果",
                        "combine":"推送计算对位次数"
                    }
                },
                {
                    "name":"计算对位次数",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算对位次数" 
                    },
                    "out":
                    {
                        "result":"计算对位次数结果",
                        "value":"对位次数"
                    }
                },
                {
                    "name":"计算对位次数整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@对位次数"                
                    },
                    "out":
                    {
                        "result":"计算对位次数整型结果",
                        "integer":"对位次数整型"
                    }
                },
                {
                    "name":"保存对位次数",
                    "type":"set_global_integer",
                    "in":
                    {
                        "input":"@对位次数整型",
                        "name":"左对位次数"
                    },
                    "out":
                    {
                        "result":"保存对位次数结果"
                    }
                },
                {
                    "name":"推送判断对位次数",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@对位次数\",\"<8\"]"
                    },
                    "out":
                    {
                        "result":"推送判断对位次数结果",
                        "combine":"推送判断对位次数"
                    }
                },
                {
                    "name":"计算判断对位次数",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送判断对位次数" 
                    },
                    "out":
                    {
                        "result":"计算判断对位次数结果",
                        "value":"判断对位次数"
                    }
                },
                {
                    "name":"判断对位次数整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@判断对位次数"                
                    },
                    "out":
                    {
                        "result":"计算判断对位次数整型结果",
                        "integer":"判断对位次数整型"
                    }
                },
                {
                    "name":"判断对位次数大",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"发送X偏移量",
                        "condition":"@判断对位次数整型"
                    }
                },
                {
                    "name":"显示对位次数大",
                    "type":"log_output",
                    "in":
                    {
                        "input":"左对位次数超范围",
                        "level":"error"
                    }
                },
                {
                    "name":"对位次数大跳转对位失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送对位NG结果"
                    }
                },
                {
                    "name":"发送X偏移量",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"204",
                        "type":"float",
                        "content":"@x偏移量"
                    },
                    "out":
                    {
                        "result":"发送X偏移量结果"
                    }
                },
                {
                    "name":"发送Y偏移量",
                    "type":"send_s7_client",
                    "in":
                    {
                        "wait":"@发送X偏移量结果",
                        "client":"左PLC",
                        "db":"520",
                        "offset":"208",
                        "type":"float",
                        "content":"@y偏移量"
                    },
                    "out":
                    {
                        "result":"发送Y偏移量结果"
                    }
                },
                {
                    "name":"发送角度偏移量",
                    "type":"send_s7_client",
                    "in":
                    {
                        "wait":"@发送Y偏移量结果",
                        "client":"左PLC",
                        "db":"520",
                        "offset":"212",
                        "type":"float",
                        "content":"@角度偏移量"
                    },
                    "out":
                    {
                        "result":"发送角度偏移量结果"
                    }
                },
                {
                    "name":"延时1",
                    "type":"sleep",
                    "in":
                    {
                        "wait":"发送角度偏移量结果",
                        "time":"50"
                    },
                    "out":
                    {
                        "result":"延时1结果"
                    }
                },
                {
                    "name":"跳转继续对位",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送继续对位结果"
                    }
                },
                {
                    "name":"发送对位OK结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "wait":"@延时1结果",
                        "client":"左PLC",
                        "db":"520",
                        "offset":"174",
                        "type":"short",
                        "content":"1"
                    }
                },
                {
                    "name":"结束对位OK",
                    "type":"end"
                },
                {
                    "name":"发送对位NG结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"174",
                        "type":"short",
                        "content":"2"
                    }
                },
                {
                    "name":"结束对位NG",
                    "type":"end"
                },
                {
                    "name":"发送继续对位结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"174",
                        "type":"short",
                        "content":"3"
                    }
                },
                {
                    "name":"结束继续对位",
                    "type":"end"
                }
            ] 
        },
        //右对位盖板
        {
            "name":"右对位盖板流程",
            "command":
            [
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"."
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取字符串长度",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取字符串长度结果",
                        "string":"获取字符串长度"
                    }
                },
                {
                    "name":"判断获取字符串长度结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"读取后壳图片",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"后壳拍照",
                    "type":"get_camera_frame",
                    "in":
                    {
                        "camera":"右拍后壳相机",
                        "exposure":"300000",
                        "gain":"8.0"
                    },
                    "out":
                    {
                        "result":"后壳拍照结果",
                        "image":"后壳拍照图片"
                    }
                },
                {
                    "name":"跳转获取时间",
                    "type":"jump",
                    "in":
                    {
                        "tag":"获取当前时间"
                    }
                },
                {
                    "name":"读取后壳图片",
                    "type":"read_image",
                    "in":
                    {
                        "path":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "image":"后壳拍照图片",
                        "result":"后壳拍照结果"
                    }
                },
                {
                    "name":"获取当前时间",
                    "type":"get_date_time",
                    "out":
                    {
                        "result":"获取当前时间结果",
                        "date_time":"当前时间"  
                    }
                    
                },
                {
                    "name":"生成图片名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"back_\",\"$右对位次数\",\"_\",\"@当前时间\",\"_\",\"$右SN码\",\".png\"]"
                    },
                    "out":
                    {
                        "result":"生成后壳名称结果",
                        "combine":"图片名称"
                    }
                },
                {
                    "name":"生成xml名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"back_\",\"$右对位次数\",\"_\",\"@当前时间\",\"_\",\"$右SN码\",\".xml\"]"
                    },
                    "out":
                    {
                        "result":"生成xml名称结果",
                        "combine":"生成xml名称"
                    }
                },
                {
                    "name":"获取后壳图片信息",
                    "type":"get_image_info",
                    "in":
                    {
                        "image":"@后壳拍照图片"
                    },
                    "out":
                    {
                        "result":"获取后壳图片信息结果",
                        "width":"获取后壳图片信息宽",
                        "height":"获取后壳图片信息高",
                        "channel":"获取后壳图片信息通道"
                    }
                },
                {
                    "name":"保存后壳图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳拍照图片",
                        "path":"$右图片文件夹",
                        "name":"@图片名称"
                    },
                    "out":
                    {
                        "result":"保存图片结果",
                        "full_name":"图片路径"
                    }
                },
                {
                    "name":"判断拍后壳结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"跳转算法流程",
                        "condition":"@后壳拍照结果"
                    }
                },
                {
                    "name":"跳转拍后壳失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"显示拍后壳失败"
                    }
                },
                {
                    "name":"跳转算法流程",
                    "type":"jump",
                    "in":
                    {
                        "tag":"后壳向下采样"
                    }
                },
                {
                    "name":"显示拍后壳失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右拍后壳失败",
                        "level":"error"
                    }
                },
                {
                    "name":"拍后壳失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送对位NG结果"
                    }
                },
                {
                    "name":"后壳向下采样",
                    "type":"pyramid",
                    "in":
                    {
                        "image":"@后壳拍照图片",
                        "type":"down",
                        "count":"2"
                    },
                    "out":
                    {
                        "result":"后壳向下采样结果",
                        "image_result":"后壳向下采样图片"
                    }
                },
                {
                    "name":"后壳找圆图像增强",
                    "type":"multiply",
                    "in":
                    {
                        "image_in":"@后壳拍照图片",
                        "mul":"3"
                    },
                    "out":
                    {
                        "result":"后壳找圆图像增强结果",
                        "image_out":"后壳向下采样增强图片"
                    }
                },
                {
                    "name":"后壳找圆",
                    "type":"find_circle",
                    "in":
                    {
                        "image":"@后壳向下采样增强图片",
                        "center_x":"2600",
                        "center_y":"2700",
                        "radius_min":"1500",
                        "radius_max":"2200",
                        "count":"200",
                        "width":"100",
                        "ksize":"17",
                        "type":"1",
                        "find_type":"1",
                        "iterations":"50000",
                        "strength":"100",
                        "thresh":"0.3",
                        "sigma":"10",
                        "end":"0.9",
                        "minimum":"0.3",
                        "use_fit":"1"
                    },
                    "out":
                    {
                        "result":"后壳找圆结果",
                        "x":"circle_X",
                        "y":"circle_Y",
                        "radius":"circle_R",
                        "debug_img":"后壳找圆图片"
                    }
                },
                {
                    "name":"判断拍找圆结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"保存筛选圆图片",
                        "condition":"@后壳找圆结果"
                    }
                },
                {
                    "name":"显示找圆失败",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右后壳找圆失败",
                        "level":"error"
                    }
                },
                {
                    "name":"找圆失败跳转",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送对位NG结果"
                    }
                },
                {
                    "name":"保存筛选圆图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳找圆图片",
                        "path":"$右调试文件夹",
                        "name":"back_ellipse.jpg"
                    },
                    "out":
                    {
                        "result":"保存筛选圆图片结果",
                        "full_name":"保存筛选圆图片路径"
                    }
                },
                {
                    "name":"推送计算圆直径公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@circle_R\",\"*2\"]"
                    },
                    "out":
                    {
                        "result":"推送计算圆直径公式结果",
                        "combine":"推送计算圆直径"
                    }
                },
                {
                    "name":"计算圆直径",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算圆直径" 
                    },
                    "out":
                    {
                        "result":"计算圆直径结果",
                        "value":"圆直径"
                    }
                },
                {
                    "name":"计算椭圆矩形",
                    "type":"bounding_box",
                    "in":
                    {
                        "x":"@circle_X",
                        "y":"@circle_Y",
                        "w":"@圆直径",
                        "h":"@圆直径",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"计算椭圆矩形结果",
                        "xmin":"椭圆矩形xmin",
                        "ymin":"椭圆矩形ymin",
                        "xmax":"椭圆矩形xmax",
                        "ymax":"椭圆矩形ymax"
                    }
                },
                {
                    "name":"保存XML",
                    "type":"save_xml",
                    "in":
                    {
                        "path":"$右图片文件夹",
                        "name":"@生成xml名称",
                        "product":"mate3.0",
                        "sn":"$右SN码",
                        "station":"Align",
                        "mac":"NULL",
                        "detection_type":"back",
                        "result":"1",
                        "folder":"$右图片文件夹",
                        "image_name":"@图片名称",
                        "width":"@获取后壳图片信息宽",
                        "height":"@获取后壳图片信息高",
                        "depth":"@获取后壳图片信息通道",
                        "name_b":"[\"Ellipse\"]",
                        "label":"[\"1\"]",
                        "standard":"[\"1\"]",
                        "xmin":"[\"@椭圆矩形xmin\"]",
                        "ymin":"[\"@椭圆矩形ymin\"]",
                        "xmax":"[\"@椭圆矩形xmax\"]",
                        "ymax":"[\"@椭圆矩形ymax\"]"
                    },
                    "out":
                    {
                        "result":"保存XML结果",
                        "full_name":"保存XML全称"
                    }
                },
                {
                    "name":"推送模板x公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"0\",\"*4\"]"
                    },
                    "out":
                    {
                        "result":"推送模板x公式结果",
                        "combine":"推送模板x"
                    }
                },
                {
                    "name":"计算模板x",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送模板x" 
                    },
                    "out":
                    {
                        "result":"计算模板x结果",
                        "value":"模板X"
                    }
                },
                {
                    "name":"推送模板y公式",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"0\",\"*4\"]"
                    },
                    "out":
                    {
                        "result":"推送模板y公式结果",
                        "combine":"推送模板y"
                    }
                },
                {
                    "name":"计算模板y",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送模板y" 
                    },
                    "out":
                    {
                        "result":"计算模板y结果",
                        "value":"模板Y"
                    }
                },
                {
                    "name":"计算屏幕圆心到后壳像素坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"$右屏幕X",
                        "y_in":"$右屏幕Y",
                        "A":"$右屏幕相机矩阵A",
                        "B":"$右屏幕相机矩阵B",
                        "C":"$右屏幕相机矩阵C",
                        "D":"$右屏幕相机矩阵D",
                        "E":"$右屏幕相机矩阵E",
                        "F":"$右屏幕相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算屏幕圆心到后壳像素坐标结果",
                        "x_out":"屏幕像素圆心x",
                        "y_out":"屏幕像素圆心y"
                    }
                },
                {
                    "name":"计算屏幕模板到后壳像素坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"$右屏幕模板X",
                        "y_in":"$右屏幕模板Y",
                        "A":"$右屏幕相机矩阵A",
                        "B":"$右屏幕相机矩阵B",
                        "C":"$右屏幕相机矩阵C",
                        "D":"$右屏幕相机矩阵D",
                        "E":"$右屏幕相机矩阵E",
                        "F":"$右屏幕相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算屏幕模板到后壳像素坐标结果",
                        "x_out":"屏幕像素模板x",
                        "y_out":"屏幕像素模板y"
                    }
                },
                {
                    "name":"计算屏幕圆心物理坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"@屏幕像素圆心x",
                        "y_in":"@屏幕像素圆心y",
                        "A":"$右对位相机矩阵A",
                        "B":"$右对位相机矩阵B",
                        "C":"$右对位相机矩阵C",
                        "D":"$右对位相机矩阵D",
                        "E":"$右对位相机矩阵E",
                        "F":"$右对位相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算屏幕圆心物理坐标结果",
                        "x_out":"屏幕物理圆心x",
                        "y_out":"屏幕物理圆心y"
                    }
                },
                {
                    "name":"计算屏幕模板物理坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"@屏幕像素模板x",
                        "y_in":"@屏幕像素模板y",
                        "A":"$右对位相机矩阵A",
                        "B":"$右对位相机矩阵B",
                        "C":"$右对位相机矩阵C",
                        "D":"$右对位相机矩阵D",
                        "E":"$右对位相机矩阵E",
                        "F":"$右对位相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算屏幕模板物理坐标结果",
                        "x_out":"屏幕物理模板x",
                        "y_out":"屏幕物理模板y"
                    }
                },
                {
                    "name":"计算后壳圆心物理坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"@circle_X",
                        "y_in":"@circle_Y",
                        "A":"$右对位相机矩阵A",
                        "B":"$右对位相机矩阵B",
                        "C":"$右对位相机矩阵C",
                        "D":"$右对位相机矩阵D",
                        "E":"$右对位相机矩阵E",
                        "F":"$右对位相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算后壳圆心物理坐标结果",
                        "x_out":"后壳物理圆心x",
                        "y_out":"后壳物理圆心y"
                    }
                },
                {
                    "name":"计算后壳模板物理坐标",
                    "type":"point_aff",
                    "in":
                    {
                        "x_in":"@模板X",
                        "y_in":"@模板Y",
                        "A":"$右对位相机矩阵A",
                        "B":"$右对位相机矩阵B",
                        "C":"$右对位相机矩阵C",
                        "D":"$右对位相机矩阵D",
                        "E":"$右对位相机矩阵E",
                        "F":"$右对位相机矩阵F"
                    },
                    "out":
                    {
                        "result":"计算后壳模板物理坐标结果",
                        "x_out":"后壳物理模板x",
                        "y_out":"后壳物理模板y"
                    }
                },
                {
                    "name":"画后壳中心",
                    "type":"draw_ellipse",
                    "in":
                    {
                        "image":"@后壳拍照图片",
                        "x":"@circle_X",
                        "y":"@circle_Y",
                        "a":"100",
                        "b":"100",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"画后壳中心结果",
                        "image":"后壳调试图片1"
                    }
                },
                {
                    "name":"画屏幕中心",
                    "type":"draw_ellipse",
                    "in":
                    {
                        "image":"@后壳调试图片1",
                        "x":"@屏幕像素圆心x",
                        "y":"@屏幕像素圆心y",
                        "a":"40",
                        "b":"40",
                        "angle":"0"
                    },
                    "out":
                    {
                        "result":"画屏幕中心结果",
                        "image":"后壳调试图片2"
                    }
                },
                {
                    "name":"画后壳直线",
                    "type":"draw_line",
                    "in":
                    {
                        "image":"@后壳调试图片2",
                        "x1":"@circle_X",
                        "y1":"@circle_Y",
                        "x2":"@模板X",
                        "y2":"@模板Y"
                    },
                    "out":
                    {
                        "result":"画后壳直线结果",
                        "image":"后壳调试图片3"
                    }
                },
                {
                    "name":"画屏幕直线",
                    "type":"draw_line",
                    "in":
                    {
                        "image":"@后壳调试图片3",
                        "x1":"@屏幕像素圆心x",
                        "y1":"@屏幕像素圆心y",
                        "x2":"@屏幕像素模板x",
                        "y2":"@屏幕像素模板y"
                    },
                    "out":
                    {
                        "result":"画后壳直线结果",
                        "image":"后壳调试图片4"
                    }
                },
                {
                    "name":"保存对位调试图片",
                    "type":"save_image",
                    "in":
                    {
                        "image":"@后壳调试图片4",
                        "path":"$右调试文件夹",
                        "name":"align.jpg"
                    },
                    "out":
                    {
                        "result":"保存对位调试图片结果",
                        "full_name":"保存对位调试图片路径"
                    }
                },
                {
                    "name":"推送计算x偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@屏幕物理圆心x\",\"-\",\"@后壳物理圆心x\"]"
                    },
                    "out":
                    {
                        "result":"推送计算x偏移量结果",
                        "combine":"推送计算x偏移量"
                    }
                },
                {
                    "name":"推送计算y偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@屏幕物理圆心y\",\"-\",\"@后壳物理圆心y\"]"
                    },
                    "out":
                    {
                        "result":"推送计算y偏移量结果",
                        "combine":"推送计算y偏移量"
                    }
                },
                {
                    "name":"计算x偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算x偏移量" 
                    },
                    "out":
                    {
                        "result":"计算x偏移量结果",
                        "value":"X偏移量"
                    }
                },
                {
                    "name":"计算y偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算y偏移量" 
                    },
                    "out":
                    {
                        "result":"计算y偏移量结果",
                        "value":"Y偏移量"
                    }
                },
                {
                    "name":"生成屏幕匹配直线",
                    "type":"creat_line",
                    "in":
                    {
                        "x_start":"@屏幕物理圆心x",
                        "y_start":"@屏幕物理圆心y",
                        "x_end":"@屏幕物理模板x",
                        "y_end":"@屏幕物理模板y"
                    },
                    "out":
                    {
                        "result":"生成屏幕匹配直线结果",
                        "line":"屏幕匹配直线"
                    }    
                },
                {
                    "name":"生成后壳匹配直线",
                    "type":"creat_line",
                    "in":
                    {
                        "x_start":"@后壳物理圆心x",
                        "y_start":"@后壳物理圆心y",
                        "x_end":"@后壳物理模板x",
                        "y_end":"@后壳物理模板y"
                    },
                    "out":
                    {
                        "result":"生成后壳匹配直线结果",
                        "line":"后壳匹配直线"
                    }    
                },
                {
                    "name":"计算直线夹角",
                    "type":"calc_line_angle",
                    "in":
                    {
                        "line_1":"@屏幕匹配直线",
                        "line_2":"@后壳匹配直线"
                    },
                    "out":
                    {
                        "result":"计算直线夹角结果",
                        "angle":"直线夹角"
                    }
                },
                {
                    "name":"推送计算X偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@X偏移量\",\"*0.8\"]"
                    },
                    "out":
                    {
                        "result":"推送计算X偏移量结果",
                        "combine":"推送计算X偏移量"
                    }
                },
                {
                    "name":"计算X偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算X偏移量" 
                    },
                    "out":
                    {
                        "result":"计算X偏移量结果",
                        "value":"x偏移量"
                    }
                },
                {
                    "name":"推送计算Y偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@Y偏移量\",\"*0.8\"]"
                    },
                    "out":
                    {
                        "result":"推送计算Y偏移量结果",
                        "combine":"推送计算Y偏移量"
                    }
                },
                {
                    "name":"计算Y偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算Y偏移量" 
                    },
                    "out":
                    {
                        "result":"计算Y偏移量结果",
                        "value":"y偏移量"
                    }
                },
                {
                    "name":"推送计算角度偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"0\",\"+0\"]"
                    },
                    "out":
                    {
                        "result":"推送计算角度偏移量结果",
                        "combine":"推送计算角度偏移量"
                    }
                },
                {
                    "name":"计算角度偏移量",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算角度偏移量" 
                    },
                    "out":
                    {
                        "result":"计算角度偏移量结果",
                        "value":"角度偏移量"
                    }
                },
                {
                    "name":"推送显示偏移量",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"次数:\",\"$右对位次数\",\",x偏移量:\",\"@x偏移量\",\",y偏移量:\",\"@y偏移量\",\",角度偏移量:\",\"@角度偏移量\"]"
                    },
                    "out":
                    {
                        "result":"推送显示偏移量结果",
                        "combine":"显示偏移量字符"
                    }
                },
                {
                    "name":"显示偏移量",
                    "type":"log_output",
                    "in":
                    {
                        "input":"@显示偏移量字符",
                        "level":"info"
                    }
                },
                {
                    "name":"推送计算偏移量范围小",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"(\",\"@x偏移量\",\">-0.15)&(\",\"@x偏移量\",\"<0.15)&(\",\"@y偏移量\",\">-0.15)&(\",\"@y偏移量\",\"<0.15)&(\",\"@角度偏移量\",\">-0.5)&(\",\"@角度偏移量\",\"<0.5)\"]"
                    },
                    "out":
                    {
                        "result":"推送计算偏移量范围小结果",
                        "combine":"推送计算偏移量范围小"
                    }
                },
                {
                    "name":"计算偏移量范围小",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算偏移量范围小" 
                    },
                    "out":
                    {
                        "result":"计算偏移量范围结果小",
                        "value":"偏移量范围小"
                    }
                },
                {
                    "name":"计算偏移量范围小整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@偏移量范围小"                
                    },
                    "out":
                    {
                        "result":"计算偏移量范围小整型结果",
                        "integer":"偏移量范围小整型"
                    }
                },
                {
                    "name":"判断偏移量范围小",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"发送对位OK结果",
                        "condition":"@偏移量范围小整型"
                    }
                },
                {
                    "name":"推送计算偏移量范围大",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"(\",\"@x偏移量\",\">-5)&(\",\"@x偏移量\",\"<5)&(\",\"@y偏移量\",\">-5)&(\",\"@y偏移量\",\"<5)&(\",\"@角度偏移量\",\">-5)&(\",\"@角度偏移量\",\"<5)\"]"
                    },
                    "out":
                    {
                        "result":"推送计算偏移量范围大结果",
                        "combine":"推送计算偏移量范围大"
                    }
                },
                {
                    "name":"计算偏移量范围大",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算偏移量范围大" 
                    },
                    "out":
                    {
                        "result":"计算偏移量范围结果大",
                        "value":"偏移量范围大"
                    }
                },
                {
                    "name":"计算偏移量范围大整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@偏移量范围大"                
                    },
                    "out":
                    {
                        "result":"计算偏移量范围大整型结果",
                        "integer":"偏移量范围大整型"
                    }
                },
                {
                    "name":"判断偏移量范围大",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"推送计算对位次数",
                        "condition":"@偏移量范围大整型"
                    }
                },
                {
                    "name":"显示偏移量大",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右对位偏移量超范围",
                        "level":"error"
                    }
                },
                {
                    "name":"偏移量范围大跳转对位失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送对位NG结果"
                    }
                },
                {
                    "name":"推送计算对位次数",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"$右对位次数\",\"+1\"]"
                    },
                    "out":
                    {
                        "result":"推送计算对位次数结果",
                        "combine":"推送计算对位次数"
                    }
                },
                {
                    "name":"计算对位次数",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送计算对位次数" 
                    },
                    "out":
                    {
                        "result":"计算对位次数结果",
                        "value":"对位次数"
                    }
                },
                {
                    "name":"计算对位次数整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@对位次数"                
                    },
                    "out":
                    {
                        "result":"计算对位次数整型结果",
                        "integer":"对位次数整型"
                    }
                },
                {
                    "name":"保存对位次数",
                    "type":"set_global_integer",
                    "in":
                    {
                        "input":"@对位次数整型",
                        "name":"右对位次数"
                    },
                    "out":
                    {
                        "result":"保存对位次数结果"
                    }
                },
                {
                    "name":"推送判断对位次数",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@对位次数\",\"<8\"]"
                    },
                    "out":
                    {
                        "result":"推送判断对位次数结果",
                        "combine":"推送判断对位次数"
                    }
                },
                {
                    "name":"计算判断对位次数",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送判断对位次数" 
                    },
                    "out":
                    {
                        "result":"计算判断对位次数结果",
                        "value":"判断对位次数"
                    }
                },
                {
                    "name":"判断对位次数整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@判断对位次数"                
                    },
                    "out":
                    {
                        "result":"计算判断对位次数整型结果",
                        "integer":"判断对位次数整型"
                    }
                },
                {
                    "name":"判断对位次数大",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"发送X偏移量",
                        "condition":"@判断对位次数整型"
                    }
                },
                {
                    "name":"显示对位次数大",
                    "type":"log_output",
                    "in":
                    {
                        "input":"右对位次数超范围",
                        "level":"error"
                    }
                },
                {
                    "name":"对位次数大跳转对位失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送对位NG结果"
                    }
                },
                {
                    "name":"发送X偏移量",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"204",
                        "type":"float",
                        "content":"@x偏移量"
                    },
                    "out":
                    {
                        "result":"发送X偏移量结果"
                    }
                },
                {
                    "name":"发送Y偏移量",
                    "type":"send_s7_client",
                    "in":
                    {
                        "wait":"@发送X偏移量结果",
                        "client":"右PLC",
                        "db":"520",
                        "offset":"208",
                        "type":"float",
                        "content":"@y偏移量"
                    },
                    "out":
                    {
                        "result":"发送Y偏移量结果"
                    }
                },
                {
                    "name":"发送角度偏移量",
                    "type":"send_s7_client",
                    "in":
                    {
                        "wait":"@发送Y偏移量结果",
                        "client":"右PLC",
                        "db":"520",
                        "offset":"212",
                        "type":"float",
                        "content":"@角度偏移量"
                    },
                    "out":
                    {
                        "result":"发送角度偏移量结果"
                    }
                },
                {
                    "name":"延时1",
                    "type":"sleep",
                    "in":
                    {
                        "wait":"发送角度偏移量结果",
                        "time":"50"
                    },
                    "out":
                    {
                        "result":"延时1结果"
                    }
                },
                {
                    "name":"跳转继续对位",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送继续对位结果"
                    }
                },
                {
                    "name":"发送对位OK结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "wait":"@延时1结果",
                        "client":"右PLC",
                        "db":"520",
                        "offset":"174",
                        "type":"short",
                        "content":"1"
                    }
                },
                {
                    "name":"结束对位OK",
                    "type":"end"
                },
                {
                    "name":"发送对位NG结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"174",
                        "type":"short",
                        "content":"2"
                    }
                },
                {
                    "name":"结束对位NG",
                    "type":"end"
                },
                {
                    "name":"发送继续对位结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"174",
                        "type":"short",
                        "content":"3"
                    }
                },
                {
                    "name":"结束继续对位",
                    "type":"end"
                }
            ] 
        }
    ]
}
