import threading
import time

import serial
import serial.tools.list_ports

from simbox_tools.Tools import Tools
from simbox_tools.Logger import Logger



class UsbCdc(object):
    _log_tag = "UsbCdc"
    __baudrate = 115200
    __port = "COM1"
    __list = []
    __serial_port = None
    __recv_proc = None
    __write_lock = None

    def __init__(self):
        self.update_list()

    def get_list(self):
        return self.__list

    def update_list(self):
        self.__list = list(serial.tools.list_ports.comports())
        pass

    def open(self, port=None, baudrate=115200, receiver=None):
        if port is not None:
            self.__port = port
        self.__baudrate = baudrate
        self.__recv_proc = receiver
        self.__write_lock = threading.Lock()
        self.__serial_port = serial.Serial(self.__port, self.__baudrate)
        thread = threading.Thread(target=self.recv_server, name="recv_server")
        thread.start()

    def close(self):
        if self.__serial_port is not None:
            self.__serial_port.close()
            self.__serial_port = None

    def is_open(self):
        return self.__serial_port is not None

    def write(self, data):
        if self.__serial_port is None:
            return Logger.console(self._log_tag,"write -> The communication is disconnect!")

        self.__write_lock.acquire()

        try:
            while True:
                if len(data) > 64:
                    self.__serial_port.flush()
                    temp = data[:64]
                    data = data[64:]
                    self.__serial_port.write(temp)
                    Tools.print_list("temp", temp)
                else:
                    self.__serial_port.flush()
                    self.__serial_port.write(data)
                    break
        except Exception as e:
            Logger.console(self._log_tag, "write -> %s" % e.args)

        self.__write_lock.release()

    def recv_server(self):
        try:
            while self.__serial_port is not None:
                data = self.__serial_port.read_all()
                if (data is not None) and (len(data) > 0):
                    if self.__recv_proc is not None:
                        self.__recv_proc(data)

                time.sleep(0.001)
        except Exception as e:
            Logger.console(self._log_tag, "recv_server -> %s" % e.args)
