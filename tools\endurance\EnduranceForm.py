
from PyQt5.QtCore import pyqtSlot
from PyQt5.QtWidgets import QWidget

from common.LogUtils import logger
from common.modbus.TASIO import TASIO
from tools.endurance.Wakeup import Wakeup<PERSON>orker
from tools.endurance.ui.UI_EnduranceForm import Ui_EnduranceForm
from utils import get_serial_list


class EnduranceForm(QWidget, Ui_EnduranceForm):

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUi(self)

        self.update_serial_port()

        self.io_client: TASIO = TASIO()
        self.io_client.state_signal.connect(self.update_device_state)
        self.io_client.error_signal.connect(self.show_io_error)
        self.io_client.device_info_signal.connect(self.update_device_info)

        self.wakeup_worker: WakeupWorker = None

        self.connect_btn.setEnabled(True)
        self.disconnect_btn.setEnabled(False)
        self.do_1_checkbox.setEnabled(False)
        self.do_2_checkbox.setEnabled(False)
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(False)

    def update_serial_port(self):
        self.serial_combo.clear()
        ports = get_serial_list()
        for port, desc in ports.items():
            self.serial_combo.addItem(desc, port)

    @pyqtSlot()
    def on_refresh_btn_clicked(self):
        logger.info("on_refresh_btn_clicked")
        """
        刷新串口列表
        :return:
        """
        self.update_serial_port()

    @pyqtSlot()
    def on_connect_btn_clicked(self):
        logger.info("on_connect_btn_clicked")
        """
        连接IO板卡
        :return:
        """
        if not self.io_client.is_open():
            port = self.serial_combo.currentData()
            if port is None:
                return

            slave_id = self.slave_spin.value()
            self.io_client.connect(port=port, slave_id=slave_id)

    @pyqtSlot()
    def on_disconnect_btn_clicked(self):
        logger.info("on_disconnect_btn_clicked")
        """
        断开连接IO板卡
        :return:
        """
        if self.io_client.is_open():
            self.io_client.disconnect()

    @pyqtSlot()
    def on_start_btn_clicked(self):
        logger.info("on_start_btn_clicked")
        """
        启动测试
        :return:
        """
        channel = 0
        if self.do_1_radio.isChecked():
            channel = 0
        elif self.do_2_radio.isChecked():
            channel = 1

        wake_time = self.wakeup_spin.value()
        sleep_time = self.sleep_spin.value()
        count = self.count_spin.value()
        if self.wakeup_worker is None:
            self.wakeup_worker = WakeupWorker(self.io_client, channel=channel, wake_time=wake_time,
                                              sleep_time=sleep_time, count=count)
        else:
            self.wakeup_worker.set_wake_time(wake_time)
            self.wakeup_worker.set_sleep_time(sleep_time)
            self.wakeup_worker.set_count(count)
        self.wakeup_worker.start()

        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)

    @pyqtSlot()
    def on_stop_btn_clicked(self):
        logger.info("on_stop_btn_clicked")
        """
        暂停测试
        :return:
        """
        if self.wakeup_worker is not None:
            self.wakeup_worker.stop()

            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)

    @pyqtSlot(bool)
    def on_do_1_checkbox_clicked(self, checked):
        if self.io_client.is_open():
            if checked:
                self.io_client.power_on(0)
            else:
                self.io_client.power_off(0)

    @pyqtSlot(bool)
    def on_do_2_checkbox_clicked(self, checked):
        if self.io_client.is_open():
            if checked:
                self.io_client.power_on(1)
            else:
                self.io_client.power_off(1)

    def update_device_state(self, state):
        logger.info("update_device_state state={}".format(state))
        self.device_radio.setChecked(state)
        if state:
            self.disconnect_btn.setEnabled(True)
            self.connect_btn.setEnabled(False)

            self.do_1_checkbox.setEnabled(True)
            self.do_2_checkbox.setEnabled(True)

            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
        else:
            self.device_name.setText('--')
            self.device_software.setText('--')
            self.device_hardware.setText('--')

            self.disconnect_btn.setEnabled(False)
            self.connect_btn.setEnabled(True)

            self.do_1_checkbox.setEnabled(False)
            self.do_2_checkbox.setEnabled(False)

            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(False)

    def update_device_info(self, device_name, software_version, hardware_version):
        self.device_name.setText(device_name)
        self.device_software.setText(software_version)
        self.device_hardware.setText(hardware_version)

    def show_io_error(self, code, msg):
        logger.info("show_io_error code={}, msg={}".format(code, msg))
