import _thread
import os
import traceback

import numpy
from PIL import Image
from PyQt5.QtCore import pyqtSlot
from PyQt5.QtGui import QColor
from PyQt5.QtWidgets import QFileDialog, QColorDialog, QMainWindow

from common.LogUtils import logger
from common.view.MessageDialog import MessageDialog
from tools.picture_tool.ui.UIPicture import Ui_PictureWindow


class PictureWindow(QMainWindow, Ui_PictureWindow):

    def __init__(self, parent=None):
        super(PictureWindow, self).__init__(parent=parent)
        self.setupUi(self)
        self.setWindowTitle("图片工具")
        self.chess_width.setEnabled(False)
        self.spinBox.setEnabled(False)
        self.color_r.setEnabled(False)
        self.color_g.setEnabled(False)
        self.color_b.setEnabled(False)

        self.color_1 = QColor(0, 0, 0)
        self.color_2 = QColor(255, 255, 255)

        self.color_1_btn.setStyleSheet("QPushButton{background-color:rgb(%d,%d,%d)}" % (
            self.color_1.red(), self.color_1.green(), self.color_1.blue()))
        self.color_2_btn.setStyleSheet("QPushButton{background-color:rgb(%d,%d,%d)}" % (
            self.color_2.red(), self.color_2.green(), self.color_2.blue()))

    @pyqtSlot()
    def on_path_button_clicked(self):
        if self.gray_picture.isChecked():
            folder_path = QFileDialog.getExistingDirectory(self, "选择文件夹")
            self.path_line.setText(folder_path)
        else:
            folder_path, ok = QFileDialog.getSaveFileName(self, "保存图片", "", ".png")
            if ok:
                self.path_line.setText(folder_path + ok)

    @pyqtSlot()
    def on_create_button_clicked(self):
        try:
            if self.gray_picture.isChecked():
                _thread.start_new_thread(self.create_gray_picture, ())
            elif self.chess_picture.isChecked():
                _thread.start_new_thread(self.create_chess_picture, ())
            elif self.radioButton.isChecked():
                _thread.start_new_thread(self.make_gradation_img, ())
            else:
                _thread.start_new_thread(self.create_custom_picture, ())
            MessageDialog.show_auto_close_message("提示", "图片创建成功", 3000)
        except Exception as e:
            print(e.args)

    @pyqtSlot()
    def on_gray_picture_clicked(self):
        self.gray_star.setEnabled(True)
        self.gray_end.setEnabled(True)
        self.chess_width.setEnabled(False)
        self.spinBox.setEnabled(False)
        self.color_r.setEnabled(False)
        self.color_g.setEnabled(False)
        self.color_b.setEnabled(False)
        self.spinBox_2.setEnabled(False)
        self.spinBox_3.setEnabled(False)
        self.spinBox_4.setEnabled(False)
        self.spinBox_5.setEnabled(False)
        self.spinBox_6.setEnabled(False)
        self.spinBox_7.setEnabled(False)

    @pyqtSlot()
    def on_chess_picture_clicked(self):
        self.gray_star.setEnabled(False)
        self.gray_end.setEnabled(False)
        self.chess_width.setEnabled(True)
        self.spinBox.setEnabled(True)
        self.color_r.setEnabled(False)
        self.color_g.setEnabled(False)
        self.color_b.setEnabled(False)
        self.spinBox_2.setEnabled(False)
        self.spinBox_3.setEnabled(False)
        self.spinBox_4.setEnabled(False)
        self.spinBox_5.setEnabled(False)
        self.spinBox_6.setEnabled(False)
        self.spinBox_7.setEnabled(False)

    @pyqtSlot()
    def on_custom_picture_clicked(self):
        self.gray_star.setEnabled(False)
        self.gray_end.setEnabled(False)
        self.chess_width.setEnabled(False)
        self.spinBox.setEnabled(False)
        self.color_r.setEnabled(True)
        self.color_g.setEnabled(True)
        self.color_b.setEnabled(True)
        self.spinBox_2.setEnabled(False)
        self.spinBox_3.setEnabled(False)
        self.spinBox_4.setEnabled(False)
        self.spinBox_5.setEnabled(False)
        self.spinBox_6.setEnabled(False)
        self.spinBox_7.setEnabled(False)

    @pyqtSlot()
    def on_radioButton_clicked(self):
        self.gray_star.setEnabled(False)
        self.gray_end.setEnabled(False)
        self.chess_width.setEnabled(False)
        self.spinBox.setEnabled(False)
        self.color_r.setEnabled(False)
        self.color_g.setEnabled(False)
        self.color_b.setEnabled(False)
        self.spinBox_2.setEnabled(True)
        self.spinBox_3.setEnabled(True)
        self.spinBox_4.setEnabled(True)
        self.spinBox_5.setEnabled(True)
        self.spinBox_6.setEnabled(True)
        self.spinBox_7.setEnabled(True)

    @pyqtSlot()
    def on_color_1_btn_clicked(self):
        self.color_1 = QColorDialog.getColor()
        self.color_1_btn.setStyleSheet("QPushButton{background-color:rgb(%d,%d,%d)}" % (
            self.color_1.red(), self.color_1.green(), self.color_1.blue()))

    @pyqtSlot()
    def on_color_2_btn_clicked(self):
        self.color_2 = QColorDialog.getColor()
        self.color_2_btn.setStyleSheet("QPushButton{background-color:rgb(%d,%d,%d)}" % (
            self.color_2.red(), self.color_2.green(), self.color_2.blue()))

    def create_custom_picture(self):
        try:
            picture_folder = self.path_line.text()
            # if not os.path.exists(picture_folder):
            #     os.makedirs(picture_folder)
            size = (self.pic_width.value(), self.pic_height.value())
            color = (self.color_r.value(), self.color_g.value(), self.color_b.value())
            image = Image.new('RGB', size, color)
            image.save(f'{picture_folder}')
        except Exception as e:
            print(e.args)

    def create_gray_picture(self):
        try:
            picture_folder = self.path_line.text()
            if not os.path.exists(picture_folder):
                os.makedirs(picture_folder)
            size = (self.pic_width.value(), self.pic_height.value())
            gray_from = self.gray_star.value()
            gray_to = self.gray_end.value()
            for i in range(gray_from, gray_to + 1):
                color = (i, i, i)
                image = Image.new('RGB', size, color)
                image.save(f'{picture_folder}/{i}.png')
        except Exception as e:
            logger.error(f"create_gray_picture exception: {str(e.args)}")
            print(traceback.format_exc())

    def create_chess_picture(self):
        try:
            picture_folder = self.path_line.text()
            width = self.pic_width.value()
            height = self.pic_height.value()
            width_interval = self.chess_width.value()
            height_interval = self.spinBox.value()
            color1 = (self.color_1.red(), self.color_1.green(), self.color_1.blue())
            color2 = (self.color_2.red(), self.color_2.green(), self.color_2.blue())
            image = Image.new('RGB', (width, height))
            w_interval = width / width_interval
            h_interval = height / height_interval
            for h in range(height):
                for w in range(width):
                    if (int(h / h_interval) + int(w / w_interval)) % 2 == 1:
                        image.putpixel((w, h), color1)
                    else:
                        image.putpixel((w, h), color2)
            image.save(f'{picture_folder}')
            # QMessageBox.information(self,"提示", "保存图片成功")
            # MessageDialog.showMessage("提示", "保存成功")
        except Exception as e:
            logger.error(f"create_chess_picture exception: {str(e.args)}")
            print(traceback.format_exc())

    def make_gradation_img(self):
        try:
            picture_folder = self.path_line.text()

            def make_gradation_img_data(width, height, rgb_start, rgb_stop, horizontal=(True, True, True)):
                result = numpy.zeros((height, width, 3), dtype=numpy.uint8)
                for i, (m, n, o) in enumerate(zip(rgb_start, rgb_stop, horizontal)):
                    if o:
                        result[:, :, i] = numpy.tile(numpy.linspace(m, n, width), (height, 1))
                    else:
                        result[:, :, i] = numpy.tile(numpy.linspace(m, n, width), (height, 1)).T
                return result

            width = self.pic_width.value()
            height = self.pic_height.value()
            rgb_start = (self.spinBox_2.value(), self.spinBox_3.value(), self.spinBox_4.value())
            rgb_stop = (self.spinBox_5.value(), self.spinBox_6.value(), self.spinBox_7.value())
            img = Image.fromarray(make_gradation_img_data(width, height, rgb_start, rgb_stop))
            img.save(f'{picture_folder}')
        except Exception as e:
            logger.error(f"make_gradation_img exception: {str(e.args)}")
            print(traceback.format_exc())
