import re
import time

import serial


class InspireClient:

    def __init__(self):
        self.ser = None

        self._is_open = False

    def is_open(self):
        return self._is_open

    def open(self, port, baudrate=115200):
        if self._is_open:
            return True
        try:
            self.ser = serial.Serial()
            self.ser.port = port
            self.ser.baudrate = baudrate
            self.ser.open()

            self._is_open = True
            return True
        except Exception as e:
            print(str(e.args))
            return False

    def close(self):
        if self.ser:
            self.ser.close()
        self._is_open = False
        return True

    def recv(self):
        res = self.ser.read_all()
        print("res=", res)
        # res = res.lstrip(b'\x00')
        res = re.sub(b'^[^\\xaaU]*', b'', res)

        if len(res) <= 3:
            return None
        if res[0] != 0xAA or res[1] != 0x55:
            return None
        num = (res[2] & 0xFF) - 2
        if len(res) < 6 + num:
            return None

        val = []
        for i in range(num):
            val.append(res[6 + i])
        return val

    def execute(self, id_, cmd_type, start, value, returning=True):
        data = [cmd_type, start]
        data.extend(value)
        data = [0x55, 0xAA, len(data), id_] + data
        checksum = 0x00
        for i in data[2:]:
            checksum += i
        checksum &= 0xFF
        data.append(checksum)

        self.ser.read_all()

        self.ser.write(data)
        time.sleep(0.1)

        if returning:
            r = self.recv()
            return r
        else:
            self.ser.read_all()
            return None

    def get_position(self, id_):
        r = self.execute(id_, 0x01, 26, [2])
        if r is None:
            return None
        return (r[1] << 8) | r[0]

    def get_tar_position(self, id_):
        r = self.execute(id_, 0x01, 55, [2])
        if r is None:
            return None
        return (r[1] << 8) | r[0]

    def set_tar_position(self, id_, p):
        high = p >> 8
        low = p & 0xff
        self.execute(id_, 0x02, 55, [low, high], False)

    def move_to(self, id_, p, model=1):
        high = p >> 8
        low = p & 0xff
        value = [low, high]

        if model == 1:
            cmd_type = 0x03
        elif model == 2:
            cmd_type = 0x21
        else:
            return

        return self.execute(id_, cmd_type, 55, value)

    def enable(self, id_):
        self.execute(id_, 0x04, 0, [0x23], False)

    def stop(self, id_):
        self.execute(id_, 0x04, 0, [0x04], False)

    def pause(self, id_):
        self.execute(id_, 0x04, 0, [0x20], False)

    def clear(self, id_):
        self.execute(id_, 0x04, 0, [0x1E], False)

    def get_status(self, id_):
        r = self.execute(id_, 0x04, 0, [0x22])
        if r is None:
            return
        tar_position = (r[2] << 8) | r[1]
        position = (r[4] << 8) | r[3]
        temper = r[5]
        err_status = r[9]
        return tar_position, position, temper, err_status

    def move_all_to(self, p, model=1):
        self.move_to(255, p, model)


inspire_client: InspireClient = InspireClient()

if __name__ == '__main__':
    inspire_client.open("COM7")
    print(inspire_client.move_to(3, 0))
    print(inspire_client.move_to(4, 0))
    print(inspire_client.move_to(5, 0))
    print(inspire_client.move_to(2, 0))
    time.sleep(0.3)
    # print(inspire_client.move_to(2, 500))
    # time.sleep(0.3)
    # print(inspire_client.move_to(3, 500))
    # time.sleep(0.3)
    # print(inspire_client.move_to(4, 500))
    # time.sleep(0.3)
    # print(inspire_client.move_to(5, 800))
    # time.sleep(0.3)
    # print(inspire_client.move_to(6, 500))
    # time.sleep(0.3)
    # print(inspire_client.move_to(7, 500))
    # time.sleep(0.3)
    # print(inspire_client.move_to(8, 500))
    # time.sleep(0.3)
    # print(inspire_client.move_to(9, 500))
    # time.sleep(0.3)
    # print(inspire_client.move_to(10, 500))
    # time.sleep(0.3)
    time.sleep(1)
    inspire_client.move_all_to(0)
