from PyQt5.QtWidgets import QWidget
from PyQt5.QtCore import QTimer

from .ui.axisStatusWidget import Ui_Form
from ..motion_control_card import mcc


class AxisStatusWidget(QWidget, Ui_Form):
    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setupUi(self)

        self.setWindowTitle("轴状态")
        self.resize(900, 700)

        for i in mcc.axis_d:
            getattr(self, f"pushButton_axis_switch_{i}").clicked.connect(self.on_axis_switch)
            getattr(self, f"pushButton_axis_stop_{i}").clicked.connect(self.on_axis_stop)
            getattr(self, f"pushButton_axis_clr_sts_{i}").clicked.connect(self.on_axis_clr_sts)
            getattr(self, f"pushButton_axis_zero_pos_{i}").clicked.connect(self.on_axis_zero_pos)

        self.timer1 = QTimer()
        self.timer1.timeout.connect(self.update_mcc_axis_status)
        self.timer1.start(100)

    def update_mcc_axis_status(self):
        for i in mcc.axis_d:
            state = mcc.axis_d[i].status
            getattr(self, f"lineEdit_e_enc_pos_{i}").setText(str(state.e_enc_pos))
            getattr(self, f"lineEdit_enc_pos_{i}").setText(str(state.axis_enc_pos))
            getattr(self, f"lineEdit_prf_pos_{i}").setText(str(state.axis_prf_pos))

            getattr(self, f"label_alarm_{i}").setText("1" if state.sts & (1 << 1) else "0")
            getattr(self, f"label_pl_alarm_{i}").setText("1" if state.sts & (1 << 5) else "0")
            getattr(self, f"label_nl_alarm_{i}").setText("1" if state.sts & (1 << 6) else "0")

            if not state.sts & (1 << 9):
                getattr(self, f"pushButton_axis_switch_{i}").setText("使能开启")
            else:
                getattr(self, f"pushButton_axis_switch_{i}").setText("使能关闭")

    def on_axis_switch(self):
        sender = self.sender().objectName()
        index = int(sender.split("_")[-1])
        if self.sender().text() == "使能开启":
            mcc.axis_d[index].axis_on()
        else:
            mcc.axis_d[index].axis_off()

    def on_axis_stop(self):
        sender = self.sender().objectName()
        index = int(sender.split("_")[-1])

        mcc.axis_d[index].stop()

    def on_axis_clr_sts(self):
        sender = self.sender().objectName()
        index = int(sender.split("_")[-1])

        mcc.axis_d[index].clr_sts()

    def on_axis_zero_pos(self):
        sender = self.sender().objectName()
        index = int(sender.split("_")[-1])

        mcc.axis_d[index].zero_pos()
