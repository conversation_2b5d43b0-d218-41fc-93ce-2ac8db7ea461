import ctypes

from .Base import gts

if gts is not None:
    gts.GTN_GetSts.argtypes = [
        ctypes.c_short, ctypes.c_short, ctypes.POINTER(ctypes.c_long),
        ctypes.c_short, ctypes.POINTER(ctypes.c_ulong)
    ]
    gts.GTN_GetSts.restype = ctypes.c_short

null_pointer = ctypes.POINTER(ctypes.c_ulong)()


def GTN_GetSts(core, axis, count=1):
    sts = ctypes.c_long(0)
    r = gts.GTN_GetSts(core, axis, ctypes.byref(sts), count, null_pointer)
    return r, sts.value


if gts is not None:
    gts.GTN_GetPrfPos.argtypes = [
        ctypes.c_short, ctypes.c_short, ctypes.POINTER(ctypes.c_double),
        ctypes.c_short, ctypes.POINTER(ctypes.c_ulong)
    ]
    gts.GTN_GetPrfPos.restype = ctypes.c_short


def GTN_GetPrfPos(core, axis, count=1):
    value = ctypes.c_double(0)
    r = gts.GTN_GetPrfPos(core, axis, ctypes.byref(value), count, null_pointer)
    return r, value.value

if gts is not None:
    gts.GTN_GetAxisPrfPos.argtypes = [
        ctypes.c_short, ctypes.c_short, ctypes.POINTER(ctypes.c_double),
        ctypes.c_short, ctypes.POINTER(ctypes.c_ulong)
    ]
    gts.GTN_GetAxisPrfPos.restype = ctypes.c_short


def GTN_GetAxisPrfPos(core, axis, count=1):
    value = ctypes.c_double(0)
    r = gts.GTN_GetAxisPrfPos(core, axis, ctypes.byref(value), count, null_pointer)
    return r, value.value


if gts is not None:
    gts.GTN_GetAxisEncPos.argtypes = [
        ctypes.c_short, ctypes.c_short, ctypes.POINTER(ctypes.c_double),
        ctypes.c_short, ctypes.POINTER(ctypes.c_ulong)
    ]
    gts.GTN_GetAxisEncPos.restype = ctypes.c_short


def GTN_GetAxisEncPos(core, axis, count=1):
    value = ctypes.c_double(0)
    r = gts.GTN_GetAxisEncPos(core, axis, ctypes.byref(value), count, null_pointer)
    return r, value.value


if gts is not None:
    gts.GTN_GetPrfVel.argtypes = [
        ctypes.c_short, ctypes.c_short, ctypes.POINTER(ctypes.c_double),
        ctypes.c_short, ctypes.POINTER(ctypes.c_ulong)
    ]
    gts.GTN_GetPrfVel.restype = ctypes.c_short


def GTN_GetPrfVel(core, axis, count=1):
    value = ctypes.c_double(0)
    r = gts.GTN_GetPrfVel(core, axis, ctypes.byref(value), count, null_pointer)
    return r, value.value


if gts is not None:
    gts.GTN_ClrSts.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_short]
    gts.GTN_ClrSts.restype = ctypes.c_short


def GTN_ClrSts(core, axis, count=1):
    return gts.GTN_ClrSts(core, axis, count)


if gts is not None:
    gts.GTN_Stop.argtypes = [ctypes.c_short, ctypes.c_long, ctypes.c_long]
    gts.GTN_Stop.restype = ctypes.c_short


def GTN_Stop(core, mask, option):
    return gts.GTN_Stop(core, mask, option)
