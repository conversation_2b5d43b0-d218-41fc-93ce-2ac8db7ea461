from PyQt5.QtWidgets import QTableWidget


class GeneralTableWidget(QTableWidget):

    def __init__(self, parent=None):
        super(GeneralTableWidget, self).__init__(parent)
        self.columnPercent = {}
        self.padding = 1.5

    def resizeEvent(self, *args, **kwargs):
        super(GeneralTableWidget, self).resizeEvent(*args, **kwargs)
        self.update_column_width()

    def set_column_percent(self, percent):
        self.columnPercent.update(percent)
        self.update_column_width()

    def update_column_width(self):
        if len(self.columnPercent) < self.columnCount():
            return

        width = self.width() - self.verticalHeader().width() - self.padding
        for i in range(self.columnCount()):
            column_width = int(width * self.columnPercent[i])
            self.setColumnWidth(i, column_width)
