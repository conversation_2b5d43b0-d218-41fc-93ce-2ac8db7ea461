# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'jogWidget.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(1147, 325)
        self.verticalLayout = QtWidgets.QVBoxLayout(Form)
        self.verticalLayout.setObjectName("verticalLayout")
        self.groupBox_2 = QtWidgets.QGroupBox(Form)
        self.groupBox_2.setTitle("")
        self.groupBox_2.setObjectName("groupBox_2")
        self.gridLayout = QtWidgets.QGridLayout(self.groupBox_2)
        self.gridLayout.setObjectName("gridLayout")
        self.label_4 = QtWidgets.QLabel(self.groupBox_2)
        self.label_4.setObjectName("label_4")
        self.gridLayout.addWidget(self.label_4, 0, 3, 1, 1)
        self.label_8 = QtWidgets.QLabel(self.groupBox_2)
        self.label_8.setObjectName("label_8")
        self.gridLayout.addWidget(self.label_8, 0, 7, 1, 1)
        self.label_10 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_10.sizePolicy().hasHeightForWidth())
        self.label_10.setSizePolicy(sizePolicy)
        self.label_10.setObjectName("label_10")
        self.gridLayout.addWidget(self.label_10, 2, 0, 1, 1)
        self.label_2 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_2.sizePolicy().hasHeightForWidth())
        self.label_2.setSizePolicy(sizePolicy)
        self.label_2.setObjectName("label_2")
        self.gridLayout.addWidget(self.label_2, 0, 1, 1, 1)
        self.label_6 = QtWidgets.QLabel(self.groupBox_2)
        self.label_6.setObjectName("label_6")
        self.gridLayout.addWidget(self.label_6, 0, 5, 1, 1)
        self.label_3 = QtWidgets.QLabel(self.groupBox_2)
        self.label_3.setObjectName("label_3")
        self.gridLayout.addWidget(self.label_3, 0, 2, 1, 1)
        self.label_11 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_11.sizePolicy().hasHeightForWidth())
        self.label_11.setSizePolicy(sizePolicy)
        self.label_11.setObjectName("label_11")
        self.gridLayout.addWidget(self.label_11, 3, 0, 1, 1)
        self.label_9 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_9.sizePolicy().hasHeightForWidth())
        self.label_9.setSizePolicy(sizePolicy)
        self.label_9.setObjectName("label_9")
        self.gridLayout.addWidget(self.label_9, 1, 0, 1, 1)
        self.label_12 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_12.sizePolicy().hasHeightForWidth())
        self.label_12.setSizePolicy(sizePolicy)
        self.label_12.setObjectName("label_12")
        self.gridLayout.addWidget(self.label_12, 4, 0, 1, 1)
        self.label = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy)
        self.label.setObjectName("label")
        self.gridLayout.addWidget(self.label, 0, 0, 1, 1)
        self.label_5 = QtWidgets.QLabel(self.groupBox_2)
        self.label_5.setObjectName("label_5")
        self.gridLayout.addWidget(self.label_5, 0, 4, 1, 1)
        self.label_7 = QtWidgets.QLabel(self.groupBox_2)
        self.label_7.setObjectName("label_7")
        self.gridLayout.addWidget(self.label_7, 0, 6, 1, 1)
        self.label_13 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_13.sizePolicy().hasHeightForWidth())
        self.label_13.setSizePolicy(sizePolicy)
        self.label_13.setObjectName("label_13")
        self.gridLayout.addWidget(self.label_13, 5, 0, 1, 1)
        self.label_14 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_14.sizePolicy().hasHeightForWidth())
        self.label_14.setSizePolicy(sizePolicy)
        self.label_14.setObjectName("label_14")
        self.gridLayout.addWidget(self.label_14, 1, 1, 1, 1)
        self.label_15 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_15.sizePolicy().hasHeightForWidth())
        self.label_15.setSizePolicy(sizePolicy)
        self.label_15.setObjectName("label_15")
        self.gridLayout.addWidget(self.label_15, 2, 1, 1, 1)
        self.label_16 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_16.sizePolicy().hasHeightForWidth())
        self.label_16.setSizePolicy(sizePolicy)
        self.label_16.setObjectName("label_16")
        self.gridLayout.addWidget(self.label_16, 3, 1, 1, 1)
        self.label_17 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_17.sizePolicy().hasHeightForWidth())
        self.label_17.setSizePolicy(sizePolicy)
        self.label_17.setObjectName("label_17")
        self.gridLayout.addWidget(self.label_17, 4, 1, 1, 1)
        self.label_18 = QtWidgets.QLabel(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_18.sizePolicy().hasHeightForWidth())
        self.label_18.setSizePolicy(sizePolicy)
        self.label_18.setObjectName("label_18")
        self.gridLayout.addWidget(self.label_18, 5, 1, 1, 1)
        self.doubleSpinBox_j_vel_1 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_vel_1.setDecimals(3)
        self.doubleSpinBox_j_vel_1.setMaximum(1000.0)
        self.doubleSpinBox_j_vel_1.setProperty("value", 50.0)
        self.doubleSpinBox_j_vel_1.setObjectName("doubleSpinBox_j_vel_1")
        self.gridLayout.addWidget(self.doubleSpinBox_j_vel_1, 1, 2, 1, 1)
        self.doubleSpinBox_j_vel_2 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_vel_2.setDecimals(3)
        self.doubleSpinBox_j_vel_2.setMaximum(1000.0)
        self.doubleSpinBox_j_vel_2.setProperty("value", 50.0)
        self.doubleSpinBox_j_vel_2.setObjectName("doubleSpinBox_j_vel_2")
        self.gridLayout.addWidget(self.doubleSpinBox_j_vel_2, 2, 2, 1, 1)
        self.doubleSpinBox_j_vel_3 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_vel_3.setDecimals(3)
        self.doubleSpinBox_j_vel_3.setMaximum(1000.0)
        self.doubleSpinBox_j_vel_3.setProperty("value", 50.0)
        self.doubleSpinBox_j_vel_3.setObjectName("doubleSpinBox_j_vel_3")
        self.gridLayout.addWidget(self.doubleSpinBox_j_vel_3, 3, 2, 1, 1)
        self.doubleSpinBox_j_vel_4 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_vel_4.setDecimals(3)
        self.doubleSpinBox_j_vel_4.setMaximum(1000.0)
        self.doubleSpinBox_j_vel_4.setProperty("value", 50.0)
        self.doubleSpinBox_j_vel_4.setObjectName("doubleSpinBox_j_vel_4")
        self.gridLayout.addWidget(self.doubleSpinBox_j_vel_4, 4, 2, 1, 1)
        self.doubleSpinBox_j_vel_5 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_vel_5.setDecimals(3)
        self.doubleSpinBox_j_vel_5.setMaximum(100.0)
        self.doubleSpinBox_j_vel_5.setProperty("value", 2.0)
        self.doubleSpinBox_j_vel_5.setObjectName("doubleSpinBox_j_vel_5")
        self.gridLayout.addWidget(self.doubleSpinBox_j_vel_5, 5, 2, 1, 1)
        self.doubleSpinBox_j_acc_1 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_acc_1.setDecimals(3)
        self.doubleSpinBox_j_acc_1.setProperty("value", 0.05)
        self.doubleSpinBox_j_acc_1.setObjectName("doubleSpinBox_j_acc_1")
        self.gridLayout.addWidget(self.doubleSpinBox_j_acc_1, 1, 3, 1, 1)
        self.doubleSpinBox_j_acc_2 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_acc_2.setDecimals(3)
        self.doubleSpinBox_j_acc_2.setProperty("value", 0.05)
        self.doubleSpinBox_j_acc_2.setObjectName("doubleSpinBox_j_acc_2")
        self.gridLayout.addWidget(self.doubleSpinBox_j_acc_2, 2, 3, 1, 1)
        self.doubleSpinBox_j_acc_3 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_acc_3.setDecimals(3)
        self.doubleSpinBox_j_acc_3.setProperty("value", 0.05)
        self.doubleSpinBox_j_acc_3.setObjectName("doubleSpinBox_j_acc_3")
        self.gridLayout.addWidget(self.doubleSpinBox_j_acc_3, 3, 3, 1, 1)
        self.doubleSpinBox_j_acc_4 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_acc_4.setDecimals(3)
        self.doubleSpinBox_j_acc_4.setProperty("value", 0.05)
        self.doubleSpinBox_j_acc_4.setObjectName("doubleSpinBox_j_acc_4")
        self.gridLayout.addWidget(self.doubleSpinBox_j_acc_4, 4, 3, 1, 1)
        self.doubleSpinBox_j_acc_5 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_acc_5.setDecimals(3)
        self.doubleSpinBox_j_acc_5.setProperty("value", 0.05)
        self.doubleSpinBox_j_acc_5.setObjectName("doubleSpinBox_j_acc_5")
        self.gridLayout.addWidget(self.doubleSpinBox_j_acc_5, 5, 3, 1, 1)
        self.doubleSpinBox_j_dec_1 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_dec_1.setDecimals(3)
        self.doubleSpinBox_j_dec_1.setProperty("value", 0.05)
        self.doubleSpinBox_j_dec_1.setObjectName("doubleSpinBox_j_dec_1")
        self.gridLayout.addWidget(self.doubleSpinBox_j_dec_1, 1, 4, 1, 1)
        self.doubleSpinBox_j_smh_1 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_smh_1.setDecimals(3)
        self.doubleSpinBox_j_smh_1.setMinimum(0.0)
        self.doubleSpinBox_j_smh_1.setMaximum(0.999)
        self.doubleSpinBox_j_smh_1.setSingleStep(0.01)
        self.doubleSpinBox_j_smh_1.setProperty("value", 0.9)
        self.doubleSpinBox_j_smh_1.setObjectName("doubleSpinBox_j_smh_1")
        self.gridLayout.addWidget(self.doubleSpinBox_j_smh_1, 1, 5, 1, 1)
        self.doubleSpinBox_j_dec_2 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_dec_2.setDecimals(3)
        self.doubleSpinBox_j_dec_2.setProperty("value", 0.05)
        self.doubleSpinBox_j_dec_2.setObjectName("doubleSpinBox_j_dec_2")
        self.gridLayout.addWidget(self.doubleSpinBox_j_dec_2, 2, 4, 1, 1)
        self.doubleSpinBox_j_dec_3 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_dec_3.setDecimals(3)
        self.doubleSpinBox_j_dec_3.setProperty("value", 0.05)
        self.doubleSpinBox_j_dec_3.setObjectName("doubleSpinBox_j_dec_3")
        self.gridLayout.addWidget(self.doubleSpinBox_j_dec_3, 3, 4, 1, 1)
        self.doubleSpinBox_j_dec_4 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_dec_4.setDecimals(3)
        self.doubleSpinBox_j_dec_4.setProperty("value", 0.05)
        self.doubleSpinBox_j_dec_4.setObjectName("doubleSpinBox_j_dec_4")
        self.gridLayout.addWidget(self.doubleSpinBox_j_dec_4, 4, 4, 1, 1)
        self.doubleSpinBox_j_dec_5 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_dec_5.setDecimals(3)
        self.doubleSpinBox_j_dec_5.setProperty("value", 0.05)
        self.doubleSpinBox_j_dec_5.setObjectName("doubleSpinBox_j_dec_5")
        self.gridLayout.addWidget(self.doubleSpinBox_j_dec_5, 5, 4, 1, 1)
        self.doubleSpinBox_j_smh_2 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_smh_2.setDecimals(3)
        self.doubleSpinBox_j_smh_2.setMaximum(0.999)
        self.doubleSpinBox_j_smh_2.setSingleStep(0.01)
        self.doubleSpinBox_j_smh_2.setProperty("value", 0.9)
        self.doubleSpinBox_j_smh_2.setObjectName("doubleSpinBox_j_smh_2")
        self.gridLayout.addWidget(self.doubleSpinBox_j_smh_2, 2, 5, 1, 1)
        self.doubleSpinBox_j_smh_3 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_smh_3.setDecimals(3)
        self.doubleSpinBox_j_smh_3.setMaximum(0.999)
        self.doubleSpinBox_j_smh_3.setSingleStep(0.01)
        self.doubleSpinBox_j_smh_3.setProperty("value", 0.9)
        self.doubleSpinBox_j_smh_3.setObjectName("doubleSpinBox_j_smh_3")
        self.gridLayout.addWidget(self.doubleSpinBox_j_smh_3, 3, 5, 1, 1)
        self.doubleSpinBox_j_smh_4 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_smh_4.setDecimals(3)
        self.doubleSpinBox_j_smh_4.setMaximum(0.999)
        self.doubleSpinBox_j_smh_4.setSingleStep(0.01)
        self.doubleSpinBox_j_smh_4.setProperty("value", 0.9)
        self.doubleSpinBox_j_smh_4.setObjectName("doubleSpinBox_j_smh_4")
        self.gridLayout.addWidget(self.doubleSpinBox_j_smh_4, 4, 5, 1, 1)
        self.doubleSpinBox_j_smh_5 = QtWidgets.QDoubleSpinBox(self.groupBox_2)
        self.doubleSpinBox_j_smh_5.setDecimals(3)
        self.doubleSpinBox_j_smh_5.setMaximum(0.999)
        self.doubleSpinBox_j_smh_5.setSingleStep(0.01)
        self.doubleSpinBox_j_smh_5.setProperty("value", 0.9)
        self.doubleSpinBox_j_smh_5.setObjectName("doubleSpinBox_j_smh_5")
        self.gridLayout.addWidget(self.doubleSpinBox_j_smh_5, 5, 5, 1, 1)
        self.pushButton_f_move_1 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_f_move_1.setObjectName("pushButton_f_move_1")
        self.gridLayout.addWidget(self.pushButton_f_move_1, 1, 6, 1, 1)
        self.pushButton_f_move_2 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_f_move_2.setObjectName("pushButton_f_move_2")
        self.gridLayout.addWidget(self.pushButton_f_move_2, 2, 6, 1, 1)
        self.pushButton_f_move_3 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_f_move_3.setObjectName("pushButton_f_move_3")
        self.gridLayout.addWidget(self.pushButton_f_move_3, 3, 6, 1, 1)
        self.pushButton_f_move_4 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_f_move_4.setObjectName("pushButton_f_move_4")
        self.gridLayout.addWidget(self.pushButton_f_move_4, 4, 6, 1, 1)
        self.pushButton_f_move_5 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_f_move_5.setObjectName("pushButton_f_move_5")
        self.gridLayout.addWidget(self.pushButton_f_move_5, 5, 6, 1, 1)
        self.pushButton_c_move_1 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_c_move_1.setObjectName("pushButton_c_move_1")
        self.gridLayout.addWidget(self.pushButton_c_move_1, 1, 7, 1, 1)
        self.pushButton_c_move_2 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_c_move_2.setObjectName("pushButton_c_move_2")
        self.gridLayout.addWidget(self.pushButton_c_move_2, 2, 7, 1, 1)
        self.pushButton_c_move_3 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_c_move_3.setObjectName("pushButton_c_move_3")
        self.gridLayout.addWidget(self.pushButton_c_move_3, 3, 7, 1, 1)
        self.pushButton_c_move_4 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_c_move_4.setObjectName("pushButton_c_move_4")
        self.gridLayout.addWidget(self.pushButton_c_move_4, 4, 7, 1, 1)
        self.pushButton_c_move_5 = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_c_move_5.setObjectName("pushButton_c_move_5")
        self.gridLayout.addWidget(self.pushButton_c_move_5, 5, 7, 1, 1)
        self.verticalLayout.addWidget(self.groupBox_2)
        spacerItem = QtWidgets.QSpacerItem(20, 79, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.label_4.setText(_translate("Form", "加速度（pulse/m^2）"))
        self.label_8.setText(_translate("Form", "反向运动"))
        self.label_10.setText(_translate("Form", "1"))
        self.label_2.setText(_translate("Form", "轴"))
        self.label_6.setText(_translate("Form", "平滑系数"))
        self.label_3.setText(_translate("Form", "速度（pulse/ms）"))
        self.label_11.setText(_translate("Form", "1"))
        self.label_9.setText(_translate("Form", "1"))
        self.label_12.setText(_translate("Form", "1"))
        self.label.setText(_translate("Form", "核"))
        self.label_5.setText(_translate("Form", "减速度（pulse/ms^2）"))
        self.label_7.setText(_translate("Form", "正向运动"))
        self.label_13.setText(_translate("Form", "1"))
        self.label_14.setText(_translate("Form", "1"))
        self.label_15.setText(_translate("Form", "2"))
        self.label_16.setText(_translate("Form", "3"))
        self.label_17.setText(_translate("Form", "4"))
        self.label_18.setText(_translate("Form", "5"))
        self.pushButton_f_move_1.setText(_translate("Form", "正向"))
        self.pushButton_f_move_2.setText(_translate("Form", "正向"))
        self.pushButton_f_move_3.setText(_translate("Form", "正向"))
        self.pushButton_f_move_4.setText(_translate("Form", "正向"))
        self.pushButton_f_move_5.setText(_translate("Form", "正向"))
        self.pushButton_c_move_1.setText(_translate("Form", "反向"))
        self.pushButton_c_move_2.setText(_translate("Form", "反向"))
        self.pushButton_c_move_3.setText(_translate("Form", "反向"))
        self.pushButton_c_move_4.setText(_translate("Form", "反向"))
        self.pushButton_c_move_5.setText(_translate("Form", "反向"))
