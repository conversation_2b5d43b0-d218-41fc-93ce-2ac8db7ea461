from photics.light_sensor_tools.light_sensor.ColorTempLightIntensityClient import \
    color_temp_light_intensity_client
from photics.light_sensor_tools.light_sensor.ColorTempLightSource import color_temp_light_source


class ColorTempLightSensor:
    def __init__(self):
        super().__init__()

    @staticmethod
    def set_light_color_temp(i):
        """
        设置光源色温
        @param i: 色温值
        @return:
        """
        return color_temp_light_source.set_color_temp(i)

    @staticmethod
    def get_color_temperature_light_intensity():
        """
        获取光源色温和照度
        @return:
        """
        return color_temp_light_source.get_color_temperature_light_intensity()

    @staticmethod
    def set_light_intensity(i):
        """
        设置光源照度
        @param i: 照度值
        @return:
        """
        return color_temp_light_source.set_light_intensity(i)

    @staticmethod
    def get_light_source_status():
        """
        获取光源连接状态
        @return:
        """
        return color_temp_light_source.is_open()

    @staticmethod
    def get_light_color_temp():
        """
        获取照度计色温值
        @return:
        """
        return color_temp_light_intensity_client.get_color_temp()

    @staticmethod
    def get_light_intensity():
        """
        获取照度计照度值
        @return:
        """
        return color_temp_light_intensity_client.get_light_intensity()


light_sensor: ColorTempLightSensor = ColorTempLightSensor()
