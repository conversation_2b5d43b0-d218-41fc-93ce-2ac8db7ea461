from PyQt5.QtGui import QTextCursor
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QPushButton

from ..ui.IT_M3200PWidget import Ui_Form
from ..tools.it_m3200_client import it_m3200_client
from ..tools.it_m3200_control import it_m3200_control
from .ListStepWidget import ListStepWidget
from .VoltItemWidget import VoltItemWidget
from ..constants import CONF, save_conf


class ITM3200PowerWidget(QWidget, Ui_Form):

    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setupUi(self)
        self.setWindowTitle("IT-M3200程控电源")
        self.conf = {}
        self.steps = []
        widget = QWidget()
        self.pushButton_add = QPushButton("增加", self)
        self.step_layout = QVBoxLayout(widget)
        self.step_layout.addWidget(self.pushButton_add)
        self.step_layout.addStretch()
        self.scrollArea.setWidget(widget)

        self.volt_items = []
        widget = QWidget()
        self.pushButton_3_add = QPushButton("增加", self)
        self.volt_item_layout = QVBoxLayout(widget)
        self.volt_item_layout.addWidget(self.pushButton_3_add)
        self.volt_item_layout.addStretch()
        self.scrollArea_3_s.setWidget(widget)

        self.pushButton_add.clicked.connect(self.add_step)
        self.pushButton_3_add.clicked.connect(self.add_volt_item)
        self.comboBox_list_index.currentTextChanged.connect(self.on_list_index_changed)
        self.pushButton_save.clicked.connect(self.on_save)
        self.pushButton_save_exec.clicked.connect(self.on_save_exec)
        self.pushButton_exec.clicked.connect(self.on_exec)
        self.pushButton_connect_device.clicked.connect(self.on_conn)
        self.pushButton_clear.clicked.connect(self.on_clear)
        self.pushButton_trigger.clicked.connect(self.on_trigger)
        self.pushButton_pause.clicked.connect(self.on_pause)
        self.pushButton_pause_release.clicked.connect(self.on_pause_release)
        self.pushButton_stop.clicked.connect(self.on_stop)

        self.pushButton_f_exec.clicked.connect(self.on_f_exec)
        self.pushButton_f_off.clicked.connect(self.on_f_off)
        self.pushButton_f_on.clicked.connect(self.on_f_on)

        it_m3200_client.msg_log_signal.connect(self.on_msg_log)

        self.on_list_index_changed("1")

        self.pushButton_1_start.clicked.connect(self.on_1_start)
        self.pushButton_1_stop.clicked.connect(self.on_control_stop)
        self.pushButton_2_start.clicked.connect(self.on_2_start)
        self.pushButton_2_stop.clicked.connect(self.on_control_stop)
        self.pushButton_3_start.clicked.connect(self.on_3_start)
        self.pushButton_3_stop.clicked.connect(self.on_control_stop)
        self.pushButton_4_start.clicked.connect(self.on_4_start)
        self.pushButton_4_stop.clicked.connect(self.on_control_stop)

        it_m3200_control.remain_count_signal.connect(self.on_remain_count_changed)
        it_m3200_control.remain_count2_signal.connect(self.on_remain_count2_changed)

    def add_step(self):
        if len(self.steps) >= 100:
            return
        s = ListStepWidget(pw=self)
        self.steps.append(s)
        self.step_layout.insertWidget(len(self.steps), s)

        return s

    def add_volt_item(self):
        s = VoltItemWidget(pw=self)
        self.volt_items.append(s)
        self.volt_item_layout.insertWidget(len(self.steps), s)

    def on_list_index_changed(self, index):
        self.conf = CONF.get(index, {})
        voltage = self.conf.get("voltage", 0)
        current = self.conf.get("current", 0)
        function = self.conf.get("function", "")
        terminate = self.conf.get("terminate", "")
        repeat = self.conf.get("repeat", 1)
        steps = self.conf.get("steps", [])

        self.doubleSpinBox_volt.setValue(voltage)
        self.doubleSpinBox_curr.setValue(current)
        self.comboBox_list_func.setCurrentText(function)
        self.comboBox_list_term.setCurrentText(terminate)
        self.spinBox_list_repeat.setValue(repeat)

        for i in self.steps:
            i.close()
        self.steps = []
        for step in steps:
            voltage = step.get("voltage")
            current = step.get("current")
            slew = step.get("slew")
            width = step.get("width")

            s = self.add_step()
            s.doubleSpinBox_volt.setValue(voltage)
            s.doubleSpinBox_curr.setValue(current)
            s.doubleSpinBox_slope.setValue(slew)
            s.doubleSpinBox_time.setValue(width)

    def _save(self):
        voltage = self.doubleSpinBox_volt.value()
        current = self.doubleSpinBox_curr.value()
        function = self.comboBox_list_func.currentText()
        terminate = self.comboBox_list_term.currentText()
        repeat = self.spinBox_list_repeat.value()

        steps = []
        for i in self.steps:
            steps.append({
                "voltage": i.doubleSpinBox_volt.value(),
                "current": i.doubleSpinBox_curr.value(),
                "slew": i.doubleSpinBox_slope.value(),
                "width": i.doubleSpinBox_time.value()
            })

        self.conf.update({
            "voltage": voltage,
            "current": current,
            "function": function,
            "terminate": terminate,
            "repeat": repeat,
            "steps": steps
        })

    def on_save(self):
        self._save()
        save_conf()

    def on_save_exec(self):
        self.on_save()
        index = self.comboBox_list_index.currentText()
        it_m3200_client.run_list(index)

    def on_exec(self):
        self._save()
        index = self.comboBox_list_index.currentText()
        it_m3200_client.run_list(index)

    def on_conn(self):
        if self.pushButton_connect_device.text() == "连接设备":
            it_m3200_client.open()
            self.pushButton_connect_device.setText("断开连接")
        else:
            it_m3200_client.close()
            self.pushButton_connect_device.setText("连接设备")

    def on_msg_log(self, msg):
        self.plainTextEdit.appendPlainText(msg)
        self.plainTextEdit.moveCursor(QTextCursor.End)

    def on_clear(self):
        self.plainTextEdit.clear()

    def on_trigger(self):
        it_m3200_client.trigger()

    def on_pause(self):
        it_m3200_client.pause(1)

    def on_pause_release(self):
        it_m3200_client.pause(0)

    def on_stop(self):
        it_m3200_client.stop()

    def on_f_exec(self):
        mode = self.comboBox_f_mode.currentText()
        volt = self.doubleSpinBox_f_volt.value()
        curr = self.doubleSpinBox_f_curr.value()
        it_m3200_client.run_fixed(mode, volt, curr)

    def on_f_off(self):
        it_m3200_client.f_off()

    def on_f_on(self):
        it_m3200_client.f_on()

    def on_1_start(self):
        check = self.checkBox_1_random_time_enable.checkState()
        volt = self.doubleSpinBox_1_volt.value()
        on_time = self.doubleSpinBox_1_on_time.value()
        off_time = self.doubleSpinBox_1_off_time.value()
        on_time_s = self.doubleSpinBox_1_on_time_s.value()
        on_time_e = self.doubleSpinBox_1_on_time_e.value()
        off_time_s = self.doubleSpinBox_1_off_time_s.value()
        off_time_e = self.doubleSpinBox_1_off_time_e.value()

        if not check:
            it_m3200_control.start_on_off_c(volt, on_time, off_time)
        else:
            it_m3200_control.start_on_off_c2(volt, on_time_s, on_time_e, off_time_s, off_time_e)

    def on_control_stop(self):
        it_m3200_control.stop()

    def on_2_start(self):
        volt1 = self.doubleSpinBox_2_volt1.value()
        volt2 = self.doubleSpinBox_2_volt2.value()
        interval = self.doubleSpinBox_2_interval.value()
        repeat = self.spinBox_2_repeat.value()
        it_m3200_control.start_high_low_volt_c(volt1, volt2, interval, repeat)

    def on_3_start(self):
        f = self.checkBox_3_volt_list_enable.checkState()
        volt_s = self.doubleSpinBox_3_volt_s.value()
        volt_e = self.doubleSpinBox_3_volt_e.value()
        interval = self.doubleSpinBox_3_internal.value()
        if not f:
            it_m3200_control.start_random_volt_c(volt_s, volt_e, interval)
        else:
            volt_list = []
            for i in self.volt_items:
                volt_list.append({
                    "volt": i.doubleSpinBox_volt.value(),
                    "time": i.doubleSpinBox_time.value()
                })
            it_m3200_control.start_random_volt_c2(volt_list)

    def on_4_start(self):
        s_volt = self.doubleSpinBox_4_volt_s.value()
        e_volt = self.doubleSpinBox_4_volt_e.value()
        step = self.doubleSpinBox_4_step.value()
        t = self.doubleSpinBox_4_time.value()
        repeat = self.spinBox_4_repeat.value()
        mode = self.comboBox_4_mode.currentText()

        it_m3200_control.start_step_volt_c(s_volt, e_volt, step, t, repeat, mode)

        print("11111111111111111111111111111111111111111")

    def on_remain_count_changed(self, count):
        if count < 0:
            self.lineEdit_2_remain_repeat.setText("oo")
        else:
            self.lineEdit_2_remain_repeat.setText(str(count))

    def on_remain_count2_changed(self, count):
        if count < 0:
            self.lineEdit_4_remain_repeat.setText("oo")
        else:
            self.lineEdit_4_remain_repeat.setText(str(count))
