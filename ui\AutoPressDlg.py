# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'AutoPressDlg.ui'
#
# Created by: PyQt5 UI code generator 5.15.6
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_Dialog(object):
    def setupUi(self, Dialog):
        Dialog.setObjectName("Dialog")
        Dialog.resize(1600, 900)
        Dialog.setMinimumSize(QtCore.QSize(1600, 900))
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(Dialog)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.groupBox = QtWidgets.QGroupBox(Dialog)
        self.groupBox.setObjectName("groupBox")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.groupBox)
        self.verticalLayout.setObjectName("verticalLayout")
        self.formLayout = QtWidgets.QFormLayout()
        self.formLayout.setObjectName("formLayout")
        self.label = QtWidgets.QLabel(self.groupBox)
        self.label.setObjectName("label")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label)
        self.label_2 = QtWidgets.QLabel(self.groupBox)
        self.label_2.setObjectName("label_2")
        self.formLayout.setWidget(1, QtWidgets.QFormLayout.LabelRole, self.label_2)
        self.label_3 = QtWidgets.QLabel(self.groupBox)
        self.label_3.setObjectName("label_3")
        self.formLayout.setWidget(3, QtWidgets.QFormLayout.LabelRole, self.label_3)
        self.spinBox_repeat = QtWidgets.QSpinBox(self.groupBox)
        self.spinBox_repeat.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_repeat.setMaximum(999999999)
        self.spinBox_repeat.setProperty("value", 10000)
        self.spinBox_repeat.setObjectName("spinBox_repeat")
        self.formLayout.setWidget(3, QtWidgets.QFormLayout.FieldRole, self.spinBox_repeat)
        self.doubleSpinBox_position = QtWidgets.QDoubleSpinBox(self.groupBox)
        self.doubleSpinBox_position.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_position.setDecimals(5)
        self.doubleSpinBox_position.setMinimum(-9.0)
        self.doubleSpinBox_position.setMaximum(0.0)
        self.doubleSpinBox_position.setObjectName("doubleSpinBox_position")
        self.formLayout.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_position)
        self.doubleSpinBox_press = QtWidgets.QDoubleSpinBox(self.groupBox)
        self.doubleSpinBox_press.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_press.setMaximum(9999999999999.99)
        self.doubleSpinBox_press.setObjectName("doubleSpinBox_press")
        self.formLayout.setWidget(1, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_press)
        self.label_6 = QtWidgets.QLabel(self.groupBox)
        self.label_6.setObjectName("label_6")
        self.formLayout.setWidget(2, QtWidgets.QFormLayout.LabelRole, self.label_6)
        self.doubleSpinBox_up = QtWidgets.QDoubleSpinBox(self.groupBox)
        self.doubleSpinBox_up.setMinimumSize(QtCore.QSize(0, 45))
        self.doubleSpinBox_up.setObjectName("doubleSpinBox_up")
        self.formLayout.setWidget(2, QtWidgets.QFormLayout.FieldRole, self.doubleSpinBox_up)
        self.verticalLayout.addLayout(self.formLayout)
        self.horizontalLayout_2.addWidget(self.groupBox)
        self.groupBox_2 = QtWidgets.QGroupBox(Dialog)
        self.groupBox_2.setObjectName("groupBox_2")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.groupBox_2)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.pushButton_start = QtWidgets.QPushButton(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton_start.sizePolicy().hasHeightForWidth())
        self.pushButton_start.setSizePolicy(sizePolicy)
        self.pushButton_start.setObjectName("pushButton_start")
        self.horizontalLayout.addWidget(self.pushButton_start)
        self.pushButton_pause = QtWidgets.QPushButton(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton_pause.sizePolicy().hasHeightForWidth())
        self.pushButton_pause.setSizePolicy(sizePolicy)
        self.pushButton_pause.setObjectName("pushButton_pause")
        self.horizontalLayout.addWidget(self.pushButton_pause)
        self.pushButton_end = QtWidgets.QPushButton(self.groupBox_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton_end.sizePolicy().hasHeightForWidth())
        self.pushButton_end.setSizePolicy(sizePolicy)
        self.pushButton_end.setObjectName("pushButton_end")
        self.horizontalLayout.addWidget(self.pushButton_end)
        self.horizontalLayout_2.addWidget(self.groupBox_2)
        self.horizontalLayout_2.setStretch(0, 1)
        self.horizontalLayout_2.setStretch(1, 1)
        self.verticalLayout_3.addLayout(self.horizontalLayout_2)
        self.groupBox_3 = QtWidgets.QGroupBox(Dialog)
        self.groupBox_3.setObjectName("groupBox_3")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.groupBox_3)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.formLayout_2 = QtWidgets.QFormLayout()
        self.formLayout_2.setObjectName("formLayout_2")
        self.label_4 = QtWidgets.QLabel(self.groupBox_3)
        self.label_4.setObjectName("label_4")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_4)
        self.label_5 = QtWidgets.QLabel(self.groupBox_3)
        self.label_5.setObjectName("label_5")
        self.formLayout_2.setWidget(1, QtWidgets.QFormLayout.LabelRole, self.label_5)
        self.lineEdit_status = QtWidgets.QLineEdit(self.groupBox_3)
        self.lineEdit_status.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_status.setObjectName("lineEdit_status")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.lineEdit_status)
        self.lineEdit_press = QtWidgets.QLineEdit(self.groupBox_3)
        self.lineEdit_press.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_press.setObjectName("lineEdit_press")
        self.formLayout_2.setWidget(1, QtWidgets.QFormLayout.FieldRole, self.lineEdit_press)
        self.verticalLayout_2.addLayout(self.formLayout_2)
        self.progressBar = QtWidgets.QProgressBar(self.groupBox_3)
        self.progressBar.setProperty("value", 24)
        self.progressBar.setObjectName("progressBar")
        self.verticalLayout_2.addWidget(self.progressBar)
        self.verticalLayout_3.addWidget(self.groupBox_3)
        self.verticalLayout_3.setStretch(0, 1)

        self.retranslateUi(Dialog)
        QtCore.QMetaObject.connectSlotsByName(Dialog)

    def retranslateUi(self, Dialog):
        _translate = QtCore.QCoreApplication.translate
        Dialog.setWindowTitle(_translate("Dialog", "Dialog"))
        self.groupBox.setTitle(_translate("Dialog", "配置"))
        self.label.setText(_translate("Dialog", "电机运动位置："))
        self.label_2.setText(_translate("Dialog", "电机按下时间："))
        self.label_3.setText(_translate("Dialog", "循环次数："))
        self.label_6.setText(_translate("Dialog", "电机抬起时间："))
        self.groupBox_2.setTitle(_translate("Dialog", "控制"))
        self.pushButton_start.setText(_translate("Dialog", "开始"))
        self.pushButton_pause.setText(_translate("Dialog", "暂停"))
        self.pushButton_end.setText(_translate("Dialog", "结束"))
        self.groupBox_3.setTitle(_translate("Dialog", "进度"))
        self.label_4.setText(_translate("Dialog", "工作状态："))
        self.label_5.setText(_translate("Dialog", "按压次数："))
