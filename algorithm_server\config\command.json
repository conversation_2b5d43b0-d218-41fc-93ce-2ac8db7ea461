{
    "command":
    [
        //流程类指令
        {
            "type":"sleep",
            "code":"0x00010000",
            "in":
            {
                "time":"integer"
            },
            "out":
            {
                "result":"integer"
            }
        },
        {
            "type":"end",
            "code":"0x00010001",
            "control":true
        },
        {
            "type":"jump",
            "code":"0x00010002",
            "control":true,
            "in":
            {
                "tag":"string"
            }
        },
        {
            "type":"jump_if",
            "code":"0x00010003",
            "control":true,
            "in":
            {
                "tag":"string",
                "condition":"integer"
            }
        },
        {
            "type":"run_sequence",
            "code":"0x00010100",
            "in":
            {
                "name":"string",
                "para":"string"
            }
        },
        {
            "type":"call_sequence",
            "code":"0x00010101",
            "in":
            {
                "name":"string",
                "para":"string"
            },
            "out":
            {
                "result":"integer",
                "run_result":"string"
            }
        },
        {
            "type":"multi_run_sequence",
            "code":"0x00010110",
            "in":
            {
                "name":"string",
                "para":"vstring"
            }
        },
        {
            "type":"multi_call_sequence",
            "code":"0x00010111",
            "in":
            {
                "name":"string",
                "para":"vstring"
            },
            "out":
            {
                "result":"integer",
                "run_result":"vstring"
            }
        },
        {
            "type":"set_result",
            "code":"0x00010200",
            "in":
            {
                "input":"string"
            }
        },
        //通信类指令
        {
            "type":"send_s7_client",
            "code":"0x00100000",
            "in":
            {
                "client":"string",
                "db":"integer",
                "offset":"integer",
                "type":"string",
                "content":"string"
            },
            "out":
            {
                "result":"integer"
            }
        },
        {
            "type":"send_tcp_server",
            "code":"0x00100001",
            "in":
            {
                "server":"string",
                "content":"string"
            },
            "out":
            {
                "result":"integer"
            }
        },
        {
            "type":"send_modbus_master",
            "code":"0x00100002",
            "in":
            {
                "master":"string",
                "offset":"integer",
                "type":"string",
                "content":"string"
            },
            "out":
            {
                "result":"integer"
            }
        },
        {
            "type":"send_tcp_client",
            "code":"0x00100003",
            "in":
            {
                "client":"string",
                "content":"string"
            },
            "out":
            {
                "result":"integer"
            }
        },
        {
            "type":"send_light",
            "code":"0x00100004",
            "in":
            {
                "name":"string",
                "bit":"integer",
                "power":"integer"
            },
            "out":
            {
                "result":"integer"
            }
        },
        {
            "type":"read_s7_client",
            "code":"0x00100010",
            "in":
            {
                "client":"string",
                "db":"integer",
                "offset":"integer",
                "type":"string"
            },
            "out":
            {
                "result":"integer",
                "content":"string"
            }
        },
        {
            "type":"read_modbus_master",
            "code":"0x00100012",
            "in":
            {
                "master":"string",
                "offset":"integer",
                "type":"string"
            },
            "out":
            {
                "result":"integer",
                "content":"string"
            }
        },
        {
            "type":"set_trigger_enabled",
            "code":"0x00101000",
            "in":
            {
                "type":"string",
                "name":"string",
                "enabled":"integer"
            },
            "out":
            {
                "result":"integer"
            }
            
        },
        //图像源类指令
        {
            "type":"get_camera_frame",
            "code":"0x00200000",
            "in":
            {
                "camera":"string",
                "exposure":"integer",
                "gain":"float"
            },
            "out":
            {
                "result":"integer",
                "image":"mat"
            }
        },
        {
            "type":"read_image",
            "code":"0x00200001",
            "in":
            {
                "path":"string"
            },
            "out":
            {
                "result":"integer",
                "image":"mat"
            }
        },
        {
            "type":"save_image",
            "code":"0x00200010",
            "in":
            {
                "image":"mat",
                "path":"string",
                "name":"string"
            },
            "out":
            {
                "result":"integer",
                "full_name":"string"
            }
        },
        {
            "type":"draw_lines",
            "code":"0x00200020",
            "in":
            {
                "image":"mat",
                "line":"vline"
            },
            "out":
            {
                "result":"integer",
                "image":"mat"
            }
        },
        {
            "type":"draw_ellipse",
            "code":"0x00200021",
            "in":
            {
                "image":"mat",
                "x":"float",
                "y":"float",
                "a":"float",
                "b":"float",
                "angle":"float"
            },
            "out":
            {
                "result":"integer",
                "image":"mat"
            }
        },
        {
            "type":"draw_line",
            "code":"0x00200022",
            "in":
            {
                "image":"mat",
                "x1":"float",
                "y1":"float",
                "x2":"float",
                "y2":"float"
            },
            "out":
            {
                "result":"integer",
                "image":"mat"
            }
        },
        {
            "type":"get_image_info",
            "code":"0x00200030",
            "in":
            {
                "image":"mat"
            },
            "out":
            {
                "result":"integer",
                "width":"integer",
                "height":"integer",
                "channel":"integer"
            }
        },
        {
            "type":"get_line_info",
            "code":"0x00200031",
            "in":
            {
                "line":"vline",
                "index":"integer"
            },
            "out":
            {
                "result":"integer",
                "xs":"float",
                "ys":"float",
                "xe":"float",
                "ye":"float"
            }
        },
        {
            "type":"creat_line",
            "code":"0x00200040",
            "in":
            {
                "x_start":"float",
                "y_start":"float",
                "x_end":"float",
                "y_end":"float"
            },
            "out":
            {
                "result":"integer",
                "line":"vline"
            }
        },
        //初级算法类指令
        {
            "type":"calc_formula",
            "code":"0x00300000",
            "in":
            {
                "formula":"string"                
            },
            "out":
            {
                "result":"integer",
                "value":"float",
                "value_i":"integer"
            }
        },
        {
            "type":"float_to_integer",
            "code":"0x00300001",
            "in":
            {
                "float":"float"                
            },
            "out":
            {
                "result":"integer",
                "integer":"integer"
            }
        },
        {
            "type":"get_roi",
            "code":"0x00300100",
            "in":
            {
                "image":"mat",
                "x":"integer",
                "y":"integer",
                "h":"integer",
                "w":"integer"
            },
            "out":
            {
                "result":"integer",
                "roi":"mat",
                "debug_img":"mat"
            }
        },
        {
            "type":"get_channel",
            "code":"0x00300101",
            "in":
            {
                "image":"mat",
                "type":"string",
                "channel":"string"
            },
            "out":
            {
                "result":"integer",
                "1channel":"mat"
            }
        },
        {
            "type":"calc_mean",
            "code":"0x00300110",
            "in":
            {
                "image":"mat"
            },
            "out":
            {
                "result":"integer",
                "mean":"float"
            }
        },
        {
            "type":"bilateral_filter",
            "code":"0x00300120",
            "in":
            {
                "image":"mat",
                "d":"integer",
                "color":"float",
                "space":"float"
            },
            "out":
            {
                "result":"integer",
                "image":"mat"
            }
        },
        {
            "type":"canny",
            "code":"0x00300130",
            "in":
            {
                "image":"mat",
                "threshold1":"float",
                "threshold2":"float",
                "size":"integer",
                "gradient":"integer"
            },
            "out":
            {
                "result":"integer",
                "image":"mat"
            }
        },
        {
            "type":"hough_lines",
            "code":"0x00300140",
            "in":
            {
                "image":"mat",
                "rho":"float",
                "theta":"float",
                "threshold":"integer",
                "srn":"float",
                "stn":"float",
                "min_theta":"float",
                "max_theta":"float"
            },
            "out":
            {
                "result":"integer",
                "line":"vline",
                "debug_img":"mat"
            }
        },
        {
            "type":"calc_line_cross_rect",
            "code":"0x00300150",
            "in":
            {
                "line":"vline",
                "x1":"float",
                "y1":"float",
                "x2":"float",
                "y2":"float"
            },
            "out":
            {
                "result":"integer",
                "line":"vline"
            }
        },
        {
            "type":"line_offset",
            "code":"0x00300151",
            "in":
            {
                "line":"vline",
                "x":"float",
                "y":"float"
            },
            "out":
            {
                "result":"integer",
                "line":"vline"
            }
        },
        {
            "type":"calc_line_distance",
            "code":"0x00300152",
            "in":
            {
                "line_1":"vline",
                "line_2":"vline"
            },
            "out":
            {
                "result":"integer",
                "distance":"float",
                "x":"float",
                "y":"float"
            }
        },
        {
            "type":"calc_line_angle",
            "code":"0x00300153",
            "in":
            {
                "line_1":"vline",
                "line_2":"vline"
            },
            "out":
            {
                "result":"integer",
                "angle":"float"
            }
        },
        {
            "type":"pyramid",
            "code":"0x00300160",
            "in":
            {
                "image":"mat",
                "type":"string",
                "count":"integer"
            },
            "out":
            {
                "result":"integer",
                "image_result":"mat"
            }
        },
        {
            "type":"point_aff",
            "code":"0x00300170",
            "in":
            {
                "x_in":"float",
                "y_in":"float",
                "A":"float",
                "B":"float",
                "C":"float",
                "D":"float",
                "E":"float",
                "F":"float"
            },
            "out":
            {
                "result":"integer",
                "x_out":"float",
                "y_out":"float"
            }
        },
        {
            "type":"bounding_box",
            "code":"0x00300180",
            "in":
            {
                "x":"float",
                "y":"float",
                "w":"float",
                "h":"float",
                "angle":"float"
            },
            "out":
            {
                "result":"integer",
                "xmin":"float",
                "ymin":"float",
                "xmax":"float",
                "ymax":"float"
            }
        },
        {
            "type":"bounding_box_scale",
            "code":"0x00300181",
            "in":
            {
                "xmin_in":"float",
                "ymin_in":"float",
                "xmax_in":"float",
                "ymax_in":"float",
                "scale":"float"
            },
            "out":
            {
                "result":"integer",
                "xmin_out":"float",
                "ymin_out":"float",
                "xmax_out":"float",
                "ymax_out":"float"
            }
        },
        {
            "type":"multiply",
            "code":"0x00300190",
            "in":
            {
                "image_in":"mat",
                "mul":"float"
            },
            "out":
            {
                "result":"integer",
                "image_out":"mat"
            }
        },
        {
            "type":"gamma",
            "code":"0x00300191",
            "in":
            {
                "image_in":"mat",
                "gamma":"float"
            },
            "out":
            {
                "result":"integer",
                "image_out":"mat"
            }
        },
        {
            "type":"find_chessboard",
            "code":"0x003001A0",
            "in":
            {
                "image_in":"mat",
                "w":"integer",
                "h":"integer",
                "flag":"integer"
            },
            "out":
            {
                "result":"integer",
                "x":"vfloat",
                "y":"vfloat",
                "image_debug":"mat"
            }
        },
        {
            "type":"estimate_affine2D",
            "code":"0x003001B0",
            "in":
            {
                "from_point_x":"vfloat",
                "from_point_y":"vfloat",
                "to_point_x":"vfloat",
                "to_point_y":"vfloat"
            },
            "out":
            {
                "result":"integer",
                "A":"float",
                "B":"float",
                "C":"float",
                "D":"float",
                "E":"float",
                "F":"float"
            }
        },
        {
            "type":"blob_detect",
            "code":"0x003001C0",
            "in":
            {
                "image":"mat",
                "min_threshold":"float",
                "max_threshold":"float",
                "min_dist":"float",
                "filter_by_area":"integer",
                "min_area":"float",
                "max_area":"float",
                "filter_by_circularity":"integer",
                "min_circularity":"float",
                "max_circularity":"float",
                "filter_by_inertia":"integer",
                "min_inertia":"float",
                "max_inertia":"float",
                "filter_by_convexity":"integer",
                "min_convexity":"float",
                "max_convexity":"float"
            },
            "out":
            {
                "result":"integer",
                "x":"vfloat",
                "y":"vfloat",
                "diameter":"vfloat"
            }
        },
        {
            "type":"threshold",
            "code":"0x003001D0",
            "in":
            {
                "image":"mat",
                "thresh":"float",
                "maxval":"float",
                "type":"integer"
            },
            "out":
            {
                "result":"integer",
                "image_out":"mat"
            }
        },

        //高级算法类指令
        {
            "type":"calc_sharp_match_template",
            "code":"0x00400000",
            "in":
            {
                "image":"mat",
                "num_features":"integer",
                "pyramid_levels_h":"integer",
                "pyramid_levels_l":"integer",
                "angle_begin":"float",
                "angle_end":"float",
                "angle_step":"float",
                "scale_begin":"float",
                "scale_end":"float",
                "scale_step":"float",
                "path":"string",
                "name":"string"
            },
            "out":
            {
                "result":"integer",
                "full_name":"string"
            }
        },
        {
            "type":"calc_sharp_match_file",
            "code":"0x00400001",
            "in":
            {
                "image":"mat",
                "template":"string",
                "threshold":"float"
            },
            "out":
            {
                "result":"integer",
                "debug_img":"mat",
                "score":"float",
                "scale":"float",
                "angle":"float",
                "x":"float",
                "y":"float"
            }
        },
        {
            "type":"calc_sharp_match_image",
            "code":"0x00400002",
            "in":
            {
                "image":"mat",
                "template":"mat",
                "num_features":"integer",
                "pyramid_levels_h":"integer",
                "pyramid_levels_l":"integer",
                "angle_begin":"float",
                "angle_end":"float",
                "angle_step":"float",
                "scale_begin":"float",
                "scale_end":"float",
                "scale_step":"float",
                "threshold":"float"
            },
            "out":
            {
                "result":"integer",
                "debug_img":"mat",
                "score":"float",
                "scale":"float",
                "angle":"float",
                "x":"float",
                "y":"float",
                "xmin":"float",
                "xmax":"float",
                "ymin":"float",
                "ymax":"float"
            }
        },
        {
            "type":"filter_strongest_line",
            "code":"0x00400010",
            "in":
            {
                "image":"mat",
                "line":"vline",
                "distance":"float",
                "size":"float",
                "filter":"float"
            },
            "out":
            {
                "result":"integer",
                "max_line":"vline",
                "max_value":"float",
                "min_line":"vline",
                "min_value":"float"
            }
        },
        {
            "type":"asls_ellipse",
            "code":"0x00400100",
            "in":
            {
                "image":"mat",
                "angle_tolerance":"float",
                "ang_th":"float",
                "density_th":"float",
                "edge_process_select":"integer",
                "inner_unit_dis_tolerance":"float",
                "log_eps":"float",
                "min_num_pnts":"float",
                "max_raid":"float",
                "min_raid":"float",
                "ab_ratio":"float",
                "n_bins":"integer",
                "quant":"float",
                "scale":"float",
                "sigma_scale":"float",
                "specified_polarity":"integer",
                "support_angle_tolerance":"float",
                "Tac":"float",
                "Tmin":"float",
                "Tr":"float"
            },
            "out":
            {
                "result":"integer",
                "x":"vfloat",
                "y":"vfloat",
                "a":"vfloat",
                "b":"vfloat",
                "angle":"vfloat"
            }
        },
        {
            "type":"filter_ellipse",
            "code":"0x00400110",
            "in":
            {
                "x":"vfloat",
                "y":"vfloat",
                "a":"vfloat",
                "b":"vfloat",
                "angle":"vfloat",
                "perfect_diam":"float",
                "diam_dif_min":"float",
                "diam_dif_max":"float",
                "diam_dif_coffee":"float",
                "perfect_x":"float",
                "perfect_y":"float",
                "distance_min":"float",
                "distance_max":"float",
                "distance_coffee":"float"
            },
            "out":
            {
                "result":"integer",
                "x_out":"float",
                "y_out":"float",
                "a_out":"float",
                "b_out":"float",
                "angle_out":"float"
            }
        },
        {
            "type":"sort_circle",
            "code":"0x00400200",
            "in":
            {
                "x":"vfloat",
                "y":"vfloat",
                "radius":"vfloat",
                "size":"integer",
                "perfect_radius":"float",
                "radius_dif_min":"float",
                "radius_dif_max":"float",
                "clockwise":"integer",
                "start_angle":"float"
            },
            "out":
            {
                "result":"integer",
                "x_out":"vfloat",
                "y_out":"vfloat",
                "radius_out":"vfloat"
            }
        },
        {
            "type":"find_line",
            "code":"0x00400300",
            "in":
            {
                "image":"mat",
                "start_x":"integer",
                "start_y":"integer",
                "end_x":"integer",
                "end_y":"integer",
                "count":"integer",
                "width":"integer",
                "height":"integer",
                "ksize":"integer",
                "type":"integer",
                "find_type":"integer",
                "iterations":"integer",
                "strength":"float",
                "thresh":"float",
                "sigma":"float",
                "end":"float",
                "minimum":"float",
                "use_fit":"integer"
            },
            "out":
            {
                "result":"integer",
                "line":"vline",
                "debug_img":"mat"
            }
        },

        //字符串处理指令
        {
            "type":"string_to_integer",
            "code":"0x00500000",
            "in":
            {
                "string":"string"
            },
            "out":
            {
                "result":"integer",
                "integer":"integer"
            }
        },
        {
            "type":"string_to_float",
            "code":"0x00500001",
            "in":
            {
                "string":"string"
            },
            "out":
            {
                "result":"integer",
                "float":"float"
            }
        },
        {
            "type":"combine_string",
            "code":"0x00500010",
            "in":
            {
                "string":"vstring"
            },
            "out":
            {
                "result":"integer",
                "combine":"string"
            }
        },
        {
            "type":"split_string",
            "code":"0x00500011",
            "in":
            {
                "string":"string",
                "splitter":"string"
            },
            "out":
            {
                "result":"integer",
                "split":"vstring",
                "length":"integer"
            }
        },
        {
            "type":"get_string",
            "code":"0x00500020",
            "in":
            {
                "vstring":"vstring",
                "index":"integer"
            },
            "out":
            {
                "result":"integer",
                "string":"string"
            }
        },
        {
            "type":"regex_match",
            "code":"0x00500030",
            "in":
            {
                "string":"string",
                "pattern":"string"
            },
            "out":
            {
                "result":"integer",
                "match":"integer"
            }
        },
        {
            "type":"regex_search",
            "code":"0x00500031",
            "in":
            {
                "string":"string",
                "pattern":"string"
            },
            "out":
            {
                "result":"integer",
                "match":"vstring"
            }
        },
        //数组处理指令
        {
            "type":"get_float",
            "code":"0x00510000",
            "in":
            {
                "vfloat":"vfloat",
                "index":"integer"
            },
            "out":
            {
                "result":"integer",
                "float":"float"
            }
        },
        {
            "type":"vfloat_mul",
            "code":"0x00510010",
            "in":
            {
                "vfloat_in":"vfloat",
                "mul":"float"
            },
            "out":
            {
                "result":"integer",
                "vfloat_out":"vfloat"
            }
        },

        //文件操作指令
        {
            "type":"creat_folder",
            "code":"0x00600000",
            "in":
            {
                "path":"string"
            },
            "out":
            {
                "result":"integer",
                "full_path":"string"              
            }
        },
        {
            "type":"join_path",
            "code":"0x00600001",
            "in":
            {
                "path":"string",
                "path_sub":"string"
            },
            "out":
            {
                "result":"integer",
                "full_path":"string"                
            }
        },
        {
            "type":"get_date_time",
            "code":"0x00600010",
            "in":
            {

            },
            "out":
            {
                "result":"integer",
                "date_time":"string"  
            }
        },
        {
            "type":"creat_unique_folder",
            "code":"0x00600011",
            "in":
            {
                "path":"string",
                "length":"integer"                
            },
            "out":
            {
                "result":"integer",
                "full_path":"string"
            }
        },
        {
            "type":"enum_files",
            "code":"0x00600020",
            "in":
            {
                "path":"string"             
            },
            "out":
            {
                "result":"integer",
                "files":"vstring"
            }
        },
        {
            "type":"save_xml",
            "code":"0x00601000",
            "in":
            {
                "path":"string",
                "name":"string",
                "product":"string",
                "sn":"string",
                "station":"string",
                "mac":"string",
                "detection_type":"string",
                "result":"string",
                "folder":"string",
                "image_name":"string",
                "width":"integer",
                "height":"integer",
                "depth":"integer",
                "name_b":"vstring",
                "label":"vstring",
                "standard":"vstring",
                "xmin":"vstring",
                "ymin":"vstring",
                "xmax":"vstring",
                "ymax":"vstring"
            },
            "out":
            {
                "result":"integer",
                "full_path":"string"
            }
        },
       

         //全局变量操作指令
        {
            "type":"set_global_integer",
            "code":"0x00700000",
            "in":
            {
                "input":"integer",
                "name":"string"
            },
            "out":
            {
                "result":"integer"
            }
        },
        {
            "type":"set_global_float",
            "code":"0x00700001",
            "in":
            {
                "input":"float",
                "name":"string"
            },
            "out":
            {
                "result":"integer"
            }
        },
        {
            "type":"set_global_string",
            "code":"0x00700002",
            "in":
            {
                "input":"string",
                "name":"string"
            },
            "out":
            {
                "result":"integer"
            }
        },
        {
            "type":"set_local_integer",
            "code":"0x00700010",
            "in":
            {
                "input":"integer" 
            },
            "out":
            {
                "result":"integer",
                "value":"integer"
            }
        },
        {
            "type":"set_local_float",
            "code":"0x00700011",
            "in":
            {
                "input":"float"
            },
            "out":
            {
                "result":"integer",
                "value":"float"
            }
        },
        {
            "type":"set_local_string",
            "code":"0x00700012",
            "in":
            {
                "input":"string"
            },
            "out":
            {
                "result":"integer",
                "value":"string"
            }
        },
        {
            "type":"log_output",
            "code":"0x00800000",
            "in":
            {
                "input":"string",
                "level":"string"
            },
            "out":
            {
                "result":"integer"
            }
        }
    ]
    
}