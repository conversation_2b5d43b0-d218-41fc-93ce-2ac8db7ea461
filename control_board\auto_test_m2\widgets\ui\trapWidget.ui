<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1317</width>
    <height>488</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QGroupBox" name="groupBox_4">
     <property name="title">
      <string/>
     </property>
     <layout class="QGridLayout" name="gridLayout_4">
      <item row="0" column="0">
       <widget class="QLabel" name="label_57">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>核</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLabel" name="label_63">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>轴</string>
        </property>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QLabel" name="label_68">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>模式</string>
        </property>
       </widget>
      </item>
      <item row="0" column="3">
       <widget class="QLabel" name="label_69">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>步长（pulse）</string>
        </property>
       </widget>
      </item>
      <item row="0" column="4">
       <widget class="QLabel" name="label_70">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>速度（pulse/ms）</string>
        </property>
       </widget>
      </item>
      <item row="0" column="5">
       <widget class="QLabel" name="label_71">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>加速度（pulse/ms^2）</string>
        </property>
       </widget>
      </item>
      <item row="0" column="6">
       <widget class="QLabel" name="label_72">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>减速度（pulse/ms^2）</string>
        </property>
       </widget>
      </item>
      <item row="0" column="7">
       <widget class="QLabel" name="label_73">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>起跳速度（pulse/ms）</string>
        </property>
       </widget>
      </item>
      <item row="0" column="8">
       <widget class="QLabel" name="label_74">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>平滑时间（ms）</string>
        </property>
       </widget>
      </item>
      <item row="0" column="9">
       <widget class="QLabel" name="label_75">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>循环次数</string>
        </property>
       </widget>
      </item>
      <item row="0" column="10">
       <widget class="QLabel" name="label_76">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>循环间隔（ms）</string>
        </property>
       </widget>
      </item>
      <item row="0" column="11">
       <widget class="QLabel" name="label_77">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>操作</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_61">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>1</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QLabel" name="label_65">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>1</string>
        </property>
       </widget>
      </item>
      <item row="1" column="2">
       <widget class="QComboBox" name="comboBox_trap_mode_1">
        <item>
         <property name="text">
          <string>绝对运动</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>相对运动</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="1" column="4">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_vel_1">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="value">
         <double>50.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="1" column="5">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_acc_1">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="value">
         <double>0.050000000000000</double>
        </property>
       </widget>
      </item>
      <item row="1" column="6">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_dec_1">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="value">
         <double>0.050000000000000</double>
        </property>
       </widget>
      </item>
      <item row="1" column="7">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_vel_start_1">
        <property name="decimals">
         <number>3</number>
        </property>
       </widget>
      </item>
      <item row="1" column="8">
       <widget class="QSpinBox" name="spinBox_trap_smooth_time_1">
        <property name="maximum">
         <number>49</number>
        </property>
        <property name="value">
         <number>30</number>
        </property>
       </widget>
      </item>
      <item row="1" column="9">
       <widget class="QSpinBox" name="spinBox"/>
      </item>
      <item row="1" column="10">
       <widget class="QSpinBox" name="spinBox_2"/>
      </item>
      <item row="1" column="11">
       <widget class="QPushButton" name="pushButton_trap_start_1">
        <property name="text">
         <string>启动</string>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_67">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>1</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QLabel" name="label_58">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>2</string>
        </property>
       </widget>
      </item>
      <item row="2" column="2">
       <widget class="QComboBox" name="comboBox_trap_mode_2">
        <item>
         <property name="text">
          <string>绝对运动</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>相对运动</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="2" column="4">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_vel_2">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="value">
         <double>50.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="2" column="5">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_acc_2">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="value">
         <double>0.050000000000000</double>
        </property>
       </widget>
      </item>
      <item row="2" column="6">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_dec_2">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="value">
         <double>0.050000000000000</double>
        </property>
       </widget>
      </item>
      <item row="2" column="7">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_vel_start_2">
        <property name="decimals">
         <number>3</number>
        </property>
       </widget>
      </item>
      <item row="2" column="8">
       <widget class="QSpinBox" name="spinBox_trap_smooth_time_2">
        <property name="maximum">
         <number>49</number>
        </property>
        <property name="value">
         <number>30</number>
        </property>
       </widget>
      </item>
      <item row="2" column="9">
       <widget class="QSpinBox" name="spinBox_6"/>
      </item>
      <item row="2" column="10">
       <widget class="QSpinBox" name="spinBox_5"/>
      </item>
      <item row="2" column="11">
       <widget class="QPushButton" name="pushButton_trap_start_2">
        <property name="text">
         <string>启动</string>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="label_66">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>1</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QLabel" name="label_56">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>3</string>
        </property>
       </widget>
      </item>
      <item row="3" column="2">
       <widget class="QComboBox" name="comboBox_trap_mode_3">
        <item>
         <property name="text">
          <string>绝对运动</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>相对运动</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="3" column="4">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_vel_3">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="value">
         <double>50.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="3" column="5">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_acc_3">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="value">
         <double>0.050000000000000</double>
        </property>
       </widget>
      </item>
      <item row="3" column="6">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_dec_3">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="value">
         <double>0.050000000000000</double>
        </property>
       </widget>
      </item>
      <item row="3" column="7">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_vel_start_3">
        <property name="decimals">
         <number>3</number>
        </property>
       </widget>
      </item>
      <item row="3" column="8">
       <widget class="QSpinBox" name="spinBox_trap_smooth_time_3">
        <property name="maximum">
         <number>49</number>
        </property>
        <property name="value">
         <number>30</number>
        </property>
       </widget>
      </item>
      <item row="3" column="9">
       <widget class="QSpinBox" name="spinBox_9"/>
      </item>
      <item row="3" column="10">
       <widget class="QSpinBox" name="spinBox_8"/>
      </item>
      <item row="3" column="11">
       <widget class="QPushButton" name="pushButton_trap_start_3">
        <property name="text">
         <string>启动</string>
        </property>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="label_59">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>1</string>
        </property>
       </widget>
      </item>
      <item row="4" column="1">
       <widget class="QLabel" name="label_62">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>4</string>
        </property>
       </widget>
      </item>
      <item row="4" column="2">
       <widget class="QComboBox" name="comboBox_trap_mode_4">
        <item>
         <property name="text">
          <string>绝对运动</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>相对运动</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="4" column="4">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_vel_4">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="value">
         <double>50.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="4" column="5">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_acc_4">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="value">
         <double>0.050000000000000</double>
        </property>
       </widget>
      </item>
      <item row="4" column="6">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_dec_4">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="value">
         <double>0.050000000000000</double>
        </property>
       </widget>
      </item>
      <item row="4" column="7">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_vel_start_4">
        <property name="decimals">
         <number>3</number>
        </property>
       </widget>
      </item>
      <item row="4" column="8">
       <widget class="QSpinBox" name="spinBox_trap_smooth_time_4">
        <property name="maximum">
         <number>49</number>
        </property>
        <property name="value">
         <number>30</number>
        </property>
       </widget>
      </item>
      <item row="4" column="9">
       <widget class="QSpinBox" name="spinBox_12"/>
      </item>
      <item row="4" column="10">
       <widget class="QSpinBox" name="spinBox_11"/>
      </item>
      <item row="4" column="11">
       <widget class="QPushButton" name="pushButton_trap_start_4">
        <property name="text">
         <string>启动</string>
        </property>
       </widget>
      </item>
      <item row="5" column="0">
       <widget class="QLabel" name="label_60">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>1</string>
        </property>
       </widget>
      </item>
      <item row="5" column="1">
       <widget class="QLabel" name="label_64">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>5</string>
        </property>
       </widget>
      </item>
      <item row="5" column="2">
       <widget class="QComboBox" name="comboBox_trap_mode_5">
        <item>
         <property name="text">
          <string>绝对运动</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>相对运动</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="5" column="4">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_vel_5">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="maximum">
         <double>9.990000000000000</double>
        </property>
        <property name="value">
         <double>2.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="5" column="5">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_acc_5">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="value">
         <double>0.050000000000000</double>
        </property>
       </widget>
      </item>
      <item row="5" column="6">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_dec_5">
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="value">
         <double>0.050000000000000</double>
        </property>
       </widget>
      </item>
      <item row="5" column="7">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_trap_vel_start_5">
        <property name="decimals">
         <number>3</number>
        </property>
       </widget>
      </item>
      <item row="5" column="8">
       <widget class="QSpinBox" name="spinBox_trap_smooth_time_5">
        <property name="maximum">
         <number>49</number>
        </property>
        <property name="value">
         <number>30</number>
        </property>
       </widget>
      </item>
      <item row="5" column="9">
       <widget class="QSpinBox" name="spinBox_15"/>
      </item>
      <item row="5" column="10">
       <widget class="QSpinBox" name="spinBox_14"/>
      </item>
      <item row="5" column="11">
       <widget class="QPushButton" name="pushButton_trap_start_5">
        <property name="text">
         <string>启动</string>
        </property>
       </widget>
      </item>
      <item row="1" column="3">
       <widget class="QSpinBox" name="spinBox_trap_step_1">
        <property name="minimum">
         <number>-999999999</number>
        </property>
        <property name="maximum">
         <number>999999999</number>
        </property>
       </widget>
      </item>
      <item row="2" column="3">
       <widget class="QSpinBox" name="spinBox_trap_step_2">
        <property name="minimum">
         <number>-999999999</number>
        </property>
        <property name="maximum">
         <number>999999999</number>
        </property>
       </widget>
      </item>
      <item row="3" column="3">
       <widget class="QSpinBox" name="spinBox_trap_step_3">
        <property name="minimum">
         <number>-999999999</number>
        </property>
        <property name="maximum">
         <number>999999999</number>
        </property>
       </widget>
      </item>
      <item row="4" column="3">
       <widget class="QSpinBox" name="spinBox_trap_step_4">
        <property name="minimum">
         <number>-999999999</number>
        </property>
        <property name="maximum">
         <number>999999999</number>
        </property>
        <property name="value">
         <number>0</number>
        </property>
       </widget>
      </item>
      <item row="5" column="3">
       <widget class="QSpinBox" name="spinBox_trap_step_5">
        <property name="minimum">
         <number>-999999999</number>
        </property>
        <property name="maximum">
         <number>999999999</number>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>242</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
