# -*- coding: utf-8 -*-
"""
user:Created by jid on 2021/3/10 15:48
email:
description:
"""
import operator
import os
import threading

from PyQt5.QtCore import QObject

from common.LogUtils import logger
from photics.color_analyzer_tools.manager import PhoticsFunction


class PhoticsManager(QObject):

    def __init__(self):
        super().__init__()
        self.__analyzer_func = PhoticsFunction.GAMMA_CURVE
        self.vds_status = False

    def get_current_analyzer_func(self):
        return self.__analyzer_func

    def set_current_func(self, func: PhoticsFunction):
        logger.info(f"set_current_func func={func}")
        self.__analyzer_func = func

    def get_analyzer_func(self):
        return self.__analyzer_func

    @staticmethod
    def detect_push_pattern_app_exists():
        logger.info('detect_push_pattern_app_exists')
        exists = False
        cmd = 'adb shell pm list packages cn.com.hwtc.fixturepushpattern'
        output = os.popen(cmd).readlines()
        logger.info('detect_push_pattern_app_exists type=%s, output=%s', type(output), output)
        if len(output) > 0:
            package = output[0]
            if package.__contains__(':'):
                packagename = package.split(':')[1]
                if packagename.__contains__('\n'):
                    packagename = packagename.replace('\n', '')
                logger.info('detect_push_pattern_app_exists packagename=%s', packagename)
                if operator.eq('cn.com.hwtc.fixturepushpattern', packagename):
                    exists = True
        logger.info('detect_push_pattern_app_exists exists=%s', exists)
        return exists

    @staticmethod
    def detect_push_pattern_app_version():
        output = os.popen('adb shell dumpsys package cn.com.hwtc.fixturepushpattern').readlines()
        logger.debug("detect_push_pattern_app_version output={}".format(output))
        version = ""
        if len(output) > 0:
            for item in output:
                if item.__contains__("versionName"):
                    version = item.split("=")[1]
                    break
        logger.info("detect_push_pattern_app_version version={}".format(version))

    def download_push_pattern_app(self):
        logger.info('download_push_pattern_app')
        exists = self.detect_push_pattern_app_exists()
        logger.info('download_push_pattern_app exists=%s', exists)
        if not exists:
            version_8031, version_3588 = self.detect_vds_os_version()
            logger.info('download_push_pattern_app version_8031={},version_3588={}'.format(version_8031, version_3588))
            if version_8031:
                version_8031_path = os.path.join('res', 'push_resource(8031)', 'FixturePushPattern.apk')
                os.system('adb root')
                os.system('adb remount')
                os.system('adb shell mkdir /system/app/FixturePushPattern/')
                os.system('adb push %s /system/app/FixturePushPattern/' % version_8031_path)
                os.system('adb reboot')
            elif version_3588:
                version_3588_path = os.path.join('res', 'push_resource(3588)', 'FixturePushPattern.apk')
                os.system('adb root')
                os.system('adb remount')
                os.system('adb shell mkdir /system/app/FixturePushPattern/')
                os.system('adb push %s /system/app/FixturePushPattern/' % version_3588_path)
                os.system('adb reboot')
            threading.Timer(interval=30, function=self.start_push_pattern_app).start()
        else:
            self.detect_push_pattern_app_version()
            self.start_push_pattern_app()

    @staticmethod
    def detect_vds_os_version():
        logger.debug('detect_vds_os_version')
        version_8031 = False
        version_3588 = False
        cmd_8031 = "adb shell getprop ro.supplier.os.version.number"
        output = os.popen(cmd_8031).readlines()
        logger.debug('detect_vds_os_version type=%s, output=%s', type(output), output)
        if len(output) > 0:
            if output[0].__contains__("videosource"):
                version_8031 = True
        if not version_8031:
            cmd_3588 = "adb shell getprop ro.hwtc.version.os"
            output = os.popen(cmd_3588).readlines()
            logger.debug('detect_vds_os_version type=%s, output=%s', type(output), output)
            if len(output) > 0:
                if output[0].__contains__("videosource"):
                    version_3588 = True
        return version_8031, version_3588

    @staticmethod
    def get_adb_status():
        logger.info('get_adb_status')
        output = os.popen('adb devices').readlines()
        logger.info('get_adb_status type=%s, output=%s', type(output), output)
        if len(output) > 2:
            return True
        else:
            return False

    @staticmethod
    def start_push_pattern_app():
        logger.info('start_push_pattern_app')
        os.popen('adb shell am start cn.com.hwtc.fixturepushpattern/cn.com.hwtc.fixturepushpattern.MainActivity')

    @staticmethod
    def set_background_color(r=0, g=0, b=0):
        bg_color = '%02X%02X%02X' % (r, g, b)
        logger.info('set_background_color bg_color=%s' % bg_color)
        os.popen('adb shell am broadcast -a cn.com.hwtc.fixturepushpattern --es pattern_rgb %s' % bg_color)

    @staticmethod
    def execute_adb_command(cmd):
        logger.info(f"execute_adb_command cmd={cmd}")
        output = []
        try:
            output = os.popen(cmd).readlines()
        except Exception as e:
            logger.error(f"execute_adb_command exception: {str(e.args)}")
        return output

    def execute_adb_command_assign_device(self, cmd):
        logger.info(f"execute_adb_command_assign_device cmd={cmd}")
        adb_list = self.check_adb_devices()
        if len(adb_list) == 0:
            return False, "adb设备未连接"
        try:
            for device in adb_list:
                execute_cmd = cmd.replace("adb", f"adb -s {device}")
                logger.info(f"execute_adb_command_assign_device execute_cmd={execute_cmd}")
                output = os.popen(execute_cmd).readlines()
                logger.info(f"execute_adb_command_assign_device output={output}")
        except Exception as e:
            logger.error(f"execute_adb_command exception: {str(e.args)}")
            return False, "adb指令执行异常"
        return True, "adb指令执行成功"

    @staticmethod
    def check_adb_devices():
        # 检查adb设备，并返回设备list
        adb_list = []
        ret = os.popen('adb devices').readlines()
        logger.info(f'check_adb_devices ret={ret}')
        if len(ret) == 1:
            logger.info('check_adb_devices 未识别到adb设备...')
            return adb_list
        else:
            for n in ret:
                if '\tdevice\n' in n:
                    adb = str(n).strip().split('\tdevice')[0].strip()
                    adb_list.append(str(adb))

            return adb_list

    def get_vds_status(self):
        vds_status = False
        try:
            version_8031, version_3588 = self.detect_vds_os_version()
            if version_8031 or version_3588:
                vds_status = True
        except Exception as e:
            logger.error("get_vds_status exception: {}".format(str(e.args)))
            vds_status = False

        return vds_status

    def update_vds_status(self):
        threading.Timer(interval=3, function=self.update_vds_status).start()
        self.thread_exec_vds_status()

    @staticmethod
    def thread_exec_vds_status():
        threading.Thread(target=photics_manager.detect_vds_status).start()

    def detect_vds_status(self):
        from utils.SignalsManager import signals_manager
        status = self.get_vds_status()
        logger.debug(f"detect_vds_status status={status}")
        if self.vds_status != status:
            self.vds_status = status
            signals_manager.vds_status_signal.emit(self.vds_status)


photics_manager: PhoticsManager = PhoticsManager()
