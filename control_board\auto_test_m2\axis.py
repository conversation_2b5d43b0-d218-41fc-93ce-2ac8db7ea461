import threading
import queue
import traceback
import time

from .. import gg_gen as gen
from .constants import CONF
from ..common.log import get_logger

logger = get_logger("auto_test_m2")


class AxisState:
    def __init__(self):
        self.bad_bit = 0
        self.fail_bit = 0
        self.good_bit = 0
        self.stop_bit = 0
        self.init_bit = 0

        self.e_enc_pos = 0
        self.axis_enc_pos = 0
        self.axis_prf_pos = 0
        self.sts = 0

        self.jog_state = 0

        self.home_result_bit = 0
        self.home_succeed_bit = 0

        self.trap_move_bit = 0
        self.trap_move_result_bit = 0
        self.trap_move_succeed_bit = 0

        self.err_msg = ""


class AxisWorkCmd:
    def __init__(self, name):
        self.name = name


class Axis:
    def __init__(self, core, prf):
        self.core = core
        self.prf = prf

        self.conf = CONF.get("axis").get(str(self.prf))
        self.state = AxisState()

        self.work_queue = queue.Queue()

        self.work_thread = threading.Thread(target=self._work, daemon=True)
        self.status_monitor_thread = threading.Thread(target=self._status_monitor, daemon=True)

    def clr_sts(self):
        r = gen.GTN_ClrSts(self.core, self.prf)
        if r != 0:
            logger.error(f"GTN_ClrSts failed {r}")
            self.state.fail_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"GTN_ClrSts failed {r}"
            return r
        return 0

    def zero_pos(self):
        r = gen.GTN_ZeroPos(self.core, self.prf)
        if r != 0:
            logger.error(f"GTN_ZeroPos failed {r}")
            self.state.fail_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"GTN_ZeroPos failed {r}"
            return r
        return 0

    def axis_on(self):
        r = gen.GTN_AxisOn(self.core, self.prf)
        if r != 0:
            logger.error(f"GTN_AxisOn failed {r}")
            self.state.fail_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"GTN_AxisOn failed {r}"
            return r
        return 0

    def axis_off(self):
        r = gen.GTN_AxisOff(self.core, self.prf)
        if r != 0:
            logger.error(f"GTN_AxisOff failed {r}")
            self.state.fail_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"GTN_AxisOff failed {r}"
            return r
        return 0

    def stop(self):
        r = gen.GTN_Stop(self.core, 1 << (self.prf - 1), 0)
        if r != 0:
            logger.error(f"GTN_Stop failed {r}")
            self.state.fail_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"GTN_Stop failed {r}"
            return r
        return 0

    def prf_jog(self):
        r = gen.GTN_PrfJog(self.core, self.prf)
        if r != 0:
            logger.error(f"GTN_PrfJog failed {r}")
            self.state.fail_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"GTN_PrfJog failed {r}"
            return r
        return 0

    def set_jog_prm(self, acc, dec, smooth):
        r = gen.GTN_SetJogPrm(self.core, self.prf, acc, dec, smooth)
        if r != 0:
            logger.error(f"GTN_SetJogPrm failed {r}")
            self.state.fail_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"GTN_SetJogPrm failed {r}"
            return r
        return 0

    def set_vel(self, vel):
        r = gen.GTN_SetVel(self.core, self.prf, vel)
        if r != 0:
            logger.error(f"GTN_SetVel failed {r}")
            self.state.fail_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"GTN_SetVel failed {r}"
            return r
        return 0

    def update(self):
        r = gen.GTN_Update(self.core, 1 << (self.prf - 1))
        if r != 0:
            logger.error(f"GTN_Update failed {r}")
            self.state.fail_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"GTN_Update failed {r}"
            return r
        return 0

    def prf_trap(self):
        r = gen.GTN_PrfTrap(self.core, self.prf)
        if r != 0:
            logger.error(f"GTN_PrfTrap failed {r}")
            self.state.fail_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"GTN_PrfTrap failed {r}"
            return r
        return 0

    def set_trap_prm(self, acc, dec, velStart, smoothTime):
        r = gen.GTN_SetTrapPrm(self.core, self.prf, acc=acc, dec=dec, velStart=velStart, smoothTime=smoothTime)
        if r != 0:
            logger.error(f"GTN_SetTrapPrm failed {r}")
            self.state.fail_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"GTN_SetTrapPrm failed {r}"
            return r
        return 0

    def set_pos(self, pos):
        r = gen.GTN_SetPos(self.core, self.prf, pos)
        if r != 0:
            logger.error(f"GTN_SetPos failed {r}")
            self.state.fail_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"GTN_SetPos failed {r}"
            return r
        return 0

    def get_sts(self):
        r, sts = gen.GTN_GetSts(self.core, self.prf)
        if r != 0:
            logger.error(f"GTN_GetSts failed {r}")
            self.state.fail_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"GTN_GetSts failed {r}"
            return r, 0
        return 0, sts

    def init(self):
        if self.state.init_bit:
            return 0

        r = gen.GTN_ClrSts(self.core, self.prf)
        if r != 0:
            logger.error(f"init GTN_ClrSts failed {r}")
            self.state.bad_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"init GTN_ClrSts failed {r}"
            return r

        band = self.conf.get("error_band")
        time_ = self.conf.get("error_time")
        r = gen.GTN_SetAxisBand(self.core, self.prf, band, time_)
        if r != 0:
            logger.error(f"init GTN_SetAxisBand failed {r}")
            self.state.bad_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"init GTN_SetAxisBand failed {r}"
            return r

        r = gen.GTN_AxisOn(self.core, self.prf)
        if r != 0:
            logger.error(f"init GTN_AxisOn failed {r}")
            self.state.bad_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"init GTN_AxisOn failed {r}"
            return r

        r = gen.GTN_ZeroPos(self.core, self.prf)
        if r != 0:
            logger.error(f"init GTN_ZeroPos failed {r}")
            self.state.bad_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"init GTN_ZeroPos failed {r}"
            return r

        self.work_thread.start()
        self.status_monitor_thread.start()

        self.state.init_bit = 1
        self.state.good_bit = 1

        return 0

    def jog_move(self, direction, vel=0, acc=0, dec=0, smooth=0):
        cmd = AxisWorkCmd("jog_move")
        cmd.direction = direction
        cmd.vel = vel
        cmd.acc = acc
        cmd.dec = dec
        cmd.smooth = smooth
        self.work_queue.put(cmd)

        return 0

    def _jog_move(self, direction, vel=0, acc=0, dec=0, smooth=0):
        if direction == 0:
            r = self.clr_sts()
            if r != 0:
                return r
            r = self.stop()
            if r != 0:
                return r
            self.state.jog_state = 0
        elif direction == -1:
            r = self.clr_sts()
            if r != 0:
                return r
            r = self.stop()
            if r != 0:
                return r
            r = self.prf_jog()
            if r != 0:
                return r
            r = self.set_jog_prm(acc, dec, smooth)
            if r != 0:
                return r
            r = self.set_vel(vel * -1)
            if r != 0:
                return r
            r = self.update()
            if r != 0:
                return r
            self.state.jog_state = -1
        elif direction == 1:
            r = self.clr_sts()
            if r != 0:
                return r
            r = self.stop()
            if r != 0:
                return r
            r = self.prf_jog()
            if r != 0:
                return r
            r = self.set_jog_prm(acc, dec, smooth)
            if r != 0:
                return r
            r = self.set_vel(vel)
            if r != 0:
                return r
            r = self.update()
            if r != 0:
                return r
            self.state.jog_state = 1

        return 0

    def trap_move(self, pos, vel, acc, dec, velStart, smoothTime):
        if self.state.trap_move_bit == 1:
            return 1

        self.state.trap_move_bit = 1
        self.state.trap_move_result_bit = 0
        self.state.trap_move_succeed_bit = 0

        cmd = AxisWorkCmd("trap_move")
        cmd.pos = pos
        cmd.vel = vel
        cmd.acc = acc
        cmd.dec = dec
        cmd.velStart = velStart
        cmd.smoothTime = smoothTime
        self.work_queue.put(cmd)

        return 0

    def _trap_move(self, pos, vel, acc, dec, velStart, smoothTime):
        r = self.clr_sts()
        if r != 0:
            return r

        r = self.prf_trap()
        if r != 0:
            return r

        r = self.set_trap_prm(acc=acc, dec=dec, velStart=velStart, smoothTime=smoothTime)
        if r != 0:
            return r

        r = self.set_pos(pos=pos)
        if r != 0:
            return r

        r = self.set_vel(vel=vel)
        if r != 0:
            return r

        r = self.update()
        if r != 0:
            return r

        while True:
            r, sts = self.get_sts()
            if r != 0:
                return r

            # r, pos = gen.GTN_GetPrfPos(self.core, self.prf)

            # print(f"sts: {sts}, pos {pos}")

            if sts & 1024 == 0:
                break

        self.state.trap_move_succeed_bit = 1
        return 0

    def home(self):
        self.state.home_result_bit = 0
        self.state.home_succeed_bit = 0

        cmd = AxisWorkCmd("home")
        self.work_queue.put(cmd)

    def _home(self):
        r = self.clr_sts()
        if r != 0:
            return r

        r = self.zero_pos()
        if r != 0:
            return r

        acc = self.conf.get("home_acc")
        dec = self.conf.get("home_dec")
        smoothTime = self.conf.get("home_smoothTime")
        vel = self.conf.get("home_vel")
        velStart = self.conf.get("home_velStart")

        r, e_enc_pos = gen.GTN_GetEcatEncPos(self.core, self.prf)
        if r != 0:
            logger.error(f"GTN_GetEcatEncPos failed {r}")
            self.state.fail_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"GTN_GetEcatEncPos failed {r}"
            return r

        homing_e_enc_pos = self.conf.get("home_e_enc_pos")
        pos = homing_e_enc_pos - e_enc_pos

        logger.info("axis(%s) trap_move pos %s", self.prf, pos)
        r = self._trap_move(pos, vel, acc, dec, velStart=velStart, smoothTime=smoothTime)
        if r != 0:
            return r

        time.sleep(1)

        r = self.clr_sts()
        if r != 0:
            return r

        r = self.zero_pos()
        if r != 0:
            return r

        soft_limit = self.conf.get("soft_limit")
        if soft_limit:
            psl = self.conf.get("positive_soft_limit")
            nsl = self.conf.get("negative_soft_limit")
            r = gen.GTN_SetSoftLimit(self.core, self.prf, psl, nsl)
            if r != 0:
                logger.error(f"GTN_SetSoftLimit failed {r}")
                self.state.fail_bit = 1
                self.state.good_bit = 0
                self.state.err_msg = f"GTN_SetSoftLimit failed {r}"
                return r
        self.state.home_succeed_bit = 1
        return 0

    def _work(self):
        while True:
            try:
                cmd = self.work_queue.get()

                if cmd.name == "jog_move":
                    self._jog_move(cmd.direction, cmd.vel, cmd.acc, cmd.dec, cmd.smooth)
                elif cmd.name == "trap_move":
                    self._trap_move(cmd.pos, cmd.vel, cmd.acc, cmd.dec, cmd.velStart, cmd.smoothTime)
                    self.state.trap_move_result_bit = 1
                    self.state.trap_move_bit = 0
                elif cmd.name == "home":
                    self._home()
                    self.state.home_result_bit = 1

            except Exception:
                logger.error("axis(%s) work thread error \n%s", self.prf, traceback.format_exc())

    def _status_monitor(self):
        while True:
            try:
                r, e_enc_pos = gen.GTN_GetEcatEncPos(self.core, self.prf)
                self.state.e_enc_pos = e_enc_pos

                r, axis_prf_pos = gen.GTN_GetAxisPrfPos(self.core, self.prf)
                self.state.axis_prf_pos = axis_prf_pos

                r, axis_enc_pos = gen.GTN_GetAxisEncPos(self.core, self.prf)
                self.state.axis_enc_pos = axis_enc_pos

                r, sts = gen.GTN_GetSts(self.core, self.prf)
                self.state.sts = sts

            except Exception:
                logger.error("axis(%s) status monitor thread error \n%s", self.prf, traceback.format_exc())

            time.sleep(0.1)
