import struct

from simbox_tools.serial_communication.CrcCheck import <PERSON><PERSON><PERSON><PERSON><PERSON>

# define the identifier


CN_SC_HEADER = 0xfe
CN_SC_END = 0x7f

# define the limit of the packet length
CN_PACKET_MAX_LENGTH = 0x200
CN_PACKET_MIN_LENGTH = 0x7

# define the index of data in the packet
EN_HEADER_INDEX = 0
EN_CNT_INDEX = 1
EN_LENGTH_INDEX = 2
EN_DATA_INDEX = 4

# define the stage of receive
EN_HEADER_STAGE = 0
EN_LENGTH_STAGE = 1
EN_END_STAGE = 2


class SerialLink(object):
    _log_tag = "SerialLink"
    __recv_stage = 0
    __recv_index = 0
    __recv_cnt = 0
    __send_cnt = 0
    __recv_packet_length = 0
    __recv_buffer = []
    __send_data = None
    __recv_data = None
    __log = None

    def __init__(self, s_func, r_func):
        self.__recv_stage = 0
        self.__recv_index = 0
        self.__recv_cnt = 0
        self.__send_cnt = 0
        self.__recv_packet_length = 0
        self.__recv_buffer = []
        self.__send_data = s_func
        self.__recv_data = r_func
        pass

    def close(self):
        pass

    def send(self, data):
        if (data is None) or (0 == len(data)):
            return 0
        packet_length = len(data) + CN_PACKET_MIN_LENGTH
        if packet_length > CN_PACKET_MAX_LENGTH:
            return 0
        format = "BBBB%ds" % len(data)
        frame1 = struct.pack(format, CN_SC_HEADER, self.__send_cnt & 0xff, (packet_length & 0xff00) >> 8,
                             packet_length & 0xff, data)
        self.__send_cnt += 1
        crc_obj = CrcCheck()
        crc_val = crc_obj.calc(frame1, packet_length - 3)
        format = "%dsBBB" % (packet_length - 3)
        frame = struct.pack(format, frame1, (crc_val & 0xff00) >> 8, crc_val & 0xff, CN_SC_END)
        self.__send_data(frame)

    def recvbytes(self, data):
        # Tools.print_list("%s recvbytes" % self._log_tag, data)
        for index in data:
            self.recv(index)

    def recv(self, data):
        if EN_HEADER_STAGE == self.__recv_stage:
            if CN_SC_HEADER == data:
                self.__recv_buffer = []
                self.__recv_buffer.append(CN_SC_HEADER)
                self.__recv_stage = EN_LENGTH_STAGE
        elif EN_LENGTH_STAGE == self.__recv_stage:
            self.__recv_buffer.append(data)
            if len(self.__recv_buffer) >= EN_DATA_INDEX:
                self.__recv_cnt = self.__recv_buffer[EN_CNT_INDEX]
                if self.__check_packet_length(self.__recv_buffer[EN_LENGTH_INDEX],
                                              self.__recv_buffer[EN_LENGTH_INDEX + 1]):
                    self.__recv_stage = EN_END_STAGE
                else:
                    self.__recv_stage = EN_HEADER_STAGE
        elif EN_END_STAGE == self.__recv_stage:
            self.__recv_buffer.append(data)
            if len(self.__recv_buffer) >= self.__recv_packet_length:
                if check_packet_data(self.__recv_buffer):
                    if self.__recv_data is not None:
                        start_index = EN_DATA_INDEX
                        end_index = start_index + len(self.__recv_buffer) - CN_PACKET_MIN_LENGTH
                        self.__recv_data(self.__recv_buffer[start_index:end_index])
                else:
                    self.__response_error()
                self.__recv_stage = EN_HEADER_STAGE

    def __check_packet_length(self, high, low):
        len = high << 8
        len += low
        if len <= CN_PACKET_MAX_LENGTH:
            self.__recv_packet_length = len
            return True
        return False

    def __response_error(self):
        error = struct.pack("B5s", 0, b"ERROR")
        self.send(error)


def check_packet_data(data):
    if (CN_SC_HEADER == data[EN_HEADER_INDEX]) and (CN_SC_END == data[-1]) and \
            (check_pakcet_crc(data)) and (len(data) > CN_PACKET_MIN_LENGTH):
        return True
    return False


def check_pakcet_crc(data):
    crc_obj = CrcCheck()
    crc_val = crc_obj.calc(data, len(data) - 3)
    val = data[-3] << 8
    val += data[-2]
    return val == crc_val
