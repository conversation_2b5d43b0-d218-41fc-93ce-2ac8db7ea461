from PyQt5.QtWidgets import QWidget, QListWidgetItem

from common.ui.BaseHead import Ui_BaseHeadForm


class HeadWidget(Ui_BaseHeadForm,QListWidgetItem):
    def __init__(self,parent=None):
        super(HeadWidget, self).__init__(parent)
        self.widget = QWidget()
        self.setupUi(self.widget)
        self.widget.mousePressEvent = self.on_mouse_press
        self.widget.mouseReleaseEvent = self.on_mouse_release
        self.widget.enterEvent = self.on_mouse_enter
        self.widget.leaveEvent = self.on_mouse_leave

    def on_mouse_press(self, event):
        # Handle mouse press event if needed
        pass

    def on_mouse_release(self, event):
        # Handle mouse release event if needed
        pass

    def on_mouse_enter(self, event):
        self.set_hovered_style()

    def on_mouse_leave(self, event):
        self.set_normal_style()

    def set_hovered_style(self):
        if self.isSelected():
            return

    def set_normal_style(self):
        if self.isSelected():
            return


