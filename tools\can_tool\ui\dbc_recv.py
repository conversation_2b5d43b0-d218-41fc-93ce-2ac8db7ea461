# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'dbc_recv.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(1600, 900)
        Form.setMinimumSize(QtCore.QSize(1600, 900))
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(Form)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.dbc_choice_button = QtWidgets.QPushButton(Form)
        self.dbc_choice_button.setMinimumSize(QtCore.QSize(0, 45))
        self.dbc_choice_button.setObjectName("dbc_choice_button")
        self.horizontalLayout.addWidget(self.dbc_choice_button)
        self.dbc_path = QtWidgets.QLabel(Form)
        self.dbc_path.setObjectName("dbc_path")
        self.horizontalLayout.addWidget(self.dbc_path)
        self.dbc_load_button = QtWidgets.QPushButton(Form)
        self.dbc_load_button.setMinimumSize(QtCore.QSize(0, 45))
        self.dbc_load_button.setObjectName("dbc_load_button")
        self.horizontalLayout.addWidget(self.dbc_load_button)
        self.connect_type = QtWidgets.QComboBox(Form)
        self.connect_type.setMinimumSize(QtCore.QSize(0, 45))
        self.connect_type.setObjectName("connect_type")
        self.connect_type.addItem("")
        self.connect_type.addItem("")
        self.connect_type.addItem("")
        self.connect_type.addItem("")
        self.connect_type.addItem("")
        self.horizontalLayout.addWidget(self.connect_type)
        self.horizontalLayout.setStretch(0, 1)
        self.horizontalLayout.setStretch(1, 5)
        self.horizontalLayout.setStretch(2, 1)
        self.horizontalLayout.setStretch(3, 2)
        self.horizontalLayout_2.addLayout(self.horizontalLayout)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.can_start_button = QtWidgets.QPushButton(Form)
        self.can_start_button.setMinimumSize(QtCore.QSize(0, 45))
        self.can_start_button.setObjectName("can_start_button")
        self.horizontalLayout_3.addWidget(self.can_start_button)
        self.can_stop_button = QtWidgets.QPushButton(Form)
        self.can_stop_button.setMinimumSize(QtCore.QSize(0, 45))
        self.can_stop_button.setObjectName("can_stop_button")
        self.horizontalLayout_3.addWidget(self.can_stop_button)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem)
        self.can_realsave_button = QtWidgets.QPushButton(Form)
        self.can_realsave_button.setMinimumSize(QtCore.QSize(0, 45))
        self.can_realsave_button.setObjectName("can_realsave_button")
        self.horizontalLayout_3.addWidget(self.can_realsave_button)
        self.can_realsaveSave_stop_button = QtWidgets.QPushButton(Form)
        self.can_realsaveSave_stop_button.setMinimumSize(QtCore.QSize(0, 45))
        self.can_realsaveSave_stop_button.setObjectName("can_realsaveSave_stop_button")
        self.horizontalLayout_3.addWidget(self.can_realsaveSave_stop_button)
        spacerItem1 = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout_3.addItem(spacerItem1)
        self.can_clear_button = QtWidgets.QPushButton(Form)
        self.can_clear_button.setMinimumSize(QtCore.QSize(0, 45))
        self.can_clear_button.setObjectName("can_clear_button")
        self.horizontalLayout_3.addWidget(self.can_clear_button)
        self.horizontalLayout_3.setStretch(0, 1)
        self.horizontalLayout_3.setStretch(1, 1)
        self.horizontalLayout_3.setStretch(2, 1)
        self.horizontalLayout_3.setStretch(3, 1)
        self.horizontalLayout_3.setStretch(4, 1)
        self.horizontalLayout_3.setStretch(5, 1)
        self.horizontalLayout_3.setStretch(6, 1)
        self.horizontalLayout_2.addLayout(self.horizontalLayout_3)
        self.horizontalLayout_2.setStretch(0, 1)
        self.horizontalLayout_2.setStretch(1, 1)
        self.verticalLayout_3.addLayout(self.horizontalLayout_2)
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.groupBox = QtWidgets.QGroupBox(Form)
        self.groupBox.setObjectName("groupBox")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.groupBox)
        self.verticalLayout.setObjectName("verticalLayout")
        self.dbc_treeWidget = QtWidgets.QTreeWidget(self.groupBox)
        self.dbc_treeWidget.setObjectName("dbc_treeWidget")
        self.verticalLayout.addWidget(self.dbc_treeWidget)
        self.horizontalLayout_5.addWidget(self.groupBox)
        self.groupBox_2 = QtWidgets.QGroupBox(Form)
        self.groupBox_2.setObjectName("groupBox_2")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.groupBox_2)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.can_time_select = QtWidgets.QLineEdit(self.groupBox_2)
        self.can_time_select.setMinimumSize(QtCore.QSize(0, 45))
        self.can_time_select.setText("")
        self.can_time_select.setObjectName("can_time_select")
        self.horizontalLayout_4.addWidget(self.can_time_select)
        self.can_origin_select = QtWidgets.QLineEdit(self.groupBox_2)
        self.can_origin_select.setMinimumSize(QtCore.QSize(0, 45))
        self.can_origin_select.setText("")
        self.can_origin_select.setObjectName("can_origin_select")
        self.horizontalLayout_4.addWidget(self.can_origin_select)
        self.can_ID_select = QtWidgets.QLineEdit(self.groupBox_2)
        self.can_ID_select.setMinimumSize(QtCore.QSize(0, 45))
        self.can_ID_select.setText("")
        self.can_ID_select.setObjectName("can_ID_select")
        self.horizontalLayout_4.addWidget(self.can_ID_select)
        self.can_type_select = QtWidgets.QComboBox(self.groupBox_2)
        self.can_type_select.setMinimumSize(QtCore.QSize(0, 45))
        self.can_type_select.setObjectName("can_type_select")
        self.can_type_select.addItem("")
        self.can_type_select.addItem("")
        self.can_type_select.addItem("")
        self.can_type_select.addItem("")
        self.horizontalLayout_4.addWidget(self.can_type_select)
        self.can_direction_select = QtWidgets.QComboBox(self.groupBox_2)
        self.can_direction_select.setMinimumSize(QtCore.QSize(0, 45))
        self.can_direction_select.setObjectName("can_direction_select")
        self.can_direction_select.addItem("")
        self.can_direction_select.addItem("")
        self.can_direction_select.addItem("")
        self.horizontalLayout_4.addWidget(self.can_direction_select)
        self.can_lenght_select = QtWidgets.QLineEdit(self.groupBox_2)
        self.can_lenght_select.setMinimumSize(QtCore.QSize(0, 45))
        self.can_lenght_select.setText("")
        self.can_lenght_select.setObjectName("can_lenght_select")
        self.horizontalLayout_4.addWidget(self.can_lenght_select)
        self.can_data_select = QtWidgets.QLineEdit(self.groupBox_2)
        self.can_data_select.setMinimumSize(QtCore.QSize(0, 45))
        self.can_data_select.setText("")
        self.can_data_select.setObjectName("can_data_select")
        self.horizontalLayout_4.addWidget(self.can_data_select)
        self.horizontalLayout_4.setStretch(0, 3)
        self.horizontalLayout_4.setStretch(1, 1)
        self.horizontalLayout_4.setStretch(2, 2)
        self.horizontalLayout_4.setStretch(5, 1)
        self.horizontalLayout_4.setStretch(6, 4)
        self.verticalLayout_2.addLayout(self.horizontalLayout_4)
        self.can_tableWidget = QtWidgets.QTableWidget(self.groupBox_2)
        self.can_tableWidget.setObjectName("can_tableWidget")
        self.can_tableWidget.setColumnCount(9)
        self.can_tableWidget.setRowCount(0)
        item = QtWidgets.QTableWidgetItem()
        self.can_tableWidget.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.can_tableWidget.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.can_tableWidget.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.can_tableWidget.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.can_tableWidget.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.can_tableWidget.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.can_tableWidget.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.can_tableWidget.setHorizontalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.can_tableWidget.setHorizontalHeaderItem(8, item)
        self.verticalLayout_2.addWidget(self.can_tableWidget)
        self.horizontalLayout_5.addWidget(self.groupBox_2)
        self.horizontalLayout_5.setStretch(0, 1)
        self.horizontalLayout_5.setStretch(1, 1)
        self.verticalLayout_3.addLayout(self.horizontalLayout_5)
        self.verticalLayout_3.setStretch(0, 1)
        self.verticalLayout_3.setStretch(1, 20)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.dbc_choice_button.setText(_translate("Form", "选择DBC"))
        self.dbc_path.setText(_translate("Form", "请加载DBC文件..."))
        self.dbc_load_button.setText(_translate("Form", "加载"))
        self.connect_type.setItemText(0, _translate("Form", "请选择通讯方式"))
        self.connect_type.setItemText(1, _translate("Form", "PCAN"))
        self.connect_type.setItemText(2, _translate("Form", "周立功"))
        self.connect_type.setItemText(3, _translate("Form", "CANOE"))
        self.connect_type.setItemText(4, _translate("Form", "LIN"))
        self.can_start_button.setText(_translate("Form", "开始"))
        self.can_stop_button.setText(_translate("Form", "暂停"))
        self.can_realsave_button.setText(_translate("Form", "实时保存开始"))
        self.can_realsaveSave_stop_button.setText(_translate("Form", "实时保存停止"))
        self.can_clear_button.setText(_translate("Form", "清空"))
        self.groupBox.setTitle(_translate("Form", "DBC视图"))
        self.dbc_treeWidget.headerItem().setText(0, _translate("Form", "次数"))
        self.dbc_treeWidget.headerItem().setText(1, _translate("Form", "时间"))
        self.dbc_treeWidget.headerItem().setText(2, _translate("Form", "间隔时间"))
        self.dbc_treeWidget.headerItem().setText(3, _translate("Form", "消息名"))
        self.dbc_treeWidget.headerItem().setText(4, _translate("Form", "帧ID"))
        self.dbc_treeWidget.headerItem().setText(5, _translate("Form", "帧类型"))
        self.dbc_treeWidget.headerItem().setText(6, _translate("Form", "方向"))
        self.dbc_treeWidget.headerItem().setText(7, _translate("Form", "长度"))
        self.dbc_treeWidget.headerItem().setText(8, _translate("Form", "数据"))
        self.groupBox_2.setTitle(_translate("Form", "CAN视图"))
        self.can_type_select.setItemText(0, _translate("Form", "全部"))
        self.can_type_select.setItemText(1, _translate("Form", "CAN"))
        self.can_type_select.setItemText(2, _translate("Form", "CANFD"))
        self.can_type_select.setItemText(3, _translate("Form", "CANFD加速"))
        self.can_direction_select.setItemText(0, _translate("Form", "全部"))
        self.can_direction_select.setItemText(1, _translate("Form", "Tx"))
        self.can_direction_select.setItemText(2, _translate("Form", "Rx"))
        item = self.can_tableWidget.horizontalHeaderItem(0)
        item.setText(_translate("Form", "次数"))
        item = self.can_tableWidget.horizontalHeaderItem(1)
        item.setText(_translate("Form", "时间标识"))
        item = self.can_tableWidget.horizontalHeaderItem(2)
        item.setText(_translate("Form", "数据时间差"))
        item = self.can_tableWidget.horizontalHeaderItem(3)
        item.setText(_translate("Form", "源通道"))
        item = self.can_tableWidget.horizontalHeaderItem(4)
        item.setText(_translate("Form", "帧ID"))
        item = self.can_tableWidget.horizontalHeaderItem(5)
        item.setText(_translate("Form", "CAN类型"))
        item = self.can_tableWidget.horizontalHeaderItem(6)
        item.setText(_translate("Form", "方向"))
        item = self.can_tableWidget.horizontalHeaderItem(7)
        item.setText(_translate("Form", "长度"))
        item = self.can_tableWidget.horizontalHeaderItem(8)
        item.setText(_translate("Form", "数据"))
