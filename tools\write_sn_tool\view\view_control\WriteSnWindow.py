# -*- coding: utf-8 -*-
"""
user:Created by jid on 2020/12/17
email:<EMAIL>
description:
"""
import operator

from PyQt5.QtWidgets import QMainWindow

from adb.AdbConnectDevice import adb_connect_device
from common.LogUtils import logger
from tools.write_sn_tool.view.ui.MainFrame import Ui_MainFrame
from utils.SignalsManager import signals_manager


class WriteSnWindow(QMainWindow, Ui_MainFrame):

    def __init__(self):
        super(WriteSnWindow, self).__init__()
        self.setup_window()
        self.hw_serial = None
        self.serial_list = None
        self.device_ip = None
        self.serial_number = None
        self.write_type_dict = {'HWSN': 1, 'PSN': 2}
        self.write_type = 0
        self.register_event()
        signals_manager.reply_adb_forward_str_msg.connect(self.update_adb_forward_str_msg)

    def update_read_sn(self, sn):
        if sn is not None:
            self.read_content_le.setText(str(sn))

    def setup_window(self):
        self.setupUi(self)
        self.setWindowTitle('写号工具')
        self.write_type_cb.setFixedWidth(150)

    def register_event(self):
        self.write_type_cb.addItems(self.write_type_dict.keys())
        self.write_btn.clicked.connect(self.write_sn)
        self.read_btn.clicked.connect(self.read_sn)

    def write_sn(self):
        self.write_type = self.write_type_dict[self.write_type_cb.currentText()]
        sn = self.write_content_le.text()
        if operator.eq("", sn):
            return logger.warning(f"write_sn 请输入有效的SN号")
        if operator.eq("HWSN", self.write_type):
            adb_connect_device.write_hwsn(sn)
        elif operator.eq("PSN", self.write_type):
            adb_connect_device.write_psn(sn)
        return True

    def read_sn(self):
        self.write_type = self.write_type_dict[self.write_type_cb.currentText()]
        if operator.eq("HWSN", self.write_type):
            adb_connect_device.read_hwsn(len(self.write_content_le.text()))
        elif operator.eq("PSN", self.write_type):
            adb_connect_device.read_psn(len(self.write_content_le.text()))
        return True

    def update_adb_forward_str_msg(self, action, value):
        if not operator.eq("HeartBreak", action):
            logger.info(f"update_adb_forward_str_msg action={action}, value={value}")

        self.write_type = self.write_type_dict[self.write_type_cb.currentText()]
        if operator.eq("HWSN", self.write_type) and action == "readHWSN":
            self.read_content_le.setText(value)
        elif operator.eq("PSN", self.write_type) and action == "readPSN":
            self.read_content_le.setText(value)
