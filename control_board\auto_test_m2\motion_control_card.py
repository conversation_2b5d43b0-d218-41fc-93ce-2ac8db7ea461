import os
import logging
import queue
import time
import threading
import traceback

from .. import gg_gen as gen
from .axis import Axis
from .io import IO
from .constants import CONF
from ..common.log import get_logger

logger = get_logger("auto_test_m2")

CORE = 1


class MotionControlCardState:
    def __init__(self):
        self.bad_bit = 0
        self.fail_bit = 0
        self.good_bit = 0
        self.stop_bit = 0
        self.init_bit = 0

        self.home_result_bit = 0
        self.home_succeed_bit = 0

        self.trap_move_bit = 0
        self.trap_move_result_bit = 0
        self.trap_move_succeed_bit = 0

        self.err_msg = ""


class MotionControlCardWorkCmd:
    def __init__(self, name):
        self.name = name


class MotionControlCard:
    def __init__(self):
        super().__init__()

        self.conf = CONF
        self.state = MotionControlCardState()

        self.axis_count = self.conf.get("axis").get("count")
        self.axis_d = {}
        for i in range(self.axis_count):
            self.axis_d[i + 1] = Axis(CORE, i + 1)
        self.io = IO()

        self.work_queue = queue.Queue()

        self.work_thread = threading.Thread(target=self._work, daemon=True)
        self.status_monitor_thread = threading.Thread(target=self._status_monitor, daemon=True)

    def init(self):
        if self.state.init_bit:
            return 0

        r = gen.GTN_Open()
        if r != 0:
            logger.error(f"init GTN_Open failed {r}")
            self.state.bad_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"init GTN_Open failed {r}"
            return r

        r = gen.GTN_TerminateEcatComm(CORE)
        if r != 0:
            logger.error(f"init GTN_TerminateEcatComm failed {r}")
            self.state.bad_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"init GTN_TerminateEcatComm failed {r}"
            return r

        eni_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Gecat.xml")
        r = gen.GTN_InitEcatCommEx(CORE, eni_path)
        if r != 0:
            logger.error(f"init GTN_InitEcatCommEx failed {r}")
            self.state.bad_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"init GTN_InitEcatCommEx failed {r}"
            return r

        s_time = time.time()
        while True:
            r, status = gen.GTN_IsEcatReady(CORE)

            if r != 0:
                logger.error(f"init GTN_IsEcatReady failed {r}")
                self.state.bad_bit = 1
                self.state.good_bit = 0
                self.state.err_msg = f"init GTN_IsEcatReady failed {r}"
                return r

            if time.time() - s_time > 5:
                logger.error(f"init GTN_IsEcatReady timeout")
                self.state.bad_bit = 1
                self.state.good_bit = 0
                self.state.err_msg = f"init GTN_IsEcatReady timeout"
                return 1

            if status == 1:
                break

        r = gen.GTN_StartEcatComm(CORE)
        if r != 0:
            logger.error(f"init GTN_StartEcatComm failed {r}")
            self.state.bad_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"init GTN_StartEcatComm failed {r}"
            return r

        r = gen.GTN_Reset(CORE)
        if r != 0:
            logger.error(f"init GTN_Reset failed {r}")
            self.state.bad_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"init GTN_StartEcatComm failed {r}"
            return r

        cfg_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "gtn_core1.cfg")
        r = gen.GTN_LoadConfig(CORE, cfg_path)
        if r != 0:
            logger.error(f"init GTN_LoadConfig failed {r}")
            self.state.bad_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"init GTN_StartEcatComm failed {r}"
            return r

        r, mc, ioc = gen.GTN_GetEcatSlaves(CORE)
        if r != 0:
            logger.error(f"init GTN_GetEcatSlaves failed {r}")
            self.state.bad_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"init GTN_StartEcatComm failed {r}"
            return r
        logger.info(f"axis count {mc}, io count: {ioc}")

        if mc != self.axis_count:
            logger.error(f"init slave motion cnt is not correct {mc}")
            self.state.bad_bit = 1
            self.state.good_bit = 0
            self.state.err_msg = f"init slave motion cnt is not correct {mc}"
            return 1

        for index in self.axis_d:
            r = self.axis_d[index].init()
            if r != 0:
                logger.error(f"init axis({index}) init failed")
                self.state.bad_bit = 1
                self.state.good_bit = 0
                self.state.err_msg = f"init axis({index}) init failed"
                return r

        # r = self.io.init()
        # if r != 0:
        #     logger.error(f"init io init failed")
        #     self.state.bad_bit = 1
        #     self.state.good_bit = 0
        #     self.state.err_msg = f"init io init failed"
        #     return r

        self.work_thread.start()
        self.status_monitor_thread.start()

        self.state.init_bit = 1
        self.state.good_bit = 1

        return 0

    def close(self):
        for i in self.axis_d:
            self.axis_d[i].axis_off()
        gen.GTN_TerminateEcatComm(CORE)
        gen.GTN_Close()

    def stop(self):
        for i in self.axis_d:
            self.axis_d[i].stop()

    def trap_move(self, pos_d, block=False):
        if self.state.trap_move_bit == 1:
            return 1
        self.state.trap_move_bit = 1
        self.state.trap_move_result_bit = 0
        self.state.trap_move_succeed_bit = 0

        cmd = MotionControlCardWorkCmd("trap_move")
        cmd.pos_d = pos_d
        self.work_queue.put(cmd)

        if block:
            while True:
                if self.state.trap_move_result_bit == 1:
                    break
                time.sleep(0.1)

        return 0

    def _trap_move(self, pos_d):
        for i in pos_d:
            axis = self.axis_d.get(i)
            if axis:
                acc = axis.conf.get("home_acc")
                dec = axis.conf.get("home_dec")
                smoothTime = axis.conf.get("home_smoothTime")
                vel = axis.conf.get("home_vel")
                velStart = axis.conf.get("home_velStart")

                r = axis.trap_move(pos_d[i], vel, acc, dec, velStart, smoothTime)
                if r != 0:
                    return r

        while True:
            f = True

            for i in pos_d:
                axis = self.axis_d.get(i)
                if axis:
                    if axis.state.trap_move_result_bit != 1:
                        f = False

            if f:
                break

            time.sleep(0.1)

        f = 1
        for i in pos_d:
            axis = self.axis_d.get(i)
            if axis:
                if axis.state.trap_move_succeed_bit != 1:
                    f = 0
        self.state.trap_move_succeed_bit = f

        return 0

    def home(self, block=False):
        self.state.home_result_bit = 0
        self.state.home_succeed_bit = 0

        cmd = MotionControlCardWorkCmd("home")
        self.work_queue.put(cmd)

        if block:
            while True:
                if self.state.home_result_bit == 1:
                    break
                time.sleep(0.1)

    def _home(self):
        print(1)
        self.axis_d[1].home()
        self.axis_d[2].home()

        while True:
            if self.axis_d[1].state.home_result_bit == 1 and self.axis_d[2].state.home_result_bit == 1:
                break
            time.sleep(0.1)
        if self.axis_d[1].state.home_succeed_bit != 1 or self.axis_d[2].state.home_succeed_bit != 1:
            print("*****")
            return 1

        self.axis_d[3].home()
        self.axis_d[4].home()
        self.axis_d[5].home()

        while True:
            if (self.axis_d[3].state.home_result_bit == 1 and self.axis_d[4].state.home_result_bit == 1
                    and self.axis_d[5].state.home_result_bit == 1):
                break
            time.sleep(0.1)

        if (self.axis_d[3].state.home_succeed_bit != 1 or self.axis_d[4].state.home_succeed_bit != 1 or
                self.axis_d[5].state.home_succeed_bit != 1):
            return 1
        self.state.home_succeed_bit = 1
        return 0

    def _work(self):
        while True:
            try:
                cmd = self.work_queue.get()

                if cmd.name == "home":
                    self._home()
                    self.state.home_result_bit = 1
                    self.state.trap_move_bit = 0
                    logger.info("home %s", self.state.home_succeed_bit)

                elif cmd.name == "trap_move":
                    self._trap_move(cmd.pos_d)
                    self.state.trap_move_result_bit = 1

            except Exception:
                logger.error("motion control card work thread error \n%s", traceback.format_exc())

    def _status_monitor(self):
        pass

    def __del__(self):
        self.close()


mcc = MotionControlCard()
