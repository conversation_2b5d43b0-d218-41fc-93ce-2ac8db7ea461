import threading
import random

from common.LogUtils import logger
from utils.PowerManager import power_manager
from utils.SignalsManager import signals_manager


class LoopPowerOnOffManager:

    def __init__(self):
        super().__init__()
        self.case_number = ""
        self.command = ""
        self.power_on_volt = 0
        self.power_on_delay_min = 0
        self.power_on_delay_max = 0
        self.power_off_volt = 0
        self.power_off_delay_min = 0
        self.power_off_delay_max = 0
        self.execute_times = 0
        self.current_times = 0
        self.channel = 1

    def execute_loop(self, case_number, command, power_on_volt, power_on_delay_min, power_on_delay_max, power_off_volt,
                     power_off_delay_min, power_off_delay_max, execute_times, channel):
        logger.info(f"execute_loop case_number={case_number}, command={command}, power_on_volt={power_on_volt}, "
                    f"power_on_delay_min={power_on_delay_min}, power_on_delay_max={power_on_delay_max}, "
                    f"power_off_volt={power_off_volt}, power_off_delay_min={power_off_delay_min}, "
                    f"power_off_delay_max={power_off_delay_max}, execute_times={execute_times}, channel={channel}")
        self.case_number = case_number
        self.command = command
        self.power_on_volt = power_on_volt
        self.power_on_delay_min = power_on_delay_min
        self.power_on_delay_max = power_on_delay_max
        self.power_off_volt = power_off_volt
        self.power_off_delay_min = power_off_delay_min
        self.power_off_delay_max = power_off_delay_max
        self.execute_times = execute_times
        self.channel = channel
        self.set_power_on()

    def reset_params(self):
        self.case_number = ""
        self.command = ""
        self.power_on_volt = 0
        self.power_on_delay_min = 0
        self.power_on_delay_max = 0
        self.power_off_volt = 0
        self.power_off_delay_min = 0
        self.power_off_delay_max = 0
        self.execute_times = 0
        self.current_times = 0

    def set_power_on(self):
        logger.info(f"set_power_on current_times={self.current_times}, execute_times={self.execute_times}")
        if self.current_times > 0:
            msg = f"执行循环上下电第{self.current_times}次成功"
            signals_manager.step_execute_process.emit(self.case_number, self.command, msg)

        if self.current_times == self.execute_times:
            msg = f"执行循环上下电第{self.current_times}次成功"
            signals_manager.step_execute_finish.emit(self.case_number, self.command, "PASS", msg)
            self.reset_params()
            return

        power_status, set_status = power_manager.set_volt(self.power_on_volt, self.channel)
        logger.info(f"set_power_on power_status={power_status}, set_status={set_status}")
        if power_status and set_status:
            # 电源连接成功并且电压设置成功
            power_on_time = random.uniform(self.power_on_delay_min, self.power_on_delay_max)
            threading.Timer(interval=power_on_time, function=self.set_power_off).start()
        else:
            if not power_status:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG", "电源未连接")
            elif not set_status:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG", "电压设置失败")

            self.reset_params()

    def set_power_off(self):
        power_status, set_status = power_manager.set_volt(self.power_off_volt, self.channel)
        logger.info(f"set_power_off power_status={power_status}, set_status={set_status}")
        if power_status and set_status:
            # 电源连接成功并且电压设置成功
            power_off_time = random.uniform(self.power_off_delay_min, self.power_off_delay_max)
            threading.Timer(interval=power_off_time, function=self.set_power_on).start()
            self.current_times += 1
        else:
            if not power_status:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG", "电源未连接")
            elif not set_status:
                signals_manager.step_execute_finish.emit(self.case_number, self.command, "NG", "电压设置失败")

            self.reset_params()


loop_power_on_off_manager: LoopPowerOnOffManager = LoopPowerOnOffManager()
