from simbox_tools.AppExcept import AppExcept


class Tools(object):
    app_path = None

    def __init__(self):
        pass

    @staticmethod
    def create_simple_elemnet(doc, target, value):
        element = doc.createElement(target)
        text = doc.createTextNode(value)
        element.appendChild(text)
        return element

    @staticmethod
    def get_xml_data(xml, tag):
        element = xml.getElementsByTagName(tag)
        if (element is None) or (len(element) <= 0):
            # return None
            raise AppExcept("The project file is corrupted!")
        if (element[0] is None) or (len(element[0].childNodes) <= 0):
            # return None
            raise AppExcept("The project file is corrupted!")
        # print("-------[%s:%s]" % (tag, element[0].childNodes[0].data))
        return element[0].childNodes[0].data

    @staticmethod
    def print_list(name, data):
        print("[%s(0x%02x)] : " % (name, len(data)), end="")
        for index in data:
            print("0x%02x" % index, end=" ")
        print("")

    @staticmethod
    def log_list(name, data, file=None):
        string = "[%s(0x%02x)] :" % (name, len(data))
        for index in data:
            string += " 0x%02x" % index
        if (file is None) or (file.write is None):
            print(string)
        else:
            file.write(string)

    @staticmethod
    def set_app_path(path):
        Tools.app_path = path

    @staticmethod
    def get_app_path():
        return Tools.app_path
