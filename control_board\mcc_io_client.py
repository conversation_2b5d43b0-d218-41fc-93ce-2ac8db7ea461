import os
import time

from common.LogUtils import logger
from utils.SignalsManager import signals_manager
from control_board.auto_test_m.io import IO
from control_board import gg_gen as gen

CORE = 1


class MCCIOClient:
    def __init__(self):
        self.io = IO(2)

        self._is_open = False

    def open(self, device_name):
        if self._is_open:
            return True

        r = gen.GTN_Open()
        print(r)
        if r != 0:
            print(f"GTN_Open({r}) 失败")
            return False

        r = gen.GTN_TerminateEcatComm(CORE)
        if r != 0:
            print(f"GTN_TerminateEcatComm({r}) 失败")
            return False

        eni_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Gecat.xml")
        r = gen.GTN_InitEcatCommEx(CORE, eni_path)
        if r != 0:
            print(f"GTN_InitEcatCommEx({r}) 失败")
            return False

        start = time.time()
        while True:
            r, status = gen.GTN_IsEcatReady(CORE)
            print("GTN_IsEcatReady", r, status)
            if r != 0:
                print(f"GTN_IsEcatReady({r}) 失败")
                return False
            if status == 1:
                break
            if time.time() - start > 5:
                print(f"GTN_IsEcatReady({r}) 超时")
                return False

            time.sleep(0.1)

        r = gen.GTN_StartEcatComm(CORE)
        if r != 0:
            print(f"GTN_StartEcatComm({r}) 失败")
            return False

        r = gen.GTN_Reset(CORE)
        if r != 0:
            print(f"GTN_Reset({r}) 失败")
            return False

        cfg_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "gtn_core1.cfg")
        r = gen.GTN_LoadConfig(CORE, cfg_path)
        if r != 0:
            print(f"GTN_LoadConfig({r}) 失败")
            return False

        r, mc, ioc = gen.GTN_GetEcatSlaves(CORE)
        if r != 0:
            print(f"GTN_GetEcatSlaves({r}) 失败")
            return False

        self.io.init()
        if not self.io.status:
            print(f"io初始化失败 {self.io.error_msg}")
            return False

        self._is_open = True
        signals_manager.update_device_status_signal.emit(device_name, True)
        return True

    def close(self):
        self.io.close()

        r = gen.GTN_TerminateEcatComm(1)
        if r != 0:
            print(f"GTN_TerminateEcatComm({r}) 失败")

        r = gen.GTN_Close()
        if r != 0:
            print(f"GTN_Close({r}) 失败")

        self._is_open = False
        return True

    def tri_color_green_open(self):
        logger.info(f"tri_color_green_open")
        self.io.set_output_by_bit(1, 1, 1)

    def tri_color_green_close(self):
        logger.info(f"tri_color_green_close")
        self.io.set_output_by_bit(1, 1, 0)

    def tri_color_red_open(self):
        logger.info(f"tri_color_red_open")
        self.io.set_output_by_bit(1, 0, 1)

    def tri_color_red_close(self):
        logger.info(f"tri_color_red_close")
        self.io.set_output_by_bit(1, 0, 0)

    def tri_color_yellow_open(self):
        logger.info(f"tri_color_yellow_open")
        self.io.set_output_by_bit(1, 2, 1)

    def tri_color_yellow_close(self):
        logger.info(f"tri_color_yellow_close")
        self.io.set_output_by_bit(1, 2, 0)

    def tri_color_buzzer_open(self):
        logger.info(f"tri_color_buzzer_open")
        self.io.set_output_by_bit(1, 3, 1)

    def tri_color_buzzer_close(self):
        logger.info(f"tri_color_buzzer_close")
        self.io.set_output_by_bit(1, 3, 0)

    def open_color_green(self):
        self.tri_color_red_close()
        self.tri_color_yellow_close()
        self.tri_color_green_open()

    def open_color_red(self):
        self.tri_color_green_close()
        self.tri_color_yellow_close()
        self.tri_color_red_open()

    def open_color_yellow(self):
        self.tri_color_green_close()
        self.tri_color_red_close()
        self.tri_color_yellow_open()

    def is_open(self):
        return self._is_open


mcc_io_client: MCCIOClient = MCCIOClient()
