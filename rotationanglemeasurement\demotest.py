

def generate():
    rotate_pos = [0,30,60,90,120,150,180,210,240,270,300,330,350]
    num = 1
    for r in rotate_pos:
        # 将r转换为16进制 例如 0 为 00 00 1为 00 01
        r_high, r_low = divmod(r, 0x100)
        # 将高低字节转换为指定格式，并确保输出格式为两个字符的大写
        r_hex = '{:02X} {:02X}'.format(r_high, r_low)

        num_str = '{:02X}'.format(num)
        data = f"12 0C 12 0C 07 03 07 03 00 01 {r_hex} {num_str} {num_str} 00 00"

        # print(data)

        step = f'<step id="48A" msg="{data}" id_ivibox="" msg_ivibox="00 00 00 00 00 00 00 00" zlg="False" ivibox="False" period="False" period_time="500" uds="False" expect="id: msg:00 00 00 00 00 00 00 00">CAN</step>'

        sp = f"""
<step wait="5" expect="">WAIT</step>
<step CustomizeCMD="Mate3BeforeRotate" data="" expect="">CustomizeCMD</step>
<step wait="5" expect="">WAIT</step>
{step}
<step wait="10" expect="">WAIT</step>
<step CustomizeCMD="Mate3AfterRotate" data="" expect="">CustomizeCMD</step>
<step wait="5" expect="">WAIT</step>
        """
        if num == 1:
            print(step)
            num += 1
            continue
        print(sp)
        num += 1
        # print(sp)

if __name__ == '__main__':
    generate()
