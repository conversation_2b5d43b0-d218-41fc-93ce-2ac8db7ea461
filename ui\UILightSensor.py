# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'UILightSensor.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_LightSensorForm(object):
    def setupUi(self, LightSensorForm):
        LightSensorForm.setObjectName("LightSensorForm")
        LightSensorForm.resize(1600, 900)
        LightSensorForm.setMinimumSize(QtCore.QSize(1600, 900))
        LightSensorForm.setStyleSheet("outline: none")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(LightSensorForm)
        self.verticalLayout_2.setContentsMargins(5, 5, 0, 0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.widget = QtWidgets.QWidget(LightSensorForm)
        self.widget.setStyleSheet("margin:10; border-radius:5; ")
        self.widget.setObjectName("widget")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.widget)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setObjectName("gridLayout")
        self.label_4 = QtWidgets.QLabel(self.widget)
        self.label_4.setStyleSheet("padding:5;")
        self.label_4.setObjectName("label_4")
        self.gridLayout.addWidget(self.label_4, 1, 3, 1, 1)
        self.label_6 = QtWidgets.QLabel(self.widget)
        self.label_6.setStyleSheet("padding:5;")
        self.label_6.setObjectName("label_6")
        self.gridLayout.addWidget(self.label_6, 2, 0, 1, 1)
        self.spinBox_light_value = QtWidgets.QSpinBox(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinBox_light_value.sizePolicy().hasHeightForWidth())
        self.spinBox_light_value.setSizePolicy(sizePolicy)
        self.spinBox_light_value.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_light_value.setStyleSheet("padding:5;")
        self.spinBox_light_value.setMaximum(999999)
        self.spinBox_light_value.setObjectName("spinBox_light_value")
        self.gridLayout.addWidget(self.spinBox_light_value, 2, 4, 1, 1)
        self.horizontalSlider_light_source = QtWidgets.QSlider(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.horizontalSlider_light_source.sizePolicy().hasHeightForWidth())
        self.horizontalSlider_light_source.setSizePolicy(sizePolicy)
        self.horizontalSlider_light_source.setMaximum(255)
        self.horizontalSlider_light_source.setOrientation(QtCore.Qt.Horizontal)
        self.horizontalSlider_light_source.setObjectName("horizontalSlider_light_source")
        self.gridLayout.addWidget(self.horizontalSlider_light_source, 2, 2, 1, 1)
        self.checkBox_2 = QtWidgets.QCheckBox(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.checkBox_2.sizePolicy().hasHeightForWidth())
        self.checkBox_2.setSizePolicy(sizePolicy)
        self.checkBox_2.setStyleSheet("padding:5;")
        self.checkBox_2.setChecked(True)
        self.checkBox_2.setObjectName("checkBox_2")
        self.gridLayout.addWidget(self.checkBox_2, 0, 2, 1, 1)
        self.spinBox_test_interval = QtWidgets.QSpinBox(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinBox_test_interval.sizePolicy().hasHeightForWidth())
        self.spinBox_test_interval.setSizePolicy(sizePolicy)
        self.spinBox_test_interval.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_test_interval.setStyleSheet("padding:5;")
        self.spinBox_test_interval.setMinimum(2)
        self.spinBox_test_interval.setMaximum(99)
        self.spinBox_test_interval.setProperty("value", 2)
        self.spinBox_test_interval.setObjectName("spinBox_test_interval")
        self.gridLayout.addWidget(self.spinBox_test_interval, 1, 4, 1, 1)
        self.label_5 = QtWidgets.QLabel(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_5.sizePolicy().hasHeightForWidth())
        self.label_5.setSizePolicy(sizePolicy)
        self.label_5.setObjectName("label_5")
        self.gridLayout.addWidget(self.label_5, 0, 5, 1, 1)
        self.label_2 = QtWidgets.QLabel(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_2.sizePolicy().hasHeightForWidth())
        self.label_2.setSizePolicy(sizePolicy)
        self.label_2.setStyleSheet("padding:5;")
        self.label_2.setObjectName("label_2")
        self.gridLayout.addWidget(self.label_2, 0, 3, 1, 1)
        self.spinBoxTestTime = QtWidgets.QSpinBox(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinBoxTestTime.sizePolicy().hasHeightForWidth())
        self.spinBoxTestTime.setSizePolicy(sizePolicy)
        self.spinBoxTestTime.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBoxTestTime.setStyleSheet("padding:5;")
        self.spinBoxTestTime.setMaximum(9999)
        self.spinBoxTestTime.setProperty("value", 100)
        self.spinBoxTestTime.setObjectName("spinBoxTestTime")
        self.gridLayout.addWidget(self.spinBoxTestTime, 1, 1, 1, 1)
        self.spinBox_light_source = QtWidgets.QSpinBox(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinBox_light_source.sizePolicy().hasHeightForWidth())
        self.spinBox_light_source.setSizePolicy(sizePolicy)
        self.spinBox_light_source.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_light_source.setStyleSheet("padding:5;")
        self.spinBox_light_source.setMaximum(9999)
        self.spinBox_light_source.setObjectName("spinBox_light_source")
        self.gridLayout.addWidget(self.spinBox_light_source, 2, 1, 1, 1)
        self.label_3 = QtWidgets.QLabel(self.widget)
        self.label_3.setStyleSheet("padding:5;")
        self.label_3.setObjectName("label_3")
        self.gridLayout.addWidget(self.label_3, 0, 0, 1, 1)
        self.label = QtWidgets.QLabel(self.widget)
        self.label.setStyleSheet("padding:5;")
        self.label.setObjectName("label")
        self.gridLayout.addWidget(self.label, 1, 0, 1, 1)
        self.spinBox = QtWidgets.QSpinBox(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinBox.sizePolicy().hasHeightForWidth())
        self.spinBox.setSizePolicy(sizePolicy)
        self.spinBox.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox.setStyleSheet("padding:5;")
        self.spinBox.setMinimum(1)
        self.spinBox.setObjectName("spinBox")
        self.gridLayout.addWidget(self.spinBox, 0, 4, 1, 1)
        self.spinBox_2 = QtWidgets.QSpinBox(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinBox_2.sizePolicy().hasHeightForWidth())
        self.spinBox_2.setSizePolicy(sizePolicy)
        self.spinBox_2.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_2.setStyleSheet("padding:5;")
        self.spinBox_2.setMaximum(9999)
        self.spinBox_2.setProperty("value", 100)
        self.spinBox_2.setObjectName("spinBox_2")
        self.gridLayout.addWidget(self.spinBox_2, 0, 6, 1, 1)
        self.label_7 = QtWidgets.QLabel(self.widget)
        self.label_7.setStyleSheet("padding:5;")
        self.label_7.setObjectName("label_7")
        self.gridLayout.addWidget(self.label_7, 2, 3, 1, 1)
        self.checkBox = QtWidgets.QCheckBox(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.checkBox.sizePolicy().hasHeightForWidth())
        self.checkBox.setSizePolicy(sizePolicy)
        self.checkBox.setStyleSheet("padding:5;")
        self.checkBox.setChecked(False)
        self.checkBox.setObjectName("checkBox")
        self.gridLayout.addWidget(self.checkBox, 0, 1, 1, 1)
        self.horizontalLayout.addLayout(self.gridLayout)
        spacerItem = QtWidgets.QSpacerItem(40, 20, QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.pushButtonStart = QtWidgets.QPushButton(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButtonStart.sizePolicy().hasHeightForWidth())
        self.pushButtonStart.setSizePolicy(sizePolicy)
        self.pushButtonStart.setMinimumSize(QtCore.QSize(200, 100))
        self.pushButtonStart.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButtonStart.setStyleSheet("padding:5;")
        self.pushButtonStart.setObjectName("pushButtonStart")
        self.horizontalLayout.addWidget(self.pushButtonStart)
        self.pushButtonPause = QtWidgets.QPushButton(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButtonPause.sizePolicy().hasHeightForWidth())
        self.pushButtonPause.setSizePolicy(sizePolicy)
        self.pushButtonPause.setMinimumSize(QtCore.QSize(200, 100))
        self.pushButtonPause.setMaximumSize(QtCore.QSize(16777215, 16777215))
        self.pushButtonPause.setStyleSheet("padding:5;")
        self.pushButtonPause.setObjectName("pushButtonPause")
        self.horizontalLayout.addWidget(self.pushButtonPause)
        self.pushButtonStop = QtWidgets.QPushButton(self.widget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButtonStop.sizePolicy().hasHeightForWidth())
        self.pushButtonStop.setSizePolicy(sizePolicy)
        self.pushButtonStop.setMinimumSize(QtCore.QSize(200, 100))
        self.pushButtonStop.setStyleSheet("padding:5;")
        self.pushButtonStop.setObjectName("pushButtonStop")
        self.horizontalLayout.addWidget(self.pushButtonStop)
        self.horizontalLayout.setStretch(0, 6)
        self.horizontalLayout.setStretch(1, 1)
        self.horizontalLayout.setStretch(2, 1)
        self.horizontalLayout.setStretch(3, 1)
        self.horizontalLayout.setStretch(4, 1)
        self.horizontalLayout_2.addLayout(self.horizontalLayout)
        self.verticalLayout.addLayout(self.horizontalLayout_2)
        self.tableWidget = QtWidgets.QTableWidget(self.widget)
        self.tableWidget.setStyleSheet("border-radius:0; margin:0;")
        self.tableWidget.setObjectName("tableWidget")
        self.tableWidget.setColumnCount(4)
        self.tableWidget.setRowCount(0)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(3, item)
        self.verticalLayout.addWidget(self.tableWidget)
        self.verticalLayout.setStretch(0, 1)
        self.verticalLayout.setStretch(1, 99)
        self.verticalLayout_2.addWidget(self.widget)

        self.retranslateUi(LightSensorForm)
        QtCore.QMetaObject.connectSlotsByName(LightSensorForm)

    def retranslateUi(self, LightSensorForm):
        _translate = QtCore.QCoreApplication.translate
        LightSensorForm.setWindowTitle(_translate("LightSensorForm", "Form"))
        self.label_4.setText(_translate("LightSensorForm", "测试间隔(s):"))
        self.label_6.setText(_translate("LightSensorForm", "光源照度值"))
        self.checkBox_2.setText(_translate("LightSensorForm", "百分比"))
        self.label_5.setText(_translate("LightSensorForm", "～"))
        self.label_2.setText(_translate("LightSensorForm", "光源亮度范围:"))
        self.label_3.setText(_translate("LightSensorForm", "光源模式:"))
        self.label.setText(_translate("LightSensorForm", "测试次数"))
        self.label_7.setText(_translate("LightSensorForm", "照度计照度值"))
        self.checkBox.setText(_translate("LightSensorForm", "量程"))
        self.pushButtonStart.setText(_translate("LightSensorForm", "启动"))
        self.pushButtonPause.setText(_translate("LightSensorForm", "暂停"))
        self.pushButtonStop.setText(_translate("LightSensorForm", "停止"))
        item = self.tableWidget.horizontalHeaderItem(0)
        item.setText(_translate("LightSensorForm", "序号"))
        item = self.tableWidget.horizontalHeaderItem(1)
        item.setText(_translate("LightSensorForm", "光源亮度值"))
        item = self.tableWidget.horizontalHeaderItem(2)
        item.setText(_translate("LightSensorForm", "照度计值"))
        item = self.tableWidget.horizontalHeaderItem(3)
        item.setText(_translate("LightSensorForm", "产品光感值"))
