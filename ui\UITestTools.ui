<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TestTools</class>
 <widget class="QDialog" name="TestTools">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>800</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1200</width>
    <height>800</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_2" rowstretch="1">
   <property name="leftMargin">
    <number>20</number>
   </property>
   <property name="topMargin">
    <number>20</number>
   </property>
   <property name="rightMargin">
    <number>20</number>
   </property>
   <property name="bottomMargin">
    <number>20</number>
   </property>
   <property name="spacing">
    <number>6</number>
   </property>
   <item row="0" column="0">
    <widget class="QFrame" name="frame_2">
     <property name="styleSheet">
      <string notr="true">border-radius:5</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout" stretch="0,0,0,0">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>15</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>15</number>
      </property>
      <property name="bottomMargin">
       <number>15</number>
      </property>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,1,1,1,1,1,1,1,1,1,1,1,1">
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="mate_push_img_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>Mate推图工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="endurance_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>继电器工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="picture_tool_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>图片生成工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_4">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="anti_pinch_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>防夹测试工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_5">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="io_control_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>板卡运动控制工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_6">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="position_control_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>板卡位置控制工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_7">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1,1,1,1,1,1,1,1,1,1,1,1,1">
        <item>
         <spacer name="horizontalSpacer_8">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="dbc_send_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>DBC发送工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_9">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="dbc_recv_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>DBC接收工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_10">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="auto_press_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>自动按键工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_11">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="tri_color_light_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>三色灯工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_12">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="tommens_power_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>Tommens程控电源</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_13">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="it_m3200_power_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>IT-M3200程控电源</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_14">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1,1,1,1,1,1,1,1,1,1,1,1,1">
        <item>
         <spacer name="horizontalSpacer_16">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="rotate_test_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>角度测试工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_17">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="optical_test_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>光学测试工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_18">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="light_sensor_test_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>光感测试工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_15">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="temp_rise_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>温升测试工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_20">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="video_test_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>视频测试工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_21">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="i2c_update_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>MCU升级</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_19">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="1,1,1,1,1,1,1,1,1,1,1,1,1">
        <property name="spacing">
         <number>6</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <item>
         <spacer name="horizontalSpacer_22">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="elevation_angle_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>  仰角测量  </string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_33">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="vds_upgrade_btn">
          <property name="enabled">
           <bool>true</bool>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>   VDS升级   </string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_30">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="lin_debug_btn">
          <property name="enabled">
           <bool>true</bool>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>LIN调试工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_24">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="write_sn_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>写号工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_31">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton_2">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>调试工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_25">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton_3">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>60</height>
           </size>
          </property>
          <property name="text">
           <string>调试工具</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_23">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
