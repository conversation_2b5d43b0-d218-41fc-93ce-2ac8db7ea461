# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'ioWidget.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(884, 759)
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(Form)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.groupBox_7 = QtWidgets.QGroupBox(Form)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.groupBox_7.sizePolicy().hasHeightForWidth())
        self.groupBox_7.setSizePolicy(sizePolicy)
        self.groupBox_7.setObjectName("groupBox_7")
        self.gridLayout_7 = QtWidgets.QGridLayout(self.groupBox_7)
        self.gridLayout_7.setObjectName("gridLayout_7")
        self.label_101 = QtWidgets.QLabel(self.groupBox_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_101.sizePolicy().hasHeightForWidth())
        self.label_101.setSizePolicy(sizePolicy)
        self.label_101.setObjectName("label_101")
        self.gridLayout_7.addWidget(self.label_101, 0, 0, 1, 1)
        self.label_in_start_s_2 = QtWidgets.QLabel(self.groupBox_7)
        self.label_in_start_s_2.setObjectName("label_in_start_s_2")
        self.gridLayout_7.addWidget(self.label_in_start_s_2, 0, 1, 1, 1)
        self.label_122 = QtWidgets.QLabel(self.groupBox_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_122.sizePolicy().hasHeightForWidth())
        self.label_122.setSizePolicy(sizePolicy)
        self.label_122.setObjectName("label_122")
        self.gridLayout_7.addWidget(self.label_122, 0, 2, 1, 1)
        self.label_in_figer3_s_2 = QtWidgets.QLabel(self.groupBox_7)
        self.label_in_figer3_s_2.setObjectName("label_in_figer3_s_2")
        self.gridLayout_7.addWidget(self.label_in_figer3_s_2, 0, 3, 1, 1)
        self.label_102 = QtWidgets.QLabel(self.groupBox_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_102.sizePolicy().hasHeightForWidth())
        self.label_102.setSizePolicy(sizePolicy)
        self.label_102.setObjectName("label_102")
        self.gridLayout_7.addWidget(self.label_102, 1, 0, 1, 1)
        self.label_in_pause_s_2 = QtWidgets.QLabel(self.groupBox_7)
        self.label_in_pause_s_2.setObjectName("label_in_pause_s_2")
        self.gridLayout_7.addWidget(self.label_in_pause_s_2, 1, 1, 1, 1)
        self.label_116 = QtWidgets.QLabel(self.groupBox_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_116.sizePolicy().hasHeightForWidth())
        self.label_116.setSizePolicy(sizePolicy)
        self.label_116.setObjectName("label_116")
        self.gridLayout_7.addWidget(self.label_116, 1, 2, 1, 1)
        self.label_in_figer4_s_2 = QtWidgets.QLabel(self.groupBox_7)
        self.label_in_figer4_s_2.setObjectName("label_in_figer4_s_2")
        self.gridLayout_7.addWidget(self.label_in_figer4_s_2, 1, 3, 1, 1)
        self.label_103 = QtWidgets.QLabel(self.groupBox_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_103.sizePolicy().hasHeightForWidth())
        self.label_103.setSizePolicy(sizePolicy)
        self.label_103.setObjectName("label_103")
        self.gridLayout_7.addWidget(self.label_103, 2, 0, 1, 1)
        self.label_in_reset_s_2 = QtWidgets.QLabel(self.groupBox_7)
        self.label_in_reset_s_2.setObjectName("label_in_reset_s_2")
        self.gridLayout_7.addWidget(self.label_in_reset_s_2, 2, 1, 1, 1)
        self.label_126 = QtWidgets.QLabel(self.groupBox_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_126.sizePolicy().hasHeightForWidth())
        self.label_126.setSizePolicy(sizePolicy)
        self.label_126.setObjectName("label_126")
        self.gridLayout_7.addWidget(self.label_126, 2, 2, 1, 1)
        self.label_in_figer5_s_2 = QtWidgets.QLabel(self.groupBox_7)
        self.label_in_figer5_s_2.setObjectName("label_in_figer5_s_2")
        self.gridLayout_7.addWidget(self.label_in_figer5_s_2, 2, 3, 1, 1)
        self.label_104 = QtWidgets.QLabel(self.groupBox_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_104.sizePolicy().hasHeightForWidth())
        self.label_104.setSizePolicy(sizePolicy)
        self.label_104.setObjectName("label_104")
        self.gridLayout_7.addWidget(self.label_104, 3, 0, 1, 1)
        self.label_in_e_stop_s_2 = QtWidgets.QLabel(self.groupBox_7)
        self.label_in_e_stop_s_2.setObjectName("label_in_e_stop_s_2")
        self.gridLayout_7.addWidget(self.label_in_e_stop_s_2, 3, 1, 1, 1)
        self.label_128 = QtWidgets.QLabel(self.groupBox_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_128.sizePolicy().hasHeightForWidth())
        self.label_128.setSizePolicy(sizePolicy)
        self.label_128.setObjectName("label_128")
        self.gridLayout_7.addWidget(self.label_128, 3, 2, 1, 1)
        self.label_in_figer6_s_2 = QtWidgets.QLabel(self.groupBox_7)
        self.label_in_figer6_s_2.setObjectName("label_in_figer6_s_2")
        self.gridLayout_7.addWidget(self.label_in_figer6_s_2, 3, 3, 1, 1)
        self.label_105 = QtWidgets.QLabel(self.groupBox_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_105.sizePolicy().hasHeightForWidth())
        self.label_105.setSizePolicy(sizePolicy)
        self.label_105.setObjectName("label_105")
        self.gridLayout_7.addWidget(self.label_105, 4, 0, 1, 1)
        self.label_in_vacuum1_s_2 = QtWidgets.QLabel(self.groupBox_7)
        self.label_in_vacuum1_s_2.setObjectName("label_in_vacuum1_s_2")
        self.gridLayout_7.addWidget(self.label_in_vacuum1_s_2, 4, 1, 1, 1)
        self.label_130 = QtWidgets.QLabel(self.groupBox_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_130.sizePolicy().hasHeightForWidth())
        self.label_130.setSizePolicy(sizePolicy)
        self.label_130.setObjectName("label_130")
        self.gridLayout_7.addWidget(self.label_130, 4, 2, 1, 1)
        self.label_in_figer7_s_2 = QtWidgets.QLabel(self.groupBox_7)
        self.label_in_figer7_s_2.setObjectName("label_in_figer7_s_2")
        self.gridLayout_7.addWidget(self.label_in_figer7_s_2, 4, 3, 1, 1)
        self.label_106 = QtWidgets.QLabel(self.groupBox_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_106.sizePolicy().hasHeightForWidth())
        self.label_106.setSizePolicy(sizePolicy)
        self.label_106.setObjectName("label_106")
        self.gridLayout_7.addWidget(self.label_106, 5, 0, 1, 1)
        self.label_in_a_pressure_s_2 = QtWidgets.QLabel(self.groupBox_7)
        self.label_in_a_pressure_s_2.setObjectName("label_in_a_pressure_s_2")
        self.gridLayout_7.addWidget(self.label_in_a_pressure_s_2, 5, 1, 1, 1)
        self.label_117 = QtWidgets.QLabel(self.groupBox_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_117.sizePolicy().hasHeightForWidth())
        self.label_117.setSizePolicy(sizePolicy)
        self.label_117.setObjectName("label_117")
        self.gridLayout_7.addWidget(self.label_117, 5, 2, 1, 1)
        self.label_in_figer8_s_2 = QtWidgets.QLabel(self.groupBox_7)
        self.label_in_figer8_s_2.setObjectName("label_in_figer8_s_2")
        self.gridLayout_7.addWidget(self.label_in_figer8_s_2, 5, 3, 1, 1)
        self.label_107 = QtWidgets.QLabel(self.groupBox_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_107.sizePolicy().hasHeightForWidth())
        self.label_107.setSizePolicy(sizePolicy)
        self.label_107.setObjectName("label_107")
        self.gridLayout_7.addWidget(self.label_107, 6, 0, 1, 1)
        self.label_in_figer1_s_2 = QtWidgets.QLabel(self.groupBox_7)
        self.label_in_figer1_s_2.setObjectName("label_in_figer1_s_2")
        self.gridLayout_7.addWidget(self.label_in_figer1_s_2, 6, 1, 1, 1)
        self.label_132 = QtWidgets.QLabel(self.groupBox_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_132.sizePolicy().hasHeightForWidth())
        self.label_132.setSizePolicy(sizePolicy)
        self.label_132.setObjectName("label_132")
        self.gridLayout_7.addWidget(self.label_132, 6, 2, 1, 1)
        self.label_in_figer9_s_2 = QtWidgets.QLabel(self.groupBox_7)
        self.label_in_figer9_s_2.setObjectName("label_in_figer9_s_2")
        self.gridLayout_7.addWidget(self.label_in_figer9_s_2, 6, 3, 1, 1)
        self.label_108 = QtWidgets.QLabel(self.groupBox_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_108.sizePolicy().hasHeightForWidth())
        self.label_108.setSizePolicy(sizePolicy)
        self.label_108.setObjectName("label_108")
        self.gridLayout_7.addWidget(self.label_108, 7, 0, 1, 1)
        self.label_in_figer2_s_2 = QtWidgets.QLabel(self.groupBox_7)
        self.label_in_figer2_s_2.setObjectName("label_in_figer2_s_2")
        self.gridLayout_7.addWidget(self.label_in_figer2_s_2, 7, 1, 1, 1)
        self.label_134 = QtWidgets.QLabel(self.groupBox_7)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_134.sizePolicy().hasHeightForWidth())
        self.label_134.setSizePolicy(sizePolicy)
        self.label_134.setObjectName("label_134")
        self.gridLayout_7.addWidget(self.label_134, 7, 2, 1, 1)
        self.label_in_vacuum2_s_2 = QtWidgets.QLabel(self.groupBox_7)
        self.label_in_vacuum2_s_2.setObjectName("label_in_vacuum2_s_2")
        self.gridLayout_7.addWidget(self.label_in_vacuum2_s_2, 7, 3, 1, 1)
        self.horizontalLayout_2.addWidget(self.groupBox_7)
        self.groupBox_8 = QtWidgets.QGroupBox(Form)
        self.groupBox_8.setObjectName("groupBox_8")
        self.gridLayout_8 = QtWidgets.QGridLayout(self.groupBox_8)
        self.gridLayout_8.setObjectName("gridLayout_8")
        self.label_out_figer2_s_2 = QtWidgets.QLabel(self.groupBox_8)
        self.label_out_figer2_s_2.setObjectName("label_out_figer2_s_2")
        self.gridLayout_8.addWidget(self.label_out_figer2_s_2, 1, 1, 1, 1)
        self.label_out_start_light_s_2 = QtWidgets.QLabel(self.groupBox_8)
        self.label_out_start_light_s_2.setObjectName("label_out_start_light_s_2")
        self.gridLayout_8.addWidget(self.label_out_start_light_s_2, 7, 1, 1, 1)
        self.label_152 = QtWidgets.QLabel(self.groupBox_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_152.sizePolicy().hasHeightForWidth())
        self.label_152.setSizePolicy(sizePolicy)
        self.label_152.setObjectName("label_152")
        self.gridLayout_8.addWidget(self.label_152, 6, 3, 1, 1)
        self.label_out_vacuum2_o_s_2 = QtWidgets.QLabel(self.groupBox_8)
        self.label_out_vacuum2_o_s_2.setObjectName("label_out_vacuum2_o_s_2")
        self.gridLayout_8.addWidget(self.label_out_vacuum2_o_s_2, 7, 4, 1, 1)
        self.label_154 = QtWidgets.QLabel(self.groupBox_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_154.sizePolicy().hasHeightForWidth())
        self.label_154.setSizePolicy(sizePolicy)
        self.label_154.setObjectName("label_154")
        self.gridLayout_8.addWidget(self.label_154, 7, 0, 1, 1)
        self.label_out_figer7_s_2 = QtWidgets.QLabel(self.groupBox_8)
        self.label_out_figer7_s_2.setObjectName("label_out_figer7_s_2")
        self.gridLayout_8.addWidget(self.label_out_figer7_s_2, 3, 4, 1, 1)
        self.label_out_pause_light_s_2 = QtWidgets.QLabel(self.groupBox_8)
        self.label_out_pause_light_s_2.setObjectName("label_out_pause_light_s_2")
        self.gridLayout_8.addWidget(self.label_out_pause_light_s_2, 6, 4, 1, 1)
        self.label_136 = QtWidgets.QLabel(self.groupBox_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_136.sizePolicy().hasHeightForWidth())
        self.label_136.setSizePolicy(sizePolicy)
        self.label_136.setObjectName("label_136")
        self.gridLayout_8.addWidget(self.label_136, 1, 3, 1, 1)
        self.label_142 = QtWidgets.QLabel(self.groupBox_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_142.sizePolicy().hasHeightForWidth())
        self.label_142.setSizePolicy(sizePolicy)
        self.label_142.setObjectName("label_142")
        self.gridLayout_8.addWidget(self.label_142, 4, 0, 1, 1)
        self.label_out_buzzer_s_2 = QtWidgets.QLabel(self.groupBox_8)
        self.label_out_buzzer_s_2.setObjectName("label_out_buzzer_s_2")
        self.gridLayout_8.addWidget(self.label_out_buzzer_s_2, 0, 4, 1, 1)
        self.label_out_vacuum1_i_s_2 = QtWidgets.QLabel(self.groupBox_8)
        self.label_out_vacuum1_i_s_2.setObjectName("label_out_vacuum1_i_s_2")
        self.gridLayout_8.addWidget(self.label_out_vacuum1_i_s_2, 4, 1, 1, 1)
        self.label_138 = QtWidgets.QLabel(self.groupBox_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_138.sizePolicy().hasHeightForWidth())
        self.label_138.setSizePolicy(sizePolicy)
        self.label_138.setObjectName("label_138")
        self.gridLayout_8.addWidget(self.label_138, 1, 0, 1, 1)
        self.label_140 = QtWidgets.QLabel(self.groupBox_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_140.sizePolicy().hasHeightForWidth())
        self.label_140.setSizePolicy(sizePolicy)
        self.label_140.setObjectName("label_140")
        self.gridLayout_8.addWidget(self.label_140, 3, 3, 1, 1)
        self.label_out_figer6_s_2 = QtWidgets.QLabel(self.groupBox_8)
        self.label_out_figer6_s_2.setObjectName("label_out_figer6_s_2")
        self.gridLayout_8.addWidget(self.label_out_figer6_s_2, 2, 4, 1, 1)
        self.label_out_figer5_s_2 = QtWidgets.QLabel(self.groupBox_8)
        self.label_out_figer5_s_2.setObjectName("label_out_figer5_s_2")
        self.gridLayout_8.addWidget(self.label_out_figer5_s_2, 1, 4, 1, 1)
        self.label_144 = QtWidgets.QLabel(self.groupBox_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_144.sizePolicy().hasHeightForWidth())
        self.label_144.setSizePolicy(sizePolicy)
        self.label_144.setObjectName("label_144")
        self.gridLayout_8.addWidget(self.label_144, 2, 0, 1, 1)
        self.label_out_figer4_s_2 = QtWidgets.QLabel(self.groupBox_8)
        self.label_out_figer4_s_2.setObjectName("label_out_figer4_s_2")
        self.gridLayout_8.addWidget(self.label_out_figer4_s_2, 3, 1, 1, 1)
        self.label_out_vacuum1_o_s_2 = QtWidgets.QLabel(self.groupBox_8)
        self.label_out_vacuum1_o_s_2.setObjectName("label_out_vacuum1_o_s_2")
        self.gridLayout_8.addWidget(self.label_out_vacuum1_o_s_2, 5, 1, 1, 1)
        self.label_146 = QtWidgets.QLabel(self.groupBox_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_146.sizePolicy().hasHeightForWidth())
        self.label_146.setSizePolicy(sizePolicy)
        self.label_146.setObjectName("label_146")
        self.gridLayout_8.addWidget(self.label_146, 0, 0, 1, 1)
        self.label_out_figer1_s_2 = QtWidgets.QLabel(self.groupBox_8)
        self.label_out_figer1_s_2.setObjectName("label_out_figer1_s_2")
        self.gridLayout_8.addWidget(self.label_out_figer1_s_2, 0, 1, 1, 1)
        self.label_148 = QtWidgets.QLabel(self.groupBox_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_148.sizePolicy().hasHeightForWidth())
        self.label_148.setSizePolicy(sizePolicy)
        self.label_148.setObjectName("label_148")
        self.gridLayout_8.addWidget(self.label_148, 4, 3, 1, 1)
        self.label_156 = QtWidgets.QLabel(self.groupBox_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_156.sizePolicy().hasHeightForWidth())
        self.label_156.setSizePolicy(sizePolicy)
        self.label_156.setObjectName("label_156")
        self.gridLayout_8.addWidget(self.label_156, 7, 3, 1, 1)
        self.label_out_figer8_s_2 = QtWidgets.QLabel(self.groupBox_8)
        self.label_out_figer8_s_2.setObjectName("label_out_figer8_s_2")
        self.gridLayout_8.addWidget(self.label_out_figer8_s_2, 4, 4, 1, 1)
        self.pushButton_out_figer1_s_2 = QtWidgets.QPushButton(self.groupBox_8)
        self.pushButton_out_figer1_s_2.setObjectName("pushButton_out_figer1_s_2")
        self.gridLayout_8.addWidget(self.pushButton_out_figer1_s_2, 0, 2, 1, 1)
        self.label_150 = QtWidgets.QLabel(self.groupBox_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_150.sizePolicy().hasHeightForWidth())
        self.label_150.setSizePolicy(sizePolicy)
        self.label_150.setObjectName("label_150")
        self.gridLayout_8.addWidget(self.label_150, 2, 3, 1, 1)
        self.label_157 = QtWidgets.QLabel(self.groupBox_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_157.sizePolicy().hasHeightForWidth())
        self.label_157.setSizePolicy(sizePolicy)
        self.label_157.setObjectName("label_157")
        self.gridLayout_8.addWidget(self.label_157, 5, 3, 1, 1)
        self.label_out_figer9_s_2 = QtWidgets.QLabel(self.groupBox_8)
        self.label_out_figer9_s_2.setObjectName("label_out_figer9_s_2")
        self.gridLayout_8.addWidget(self.label_out_figer9_s_2, 5, 4, 1, 1)
        self.label_out_figer3_s_2 = QtWidgets.QLabel(self.groupBox_8)
        self.label_out_figer3_s_2.setObjectName("label_out_figer3_s_2")
        self.gridLayout_8.addWidget(self.label_out_figer3_s_2, 2, 1, 1, 1)
        self.label_158 = QtWidgets.QLabel(self.groupBox_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_158.sizePolicy().hasHeightForWidth())
        self.label_158.setSizePolicy(sizePolicy)
        self.label_158.setObjectName("label_158")
        self.gridLayout_8.addWidget(self.label_158, 6, 0, 1, 1)
        self.label_out_vacuum2_i_s_2 = QtWidgets.QLabel(self.groupBox_8)
        self.label_out_vacuum2_i_s_2.setObjectName("label_out_vacuum2_i_s_2")
        self.gridLayout_8.addWidget(self.label_out_vacuum2_i_s_2, 6, 1, 1, 1)
        self.label_159 = QtWidgets.QLabel(self.groupBox_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_159.sizePolicy().hasHeightForWidth())
        self.label_159.setSizePolicy(sizePolicy)
        self.label_159.setObjectName("label_159")
        self.gridLayout_8.addWidget(self.label_159, 3, 0, 1, 1)
        self.label_160 = QtWidgets.QLabel(self.groupBox_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_160.sizePolicy().hasHeightForWidth())
        self.label_160.setSizePolicy(sizePolicy)
        self.label_160.setObjectName("label_160")
        self.gridLayout_8.addWidget(self.label_160, 0, 3, 1, 1)
        self.label_161 = QtWidgets.QLabel(self.groupBox_8)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_161.sizePolicy().hasHeightForWidth())
        self.label_161.setSizePolicy(sizePolicy)
        self.label_161.setObjectName("label_161")
        self.gridLayout_8.addWidget(self.label_161, 5, 0, 1, 1)
        self.pushButton_out_buzzer_s_2 = QtWidgets.QPushButton(self.groupBox_8)
        self.pushButton_out_buzzer_s_2.setObjectName("pushButton_out_buzzer_s_2")
        self.gridLayout_8.addWidget(self.pushButton_out_buzzer_s_2, 0, 5, 1, 1)
        self.pushButton_out_figer2_s_2 = QtWidgets.QPushButton(self.groupBox_8)
        self.pushButton_out_figer2_s_2.setObjectName("pushButton_out_figer2_s_2")
        self.gridLayout_8.addWidget(self.pushButton_out_figer2_s_2, 1, 2, 1, 1)
        self.pushButton_out_figer3_s_2 = QtWidgets.QPushButton(self.groupBox_8)
        self.pushButton_out_figer3_s_2.setObjectName("pushButton_out_figer3_s_2")
        self.gridLayout_8.addWidget(self.pushButton_out_figer3_s_2, 2, 2, 1, 1)
        self.pushButton_out_figer4_s_2 = QtWidgets.QPushButton(self.groupBox_8)
        self.pushButton_out_figer4_s_2.setObjectName("pushButton_out_figer4_s_2")
        self.gridLayout_8.addWidget(self.pushButton_out_figer4_s_2, 3, 2, 1, 1)
        self.pushButton_out_vacuum1_i_s_2 = QtWidgets.QPushButton(self.groupBox_8)
        self.pushButton_out_vacuum1_i_s_2.setObjectName("pushButton_out_vacuum1_i_s_2")
        self.gridLayout_8.addWidget(self.pushButton_out_vacuum1_i_s_2, 4, 2, 1, 1)
        self.pushButton_out_vacuum1_o_s_2 = QtWidgets.QPushButton(self.groupBox_8)
        self.pushButton_out_vacuum1_o_s_2.setObjectName("pushButton_out_vacuum1_o_s_2")
        self.gridLayout_8.addWidget(self.pushButton_out_vacuum1_o_s_2, 5, 2, 1, 1)
        self.pushButton_out_vacuum2_i_s_2 = QtWidgets.QPushButton(self.groupBox_8)
        self.pushButton_out_vacuum2_i_s_2.setObjectName("pushButton_out_vacuum2_i_s_2")
        self.gridLayout_8.addWidget(self.pushButton_out_vacuum2_i_s_2, 6, 2, 1, 1)
        self.pushButton_out_start_light_s_2 = QtWidgets.QPushButton(self.groupBox_8)
        self.pushButton_out_start_light_s_2.setObjectName("pushButton_out_start_light_s_2")
        self.gridLayout_8.addWidget(self.pushButton_out_start_light_s_2, 7, 2, 1, 1)
        self.pushButton_out_figer5_s_2 = QtWidgets.QPushButton(self.groupBox_8)
        self.pushButton_out_figer5_s_2.setObjectName("pushButton_out_figer5_s_2")
        self.gridLayout_8.addWidget(self.pushButton_out_figer5_s_2, 1, 5, 1, 1)
        self.pushButton_out_figer6_s_2 = QtWidgets.QPushButton(self.groupBox_8)
        self.pushButton_out_figer6_s_2.setObjectName("pushButton_out_figer6_s_2")
        self.gridLayout_8.addWidget(self.pushButton_out_figer6_s_2, 2, 5, 1, 1)
        self.pushButton_out_figer7_s_2 = QtWidgets.QPushButton(self.groupBox_8)
        self.pushButton_out_figer7_s_2.setObjectName("pushButton_out_figer7_s_2")
        self.gridLayout_8.addWidget(self.pushButton_out_figer7_s_2, 3, 5, 1, 1)
        self.pushButton_out_figer8_s_2 = QtWidgets.QPushButton(self.groupBox_8)
        self.pushButton_out_figer8_s_2.setObjectName("pushButton_out_figer8_s_2")
        self.gridLayout_8.addWidget(self.pushButton_out_figer8_s_2, 4, 5, 1, 1)
        self.pushButton_out_figer9_s_2 = QtWidgets.QPushButton(self.groupBox_8)
        self.pushButton_out_figer9_s_2.setObjectName("pushButton_out_figer9_s_2")
        self.gridLayout_8.addWidget(self.pushButton_out_figer9_s_2, 5, 5, 1, 1)
        self.pushButton_out_pause_light_s_2 = QtWidgets.QPushButton(self.groupBox_8)
        self.pushButton_out_pause_light_s_2.setObjectName("pushButton_out_pause_light_s_2")
        self.gridLayout_8.addWidget(self.pushButton_out_pause_light_s_2, 6, 5, 1, 1)
        self.pushButton_out_vacuum2_o_s_2 = QtWidgets.QPushButton(self.groupBox_8)
        self.pushButton_out_vacuum2_o_s_2.setObjectName("pushButton_out_vacuum2_o_s_2")
        self.gridLayout_8.addWidget(self.pushButton_out_vacuum2_o_s_2, 7, 5, 1, 1)
        self.horizontalLayout_2.addWidget(self.groupBox_8)
        self.horizontalLayout_2.setStretch(0, 1)
        self.horizontalLayout_2.setStretch(1, 1)
        self.verticalLayout_4.addLayout(self.horizontalLayout_2)
        spacerItem = QtWidgets.QSpacerItem(20, 413, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_4.addItem(spacerItem)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.groupBox_7.setTitle(_translate("Form", "输入"))
        self.label_101.setText(_translate("Form", "启动按钮"))
        self.label_in_start_s_2.setText(_translate("Form", "none"))
        self.label_122.setText(_translate("Form", "指头3伸出"))
        self.label_in_figer3_s_2.setText(_translate("Form", "none"))
        self.label_102.setText(_translate("Form", "暂停按钮"))
        self.label_in_pause_s_2.setText(_translate("Form", "none"))
        self.label_116.setText(_translate("Form", "指头4伸出"))
        self.label_in_figer4_s_2.setText(_translate("Form", "none"))
        self.label_103.setText(_translate("Form", "复位按钮"))
        self.label_in_reset_s_2.setText(_translate("Form", "none"))
        self.label_126.setText(_translate("Form", "指头5伸出"))
        self.label_in_figer5_s_2.setText(_translate("Form", "none"))
        self.label_104.setText(_translate("Form", "急停按钮"))
        self.label_in_e_stop_s_2.setText(_translate("Form", "none"))
        self.label_128.setText(_translate("Form", "指头6伸出"))
        self.label_in_figer6_s_2.setText(_translate("Form", "none"))
        self.label_105.setText(_translate("Form", "真空1反馈"))
        self.label_in_vacuum1_s_2.setText(_translate("Form", "none"))
        self.label_130.setText(_translate("Form", "指头7伸出"))
        self.label_in_figer7_s_2.setText(_translate("Form", "none"))
        self.label_106.setText(_translate("Form", "气压检测"))
        self.label_in_a_pressure_s_2.setText(_translate("Form", "none"))
        self.label_117.setText(_translate("Form", "指头8伸出"))
        self.label_in_figer8_s_2.setText(_translate("Form", "none"))
        self.label_107.setText(_translate("Form", "指头1伸出"))
        self.label_in_figer1_s_2.setText(_translate("Form", "none"))
        self.label_132.setText(_translate("Form", "指头9伸出"))
        self.label_in_figer9_s_2.setText(_translate("Form", "none"))
        self.label_108.setText(_translate("Form", "指头2伸出"))
        self.label_in_figer2_s_2.setText(_translate("Form", "none"))
        self.label_134.setText(_translate("Form", "真空2伸出"))
        self.label_in_vacuum2_s_2.setText(_translate("Form", "none"))
        self.groupBox_8.setTitle(_translate("Form", "输出"))
        self.label_out_figer2_s_2.setText(_translate("Form", "none"))
        self.label_out_start_light_s_2.setText(_translate("Form", "none"))
        self.label_152.setText(_translate("Form", "暂停按钮灯"))
        self.label_out_vacuum2_o_s_2.setText(_translate("Form", "none"))
        self.label_154.setText(_translate("Form", "状态灯"))
        self.label_out_figer7_s_2.setText(_translate("Form", "none"))
        self.label_out_pause_light_s_2.setText(_translate("Form", "none"))
        self.label_136.setText(_translate("Form", "指头5下降"))
        self.label_142.setText(_translate("Form", "真空1吸"))
        self.label_out_buzzer_s_2.setText(_translate("Form", "none"))
        self.label_out_vacuum1_i_s_2.setText(_translate("Form", "none"))
        self.label_138.setText(_translate("Form", "指头2下降"))
        self.label_140.setText(_translate("Form", "指头7下降"))
        self.label_out_figer6_s_2.setText(_translate("Form", "none"))
        self.label_out_figer5_s_2.setText(_translate("Form", "none"))
        self.label_144.setText(_translate("Form", "指头3下降"))
        self.label_out_figer4_s_2.setText(_translate("Form", "none"))
        self.label_out_vacuum1_o_s_2.setText(_translate("Form", "none"))
        self.label_146.setText(_translate("Form", "指头1下降"))
        self.label_out_figer1_s_2.setText(_translate("Form", "none"))
        self.label_148.setText(_translate("Form", "指头8下降"))
        self.label_156.setText(_translate("Form", "真空2破"))
        self.label_out_figer8_s_2.setText(_translate("Form", "none"))
        self.pushButton_out_figer1_s_2.setText(_translate("Form", "转换"))
        self.label_150.setText(_translate("Form", "指头6下降"))
        self.label_157.setText(_translate("Form", "指头9伸出"))
        self.label_out_figer9_s_2.setText(_translate("Form", "none"))
        self.label_out_figer3_s_2.setText(_translate("Form", "none"))
        self.label_158.setText(_translate("Form", "真空2吸"))
        self.label_out_vacuum2_i_s_2.setText(_translate("Form", "none"))
        self.label_159.setText(_translate("Form", "指头4下降"))
        self.label_160.setText(_translate("Form", "蜂鸣器"))
        self.label_161.setText(_translate("Form", "真空1破"))
        self.pushButton_out_buzzer_s_2.setText(_translate("Form", "转换"))
        self.pushButton_out_figer2_s_2.setText(_translate("Form", "转换"))
        self.pushButton_out_figer3_s_2.setText(_translate("Form", "转换"))
        self.pushButton_out_figer4_s_2.setText(_translate("Form", "转换"))
        self.pushButton_out_vacuum1_i_s_2.setText(_translate("Form", "转换"))
        self.pushButton_out_vacuum1_o_s_2.setText(_translate("Form", "转换"))
        self.pushButton_out_vacuum2_i_s_2.setText(_translate("Form", "转换"))
        self.pushButton_out_start_light_s_2.setText(_translate("Form", "转换"))
        self.pushButton_out_figer5_s_2.setText(_translate("Form", "转换"))
        self.pushButton_out_figer6_s_2.setText(_translate("Form", "转换"))
        self.pushButton_out_figer7_s_2.setText(_translate("Form", "转换"))
        self.pushButton_out_figer8_s_2.setText(_translate("Form", "转换"))
        self.pushButton_out_figer9_s_2.setText(_translate("Form", "转换"))
        self.pushButton_out_pause_light_s_2.setText(_translate("Form", "转换"))
        self.pushButton_out_vacuum2_o_s_2.setText(_translate("Form", "转换"))
