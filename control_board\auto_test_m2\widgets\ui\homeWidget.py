# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'homeWidget.ui'
#
# Created by: PyQt5 UI code generator 5.15.9
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(1266, 516)
        self.verticalLayout = QtWidgets.QVBoxLayout(Form)
        self.verticalLayout.setObjectName("verticalLayout")
        self.groupBox_3 = QtWidgets.QGroupBox(Form)
        self.groupBox_3.setTitle("")
        self.groupBox_3.setObjectName("groupBox_3")
        self.gridLayout_3 = QtWidgets.QGridLayout(self.groupBox_3)
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.label_50 = QtWidgets.QLabel(self.groupBox_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_50.sizePolicy().hasHeightForWidth())
        self.label_50.setSizePolicy(sizePolicy)
        self.label_50.setObjectName("label_50")
        self.gridLayout_3.addWidget(self.label_50, 1, 1, 1, 1)
        self.label_44 = QtWidgets.QLabel(self.groupBox_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_44.sizePolicy().hasHeightForWidth())
        self.label_44.setSizePolicy(sizePolicy)
        self.label_44.setObjectName("label_44")
        self.gridLayout_3.addWidget(self.label_44, 4, 0, 1, 1)
        self.label_51 = QtWidgets.QLabel(self.groupBox_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_51.sizePolicy().hasHeightForWidth())
        self.label_51.setSizePolicy(sizePolicy)
        self.label_51.setObjectName("label_51")
        self.gridLayout_3.addWidget(self.label_51, 2, 0, 1, 1)
        self.pushButton_home_3 = QtWidgets.QPushButton(self.groupBox_3)
        self.pushButton_home_3.setObjectName("pushButton_home_3")
        self.gridLayout_3.addWidget(self.pushButton_home_3, 3, 2, 1, 1)
        self.label_45 = QtWidgets.QLabel(self.groupBox_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_45.sizePolicy().hasHeightForWidth())
        self.label_45.setSizePolicy(sizePolicy)
        self.label_45.setObjectName("label_45")
        self.gridLayout_3.addWidget(self.label_45, 4, 1, 1, 1)
        self.label_48 = QtWidgets.QLabel(self.groupBox_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_48.sizePolicy().hasHeightForWidth())
        self.label_48.setSizePolicy(sizePolicy)
        self.label_48.setObjectName("label_48")
        self.gridLayout_3.addWidget(self.label_48, 0, 1, 1, 1)
        self.label_53 = QtWidgets.QLabel(self.groupBox_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_53.sizePolicy().hasHeightForWidth())
        self.label_53.setSizePolicy(sizePolicy)
        self.label_53.setObjectName("label_53")
        self.gridLayout_3.addWidget(self.label_53, 5, 1, 1, 1)
        self.label_54 = QtWidgets.QLabel(self.groupBox_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_54.sizePolicy().hasHeightForWidth())
        self.label_54.setSizePolicy(sizePolicy)
        self.label_54.setObjectName("label_54")
        self.gridLayout_3.addWidget(self.label_54, 1, 0, 1, 1)
        self.label_46 = QtWidgets.QLabel(self.groupBox_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_46.sizePolicy().hasHeightForWidth())
        self.label_46.setSizePolicy(sizePolicy)
        self.label_46.setObjectName("label_46")
        self.gridLayout_3.addWidget(self.label_46, 5, 0, 1, 1)
        self.label_47 = QtWidgets.QLabel(self.groupBox_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_47.sizePolicy().hasHeightForWidth())
        self.label_47.setSizePolicy(sizePolicy)
        self.label_47.setObjectName("label_47")
        self.gridLayout_3.addWidget(self.label_47, 0, 0, 1, 1)
        self.label_43 = QtWidgets.QLabel(self.groupBox_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_43.sizePolicy().hasHeightForWidth())
        self.label_43.setSizePolicy(sizePolicy)
        self.label_43.setObjectName("label_43")
        self.gridLayout_3.addWidget(self.label_43, 3, 0, 1, 1)
        self.label_52 = QtWidgets.QLabel(self.groupBox_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_52.sizePolicy().hasHeightForWidth())
        self.label_52.setSizePolicy(sizePolicy)
        self.label_52.setObjectName("label_52")
        self.gridLayout_3.addWidget(self.label_52, 3, 1, 1, 1)
        self.pushButton_home_1 = QtWidgets.QPushButton(self.groupBox_3)
        self.pushButton_home_1.setObjectName("pushButton_home_1")
        self.gridLayout_3.addWidget(self.pushButton_home_1, 1, 2, 1, 1)
        self.pushButton_home_4 = QtWidgets.QPushButton(self.groupBox_3)
        self.pushButton_home_4.setObjectName("pushButton_home_4")
        self.gridLayout_3.addWidget(self.pushButton_home_4, 4, 2, 1, 1)
        self.label_49 = QtWidgets.QLabel(self.groupBox_3)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_49.sizePolicy().hasHeightForWidth())
        self.label_49.setSizePolicy(sizePolicy)
        self.label_49.setObjectName("label_49")
        self.gridLayout_3.addWidget(self.label_49, 2, 1, 1, 1)
        self.pushButton_home_2 = QtWidgets.QPushButton(self.groupBox_3)
        self.pushButton_home_2.setObjectName("pushButton_home_2")
        self.gridLayout_3.addWidget(self.pushButton_home_2, 2, 2, 1, 1)
        self.pushButton_home_5 = QtWidgets.QPushButton(self.groupBox_3)
        self.pushButton_home_5.setObjectName("pushButton_home_5")
        self.gridLayout_3.addWidget(self.pushButton_home_5, 5, 2, 1, 1)
        self.label_55 = QtWidgets.QLabel(self.groupBox_3)
        self.label_55.setObjectName("label_55")
        self.gridLayout_3.addWidget(self.label_55, 0, 2, 1, 1)
        self.verticalLayout.addWidget(self.groupBox_3)
        spacerItem = QtWidgets.QSpacerItem(20, 270, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.label_50.setText(_translate("Form", "1"))
        self.label_44.setText(_translate("Form", "1"))
        self.label_51.setText(_translate("Form", "1"))
        self.pushButton_home_3.setText(_translate("Form", "回零"))
        self.label_45.setText(_translate("Form", "4"))
        self.label_48.setText(_translate("Form", "轴"))
        self.label_53.setText(_translate("Form", "5"))
        self.label_54.setText(_translate("Form", "1"))
        self.label_46.setText(_translate("Form", "1"))
        self.label_47.setText(_translate("Form", "核"))
        self.label_43.setText(_translate("Form", "1"))
        self.label_52.setText(_translate("Form", "3"))
        self.pushButton_home_1.setText(_translate("Form", "回零"))
        self.pushButton_home_4.setText(_translate("Form", "回零"))
        self.label_49.setText(_translate("Form", "2"))
        self.pushButton_home_2.setText(_translate("Form", "回零"))
        self.pushButton_home_5.setText(_translate("Form", "回零"))
        self.label_55.setText(_translate("Form", "回零"))
