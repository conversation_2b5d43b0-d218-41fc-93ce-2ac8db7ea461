import os

from PIL import Image
from PyQt5.QtGui import QDragEnterEvent, QDropEvent, QPixmap
from PyQt5.QtWidgets import QWidget, QFileDialog

from common.view.MessageDialog import MessageDialog
from mate.ui.PushImage import Ui_PushImgForm
from simbox_tools.main import simbox_control


class PushImgWindow(QWidget, Ui_PushImgForm):
    def __init__(self, parent=None):
        super(PushImgWindow, self).__init__(parent=parent)
        self.setupUi(self)
        self.setWindowTitle("推送图片")
        self.setAcceptDrops(True)
        self.toolButtonOpen.clicked.connect(self.open_img)
        self.pushButtonPushImg.clicked.connect(self.push_img)
        self.pushButtonPushVideo.clicked.connect(self.push_video)
        self.pushButtonWB.clicked.connect(self.push_wb)
        self.pushButtonRotate.clicked.connect(self.push_rotate)
        self.pushButtonMate3Rotate.clicked.connect(self.push_mate3_rotate)
        path = os.path.join(os.getcwd(), "images", "ca6.jpg")
        self.lineEdit.setText(path)

    def push_mate3_rotate(self):
        path = self.lineEdit.text()
        if path == "" or not os.path.isfile(path):
            MessageDialog.show_message("提示", "路径为空或文件不存在！")
            return
        yaw_angle = self.spinBoxYaw.value() + 170  #
        pitch_angle = self.spinBoxPitch.value()
        if yaw_angle > 340:
            MessageDialog.show_message("提示", "yaw_angle 范围0~340 ")
            return
        simbox_control.push_img_rotate2mate3(img_path=path, yaw_angle=yaw_angle, pitch_angle=pitch_angle)

    def push_rotate(self):
        angle = self.spinBox.value()
        simbox_control.push_rotate2nomi(angle)

    def push_wb(self):
        simbox_control.push_wb2nomi()

    def push_video(self):
        path = self.lineEdit.text()
        print(path)
        if path == "" or not os.path.isfile(path):
            MessageDialog.show_message("提示", "路径为空或文件不存在！")
            return

        simbox_control.push_video2nomi(path)

    def crop_center(self, img, desired_size):
        """从图片的中心裁剪出指定大小的部分。"""
        w, h = img.size
        left = (w - desired_size[0]) / 2
        top = (h - desired_size[1]) / 2
        right = (w + desired_size[0]) / 2
        bottom = (h + desired_size[1]) / 2

        return img.crop((left, top, right, bottom))

    def resize_image(self, input_path, output_path, size=(480, 480)):
        """调整图片大小为指定的尺寸并保存到指定路径."""
        with Image.open(input_path) as img:
            # 确定需要裁剪到的尺寸
            min_side = min(img.size)
            left = int((img.size[0] - min_side) / 2)
            top = int((img.size[1] - min_side) / 2)
            right = int((img.size[0] + min_side) / 2)
            bottom = int((img.size[1] + min_side) / 2)

            # 从中心裁剪出1:1的部分
            img = img.crop((left, top, right, bottom))

            resized_img = img.resize(size, )
            resized_img.save(output_path)

    def push_img(self):
        # 判断
        path = self.lineEdit.text()
        print(path)
        if path == "" or not os.path.isfile(path):
            MessageDialog.show_message("提示", "路径为空或文件不存在！")
            return
        output_path = os.path.join(os.getcwd(), "images", "tmp", "bg.png")

        # 将图片的尺寸修改为400*400 并另存为
        self.resize_image(path, output_path)
        # print("get_animation_state:", simbox_control.communication_manager.get_animation_state())
        # simbox_control.set_background_color(255,0,255)
        simbox_control.push_img2nomi(output_path)

    def show_img(self, file_path):
        self.labelImg.setPixmap(QPixmap(file_path).scaled(400, 400))

    def open_img(self):
        options = QFileDialog.Options()
        file_name, _ = QFileDialog.getOpenFileName(self, "选择文件", "", "所有文件 (*);;图片文件 (*.png *.jpg *.jpeg)",
                                                   options=options)
        if file_name:
            self.lineEdit.setText(file_name)
            self.show_img(file_name)

    def dragEnterEvent(self, event: QDragEnterEvent):
        # 检查拖入的数据是否包含一个 URL
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dropEvent(self, event: QDropEvent):
        # 获取拖入的文件路径，并更新标签的文本
        mime_data = event.mimeData()

        if mime_data.hasUrls():
            file_path = mime_data.urls()[0].toLocalFile()
            self.lineEdit.setText(file_path)
            self.show_img(file_path)
