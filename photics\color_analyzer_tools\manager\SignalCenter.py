# -*- coding: utf-8 -*-
"""
user:Created by jid on 2021/4/1 11:35
email:<EMAIL>
description:
"""
from PyQt5.QtCore import QObject, pyqtSignal

from photics.color_analyzer_tools.manager import MeasureType


class SignalCenter(QObject):
    gamma_measure_event_signal = pyqtSignal(MeasureType)
    white_balance_measure_event_signal = pyqtSignal(MeasureType)
    brightness_measure_event_signal = pyqtSignal(MeasureType)
    gamma_curve_measure_data_signal = pyqtSignal(tuple)
    white_balance_measure_data_signal = pyqtSignal(tuple)
    brightness_curve_measure_data_signal = pyqtSignal(tuple)


signal_center: SignalCenter = SignalCenter()
