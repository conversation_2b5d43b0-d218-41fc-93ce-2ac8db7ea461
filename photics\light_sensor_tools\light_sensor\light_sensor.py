import logging

from common.LogUtils import logger
from photics.light_sensor_tools.light_sensor.light_intensity_client import LightIntensityClient
from photics.light_sensor_tools.light_sensor.light_source_client import LightSourceClient


class LightSensor:

    def __init__(self):
        self.light_source_client = LightSourceClient()
        self.light_intensity_client = LightIntensityClient()

    def set_light_intensity(self, i):
        return self.light_source_client.set_light_intensity(i)

    def set_channels_light_intensity(self, i):
        return self.light_source_client.set_channels_light_intensity(i)

    def get_light_source_intensity(self):
        return self.light_source_client.get_light_intensity()

    def get_light_source_status(self):
        return self.light_source_client.is_open()

    def get_light_intensity(self):
        return self.light_intensity_client.get_light_intensity()

    def open_light_source_client(self, port, baudrate=9600, channel=1, bytesize=8, parity="N", stopbits=1):
        try:
            self.light_source_client.open(
                port=port,
                baudrate=baudrate,
                channel=channel,
                bytesize=bytesize,
                parity=parity,
                stopbits=stopbits
            )
            return self.light_source_client.is_open()
        except Exception as e:
            logger.error("open_light_source_client exception: {}".format(str(e.args)))
            return False

    def close_light_source_client(self):
        return self.light_source_client.close()

    def open_light_intensity_client(self, port, baudrate=4800, bytesize=8, parity="N", stopbits=1, slave_id=1):
        try:
            self.light_intensity_client.open(
                port=port,
                baudrate=baudrate,
                bytesize=bytesize,
                parity=parity,
                stopbits=stopbits,
                slave_id=slave_id
            )
            return self.light_intensity_client.get_status()
        except Exception as e:
            logger.error("open_light_intensity_client exception: {}".format(str(e.args)))
            return False

    def close_light_intensity_client(self):
        return self.light_intensity_client.close()


light_sensor = LightSensor()
