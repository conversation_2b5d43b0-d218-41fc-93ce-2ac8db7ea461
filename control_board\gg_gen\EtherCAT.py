import ctypes

from .Base import gts

if gts is not None:
    gts.GTN_InitEcatCommEx.argtypes = [ctypes.c_short, ctypes.c_char_p]
    gts.GTN_InitEcatCommEx.restype = ctypes.c_short


def GTN_InitEcatCommEx(core, eniFilePath):
    return gts.GTN_InitEcatCommEx(core, eniFilePath.encode("utf-8"))


if gts is not None:
    gts.GTN_StartEcatComm.argtypes = [ctypes.c_short]
    gts.GTN_StartEcatComm.restype = ctypes.c_short


def GTN_StartEcatComm(core):
    return gts.GTN_StartEcatComm(core)


if gts is not None:
    gts.GTN_TerminateEcatComm.argtypes = [ctypes.c_short]
    gts.GTN_TerminateEcatComm.restype = ctypes.c_short


def GTN_TerminateEcatComm(core):
    return gts.GTN_TerminateEcatComm(core)


if gts is not None:
    gts.GTN_IsEcatReady.argtypes = [ctypes.c_short, ctypes.POINTER(ctypes.c_short)]
    gts.GTN_IsEcatReady.restype = ctypes.c_short


def GTN_IsEcatReady(core):
    pStatus = ctypes.c_short(0)
    r = gts.GTN_IsEcatReady(core, ctypes.byref(pStatus))
    return r, pStatus.value


if gts is not None:
    gts.GTN_GetEcatSlaves.argtypes = [ctypes.c_short, ctypes.POINTER(ctypes.c_short), ctypes.POINTER(ctypes.c_short)]
    gts.GTN_GetEcatSlaves.restype = ctypes.c_short


def GTN_GetEcatSlaves(core):
    SlaveMotionCnt = ctypes.c_short(0)
    SlaveIOCnt = ctypes.c_short(0)
    r = gts.GTN_GetEcatSlaves(core, ctypes.byref(SlaveMotionCnt), ctypes.byref(SlaveIOCnt))
    return r, SlaveMotionCnt.value, SlaveIOCnt.value


if gts is not None:
    gts.GTN_SetEcatHomingPrm.argtypes = [
        ctypes.c_short, ctypes.c_short, ctypes.c_short,
        ctypes.c_double, ctypes.c_double, ctypes.c_double,
        ctypes.c_long, ctypes.c_ushort
    ]
    gts.GTN_SetEcatHomingPrm.restype = ctypes.c_short


def GTN_SetEcatHomingPrm(core, axis, method, speed1, speed2, acc, offset, probeFunction):
    r = gts.GTN_SetEcatHomingPrm(core, axis, method, speed1, speed2, acc, offset, probeFunction)
    return r


if gts is not None:
    gts.GTN_SetHomingMode.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_short]
    gts.GTN_SetHomingMode.restype = ctypes.c_short


def GTN_SetHomingMode(core, axis, mode):
    r = gts.GTN_SetHomingMode(core, axis, mode)
    return r


if gts is not None:
    gts.GTN_StartEcatHoming.argtypes = [ctypes.c_short, ctypes.c_short]
    gts.GTN_StartEcatHoming.restype = ctypes.c_short


def GTN_StartEcatHoming(core, axis):
    r = gts.GTN_StartEcatHoming(core, axis)
    return r


if gts is not None:
    gts.GTN_StopEcatHoming.argtypes = [ctypes.c_short, ctypes.c_short]
    gts.GTN_StopEcatHoming.restype = ctypes.c_short


def GTN_StopEcatHoming(core, axis):
    r = gts.GTN_StopEcatHoming(core, axis)
    return r


if gts is not None:
    gts.GTN_GetEcatEncPos.argtypes = [
        ctypes.c_short, ctypes.c_short, ctypes.POINTER(ctypes.c_long)
    ]
    gts.GTN_GetEcatEncPos.restype = ctypes.c_short


def GTN_GetEcatEncPos(core, axis):
    pos = ctypes.c_long(0)
    r = gts.GTN_GetEcatEncPos(core, axis, ctypes.byref(pos))
    return r, pos.value


if gts is not None:
    gts.GTN_EcatIOReadInput.argtypes = [
        ctypes.c_short, ctypes.c_ushort, ctypes.c_ushort,
        ctypes.c_ushort, ctypes.POINTER(ctypes.c_ubyte)
    ]
    gts.GTN_EcatIOReadInput.restype = ctypes.c_short


def GTN_EcatIOReadInput(core, slave, offset, nSize):
    value = ctypes.c_ubyte(0)
    r = gts.GTN_EcatIOReadInput(core, slave, offset, nSize, ctypes.byref(value))
    return r, value.value
