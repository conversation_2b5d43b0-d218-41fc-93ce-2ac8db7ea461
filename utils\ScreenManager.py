# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/3/31 22:24
@Desc   : 
"""


class ScreenManager:

    def __init__(self):
        self._screen_width = 0
        self._screen_height = 0

    @property
    def screen_width(self):
        return self._screen_width

    @screen_width.setter
    def screen_width(self, width):
        self._screen_width = width

    @property
    def screen_height(self):
        return self._screen_height

    @screen_height.setter
    def screen_height(self, height):
        self._screen_height = height


screen_manager: ScreenManager = ScreenManager()
