<?xml version="1.0" encoding="UTF-8"?>

<data>
    <country name="(10)诊断会话控制" >
        <country name="01-默认会话" />
        <country name="02-编程会话" />
        <country name="03-扩展诊断会话" />
        <country name="04-安全系统诊断会话" />
    </country>
    <country name="(11)ECU重置" >
        <country name="01-硬重置" />
        <country name="02-点火药匙关闭/重置" />
        <country name="03-软重置" />
        <country name="04-启动快速断电" />
        <country name="05-禁用快速断电" />
    </country>
    <country name="(14)清除诊断信息" />
    <country name="(19)读取DTC信息" >
        <country name="01-按状态掩码报告DTC数量" />
        <country name="02-按状态掩码报告DTC" />
        <country name="03-报告DTC快速标识" />
        <country name="04-按DTC编号报告DTC快照记录" />
        <country name="05-按记录编号报告DTC存储记录" />
        <country name="06-按DTC编号报告DTC扩展数据记录" />
        <country name="07-按严重性掩码记录报告DTC编号" />
        <country name="08-按严重性掩码记录报告DTC" />
        <country name="09-报告DTC严重性信息" />
        <country name="0A-报告受支持的DTC" />
        <country name="0B-报告首个失败DTC" />
        <country name="0C-报告首个确认DTC" />
        <country name="0D-报告最新测试失败DTC" />
        <country name="0E-报告最新确认DTC" />
        <country name="0F-按状态掩码报告镜像内存DTC" />
        <country name="10-按DTC编号报告镜像内存DTC扩展数据记录" />
        <country name="11-按状态掩码报告镜像内存DTC数量" />
        <country name="12-按状态掩码报告排放OBD DTC的数量" />
        <country name="13-按报告掩码报告排放OBD DTC" />
        <country name="14-报告DTC故障监测计时器" />
        <country name="15-报告为永久性状态的DTC" />
        <country name="16-按记录编号报告DTC扩展数据记录" />
        <country name="17-按状态掩码报告用户定义内存" />
        <country name="18-按DTC编号报告用户定义内存DTC快照记录" />
    </country>
    <country name="(22)按标识符读取数据" />
    <country name="(23)按地址读取内容" />
    <country name="(24)按标识符读取换算数据" />
    <country name="(27)安全访问" >
        <country name="01-请求Seed 1" />
        <country name="02-发送Key 2" />
        <country name="03-请求Seed 3" />
        <country name="04-发送Key 4" />
        <country name="05-请求Seed 5" />
        <country name="05-发送Key 6" />
    </country>
    <country name="(28)通讯控制" >
        <country name="00-启用Rx和Tx" />
        <country name="01-启动Rx和禁用Tx" />
        <country name="02-禁用Rx和启用Tx" />
        <country name="03-禁用Rx和Tx" />
        <country name="04-根据强化的地址信息启用Rx和禁用Tx" />
        <country name="05-根据强化的地址信息启用Rx和Tx" />
    </country>
    <country name="(2A)按周期性标识符读取数据" />
    <country name="(2C)动态定义数据标识符" >
        <country name="01-按标识符定义" />
        <country name="02-安内存地址定义" />
        <country name="03-清除动态定义的数据标识符" />
    </country>
    <country name="(2E)按标识符写数据" />
    <country name="(2F)按标识符的输入输出控制" />
    <country name="(31)例程控制" >
        <country name="01-启动例程" />
        <country name="02-停止例程" />
        <country name="03-请求例程结果" />
    </country>
    <country name="(34)文件下载" />
    <country name="(3D)按地址写内存" />
    <country name="(83)访问计时参数" >
        <country name="01-读取扩展的计时参数集" />
        <country name="02-计时参数设置默认值" />
        <country name="03-读取当前活动的计时参数" />
        <country name="03-计时参数设置为指定值" />
    </country>
    <country name="(84)受保护的数据传输" />
    <country name="(85)控制DTC设置" >
        <country name="01-开" />
        <country name="02-关" />
    </country>
    <country name="(86)基于事件响应" >
        <country name="00-停止基于事件响应" />
        <country name="01-关于DTC状态更改" />
        <country name="02-关于计时器中断" />
        <country name="03-关于数据标识符更改" />
        <country name="04-报告激活事件" />
        <country name="05-启动基于事件响应" />
        <country name="06-清除基于事件响应" />
        <country name="07-关闭值对比" />
    </country>
    <country name="(87)链路控制" >
        <country name="01-验证能否使用固定参数进行模式转换" />
        <country name="02-验证能否使用特定参数进行模式转换" />
        <country name="03-转换模式" />
    </country>
</data>
