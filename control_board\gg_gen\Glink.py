import ctypes

from .Base import gts


class GlinkCommSts(ctypes.Structure):
    _fields_ = [("onlineSlaveNum", ctypes.c_ubyte), ("initSlaveNum", ctypes.c_ubyte),
                ("commStatus", ctypes.c_ubyte), ("dump", ctypes.c_ubyte * 13)]


if gts is not None:
    gts.GT_GLinkInitEx.argtypes = [ctypes.c_short, ctypes.c_short]
    gts.GT_GLinkInitEx.restype = ctypes.c_short


def GT_GLinkInitEx(cardNum=0, opMode=1):
    return gts.GT_GLinkInitEx(cardNum, opMode)


if gts is not None:
    gts.GT_GLinkInit.argtypes = [ctypes.c_short]
    gts.GT_GLinkInit.restype = ctypes.c_short


def GT_GLinkInit(cardNum=0):
    return gts.GT_GLinkInit(cardNum)


if gts is not None:
    gts.GT_GetGLinkOnlineSlaveNum.argtypes = [ctypes.POINTER(ctypes.c_ubyte)]
    gts.GT_GetGLinkOnlineSlaveNum.restype = ctypes.c_short


def GT_GetGLinkOnlineSlaveNum():
    slave_num = ctypes.c_ubyte(0)
    r = gts.GT_GetGLinkOnlineSlaveNum(ctypes.byref(slave_num))
    return r, slave_num.value


if gts is not None:
    gts.GT_GetGLinkCommStatus.argtypes = [ctypes.POINTER(GlinkCommSts)]
    gts.GT_GetGLinkCommStatus.restype = ctypes.c_short


def GT_GetGLinkCommStatus():
    sts = GlinkCommSts()
    r = gts.GT_GetGLinkCommStatus(ctypes.byref(sts))
    return r, sts.onlineSlaveNum, sts.initSlaveNum, sts.commStatus


if gts is not None:
    gts.GT_GetGLinkDi.argtypes = [ctypes.c_short, ctypes.c_ushort, ctypes.POINTER(ctypes.c_ubyte), ctypes.c_ushort]
    gts.GT_GetGLinkDi.restype = ctypes.c_short


def GT_GetGLinkDi(slaveNo, offset, byteLength):
    data = ctypes.c_uint(0)
    r = gts.GT_GetGLinkDi(slaveNo, offset, ctypes.cast(ctypes.byref(data), ctypes.POINTER(ctypes.c_ubyte)), byteLength)
    return r, data.value


if gts is not None:
    gts.GT_GetGLinkDiBit.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.POINTER(ctypes.c_ubyte)]
    gts.GT_GetGLinkDiBit.restype = ctypes.c_short


def GT_GetGLinkDiBit(slaveNo, diIndex):
    value = ctypes.c_ubyte(0)
    r = gts.GT_GetGLinkDiBit(slaveNo, diIndex, ctypes.byref(value))
    return r, value.value


if gts is not None:
    gts.GT_GetGLinkDo.argtypes = [ctypes.c_short, ctypes.c_ushort, ctypes.POINTER(ctypes.c_ubyte), ctypes.c_ushort]
    gts.GT_GetGLinkDo.restype = ctypes.c_short


def GT_GetGLinkDo(slaveNo, offset, byteLength):
    data = ctypes.c_uint(0)
    r = gts.GT_GetGLinkDo(slaveNo, offset, ctypes.cast(ctypes.byref(data), ctypes.POINTER(ctypes.c_ubyte)), byteLength)
    return r, data.value


if gts is not None:
    gts.GT_SetGLinkDo.argtypes = [ctypes.c_short, ctypes.c_ushort, ctypes.POINTER(ctypes.c_ubyte), ctypes.c_ushort]
    gts.GT_SetGLinkDo.restype = ctypes.c_short


def GT_SetGLinkDo(slaveNo, offset, data, byteLength):
    data = ctypes.c_uint(data)
    r = gts.GT_SetGLinkDo(slaveNo, offset, ctypes.cast(ctypes.byref(data), ctypes.POINTER(ctypes.c_ubyte)), byteLength)
    return r


if gts is not None:
    gts.GT_SetGLinkDoBit.argtypes = [ctypes.c_short, ctypes.c_short, ctypes.c_ubyte]
    gts.GT_SetGLinkDoBit.restype = ctypes.c_short


def GT_SetGLinkDoBit(slaveNo, doIndex, value):
    if value != 0:
        value = 1
    r = gts.GT_SetGLinkDoBit(slaveNo, doIndex, value)
    return r
