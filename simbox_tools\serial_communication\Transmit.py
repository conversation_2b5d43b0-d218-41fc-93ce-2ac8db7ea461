import struct

from simbox_tools.serial_communication.SerialLink import <PERSON><PERSON><PERSON>ink

from simbox_tools.Logger import Logger


class Transmit(object):
    _log_tag = "Transmit"
    __port_list = []
    __link = None

    def __init__(self, s_func):
        self.__port_list = []
        self.__link = SerialLink(s_func, self.__recv)
        pass

    def close(self):
        Logger.console(self._log_tag, "close")
        self.__link.close()
        self.__port_list = []

    def register(self, number, r_func):
        port = TransPort(number, self.__link.send, r_func)
        self.__port_list.append(port)
        return port

    def __recv(self, data):
        if (data is None) or (0 == len(data)):
            return
        number = data[0] << 8
        number += data[1]
        for port in self.__port_list:
            if number == port.get_number():
                port.recv_proc(data[2:])

    def get_recvbytes(self):
        return self.__link.recvbytes


class TransPort(object):
    __number = 0
    recv_proc = None
    __send = None

    def __init__(self, number, s_func, r_func):
        self.__number = number
        self.recv_proc = r_func
        self.__send = s_func

    def write(self, data):
        if (data is None) or (0 == len(data)):
            return 0
        format = "BB%ds" % len(data)
        port_num_h = (self.__number & 0xff00) >> 8
        port_num_l = self.__number & 0xff
        buffer = struct.pack(format, port_num_h, port_num_l, data)
        if self.__send is not None:
            self.__send(buffer)

    def get_number(self):
        return self.__number
