"""
ADB模块 - 处理ADB相关命令
"""
import logging
import operator
from typing import Dict, Any, Set
from case.module_manager import ModuleHandler

logger = logging.getLogger(__name__)


class AdbModule(ModuleHandler):
    """ADB模块处理器"""
    
    def __init__(self):
        super().__init__()
        # 延迟加载的资源
        self.adb_connect_device = None
        self.vds_detect_manager = None
        self.signals_manager = None
        self.adb_manager = None
    
    def get_supported_commands(self) -> Set[str]:
        """获取支持的命令列表"""
        return {
            'SendCANMsg', 'SendCycleCANMsg', 'SendLINMsg', 'SendCycleLINMsg',
            'SwitchColor', 'SwitchBrightness', 'SwitchSleep', 'SwitchWakeup',
            'TpcmTest', 'SwitchBackLight', 'ReadBackLightStatus',
            'SwitchBistPattern', 'ReadBistPatternStatus', 'DisplayReboot',
            'TconReset', 'ExecuteAdbCmd', 'SerialProtocolTest'
        }
    
    def _load_resources(self):
        """加载ADB模块资源"""
        logger.info("正在加载ADB模块资源...")
        
        # 导入ADB相关模块
        from adb.AdbConnectDevice import adb_connect_device
        from case.VdsDetectManager import vds_detect_manager
        from utils.SignalsManager import signals_manager
        from adb.AdbManager import adb_manager
        
        self.adb_connect_device = adb_connect_device
        self.vds_detect_manager = vds_detect_manager
        self.signals_manager = signals_manager
        self.adb_manager = adb_manager
        
        logger.info("ADB模块资源加载完成")
    
    def execute_command(self, command: str, step: Dict[str, Any]):
        """执行ADB命令"""
        case_number = step.get("case_number", "")
        data = step.get("params", "")
        expect = step.get("expect", "")
        
        if operator.eq("SendCANMsg", command):
            self._handle_send_can_msg(case_number, command, data)
        elif operator.eq("SendCycleCANMsg", command):
            self._handle_send_cycle_can_msg(case_number, command, data)
        elif operator.eq("SendLINMsg", command):
            self._handle_send_lin_msg(case_number, command, data)
        elif operator.eq("SendCycleLINMsg", command):
            self._handle_send_cycle_lin_msg(case_number, command, data)
        elif operator.eq("SwitchColor", command):
            self._handle_switch_color(case_number, command, data)
        elif operator.eq("SwitchBrightness", command):
            self._handle_switch_brightness(case_number, command, data)
        elif operator.eq("SwitchSleep", command):
            self._handle_switch_sleep(case_number, command)
        elif operator.eq("SwitchWakeup", command):
            self._handle_switch_wakeup(case_number, command)
        elif operator.eq("TpcmTest", command):
            self._handle_tpcm_test(case_number, command)
        elif operator.eq("SwitchBackLight", command):
            self._handle_switch_back_light(case_number, command, data)
        elif operator.eq("ReadBackLightStatus", command):
            self._handle_read_back_light_status(case_number, command, expect)
        elif operator.eq("SwitchBistPattern", command):
            self._handle_switch_bist_pattern(case_number, command, data)
        elif operator.eq("ReadBistPatternStatus", command):
            self._handle_read_bist_pattern_status(case_number, command, expect)
        elif operator.eq("DisplayReboot", command):
            self._handle_display_reboot(case_number, command)
        elif operator.eq("TconReset", command):
            self._handle_tcon_reset(case_number, command)
        elif operator.eq("ExecuteAdbCmd", command):
            self._handle_execute_adb_cmd(case_number, command, data)
        elif operator.eq("SerialProtocolTest", command):
            self._handle_serial_protocol_test(case_number, command, data)
        else:
            logger.warning(f"ADB模块不支持命令: {command}")
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", f"不支持的命令: {command}")
    
    def _handle_send_can_msg(self, case_number: str, command: str, data: str):
        """处理发送CAN消息"""
        if self.adb_connect_device.is_connect():
            self.adb_connect_device.send_can_msg(data)
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")
    
    def _handle_send_cycle_can_msg(self, case_number: str, command: str, data: str):
        """处理发送周期CAN消息"""
        if self.adb_connect_device.is_connect():
            self.adb_connect_device.send_cycle_can_msg(data)
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")
    
    def _handle_send_lin_msg(self, case_number: str, command: str, data: str):
        """处理发送LIN消息"""
        if self.adb_connect_device.is_connect():
            self.adb_connect_device.send_lin_msg(data)
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")
    
    def _handle_send_cycle_lin_msg(self, case_number: str, command: str, data: str):
        """处理发送周期LIN消息"""
        if self.adb_connect_device.is_connect():
            self.adb_connect_device.send_cycle_lin_msg(data)
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")
    
    def _handle_switch_color(self, case_number: str, command: str, data: str):
        """处理切换颜色"""
        if self.adb_connect_device.is_connect():
            self.adb_connect_device.switch_color(data)
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")
    
    def _handle_switch_brightness(self, case_number: str, command: str, data: str):
        """处理切换亮度"""
        if self.adb_connect_device.is_connect():
            self.adb_connect_device.switch_brightness(brightness=data)
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")
    
    def _handle_switch_sleep(self, case_number: str, command: str):
        """处理切换睡眠"""
        self.adb_connect_device.switch_sleep()
        self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
    
    def _handle_switch_wakeup(self, case_number: str, command: str):
        """处理切换唤醒"""
        self.adb_connect_device.switch_wakeup()
        self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
    
    def _handle_tpcm_test(self, case_number: str, command: str):
        """处理TPCM测试"""
        self.adb_connect_device.test_tpcm()
        self.vds_detect_manager.set_expect_test_tpcm(case_number, command)
    
    def _handle_switch_back_light(self, case_number: str, command: str, data: str):
        """处理切换背光"""
        if self.adb_connect_device.is_connect():
            self.adb_connect_device.switch_back_light(data)
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")
    
    def _handle_read_back_light_status(self, case_number: str, command: str, expect: str):
        """处理读取背光状态"""
        if self.adb_connect_device.is_connect():
            self.adb_connect_device.read_back_light_status()
            self.vds_detect_manager.set_expect_back_light_status(case_number, command, expect)
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")
    
    def _handle_switch_bist_pattern(self, case_number: str, command: str, data: str):
        """处理切换BIST模式"""
        if self.adb_connect_device.is_connect():
            self.adb_connect_device.switch_bist_pattern(data)
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")
    
    def _handle_read_bist_pattern_status(self, case_number: str, command: str, expect: str):
        """处理读取BIST模式状态"""
        if self.adb_connect_device.is_connect():
            self.adb_connect_device.read_bist_pattern_status()
            self.vds_detect_manager.set_expect_bist_pattern_status(case_number, command, expect)
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")
    
    def _handle_display_reboot(self, case_number: str, command: str):
        """处理显示重启"""
        if self.adb_connect_device.is_connect():
            self.adb_connect_device.display_reboot()
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")
    
    def _handle_tcon_reset(self, case_number: str, command: str):
        """处理TCON重置"""
        if self.adb_connect_device.is_connect():
            self.adb_connect_device.tcon_reset()
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        else:
            self.signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")
    
    def _handle_execute_adb_cmd(self, case_number: str, command: str, data: str):
        """处理执行ADB命令"""
        self.adb_manager.handle_execute_adb_cmd(case_number, command, data)
    
    def _handle_serial_protocol_test(self, case_number: str, command: str, data: str):
        """处理串口协议测试"""
        send_data = data.split(",")[0]
        receive_data = data.split(",")[1]
        self.adb_connect_device.send_serial_protocol_data(send_data)
        if operator.eq("NA", receive_data):
            self.signals_manager.step_execute_finish.emit(case_number, command, "PASS", "串口协议数据发送成功")
        else:
            # 比对发送串口协议数据之后，接收到的数据是否和期望接收的串口协议数据为包含关系
            self.vds_detect_manager.set_expect_serial_protocol_data(case_number, command, receive_data)
