import copy
import datetime
import json
import logging
import operator
import os
import random
import re
import shutil
import threading

import psutil
import time
import traceback
from queue import Queue
from threading import Timer

import can

from adb.AdbManager import adb_manager
from common.AppConfig import app_config
from common.LogUtils import logger
from color_temp.ColorTempLightSensorAuto import color_temp_light_sensor_auto
from control_board.inspire_robots.uart_client import inspire_client

from environment.EnvTestAuto import env_test_auto
from power.manager.WorkCurrentMonitorManager import work_current_monitor_manager
from power.tools.ETM3020PCControl import etm_3020pc_control
from power.tools.etm_mu3_control import etm_mu3_control
from power.tools.it_m3200_control import it_m3200_control
from tools.tri_color_light_tool.tri_color_light import tri_color_light
from touch.TouchManager import touch_manager
from utils.DtcDetectManager import dtc_detect_manager
from utils.RecordImgAlgorithm import alg_recorder
from utils.elevation_angle_tool import elevation_angle_tool, execute_comand
from vision.VisualDetectSignal import visual_detect_signal
from utils.LogReader import logcat_reader, mcu_log_reader, soc_log_reader
from photics import photics_manager, PhoticsFunction
from photics.AutoOpticalTestManager import auto_optical_test_manager
from photics.BrightnessManager import brightness_manager
from photics.color_analyzer_tools.manager.ColorAnalyzerManager import color_analyzer_manager
from photics.light_sensor_tools.brightness.BrightnessCalibrateManager import brightness_calibrate_manager
from photics.light_sensor_tools.brightness.BrightnessTestManager import brightness_test_manager
from photics.light_sensor_tools.color_temp.ColorTempCalibrateManager import color_temp_calibrate_manager
from photics.light_sensor_tools.color_temp.ColorTempTestManager import color_temp_test_manager
from adb.AdbConnectDevice import adb_connect_device
from adb.zlgcan.zlgcan import ZCAN_TransmitFD_Data, ZCAN_Transmit_Data
from bat.BatManager import bat_manager
from case.CaseManager import case_manager, CaseStatus
from case.VdsDetectManager import vds_detect_manager
from control_board.calibration import pixel_to_physical, Calibration
from control_board.mcc_io_client import mcc_io_client
from control_board.touchPointTest import touch_card
from tools.endurance.RelayClient import relay_client
from fs_manager.FSManager import fs_manager
from hw_file_system.NextcloudHelper import create_cloud_share, upload_folder_to_cloud, upload_file_to_cloud
from logic_analyzer.LogicManager import logic_manager
from oscilloscope.OscilloscopeManager import oscilloscope_manager
from touch.AutoKeyPress import auto_key_press
from utils import listdir
from utils.CompressManager import compress_manager
from utils.LogManager import log_manager
from utils.Logger import CACHE_PATH
from utils.LoopPowerOnOffManager import loop_power_on_off_manager
from utils.PowerManager import power_manager
from utils.ProjectManager import project_manager
from utils.SignalsManager import signals_manager
from utils.TimerManager import timer_manager
from vision.CameraConfig import camera_config
from vision.CameraManager import camera_manager
from vision.FrequencyTest import frequency_test
from vision.VisionManager import vision_manager
from view.LinDebugWidget import lin_sender


class StepManager:
    DISPATCH_STEP_INTERVAL = 0.05

    def __init__(self):
        super().__init__()
        self.status = True
        self._receivers = []
        self._timer = Timer(0, self.dispatch_step)
        self._step_queue = Queue()
        self.steps = []
        self.step_index = 0
        self.step_status = {}
        self.vision_reverts = {}
        self.vision_algorithms = {}
        self.execute_modes = {}
        self.step_command = ""
        self.step_retry_times = 0
        self.lin_msg_count = 0
        self.lin_stop_event = threading.Event()
        self.check_step_timeout_timer = None
        signals_manager.step_execute_finish.connect(self.step_execute_finish)
        signals_manager.reply_adb_forward_str_msg.connect(self.update_adb_forward_str_msg)
        signals_manager.export_logic_analyzer_data.connect(self.export_logic_analyzer_data)

    def export_logic_analyzer_data(self):
        case_start_time = case_manager.get_case_start_time()
        logger.info(f"export_logic_analyzer_data case_start_time={case_start_time}")
        if case_start_time is not None:
            case_start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
            file_date = f"{case_manager.current_case_number}_{case_start_time}"
            save_time = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S-%f")[:-3]
            logic_save_path = self.save_logic_data(file_date, case_manager.current_case_number, save_time)
            logger.info(f"export_logic_analyzer_data file_date={file_date}, logic_save_path={logic_save_path}")
        else:
            logger.warning("export_logic_analyzer_data fail, case_start_time is None")

    def stop_lin_cycle_msg(self):
        self.lin_stop_event.set()

    def reset_params(self):
        logger.info("reset_params")
        self.step_index = 0
        self.step_command = ""
        self.step_status.clear()
        self.vision_reverts.clear()
        self.vision_algorithms.clear()
        self.execute_modes.clear()

    @staticmethod
    def update_adb_forward_str_msg(action, value):
        if not operator.eq("HeartBreak", action):
            logger.info(f"update_adb_forward_str_msg action={action}, value={value}")

        vds_detect_manager.detect_action(action=action, info=value)

    def upload_can_log(self, record_item_id, file_date, can_msgs):
        project_number = project_manager.get_test_plan_project_number()
        logger.info(f"upload_can_log can_msgs_size={len(can_msgs)}")
        if file_date is None:
            return
        if len(can_msgs) > 0:
            self.save_log_file(project_number, record_item_id, file_date, can_msgs, "Can")

    @staticmethod
    def upload_upper_computer_log(record_item_id, file_date):
        logger.info(f"upload_upper_computer_log record_item_id={record_item_id}, file_date={file_date}")
        if file_date is None:
            return
        from common.LogUtils import CACHE_PATH
        project_number = project_manager.get_test_plan_project_number()
        upper_computer_log_path = os.path.join(CACHE_PATH, "UpperComputer")
        # 文件生成后上传到海微文件服务器
        date = datetime.datetime.now().strftime("%Y-%m-%d")
        for resource_name in listdir(upper_computer_log_path):
            suffix = resource_name.split(".", 1)[1]
            logger.info(f"upload_upper_computer_log suffix={suffix}")
            if suffix in ["log", "log.1"]:
                upload_file_to_cloud(project_number, date, "UpperComputerLog", file_date, resource_name,
                                     os.path.join(upper_computer_log_path, resource_name))
        share_url, status = create_cloud_share(project_number, date, "UpperComputerLog", file_date)
        fs_manager.post_v2_test_records_items_resources(record_item_id, "上位机日志", share_url)

    def upload_mcu_log(self, record_item_id, file_date, mcu_msgs):
        project_number = project_manager.get_test_plan_project_number()
        logger.info(f"upload_mcu_log mcu_msgs_size={len(mcu_msgs)}")
        if file_date is None:
            return
        if len(mcu_msgs) > 0:
            self.save_log_file(project_number, record_item_id, file_date, mcu_msgs, "Mcu")

    def upload_soc_log(self, record_item_id, file_date, soc_msgs):
        project_number = project_manager.get_test_plan_project_number()
        logger.info(f"upload_mcu_log soc_msgs_size={len(soc_msgs)}")
        if file_date is None:
            return
        if len(soc_msgs) > 0:
            self.save_log_file(project_number, record_item_id, file_date, soc_msgs, "Soc")

    def upload_os_log(self, record_item_id, file_date, os_msgs):
        project_number = project_manager.get_test_plan_project_number()
        logger.info(f"upload_os_log os_msgs_size={len(os_msgs)}")
        if file_date is None:
            return
        if len(os_msgs) > 0:
            self.save_log_file(project_number, record_item_id, file_date, os_msgs, "Os")

    def upload_vds_app_log(self, record_item_id, file_date, vds_app_msgs):
        project_number = project_manager.get_test_plan_project_number()
        logger.info(f"upload_vds_app_log vds_app_msgs_size={len(vds_app_msgs)}")
        if file_date is None:
            return
        if len(vds_app_msgs) > 0:
            self.save_log_file(project_number, record_item_id, file_date, vds_app_msgs, "VdsApp")

    @staticmethod
    def save_log_file(project_number, record_item_id, file_date, msgs, log_mark=""):
        os_path = os.path.join(CACHE_PATH, log_mark)
        if not os.path.exists(os_path):
            os.makedirs(os_path)
        case_path = os.path.join(os_path, file_date)
        if not os.path.exists(case_path):
            os.makedirs(case_path)
        logger_file = os.path.join(case_path, f'{log_mark}.log')
        logger.info(f"save_log_file logger_file={logger_file}")
        with open(logger_file, "w") as file:
            file.writelines(msgs)

        os_log_path = os.path.join(CACHE_PATH, log_mark, file_date)
        # 文件生成后上传到海微文件服务器
        date = datetime.datetime.now().strftime("%Y-%m-%d")
        if operator.eq("示波器", log_mark):
            log_name = f"OscilloscopeLog"
        else:
            log_name = f"{log_mark}Log"
        upload_folder_to_cloud(project_number, date, log_name, file_date, os_log_path)
        share_url, status = create_cloud_share(project_number, date, f"{log_mark}Log", file_date)
        fs_manager.post_v2_test_records_items_resources(record_item_id, f"{log_mark}日志", share_url)

    def upload_oscilloscope_data(self, record_item_id, file_date):
        logger.info(f"upload_oscilloscope_data record_item_id={record_item_id}, file_date={file_date}")
        if file_date is None:
            return
        project_number = project_manager.get_test_plan_project_number()
        if oscilloscope_manager.is_open():
            oscilloscope_manager.stop_oscilloscope()
            time.sleep(0.5)
            # 等待0.5秒后导出通道1~4的缓存数据
            oscilloscope_manager.set_wav_params(channel=1)
            data_1 = oscilloscope_manager.output_wav_data()

            oscilloscope_manager.set_wav_params(channel=2)
            data_2 = oscilloscope_manager.output_wav_data()

            oscilloscope_manager.set_wav_params(channel=3)
            data_3 = oscilloscope_manager.output_wav_data()

            oscilloscope_manager.set_wav_params(channel=4)
            data_4 = oscilloscope_manager.output_wav_data()

            data = []
            data.extend(data_1)
            data.append("\n")
            data.extend(data_2)
            data.append("\n")
            data.extend(data_3)
            data.append("\n")
            data.extend(data_4)
            logger.info(f"upload_oscilloscope_data data={data}")
            if len(data_1) == 0 and len(data_2) == 0 and len(data_3) == 0 and len(data_4) == 0:
                logger.warning("upload_oscilloscope_data fail, data is empty")
                return
            self.save_log_file(project_number, record_item_id, file_date, str(data), "示波器")

    @staticmethod
    def upload_logic_data(record_item_id, file_date, times=10):
        logger.info(f"upload_logic_data record_item_id={record_item_id}, file_date={file_date}")
        if file_date is None:
            return
        project_number = project_manager.get_test_plan_project_number()
        logic_analyzer_path = os.path.join(CACHE_PATH, "LogicAnalyzer")
        if not os.path.exists(logic_analyzer_path):
            os.mkdir(logic_analyzer_path)
        save_path = os.path.join(logic_analyzer_path, file_date)
        if not os.path.exists(save_path):
            os.mkdir(save_path)
        files = []
        for i in range(times):
            files = os.listdir(save_path)
            if len(files) > 0:
                break
            time.sleep(0.5)
        logger.info(f"upload_logic_data files={files}")
        if len(files) > 0:
            # 文件上传到海微文件服务器
            date = datetime.datetime.now().strftime("%Y-%m-%d")
            upload_folder_to_cloud(project_number, date, "LogicAnalyzer", file_date, save_path)
            share_url, status = create_cloud_share(project_number, date, "LogicAnalyzer", file_date)
            fs_manager.post_v2_test_records_items_resources(record_item_id, "逻辑分析仪数据", share_url)

    @staticmethod
    def save_logic_data(file_date, case_number, save_time, channel="0 1 2 3 4"):
        logic_manager.stop()
        # 停止后等待0.5秒再导出数据
        time.sleep(0.5)
        logic_analyzer_path = os.path.join(CACHE_PATH, "LogicAnalyzer")
        if not os.path.exists(logic_analyzer_path):
            os.mkdir(logic_analyzer_path)
        save_path = os.path.join(logic_analyzer_path, file_date)
        if not os.path.exists(save_path):
            os.mkdir(save_path)
        file_name = os.path.join(save_path, f"{case_number}_{save_time}.kvdat --chn-select {channel}")
        logic_manager.send_message(f"export-data {file_name}")
        # TEMP_CSV_FILE_PATH = r'tmp.csv'
        # temp_dir = os.path.dirname(TEMP_CSV_FILE_PATH)
        # if temp_dir and not os.path.exists(temp_dir):
        #      try:
        #          os.makedirs(temp_dir)
        #          print(f"创建目录: {temp_dir}")
        #      except OSError as e:
        #          print(f"错误: 无法创建临时文件目录 {temp_dir}: {e}")
        #          return

        # # 确保文件名包含在引号内，以处理路径中的空格
        # logic_manager.send_message(f'export-data {TEMP_CSV_FILE_PATH} --chn-select {channel}')
        # if not logic_manager.parse_csv_to_json(TEMP_CSV_FILE_PATH):
        #     logger.info("logic_manager csv failed to json")
        return save_path

    @staticmethod
    def upload_vision_ng_img(record_item_id, file_date):
        logger.info(f"upload_vision_ng_img record_item_id={record_item_id}, file_date={file_date}")
        if file_date is None:
            return
        project_number = project_manager.get_test_plan_project_number()
        save_ng_flicker_img_path = os.path.join(camera_config.get_ng_flicker_img_path(), file_date)
        save_ng_grainy_img_path = os.path.join(camera_config.get_ng_grainy_img_path(), file_date)
        save_ng_black_img_path = os.path.join(camera_config.get_ng_black_img_path(), file_date)

        logger.info(f"upload_vision_ng_img save_ng_flicker_img_path={save_ng_flicker_img_path}")
        ng_flicker_files = []
        if os.path.exists(save_ng_flicker_img_path):
            ng_flicker_files = listdir(save_ng_flicker_img_path)
        logger.info(f"upload_vision_ng_img ng_flicker_files={ng_flicker_files}")
        if len(ng_flicker_files) > 0:
            # 文件生成后上传到海微文件服务器
            date = datetime.datetime.now().strftime("%Y-%m-%d")
            upload_folder_to_cloud(project_number, date, "VisionNgImg/NgFlickerImg", file_date,
                                   save_ng_flicker_img_path)
            share_url, upload_status = create_cloud_share(project_number, date, "VisionNgImg/NgFlickerImg", file_date)
            status, msg = fs_manager.post_v2_test_records_items_resources(record_item_id, "视觉检测闪屏NG图片",
                                                                          share_url)
            if upload_status and status:
                # 上传成功之后删除本地文件
                shutil.rmtree(save_ng_flicker_img_path)
                logger.info(f"upload_vision_ng_img delete {save_ng_flicker_img_path} success")

        logger.info(f"upload_vision_ng_img save_ng_grainy_img_path={save_ng_grainy_img_path}")
        ng_grainy_files = []
        if os.path.exists(save_ng_grainy_img_path):
            ng_grainy_files = listdir(save_ng_grainy_img_path)
        logger.info(f"upload_vision_ng_img ng_grainy_files={ng_grainy_files}")
        if len(ng_grainy_files) > 0:
            # 文件生成后上传到海微文件服务器
            date = datetime.datetime.now().strftime("%Y-%m-%d")
            upload_folder_to_cloud(project_number, date, "VisionNgImg/NgGrainyImg", file_date,
                                   save_ng_grainy_img_path)
            share_url, upload_status = create_cloud_share(project_number, date, "VisionNgImg/NgGrainyImg", file_date)
            status, msg = fs_manager.post_v2_test_records_items_resources(record_item_id, "视觉检测花屏NG图片",
                                                                          share_url)
            if upload_status and status:
                # 上传成功之后删除本地文件
                shutil.rmtree(save_ng_grainy_img_path)
                logger.info(f"upload_vision_ng_img delete {save_ng_grainy_img_path} success")

        logger.info(f"upload_vision_ng_img save_ng_black_img_path={save_ng_black_img_path}")
        ng_black_screen_files = []
        if os.path.exists(save_ng_black_img_path):
            ng_black_screen_files = listdir(save_ng_black_img_path)
        logger.info(f"upload_vision_ng_img ng_black_screen_files={ng_black_screen_files}")
        if len(ng_black_screen_files) > 0:
            # 文件生成后上传到海微文件服务器
            date = datetime.datetime.now().strftime("%Y-%m-%d")
            upload_folder_to_cloud(project_number, date, "VisionNgImg/NgBlackScreenImg", file_date,
                                   save_ng_black_img_path)
            share_url, upload_status = create_cloud_share(project_number, date, "VisionNgImg/NgBlackScreenImg",
                                                          file_date)
            status, msg = fs_manager.post_v2_test_records_items_resources(record_item_id, "视觉检测黑屏NG图片",
                                                                          share_url)
            if upload_status and status:
                # 上传成功之后删除本地文件
                shutil.rmtree(save_ng_grainy_img_path)
                logger.info(f"upload_vision_ng_img delete {save_ng_grainy_img_path} success")

    @staticmethod
    def upload_canoe_results(record_item_id, file_date):
        logger.info(f"upload_canoe_results record_item_id={record_item_id}, file_date={file_date}")
        if file_date is None:
            return
        project_number = project_manager.get_test_plan_project_number()
        # 路径
        try:
            if project_number not in ["HW-T-0019", "HW-T-0020"]:
                return
            project_extra = fs_manager.get_project_extra_info(project_number)
            logging.info(f"get_project_extra_info from {project_number}={project_extra}")
            cfg_path = project_extra["data"]["configs"]["canoe_cfg_path"]
            logging.info(f"cfg_path is {cfg_path}")
        except Exception:
            logging.info(f"cfg_path is None!{traceback.format_exc()}")
            cfg_path = None
        if not cfg_path:
            return
        # cfg_path 的父路径
        dir_p = os.path.dirname(cfg_path)
        canoe_tse_path = os.path.join(dir_p, f"log/{signals_manager.current_canoe_asc_dir}")
        logging.info(f"canoe_tse_path is {canoe_tse_path}")

        if not os.path.exists(canoe_tse_path):
            logging.info(f"no exists : {canoe_tse_path}")
            return

        time.sleep(1)
        files = listdir(canoe_tse_path)

        if len(files) > 0:
            files = listdir(canoe_tse_path)
            logger.info(f"upload_canoe_results save_path={canoe_tse_path}, files={files}")

            # 文件生成后上传到海微文件服务器
            date = datetime.datetime.now().strftime("%Y-%m-%d")
            upload_folder_to_cloud(project_number, date, "canoeAsc", file_date, canoe_tse_path)
            share_url, share_status = create_cloud_share(project_number, date, "canoeAsc", file_date)
            fs_manager.post_v2_test_records_items_resources(record_item_id, "canoe测试日志", share_url)

    @staticmethod
    def upload_monitor_video(record_item_id, file_date):
        logger.info(f"upload_monitor_video record_item_id={record_item_id}, file_date={file_date}")
        if file_date is None:
            return
        project_number = project_manager.get_test_plan_project_number()
        monitor_video_path = os.path.join(CACHE_PATH, "MonitorVideo")
        if not os.path.exists(monitor_video_path):
            os.mkdir(monitor_video_path)
        save_path = os.path.join(monitor_video_path, file_date)
        if not os.path.exists(save_path):
            os.mkdir(save_path)
        files = listdir(save_path)
        # logger.info(f"upload_monitor_video save_path={save_path}, files={files}")
        if len(files) > 0:
            if operator.eq("0", project_manager.get_video_compress()):
                for file in files:
                    compress_manager.compress_video(os.path.join(save_path, file))

            # 可能存在压缩失败的情况，压缩失败之后保留原文件并上传到服务器
            files = listdir(save_path)
            if len(files) >= 2:
                for file in files:
                    if not file.__contains__("compress"):
                        # 文件压缩成功后删除原文件，保留压缩视频文件
                        os.remove(os.path.join(save_path, file))
            files = listdir(save_path)
            logger.info(f"upload_monitor_video save_path={save_path}, files={files}")

            # 文件生成后上传到海微文件服务器
            date = datetime.datetime.now().strftime("%Y-%m-%d")
            upload_status = upload_folder_to_cloud(project_number, date, "MonitorVideo", file_date, save_path)
            share_url, share_status = create_cloud_share(project_number, date, "MonitorVideo", file_date)
            status, msg = fs_manager.post_v2_test_records_items_resources(record_item_id, "监控视频", share_url)
            if upload_status and share_status and status:
                # 上传成功之后删除本地文件
                shutil.rmtree(save_path)
                logger.info(f"upload_monitor_video delete {save_path} success")

    def post_test_records(self, case_number, case_name, case_id, case_version, case_start_time, case_end_time, result,
                          steps, case_order, file_date, abnormal_stop):
        mcu_log_reader.delay_stop_time = 0
        soc_log_reader.delay_stop_time = 0
        logcat_reader.delay_stop_time = 0
        # 处理CAN消息日志
        can_msgs = copy.deepcopy(adb_connect_device.can_msgs)
        adb_connect_device.can_msgs.clear()
        # 处理MCU消息日志
        mcu_msgs = copy.deepcopy(adb_connect_device.mcu_msgs)
        adb_connect_device.mcu_msgs.clear()
        # 处理SOC消息日志
        soc_msgs = copy.deepcopy(adb_connect_device.soc_msgs)
        adb_connect_device.soc_msgs.clear()
        # 处理OS消息日志
        os_msgs = copy.deepcopy(adb_connect_device.os_msgs)
        adb_connect_device.os_msgs.clear()
        # 处理VDS APP消息日志
        vds_app_msgs = copy.deepcopy(adb_connect_device.vds_app_msgs)
        adb_connect_device.vds_app_msgs.clear()

        # 测试用例执行完成后上报测试记录
        function_test_result = []
        test_record_id = project_manager.get_test_record_id()

        project_number = project_manager.get_test_plan_project_number()
        project_name = project_manager.get_test_plan_project_name()
        test_plan_name = project_manager.get_test_plan_name()
        test_plan_id = project_manager.get_test_plan_id()
        machine_number = project_manager.get_machine_number()
        if auto_optical_test_manager.test_result is not None:
            function_test_result.append(copy.deepcopy(auto_optical_test_manager.test_result))
        if color_temp_light_sensor_auto.test_result is not None:
            function_test_result.append(copy.deepcopy(color_temp_light_sensor_auto.test_result))
        if env_test_auto.test_result is not None:
            function_test_result.append(copy.deepcopy(env_test_auto.test_result))
        if alg_recorder.test_result is not None:
            function_test_result.append(copy.deepcopy(alg_recorder.test_result))
        if adb_connect_device.test_result is not None:

            function_test_result.append(copy.deepcopy(adb_connect_device.test_result))

        status, record_item_id = fs_manager.post_v2_test_records(test_record_id=test_record_id,
                                                                 order=case_order,
                                                                 project_number=project_number,
                                                                 project_name=project_name,
                                                                 test_plan_name=test_plan_name,
                                                                 test_plan_id=test_plan_id,
                                                                 case_number=case_number,
                                                                 case_name=case_name,
                                                                 case_id=case_id,
                                                                 case_version=case_version,
                                                                 start_time=case_start_time,
                                                                 end_time=case_end_time,
                                                                 machine_number=machine_number,
                                                                 result=result,
                                                                 steps=steps,
                                                                 file_date=file_date,
                                                                 can_msgs=can_msgs,
                                                                 mcu_msgs=mcu_msgs,
                                                                 soc_msgs=soc_msgs,
                                                                 os_msgs=os_msgs,
                                                                 vds_app_msgs=vds_app_msgs,
                                                                 function_test_result=function_test_result)
        # 测试用例记录上传成功后开启线程异步上传测试用例资源文件
        if status:
            # 上传逻辑分析仪日志
            threading.Thread(target=self.upload_logic_data, args=(record_item_id, file_date)).start()
            # 上传CAN日志
            threading.Thread(target=self.upload_can_log, args=(record_item_id, file_date, can_msgs)).start()
            # 上传上位机日志
            threading.Thread(target=self.upload_upper_computer_log, args=(record_item_id, file_date)).start()
            # 上传MCU日志
            threading.Thread(target=self.upload_mcu_log, args=(record_item_id, file_date, mcu_msgs)).start()
            # 上传SOC日志
            threading.Thread(target=self.upload_soc_log, args=(record_item_id, file_date, soc_msgs)).start()
            # 上传OS日志
            threading.Thread(target=self.upload_os_log, args=(record_item_id, file_date, os_msgs)).start()
            # 上传VDS APP日志
            threading.Thread(target=self.upload_vds_app_log, args=(record_item_id, file_date, vds_app_msgs)).start()
            # 上传示波器缓存数据
            if abnormal_stop:
                # 异常停止时根据用例中设置的延时停止时间来执行
                threading.Timer(interval=oscilloscope_manager.get_delay_stop_time(),
                                function=self.upload_oscilloscope_data,
                                args=(record_item_id, file_date)).start()
            else:
                threading.Thread(target=self.upload_oscilloscope_data, args=(record_item_id, file_date)).start()
            # 上传视觉检测NG图片
            threading.Thread(target=self.upload_vision_ng_img, args=(record_item_id, file_date)).start()
            # 上传监控视频
            threading.Thread(target=self.upload_monitor_video, args=(record_item_id, file_date)).start()
            # 上传canoe执行结果
            threading.Thread(target=self.upload_canoe_results, args=(record_item_id, file_date)).start()

            if abnormal_stop:
                project_number = project_manager.get_test_plan_project_number()
                case_pass_number, case_ng_number, decision_number = case_manager.get_case_pass_ng_number()
                execute_number = case_pass_number + case_ng_number + decision_number
                fs_manager.post_test_exception_msg(project_number=project_number,
                                                   record_item_id=record_item_id,
                                                   plan_name=case_manager.current_plan_name,
                                                   tester=project_manager.get_test_user(),
                                                   test_case_total=len(case_manager.cases),
                                                   test_case_exec=execute_number,
                                                   test_case_ng=case_ng_number)

        auto_optical_test_manager.test_result = None
        color_temp_light_sensor_auto.test_result = None
        env_test_auto.test_result = None
        alg_recorder.test_result = None
        oscilloscope_manager.reset_delay_stop_time()

    def post_manual_test_records(self, case_number, result, steps):
        # 手动测试用例执行完成后上报测试记录
        logger.info(f"post_manual_test_records case_number={case_number}, result={result}")
        case_name = case_manager.current_case.get("name", "")
        case_id = case_manager.current_case.get("id", 0)
        case_version = case_manager.current_case.get("version", "")
        case_order = case_manager.case_order
        case_start_time = case_manager.get_case_start_time()
        case_end_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        test_record_id = project_manager.get_test_record_id()
        project_number = project_manager.get_test_plan_project_number()
        project_name = project_manager.get_test_plan_project_name()
        test_plan_name = project_manager.get_test_plan_name()
        test_plan_id = project_manager.get_test_plan_id()
        machine_number = project_manager.get_machine_number()
        start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
        end_time = case_end_time.replace(" ", "_").replace(":", "-").replace(".", "-")
        # 停止测试用例监控视频
        frequency_test.stop_case_monitor_video()
        file_date = f"{case_number}_{start_time}"
        # 保存逻辑分析仪数据
        self.save_logic_data(file_date, case_number, end_time)
        # 处理CAN消息日志
        can_msgs = copy.deepcopy(adb_connect_device.can_msgs)
        adb_connect_device.can_msgs.clear()
        # 处理MCU消息日志
        mcu_msgs = copy.deepcopy(adb_connect_device.mcu_msgs)
        adb_connect_device.mcu_msgs.clear()
        # 处理SOC消息日志
        soc_msgs = copy.deepcopy(adb_connect_device.soc_msgs)
        adb_connect_device.soc_msgs.clear()
        # 处理OS消息日志
        os_msgs = copy.deepcopy(adb_connect_device.os_msgs)
        adb_connect_device.os_msgs.clear()
        # 处理VDS APP消息日志
        vds_app_msgs = copy.deepcopy(adb_connect_device.vds_app_msgs)
        adb_connect_device.vds_app_msgs.clear()

        status, record_item_id = fs_manager.post_v2_test_records(test_record_id=test_record_id,
                                                                 order=case_order,
                                                                 project_number=project_number,
                                                                 project_name=project_name,
                                                                 test_plan_name=test_plan_name,
                                                                 test_plan_id=test_plan_id,
                                                                 case_number=case_number,
                                                                 case_name=case_name,
                                                                 case_id=case_id,
                                                                 case_version=case_version,
                                                                 start_time=case_start_time,
                                                                 end_time=case_end_time,
                                                                 machine_number=machine_number,
                                                                 result=result,
                                                                 steps=steps,
                                                                 file_date=file_date,
                                                                 can_msgs=can_msgs,
                                                                 mcu_msgs=mcu_msgs,
                                                                 soc_msgs=soc_msgs,
                                                                 os_msgs=os_msgs,
                                                                 vds_app_msgs=vds_app_msgs)
        logger.info(f"post_manual_test_records status={status}, record_item_id={record_item_id}")
        # 测试用例记录上传成功后开启线程异步上传测试用例资源文件
        if status:
            # 上传逻辑分析仪日志
            threading.Thread(target=self.upload_logic_data, args=(record_item_id, file_date)).start()
            # 上传CAN日志
            threading.Thread(target=self.upload_can_log, args=(record_item_id, file_date, can_msgs)).start()
            # 上传上位机日志
            threading.Thread(target=self.upload_upper_computer_log, args=(record_item_id, file_date)).start()
            # 上传MCU日志
            threading.Thread(target=self.upload_mcu_log, args=(record_item_id, file_date, mcu_msgs)).start()
            # 上传SOC日志
            threading.Thread(target=self.upload_soc_log, args=(record_item_id, file_date, soc_msgs)).start()
            # 上传OS日志
            threading.Thread(target=self.upload_os_log, args=(record_item_id, file_date, os_msgs)).start()
            # 上传VDS APP日志
            threading.Thread(target=self.upload_vds_app_log, args=(record_item_id, file_date, vds_app_msgs)).start()
            # 上传示波器缓存数据
            threading.Thread(target=self.upload_oscilloscope_data, args=(record_item_id, file_date)).start()
            # 上传视觉检测NG图片
            threading.Thread(target=self.upload_vision_ng_img, args=(record_item_id, file_date)).start()
            # 上传监控视频
            threading.Thread(target=self.upload_monitor_video, args=(record_item_id, file_date)).start()
            # 上传canoe执行结果
            threading.Thread(target=self.upload_canoe_results, args=(record_item_id, file_date)).start()

    def stop_test_plan(self, case_number):
        if mcu_log_reader.delay_stop_time == 0:
            mcu_log_reader.stop()
        if soc_log_reader.delay_stop_time == 0:
            soc_log_reader.stop()
        if logcat_reader.delay_stop_time == 0:
            logcat_reader.stop()
        timer_manager.stop_timer()
        lin_sender.stop_all_periodic_send()
        if adb_connect_device.tsmaster_lin_bus is not None:
            adb_connect_device.tsmaster_lin_bus.stop_all_periodic_send()
        self.set_step_status(False, False)
        case_manager.status = CaseStatus.FINISH
        case_manager.cases_execute_finish("主动停止")
        signals_manager.update_test_status.emit(CaseStatus.FINISH)
        work_current_monitor_manager.interrupt_stop()
        case_name = case_manager.current_case.get("name", "")
        case_id = case_manager.current_case.get("id", 0)
        case_version = case_manager.current_case.get("version", "")
        steps = self.steps
        case_order = case_manager.case_order
        case_start_time = case_manager.get_case_start_time()
        start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
        case_end_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        end_time = case_end_time.replace(" ", "_").replace(":", "-").replace(".", "-")
        # 停止测试用例监控视频
        frequency_test.stop_case_monitor_video()
        file_date = f"{case_number}_{start_time}"
        # 保存逻辑分析仪数据
        self.save_logic_data(file_date, case_number, end_time)

        threading.Thread(target=self.post_test_records, args=(case_number, case_name, case_id, case_version,
                                                              case_start_time, case_end_time, None, steps, case_order,
                                                              file_date, False)).start()

        if mcc_io_client.is_open():
            mcc_io_client.open_color_yellow()
        elif tri_color_light.is_open():
            tri_color_light.open_color_yellow()
        mcu_log_reader.clear_error_mark()
        soc_log_reader.clear_error_mark()
        logcat_reader.clear_error_mark()

    def step_execute_finish(self, case_number, step_command, step_result, step_actual):
        logger.info(f"step_execute_finish case_number={case_number}, step_command={step_command}, "
                    f"step_result={step_result}, step_actual={step_actual}, step_status={self.status}")
        # 当前测试项测试完成后丢弃未执行完的步骤
        if operator.eq(CaseStatus.FINISH.value, case_manager.get_case_status(case_number)) or not self.status:
            return logger.warning("step_execute_finish case status is finish")

        if not operator.eq(step_command, self.get_current_step_command()):
            return logger.warning(f"step_execute_finish step_command: {step_command} is not match "
                                  f"current_step_command: {self.get_current_step_command()}")

        step = self.get_current_step()
        if step is None:
            return logger.warning(f"step_execute_finish current step is None")

        step_end_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        step.update({"end_time": step_end_time})
        step.update({"status": "已执行"})
        step.update({"step_result": step_result})
        step.update({"step_actual": step_actual})
        logger.info("step_execute_finish step_result={}".format(step.get("step_result", "NG")))
        signals_manager.update_step_status.emit(step_command, step_result, step_actual)
        self.update_step_status(step_command, step_result)
        signals_manager.update_step_end_time.emit(step_command, step_end_time)

        self.increase_step_index()

        if self.check_steps_finish():
            case_name = case_manager.current_case.get("name", "")
            case_id = case_manager.current_case.get("id", 0)
            case_version = case_manager.current_case.get("version", "")
            steps = copy.deepcopy(self.steps)
            case_order = case_manager.case_order
            case_start_time = case_manager.get_case_start_time()
            start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
            case_end_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            end_time = case_end_time.replace(" ", "_").replace(":", "-").replace(".", "-")
            case_manager.update_end_time(case_end_time=case_end_time)
            # 停止测试用例监控视频
            frequency_test.stop_case_monitor_video()
            file_date = f"{case_number}_{start_time}"
            # 保存逻辑分析仪数据
            self.save_logic_data(file_date, case_number, end_time)

            # 功能测试用例执行结束
            self.step_index = 0
            case_result = ""
            result = None
            if case_number in self.vision_reverts.keys() and case_number in self.execute_modes.keys():
                vision_revert = self.vision_reverts[case_number]
                execute_mode = self.execute_modes[case_number]
                logger.info(f"step_execute_finish vision_revert={vision_revert}, execute_mode={execute_mode}")
                if execute_mode.__eq__("SEMI_AUTOMATED_EXECUTION"):
                    # 半自动化测试用例通过人工判定测试结果，默认上传空字符测试结果
                    case_result = "待判定"
                else:
                    # 通过测试步骤的执行情况判定测试结果
                    case_result = self.calculate_case_result()

                if operator.eq("PASS", case_result):
                    result = True
                elif operator.eq("NG", case_result):
                    result = False
                elif operator.eq("待判定", case_result):
                    result = None

            if case_manager.abnormal_stop and operator.eq("NG", case_result):
                signals_manager.case_execute_finish.emit(case_number, case_result, True)
                abnormal_stop = True
                self.set_step_status(False, True)
                case_manager.status = CaseStatus.FINISH
                case_manager.cases_execute_finish("异常停止")
                signals_manager.update_test_status.emit(CaseStatus.FINISH)

                # 将三色灯显示为红色
                if mcc_io_client.is_open():
                    mcc_io_client.open_color_red()
                elif tri_color_light.is_open():
                    tri_color_light.open_color_red()
            else:
                signals_manager.case_execute_finish.emit(case_number, case_result, False)
                abnormal_stop = False

            threading.Thread(target=self.post_test_records, args=(case_number, case_name, case_id, case_version,
                                                                  case_start_time, case_end_time, result, steps,
                                                                  case_order, file_date, abnormal_stop)).start()
        else:
            logger.info(f"step_execute_finish abnormal_stop={case_manager.abnormal_stop}, step_result={step_result}")
            if case_manager.abnormal_stop and operator.eq("NG", step_result):
                self.set_step_status(False, True)
                signals_manager.case_execute_finish.emit(case_number, "NG", True)
                case_manager.status = CaseStatus.FINISH
                case_manager.cases_execute_finish("异常停止")
                signals_manager.update_test_status.emit(CaseStatus.FINISH)

                case_name = case_manager.current_case.get("name", "")
                case_id = case_manager.current_case.get("id", 0)
                case_version = case_manager.current_case.get("version", "")
                steps = copy.deepcopy(self.steps)
                case_order = case_manager.case_order
                case_start_time = case_manager.get_case_start_time()
                start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
                case_end_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                end_time = case_end_time.replace(" ", "_").replace(":", "-").replace(".", "-")
                # 停止测试用例监控视频
                frequency_test.stop_case_monitor_video()
                file_date = f"{case_number}_{start_time}"
                # 保存逻辑分析仪数据
                self.save_logic_data(file_date, case_number, end_time)

                delay_time = max(mcu_log_reader.delay_stop_time, soc_log_reader.delay_stop_time, logcat_reader.delay_stop_time)
                logger.info(f"step_execute_finish delay_time={delay_time}")
                threading.Timer(
                    interval=delay_time,  # 延迟执行日志上传
                    function=self.post_test_records, args=(case_number, case_name, case_id, case_version,
                                                           case_start_time, case_end_time, False, steps,
                                                           case_order, file_date, True)).start()

                # 将三色灯显示为红色
                if mcc_io_client.is_open():
                    mcc_io_client.open_color_red()
                elif tri_color_light.is_open():
                    tri_color_light.open_color_red()
            else:
                self.set_step()

    @staticmethod
    def detect_functionality_result(vision_revert, result_dict):
        logger.info(f"detect_functionality_result vision_revert={vision_revert}, result_dict={result_dict}")
        if len(result_dict) == 0:
            return True, 0

        result = True
        err_desc = []
        grainy_screen_detect_value = []
        flicker_screen_detect_value = []
        black_screen_detect_value = []
        for key, value in result_dict.items():
            flicker_screen = value["flicker_screen"]
            grainy_screen = value["grainy_screen"]
            black_screen = value["black_screen"]
            flicker_screen_reason = value.get("flicker_screen_reason", "")
            grainy_screen_reason = value.get("grainy_screen_reason", "")
            black_screen_reason = value.get("black_screen_reason", "")
            grainy_screen_detect_value = value["grainy_screen_detect_value"]
            flicker_screen_detect_value = value["flicker_screen_detect_value"]
            black_screen_detect_value = value["black_screen_detect_value"]
            logger.info(f"detect_functionality_result flicker_screen={flicker_screen}, grainy_screen={grainy_screen}, "
                        f"black_screen={black_screen}, grainy_screen_detect_value={grainy_screen_detect_value}, "
                        f"flicker_screen_detect_value={flicker_screen_detect_value}, "
                        f"black_screen_detect_value={black_screen_detect_value}")
            if vision_revert:
                if black_screen != 1:
                    result = False
                    err_desc.append("黑屏异常")
                if flicker_screen > 0:
                    result = False
                    err_desc.append("闪屏异常")
                if grainy_screen > 0:
                    result = False
                    err_desc.append("花屏异常")
            else:
                if flicker_screen > 0:
                    result = False
                    err_desc.append(f"闪屏异常【{flicker_screen_reason}】")
                if grainy_screen > 0:
                    result = False
                    err_desc.append(f"花屏异常【{grainy_screen_reason}】")
                if black_screen > 0:
                    result = False
                    err_desc.append(f"黑屏异常【{black_screen_reason}】")
        ret_err_desc = " & ".join(err_desc)
        return result, ret_err_desc, grainy_screen_detect_value, flicker_screen_detect_value, black_screen_detect_value

    def dispatch_step(self):
        logger.debug("dispatch_step")
        self._timer = Timer(interval=self.DISPATCH_STEP_INTERVAL, function=self.dispatch_step)
        self._timer.start()
        if self._step_queue.empty():
            return
        step = self._step_queue.get()
        if step is None:
            return

        self.receive_step(step)

    def receive_step(self, step):
        case_number = step.get("case_number", "")
        case_name = step.get("case_name", "")
        execute_mode = step.get("execute_mode", "")
        step_type = step.get("step_type", "")
        command = step.get("command", "")
        params = step.get("params", "")
        data = step.get("data", "")
        expect = step.get("expect", "")
        logger.info(f"receive_step case_number={case_number}, case_name={case_name}, execute_mode={execute_mode}, "
                    f"step_type={step_type}, command={command}, params={params}, data={data}, expect={expect}")

        start_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        signals_manager.update_step_start_time.emit(command, start_time)
        step.update({"start_time": start_time})
        if not execute_mode.__eq__("MANUAL_EXECUTION"):
            signals_manager.update_step_status.emit(command, "执行中", "")
        self.update_step_status(command, "执行中")
        self.execute_step(step)
        self.step_command = command
        self.step_retry_times = 0
        # 测试时长大于10s
        if command not in ["UpdateFwByRandom", "ExecuteBat", "SetDelayTime", "SetRandomDelayTime", "SwitchStepVoltage",
                           "TouchStillTest", "Mate3AfterRotate", "ReadPeriodWorkCurrent", "SwitchStepBrightness",
                           "TouchMarkingTest", "M410Test", "M410Test--GAMMA_CURVE", "M410Test--BRIGHTNESS_CURVE",
                           "M410Test--CONTRAST_RATIO", "M410Test--COLOUR_GAMUT", "M410Test--UNIFORMITY",
                           "LightSensorAutoTest", "EnvTemperatureTest", "ExecuteLoopPowerOnOff", "M410--9PointsColor",
                           "ReadColorCoordinates", "TouchPointsDetect", "M410--9PointsBrightness", "ReadAEQTASK",
                           "CanProtocolAddTestID", "TpcmTest", "RecordImageAlgorithm", "recordCanMsg", "TMfingerClick",
                           "M410--FlickerTest", "M410--ResponseTimeTest", "ExecuteBlockBat", "DetectAngle", "LinSender","M410--SinglePointBrightness"
                           "UpdateFwByStep", "MonitorMultiChannelWorkCurrent", "HXCTA_Test"]:

            if not execute_mode.__eq__("MANUAL_EXECUTION"):
                if self.check_step_timeout_timer is not None:
                    self.check_step_timeout_timer.cancel()
                self.check_step_state(case_number, command, max_retry_times=10)

    def check_step_state(self, case_number, command, max_retry_times=10):
        logger.debug(f"check_step_state case_number={case_number}, command={command}, max_retry_times={max_retry_times}"
                     f", step_command={self.step_command}")

        self.check_step_timeout_timer = threading.Timer(interval=1,
                                                        function=self.check_step_state,
                                                        args=(case_number, command, max_retry_times))
        self.check_step_timeout_timer.start()

        if operator.eq(command, self.step_command):
            self.step_retry_times += 1
            if self.step_index in self.step_status.keys():
                step_status = self.step_status[self.step_index]
                logger.info(f"check_step_state step_status={step_status}, step_command={self.step_command}")
                if operator.eq(self.step_command, step_status["command"]):
                    logger.info(f"check_step_state step_retry_times={self.step_retry_times}")
                    if self.step_retry_times >= max_retry_times:
                        if operator.eq("执行中", step_status["status"]):
                            signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG: 步骤执行超时")

                        self.check_step_timeout_timer.cancel()
                else:
                    self.check_step_timeout_timer.cancel()
        else:
            self.check_step_timeout_timer.cancel()

    def update_step_status(self, command, status):
        if self.step_index in self.step_status.keys():
            step_status = self.step_status[self.step_index]
            if operator.eq(command, step_status["command"]):
                step_status["status"] = status

    def calculate_case_result(self):
        logger.info("calculate_case_result")
        for step in self.steps:
            step_result = step.get("step_result", "NG")
            if operator.eq("NG", step_result):
                return step_result
            elif operator.eq("待判定", step_result):
                return step_result
        return "PASS"

    def get_current_step(self):
        if self.step_index < len(self.steps):
            return self.steps[self.step_index]
        return None

    def get_current_step_command(self):
        step = self.get_current_step()
        if step is None:
            return ""
        else:
            return step.get("command", "")

    def set_step(self):
        logger.info(f"set_step status={self.status}, step_index={self.step_index}")
        try:
            if self.status:
                if self.step_index < len(self.steps):
                    self._step_queue.put(self.steps[self.step_index])
        except Exception as e:
            logger.error(f"set_step exception: {str(e.args)}")

    def start_step_center(self):
        self._timer.start()

    def increase_step_index(self):
        self.step_index += 1
        logger.info(f"increase_step_index step_index={self.step_index}")

    def check_steps_finish(self):
        finish = self.step_index >= len(self.steps)
        logger.debug(f"check_steps_finish step_index={self.step_index}, steps={self.steps}, finish={finish}")
        return finish

    def execute_step(self, step):
        step_type = step.get("step_type", "")
        expect = step.get("expect", "")
        logger.info("execute_step step_type={}, expect={}".format(step_type, expect))
        if operator.eq("CAN", step_type):
            self.execute_can(step)
        elif operator.eq("LIN", step_type):
            self.lin_stop_event.clear()
            self.execute_lin(step)
        elif operator.eq("I2C", step_type):
            self.execute_i2c(step)
        elif operator.eq("CustomizeCMD", step_type):
            self.execute_customize_cmd(step)

    @staticmethod
    def execute_i2c(step):
        def high_low_to_int(high, low):
            """
            将两个字节的高低位转化为整数
            :param high:
            :param low:
            :return:
            """
            return low | (high << 8)

        def parse_result(result, expect):
            value_list = []
            if "XX" in expect:
                expect_list = expect.split(" ")
                result_list = result.split(" ")
                for i in range(len(expect_list)):
                    if expect_list[i] == "XX":
                        value = result_list[i]
                        value_list.append(value)

            return value_list

        def compare_result(step, result):
            expect = step.get("expect")
            option = step.get("option")
            min = step.get("min")
            max = step.get("max")
            logger.info(f"compare_result step is {step}")

            if option == "不匹配":
                signals_manager.step_execute_finish.emit(case_number, "I2C", "PASS", "PASS")
                return True, None
            elif option == "等于":
                match = re.match(expect, result, re.M | re.I)
                if match is None:
                    return False, result
                value_list = re.findall(expect, result, re.M | re.I)
                if len(value_list) == 0:
                    return False, result
                if isinstance(value_list[0], str):
                    value = int(value_list[0], 16)
                elif isinstance(value_list[0], tuple):
                    value = high_low_to_int(int(value_list[0][0], 16), int(value_list[0][1], 16))
                else:
                    return False, json.dumps(value_list)
                if int(value) == int(float(min)):
                    return True, result.lower()
                else:
                    return False, result.lower()
            elif option == "范围":
                match = re.match(expect, result, re.M | re.I)
                if match is None:
                    return False, result
                value_list = re.findall(expect, result, re.M | re.I)
                if len(value_list) == 0:
                    return False, result
                if isinstance(value_list[0], str):
                    value = int(value_list[0], 16)
                elif isinstance(value_list[0], tuple):
                    tmp = list(value_list[0])
                    value = high_low_to_int(int(tmp[0], 16), int(tmp[1], 16))
                else:
                    return False, json.dumps(value_list)
                if float(max) >= value >= float(min):
                    return True, value
                else:
                    return False, value
            elif option == "完全匹配":
                if expect.lower() not in result.lower():
                    return False, result
                else:
                    return True, result
            else:
                logger.error(f"option is not right {step}")

        logger.info("execute_i2c step={}".format(step))
        case_number = step.get("case_number")
        command = step.get("params")  # 不是cmd
        expect = step.get("expect")
        logger.info("execute_i2c command={}, expect={}".format(command, expect))
        try:
            tmp = photics_manager.execute_adb_command(command)
            if isinstance(tmp, list):
                if len(tmp) == 0:
                    result = "执行成功"
                else:
                    result = "".join(tmp).strip()
            else:
                result = tmp
            logger.info("execute_i2c command={}, result={}".format(command, result))
            flag, value = compare_result(step, result)
            if flag:
                signals_manager.step_execute_finish.emit(case_number, "I2C", "PASS", str(result))
            else:
                signals_manager.step_execute_finish.emit(case_number, "I2C", "NG", str(result))
        except Exception as e:
            logger.error("execute_i2c exception: {}".format(traceback.format_exc()))
            signals_manager.step_execute_finish.emit(case_number, "I2C", "NG", str(e.args))

    def switch_can_bus(self, chl):
        if adb_connect_device.can_bus is not None:
            adb_connect_device.can_bus = adb_connect_device.can_bus_list[chl - 1]
            if adb_connect_device.can_bus.name == "zlg":
                if chl == 1:
                    adb_connect_device.can_bus.set_channel(adb_connect_device.can_bus._can_handle)
                else:
                    adb_connect_device.can_bus.set_channel(adb_connect_device.can_bus._can_handle2)
            if adb_connect_device.can_bus.name == "pcan":
                if chl == 1:
                    adb_connect_device.can_bus.set_channel(adb_connect_device.can_bus.pcan_channel)
                else:
                    adb_connect_device.can_bus.set_channel(adb_connect_device.can_bus.pcan_channel2)

    def execute_can(self, step):
        logger.info(f"execute_can step={step}")
        case_number = step.get("case_number")
        command = step.get("command")
        msg_type = step.get("msg_type")
        try:
            if operator.eq("MSG_FUNC_ADDR", msg_type):
                send_id = project_manager.get_func_address_id()
                recv_id = project_manager.get_response_id()
            elif operator.eq("MSG_PHY_ADDR", msg_type):
                send_id = project_manager.get_phy_address_id()
                recv_id = project_manager.get_response_id()
            else:
                send_id = step.get("id")
                recv_id = step.get("recv_id")

            if step.get("id") and step.get("msg"):
                can_dict = {
                    "id": send_id,
                    "msg": step.get("msg"),
                    "can_type": step.get("can_type"),
                    "cycle_period": step.get("cycle_period"),
                    "uds": step.get("uds"),
                    "recv_id": recv_id,
                    "expect": step.get("expect") if isinstance(step.get("expect"), list) else [step.get("expect")],
                    "channel": step.get("channel"),
                }

                if adb_connect_device.can_bus is None:
                    signals_manager.step_execute_finish.emit(case_number, command, "NG",
                                                             "NG: CAN设备未连接，请连接CAN设备后再测试")
                    return
                # 切换can通道
                self.switch_can_bus(int(step.get("channel")))

                if adb_connect_device.can_bus.name == "zlg":
                    msg = self.set_message(can_dict)
                    adb_connect_device.zlg_send_and_read_msg(case_number, command, msg, can_dict)
                elif adb_connect_device.can_bus.name == "pcan":
                    msg = self.set_message(can_dict)
                    adb_connect_device.pcan_send_and_read_msg(case_number, command, msg, can_dict)
                elif adb_connect_device.can_bus.name == "canoe":
                    msg = self.set_message(can_dict)
                    adb_connect_device.canoe_send_and_read_msg(case_number, command, msg, can_dict)
                elif adb_connect_device.can_bus.name == "tsmaster":
                    msg = self.set_message(can_dict)
                    adb_connect_device.tsmaster_send_and_read_msg(case_number, command, msg, can_dict)
        except Exception as e:
            print(f"execute_can exception: {str(e.args)}")
            logger.error("execute_can exception: {}".format(traceback.format_exc()))

    def send_can_msg(self, can_dict, msg):
        try:
            adb_connect_device.can_bus.send(msg)
            if can_dict["period"] == "True":
                Timer(float(can_dict["period_time"]) * 0.001, function=self.send_can_msg, args=(can_dict, msg)).start()
        except Exception as e:
            logger.error("send_can_msg exception: {}".format(str(e.args)))

    @staticmethod
    def dlc2len(dlc):
        if dlc <= 8:
            return dlc
        elif dlc == 9:
            return 12
        elif dlc == 10:
            return 16
        elif dlc == 11:
            return 20
        elif dlc == 12:
            return 24
        elif dlc == 13:
            return 32
        elif dlc == 14:
            return 48
        else:
            return 64

    @staticmethod
    def set_message(can_dict):
        try:
            if adb_connect_device.can_bus.name == "zlg":
                # 周立功的发送在zlgBase.py中，这里修改无效
                data = can_dict["msg"].split(' ')
                msg_len = len(data)
                is_canfd_msg = True if adb_connect_device.can_bus.can_type.lower() == "canfd" else False
                if is_canfd_msg:
                    msg = ZCAN_TransmitFD_Data()
                else:
                    msg = ZCAN_Transmit_Data()
                msg.transmit_type = 0  # 正常发送 1 单次发送 2自发自收
                try:
                    msg.frame.can_id = int(can_dict["id"], 16)
                except Exception as e:
                    print(str(e.args))
                    msg.frame.can_id = 0
                msg.frame.rtr = 0  # ("数据帧", "远程帧")
                msg.frame.eff = 0  # ("标准帧", "扩展帧")

                if not is_canfd_msg:
                    msg.frame.can_dlc = msg_len
                    msg_len = msg.frame.can_dlc
                else:
                    msg.frame.brs = 0  # can canfd 0，"CANFD BRS" 1
                    # msg.frame.len = StepManager.dlc2len(msg_len)
                    msg.frame.len = msg_len
                    # msg_len = msg.frame.len
                    # msg.frame.can_dlc = dlc_dict[str(msg_len)]

                for i in range(msg_len):
                    if i < len(data):
                        try:
                            msg.frame.data[i] = int(data[i], 16)
                        except Exception as e:
                            print(str(e.args))
                            msg.frame.data[i] = 0
                    else:
                        msg.frame.data[i] = 0
                return msg
            elif adb_connect_device.can_bus.name == "canoe":
                if adb_connect_device.can_bus.can_type.lower() == "can":
                    msg = can.Message(
                        arbitration_id=int(can_dict["id"], 16),
                        is_extended_id=False,
                        # dlc=dlc_dict[str(len(can_dict["msg"].strip().split(" ")))],
                        data=[int(i, 16) for i in can_dict["msg"].strip().split(" ")]
                    )
                    return msg
                else:
                    msg = can.Message(
                        arbitration_id=int(can_dict["id"], 16),
                        is_extended_id=False,
                        is_fd=True,
                        # dlc=dlc_dict[str(len(can_dict["msg"].strip().split(" ")))],
                        data=[int(i, 16) for i in can_dict["msg"].strip().split(" ")]
                    )
                    return msg
            elif adb_connect_device.can_bus.name.lower() == "tsmaster":
                msg = can.Message(
                    arbitration_id=int(can_dict["id"], 16),
                    is_extended_id=False,
                    is_fd=adb_connect_device.can_bus.is_fd,
                    # dlc=len(can_dict["msg"].strip().split(" ")),
                    # dlc=dlc_dict[str(len(can_dict["msg"].strip().split(" ")))],
                    channel=adb_connect_device.can_bus.channel,
                    data=[int(i, 16) for i in can_dict["msg"].strip().split(" ")]
                )
                return msg

            else:
                msg = can.Message(
                    arbitration_id=int(can_dict["id"], 16),
                    is_extended_id=False,
                    dlc=len(can_dict["msg"].strip().split(" ")),
                    # dlc=dlc_dict[str(len(can_dict["msg"].strip().split(" ")))],
                    data=[int(i, 16) for i in can_dict["msg"].strip().split(" ")]
                )
                return msg
        except Exception as e:
            print(str(e.args))
            logger.error("set_message exception: {}".format(traceback.format_exc()))

    @staticmethod
    def execute_lin(step):
        case_number = step.get("case_number")
        command = step.get("command")
        try:

            if step.get("id") and step.get("msg"):
                adb_connect_device.plin_send_msg(step)
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        except Exception as e:
            print(str(e.args))
            logger.error("execute_lin exception: {}".format(str(traceback.format_exc())))
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG")

    def execute_customize_cmd(self, step):
        """执行自定义命令 - 优化版本使用命令注册器"""
        from case.command_registry import command_registry

        total_times = step.get("total_times")
        current_times = step.get("current_times")
        case_number = step.get("case_number")
        case_name = step.get("case_name")
        expect = step.get("expect")
        customize_cmd = step.get("CustomizeCMD")
        data = step.get("data")
        command = step.get("command")

        logger.info(f"execute_customize_cmd total_times={total_times}, current_times={current_times}, "
                    f"case_number={case_number}, case_name={case_name}, expect={expect}, "
                    f"customize_cmd={customize_cmd}, data={data}, command={command}")

        # 使用命令注册器处理命令
        try:
            command_registry.execute_command(command, step)
        except Exception as e:
            logger.error(f"execute_customize_cmd exception: {str(traceback.format_exc())}")
            signals_manager.step_execute_finish.emit(case_number, command, "NG", f"命令执行异常: {str(e)}")

        # 保留原有的 if 分支作为兼容性处理，如果命令注册器中没有找到对应的处理器
        if not command_registry.has_command(command):
            self._execute_legacy_command(step)

    def _execute_legacy_command(self, step):
        """执行传统命令处理逻辑（兼容性保留）"""
        total_times = step.get("total_times")
        current_times = step.get("current_times")
        case_number = step.get("case_number")
        case_name = step.get("case_name")
        expect = step.get("expect")
        customize_cmd = step.get("CustomizeCMD")
        data = step.get("data")
        command = step.get("command")

        # 这里保留一些最常用的命令作为示例，其他命令已移动到专门的处理器中
        logger.warning(f"命令 {command} 未在新的命令处理器中找到，使用传统处理方式")

        # 保留一些特殊的命令处理逻辑，这些命令可能需要特殊的上下文或复杂的逻辑
        if command.startswith("Detect") and command.endswith("Error"):
            params = data.split(',')
            error_mark = params[0]
            max_count = int(params[1]) if len(params) > 1 else 1
            delay_time = int(params[2]) if len(params) > 2 else 60

            if operator.eq("DetectMcuError", command):
                mcu_log_reader.append_error_mark(error_mark, max_count, delay_time)
            elif operator.eq("DetectSocError", command):
                soc_log_reader.append_error_mark(error_mark, max_count, delay_time)
            elif operator.eq("DetectOsError", command):
                logcat_reader.append_error_mark(error_mark, max_count, delay_time)
            elif operator.eq("DetectVdsAppError", command):
                logcat_reader.append_error_mark(error_mark, max_count, delay_time)

            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")

        # 保留一些特殊的CAN协议相关命令，因为它们需要特殊的上下文
        elif operator.eq("CanProtocolStartApp", command):
            from adb.util.canoeAuto import get_canoe_app
            cfg_path = data.split(",")[0]
            tse_path = data.split(",")[1]
            canoe_app = get_canoe_app()
            if not canoe_app:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "CanoeApp is None")
                return
            canoe_app.run_test_modules(cfg_path, tse_path, case_number, command, )
            canoe_app.set_SysVar("pythonsys", "run_test", 1)

        elif operator.eq("CanProtocolStopApp", command):
            from adb.util.canoeAuto import canoe_app
            if canoe_app:
                canoe_app.Stop()
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "CanoeApp is None")
            # kill task CANoe64.exe
            os.system("taskkill /f /im CANoe64.exe")

        elif operator.eq("CanProtocolAddTestID", command):
            from adb.util.canoeAuto import get_canoe_app
            canoe_app = get_canoe_app()
            if canoe_app is None:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "CanoeApp is None")
                return

            module_id = data.split(",")[0]
            test_id = data.split(",")[1]
            while canoe_app.is_alive():
                value = canoe_app.get_SysVar("pythonsys", "run_test")
                if int(value) != 1:
                    logging.info(f"value is {value} waiting for canoe value to be set")
                    canoe_app.set_SysVar("pythonsys", "run_test", 1)
                    time.sleep(0.2)
                    continue
                logging.info(f"value is {value}")
                time.sleep(0.5)
                break
            canoe_app.AddTest(module_id, test_id)
            flag = False
            result = ""
            while canoe_app.is_alive():
                value = canoe_app.get_SysVar("pythonsys", "run_test")
                result = canoe_app.GetTestResult()
                logging.info(f"SysVar value is{value}, get CanProtocolAddTestID is {result}")
                if not result:
                    time.sleep(1)
                    continue
                if "NG" in result.upper():
                    flag = False
                else:
                    flag = True
                break
            signals_manager.current_canoe_asc_dir = f"{module_id.upper()}_{test_id}"
            if flag:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", result)
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG")

        elif operator.eq("SecurityLevel", command):
            from adb.util.canTools import can_tool
            project = project_manager.get_test_plan_project_number()
            position = data.split(",")[0]
            level = data.split(",")[1]
            send_id = data.split(",")[2]
            send_msg = data.split(",")[3]
            recv_id = data.split(",")[4]
            recv_msg = data.split(",")[5]
            channel = data.split(",")[6]

            self.switch_can_bus(int(channel))
            # 进入安全等级前置条件
            status, result = can_tool.run_security_level(project, position, level, send_id, send_msg, recv_id, recv_msg,
                                                         channel)
            signals_manager.step_execute_finish.emit(case_number, command, "PASS" if status else "NG", result)

        elif operator.eq("SecurityLevelEnter", command):
            from adb.util.canTools import can_tool
            project = project_manager.get_test_plan_project_number()
            position = data.split(",")[0]
            level = data.split(",")[1]
            send_id = data.split(",")[2]
            send_msg = data.split(",")[3]
            recv_id = data.split(",")[4]
            recv_msg = data.split(",")[5]
            channel = data.split(",")[6]

            self.switch_can_bus(int(channel))
            status, result = can_tool.enter_level(project, position, level, send_id, send_msg, recv_id, recv_msg,
                                                  channel)
            signals_manager.step_execute_finish.emit(case_number, command, "PASS" if status else "NG", result)

        else:
            # 如果命令没有被处理，记录警告
            logger.warning(f"未处理的命令: {command}, 该命令可能需要添加到相应的处理器中")
            signals_manager.step_execute_finish.emit(case_number, command, "NG", f"未支持的命令: {command}")

        # 所有其他命令都已经移动到专门的处理器中了
        # 如果到达这里，说明命令没有被任何处理器处理
        logger.warning(f"命令 {command} 没有被任何处理器处理")
        signals_manager.step_execute_finish.emit(case_number, command, "NG", f"未找到处理器: {command}")

    def switch_vision_collect(self, status: bool):
        """切换视觉采集状态"""
        logger.info(f"switch_vision_collect status={status}")
        visual_detect_signal.start_collect.emit(status)

    def functionality_stop_record(self, file_date: str, now_count: int, all_count: int,
                                 threshold_flicker: float, threshold_grainy: float,
                                 threshold_black: float, test_mode: int):
        """功能性停止录制"""
        logger.info(f"functionality_stop_record file_date={file_date}, now_count={now_count}, all_count={all_count}, "
                   f"threshold_flicker={threshold_flicker}, threshold_grainy={threshold_grainy}, "
                   f"threshold_black={threshold_black}, test_mode={test_mode}")
        frequency_test.start_record_index = False
        frequency_test.all_count = all_count
        threading.Timer(interval=3, function=self.functionality_video_test,
                       args=(frequency_test.record_video_path, file_date, now_count, all_count, threshold_flicker,
                             threshold_grainy, threshold_black, test_mode, camera_config.get_base_path())).start()

    @staticmethod
    def functionality_video_test(file_path: str, file_date: str, now_count: int, all_count: int,
                                threshold_flicker: float, threshold_grainy: float,
                                threshold_black: float, test_mode: int, base_path: str):
        """功能性视频测试"""
        if vision_manager.vision_calibrate_dialog is not None:
            threading.Thread(target=frequency_test.start_functionality_video_test,
                           args=([file_path, ], vision_manager.vision_calibrate_dialog, file_date, now_count,
                                 all_count, threshold_flicker, threshold_grainy, threshold_black, test_mode,
                                 base_path)).start()

    @staticmethod
    def detect_functionality_result(vision_revert: bool, result_dict: dict):
        """检测功能性结果"""
        logger.info(f"detect_functionality_result vision_revert={vision_revert}, result_dict={result_dict}")
        if len(result_dict) == 0:
            return True, 0, [], [], []

        result = True
        err_desc = []
        grainy_screen_detect_value = []
        flicker_screen_detect_value = []
        black_screen_detect_value = []

        for key, value in result_dict.items():
            flicker_screen = value["flicker_screen"]
            grainy_screen = value["grainy_screen"]
            black_screen = value["black_screen"]
            flicker_screen_reason = value.get("flicker_screen_reason", "")
            grainy_screen_reason = value.get("grainy_screen_reason", "")
            black_screen_reason = value.get("black_screen_reason", "")
            grainy_screen_detect_value = value["grainy_screen_detect_value"]
            flicker_screen_detect_value = value["flicker_screen_detect_value"]
            black_screen_detect_value = value["black_screen_detect_value"]

            logger.info(f"detect_functionality_result flicker_screen={flicker_screen}, grainy_screen={grainy_screen}, "
                       f"black_screen={black_screen}, grainy_screen_detect_value={grainy_screen_detect_value}, "
                       f"flicker_screen_detect_value={flicker_screen_detect_value}, "
                       f"black_screen_detect_value={black_screen_detect_value}")

            if vision_revert:
                if black_screen != 1:
                    result = False
                    err_desc.append("黑屏异常")
                if flicker_screen > 0:
                    result = False
                    err_desc.append("闪屏异常")
                if grainy_screen > 0:
                    result = False
                    err_desc.append("花屏异常")
            else:
                if flicker_screen > 0:
                    result = False
                    err_desc.append(f"闪屏异常【{flicker_screen_reason}】")
                if grainy_screen > 0:
                    result = False
                    err_desc.append(f"花屏异常【{grainy_screen_reason}】")
                if black_screen > 0:
                    result = False
                    err_desc.append(f"黑屏异常【{black_screen_reason}】")

        ret_err_desc = " & ".join(err_desc)
        return result, ret_err_desc, grainy_screen_detect_value, flicker_screen_detect_value, black_screen_detect_value

    def set_delay_time(self, case_number: str, command: str, time_size: float):
        """设置延时时间回调"""
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", f"设置延时：{time_size}秒")

    @staticmethod
    def go_to_center():
        """移动到中心点"""
        logger.info("go_to_center")
        touch_card.display_center_point()
        points = []
        for i in range(20):
            time.sleep(3)
            logger.info(f"go_to_center {i}")
            frame = touch_card.get_camera_image()
            points = touch_card.detact_point(frame)
            if len(points) == 0:
                continue
            else:
                break
        logger.info(f"go_to_center detect point is {points}")
        # 计算坐标
        physical_coordinates = pixel_to_physical(points, M=Calibration["color_analyser"])
        print("坐标:", physical_coordinates[0])
        touch_card.move_point(physical_coordinates[0][0], physical_coordinates[0][1], z=23567.0, Z2_value=-59508.0,
                              R_value=-12659.0)


step_manager: StepManager = StepManager()
step_manager.start_step_center()
