import copy
import datetime
import json
import logging
import operator
import os
import random
import re
import shutil
import threading

import psutil
import time
import traceback
from queue import Queue
from threading import Timer

import can

from adb.AdbManager import adb_manager
from common.AppConfig import app_config
from common.LogUtils import logger
from color_temp.ColorTempLightSensorAuto import color_temp_light_sensor_auto
from control_board.inspire_robots.uart_client import inspire_client

from environment.EnvTestAuto import env_test_auto
from power.manager.WorkCurrentMonitorManager import work_current_monitor_manager
from power.tools.ETM3020PCControl import etm_3020pc_control
from power.tools.etm_mu3_control import etm_mu3_control
from power.tools.it_m3200_control import it_m3200_control
from tools.tri_color_light_tool.tri_color_light import tri_color_light
from touch.TouchManager import touch_manager
from utils.DtcDetectManager import dtc_detect_manager
from utils.RecordImgAlgorithm import alg_recorder
from utils.elevation_angle_tool import elevation_angle_tool, execute_comand
from vision.VisualDetectSignal import visual_detect_signal
from utils.LogReader import logcat_reader, mcu_log_reader, soc_log_reader
from photics import photics_manager, PhoticsFunction
from photics.AutoOpticalTestManager import auto_optical_test_manager
from photics.BrightnessManager import brightness_manager
from photics.color_analyzer_tools.manager.ColorAnalyzerManager import color_analyzer_manager
from photics.light_sensor_tools.brightness.BrightnessCalibrateManager import brightness_calibrate_manager
from photics.light_sensor_tools.brightness.BrightnessTestManager import brightness_test_manager
from photics.light_sensor_tools.color_temp.ColorTempCalibrateManager import color_temp_calibrate_manager
from photics.light_sensor_tools.color_temp.ColorTempTestManager import color_temp_test_manager
from adb.AdbConnectDevice import adb_connect_device
from adb.zlgcan.zlgcan import ZCAN_TransmitFD_Data, ZCAN_Transmit_Data
from bat.BatManager import bat_manager
from case.CaseManager import case_manager, CaseStatus
from case.VdsDetectManager import vds_detect_manager
from control_board.calibration import pixel_to_physical, Calibration
from control_board.mcc_io_client import mcc_io_client
from control_board.touchPointTest import touch_card
from tools.endurance.RelayClient import relay_client
from fs_manager.FSManager import fs_manager
from hw_file_system.NextcloudHelper import create_cloud_share, upload_folder_to_cloud, upload_file_to_cloud
from logic_analyzer.LogicManager import logic_manager
from oscilloscope.OscilloscopeManager import oscilloscope_manager
from touch.AutoKeyPress import auto_key_press
from utils import listdir
from utils.CompressManager import compress_manager
from utils.LogManager import log_manager
from utils.Logger import CACHE_PATH
from utils.LoopPowerOnOffManager import loop_power_on_off_manager
from utils.PowerManager import power_manager
from utils.ProjectManager import project_manager
from utils.SignalsManager import signals_manager
from utils.TimerManager import timer_manager
from vision.CameraConfig import camera_config
from vision.CameraManager import camera_manager
from vision.FrequencyTest import frequency_test
from vision.VisionManager import vision_manager
from view.LinDebugWidget import lin_sender


class StepManager:
    DISPATCH_STEP_INTERVAL = 0.05

    def __init__(self):
        super().__init__()
        self.status = True
        self._receivers = []
        self._timer = Timer(0, self.dispatch_step)
        self._step_queue = Queue()
        self.steps = []
        self.step_index = 0
        self.step_status = {}
        self.vision_reverts = {}
        self.vision_algorithms = {}
        self.execute_modes = {}
        self.step_command = ""
        self.step_retry_times = 0
        self.lin_msg_count = 0
        self.lin_stop_event = threading.Event()
        self.check_step_timeout_timer = None
        signals_manager.step_execute_finish.connect(self.step_execute_finish)
        signals_manager.reply_adb_forward_str_msg.connect(self.update_adb_forward_str_msg)
        signals_manager.export_logic_analyzer_data.connect(self.export_logic_analyzer_data)

    def export_logic_analyzer_data(self):
        case_start_time = case_manager.get_case_start_time()
        logger.info(f"export_logic_analyzer_data case_start_time={case_start_time}")
        if case_start_time is not None:
            case_start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
            file_date = f"{case_manager.current_case_number}_{case_start_time}"
            save_time = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S-%f")[:-3]
            logic_save_path = self.save_logic_data(file_date, case_manager.current_case_number, save_time)
            logger.info(f"export_logic_analyzer_data file_date={file_date}, logic_save_path={logic_save_path}")
        else:
            logger.warning("export_logic_analyzer_data fail, case_start_time is None")

    def stop_lin_cycle_msg(self):
        self.lin_stop_event.set()

    def reset_params(self):
        logger.info("reset_params")
        self.step_index = 0
        self.step_command = ""
        self.step_status.clear()
        self.vision_reverts.clear()
        self.vision_algorithms.clear()
        self.execute_modes.clear()

    @staticmethod
    def update_adb_forward_str_msg(action, value):
        if not operator.eq("HeartBreak", action):
            logger.info(f"update_adb_forward_str_msg action={action}, value={value}")

        vds_detect_manager.detect_action(action=action, info=value)

    def upload_can_log(self, record_item_id, file_date, can_msgs):
        project_number = project_manager.get_test_plan_project_number()
        logger.info(f"upload_can_log can_msgs_size={len(can_msgs)}")
        if file_date is None:
            return
        if len(can_msgs) > 0:
            self.save_log_file(project_number, record_item_id, file_date, can_msgs, "Can")

    @staticmethod
    def upload_upper_computer_log(record_item_id, file_date):
        logger.info(f"upload_upper_computer_log record_item_id={record_item_id}, file_date={file_date}")
        if file_date is None:
            return
        from common.LogUtils import CACHE_PATH
        project_number = project_manager.get_test_plan_project_number()
        upper_computer_log_path = os.path.join(CACHE_PATH, "UpperComputer")
        # 文件生成后上传到海微文件服务器
        date = datetime.datetime.now().strftime("%Y-%m-%d")
        for resource_name in listdir(upper_computer_log_path):
            suffix = resource_name.split(".", 1)[1]
            logger.info(f"upload_upper_computer_log suffix={suffix}")
            if suffix in ["log", "log.1"]:
                upload_file_to_cloud(project_number, date, "UpperComputerLog", file_date, resource_name,
                                     os.path.join(upper_computer_log_path, resource_name))
        share_url, status = create_cloud_share(project_number, date, "UpperComputerLog", file_date)
        fs_manager.post_v2_test_records_items_resources(record_item_id, "上位机日志", share_url)

    def upload_mcu_log(self, record_item_id, file_date, mcu_msgs):
        project_number = project_manager.get_test_plan_project_number()
        logger.info(f"upload_mcu_log mcu_msgs_size={len(mcu_msgs)}")
        if file_date is None:
            return
        if len(mcu_msgs) > 0:
            self.save_log_file(project_number, record_item_id, file_date, mcu_msgs, "Mcu")

    def upload_soc_log(self, record_item_id, file_date, soc_msgs):
        project_number = project_manager.get_test_plan_project_number()
        logger.info(f"upload_mcu_log soc_msgs_size={len(soc_msgs)}")
        if file_date is None:
            return
        if len(soc_msgs) > 0:
            self.save_log_file(project_number, record_item_id, file_date, soc_msgs, "Soc")

    def upload_os_log(self, record_item_id, file_date, os_msgs):
        project_number = project_manager.get_test_plan_project_number()
        logger.info(f"upload_os_log os_msgs_size={len(os_msgs)}")
        if file_date is None:
            return
        if len(os_msgs) > 0:
            self.save_log_file(project_number, record_item_id, file_date, os_msgs, "Os")

    def upload_vds_app_log(self, record_item_id, file_date, vds_app_msgs):
        project_number = project_manager.get_test_plan_project_number()
        logger.info(f"upload_vds_app_log vds_app_msgs_size={len(vds_app_msgs)}")
        if file_date is None:
            return
        if len(vds_app_msgs) > 0:
            self.save_log_file(project_number, record_item_id, file_date, vds_app_msgs, "VdsApp")

    @staticmethod
    def save_log_file(project_number, record_item_id, file_date, msgs, log_mark=""):
        os_path = os.path.join(CACHE_PATH, log_mark)
        if not os.path.exists(os_path):
            os.makedirs(os_path)
        case_path = os.path.join(os_path, file_date)
        if not os.path.exists(case_path):
            os.makedirs(case_path)
        logger_file = os.path.join(case_path, f'{log_mark}.log')
        logger.info(f"save_log_file logger_file={logger_file}")
        with open(logger_file, "w") as file:
            file.writelines(msgs)

        os_log_path = os.path.join(CACHE_PATH, log_mark, file_date)
        # 文件生成后上传到海微文件服务器
        date = datetime.datetime.now().strftime("%Y-%m-%d")
        if operator.eq("示波器", log_mark):
            log_name = f"OscilloscopeLog"
        else:
            log_name = f"{log_mark}Log"
        upload_folder_to_cloud(project_number, date, log_name, file_date, os_log_path)
        share_url, status = create_cloud_share(project_number, date, f"{log_mark}Log", file_date)
        fs_manager.post_v2_test_records_items_resources(record_item_id, f"{log_mark}日志", share_url)

    def upload_oscilloscope_data(self, record_item_id, file_date):
        logger.info(f"upload_oscilloscope_data record_item_id={record_item_id}, file_date={file_date}")
        if file_date is None:
            return
        project_number = project_manager.get_test_plan_project_number()
        if oscilloscope_manager.is_open():
            oscilloscope_manager.stop_oscilloscope()
            time.sleep(0.5)
            # 等待0.5秒后导出通道1~4的缓存数据
            oscilloscope_manager.set_wav_params(channel=1)
            data_1 = oscilloscope_manager.output_wav_data()

            oscilloscope_manager.set_wav_params(channel=2)
            data_2 = oscilloscope_manager.output_wav_data()

            oscilloscope_manager.set_wav_params(channel=3)
            data_3 = oscilloscope_manager.output_wav_data()

            oscilloscope_manager.set_wav_params(channel=4)
            data_4 = oscilloscope_manager.output_wav_data()

            data = []
            data.extend(data_1)
            data.append("\n")
            data.extend(data_2)
            data.append("\n")
            data.extend(data_3)
            data.append("\n")
            data.extend(data_4)
            logger.info(f"upload_oscilloscope_data data={data}")
            if len(data_1) == 0 and len(data_2) == 0 and len(data_3) == 0 and len(data_4) == 0:
                logger.warning("upload_oscilloscope_data fail, data is empty")
                return
            self.save_log_file(project_number, record_item_id, file_date, str(data), "示波器")

    @staticmethod
    def upload_logic_data(record_item_id, file_date, times=10):
        logger.info(f"upload_logic_data record_item_id={record_item_id}, file_date={file_date}")
        if file_date is None:
            return
        project_number = project_manager.get_test_plan_project_number()
        logic_analyzer_path = os.path.join(CACHE_PATH, "LogicAnalyzer")
        if not os.path.exists(logic_analyzer_path):
            os.mkdir(logic_analyzer_path)
        save_path = os.path.join(logic_analyzer_path, file_date)
        if not os.path.exists(save_path):
            os.mkdir(save_path)
        files = []
        for i in range(times):
            files = os.listdir(save_path)
            if len(files) > 0:
                break
            time.sleep(0.5)
        logger.info(f"upload_logic_data files={files}")
        if len(files) > 0:
            # 文件上传到海微文件服务器
            date = datetime.datetime.now().strftime("%Y-%m-%d")
            upload_folder_to_cloud(project_number, date, "LogicAnalyzer", file_date, save_path)
            share_url, status = create_cloud_share(project_number, date, "LogicAnalyzer", file_date)
            fs_manager.post_v2_test_records_items_resources(record_item_id, "逻辑分析仪数据", share_url)

    @staticmethod
    def save_logic_data(file_date, case_number, save_time, channel="0 1 2 3 4"):
        logic_manager.stop()
        # 停止后等待0.5秒再导出数据
        time.sleep(0.5)
        logic_analyzer_path = os.path.join(CACHE_PATH, "LogicAnalyzer")
        if not os.path.exists(logic_analyzer_path):
            os.mkdir(logic_analyzer_path)
        save_path = os.path.join(logic_analyzer_path, file_date)
        if not os.path.exists(save_path):
            os.mkdir(save_path)
        file_name = os.path.join(save_path, f"{case_number}_{save_time}.kvdat --chn-select {channel}")
        logic_manager.send_message(f"export-data {file_name}")
        # TEMP_CSV_FILE_PATH = r'tmp.csv'
        # temp_dir = os.path.dirname(TEMP_CSV_FILE_PATH)
        # if temp_dir and not os.path.exists(temp_dir):
        #      try:
        #          os.makedirs(temp_dir)
        #          print(f"创建目录: {temp_dir}")
        #      except OSError as e:
        #          print(f"错误: 无法创建临时文件目录 {temp_dir}: {e}")
        #          return

        # # 确保文件名包含在引号内，以处理路径中的空格
        # logic_manager.send_message(f'export-data {TEMP_CSV_FILE_PATH} --chn-select {channel}')
        # if not logic_manager.parse_csv_to_json(TEMP_CSV_FILE_PATH):
        #     logger.info("logic_manager csv failed to json")
        return save_path

    @staticmethod
    def upload_vision_ng_img(record_item_id, file_date):
        logger.info(f"upload_vision_ng_img record_item_id={record_item_id}, file_date={file_date}")
        if file_date is None:
            return
        project_number = project_manager.get_test_plan_project_number()
        save_ng_flicker_img_path = os.path.join(camera_config.get_ng_flicker_img_path(), file_date)
        save_ng_grainy_img_path = os.path.join(camera_config.get_ng_grainy_img_path(), file_date)
        save_ng_black_img_path = os.path.join(camera_config.get_ng_black_img_path(), file_date)

        logger.info(f"upload_vision_ng_img save_ng_flicker_img_path={save_ng_flicker_img_path}")
        ng_flicker_files = []
        if os.path.exists(save_ng_flicker_img_path):
            ng_flicker_files = listdir(save_ng_flicker_img_path)
        logger.info(f"upload_vision_ng_img ng_flicker_files={ng_flicker_files}")
        if len(ng_flicker_files) > 0:
            # 文件生成后上传到海微文件服务器
            date = datetime.datetime.now().strftime("%Y-%m-%d")
            upload_folder_to_cloud(project_number, date, "VisionNgImg/NgFlickerImg", file_date,
                                   save_ng_flicker_img_path)
            share_url, upload_status = create_cloud_share(project_number, date, "VisionNgImg/NgFlickerImg", file_date)
            status, msg = fs_manager.post_v2_test_records_items_resources(record_item_id, "视觉检测闪屏NG图片",
                                                                          share_url)
            if upload_status and status:
                # 上传成功之后删除本地文件
                shutil.rmtree(save_ng_flicker_img_path)
                logger.info(f"upload_vision_ng_img delete {save_ng_flicker_img_path} success")

        logger.info(f"upload_vision_ng_img save_ng_grainy_img_path={save_ng_grainy_img_path}")
        ng_grainy_files = []
        if os.path.exists(save_ng_grainy_img_path):
            ng_grainy_files = listdir(save_ng_grainy_img_path)
        logger.info(f"upload_vision_ng_img ng_grainy_files={ng_grainy_files}")
        if len(ng_grainy_files) > 0:
            # 文件生成后上传到海微文件服务器
            date = datetime.datetime.now().strftime("%Y-%m-%d")
            upload_folder_to_cloud(project_number, date, "VisionNgImg/NgGrainyImg", file_date,
                                   save_ng_grainy_img_path)
            share_url, upload_status = create_cloud_share(project_number, date, "VisionNgImg/NgGrainyImg", file_date)
            status, msg = fs_manager.post_v2_test_records_items_resources(record_item_id, "视觉检测花屏NG图片",
                                                                          share_url)
            if upload_status and status:
                # 上传成功之后删除本地文件
                shutil.rmtree(save_ng_grainy_img_path)
                logger.info(f"upload_vision_ng_img delete {save_ng_grainy_img_path} success")

        logger.info(f"upload_vision_ng_img save_ng_black_img_path={save_ng_black_img_path}")
        ng_black_screen_files = []
        if os.path.exists(save_ng_black_img_path):
            ng_black_screen_files = listdir(save_ng_black_img_path)
        logger.info(f"upload_vision_ng_img ng_black_screen_files={ng_black_screen_files}")
        if len(ng_black_screen_files) > 0:
            # 文件生成后上传到海微文件服务器
            date = datetime.datetime.now().strftime("%Y-%m-%d")
            upload_folder_to_cloud(project_number, date, "VisionNgImg/NgBlackScreenImg", file_date,
                                   save_ng_black_img_path)
            share_url, upload_status = create_cloud_share(project_number, date, "VisionNgImg/NgBlackScreenImg",
                                                          file_date)
            status, msg = fs_manager.post_v2_test_records_items_resources(record_item_id, "视觉检测黑屏NG图片",
                                                                          share_url)
            if upload_status and status:
                # 上传成功之后删除本地文件
                shutil.rmtree(save_ng_grainy_img_path)
                logger.info(f"upload_vision_ng_img delete {save_ng_grainy_img_path} success")

    @staticmethod
    def upload_canoe_results(record_item_id, file_date):
        logger.info(f"upload_canoe_results record_item_id={record_item_id}, file_date={file_date}")
        if file_date is None:
            return
        project_number = project_manager.get_test_plan_project_number()
        # 路径
        try:
            if project_number not in ["HW-T-0019", "HW-T-0020"]:
                return
            project_extra = fs_manager.get_project_extra_info(project_number)
            logging.info(f"get_project_extra_info from {project_number}={project_extra}")
            cfg_path = project_extra["data"]["configs"]["canoe_cfg_path"]
            logging.info(f"cfg_path is {cfg_path}")
        except Exception:
            logging.info(f"cfg_path is None!{traceback.format_exc()}")
            cfg_path = None
        if not cfg_path:
            return
        # cfg_path 的父路径
        dir_p = os.path.dirname(cfg_path)
        canoe_tse_path = os.path.join(dir_p, f"log/{signals_manager.current_canoe_asc_dir}")
        logging.info(f"canoe_tse_path is {canoe_tse_path}")

        if not os.path.exists(canoe_tse_path):
            logging.info(f"no exists : {canoe_tse_path}")
            return

        time.sleep(1)
        files = listdir(canoe_tse_path)

        if len(files) > 0:
            files = listdir(canoe_tse_path)
            logger.info(f"upload_canoe_results save_path={canoe_tse_path}, files={files}")

            # 文件生成后上传到海微文件服务器
            date = datetime.datetime.now().strftime("%Y-%m-%d")
            upload_folder_to_cloud(project_number, date, "canoeAsc", file_date, canoe_tse_path)
            share_url, share_status = create_cloud_share(project_number, date, "canoeAsc", file_date)
            fs_manager.post_v2_test_records_items_resources(record_item_id, "canoe测试日志", share_url)

    @staticmethod
    def upload_monitor_video(record_item_id, file_date):
        logger.info(f"upload_monitor_video record_item_id={record_item_id}, file_date={file_date}")
        if file_date is None:
            return
        project_number = project_manager.get_test_plan_project_number()
        monitor_video_path = os.path.join(CACHE_PATH, "MonitorVideo")
        if not os.path.exists(monitor_video_path):
            os.mkdir(monitor_video_path)
        save_path = os.path.join(monitor_video_path, file_date)
        if not os.path.exists(save_path):
            os.mkdir(save_path)
        files = listdir(save_path)
        # logger.info(f"upload_monitor_video save_path={save_path}, files={files}")
        if len(files) > 0:
            if operator.eq("0", project_manager.get_video_compress()):
                for file in files:
                    compress_manager.compress_video(os.path.join(save_path, file))

            # 可能存在压缩失败的情况，压缩失败之后保留原文件并上传到服务器
            files = listdir(save_path)
            if len(files) >= 2:
                for file in files:
                    if not file.__contains__("compress"):
                        # 文件压缩成功后删除原文件，保留压缩视频文件
                        os.remove(os.path.join(save_path, file))
            files = listdir(save_path)
            logger.info(f"upload_monitor_video save_path={save_path}, files={files}")

            # 文件生成后上传到海微文件服务器
            date = datetime.datetime.now().strftime("%Y-%m-%d")
            upload_status = upload_folder_to_cloud(project_number, date, "MonitorVideo", file_date, save_path)
            share_url, share_status = create_cloud_share(project_number, date, "MonitorVideo", file_date)
            status, msg = fs_manager.post_v2_test_records_items_resources(record_item_id, "监控视频", share_url)
            if upload_status and share_status and status:
                # 上传成功之后删除本地文件
                shutil.rmtree(save_path)
                logger.info(f"upload_monitor_video delete {save_path} success")

    def post_test_records(self, case_number, case_name, case_id, case_version, case_start_time, case_end_time, result,
                          steps, case_order, file_date, abnormal_stop):
        mcu_log_reader.delay_stop_time = 0
        soc_log_reader.delay_stop_time = 0
        logcat_reader.delay_stop_time = 0
        # 处理CAN消息日志
        can_msgs = copy.deepcopy(adb_connect_device.can_msgs)
        adb_connect_device.can_msgs.clear()
        # 处理MCU消息日志
        mcu_msgs = copy.deepcopy(adb_connect_device.mcu_msgs)
        adb_connect_device.mcu_msgs.clear()
        # 处理SOC消息日志
        soc_msgs = copy.deepcopy(adb_connect_device.soc_msgs)
        adb_connect_device.soc_msgs.clear()
        # 处理OS消息日志
        os_msgs = copy.deepcopy(adb_connect_device.os_msgs)
        adb_connect_device.os_msgs.clear()
        # 处理VDS APP消息日志
        vds_app_msgs = copy.deepcopy(adb_connect_device.vds_app_msgs)
        adb_connect_device.vds_app_msgs.clear()

        # 测试用例执行完成后上报测试记录
        function_test_result = []
        test_record_id = project_manager.get_test_record_id()

        project_number = project_manager.get_test_plan_project_number()
        project_name = project_manager.get_test_plan_project_name()
        test_plan_name = project_manager.get_test_plan_name()
        test_plan_id = project_manager.get_test_plan_id()
        machine_number = project_manager.get_machine_number()
        if auto_optical_test_manager.test_result is not None:
            function_test_result.append(copy.deepcopy(auto_optical_test_manager.test_result))
        if color_temp_light_sensor_auto.test_result is not None:
            function_test_result.append(copy.deepcopy(color_temp_light_sensor_auto.test_result))
        if env_test_auto.test_result is not None:
            function_test_result.append(copy.deepcopy(env_test_auto.test_result))
        if alg_recorder.test_result is not None:
            function_test_result.append(copy.deepcopy(alg_recorder.test_result))
        if adb_connect_device.test_result is not None:

            function_test_result.append(copy.deepcopy(adb_connect_device.test_result))

        status, record_item_id = fs_manager.post_v2_test_records(test_record_id=test_record_id,
                                                                 order=case_order,
                                                                 project_number=project_number,
                                                                 project_name=project_name,
                                                                 test_plan_name=test_plan_name,
                                                                 test_plan_id=test_plan_id,
                                                                 case_number=case_number,
                                                                 case_name=case_name,
                                                                 case_id=case_id,
                                                                 case_version=case_version,
                                                                 start_time=case_start_time,
                                                                 end_time=case_end_time,
                                                                 machine_number=machine_number,
                                                                 result=result,
                                                                 steps=steps,
                                                                 file_date=file_date,
                                                                 can_msgs=can_msgs,
                                                                 mcu_msgs=mcu_msgs,
                                                                 soc_msgs=soc_msgs,
                                                                 os_msgs=os_msgs,
                                                                 vds_app_msgs=vds_app_msgs,
                                                                 function_test_result=function_test_result)
        # 测试用例记录上传成功后开启线程异步上传测试用例资源文件
        if status:
            # 上传逻辑分析仪日志
            threading.Thread(target=self.upload_logic_data, args=(record_item_id, file_date)).start()
            # 上传CAN日志
            threading.Thread(target=self.upload_can_log, args=(record_item_id, file_date, can_msgs)).start()
            # 上传上位机日志
            threading.Thread(target=self.upload_upper_computer_log, args=(record_item_id, file_date)).start()
            # 上传MCU日志
            threading.Thread(target=self.upload_mcu_log, args=(record_item_id, file_date, mcu_msgs)).start()
            # 上传SOC日志
            threading.Thread(target=self.upload_soc_log, args=(record_item_id, file_date, soc_msgs)).start()
            # 上传OS日志
            threading.Thread(target=self.upload_os_log, args=(record_item_id, file_date, os_msgs)).start()
            # 上传VDS APP日志
            threading.Thread(target=self.upload_vds_app_log, args=(record_item_id, file_date, vds_app_msgs)).start()
            # 上传示波器缓存数据
            if abnormal_stop:
                # 异常停止时根据用例中设置的延时停止时间来执行
                threading.Timer(interval=oscilloscope_manager.get_delay_stop_time(),
                                function=self.upload_oscilloscope_data,
                                args=(record_item_id, file_date)).start()
            else:
                threading.Thread(target=self.upload_oscilloscope_data, args=(record_item_id, file_date)).start()
            # 上传视觉检测NG图片
            threading.Thread(target=self.upload_vision_ng_img, args=(record_item_id, file_date)).start()
            # 上传监控视频
            threading.Thread(target=self.upload_monitor_video, args=(record_item_id, file_date)).start()
            # 上传canoe执行结果
            threading.Thread(target=self.upload_canoe_results, args=(record_item_id, file_date)).start()

            if abnormal_stop:
                project_number = project_manager.get_test_plan_project_number()
                case_pass_number, case_ng_number, decision_number = case_manager.get_case_pass_ng_number()
                execute_number = case_pass_number + case_ng_number + decision_number
                fs_manager.post_test_exception_msg(project_number=project_number,
                                                   record_item_id=record_item_id,
                                                   plan_name=case_manager.current_plan_name,
                                                   tester=project_manager.get_test_user(),
                                                   test_case_total=len(case_manager.cases),
                                                   test_case_exec=execute_number,
                                                   test_case_ng=case_ng_number)

        auto_optical_test_manager.test_result = None
        color_temp_light_sensor_auto.test_result = None
        env_test_auto.test_result = None
        alg_recorder.test_result = None
        oscilloscope_manager.reset_delay_stop_time()

    def post_manual_test_records(self, case_number, result, steps):
        # 手动测试用例执行完成后上报测试记录
        logger.info(f"post_manual_test_records case_number={case_number}, result={result}")
        case_name = case_manager.current_case.get("name", "")
        case_id = case_manager.current_case.get("id", 0)
        case_version = case_manager.current_case.get("version", "")
        case_order = case_manager.case_order
        case_start_time = case_manager.get_case_start_time()
        case_end_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        test_record_id = project_manager.get_test_record_id()
        project_number = project_manager.get_test_plan_project_number()
        project_name = project_manager.get_test_plan_project_name()
        test_plan_name = project_manager.get_test_plan_name()
        test_plan_id = project_manager.get_test_plan_id()
        machine_number = project_manager.get_machine_number()
        start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
        end_time = case_end_time.replace(" ", "_").replace(":", "-").replace(".", "-")
        # 停止测试用例监控视频
        frequency_test.stop_case_monitor_video()
        file_date = f"{case_number}_{start_time}"
        # 保存逻辑分析仪数据
        self.save_logic_data(file_date, case_number, end_time)
        # 处理CAN消息日志
        can_msgs = copy.deepcopy(adb_connect_device.can_msgs)
        adb_connect_device.can_msgs.clear()
        # 处理MCU消息日志
        mcu_msgs = copy.deepcopy(adb_connect_device.mcu_msgs)
        adb_connect_device.mcu_msgs.clear()
        # 处理SOC消息日志
        soc_msgs = copy.deepcopy(adb_connect_device.soc_msgs)
        adb_connect_device.soc_msgs.clear()
        # 处理OS消息日志
        os_msgs = copy.deepcopy(adb_connect_device.os_msgs)
        adb_connect_device.os_msgs.clear()
        # 处理VDS APP消息日志
        vds_app_msgs = copy.deepcopy(adb_connect_device.vds_app_msgs)
        adb_connect_device.vds_app_msgs.clear()

        status, record_item_id = fs_manager.post_v2_test_records(test_record_id=test_record_id,
                                                                 order=case_order,
                                                                 project_number=project_number,
                                                                 project_name=project_name,
                                                                 test_plan_name=test_plan_name,
                                                                 test_plan_id=test_plan_id,
                                                                 case_number=case_number,
                                                                 case_name=case_name,
                                                                 case_id=case_id,
                                                                 case_version=case_version,
                                                                 start_time=case_start_time,
                                                                 end_time=case_end_time,
                                                                 machine_number=machine_number,
                                                                 result=result,
                                                                 steps=steps,
                                                                 file_date=file_date,
                                                                 can_msgs=can_msgs,
                                                                 mcu_msgs=mcu_msgs,
                                                                 soc_msgs=soc_msgs,
                                                                 os_msgs=os_msgs,
                                                                 vds_app_msgs=vds_app_msgs)
        logger.info(f"post_manual_test_records status={status}, record_item_id={record_item_id}")
        # 测试用例记录上传成功后开启线程异步上传测试用例资源文件
        if status:
            # 上传逻辑分析仪日志
            threading.Thread(target=self.upload_logic_data, args=(record_item_id, file_date)).start()
            # 上传CAN日志
            threading.Thread(target=self.upload_can_log, args=(record_item_id, file_date, can_msgs)).start()
            # 上传上位机日志
            threading.Thread(target=self.upload_upper_computer_log, args=(record_item_id, file_date)).start()
            # 上传MCU日志
            threading.Thread(target=self.upload_mcu_log, args=(record_item_id, file_date, mcu_msgs)).start()
            # 上传SOC日志
            threading.Thread(target=self.upload_soc_log, args=(record_item_id, file_date, soc_msgs)).start()
            # 上传OS日志
            threading.Thread(target=self.upload_os_log, args=(record_item_id, file_date, os_msgs)).start()
            # 上传VDS APP日志
            threading.Thread(target=self.upload_vds_app_log, args=(record_item_id, file_date, vds_app_msgs)).start()
            # 上传示波器缓存数据
            threading.Thread(target=self.upload_oscilloscope_data, args=(record_item_id, file_date)).start()
            # 上传视觉检测NG图片
            threading.Thread(target=self.upload_vision_ng_img, args=(record_item_id, file_date)).start()
            # 上传监控视频
            threading.Thread(target=self.upload_monitor_video, args=(record_item_id, file_date)).start()
            # 上传canoe执行结果
            threading.Thread(target=self.upload_canoe_results, args=(record_item_id, file_date)).start()

    def stop_test_plan(self, case_number):
        if mcu_log_reader.delay_stop_time == 0:
            mcu_log_reader.stop()
        if soc_log_reader.delay_stop_time == 0:
            soc_log_reader.stop()
        if logcat_reader.delay_stop_time == 0:
            logcat_reader.stop()
        timer_manager.stop_timer()
        lin_sender.stop_all_periodic_send()
        if adb_connect_device.tsmaster_lin_bus is not None:
            adb_connect_device.tsmaster_lin_bus.stop_all_periodic_send()
        self.set_step_status(False, False)
        case_manager.status = CaseStatus.FINISH
        case_manager.cases_execute_finish("主动停止")
        signals_manager.update_test_status.emit(CaseStatus.FINISH)
        work_current_monitor_manager.interrupt_stop()
        case_name = case_manager.current_case.get("name", "")
        case_id = case_manager.current_case.get("id", 0)
        case_version = case_manager.current_case.get("version", "")
        steps = self.steps
        case_order = case_manager.case_order
        case_start_time = case_manager.get_case_start_time()
        start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
        case_end_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        end_time = case_end_time.replace(" ", "_").replace(":", "-").replace(".", "-")
        # 停止测试用例监控视频
        frequency_test.stop_case_monitor_video()
        file_date = f"{case_number}_{start_time}"
        # 保存逻辑分析仪数据
        self.save_logic_data(file_date, case_number, end_time)

        threading.Thread(target=self.post_test_records, args=(case_number, case_name, case_id, case_version,
                                                              case_start_time, case_end_time, None, steps, case_order,
                                                              file_date, False)).start()

        if mcc_io_client.is_open():
            mcc_io_client.open_color_yellow()
        elif tri_color_light.is_open():
            tri_color_light.open_color_yellow()
        mcu_log_reader.clear_error_mark()
        soc_log_reader.clear_error_mark()
        logcat_reader.clear_error_mark()

    def step_execute_finish(self, case_number, step_command, step_result, step_actual):
        logger.info(f"step_execute_finish case_number={case_number}, step_command={step_command}, "
                    f"step_result={step_result}, step_actual={step_actual}, step_status={self.status}")
        # 当前测试项测试完成后丢弃未执行完的步骤
        if operator.eq(CaseStatus.FINISH.value, case_manager.get_case_status(case_number)) or not self.status:
            return logger.warning("step_execute_finish case status is finish")

        if not operator.eq(step_command, self.get_current_step_command()):
            return logger.warning(f"step_execute_finish step_command: {step_command} is not match "
                                  f"current_step_command: {self.get_current_step_command()}")

        step = self.get_current_step()
        if step is None:
            return logger.warning(f"step_execute_finish current step is None")

        step_end_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        step.update({"end_time": step_end_time})
        step.update({"status": "已执行"})
        step.update({"step_result": step_result})
        step.update({"step_actual": step_actual})
        logger.info("step_execute_finish step_result={}".format(step.get("step_result", "NG")))
        signals_manager.update_step_status.emit(step_command, step_result, step_actual)
        self.update_step_status(step_command, step_result)
        signals_manager.update_step_end_time.emit(step_command, step_end_time)

        self.increase_step_index()

        if self.check_steps_finish():
            case_name = case_manager.current_case.get("name", "")
            case_id = case_manager.current_case.get("id", 0)
            case_version = case_manager.current_case.get("version", "")
            steps = copy.deepcopy(self.steps)
            case_order = case_manager.case_order
            case_start_time = case_manager.get_case_start_time()
            start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
            case_end_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            end_time = case_end_time.replace(" ", "_").replace(":", "-").replace(".", "-")
            case_manager.update_end_time(case_end_time=case_end_time)
            # 停止测试用例监控视频
            frequency_test.stop_case_monitor_video()
            file_date = f"{case_number}_{start_time}"
            # 保存逻辑分析仪数据
            self.save_logic_data(file_date, case_number, end_time)

            # 功能测试用例执行结束
            self.step_index = 0
            case_result = ""
            result = None
            if case_number in self.vision_reverts.keys() and case_number in self.execute_modes.keys():
                vision_revert = self.vision_reverts[case_number]
                execute_mode = self.execute_modes[case_number]
                logger.info(f"step_execute_finish vision_revert={vision_revert}, execute_mode={execute_mode}")
                if execute_mode.__eq__("SEMI_AUTOMATED_EXECUTION"):
                    # 半自动化测试用例通过人工判定测试结果，默认上传空字符测试结果
                    case_result = "待判定"
                else:
                    # 通过测试步骤的执行情况判定测试结果
                    case_result = self.calculate_case_result()

                if operator.eq("PASS", case_result):
                    result = True
                elif operator.eq("NG", case_result):
                    result = False
                elif operator.eq("待判定", case_result):
                    result = None

            if case_manager.abnormal_stop and operator.eq("NG", case_result):
                signals_manager.case_execute_finish.emit(case_number, case_result, True)
                abnormal_stop = True
                self.set_step_status(False, True)
                case_manager.status = CaseStatus.FINISH
                case_manager.cases_execute_finish("异常停止")
                signals_manager.update_test_status.emit(CaseStatus.FINISH)

                # 将三色灯显示为红色
                if mcc_io_client.is_open():
                    mcc_io_client.open_color_red()
                elif tri_color_light.is_open():
                    tri_color_light.open_color_red()
            else:
                signals_manager.case_execute_finish.emit(case_number, case_result, False)
                abnormal_stop = False

            threading.Thread(target=self.post_test_records, args=(case_number, case_name, case_id, case_version,
                                                                  case_start_time, case_end_time, result, steps,
                                                                  case_order, file_date, abnormal_stop)).start()
        else:
            logger.info(f"step_execute_finish abnormal_stop={case_manager.abnormal_stop}, step_result={step_result}")
            if case_manager.abnormal_stop and operator.eq("NG", step_result):
                self.set_step_status(False, True)
                signals_manager.case_execute_finish.emit(case_number, "NG", True)
                case_manager.status = CaseStatus.FINISH
                case_manager.cases_execute_finish("异常停止")
                signals_manager.update_test_status.emit(CaseStatus.FINISH)

                case_name = case_manager.current_case.get("name", "")
                case_id = case_manager.current_case.get("id", 0)
                case_version = case_manager.current_case.get("version", "")
                steps = copy.deepcopy(self.steps)
                case_order = case_manager.case_order
                case_start_time = case_manager.get_case_start_time()
                start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
                case_end_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                end_time = case_end_time.replace(" ", "_").replace(":", "-").replace(".", "-")
                # 停止测试用例监控视频
                frequency_test.stop_case_monitor_video()
                file_date = f"{case_number}_{start_time}"
                # 保存逻辑分析仪数据
                self.save_logic_data(file_date, case_number, end_time)

                delay_time = max(mcu_log_reader.delay_stop_time, soc_log_reader.delay_stop_time, logcat_reader.delay_stop_time)
                logger.info(f"step_execute_finish delay_time={delay_time}")
                threading.Timer(
                    interval=delay_time,  # 延迟执行日志上传
                    function=self.post_test_records, args=(case_number, case_name, case_id, case_version,
                                                           case_start_time, case_end_time, False, steps,
                                                           case_order, file_date, True)).start()

                # 将三色灯显示为红色
                if mcc_io_client.is_open():
                    mcc_io_client.open_color_red()
                elif tri_color_light.is_open():
                    tri_color_light.open_color_red()
            else:
                self.set_step()

    @staticmethod
    def detect_functionality_result(vision_revert, result_dict):
        logger.info(f"detect_functionality_result vision_revert={vision_revert}, result_dict={result_dict}")
        if len(result_dict) == 0:
            return True, 0

        result = True
        err_desc = []
        grainy_screen_detect_value = []
        flicker_screen_detect_value = []
        black_screen_detect_value = []
        for key, value in result_dict.items():
            flicker_screen = value["flicker_screen"]
            grainy_screen = value["grainy_screen"]
            black_screen = value["black_screen"]
            flicker_screen_reason = value.get("flicker_screen_reason", "")
            grainy_screen_reason = value.get("grainy_screen_reason", "")
            black_screen_reason = value.get("black_screen_reason", "")
            grainy_screen_detect_value = value["grainy_screen_detect_value"]
            flicker_screen_detect_value = value["flicker_screen_detect_value"]
            black_screen_detect_value = value["black_screen_detect_value"]
            logger.info(f"detect_functionality_result flicker_screen={flicker_screen}, grainy_screen={grainy_screen}, "
                        f"black_screen={black_screen}, grainy_screen_detect_value={grainy_screen_detect_value}, "
                        f"flicker_screen_detect_value={flicker_screen_detect_value}, "
                        f"black_screen_detect_value={black_screen_detect_value}")
            if vision_revert:
                if black_screen != 1:
                    result = False
                    err_desc.append("黑屏异常")
                if flicker_screen > 0:
                    result = False
                    err_desc.append("闪屏异常")
                if grainy_screen > 0:
                    result = False
                    err_desc.append("花屏异常")
            else:
                if flicker_screen > 0:
                    result = False
                    err_desc.append(f"闪屏异常【{flicker_screen_reason}】")
                if grainy_screen > 0:
                    result = False
                    err_desc.append(f"花屏异常【{grainy_screen_reason}】")
                if black_screen > 0:
                    result = False
                    err_desc.append(f"黑屏异常【{black_screen_reason}】")
        ret_err_desc = " & ".join(err_desc)
        return result, ret_err_desc, grainy_screen_detect_value, flicker_screen_detect_value, black_screen_detect_value

    def dispatch_step(self):
        logger.debug("dispatch_step")
        self._timer = Timer(interval=self.DISPATCH_STEP_INTERVAL, function=self.dispatch_step)
        self._timer.start()
        if self._step_queue.empty():
            return
        step = self._step_queue.get()
        if step is None:
            return

        self.receive_step(step)

    def receive_step(self, step):
        case_number = step.get("case_number", "")
        case_name = step.get("case_name", "")
        execute_mode = step.get("execute_mode", "")
        step_type = step.get("step_type", "")
        command = step.get("command", "")
        params = step.get("params", "")
        data = step.get("data", "")
        expect = step.get("expect", "")
        logger.info(f"receive_step case_number={case_number}, case_name={case_name}, execute_mode={execute_mode}, "
                    f"step_type={step_type}, command={command}, params={params}, data={data}, expect={expect}")

        start_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        signals_manager.update_step_start_time.emit(command, start_time)
        step.update({"start_time": start_time})
        if not execute_mode.__eq__("MANUAL_EXECUTION"):
            signals_manager.update_step_status.emit(command, "执行中", "")
        self.update_step_status(command, "执行中")
        self.execute_step(step)
        self.step_command = command
        self.step_retry_times = 0
        # 测试时长大于10s
        if command not in ["UpdateFwByRandom", "ExecuteBat", "SetDelayTime", "SetRandomDelayTime", "SwitchStepVoltage",
                           "TouchStillTest", "Mate3AfterRotate", "ReadPeriodWorkCurrent", "SwitchStepBrightness",
                           "TouchMarkingTest", "M410Test", "M410Test--GAMMA_CURVE", "M410Test--BRIGHTNESS_CURVE",
                           "M410Test--CONTRAST_RATIO", "M410Test--COLOUR_GAMUT", "M410Test--UNIFORMITY",
                           "LightSensorAutoTest", "EnvTemperatureTest", "ExecuteLoopPowerOnOff", "M410--9PointsColor",
                           "ReadColorCoordinates", "TouchPointsDetect", "M410--9PointsBrightness", "ReadAEQTASK",
                           "CanProtocolAddTestID", "TpcmTest", "RecordImageAlgorithm", "recordCanMsg", "TMfingerClick",
                           "M410--FlickerTest", "M410--ResponseTimeTest", "ExecuteBlockBat", "DetectAngle", "LinSender","M410--SinglePointBrightness"
                           "UpdateFwByStep", "MonitorMultiChannelWorkCurrent", "HXCTA_Test"]:

            if not execute_mode.__eq__("MANUAL_EXECUTION"):
                if self.check_step_timeout_timer is not None:
                    self.check_step_timeout_timer.cancel()
                self.check_step_state(case_number, command, max_retry_times=10)

    def check_step_state(self, case_number, command, max_retry_times=10):
        logger.debug(f"check_step_state case_number={case_number}, command={command}, max_retry_times={max_retry_times}"
                     f", step_command={self.step_command}")

        self.check_step_timeout_timer = threading.Timer(interval=1,
                                                        function=self.check_step_state,
                                                        args=(case_number, command, max_retry_times))
        self.check_step_timeout_timer.start()

        if operator.eq(command, self.step_command):
            self.step_retry_times += 1
            if self.step_index in self.step_status.keys():
                step_status = self.step_status[self.step_index]
                logger.info(f"check_step_state step_status={step_status}, step_command={self.step_command}")
                if operator.eq(self.step_command, step_status["command"]):
                    logger.info(f"check_step_state step_retry_times={self.step_retry_times}")
                    if self.step_retry_times >= max_retry_times:
                        if operator.eq("执行中", step_status["status"]):
                            signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG: 步骤执行超时")

                        self.check_step_timeout_timer.cancel()
                else:
                    self.check_step_timeout_timer.cancel()
        else:
            self.check_step_timeout_timer.cancel()

    def update_step_status(self, command, status):
        if self.step_index in self.step_status.keys():
            step_status = self.step_status[self.step_index]
            if operator.eq(command, step_status["command"]):
                step_status["status"] = status

    def calculate_case_result(self):
        logger.info("calculate_case_result")
        for step in self.steps:
            step_result = step.get("step_result", "NG")
            if operator.eq("NG", step_result):
                return step_result
            elif operator.eq("待判定", step_result):
                return step_result
        return "PASS"

    def get_current_step(self):
        if self.step_index < len(self.steps):
            return self.steps[self.step_index]
        return None

    def get_current_step_command(self):
        step = self.get_current_step()
        if step is None:
            return ""
        else:
            return step.get("command", "")

    def set_step(self):
        logger.info(f"set_step status={self.status}, step_index={self.step_index}")
        try:
            if self.status:
                if self.step_index < len(self.steps):
                    self._step_queue.put(self.steps[self.step_index])
        except Exception as e:
            logger.error(f"set_step exception: {str(e.args)}")

    def start_step_center(self):
        self._timer.start()

    def increase_step_index(self):
        self.step_index += 1
        logger.info(f"increase_step_index step_index={self.step_index}")

    def check_steps_finish(self):
        finish = self.step_index >= len(self.steps)
        logger.debug(f"check_steps_finish step_index={self.step_index}, steps={self.steps}, finish={finish}")
        return finish

    def execute_step(self, step):
        step_type = step.get("step_type", "")
        expect = step.get("expect", "")
        logger.info("execute_step step_type={}, expect={}".format(step_type, expect))
        if operator.eq("CAN", step_type):
            self.execute_can(step)
        elif operator.eq("LIN", step_type):
            self.lin_stop_event.clear()
            self.execute_lin(step)
        elif operator.eq("I2C", step_type):
            self.execute_i2c(step)
        elif operator.eq("CustomizeCMD", step_type):
            self.execute_customize_cmd(step)

    @staticmethod
    def execute_i2c(step):
        def high_low_to_int(high, low):
            """
            将两个字节的高低位转化为整数
            :param high:
            :param low:
            :return:
            """
            return low | (high << 8)

        def parse_result(result, expect):
            value_list = []
            if "XX" in expect:
                expect_list = expect.split(" ")
                result_list = result.split(" ")
                for i in range(len(expect_list)):
                    if expect_list[i] == "XX":
                        value = result_list[i]
                        value_list.append(value)

            return value_list

        def compare_result(step, result):
            expect = step.get("expect")
            option = step.get("option")
            min = step.get("min")
            max = step.get("max")
            logger.info(f"compare_result step is {step}")

            if option == "不匹配":
                signals_manager.step_execute_finish.emit(case_number, "I2C", "PASS", "PASS")
                return True, None
            elif option == "等于":
                match = re.match(expect, result, re.M | re.I)
                if match is None:
                    return False, result
                value_list = re.findall(expect, result, re.M | re.I)
                if len(value_list) == 0:
                    return False, result
                if isinstance(value_list[0], str):
                    value = int(value_list[0], 16)
                elif isinstance(value_list[0], tuple):
                    value = high_low_to_int(int(value_list[0][0], 16), int(value_list[0][1], 16))
                else:
                    return False, json.dumps(value_list)
                if int(value) == int(float(min)):
                    return True, result.lower()
                else:
                    return False, result.lower()
            elif option == "范围":
                match = re.match(expect, result, re.M | re.I)
                if match is None:
                    return False, result
                value_list = re.findall(expect, result, re.M | re.I)
                if len(value_list) == 0:
                    return False, result
                if isinstance(value_list[0], str):
                    value = int(value_list[0], 16)
                elif isinstance(value_list[0], tuple):
                    tmp = list(value_list[0])
                    value = high_low_to_int(int(tmp[0], 16), int(tmp[1], 16))
                else:
                    return False, json.dumps(value_list)
                if float(max) >= value >= float(min):
                    return True, value
                else:
                    return False, value
            elif option == "完全匹配":
                if expect.lower() not in result.lower():
                    return False, result
                else:
                    return True, result
            else:
                logger.error(f"option is not right {step}")

        logger.info("execute_i2c step={}".format(step))
        case_number = step.get("case_number")
        command = step.get("params")  # 不是cmd
        expect = step.get("expect")
        logger.info("execute_i2c command={}, expect={}".format(command, expect))
        try:
            tmp = photics_manager.execute_adb_command(command)
            if isinstance(tmp, list):
                if len(tmp) == 0:
                    result = "执行成功"
                else:
                    result = "".join(tmp).strip()
            else:
                result = tmp
            logger.info("execute_i2c command={}, result={}".format(command, result))
            flag, value = compare_result(step, result)
            if flag:
                signals_manager.step_execute_finish.emit(case_number, "I2C", "PASS", str(result))
            else:
                signals_manager.step_execute_finish.emit(case_number, "I2C", "NG", str(result))
        except Exception as e:
            logger.error("execute_i2c exception: {}".format(traceback.format_exc()))
            signals_manager.step_execute_finish.emit(case_number, "I2C", "NG", str(e.args))

    def switch_can_bus(self, chl):
        if adb_connect_device.can_bus is not None:
            adb_connect_device.can_bus = adb_connect_device.can_bus_list[chl - 1]
            if adb_connect_device.can_bus.name == "zlg":
                if chl == 1:
                    adb_connect_device.can_bus.set_channel(adb_connect_device.can_bus._can_handle)
                else:
                    adb_connect_device.can_bus.set_channel(adb_connect_device.can_bus._can_handle2)
            if adb_connect_device.can_bus.name == "pcan":
                if chl == 1:
                    adb_connect_device.can_bus.set_channel(adb_connect_device.can_bus.pcan_channel)
                else:
                    adb_connect_device.can_bus.set_channel(adb_connect_device.can_bus.pcan_channel2)

    def execute_can(self, step):
        logger.info(f"execute_can step={step}")
        case_number = step.get("case_number")
        command = step.get("command")
        msg_type = step.get("msg_type")
        try:
            if operator.eq("MSG_FUNC_ADDR", msg_type):
                send_id = project_manager.get_func_address_id()
                recv_id = project_manager.get_response_id()
            elif operator.eq("MSG_PHY_ADDR", msg_type):
                send_id = project_manager.get_phy_address_id()
                recv_id = project_manager.get_response_id()
            else:
                send_id = step.get("id")
                recv_id = step.get("recv_id")

            if step.get("id") and step.get("msg"):
                can_dict = {
                    "id": send_id,
                    "msg": step.get("msg"),
                    "can_type": step.get("can_type"),
                    "cycle_period": step.get("cycle_period"),
                    "uds": step.get("uds"),
                    "recv_id": recv_id,
                    "expect": step.get("expect") if isinstance(step.get("expect"), list) else [step.get("expect")],
                    "channel": step.get("channel"),
                }

                if adb_connect_device.can_bus is None:
                    signals_manager.step_execute_finish.emit(case_number, command, "NG",
                                                             "NG: CAN设备未连接，请连接CAN设备后再测试")
                    return
                # 切换can通道
                self.switch_can_bus(int(step.get("channel")))

                if adb_connect_device.can_bus.name == "zlg":
                    msg = self.set_message(can_dict)
                    adb_connect_device.zlg_send_and_read_msg(case_number, command, msg, can_dict)
                elif adb_connect_device.can_bus.name == "pcan":
                    msg = self.set_message(can_dict)
                    adb_connect_device.pcan_send_and_read_msg(case_number, command, msg, can_dict)
                elif adb_connect_device.can_bus.name == "canoe":
                    msg = self.set_message(can_dict)
                    adb_connect_device.canoe_send_and_read_msg(case_number, command, msg, can_dict)
                elif adb_connect_device.can_bus.name == "tsmaster":
                    msg = self.set_message(can_dict)
                    adb_connect_device.tsmaster_send_and_read_msg(case_number, command, msg, can_dict)
        except Exception as e:
            print(f"execute_can exception: {str(e.args)}")
            logger.error("execute_can exception: {}".format(traceback.format_exc()))

    def send_can_msg(self, can_dict, msg):
        try:
            adb_connect_device.can_bus.send(msg)
            if can_dict["period"] == "True":
                Timer(float(can_dict["period_time"]) * 0.001, function=self.send_can_msg, args=(can_dict, msg)).start()
        except Exception as e:
            logger.error("send_can_msg exception: {}".format(str(e.args)))

    @staticmethod
    def dlc2len(dlc):
        if dlc <= 8:
            return dlc
        elif dlc == 9:
            return 12
        elif dlc == 10:
            return 16
        elif dlc == 11:
            return 20
        elif dlc == 12:
            return 24
        elif dlc == 13:
            return 32
        elif dlc == 14:
            return 48
        else:
            return 64

    @staticmethod
    def set_message(can_dict):
        try:
            if adb_connect_device.can_bus.name == "zlg":
                # 周立功的发送在zlgBase.py中，这里修改无效
                data = can_dict["msg"].split(' ')
                msg_len = len(data)
                is_canfd_msg = True if adb_connect_device.can_bus.can_type.lower() == "canfd" else False
                if is_canfd_msg:
                    msg = ZCAN_TransmitFD_Data()
                else:
                    msg = ZCAN_Transmit_Data()
                msg.transmit_type = 0  # 正常发送 1 单次发送 2自发自收
                try:
                    msg.frame.can_id = int(can_dict["id"], 16)
                except Exception as e:
                    print(str(e.args))
                    msg.frame.can_id = 0
                msg.frame.rtr = 0  # ("数据帧", "远程帧")
                msg.frame.eff = 0  # ("标准帧", "扩展帧")

                if not is_canfd_msg:
                    msg.frame.can_dlc = msg_len
                    msg_len = msg.frame.can_dlc
                else:
                    msg.frame.brs = 0  # can canfd 0，"CANFD BRS" 1
                    # msg.frame.len = StepManager.dlc2len(msg_len)
                    msg.frame.len = msg_len
                    # msg_len = msg.frame.len
                    # msg.frame.can_dlc = dlc_dict[str(msg_len)]

                for i in range(msg_len):
                    if i < len(data):
                        try:
                            msg.frame.data[i] = int(data[i], 16)
                        except Exception as e:
                            print(str(e.args))
                            msg.frame.data[i] = 0
                    else:
                        msg.frame.data[i] = 0
                return msg
            elif adb_connect_device.can_bus.name == "canoe":
                if adb_connect_device.can_bus.can_type.lower() == "can":
                    msg = can.Message(
                        arbitration_id=int(can_dict["id"], 16),
                        is_extended_id=False,
                        # dlc=dlc_dict[str(len(can_dict["msg"].strip().split(" ")))],
                        data=[int(i, 16) for i in can_dict["msg"].strip().split(" ")]
                    )
                    return msg
                else:
                    msg = can.Message(
                        arbitration_id=int(can_dict["id"], 16),
                        is_extended_id=False,
                        is_fd=True,
                        # dlc=dlc_dict[str(len(can_dict["msg"].strip().split(" ")))],
                        data=[int(i, 16) for i in can_dict["msg"].strip().split(" ")]
                    )
                    return msg
            elif adb_connect_device.can_bus.name.lower() == "tsmaster":
                msg = can.Message(
                    arbitration_id=int(can_dict["id"], 16),
                    is_extended_id=False,
                    is_fd=adb_connect_device.can_bus.is_fd,
                    # dlc=len(can_dict["msg"].strip().split(" ")),
                    # dlc=dlc_dict[str(len(can_dict["msg"].strip().split(" ")))],
                    channel=adb_connect_device.can_bus.channel,
                    data=[int(i, 16) for i in can_dict["msg"].strip().split(" ")]
                )
                return msg

            else:
                msg = can.Message(
                    arbitration_id=int(can_dict["id"], 16),
                    is_extended_id=False,
                    dlc=len(can_dict["msg"].strip().split(" ")),
                    # dlc=dlc_dict[str(len(can_dict["msg"].strip().split(" ")))],
                    data=[int(i, 16) for i in can_dict["msg"].strip().split(" ")]
                )
                return msg
        except Exception as e:
            print(str(e.args))
            logger.error("set_message exception: {}".format(traceback.format_exc()))

    @staticmethod
    def execute_lin(step):
        case_number = step.get("case_number")
        command = step.get("command")
        try:

            if step.get("id") and step.get("msg"):
                adb_connect_device.plin_send_msg(step)
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        except Exception as e:
            print(str(e.args))
            logger.error("execute_lin exception: {}".format(str(traceback.format_exc())))
            signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG")

    def execute_customize_cmd(self, step):
        """执行自定义命令 - 优化版本使用命令注册器"""
        from case.command_registry import command_registry

        total_times = step.get("total_times")
        current_times = step.get("current_times")
        case_number = step.get("case_number")
        case_name = step.get("case_name")
        expect = step.get("expect")
        customize_cmd = step.get("CustomizeCMD")
        data = step.get("data")
        command = step.get("command")

        logger.info(f"execute_customize_cmd total_times={total_times}, current_times={current_times}, "
                    f"case_number={case_number}, case_name={case_name}, expect={expect}, "
                    f"customize_cmd={customize_cmd}, data={data}, command={command}")

        # 使用命令注册器处理命令
        try:
            command_registry.execute_command(command, step)
        except Exception as e:
            logger.error(f"execute_customize_cmd exception: {str(traceback.format_exc())}")
            signals_manager.step_execute_finish.emit(case_number, command, "NG", f"命令执行异常: {str(e)}")

        # 保留原有的 if 分支作为兼容性处理，如果命令注册器中没有找到对应的处理器
        if not command_registry.has_command(command):
            self._execute_legacy_command(step)

    def _execute_legacy_command(self, step):
        """执行传统命令处理逻辑（兼容性保留）"""
        total_times = step.get("total_times")
        current_times = step.get("current_times")
        case_number = step.get("case_number")
        case_name = step.get("case_name")
        expect = step.get("expect")
        customize_cmd = step.get("CustomizeCMD")
        data = step.get("data")
        command = step.get("command")

        # 这里保留一些最常用的命令作为示例，其他命令已移动到专门的处理器中
        logger.warning(f"命令 {command} 未在新的命令处理器中找到，使用传统处理方式")

        # 保留一些特殊的命令处理逻辑，这些命令可能需要特殊的上下文或复杂的逻辑
        if command.startswith("Detect") and command.endswith("Error"):
            params = data.split(',')
            error_mark = params[0]
            max_count = int(params[1]) if len(params) > 1 else 1
            delay_time = int(params[2]) if len(params) > 2 else 60

            if operator.eq("DetectMcuError", command):
                mcu_log_reader.append_error_mark(error_mark, max_count, delay_time)
            elif operator.eq("DetectSocError", command):
                soc_log_reader.append_error_mark(error_mark, max_count, delay_time)
            elif operator.eq("DetectOsError", command):
                logcat_reader.append_error_mark(error_mark, max_count, delay_time)
            elif operator.eq("DetectVdsAppError", command):
                logcat_reader.append_error_mark(error_mark, max_count, delay_time)

            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")

        # 保留一些特殊的CAN协议相关命令，因为它们需要特殊的上下文
        elif operator.eq("CanProtocolStartApp", command):
            from adb.util.canoeAuto import get_canoe_app
            cfg_path = data.split(",")[0]
            tse_path = data.split(",")[1]
            canoe_app = get_canoe_app()
            if not canoe_app:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "CanoeApp is None")
                return
            canoe_app.run_test_modules(cfg_path, tse_path, case_number, command, )
            canoe_app.set_SysVar("pythonsys", "run_test", 1)

        elif operator.eq("CanProtocolStopApp", command):
            from adb.util.canoeAuto import canoe_app
            if canoe_app:
                canoe_app.Stop()
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "CanoeApp is None")
            # kill task CANoe64.exe
            os.system("taskkill /f /im CANoe64.exe")

        elif operator.eq("CanProtocolAddTestID", command):
            from adb.util.canoeAuto import get_canoe_app
            canoe_app = get_canoe_app()
            if canoe_app is None:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "CanoeApp is None")
                return

            module_id = data.split(",")[0]
            test_id = data.split(",")[1]
            while canoe_app.is_alive():
                value = canoe_app.get_SysVar("pythonsys", "run_test")
                if int(value) != 1:
                    logging.info(f"value is {value} waiting for canoe value to be set")
                    canoe_app.set_SysVar("pythonsys", "run_test", 1)
                    time.sleep(0.2)
                    continue
                logging.info(f"value is {value}")
                time.sleep(0.5)
                break
            canoe_app.AddTest(module_id, test_id)
            flag = False
            result = ""
            while canoe_app.is_alive():
                value = canoe_app.get_SysVar("pythonsys", "run_test")
                result = canoe_app.GetTestResult()
                logging.info(f"SysVar value is{value}, get CanProtocolAddTestID is {result}")
                if not result:
                    time.sleep(1)
                    continue
                if "NG" in result.upper():
                    flag = False
                else:
                    flag = True
                break
            signals_manager.current_canoe_asc_dir = f"{module_id.upper()}_{test_id}"
            if flag:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", result)
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG")

        elif operator.eq("SecurityLevel", command):
            from adb.util.canTools import can_tool
            project = project_manager.get_test_plan_project_number()
            position = data.split(",")[0]
            level = data.split(",")[1]
            send_id = data.split(",")[2]
            send_msg = data.split(",")[3]
            recv_id = data.split(",")[4]
            recv_msg = data.split(",")[5]
            channel = data.split(",")[6]

            self.switch_can_bus(int(channel))
            # 进入安全等级前置条件
            status, result = can_tool.run_security_level(project, position, level, send_id, send_msg, recv_id, recv_msg,
                                                         channel)
            signals_manager.step_execute_finish.emit(case_number, command, "PASS" if status else "NG", result)

        elif operator.eq("SecurityLevelEnter", command):
            from adb.util.canTools import can_tool
            project = project_manager.get_test_plan_project_number()
            position = data.split(",")[0]
            level = data.split(",")[1]
            send_id = data.split(",")[2]
            send_msg = data.split(",")[3]
            recv_id = data.split(",")[4]
            recv_msg = data.split(",")[5]
            channel = data.split(",")[6]

            self.switch_can_bus(int(channel))
            status, result = can_tool.enter_level(project, position, level, send_id, send_msg, recv_id, recv_msg,
                                                  channel)
            signals_manager.step_execute_finish.emit(case_number, command, "PASS" if status else "NG", result)

        else:
            # 如果命令没有被处理，记录警告
            logger.warning(f"未处理的命令: {command}, 该命令可能需要添加到相应的处理器中")
            signals_manager.step_execute_finish.emit(case_number, command, "NG", f"未支持的命令: {command}")

        # 所有其他命令都已经移动到专门的处理器中了
        # 如果到达这里，说明命令没有被任何处理器处理
        logger.warning(f"命令 {command} 没有被任何处理器处理")
        signals_manager.step_execute_finish.emit(case_number, command, "NG", f"未找到处理器: {command}")

    def switch_vision_collect(self, status: bool):
        """切换视觉采集状态"""
        logger.info(f"switch_vision_collect status={status}")
        visual_detect_signal.start_collect.emit(status)

    def functionality_stop_record(self, file_date: str, now_count: int, all_count: int,
                                 threshold_flicker: float, threshold_grainy: float,
                                 threshold_black: float, test_mode: int):
        """功能性停止录制"""
        logger.info(f"functionality_stop_record file_date={file_date}, now_count={now_count}, all_count={all_count}, "
                   f"threshold_flicker={threshold_flicker}, threshold_grainy={threshold_grainy}, "
                   f"threshold_black={threshold_black}, test_mode={test_mode}")
        frequency_test.start_record_index = False
        frequency_test.all_count = all_count
        threading.Timer(interval=3, function=self.functionality_video_test,
                       args=(frequency_test.record_video_path, file_date, now_count, all_count, threshold_flicker,
                             threshold_grainy, threshold_black, test_mode, camera_config.get_base_path())).start()

    @staticmethod
    def functionality_video_test(file_path: str, file_date: str, now_count: int, all_count: int,
                                threshold_flicker: float, threshold_grainy: float,
                                threshold_black: float, test_mode: int, base_path: str):
        """功能性视频测试"""
        if vision_manager.vision_calibrate_dialog is not None:
            threading.Thread(target=frequency_test.start_functionality_video_test,
                           args=([file_path, ], vision_manager.vision_calibrate_dialog, file_date, now_count,
                                 all_count, threshold_flicker, threshold_grainy, threshold_black, test_mode,
                                 base_path)).start()

    @staticmethod
    def detect_functionality_result(vision_revert: bool, result_dict: dict):
        """检测功能性结果"""
        logger.info(f"detect_functionality_result vision_revert={vision_revert}, result_dict={result_dict}")
        if len(result_dict) == 0:
            return True, 0, [], [], []

        result = True
        err_desc = []
        grainy_screen_detect_value = []
        flicker_screen_detect_value = []
        black_screen_detect_value = []

        for key, value in result_dict.items():
            flicker_screen = value["flicker_screen"]
            grainy_screen = value["grainy_screen"]
            black_screen = value["black_screen"]
            flicker_screen_reason = value.get("flicker_screen_reason", "")
            grainy_screen_reason = value.get("grainy_screen_reason", "")
            black_screen_reason = value.get("black_screen_reason", "")
            grainy_screen_detect_value = value["grainy_screen_detect_value"]
            flicker_screen_detect_value = value["flicker_screen_detect_value"]
            black_screen_detect_value = value["black_screen_detect_value"]

            logger.info(f"detect_functionality_result flicker_screen={flicker_screen}, grainy_screen={grainy_screen}, "
                       f"black_screen={black_screen}, grainy_screen_detect_value={grainy_screen_detect_value}, "
                       f"flicker_screen_detect_value={flicker_screen_detect_value}, "
                       f"black_screen_detect_value={black_screen_detect_value}")

            if vision_revert:
                if black_screen != 1:
                    result = False
                    err_desc.append("黑屏异常")
                if flicker_screen > 0:
                    result = False
                    err_desc.append("闪屏异常")
                if grainy_screen > 0:
                    result = False
                    err_desc.append("花屏异常")
            else:
                if flicker_screen > 0:
                    result = False
                    err_desc.append(f"闪屏异常【{flicker_screen_reason}】")
                if grainy_screen > 0:
                    result = False
                    err_desc.append(f"花屏异常【{grainy_screen_reason}】")
                if black_screen > 0:
                    result = False
                    err_desc.append(f"黑屏异常【{black_screen_reason}】")

        ret_err_desc = " & ".join(err_desc)
        return result, ret_err_desc, grainy_screen_detect_value, flicker_screen_detect_value, black_screen_detect_value

    def set_delay_time(self, case_number: str, command: str, time_size: float):
        """设置延时时间回调"""
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", f"设置延时：{time_size}秒")

    @staticmethod
    def go_to_center():
        """移动到中心点"""
        logger.info("go_to_center")
        touch_card.display_center_point()
        points = []
        for i in range(20):
            time.sleep(3)
            logger.info(f"go_to_center {i}")
            frame = touch_card.get_camera_image()
            points = touch_card.detact_point(frame)
            if len(points) == 0:
                continue
            else:
                break
        logger.info(f"go_to_center detect point is {points}")
        # 计算坐标
        physical_coordinates = pixel_to_physical(points, M=Calibration["color_analyser"])
        print("坐标:", physical_coordinates[0])
        touch_card.move_point(physical_coordinates[0][0], physical_coordinates[0][1], z=23567.0, Z2_value=-59508.0,
                              R_value=-12659.0)


step_manager: StepManager = StepManager()
step_manager.start_step_center()
                min_current = float(data.split(",")[0])
                max_current = float(data.split(",")[1])
                power_type = data.split(",")[2]

                if operator.eq("IT-M3200", power_type):
                    work_current = it_m3200_control.read_work_current()
                    work_current = round(work_current, 3)
                    if min_current < work_current < max_current:
                        signals_manager.step_execute_finish.emit(case_number, command, "PASS", str(work_current))
                    else:
                        signals_manager.step_execute_finish.emit(case_number, command, "NG", str(work_current))
                elif operator.eq("TOMMENS", power_type):
                    work_current = 0.0
                    if etm_3020pc_control.is_open():
                        status, work_current = etm_3020pc_control.read_work_current()
                        if not status:
                            return signals_manager.step_execute_finish.emit(case_number, command, "NG", "电流读取失败")
                    elif etm_mu3_control.is_open():
                        channel = int(data.split(",")[3])
                        status, work_current = etm_mu3_control.read_work_current(channel=channel)
                        if not status:
                            return signals_manager.step_execute_finish.emit(case_number, command, "NG", "电流读取失败")
                    work_current = round(work_current, 3)
                    logger.info(f"execute_customize_cmd case_number {case_number} work_current value is {work_current}")
                    if min_current <= work_current <= max_current:
                        signals_manager.step_execute_finish.emit(case_number, command, "PASS", str(work_current))
                    else:
                        signals_manager.step_execute_finish.emit(case_number, command, "NG", str(work_current))
        elif operator.eq("ReadPeriodWorkCurrent", command):
            if data.__contains__(","):
                read_interval = float(data.split(",")[0])
                read_time = float(data.split(",")[1])
                min_current = float(data.split(",")[2])
                max_current = float(data.split(",")[3])

                if it_m3200_control.is_connect():
                    status, error_work_current = it_m3200_control.read_period_work_current(read_interval, read_time,
                                                                                           min_current, max_current)
                    if status:
                        logger.info(f"execute_customize_cmd 周期工作电流读取正常")
                        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
                    else:
                        logger.info(f"execute_customize_cmd 周期工作电流读取异常，异常工作电流值：{error_work_current}")
                        signals_manager.step_execute_finish.emit(case_number, command, "NG", str(error_work_current))
                elif etm_3020pc_control.is_open():
                    status, error_work_current = etm_3020pc_control.read_period_work_current(read_interval, read_time,
                                                                                             min_current, max_current)
                    if status:
                        logger.info(f"execute_customize_cmd 周期工作电流读取正常")
                        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
                    else:
                        logger.info(f"execute_customize_cmd 周期工作电流读取异常，异常工作电流值：{error_work_current}")
                        signals_manager.step_execute_finish.emit(case_number, command, "NG", str(error_work_current))
                elif etm_mu3_control.is_open():
                    channel = int(data.split(",")[4])
                    status, error_work_current = etm_mu3_control.read_period_work_current(read_interval,
                                                                                          read_time, min_current,
                                                                                          max_current, channel)
                    if status:
                        logger.info(f"execute_customize_cmd 周期工作电流读取正常")
                        signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
                    else:
                        logger.info(f"execute_customize_cmd  周期工作电流读取异常，异常工作电流值：{error_work_current}")
                        signals_manager.step_execute_finish.emit(case_number, command, "NG", str(error_work_current))
        elif operator.eq("ReadWorkVoltage", command):
            if data.__contains__(","):
                min_voltage = data.split(",")[0]
                max_voltage = data.split(",")[1]
                adb_connect_device.adb_forward_send_data(action="readWorkVoltage")
                vds_detect_manager.set_expect_work_voltage(case_number, command, min_voltage, max_voltage)
        elif operator.eq("SwitchSleep", command):
            adb_connect_device.switch_sleep()
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        elif operator.eq("SwitchWakeup", command):
            adb_connect_device.switch_wakeup()
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        elif operator.eq("TpcmTest", command):
            adb_connect_device.test_tpcm()
            vds_detect_manager.set_expect_test_tpcm(case_number, command)
        elif operator.eq("PhoticsGammaCurve", command):
            photics_manager.set_current_func(PhoticsFunction.GAMMA_CURVE)
        elif operator.eq("PhoticsBrightnessCurve", command):
            photics_manager.set_current_func(PhoticsFunction.BRIGHTNESS_CURVE)
        elif operator.eq("PhoticsContrastRatio", command):
            photics_manager.set_current_func(PhoticsFunction.CONTRAST_RATIO)
        elif operator.eq("PhoticsColourGamut", command):
            photics_manager.set_current_func(PhoticsFunction.COLOUR_GAMUT)
        elif operator.eq("PhoticsUniformity", command):
            photics_manager.set_current_func(PhoticsFunction.UNIFORMITY)
        elif operator.eq("LightSensorCurve", command):
            signals_manager.start_light_sensor_curve_signal.emit(case_number, command)
        elif operator.eq("TemperatureRise", command):
            signals_manager.start_env_temperature_rise_signal.emit(case_number, command)
        elif operator.eq("StartRecord", command):
            if case_manager.demarcated:
                case_start_time = case_manager.get_case_start_time()
                start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
                file_date = f"{case_number}_{start_time}"
                date = f"{datetime.datetime.now().strftime('%Y-%m-%d')}"
                base_path = os.path.join(app_config.vision_folder, project_manager.get_test_plan_project_number(), date)
                camera_config.set_resource_path(base_path)
                threading.Thread(target=frequency_test.start_record,
                                 args=(camera_manager.video_list_function, file_date)).start()
                self.switch_vision_collect(True)
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG: 视觉算法画面未标定")
        elif operator.eq("StopRecord", command):
            vision_revert = self.vision_reverts[case_number]
            case_start_time = case_manager.get_case_start_time()
            start_time = case_start_time.replace(" ", "_").replace(":", "-").replace(".", "-")
            file_date = f"{case_number}_{start_time}"
            visual_detect_signal.function_test_clear_result.emit()
            if case_manager.demarcated:
                self.switch_vision_collect(False)
                test_mode = int(data.split(",")[0])
                threshold_flicker = float(data.split(",")[1])
                threshold_grainy = float(data.split(",")[2])
                try:
                    threshold_black = float(data.split(",")[3])
                except Exception as e:
                    threshold_black = 10
                    logger.info(f"execute_customize_cmd exception: {str(e.args)}")
                self.functionality_stop_record(file_date, current_times, total_times, threshold_flicker,
                                               threshold_grainy, threshold_black, test_mode)
                result_dict = frequency_test.get_functionality_result()
                logger.info(f"execute_customize_cmd result_dict={result_dict}")
                if result_dict is None:
                    return signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")

                if len(result_dict) == 0:
                    return signals_manager.step_execute_finish.emit(case_number, command, "NG",
                                                                    "视觉检测异常：未检测到结果")

                result = self.detect_functionality_result(vision_revert, result_dict)
                if result[0]:
                    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
                else:
                    signals_manager.step_execute_finish.emit(case_number, command, "NG", f"视觉检测异常：{result[1]}")

                signals_manager.update_grainy_screen_detect_value.emit(result[2])
                signals_manager.update_flicker_screen_detect_value.emit(result[3])
                signals_manager.update_black_screen_detect_value.emit(result[4])

                if frequency_test.current_files_path is not None:
                    shutil.rmtree(frequency_test.current_files_path)
                    logger.info(f"execute_customize_cmd delete {frequency_test.current_files_path} success")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG: 视觉算法画面未标定")
        elif operator.eq("SetDelayTime", command):
            if data.__contains__(","):
                time_size = 0
                value = data.split(",")[0]
                unit = data.split(",")[1]
                if operator.eq("s", unit):
                    time_size = float(value)
                elif operator.eq("ms", unit):
                    time_size = float(value) / 1000
            else:
                time_size = float(data)

            if time_size >= 2:
                timer_manager.set_params(case_number, command, time_size)
                timer_manager.stop_timer()
                timer_manager.start_timer()
            else:
                threading.Timer(interval=time_size, function=self.set_delay_time,
                                args=(case_number, command, time_size)).start()
        elif operator.eq("SetRandomDelayTime", command):
            datas = data.split(",")
            time_size = 0
            if len(datas) == 2:
                value = random.uniform(float(datas[0]), float(datas[1]))
                time_size = round(float(value), 3)
            elif len(datas) == 3:
                value = random.uniform(float(datas[0]), float(datas[1]))
                unit = datas[2]
                if operator.eq("s", unit):
                    time_size = round(float(value), 3)
                elif operator.eq("ms", unit):
                    time_size = round(float(value) / 1000, 3)

            if time_size >= 2:
                timer_manager.set_params(case_number, command, time_size)
                timer_manager.stop_timer()
                timer_manager.start_timer()
            else:
                threading.Timer(interval=time_size, function=self.set_delay_time,
                                args=(case_number, command, time_size)).start()
        elif operator.eq("StartCollectVisionBrightness", command):
            visual_detect_signal.start_brightness_test.emit()
        elif operator.eq("DetectVisionBrightness", command):
            # 开启采集视觉亮度之后需要延时一段时间(例如5s)再去检测视觉亮度
            if data.__contains__(","):
                min_brightness = int(data.split(",")[0])
                max_brightness = int(data.split(",")[1])
                detect_brightness = frequency_test.get_black_screen_value()
                if min_brightness <= detect_brightness <= max_brightness:
                    signals_manager.step_execute_finish.emit(case_number, command, "PASS", str(detect_brightness))
                else:
                    signals_manager.step_execute_finish.emit(case_number, command, "NG", str(detect_brightness))
        elif operator.eq("StopCycleCANMsg", command):
            adb_connect_device.stop_cycle_can_msg(int(data, 16))
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        elif operator.eq("ClearCycleCANMsg", command):
            adb_connect_device.clear_cycle_can_msg()
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        elif operator.eq("StartAutoCyclePress", command):
            if data.__contains__(","):
                delay = data.split(",")[0]
                counter = data.split(",")[1]
                auto_key_press.start_auto_cycle_press(delay, counter)
            else:
                auto_key_press.start_auto_cycle_press()
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        elif operator.eq("StopAutoCyclePress", command):
            auto_key_press.stop_mb()
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        elif operator.eq("StartCustomCyclePress", command):
            if data.__contains__(","):
                repeat = data.split(",")[0]
                position = data.split(",")[1]
                auto_key_press.reset_custom_params()
                auto_key_press.start_custom_cycle_press(repeat=int(repeat), position=position)
            else:
                auto_key_press.reset_custom_params()
                auto_key_press.start_custom_cycle_press()
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        elif operator.eq("StopCustomCyclePress", command):
            auto_key_press.stop_custom_cycle_press()
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        elif operator.eq("UpdateFwByRandom", command):
            bat_manager.handle_update_fw_by_random(case_number, command, data)
        elif operator.eq("UpdateFwByStep", command):
            bat_manager.handle_update_fw_by_step(case_number, command, data)
        elif operator.eq("ExecuteAdbCmd", command):
            adb_manager.handle_execute_adb_cmd(case_number, command, data)
        elif operator.eq("ExecuteBat", command):
            bat_manager.handle_execute_bat(case_number, command, data)
        elif operator.eq("ExecuteBlockBat", command):
            bat_manager.handle_execute_block_bat(case_number, command, data)
        elif operator.eq("TouchStillTest", command):
            adb_connect_device.test_touch_still()
            touch_manager.handle_touch_still_test(case_number, command, float(data))
        elif operator.eq("TouchMarkingTest", command):
            from control_board.auto_test_m.ctr_card import ctr_card
            tms = int(data.split(",")[1])
            points_num = int(data.split(",")[0])
            start_index = int(data.split(",")[2])
            end_index = int(data.split(",")[3])
            touch_card.finger2zero()
            ctr_card.go_home()

            time.sleep(3)
            points = touch_card.get_9_points()
            points.sort(key=lambda x: x[0])
            left = copy.deepcopy(points[0:3])
            right = copy.deepcopy(points[6:9])
            left.sort(key=lambda x: x[1])
            right.sort(key=lambda x: x[1])

            if len(points) == 0:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "No Points Display!")
                return
            # p1 = points[int(start_index)]
            # p2 = points[int(end_index)]
            p1 = list(left[1])
            p2 = list(right[1])
            # points.sort(key=lambda x: x[0])
            # points_head = points[0:3]
            # points_head.sort(key=lambda x: x[1])
            # p1 = points_head[1]
            # points_tail = points[-3:]
            # points_tail.sort(key=lambda x: x[1])
            # p2 = points_tail[1]
            # # p1, p2 = touch_card.find_midpoints(points, axis="y")
            # p1 = list(p1)
            # p2 = list(p2)
            touch_card.finger_move_line_result = True
            touch_card.case_number = case_number
            touch_card.command = command
            # vds_detect_manager.set_expect_end_draw_lines(case_number, command, data)
            M = Calibration[f"point3"]
            p1 = pixel_to_physical([p1], M=M)
            p2 = pixel_to_physical([p2], M=M)
            touch_card.finger_move_line(p1[0], p2[0], points_num, tms)
            # signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        elif operator.eq("TouchPointsDetect", command):
            points_num = int(data.split(",")[0])
            touch_card.display_center_point()
            time.sleep(1)
            frame = touch_card.get_camera_image()
            points = touch_card.detact_point(frame)
            logger.info(f"go_to_center detect point is {points}")
            # 计算坐标
            M = Calibration["point3"]
            physical_coords = pixel_to_physical(points, M=M)
            touch_card.finger_move_line_result = True
            touch_card.case_number = case_number
            touch_card.command = command
            vds_detect_manager.expect_end_touch_points_count = points_num
            touch_card.move_point(physical_coords[0][0], physical_coords[0][1], z=-62369.0, R_value=12573.0, Z2_value=0)
            touch_card.touch_points_count(points_num)
        elif operator.eq("TouchRespTimes", command):
            interval_time = float(data.split(",")[0])
            calibrate_resp_times = int(data.split(",")[1])
            # action返回结果，用于判断
            vds_detect_manager.set_expect_touch_resp_times(case_number, command, data)
            threading.Thread(target=touch_manager.handle_touch_resp_times_test,
                             args=(case_number, command, interval_time, calibrate_resp_times)).start()
        elif operator.eq("TouchRespTime", command):
            adb_connect_device.test_touch_resp_time()
            interval_time = float(data.split(",")[0])
            calibrate_resp_time = float(data.split(",")[1])
            threading.Thread(target=touch_manager.handle_touch_resp_time_test,
                             args=(case_number, command, interval_time, calibrate_resp_time)).start()
        elif operator.eq("TouchReportRate", command):
            adb_connect_device.test_touch_report_rate()
            calibrate_report_rate = int(data)
            touch_manager.handle_touch_report_rate_test(case_number, command, calibrate_report_rate)
        elif operator.eq("ReadNitBrightness", command):
            if data.__contains__(","):
                min_brightness = float(data.split(",")[0])
                max_brightness = float(data.split(",")[1])
                analyzer_data = color_analyzer_manager.read_xyLv_data()
                logger.info(f"execute_customize_cmd analyzer_data={analyzer_data}, min_brightness={min_brightness}, "
                            f"max_brightness={max_brightness}")
                if min_brightness < float(analyzer_data[2]) < max_brightness:
                    signals_manager.step_execute_finish.emit(case_number, command, "PASS", str(float(analyzer_data[2])))
                else:
                    signals_manager.step_execute_finish.emit(case_number, command, "NG", str(float(analyzer_data[2])))
        elif operator.eq("ReadColorCoordinates", command):
            from control_board.auto_test_m.ctr_card import ctr_card

            ctr_card.go_home()
            # 测试色坐标之前运动到中心点
            self.go_to_center()
            if data.__contains__(","):
                min_x_coordinates = float(data.split(",")[0])
                max_x_coordinates = float(data.split(",")[1])
                min_y_coordinates = float(data.split(",")[2])
                max_y_coordinates = float(data.split(",")[3])
                background_color = data.split(",")[4]
                adb_connect_device.switch_color(background_color)
                time.sleep(1)
                analyzer_data = color_analyzer_manager.read_xyLv_data()
                value = f"{float(analyzer_data[0])}, {float(analyzer_data[1])}"
                if (min_x_coordinates < float(analyzer_data[0]) < max_x_coordinates and
                        min_y_coordinates < float(analyzer_data[1]) < max_y_coordinates):
                    signals_manager.step_execute_finish.emit(case_number, command, "PASS", value)
                else:
                    signals_manager.step_execute_finish.emit(case_number, command, "NG", value)
                ctr_card.go_home()
        elif operator.eq("I2cChecksumDetect", command):
            if data.__contains__(","):
                i2c_data = data.split(",")[0]
                checksum = data.split(",")[1]
                adb_connect_device.i2c_checksum_detect(i2c_data, checksum)
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        elif operator.eq("CalibrateColorTemperature", command):
            color_temp_calibrate_manager.calibrate(case_number, command, float(data))
        elif operator.eq("TestColorTemperature", command):
            color_temp_test_manager.test(case_number, command, float(data))
        elif operator.eq("CalibrateBrightness", command):
            brightness_calibrate_manager.calibrate(case_number, command, float(data))
        elif operator.eq("TestBrightness", command):
            brightness_test_manager.test(case_number, command, float(data))
        elif operator.eq("SecurityLevel", command):
            from adb.util.canTools import can_tool
            project = project_manager.get_test_plan_project_number()
            position = data.split(",")[0]
            level = data.split(",")[1]
            send_id = data.split(",")[2]
            send_msg = data.split(",")[3]
            recv_id = data.split(",")[4]
            recv_msg = data.split(",")[5]
            channel = data.split(",")[6]

            self.switch_can_bus(int(channel))
            # 进入安全等级前置条件
            status, result = can_tool.run_security_level(project, position, level, send_id, send_msg, recv_id, recv_msg,
                                                         channel)

            signals_manager.step_execute_finish.emit(case_number, command, "PASS" if status else "NG", result)
        elif operator.eq("SecurityLevelEnter", command):
            from adb.util.canTools import can_tool
            project = project_manager.get_test_plan_project_number()
            position = data.split(",")[0]
            level = data.split(",")[1]
            send_id = data.split(",")[2]
            send_msg = data.split(",")[3]
            recv_id = data.split(",")[4]
            recv_msg = data.split(",")[5]

            channel = data.split(",")[6]
            self.switch_can_bus(int(channel))
            status, result = can_tool.enter_level(project, position, level, send_id, send_msg, recv_id, recv_msg,
                                                  channel)
            signals_manager.step_execute_finish.emit(case_number, command, "PASS" if status else "NG", result)
        elif operator.eq("DetectMcuLog", command):
            if data.__contains__(","):
                mark = data.split(",")[0]
                delay_time = int(data.split(",")[1])
                mcu_log_reader.delay_stop_time = delay_time
                log_manager.parse_mcu_log(case_number, command, mark)
        elif operator.eq("DetectSocLog", command):
            delay_time = int(data.split(",")[1])
            soc_log_reader.delay_stop_time = delay_time
            log_manager.parse_soc_log(case_number, command, data)
        elif operator.eq("DetectOsLog", command):
            delay_time = int(data.split(",")[1])
            logcat_reader.delay_stop_time = delay_time
            log_manager.parse_os_log(case_number, command, data)
        elif operator.eq("DetectVdsAppLog", command):
            delay_time = int(data.split(",")[1])
            logcat_reader.delay_stop_time = delay_time
            log_manager.parse_vds_app_log(case_number, command, data)
        elif operator.eq("SetCanMsgDelayTime", command):
            delay_time = data
            adb_connect_device.reset_can_msg_delay(float(delay_time))
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        elif operator.eq("CanProtocolStartApp", command):
            from adb.util.canoeAuto import get_canoe_app
            cfg_path = data.split(",")[0]
            tse_path = data.split(",")[1]
            canoe_app = get_canoe_app()
            if not canoe_app:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "CanoeApp is None")
                return

            canoe_app.run_test_modules(cfg_path, tse_path, case_number, command, )
            canoe_app.set_SysVar("pythonsys", "run_test", 1)
        elif operator.eq("CanProtocolStopApp", command):
            from adb.util.canoeAuto import canoe_app
            if canoe_app:
                canoe_app.Stop()
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "CanoeApp is None")
            # kill task CANoe64.exe
            os.system("taskkill /f /im CANoe64.exe")
        elif operator.eq("CanProtocolAddTestID", command):
            from adb.util.canoeAuto import get_canoe_app
            canoe_app = get_canoe_app()
            if canoe_app is None:
                return signals_manager.step_execute_finish.emit(case_number, command, "NG", "CanoeApp is None")

            module_id = data.split(",")[0]
            test_id = data.split(",")[1]
            while canoe_app.is_alive():
                value = canoe_app.get_SysVar("pythonsys", "run_test")
                if int(value) != 1:
                    logging.info(f"value is {value} waiting for canoe value to be set")
                    canoe_app.set_SysVar("pythonsys", "run_test", 1)
                    time.sleep(0.2)
                    continue
                logging.info(f"value is {value}")
                time.sleep(0.5)
                break
            canoe_app.AddTest(module_id, test_id)
            flag = False
            result = ""
            while canoe_app.is_alive():
                value = canoe_app.get_SysVar("pythonsys", "run_test")
                result = canoe_app.GetTestResult()
                logging.info(f"SysVar value is{value}, get CanProtocolAddTestID is {result}")
                if not result:
                    time.sleep(1)
                    continue
                if "NG" in result.upper():
                    flag = False
                else:
                    flag = True
                # signals_manager.step_execute_finish.emit(case_number, command, result, result)
                break
            signals_manager.current_canoe_asc_dir = f"{module_id.upper()}_{test_id}"
            if flag:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", result)
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG")
        elif operator.eq("ServoMotorPositioning", command):
            marking = data
            if marking.lower() == "center_point":
                try:
                    touch_card.display_center_point()
                    time.sleep(1)
                    frame = touch_card.get_camera_image()
                    points = touch_card.detact_point(frame)
                    logger.info(f"ServoMotorPositioning detect point is {points}")
                    # 计算坐标
                    M = Calibration["point1"]
                    physical_coords = pixel_to_physical(points, M=M)
                    touch_card.move_point(physical_coords[0][0], physical_coords[0][1])
                    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
                except Exception as e:
                    logging.error(traceback.format_exc())
                    signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG")
            elif marking.lower() == "9_point":
                try:
                    points = touch_card.get_9_points()
                    if len(points) != 9:
                        signals_manager.step_execute_finish.emit(case_number, command, "NG", json.dumps(points))
                        return
                    logger.info(f"detect point is {points}")
                    # 计算坐标
                    M = Calibration["point1"]
                    physical_coords = pixel_to_physical(points, M=M)
                    # 运行对角划线
                    touch_card.draw_line_9points(physical_coords)

                    signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
                except Exception:
                    logging.error(traceback.format_exc())
                    signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG")
        elif command.startswith("Detect") and command.endswith("Error"):
            params = data.split(',')
            error_mark = params[0]
            max_count = int(params[1]) if len(params) > 1 else 1
            delay_time = int(params[2]) if len(params) > 2 else 60
            if operator.eq("DetectMcuError", command):
                mcu_log_reader.append_error_mark(error_mark, max_count, delay_time)
            elif operator.eq("DetectSocError", command):
                soc_log_reader.append_error_mark(error_mark, max_count, delay_time)
            elif operator.eq("DetectOsError", command):
                logcat_reader.append_error_mark(error_mark, max_count, delay_time)
            elif operator.eq("DetectVdsAppError", command):
                logcat_reader.append_error_mark(error_mark, max_count, delay_time)
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        elif operator.eq("CheckCanMsgThreading", command):
            adb_connect_device.check_recv_msg_flag = True
            for thread in threading.enumerate():
                if thread.name == "check_recv_can_msg_t":
                    break
            else:
                threading.Thread(target=adb_connect_device.check_recv_can_msg, name="check_recv_can_msg_t").start()
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        elif operator.eq("PushExceptCanMsg", command):
            expect_id = data.split(",")[0]
            expect_content = data.split(",")[1]
            adb_connect_device.check_recv_msg_except = [expect_id, expect_content]
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        elif operator.eq("TMfingerClick", command):
            adb_connect_device.detect_click = True
            adb_connect_device.can_msg_delay = 0
            # tm_motor_client.click(-9, 0, 0.5)
            start = time.time()
            timeout = 259200
            while time.time() - start < timeout:
                if adb_connect_device.detect_click:
                    # signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
                    time.sleep(.1)
                    continue
                else:
                    time.sleep(.2)
                    signals_manager.step_execute_finish.emit(case_number, command, "NG", "NG")
                    return
            signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
        elif operator.eq("SwitchBackLight", command):
            if adb_connect_device.is_connect():
                adb_connect_device.switch_back_light(data)
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")
        elif operator.eq("ReadBackLightStatus", command):
            if adb_connect_device.is_connect():
                adb_connect_device.read_back_light_status()
                vds_detect_manager.set_expect_back_light_status(case_number, command, expect)
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")
        elif operator.eq("SwitchBistPattern", command):
            if adb_connect_device.is_connect():
                adb_connect_device.switch_bist_pattern(data)
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")
        elif operator.eq("ReadBistPatternStatus", command):
            if adb_connect_device.is_connect():
                adb_connect_device.read_bist_pattern_status()
                vds_detect_manager.set_expect_bist_pattern_status(case_number, command, expect)
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")
        elif operator.eq("DisplayReboot", command):
            if adb_connect_device.is_connect():
                adb_connect_device.display_reboot()
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")
        elif operator.eq("TconReset", command):
            if adb_connect_device.is_connect():
                adb_connect_device.tcon_reset()
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "adb forward 未连接")
        elif operator.eq("M410Test--GAMMA_CURVE", command):
            chl = data.split(",")[0]
            communicate_type = data.split(",")[1]
            can_parameter = data.split(",")[2]
            auto_optical_test_manager.gamma_range = [float(data.split(",")[3]), float(data.split(",")[4])]
            auto_optical_test_manager.set_test_function('Gamma曲线测试', chl, communicate_type, can_parameter)
            auto_optical_test_manager.case_number = case_number
            auto_optical_test_manager.command = command
            auto_optical_test_manager.start()
        elif operator.eq("M410Test--BRIGHTNESS_CURVE", command):
            chl = data.split(",")[0]
            communicate_type = data.split(",")[1]
            can_parameter = data.split(",")[2]
            try:
                auto_optical_test_manager.brightness_range = [float(data.split(",")[3]), float(data.split(",")[4])]
                auto_optical_test_manager.bright_smoothness = float(data.split(",")[5])
            except Exception:
                auto_optical_test_manager.brightness_range = [0,1000]
                auto_optical_test_manager.bright_smoothness = 0.3
            auto_optical_test_manager.set_test_function('亮度曲线测试', chl, communicate_type, can_parameter)
            auto_optical_test_manager.case_number = case_number
            auto_optical_test_manager.command = command
            auto_optical_test_manager.start()
        elif operator.eq("M410Test--CONTRAST_RATIO", command):
            chl = data.split(",")[0]
            communicate_type = data.split(",")[1]
            can_parameter = data.split(",")[2]
            auto_optical_test_manager.contrast_ratio_min = float(data.split(",")[3])
            auto_optical_test_manager.set_test_function('对比度测试', chl, communicate_type, can_parameter)
            auto_optical_test_manager.case_number = case_number
            auto_optical_test_manager.command = command
            auto_optical_test_manager.start()
        elif operator.eq("M410Test--COLOUR_GAMUT", command):
            chl = data.split(",")[0]
            communicate_type = data.split(",")[1]
            can_parameter = data.split(",")[2]
            auto_optical_test_manager.color_gamut = float(data.split(",")[3])
            auto_optical_test_manager.set_test_function('色域测试', chl, communicate_type, can_parameter)
            auto_optical_test_manager.case_number = case_number
            auto_optical_test_manager.command = command
            auto_optical_test_manager.start()
        elif operator.eq("M410Test--UNIFORMITY", command):
            chl = data.split(",")[0]
            communicate_type = data.split(",")[1]
            can_parameter = data.split(",")[2]
            auto_optical_test_manager.uniformity_min = float(data.split(",")[3])
            color = data.split(",")[4]
            hex_color = color.lstrip('#')
            # 转换成 RGB 数值
            rgb = tuple(int(hex_color[i:i + 2], 16) for i in (0, 2, 4))
            auto_optical_test_manager.uniformity_bg = rgb
            auto_optical_test_manager.set_test_function('均一性测试', chl, communicate_type, can_parameter)
            auto_optical_test_manager.case_number = case_number
            auto_optical_test_manager.command = command
            auto_optical_test_manager.start()
        elif operator.eq("M410--9PointsColor", command):
            chl = data.split(",")[0]
            communicate_type = data.split(",")[1]
            can_parameter = data.split(",")[2]
            color = data.split(",")[3]
            hex_color = color.lstrip('#')
            # 转换成 RGB 数值
            rgb = tuple(int(hex_color[i:i + 2], 16) for i in (0, 2, 4))
            auto_optical_test_manager.color_bg = rgb
            auto_optical_test_manager.color_x_range = [float(data.split(",")[4]), float(data.split(",")[5])]
            auto_optical_test_manager.color_y_range = [float(data.split(",")[6]), float(data.split(",")[7])]
            auto_optical_test_manager.set_test_function('9点色度', chl, communicate_type, can_parameter)
            auto_optical_test_manager.case_number = case_number
            auto_optical_test_manager.command = command
            auto_optical_test_manager.start()
        elif operator.eq("SetRelayStatus", command):
            relay_number = int(data.split(",")[0])
            value = int(data.split(",")[1])
            relay_status = True if value == 0 else False
            ret = relay_client.set_relay_status(relay_number, relay_status)
            if ret is None:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "设置继电器开关失败")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "设置继电器开关成功")
        elif operator.eq("LightSensorAutoTest", command):
            light_mode = True if "range" in data.split(",")[0].lower() else False
            time_delay = int(data.split(",")[1])
            color_temp_light_sensor_auto.command = command
            color_temp_light_sensor_auto.case_number = case_number
            color_temp_light_sensor_auto.set_parameter(light_mode, time_delay)
            color_temp_light_sensor_auto.start_light_sensor_curve()
        elif operator.eq("EnvTemperatureTest", command):
            device = data.split(",")[0]
            frequency = data.split(",")[1]
            total_test_time = data.split(",")[2]
            channels = data.split(",")[3].split("|")
            env_test_auto.command = command
            env_test_auto.case_number = case_number
            env_test_auto.set_parameter(device, frequency, total_test_time, channels)
            env_test_auto.start()
        elif operator.eq("ExecuteLoopPowerOnOff", command):
            power_on_volt = float(data.split(",")[0])
            power_on_delay_min = float(data.split(",")[1])
            power_on_delay_max = float(data.split(",")[2])
            power_off_volt = float(data.split(",")[3])
            power_off_delay_min = float(data.split(",")[4])
            power_off_delay_max = float(data.split(",")[5])
            execute_times = int(data.split(",")[6])
            try:
                channel = int(data.split(",")[7])
            except Exception as e:
                logger.error(f"execute_customize_cmd exception: {str(e.args)}")
                channel = 1
            loop_power_on_off_manager.execute_loop(case_number, command, power_on_volt, power_on_delay_min,
                                                   power_on_delay_max, power_off_volt, power_off_delay_min,
                                                   power_off_delay_max, execute_times, channel)
        elif operator.eq("M410--9PointsBrightness", command):
            chl = data.split(",")[0]
            communicate_type = data.split(",")[1]
            can_parameter = data.split(",")[2]
            color = data.split(",")[3]
            hex_color = color.lstrip('#')
            # 转换成 RGB 数值
            rgb = tuple(int(hex_color[i:i + 2], 16) for i in (0, 2, 4))
            auto_optical_test_manager.color_bg = rgb
            brightness = int(data.split(",")[4])

            adb_connect_device.switch_brightness(brightness=f"1:{brightness}")
            time.sleep(1)
            auto_optical_test_manager.brightness_range = [float(data.split(",")[5]), float(data.split(",")[6])]
            auto_optical_test_manager.set_test_function('9点亮度', chl, communicate_type, can_parameter)
            auto_optical_test_manager.case_number = case_number
            auto_optical_test_manager.command = command
            auto_optical_test_manager.start()
        elif operator.eq("M410--SinglePointBrightness", command):
            chl = data.split(",")[0]
            communicate_type = data.split(",")[1]
            can_parameter = data.split(",")[2]
            color = data.split(",")[3]
            hex_color = color.lstrip('#')
            # 转换成 RGB 数值
            rgb = tuple(int(hex_color[i:i + 2], 16) for i in (0, 2, 4))
            auto_optical_test_manager.color_bg = rgb
            brightness = int(data.split(",")[4])

            adb_connect_device.switch_brightness(brightness=f"1:{brightness}")
            time.sleep(1)
            auto_optical_test_manager.brightness_range = [float(data.split(",")[5]), float(data.split(",")[6])]
            auto_optical_test_manager.set_test_function('单点亮度', chl, communicate_type, can_parameter)
            auto_optical_test_manager.case_number = case_number
            auto_optical_test_manager.command = command
            auto_optical_test_manager.start()
        elif operator.eq("M410--FlickerTest", command):
            freq_resolution = data.split(",")[0]
            sampling_frequency = data.split(",")[1]
            auto_optical_test_manager.freq_resolution = float(freq_resolution)
            auto_optical_test_manager.sampling_frequency = int(sampling_frequency)
            auto_optical_test_manager.set_test_function('FlickerTest', freq_resolution, sampling_frequency)
            auto_optical_test_manager.case_number = case_number
            auto_optical_test_manager.command = command
            auto_optical_test_manager.start()
        elif operator.eq("M410--ResponseTimeTest", command):
            auto_optical_test_manager.flicker_method = int(data.split(",")[0])
            auto_optical_test_manager.max_time = float(data.split(",")[1])
            auto_optical_test_manager.set_test_function('ResponseTimeTest')
            auto_optical_test_manager.case_number = case_number
            auto_optical_test_manager.command = command
            auto_optical_test_manager.start()
        elif operator.eq("DetectAngle", command):
            angle = int(data.split(",")[0])
            deviation = int(data.split(",")[1])
            open_time = int(data.split(",")[2])
            # 检查server服务
            for proc in psutil.process_iter(['pid', 'name']):
                # 如果进程名匹配
                if proc.info['name'] == "orbbec_server.exe":
                    break
            else:
                # 开启进程
                path = os.path.join(os.getcwd(), "orbbec", "orbbec_server.exe")
                threading.Thread(target=execute_comand, args=(path,)).start()
                logger.info("start orbbec_server...")
                time.sleep(.2)
            # 遍历所有的线程
            for thread in threading.enumerate():
                name = thread.name
                if thread.is_alive() and name == "elevation_angle_test":
                    break
            else:
                threading.Thread(target=elevation_angle_tool.detect_angle,  args=(True,),name= "elevation_angle_test").start()
            time.sleep(1)
            adb_connect_device.adb_forward_send_data(action="open_ceiling")
            time.sleep(open_time)
            detect_angle = elevation_angle_tool.real_time_angle
            if angle - deviation <= detect_angle <= angle + deviation:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", f"{detect_angle}")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", f"{detect_angle}°")
            adb_connect_device.adb_forward_send_data(action="close_ceiling")
            time.sleep(open_time)
            elevation_angle_tool.stop_detect_angle = True

        elif operator.eq("recordCanMsg", command):
            delay_time = int(data.split(",")[0])
            try:
                adb_connect_device.record_can_recv_msg(delay_time)
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "")
            except Exception as e:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", str(e.args))
        elif operator.eq("SerialProtocolTest", command):
            send_data = data.split(",")[0]
            receive_data = data.split(",")[1]
            adb_connect_device.send_serial_protocol_data(send_data)
            if operator.eq("NA", receive_data):
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "串口协议数据发送成功")
            else:
                # 比对发送串口协议数据之后，接收到的数据是否和期望接收的串口协议数据为包含关系
                vds_detect_manager.set_expect_serial_protocol_data(case_number, command, receive_data)
        elif operator.eq("RecordImageAlgorithm", command):
            header = ["时间", "算法", "路径", "状态", "产品型号"]

            try:
                alg_recorder.set_info(case_number, command)
                alg_recorder.check_server()  # 检测开启server
                is_conn = alg_recorder.connect()
                if not is_conn:
                    signals_manager.step_execute_finish.emit(case_number, command, "NG", f"{traceback.format_exc()}")
                    return
                # while True:
                alg_name = data.split(",")[0]
                path = data.split(",")[1]
                product_type = data.split(",")[2]
                alg_recorder.send(f"init:{alg_name};{path};{product_type}")
                time.sleep(1)
                result = alg_recorder.recv()

                item = {
                    "test_function": "RecordImageAlgorithm", "header": header, "content": result,
                    # "gamut": "", "ratio": "",
                }
                alg_recorder.test_result = item
                logger.info(f"RecordImageAlgorithm result:{item}")
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", f"{traceback.format_exc()}")
            except Exception:
                logger.error(traceback.format_exc())
                signals_manager.step_execute_finish.emit(case_number, command, "NG", f"{traceback.format_exc()}")

        elif operator.eq("SwitchColorAnalyzerChannel", command):
            status = color_analyzer_manager.switch_channel(data)
            if status:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "通道切换成功")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "通道切换失败：色彩分析仪未连接")
        elif operator.eq("CheckerboardClick", command):
            send_data = data.split(",")[0]
            receive_data = data.split(",")[1]
            vds_detect_manager.case_number = case_number
            vds_detect_manager.command = command
            points = touch_card.get_checkerboard_coordinate()
            M = Calibration["point3"]
            physical_coords = pixel_to_physical(points, M=M)
            physical_coords = sorted(physical_coords, key=lambda x: x[0])
            for coord in physical_coords:  # 获取数据并添加到列表中
                touch_card.move_point(coord[0], coord[1], z=23567.0, Z2_value=-59508.0, R_value=-12659.0, )
                # 等待响应坐标信息
        elif operator.eq("LinSender", command):       
            try:     
                device = int(data.split(",")[0].strip())
                pid = data.split(",")[1].strip()
                lin_data = data.split(",")[2].strip()
                interval = float(data.split(",")[3].strip())
            except ValueError:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "输入数据不规范")
                return

            if device == 1:
                if not lin_sender.is_connected:
                    signals_manager.step_execute_finish.emit(case_number, command, "NG", "设备未连接")
                    return
                if interval > 0:
                    #使用周期性发送
                    res, msg = lin_sender.start_periodic_send(pid, lin_data, interval)
                    signals_manager.step_execute_finish.emit(case_number, command, "PASS" if res else "NG", msg)
                else:
                    res, msg = lin_sender.send_message(pid, lin_data)
                    signals_manager.step_execute_finish.emit(case_number, command, "PASS" if res else "NG", msg)
            elif device == 2:
                if adb_connect_device.tsmaster_lin_bus is None:
                    signals_manager.step_execute_finish.emit(case_number, command, "NG", "设备未连接")
                    return
                if interval > 0:
                    #使用周期性发送
                    res, msg = adb_connect_device.tsmaster_lin_bus.start_periodic_send(pid, lin_data, interval)
                    signals_manager.step_execute_finish.emit(case_number, command, "PASS" if res else "NG", msg)
                else:
                    res, msg = adb_connect_device.tsmaster_lin_bus.send_message(pid + " " + lin_data)
                    signals_manager.step_execute_finish.emit(case_number, command, "PASS" if (res == 0) else "NG", msg)
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", "设备选择错误")
        elif operator.eq("LinStopSender", command):
            pid = data.split(",")[0]
            try:
                if pid =='-':
                    lin_sender.stop_all_periodic_send()
                    if adb_connect_device.tsmaster_lin_bus is not None:
                        adb_connect_device.tsmaster_lin_bus.stop_all_periodic_send()
                else:
                    if pid in lin_sender.periodic_threads:
                        lin_sender.stop_periodic_send(pid)
                    if adb_connect_device.tsmaster_lin_bus is not None:
                        if pid in adb_connect_device.tsmaster_lin_bus.periodic_threads:
                            adb_connect_device.tsmaster_lin_bus.stop_periodic_send(pid)
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            except Exception as e:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", str(e.args))
        elif operator.eq("StartProcessMonitor", command):
            try:
                adb_connect_device.start_process_monitor()
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            except Exception as e:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", str(e.args))
        elif operator.eq("StopProcessMonitor", command):
            try:
                adb_connect_device.stop_process_monitor()
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "")
            except Exception as e:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", str(e.args))
        elif operator.eq("SetOscilloscopeDelayStop", command):
            oscilloscope_manager.set_delay_stop_time(float(data))
            signals_manager.step_execute_finish.emit(case_number, command, "PASS",
                                                     f"示波器设置延时停止时长：{float(data)}秒")
        elif operator.eq("MonitorMultiChannelWorkCurrent", command):
            work_current_monitor_manager.handle_monitor_multi_channel_work_current(case_number, command, data)
        elif operator.eq("MarkDtcNormalMsg", command):
            dtc_detect_manager.handle_dtc_detect(case_number, command, data)
        elif operator.eq("WhiteBalanceTest", command):
            #TODO
            auto_optical_test_manager.set_test_function('white_balance_test')
            auto_optical_test_manager.case_number = case_number
            auto_optical_test_manager.command = command
            auto_optical_test_manager.start()


    @staticmethod
    def set_delay_time(case_number, command, time_size):
        signals_manager.step_execute_finish.emit(case_number, command, "PASS", f"设置延时：{time_size}秒")

    def append_step(self, step):
        self.steps.append(step)
        command = step.get("command", "")
        params = step.get("params", "")
        status = step.get("status", "")
        expect = step.get("expect", "")
        step_index = step.get("step_index", 0)
        self.step_status.update({step_index: {"command": command, "params": params, "status": status,
                                              "expect": expect}})
        logger.debug(f"append_step step_index={step_index}, step_status={self.step_status}")

    def clear_steps(self):
        self.steps.clear()
        logger.info("clear_steps steps={}".format(len(self.steps)))
        # 取出队列中未完成的测试步骤清空队列
        while not self._step_queue.empty():
            step = self._step_queue.get()
            logger.info("clear_steps step_type={}".format(step.get("step_type")))

    def set_vision_revert(self, case_number, state):
        logger.info(f"set_vision_revert case_number={case_number}, state={state}")
        self.vision_reverts.update({case_number: state})

    def set_vision_algorithm(self, case_number, state):
        logger.info(f"set_vision_algorithm case_number={case_number}, state={state}")
        self.vision_algorithms.update({case_number: state})

    def set_execute_mode(self, case_number, execute_mode):
        logger.info(f"set_execute_mode case_number={case_number}, execute_mode={execute_mode}")
        self.execute_modes.update({case_number: execute_mode})

    @staticmethod
    def switch_vision_collect(status):
        logger.info(f"switch_vision_collect status={status}")
        visual_detect_signal.start_collect.emit(status)

    def set_step_status(self, status, interrupt):
        logger.info(f"set_step_status status={status}, interrupt={interrupt}")
        self.status = status
        if not status:
            if not interrupt:
                # 异常停止不清空CAN周期性消息
                adb_connect_device.clear_cycle_can_msg()

            # 停止步骤状态检测定时器
            if self.check_step_timeout_timer is not None:
                self.check_step_timeout_timer.cancel()

    def functionality_stop_record(self, file_date, now_count, all_count, threshold_flicker, threshold_grainy,
                                  threshold_black, test_mode):
        logger.info(f"functionality_stop_record file_date={file_date}, now_count={now_count}, all_count={all_count}, "
                    f"threshold_flicker={threshold_flicker}, threshold_grainy={threshold_grainy}, "
                    f"threshold_black={threshold_black}, test_mode={test_mode}")
        frequency_test.start_record_index = False
        frequency_test.all_count = all_count
        threading.Timer(interval=3, function=self.functionality_video_test,
                        args=(frequency_test.record_video_path, file_date, now_count, all_count, threshold_flicker,
                              threshold_grainy, threshold_black, test_mode, camera_config.get_base_path())).start()

    @staticmethod
    def functionality_video_test(file_path, file_date, now_count, all_count, threshold_flicker, threshold_grainy,
                                 threshold_black, test_mode, base_path):
        if vision_manager.vision_calibrate_dialog is not None:
            threading.Thread(target=frequency_test.start_functionality_video_test,
                             args=([file_path, ], vision_manager.vision_calibrate_dialog, file_date, now_count,
                                   all_count, threshold_flicker, threshold_grainy, threshold_black, test_mode,
                                   base_path)).start()

    @staticmethod
    def go_to_center():
        logger.info(f"go_to_center")
        touch_card.display_center_point()
        points = []
        for i in range(20):
            time.sleep(3)
            logger.info(f"enter go_to_center {i}")
            frame = touch_card.get_camera_image()
            points = touch_card.detact_point(frame)

            if len(points) == 0:
                continue
            else:
                break
        logger.info(f"go_to_center detect point is {points}")
        # 计算坐标
        physical_coordinates = pixel_to_physical(points, M=Calibration["color_analyser"])
        print("坐标:", physical_coordinates[0])
        touch_card.move_point(physical_coordinates[0][0], physical_coordinates[0][1], z=23567.0, Z2_value=-59508.0,
                              R_value=-12659.0)


step_manager: StepManager = StepManager()
step_manager.start_step_center()
