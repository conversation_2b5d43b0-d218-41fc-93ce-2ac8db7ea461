import os
import json

BASE_PATH = os.path.dirname(os.path.abspath(__file__))
CONF_PATH = os.path.join(BASE_PATH, "configs", "config.json")

with open(CONF_PATH, "rb") as f:
    CONF = json.load(f)

PARITY_MAP = {
    "PARITY_NONE": "N",
    "PARITY_EVEN": "E",
    "PARITY_ODD": "O",
    "PARITY_MARK": "M",
    "PARITY_SPACE": "S"
}

PARITY_MAP2 = {
    "N": "PARITY_NONE",
    "E": "PARITY_EVEN",
    "O": "PARITY_ODD",
    "M": "PARITY_MARK",
    "S": "PARITY_SPACE"
}

STOPBITS_MAP = {
    "1": 1,
    "1.5": 1.5,
    "2": 2
}

STOPBITS_MAP2 = {
    1: "1",
    1.5: "1.5",
    2: "2"
}


def update_conf(conf):
    CONF.update(conf)

    with open(CONF_PATH, "wt") as f:
        json.dump(CONF, f, indent=True)