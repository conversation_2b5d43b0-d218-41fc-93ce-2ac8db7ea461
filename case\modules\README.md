# 模块化命令处理系统

## 概述

这是一个全新的模块化命令处理系统，实现了按需加载不同模块的资源，避免了在启动时加载所有资源，提高了系统的启动速度和内存使用效率。

## 设计理念

### 🎯 核心优势

1. **按需加载**: 只有在需要执行特定命令时，才会加载对应模块的资源
2. **模块分离**: 不同功能的命令被分组到不同的模块中，便于维护
3. **懒加载**: 资源只在第一次使用时加载，避免不必要的初始化开销
4. **可扩展性**: 新增命令只需要添加到对应模块，或创建新模块

### 🏗️ 架构设计

```
ModuleManager (模块管理器)
├── AdbModule (ADB模块)
├── PowerModule (电源模块)  
├── VisionModule (视觉模块)
├── TouchModule (触摸模块)
├── OpticalModule (光学模块)
├── SystemModule (系统模块)
├── CanModule (CAN模块)
├── LogModule (日志模块)
└── MiscModule (杂项模块)
```

## 模块分类

### 📱 ADB模块 (`adb_module.py`)
处理与Android设备通信相关的命令：
- `SendCANMsg`, `SendCycleCANMsg`
- `SendLINMsg`, `SendCycleLINMsg`
- `SwitchColor`, `SwitchBrightness`
- `SwitchSleep`, `SwitchWakeup`
- `TpcmTest`, `SwitchBackLight`
- 等等...

### ⚡ 电源模块 (`power_module.py`)
处理电源控制相关的命令：
- `SwitchPower`, `SwitchVoltage`
- `SwitchStepVoltage`
- `ReadWorkCurrent`, `ReadPeriodWorkCurrent`
- `ReadWorkVoltage`
- `MonitorMultiChannelWorkCurrent`
- 等等...

### 👁️ 视觉模块 (`vision_module.py`)
处理视觉检测相关的命令：
- `StartRecord`, `StopRecord`
- `StartCollectVisionBrightness`
- `DetectVisionBrightness`
- `RecordImageAlgorithm`

### 🖱️ 触摸模块 (`touch_module.py`)
处理触摸测试相关的命令：
- `TouchStillTest`, `TouchMarkingTest`
- `TouchPointsDetect`
- `TouchRespTimes`, `TouchRespTime`
- `TouchReportRate`
- 等等...

### 🔬 光学模块 (`optical_module.py`)
处理光学测试相关的命令：
- `PhoticsGammaCurve`, `PhoticsBrightnessCurve`
- `PhoticsContrastRatio`, `PhoticsColourGamut`
- `ReadNitBrightness`, `ReadColorCoordinates`
- `M410Test--*` 系列命令
- 等等...

### 🖥️ 系统模块 (`system_module.py`)
处理系统级操作相关的命令：
- `SetDelayTime`, `SetRandomDelayTime`
- `UpdateFwByRandom`, `UpdateFwByStep`
- `ExecuteBat`, `ExecuteBlockBat`
- `SetRelayStatus`, `EnvTemperatureTest`
- 等等...

## 使用方法

### 1. 创建新模块

```python
from case.module_manager import ModuleHandler
from typing import Dict, Any, Set

class MyModule(ModuleHandler):
    def __init__(self):
        super().__init__()
        # 延迟加载的资源
        self.my_resource = None
    
    def get_supported_commands(self) -> Set[str]:
        return {'MyCommand1', 'MyCommand2'}
    
    def _load_resources(self):
        # 只在需要时加载资源
        from my_package import my_resource
        self.my_resource = my_resource
    
    def execute_command(self, command: str, step: Dict[str, Any]):
        # 处理命令逻辑
        pass
```

### 2. 注册新模块

在 `module_manager.py` 的 `_module_configs` 中添加：

```python
'my_module': {
    'class_path': 'case.modules.my_module.MyModule',
    'commands': {'MyCommand1', 'MyCommand2'}
}
```

### 3. 执行命令

系统会自动根据命令名称找到对应的模块并执行：

```python
# 在 StepManager 中
module_manager.execute_command(command, step)
```

## 性能优势

### 🚀 启动速度提升
- **原系统**: 启动时加载所有模块资源
- **新系统**: 启动时只加载模块管理器，资源按需加载

### 💾 内存使用优化
- **原系统**: 所有资源常驻内存
- **新系统**: 只有使用过的模块资源才会加载到内存

### 🔧 维护性提升
- **模块化**: 每个模块独立维护，互不影响
- **可扩展**: 新增功能只需要添加新模块
- **可测试**: 每个模块可以独立测试

## 兼容性

系统保持了向后兼容性：
- 原有的命令处理逻辑作为 `_execute_legacy_command` 保留
- 如果新模块中没有找到命令处理器，会自动回退到原有逻辑
- 现有代码无需修改即可使用新系统

## 扩展指南

### 添加新命令到现有模块

1. 在对应模块的 `get_supported_commands()` 中添加命令名
2. 在 `execute_command()` 中添加命令处理逻辑
3. 在 `module_manager.py` 的配置中添加命令映射

### 创建新模块

1. 继承 `ModuleHandler` 基类
2. 实现必要的抽象方法
3. 在 `module_manager.py` 中注册新模块
4. 添加命令到模块的映射关系

## 最佳实践

1. **资源懒加载**: 在 `_load_resources()` 中加载资源，不要在 `__init__` 中加载
2. **异常处理**: 在命令处理中添加适当的异常处理
3. **日志记录**: 使用统一的日志格式记录操作
4. **模块分组**: 按功能相关性将命令分组到合适的模块中
5. **文档更新**: 添加新命令时更新相应的文档
