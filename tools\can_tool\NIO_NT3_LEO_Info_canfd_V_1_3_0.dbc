VERSION "v00.03.00_00"


NS_ :
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER_
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: AMP CNSL_I FI_R_R HUD IC ICS NOMI SWC ZONE_FTM

        
BO_ 836 AMP_01: 64 AMP
 SG_ AMP_SetEPMode_Sts : 31|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ AMP_SetEPSwitch_Sts : 103|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ AMP_SetKROKMicType_Sts : 95|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ AMP_SetKROKSwitch_Sts : 94|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ AMP_SetKROKVol_Sts : 28|5@0+ (1,0) [0|30] "dB" ZONE_FTM
 SG_ AMP_SetMediaFrontMute_Sts : 118|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ AMP_SetMediaPath_Sts : 55|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ AMP_SetMediaRearMute_Sts : 29|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ AMP_SetMediaSwitch_Sts : 87|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ AMP_SetMediaType_Sts : 63|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ AMP_SetMediaVol_Sts : 132|5@0+ (1,0) [0|30] "dB" ZONE_FTM
 SG_ AMP_SetMute_Sts : 86|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ AMP_SetNomiSwitch_Sts : 70|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ AMP_SetNomiType_Sts : 47|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ AMP_SetNomiVol_Sts : 244|5@0+ (1,0) [0|30] "dB" ZONE_FTM
 SG_ AMP_SetPEQFrqPoint_10_Sts : 117|6@0+ (1,0) [0|63] "" ZONE_FTM
 SG_ AMP_SetPEQFrqPoint_1_Sts : 45|6@0+ (1,0) [0|63] "" ZONE_FTM
 SG_ AMP_SetPEQFrqPoint_2_Sts : 53|6@0+ (1,0) [0|63] "" ZONE_FTM
 SG_ AMP_SetPEQFrqPoint_3_Sts : 61|6@0+ (1,0) [0|63] "" ZONE_FTM
 SG_ AMP_SetPEQFrqPoint_4_Sts : 69|6@0+ (1,0) [0|63] "" ZONE_FTM
 SG_ AMP_SetPEQFrqPoint_5_Sts : 77|6@0+ (1,0) [0|63] "" ZONE_FTM
 SG_ AMP_SetPEQFrqPoint_6_Sts : 85|6@0+ (1,0) [0|63] "" ZONE_FTM
 SG_ AMP_SetPEQFrqPoint_7_Sts : 93|6@0+ (1,0) [0|63] "" ZONE_FTM
 SG_ AMP_SetPEQFrqPoint_8_Sts : 101|6@0+ (1,0) [0|63] "" ZONE_FTM
 SG_ AMP_SetPEQFrqPoint_9_Sts : 109|6@0+ (1,0) [0|63] "" ZONE_FTM
 SG_ AMP_SetPEQGain_10_Sts : 39|4@0+ (1,-6) [-6|6] "" ZONE_FTM
 SG_ AMP_SetPEQGain_1_Sts : 35|4@0+ (1,-6) [-6|6] "" ZONE_FTM
 SG_ AMP_SetPEQGain_2_Sts : 127|4@0+ (1,-6) [-6|6] "" ZONE_FTM
 SG_ AMP_SetPEQGain_3_Sts : 123|4@0+ (1,-6) [-6|6] "" ZONE_FTM
 SG_ AMP_SetPEQGain_4_Sts : 143|4@0+ (1,-6) [-6|6] "" ZONE_FTM
 SG_ AMP_SetPEQGain_5_Sts : 139|4@0+ (1,-6) [-6|6] "" ZONE_FTM
 SG_ AMP_SetPEQGain_6_Sts : 151|4@0+ (1,-6) [-6|6] "" ZONE_FTM
 SG_ AMP_SetPEQGain_7_Sts : 147|4@0+ (1,-6) [-6|6] "" ZONE_FTM
 SG_ AMP_SetPEQGain_8_Sts : 159|4@0+ (1,-6) [-6|6] "" ZONE_FTM
 SG_ AMP_SetPEQGain_9_Sts : 155|4@0+ (1,-6) [-6|6] "" ZONE_FTM
 SG_ AMP_SetPEQSwitch_Sts : 111|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ AMP_SetPEQValue_10_Sts : 236|5@0+ (0.2,0.2) [0.2|4] "" ZONE_FTM
 SG_ AMP_SetPEQValue_1_Sts : 164|5@0+ (0.2,0.2) [0.2|4] "" ZONE_FTM
 SG_ AMP_SetPEQValue_2_Sts : 172|5@0+ (0.2,0.2) [0.2|4] "" ZONE_FTM
 SG_ AMP_SetPEQValue_3_Sts : 180|5@0+ (0.2,0.2) [0.2|4] "" ZONE_FTM
 SG_ AMP_SetPEQValue_4_Sts : 188|5@0+ (0.2,0.2) [0.2|4] "" ZONE_FTM
 SG_ AMP_SetPEQValue_5_Sts : 196|5@0+ (0.2,0.2) [0.2|4] "" ZONE_FTM
 SG_ AMP_SetPEQValue_6_Sts : 204|5@0+ (0.2,0.2) [0.2|4] "" ZONE_FTM
 SG_ AMP_SetPEQValue_7_Sts : 212|5@0+ (0.2,0.2) [0.2|4] "" ZONE_FTM
 SG_ AMP_SetPEQValue_8_Sts : 220|5@0+ (0.2,0.2) [0.2|4] "" ZONE_FTM
 SG_ AMP_SetPEQValue_9_Sts : 228|5@0+ (0.2,0.2) [0.2|4] "" ZONE_FTM
 SG_ AMP_SetPhonePos_Sts : 79|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ AMP_SetPhoneSwitch_Sts : 71|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ AMP_SetRNCSwitch_Sts : 110|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ AMP_SetSoundFiledSwitch_Sts : 102|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ AMP_SetSoundFiled_Sts : 135|3@0+ (1,0) [0|7] "" ZONE_FTM
 SG_ AMP_SetWorkMode_Sts : 251|4@0+ (1,0) [0|7] "" ZONE_FTM
        
BO_ 917 AMP_02: 64 AMP
 SG_ A2B1_TDM_Lost_Times : 95|8@0+ (1,0) [0|255] "" ZONE_FTM
 SG_ A2B1_TDM_State : 111|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ A2B2_TDM_Lost_Times : 103|8@0+ (1,0) [0|255] "" ZONE_FTM
 SG_ A2B2_TDM_State : 110|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ AMP_Dsp1HeartBeat : 247|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ AMP_Dsp2HeartBeat : 31|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ AMP_Dsp3HeartBeat : 63|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ Audio_status : 109|4@0+ (1,0) [0|15] "" ZONE_FTM
 SG_ Chip_10_comm_status : 232|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ Chip_10_status : 227|4@0+ (1,0) [0|15] "" ZONE_FTM
 SG_ Chip_1_comm_status : 105|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ Chip_1_status : 199|4@0+ (1,0) [0|15] "" ZONE_FTM
 SG_ Chip_2_comm_status : 104|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ Chip_2_status : 195|4@0+ (1,0) [0|15] "" ZONE_FTM
 SG_ Chip_3_comm_status : 239|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ Chip_3_status : 207|4@0+ (1,0) [0|15] "" ZONE_FTM
 SG_ Chip_4_comm_status : 238|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ Chip_4_status : 203|4@0+ (1,0) [0|15] "" ZONE_FTM
 SG_ Chip_5_comm_status : 237|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ Chip_5_status : 215|4@0+ (1,0) [0|15] "" ZONE_FTM
 SG_ Chip_6_comm_status : 236|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ Chip_6_status : 211|4@0+ (1,0) [0|15] "" ZONE_FTM
 SG_ Chip_7_comm_status : 235|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ Chip_7_status : 223|4@0+ (1,0) [0|15] "" ZONE_FTM
 SG_ Chip_8_comm_status : 234|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ Chip_8_status : 219|4@0+ (1,0) [0|15] "" ZONE_FTM
 SG_ Chip_9_comm_status : 233|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ Chip_9_status : 231|4@0+ (1,0) [0|15] "" ZONE_FTM
 SG_ DAP_Ver : 119|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DSP1_Ver : 151|8@0+ (1,0) [0|255] "" ZONE_FTM
 SG_ DSP2_Ver : 159|8@0+ (1,0) [0|255] "" ZONE_FTM
 SG_ DSP3_Ver : 167|8@0+ (1,0) [0|255] "" ZONE_FTM
 SG_ MCU_App_Ver : 175|8@0+ (1,0) [0|255] "" ZONE_FTM
 SG_ MCU_AutoSar_Ver : 183|8@0+ (1,0) [0|255] "" ZONE_FTM
 SG_ MCU_Mcal_Ver : 191|8@0+ (1,0) [0|255] "" ZONE_FTM
        
BO_ 918 AMP_03: 64 AMP
 SG_ DSP1_STS : 71|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DSP1_TEMP : 23|16@0+ (1,0) [0|65535] "" ZONE_FTM
 SG_ DSP2_STS : 103|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DSP2_TEMP : 39|16@0+ (1,0) [0|65535] "" ZONE_FTM
 SG_ DSP3_STS : 135|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DSP3_TEMP : 55|16@0+ (1,0) [0|65535] "" ZONE_FTM
 SG_ InputVolt : 164|5@0+ (1,0) [0|31] "V" ZONE_FTM
 SG_ NCU_TEMP : 7|16@0+ (1,0) [0|65535] "" ZONE_FTM
        
BO_ 1200 AMP_DEVLP_01: 8 AMP
 SG_ AMP_Tx_Sig01 : 7|8@0+ (1,0) [0|255] "" ZONE_FTM
 SG_ AMP_Tx_Sig02 : 15|8@0+ (1,0) [0|255] "" ZONE_FTM
 SG_ AMP_Tx_Sig03 : 23|8@0+ (1,0) [0|255] "" ZONE_FTM
 SG_ AMP_Tx_Sig04 : 31|8@0+ (1,0) [0|255] "" ZONE_FTM
 SG_ AMP_Tx_Sig05 : 39|8@0+ (1,0) [0|255] "" ZONE_FTM
 SG_ AMP_Tx_Sig06 : 47|8@0+ (1,0) [0|255] "" ZONE_FTM
 SG_ AMP_Tx_Sig07 : 55|8@0+ (1,0) [0|255] "" ZONE_FTM
 SG_ AMP_Tx_Sig08 : 63|8@0+ (1,0) [0|255] "" ZONE_FTM
        
BO_ 1335 NM_AMP: 8 AMP
 SG_ ActvWakeup_AMP : 12|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ NetMngtCoorn_AMP : 10|2@0+ (1,0) [0|3] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ NetMngtCoorrSleepRdy_AMP : 11|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN10_AMP : 25|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN11_AMP : 26|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN12_AMP : 27|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN13_AMP : 28|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN14_AMP : 29|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN15_AMP : 30|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN16_AMP : 31|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN17_AMP : 32|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN18_AMP : 33|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN19_AMP : 34|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN1_AMP : 16|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN20_AMP : 35|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN21_AMP : 36|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN22_AMP : 37|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN23_AMP : 38|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN24_AMP : 39|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN25_AMP : 40|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN26_AMP : 41|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN27_AMP : 42|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN28_AMP : 43|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN29_AMP : 44|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN2_AMP : 17|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN30_AMP : 45|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN31_AMP : 46|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN32_AMP : 47|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN33_AMP : 48|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN34_AMP : 49|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN35_AMP : 50|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN36_AMP : 51|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN37_AMP : 52|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN38_AMP : 53|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN39_AMP : 54|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN3_AMP : 18|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN40_AMP : 55|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN41_AMP : 56|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN42_AMP : 57|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN43_AMP : 58|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN44_AMP : 59|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN45_AMP : 60|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN46_AMP : 61|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN47_AMP : 62|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN48_AMP : 63|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN4_AMP : 19|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN5_AMP : 20|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN6_AMP : 21|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN7_AMP : 22|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN8_AMP : 23|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN9_AMP : 24|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PtlNetInfo_AMP : 14|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ RepMsgReq_AMP : 8|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ ResdBit1_AMP : 13|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ ResdBit2_AMP : 15|1@0+ (1,0) [0|1] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ ScrNodId_AMP : 7|8@0+ (1,0) [0|255] "" CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
        
BO_ 1719 DIAG_RESP_AMP: 64 AMP
 SG_ DiagRespData0_AMP : 7|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData10_AMP : 327|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData11_AMP : 359|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData12_AMP : 391|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData13_AMP : 423|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData14_AMP : 455|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData15_AMP : 487|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData1_AMP : 39|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData2_AMP : 71|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData3_AMP : 103|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData4_AMP : 135|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData5_AMP : 167|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData6_AMP : 199|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData7_AMP : 231|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData8_AMP : 263|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData9_AMP : 295|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
        
BO_ 456 CNSL_I_1C8: 8 CNSL_I
 SG_ CNSL_I_LeftBtn_LongPress : 4|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_MidTp_LongPress : 5|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_RightBtn_LongPress : 6|1@0+ (1,0) [0|1] "" ZONE_FTM
        
BO_ 659 CNSL_I_293: 8 CNSL_I
 SG_ CNSL_I_EnableSwSt : 4|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_OpModeSt : 14|3@0+ (1,0) [0|7] "" ZONE_FTM
 SG_ CNSL_I_SensSt : 6|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ CNSL_I_St : 3|4@0+ (1,0) [0|15] "" ZONE_FTM
 SG_ CNSL_I_VibDeltalSt : 11|4@0+ (1,0) [0|15] "" ZONE_FTM
 SG_ CNSL_I_VibFbSt : 7|1@0+ (1,0) [0|1] "" ZONE_FTM
        
BO_ 1178 CNSL_I_49A: 64 CNSL_I
 SG_ CNSL_I_LeftBtn_Press : 0|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_LeftBtn_SwipeDown : 2|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_LeftBtn_SwipeUp : 1|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_LeftBtn_TouchCordData : 15|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ CNSL_I_LeftBtn_TouchRawData : 47|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ CNSL_I_LeftBtn_TouchSt : 3|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_MidTp_2Finger_SwipeDown : 74|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_MidTp_2Finger_SwipeLeft : 75|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_MidTp_2Finger_SwipeRight : 76|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_MidTp_2Finger_SwipeUp : 73|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_MidTp_3Finger_SwipeDown : 78|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_MidTp_3Finger_SwipeLeft : 79|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_MidTp_3Finger_SwipeRight : 80|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_MidTp_3Finger_SwipeUp : 77|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_MidTp_F1_TouchRawData_F1 : 127|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ CNSL_I_MidTp_Press : 4|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_MidTp_SwipeDown : 6|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_MidTp_SwipeLeft : 7|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_MidTp_SwipeRight : 72|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_MidTp_SwipeUp : 5|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_MidTp_TouchCordData_F1 : 95|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ CNSL_I_MidTp_TouchCordData_F2 : 159|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ CNSL_I_MidTp_TouchRawData_F2 : 191|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ CNSL_I_MidTp_TouchSt_F1 : 81|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_MidTp_TouchSt_F2 : 82|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_RightBtn_Press : 83|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_RightBtn_SwipeDown : 85|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_RightBtn_SwipeUp : 84|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ CNSL_I_RightBtn_TouchCordData : 223|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ CNSL_I_RightBtn_TouchRawData : 255|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ CNSL_I_RightBtn_TouchSt : 86|1@0+ (1,0) [0|1] "" ZONE_FTM
        
BO_ 1339 NM_CNSL_I: 8 CNSL_I
 SG_ ActvWakeup_CNSL_I : 12|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ NetMngtCoorn_CNSL_I : 10|2@0+ (1,0) [0|3] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ NetMngtCoorrSleepRdy_CNSL_I : 11|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC10_CNSL_I : 25|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC11_CNSL_I : 26|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC12_CNSL_I : 27|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC13_CNSL_I : 28|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC14_CNSL_I : 29|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC15_CNSL_I : 30|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC16_CNSL_I : 31|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC17_CNSL_I : 32|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC18_CNSL_I : 33|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC19_CNSL_I : 34|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC1_CNSL_I : 16|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC20_CNSL_I : 35|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC21_CNSL_I : 36|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC22_CNSL_I : 37|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC23_CNSL_I : 38|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC24_CNSL_I : 39|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC25_CNSL_I : 40|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC26_CNSL_I : 41|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC27_CNSL_I : 42|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC28_CNSL_I : 43|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC29_CNSL_I : 44|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC2_CNSL_I : 17|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC30_CNSL_I : 45|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC31_CNSL_I : 46|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC32_CNSL_I : 47|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC33_CNSL_I : 48|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC34_CNSL_I : 49|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC35_CNSL_I : 50|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC36_CNSL_I : 51|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC37_CNSL_I : 52|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC38_CNSL_I : 53|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC39_CNSL_I : 54|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC3_CNSL_I : 18|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC40_CNSL_I : 55|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC41_CNSL_I : 56|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC42_CNSL_I : 57|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC43_CNSL_I : 58|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC44_CNSL_I : 59|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC45_CNSL_I : 60|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC46_CNSL_I : 61|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC47_CNSL_I : 62|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC48_CNSL_I : 63|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC4_CNSL_I : 19|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC5_CNSL_I : 20|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC6_CNSL_I : 21|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC7_CNSL_I : 22|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC8_CNSL_I : 23|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PNC9_CNSL_I : 24|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PtlNetInfo_CNSL_I : 14|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ RepMsgReq_CNSL_I : 8|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ ResdBit1_CNSL_I : 13|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ ResdBit2_CNSL_I : 15|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ ScrNodId_CNSL_I : 7|8@0+ (1,0) [0|255] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
        
BO_ 1723 DIAG_RESP_CNSL_I: 64 CNSL_I
 SG_ DiagRespData0_CNSL_I : 7|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData10_CNSL_I : 327|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData11_CNSL_I : 359|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData12_CNSL_I : 391|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData13_CNSL_I : 423|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData14_CNSL_I : 455|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData15_CNSL_I : 487|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData1_CNSL_I : 39|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData2_CNSL_I : 71|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData3_CNSL_I : 103|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData4_CNSL_I : 135|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData5_CNSL_I : 167|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData6_CNSL_I : 199|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData7_CNSL_I : 231|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData8_CNSL_I : 263|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData9_CNSL_I : 295|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
        
BO_ 804 FI_R_01: 8 FI_R_R
 SG_ FI_R_BrightnessSts : 6|7@0+ (1,0) [0|100] "" ZONE_FTM
 SG_ FI_R_Sts : 10|3@0+ (1,0) [0|7] "" ZONE_FTM
 SG_ FI_R_WarningRsp_BriLimt : 11|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ FI_R_WarningRsp_LCD : 12|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ FI_R_WarningRsp_LedDrvr : 13|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ FI_R_WarningRsp_LocalDimmingFail : 14|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ FI_R_WarningRsp_LockSts : 7|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ FI_R_WarningRsp_PMICErr : 22|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ FI_R_WarningRsp_TconErr : 23|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ FI_R_WarningRsp_Thi : 15|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ FI_R_WarningRsp_TouchErr : 17|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ FI_R_WarningRsp_UHiLo : 16|1@0+ (1,0) [0|1] "" ZONE_FTM
        
BO_ 1344 NM_FI_RR: 8 FI_R_R
 SG_ ActvWakeup_FI_RR : 12|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ NetMngtCoorn_FI_RR : 10|2@0+ (1,0) [0|3] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ NetMngtCoorrSleepRdy_FI_RR : 11|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN10_FI_RR : 25|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN11_FI_RR : 26|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN12_FI_RR : 27|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN13_FI_RR : 28|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN14_FI_RR : 29|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN15_FI_RR : 30|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN16_FI_RR : 31|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN17_FI_RR : 32|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN18_FI_RR : 33|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN19_FI_RR : 34|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN1_FI_RR : 16|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN20_FI_RR : 35|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN21_FI_RR : 36|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN22_FI_RR : 37|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN23_FI_RR : 38|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN24_FI_RR : 39|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN25_FI_RR : 40|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN26_FI_RR : 41|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN27_FI_RR : 42|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN28_FI_RR : 43|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN29_FI_RR : 44|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN2_FI_RR : 17|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN30_FI_RR : 45|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN31_FI_RR : 46|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN32_FI_RR : 47|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN33_FI_RR : 48|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN34_FI_RR : 49|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN35_FI_RR : 50|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN36_FI_RR : 51|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN37_FI_RR : 52|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN38_FI_RR : 53|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN39_FI_RR : 54|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN3_FI_RR : 18|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN40_FI_RR : 55|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN41_FI_RR : 56|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN42_FI_RR : 57|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN43_FI_RR : 58|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN44_FI_RR : 59|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN45_FI_RR : 60|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN46_FI_RR : 61|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN47_FI_RR : 62|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN48_FI_RR : 63|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN4_FI_RR : 19|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN5_FI_RR : 20|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN6_FI_RR : 21|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN7_FI_RR : 22|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN8_FI_RR : 23|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN9_FI_RR : 24|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PtlNetInfo_FI_RR : 14|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ RepMsgReq_FI_RR : 8|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ ResdBit1_FI_RR : 13|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ ResdBit2_FI_RR : 15|1@0+ (1,0) [0|1] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ ScrNodId_FI_RR : 7|8@0+ (1,0) [0|255] "" AMP,CNSL_I,HUD,IC,ICS,NOMI,SWC,ZONE_FTM
        
BO_ 1728 DIAG_RESP_FI_R_R: 64 FI_R_R
 SG_ DiagRespData0_FI_R : 7|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData10_FI_R : 327|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData11_FI_R : 359|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData12_FI_R : 391|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData13_FI_R : 423|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData14_FI_R : 455|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData15_FI_R : 487|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData1_FI_R : 39|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData2_FI_R : 71|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData3_FI_R : 103|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData4_FI_R : 135|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData5_FI_R : 167|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData6_FI_R : 199|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData7_FI_R : 231|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData8_FI_R : 263|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData9_FI_R : 295|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
        
BO_ 776 HUD_01: 8 HUD
 SG_ HUD_AgAdjmtSts : 15|5@0+ (0.3,-3) [-3|3] "" ZONE_FTM
 SG_ HUD_HeiSts : 30|7@0+ (1,0) [0|100] "%" ZONE_FTM
 SG_ HUD_LCDbackLiSts : 22|7@0+ (1,0) [0|100] "%" ZONE_FTM
 SG_ HUD_Sts : 7|3@0+ (1,0) [0|7] "" ZONE_FTM
 SG_ HUD_WarningRsp_BriLimd : 2|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ HUD_WarningRsp_LockSts : 4|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ HUD_WarningRsp_MotFail : 1|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ HUD_WarningRsp_TFT_LEDFail : 0|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ HUD_WarningRsp_Thi : 3|1@0+ (1,0) [0|1] "" ZONE_FTM
        
BO_ 1333 NM_HUD: 8 HUD
 SG_ ActvWakeup_HUD : 12|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ NetMngtCoorn_HUD : 10|2@0+ (1,0) [0|3] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ NetMngtCoorrSleepRdy_HUD : 11|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN10_HUD : 25|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN11_HUD : 26|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN12_HUD : 27|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN13_HUD : 28|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN14_HUD : 29|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN15_HUD : 30|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN16_HUD : 31|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN17_HUD : 32|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN18_HUD : 33|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN19_HUD : 34|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN1_HUD : 16|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN20_HUD : 35|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN21_HUD : 36|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN22_HUD : 37|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN23_HUD : 38|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN24_HUD : 39|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN25_HUD : 40|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN26_HUD : 41|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN27_HUD : 42|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN28_HUD : 43|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN29_HUD : 44|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN2_HUD : 17|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN30_HUD : 45|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN31_HUD : 46|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN32_HUD : 47|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN33_HUD : 48|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN34_HUD : 49|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN35_HUD : 50|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN36_HUD : 51|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN37_HUD : 52|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN38_HUD : 53|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN39_HUD : 54|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN3_HUD : 18|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN40_HUD : 55|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN41_HUD : 56|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN42_HUD : 57|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN43_HUD : 58|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN44_HUD : 59|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN45_HUD : 60|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN46_HUD : 61|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN47_HUD : 62|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN48_HUD : 63|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN4_HUD : 19|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN5_HUD : 20|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN6_HUD : 21|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN7_HUD : 22|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN8_HUD : 23|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN9_HUD : 24|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ PtlNetInfo_HUD : 14|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ RepMsgReq_HUD : 8|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ ResdBit1_HUD : 13|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ ResdBit2_HUD : 15|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
 SG_ ScrNodId_HUD : 7|8@0+ (1,0) [0|255] "" AMP,CNSL_I,FI_R_R,IC,ICS,NOMI,SWC,ZONE_FTM
        
BO_ 1717 DIAG_RESP_HUD: 64 HUD
 SG_ DiagRespData0_HUD : 7|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData10_HUD : 327|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData11_HUD : 359|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData12_HUD : 391|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData13_HUD : 423|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData14_HUD : 455|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData15_HUD : 487|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData1_HUD : 39|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData2_HUD : 71|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData3_HUD : 103|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData4_HUD : 135|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData5_HUD : 167|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData6_HUD : 199|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData7_HUD : 231|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData8_HUD : 263|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData9_HUD : 295|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
        
BO_ 741 IC_01: 8 IC
 SG_ IC_LCDbackLiSts_L : 22|7@0+ (1,0) [0|100] "%" ZONE_FTM
 SG_ IC_LCDbackLiSts_R : 30|7@0+ (1,0) [0|100] "%" ZONE_FTM
 SG_ IC_Sts : 15|3@0+ (1,0) [0|7] "" ZONE_FTM
 SG_ IC_WarningRsp_BriLimt_L : 3|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ IC_WarningRsp_BriLimt_R : 8|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ IC_WarningRsp_LCDFail_L : 4|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ IC_WarningRsp_LCDFail_R : 9|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ IC_WarningRsp_LedDrvr_L : 6|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ IC_WarningRsp_LedDrvr_R : 11|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ IC_WarningRsp_LocalDimmingFail_L : 5|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ IC_WarningRsp_LocalDimmingFail_R : 10|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ IC_WarningRsp_LockSts : 1|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ IC_WarningRsp_Thi_L : 2|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ IC_WarningRsp_Thi_R : 23|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ IC_WarningRsp_UHiLo_L : 7|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ IC_WarningRsp_UHiLo_R : 12|1@0+ (1,0) [0|1] "" ZONE_FTM
        
BO_ 1331 NM_IC: 8 IC
 SG_ ActvWakeup_IC : 12|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ NetMngtCoorn_IC : 10|2@0+ (1,0) [0|3] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ NetMngtCoorrSleepRdy_IC : 11|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN10_IC : 25|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN11_IC : 26|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN12_IC : 27|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN13_IC : 28|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN14_IC : 29|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN15_IC : 30|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN16_IC : 31|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN17_IC : 32|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN18_IC : 33|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN19_IC : 34|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN1_IC : 16|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN20_IC : 35|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN21_IC : 36|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN22_IC : 37|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN23_IC : 38|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN24_IC : 39|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN25_IC : 40|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN26_IC : 41|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN27_IC : 42|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN28_IC : 43|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN29_IC : 44|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN2_IC : 17|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN30_IC : 45|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN31_IC : 46|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN32_IC : 47|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN33_IC : 48|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN34_IC : 49|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN35_IC : 50|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN36_IC : 51|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN37_IC : 52|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN38_IC : 53|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN39_IC : 54|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN3_IC : 18|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN40_IC : 55|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN41_IC : 56|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN42_IC : 57|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN43_IC : 58|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN44_IC : 59|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN45_IC : 60|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN46_IC : 61|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN47_IC : 62|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN48_IC : 63|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN4_IC : 19|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN5_IC : 20|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN6_IC : 21|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN7_IC : 22|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN8_IC : 23|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PN9_IC : 24|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ PtlNetInfo_IC : 14|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ RepMsgReq_IC : 8|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ ResdBit1_IC : 13|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ ResdBit2_IC : 15|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
 SG_ ScrNodId_IC : 7|8@0+ (1,0) [0|255] "" AMP,CNSL_I,FI_R_R,HUD,ICS,NOMI,SWC,ZONE_FTM
        
BO_ 1715 DIAG_RESP_IC: 64 IC
 SG_ DiagRespData0_IC : 7|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData10_IC : 327|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData11_IC : 359|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData12_IC : 391|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData13_IC : 423|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData14_IC : 455|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData15_IC : 487|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData1_IC : 39|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData2_IC : 71|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData3_IC : 103|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData4_IC : 135|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData5_IC : 167|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData6_IC : 199|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData7_IC : 231|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData8_IC : 263|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData9_IC : 295|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
        
BO_ 755 ICS_01: 8 ICS
 SG_ ICS_BrightnessSts : 15|7@0+ (1,0) [0|100] "%" ZONE_FTM
 SG_ ICS_Sts : 2|3@0+ (1,0) [0|7] "" ZONE_FTM
 SG_ ICS_WarningRsp_BriLimt : 4|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ ICS_WarningRsp_BridgeErr : 23|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ ICS_WarningRsp_LockSts : 8|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ ICS_WarningRsp_PMICErr : 22|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ ICS_WarningRsp_TconErr : 5|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ ICS_WarningRsp_Thi : 3|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ ICS_WarningRsp_TouchErr : 7|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ ICS_WarningRsp_UHiLo : 6|1@0+ (1,0) [0|1] "" ZONE_FTM
        
BO_ 1334 NM_ICS: 8 ICS
 SG_ ActvWakeup_ICS : 12|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ NetMngtCoorn_ICS : 10|2@0+ (1,0) [0|3] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ NetMngtCoorrSleepRdy_ICS : 11|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN10_ICS : 25|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN11_ICS : 26|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN12_ICS : 27|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN13_ICS : 28|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN14_ICS : 29|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN15_ICS : 30|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN16_ICS : 31|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN17_ICS : 32|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN18_ICS : 33|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN19_ICS : 34|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN1_ICS : 16|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN20_ICS : 35|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN21_ICS : 36|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN22_ICS : 37|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN23_ICS : 38|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN24_ICS : 39|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN25_ICS : 40|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN26_ICS : 41|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN27_ICS : 42|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN28_ICS : 43|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN29_ICS : 44|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN2_ICS : 17|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN30_ICS : 45|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN31_ICS : 46|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN32_ICS : 47|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN33_ICS : 48|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN34_ICS : 49|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN35_ICS : 50|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN36_ICS : 51|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN37_ICS : 52|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN38_ICS : 53|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN39_ICS : 54|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN3_ICS : 18|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN40_ICS : 55|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN41_ICS : 56|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN42_ICS : 57|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN43_ICS : 58|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN44_ICS : 59|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN45_ICS : 60|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN46_ICS : 61|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN47_ICS : 62|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN48_ICS : 63|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN4_ICS : 19|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN5_ICS : 20|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN6_ICS : 21|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN7_ICS : 22|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN8_ICS : 23|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PN9_ICS : 24|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ PtlNetInfo_ICS : 14|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ RepMsgReq_ICS : 8|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ ResdBit1_ICS : 13|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ ResdBit2_ICS : 15|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
 SG_ ScrNodId_ICS : 7|8@0+ (1,0) [0|255] "" AMP,CNSL_I,FI_R_R,HUD,IC,NOMI,SWC,ZONE_FTM
        
BO_ 1718 DIAG_RESP_ICS: 64 ICS
 SG_ DiagRespData0_ICS : 7|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData10_ICS : 327|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData11_ICS : 359|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData12_ICS : 391|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData13_ICS : 423|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData14_ICS : 455|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData15_ICS : 487|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData1_ICS : 39|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData2_ICS : 71|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData3_ICS : 103|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData4_ICS : 135|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData5_ICS : 167|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData6_ICS : 199|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData7_ICS : 231|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData8_ICS : 263|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData9_ICS : 295|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
        
BO_ 453 NOMI_RESP_01: 32 NOMI
 SG_ HALO_WORK_STATUS : 79|8@0+ (1,0) [0|255] "" ZONE_FTM
 SG_ MATEPosPitch : 87|8@0+ (1,0) [0|40] "Deg" ZONE_FTM
 SG_ MATEPosYaw : 63|16@0+ (1,-177) [-177|177] "Deg" ZONE_FTM
 SG_ NOMI_BackLiSts : 47|7@0+ (1,0) [0|100] "%" ZONE_FTM
 SG_ NOMI_Sts : 39|3@0+ (1,0) [0|7] "" ZONE_FTM
 SG_ NOMI_Type : 36|5@0+ (1,0) [0|7] "" ZONE_FTM
 SG_ NOMI_WarningRsp : 55|8@0+ (1,0) [0|255] "" ZONE_FTM
        
BO_ 576 MATE_RESP_02: 64 NOMI
 SG_ MateRespNfcUid0 : 7|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ MateRespNfcUid1 : 39|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ MateRespNfcUid2 : 71|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ MateRespNfcUid3 : 103|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ MateRespNfcUid4 : 135|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ MateRespNfcUid5 : 167|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ MateRespNfcUid6 : 199|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ MateRespNfcUid7 : 231|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ MateRespNioUid0 : 263|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ MateRespNioUid1 : 295|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ MateRespNioUid2 : 327|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ MateRespNioUid3 : 359|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ MateRespNioUid4 : 391|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ MateRespNioUid5 : 423|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ MateRespNioUid6 : 455|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ MateRespNioUid7 : 487|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
        
BO_ 1163 MATE_RESP_01: 8 NOMI
 SG_ MATEFrameRPCount : 7|8@0+ (1,0) [0|255] "" ZONE_FTM
 SG_ MATEFrameRYCount : 23|8@0+ (1,0) [0|255] "" ZONE_FTM
 SG_ MATERespFramePAck : 15|3@0+ (1,0) [0|7] "" ZONE_FTM
 SG_ MATERespFrameYAck : 12|3@0+ (1,0) [0|7] "" ZONE_FTM
 SG_ MateRespCmdAction : 39|3@0+ (1,0) [0|7] "" ZONE_FTM
 SG_ MateRespCmdActionCount : 31|8@0+ (1,0) [0|255] "" ZONE_FTM
        
BO_ 1169 HALO_RESP_01: 64 NOMI
 SG_ HALO_RSP_CODE : 39|8@0+ (1,0) [0|255] "null" ZONE_FTM
 SG_ HALO_RSP_Count : 7|16@0+ (1,0) [0|65535] "null" ZONE_FTM
 SG_ HALO_RSP_ID : 23|16@0+ (1,0) [0|65535] "null" ZONE_FTM
        
BO_ 1341 NM_NOMI: 8 NOMI
 SG_ ActvWakeup_NOMI : 12|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ NetMngtCoorn_NOMI : 10|2@0+ (1,0) [0|3] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ NetMngtCoorrSleepRdy_NOMI : 11|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN10_NOMI : 25|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN11_NOMI : 26|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN12_NOMI : 27|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN13_NOMI : 28|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN14_NOMI : 29|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN15_NOMI : 30|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN16_NOMI : 31|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN17_NOMI : 32|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN18_NOMI : 33|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN19_NOMI : 34|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN1_NOMI : 16|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN20_NOMI : 35|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN21_NOMI : 36|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN22_NOMI : 37|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN23_NOMI : 38|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN24_NOMI : 39|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN25_NOMI : 40|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN26_NOMI : 41|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN27_NOMI : 42|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN28_NOMI : 43|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN29_NOMI : 44|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN2_NOMI : 17|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN30_NOMI : 45|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN31_NOMI : 46|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN32_NOMI : 47|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN33_NOMI : 48|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN34_NOMI : 49|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN35_NOMI : 50|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN36_NOMI : 51|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN37_NOMI : 52|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN38_NOMI : 53|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN39_NOMI : 54|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN3_NOMI : 18|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN40_NOMI : 55|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN41_NOMI : 56|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN42_NOMI : 57|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN43_NOMI : 58|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN44_NOMI : 59|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN45_NOMI : 60|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN46_NOMI : 61|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN47_NOMI : 62|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN48_NOMI : 63|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN4_NOMI : 19|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN5_NOMI : 20|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN6_NOMI : 21|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN7_NOMI : 22|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN8_NOMI : 23|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PN9_NOMI : 24|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ PtlNetInfo_NOMI : 14|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ RepMsgReq_NOMI : 8|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ ResdBit1_NOMI : 13|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ ResdBit2_NOMI : 15|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
 SG_ ScrNodId_NOMI : 7|8@0+ (1,0) [0|255] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,SWC,ZONE_FTM
        
BO_ 1725 DIAG_RESP_NOMI: 64 NOMI
 SG_ DiagRespData0_NOMI : 7|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData10_NOMI : 327|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData11_NOMI : 359|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData12_NOMI : 391|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData13_NOMI : 423|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData14_NOMI : 455|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData15_NOMI : 487|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData1_NOMI : 39|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData2_NOMI : 71|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData3_NOMI : 103|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData4_NOMI : 135|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData5_NOMI : 167|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData6_NOMI : 199|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData7_NOMI : 231|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData8_NOMI : 263|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData9_NOMI : 295|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
        
BO_ 671 SWC_29F: 8 SWC
 SG_ MenuPushSwtSts : 21|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ NOMICtrlPushSwtSts : 45|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ SWCFailSts : 53|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ SWCLe_CentPushSwtSts : 19|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ SWCLe_DwnPushSwtSts : 17|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ SWCLe_LePushSwtSts : 29|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ SWCLe_RiPushSwtSts : 31|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ SWCLe_UpPushSwtSts : 51|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ SWCRi_CentPushSwtSts : 55|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ SWCRi_DwnPushSwtSts : 37|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ SWCRi_LePushSwtSts : 43|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ SWCRi_RiPushSwtSts : 41|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ SWCRi_UpPushSwtSts : 35|2@0+ (1,0) [0|3] "" ZONE_FTM
 SG_ SWC_29F_CRC : 7|8@0+ (1,0) [0|255] "" ZONE_FTM
 SG_ SWC_29F_MsgCntr : 11|4@0+ (1,0) [0|14] "" ZONE_FTM
 SG_ Spare01PushSwtSts : 47|2@0+ (1,0) [0|3] "" ZONE_FTM
        
BO_ 77 SWC_4D: 8 SWC
 SG_ SWCLeSts : 32|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ SWCRiSts : 33|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ SWCRi_BarBtmPressed : 34|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ SWCRi_BarPos : 23|12@0+ (1,0) [0|4095] "" ZONE_FTM
 SG_ SWCRi_BarTopPressed : 24|1@0+ (1,0) [0|1] "" ZONE_FTM
 SG_ SWCRi_BarTouch : 27|3@0+ (1,0) [0|7] "" ZONE_FTM
        
BO_ 1322 NM_SWC: 8 SWC
 SG_ ActvWakeup_SWC : 12|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ NetMngtCoorn_SWC : 10|2@0+ (1,0) [0|3] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ NetMngtCoorrSleepRdy_SWC : 11|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN10_SWC : 25|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN11_SWC : 26|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN12_SWC : 27|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN13_SWC : 28|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN14_SWC : 29|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN15_SWC : 30|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN16_SWC : 31|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN17_SWC : 32|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN18_SWC : 33|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN19_SWC : 34|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN1_SWC : 16|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN20_SWC : 35|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN21_SWC : 36|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN22_SWC : 37|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN23_SWC : 38|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN24_SWC : 39|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN25_SWC : 40|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN26_SWC : 41|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN27_SWC : 42|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN28_SWC : 43|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN29_SWC : 44|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN2_SWC : 17|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN30_SWC : 45|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN31_SWC : 46|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN32_SWC : 47|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN33_SWC : 48|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN34_SWC : 49|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN35_SWC : 50|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN36_SWC : 51|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN37_SWC : 52|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN38_SWC : 53|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN39_SWC : 54|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN3_SWC : 18|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN40_SWC : 55|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN41_SWC : 56|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN42_SWC : 57|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN43_SWC : 58|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN44_SWC : 59|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN45_SWC : 60|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN46_SWC : 61|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN47_SWC : 62|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN48_SWC : 63|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN4_SWC : 19|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN5_SWC : 20|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN6_SWC : 21|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN7_SWC : 22|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN8_SWC : 23|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PN9_SWC : 24|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ PtlNetInfo_SWC : 14|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ RepMsgReq_SWC : 8|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ ResdBit1_SWC : 13|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ ResdBit2_SWC : 15|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
 SG_ ScrNodId_SWC : 7|8@0+ (1,0) [0|255] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,ZONE_FTM
        
BO_ 1706 DIAG_RESP_SWC: 64 SWC
 SG_ DiagRespData0_SWC : 7|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData10_SWC : 327|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData11_SWC : 359|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData12_SWC : 391|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData13_SWC : 423|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData14_SWC : 455|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData15_SWC : 487|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData1_SWC : 39|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData2_SWC : 71|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData3_SWC : 103|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData4_SWC : 135|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData5_SWC : 167|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData6_SWC : 199|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData7_SWC : 231|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData8_SWC : 263|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
 SG_ DiagRespData9_SWC : 295|32@0+ (1,0) [0|4294967295] "" ZONE_FTM
        
BO_ 286 BCU_11E: 8 ZONE_FTM
 SG_ BCU_11E_CRC : 7|8@0+ (1,0) [0|255] "" Vector__XXX
 SG_ BCU_11E_MsgCntr : 11|4@0+ (1,0) [0|14] "" Vector__XXX
 SG_ VehSpdSts_Rdnt1 : 29|1@0+ (1,0) [0|1] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ VehSpd_Rdnt1 : 28|13@0+ (0.05625,0) [0|360] "km/h" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
        
BO_ 316 ZONE_13C: 8 ZONE_FTM
 SG_ DrvState : 18|3@0+ (1,0) [0|7] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ DrvState_SrvSts : 13|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ OperatorState : 21|3@0+ (1,0) [0|7] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ OperatorState_SrvSts : 12|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ VehStateDetailed : 27|4@0+ (1,0) [0|15] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ VehStateDetailed_SrvSts : 14|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ ZONE_13C_CRC : 7|8@0+ (1,0) [0|255] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ ZONE_13C_MsgCntr : 11|4@0+ (1,0) [0|14] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
        
BO_ 371 ZONE_173: 24 ZONE_FTM
 SG_ AccrPedlActPosn : 87|8@0+ (0.392,0) [0|99.96] "%" AMP
 SG_ BMSChrgState : 61|3@0+ (1,0) [0|7] "" NOMI
 SG_ BackLiDutyCycCmd : 39|7@0+ (1,0) [0|100] "%" FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ ComfEna : 31|2@0+ (1,0) [0|3] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ HVBattSOCLowWarn : 62|1@0+ (1,0) [0|1] "" NOMI
 SG_ ImobEnaReq : 73|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ LiSnsrData : 47|16@0+ (1,0) [0|10000] "cd/m2" HUD,NOMI
 SG_ LiSnsrFailSts : 32|1@0+ (1,0) [0|1] "" HUD,NOMI
 SG_ PEUFMotSpd : 95|16@0+ (1,-32768) [-32768|32767] "rpm" AMP
 SG_ PEURMotSpd : 111|16@0+ (1,0) [0|65535] "rpm" AMP
 SG_ VCUActGear : 58|3@0+ (1,0) [0|7] "" AMP,NOMI
 SG_ VCUActGearValid : 74|1@0+ (1,0) [0|1] "" Vector__XXX
 SG_ VCUVehDispdSpd : 127|13@0+ (0.05625,0) [0|360] "KMPH" AMP
        
BO_ 549 Zone_FL_NOMI_01: 48 ZONE_FTM
 SG_ HALO_AIR_QUALITY : 1|2@0+ (1,0) [0|3] "" NOMI
 SG_ HALO_AMBIENT_LIGHT : 83|4@0+ (1,0) [0|15] "" NOMI
 SG_ HALO_CHARGING_DIRECTION : 95|9@0+ (1,0) [0|360] "degree" NOMI
 SG_ HALO_LISTENING_DIRECTION : 31|3@0+ (1,0) [0|7] "" NOMI
 SG_ HALO_LISTENING_VOLUME : 47|7@0+ (1,0) [0|127] "%" NOMI
 SG_ HALO_MUSIC_BPM : 55|7@0+ (1,0) [0|127] "" NOMI
 SG_ HALO_MUSIC_BURST_VOLUME : 63|7@0+ (1,0) [0|127] "" NOMI
 SG_ HALO_MUSIC_GENRE : 71|7@0+ (1,0) [0|127] "" NOMI
 SG_ HALO_OBSTACLE_DIRECTION : 64|9@0+ (1,0) [0|300] "degree" NOMI
 SG_ HALO_OBSTACLE_DISTANCE : 87|4@0+ (1,0) [0|15] "" NOMI
 SG_ HALO_REMIND_RISK : 25|2@0+ (1,0) [0|3] "" NOMI
 SG_ HALO_TALKING_DIRECTION : 4|3@0+ (1,0) [0|7] "" NOMI
 SG_ HALO_TALKING_VOLUME : 39|7@0+ (1,0) [0|127] "%" NOMI
 SG_ HALO_WORK_MODE : 5|1@0+ (1,0) [0|1] "" NOMI
 SG_ HALO_WORK_SELECTOR : 15|8@0+ (1,0) [0|255] "" NOMI
 SG_ NOMI_BackLiReq : 23|7@0+ (1,0) [0|100] "%" NOMI
 SG_ NOMI_OnOffCmd : 7|2@0+ (1,0) [0|3] "" NOMI
 SG_ NOMI_VideoSrcReq : 27|2@0+ (1,0) [0|3] "" NOMI
        
BO_ 648 ZONE_288: 64 ZONE_FTM
 SG_ CNSL_I_EnableSwReq : 74|2@0+ (1,0) [0|3] "" CNSL_I
 SG_ CNSL_I_OpModeReq : 106|3@0+ (1,0) [0|7] "" CNSL_I
 SG_ CNSL_I_SensReq : 76|2@0+ (1,0) [0|3] "" CNSL_I
 SG_ CNSL_I_VibDeltaReq : 51|4@0+ (1,0) [0|15] "" CNSL_I
 SG_ CNSL_I_VibFbReq : 53|2@0+ (1,0) [0|3] "" CNSL_I
 SG_ CenLockUnlockSts : 125|3@0+ (1,0) [0|7] "" NOMI
 SG_ DoorAjarFrntLeSts : 143|2@0+ (1,0) [0|3] "" AMP,NOMI
 SG_ DoorAjarFrntRiSts : 141|2@0+ (1,0) [0|3] "" AMP,NOMI
 SG_ DoorAjarReLeSts : 139|2@0+ (1,0) [0|3] "" AMP,NOMI
 SG_ DoorAjarReRiSts : 145|2@0+ (1,0) [0|3] "" AMP,NOMI
 SG_ ECOPlusModSts : 72|1@0+ (1,0) [0|1] "" AMP,FI_R_R
 SG_ ETCPaymentCtrl : 45|2@0+ (1,0) [0|3] "" Vector__XXX
 SG_ FI_R_OnReq : 43|2@0+ (1,0) [0|3] "" FI_R_R
 SG_ FI_R_ResetReq : 95|2@0+ (1,0) [0|3] "" FI_R_R
 SG_ FI_R_SetBackLight : 103|7@0+ (1,0) [0|100] "" FI_R_R
 SG_ FI_R_VideoSrcReq : 93|2@0+ (1,0) [0|3] "" FI_R_R
 SG_ HUD_AgAdjmtReq : 111|5@0+ (0.3,-3) [-3|3] "" HUD
 SG_ HUD_Cmd : 55|2@0+ (1,0) [0|3] "" HUD
 SG_ HUD_LCDbackLiReq : 31|7@0+ (1,0) [0|100] "%" HUD
 SG_ HUD_ResetReq : 47|2@0+ (1,0) [0|3] "" HUD
 SG_ HoodAjarSts : 151|2@0+ (1,0) [0|3] "" NOMI
 SG_ ICS_BrightnessReq : 135|7@0+ (1,0) [0|100] "%" ICS
 SG_ ICS_OnReq : 127|2@0+ (1,0) [0|3] "" ICS
 SG_ ICS_ResetReq : 122|2@0+ (1,0) [0|3] "" ICS
 SG_ ICS_VideoSrcReq : 137|2@0+ (1,0) [0|3] "" ICS
 SG_ IC_LCDbackLiReq_L : 119|7@0+ (1,0) [0|100] "%" IC
 SG_ IC_LCDbackLiReq_R : 190|7@0+ (1,0) [0|100] "%" IC
 SG_ IC_ResetReq_L : 91|2@0+ (1,0) [0|3] "" IC
 SG_ IC_ResetReq_R : 41|2@0+ (1,0) [0|3] "" IC
 SG_ IC_VideoSrcReq_L : 89|2@0+ (1,0) [0|3] "" IC
 SG_ IC_VideoSrcReq_R : 193|2@0+ (1,0) [0|3] "" IC
 SG_ LVBattUSts_LFP : 147|2@0+ (1,0) [0|3] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ LVBattU_LFP : 159|16@0+ (0.001,0) [0|65.535] "V" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PositionReq : 39|7@0+ (1,0) [0|100] "%" HUD
 SG_ TrAjarSts : 149|2@0+ (1,0) [0|3] "" NOMI
 SG_ VCUPwrDisp : 175|10@0+ (1,-300) [-300|700] "kw" AMP
        
BO_ 747 Zone_AMP_02: 64 ZONE_FTM
 SG_ CDF_SetBrake : 35|3@0+ (1,0) [0|7] "" AMP
 SG_ CDF_SetGear : 55|3@0+ (1,0) [0|7] "" AMP
 SG_ CDF_SetPedal : 38|3@0+ (1,0) [0|7] "" AMP
 SG_ CDF_SetRotSpeed : 143|8@0+ (1,0) [0|255] "" AMP
 SG_ CDF_SetTorqueFront : 47|8@0+ (1,0) [0|255] "" AMP
 SG_ CDF_SetTorqueRear : 119|8@0+ (1,0) [0|255] "" AMP
 SG_ CDF_SetVehSpeed : 31|9@0+ (1,0) [0|300] "km/h" AMP
 SG_ CompressorSpd : 207|8@0+ (1,0) [0|255] "" AMP
 SG_ DriveModType : 426|3@0+ (1,0) [0|7] "" AMP
 SG_ Envtemp : 127|8@0+ (1,-40) [-40|86] "" AMP
 SG_ Hvac2ndAutoSts : 130|1@0+ (1,0) [0|1] "" AMP
 SG_ Hvac2ndBlwSpd : 191|8@0+ (1,0) [0|255] "" AMP
 SG_ Hvac2ndBlwSpdAutoSts : 256|1@0+ (1,0) [0|1] "" AMP
 SG_ Hvac2ndFanDirAutoSts : 352|1@0+ (1,0) [0|1] "" AMP
 SG_ Hvac2ndFanDirFaceSts : 304|1@0+ (1,0) [0|1] "" AMP
 SG_ Hvac2ndFanDirFloorSts : 336|1@0+ (1,0) [0|1] "" AMP
 SG_ Hvac2ndOnOffSts : 88|1@0+ (1,0) [0|1] "" AMP
 SG_ Hvac3rdAutoSts : 129|1@0+ (1,0) [0|1] "" AMP
 SG_ Hvac3rdBlwSpd : 199|8@0+ (1,0) [0|255] "" AMP
 SG_ Hvac3rdBlwSpdAutoSts : 248|1@0+ (1,0) [0|1] "" AMP
 SG_ Hvac3rdFanDirAutoSts : 280|1@0+ (1,0) [0|1] "" AMP
 SG_ Hvac3rdFanDirFaceSts : 296|1@0+ (1,0) [0|1] "" AMP
 SG_ Hvac3rdFanDirFloorSts : 328|1@0+ (1,0) [0|1] "" AMP
 SG_ Hvac3rdOnOffSts : 96|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacAcSts : 128|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacAutoSts : 152|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacBlwSpd : 175|8@0+ (1,0) [0|255] "" AMP
 SG_ HvacBlwSpdAutoSts : 272|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacFanDirAutoSts : 320|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacFanDirFaceSts : 360|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacFanDirFloorSts : 288|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacFrntDefogSts : 384|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacFrntRiAutoSts : 131|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacFrntRiBlwSpd : 183|8@0+ (1,0) [0|255] "" AMP
 SG_ HvacFrntRiBlwSpdAutoSts : 264|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacFrntRiFanDirAutoSts : 312|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacFrntRiFanDirFaceSts : 407|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacFrntRiFanDirFloorSts : 344|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacFrntRiOnOffSts : 80|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacMaxAcSts : 144|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacMaxDefogSts : 368|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacMaxHtSts : 392|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacOnOffSts : 48|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacRearDefogSts : 376|1@0+ (1,0) [0|1] "" AMP
 SG_ HvacRecSts : 428|2@0+ (1,0) [0|3] "" AMP
 SG_ IntrTemp : 167|8@0+ (1,-40) [-40|86] "" AMP
 SG_ LidarSpd : 215|8@0+ (1,0) [0|255] "" AMP
 SG_ Seat2ndLeFAPosn : 303|7@0+ (1,0) [0|100] "%" AMP
 SG_ Seat2ndLeHdrPosn : 327|7@0+ (1,0) [0|100] "%" AMP
 SG_ Seat2ndLeRecPosn : 319|7@0+ (1,0) [0|100] "%" AMP
 SG_ Seat2ndLeUPPosn : 311|7@0+ (1,0) [0|100] "%" AMP
 SG_ Seat2ndRiFAPosn : 335|7@0+ (1,0) [0|100] "%" AMP
 SG_ Seat2ndRiHdrPosn : 359|7@0+ (1,0) [0|100] "%" AMP
 SG_ Seat2ndRiRecPosn : 351|7@0+ (1,0) [0|100] "%" AMP
 SG_ Seat2ndRiUPPosn : 343|7@0+ (1,0) [0|100] "%" AMP
 SG_ Seat3rdLeFAPosn : 367|7@0+ (1,0) [0|100] "%" AMP
 SG_ Seat3rdLeHdrPosn : 391|7@0+ (1,0) [0|100] "%" AMP
 SG_ Seat3rdLeRecPosn : 383|7@0+ (1,0) [0|100] "%" AMP
 SG_ Seat3rdLeUPPosn : 375|7@0+ (1,0) [0|100] "%" AMP
 SG_ Seat3rdRiFAPosn : 399|7@0+ (1,0) [0|100] "%" AMP
 SG_ Seat3rdRiHdrPosn : 414|7@0+ (1,0) [0|100] "%" AMP
 SG_ Seat3rdRiRecPosn : 406|7@0+ (1,0) [0|100] "%" AMP
 SG_ Seat3rdRiUPPosn : 422|7@0+ (1,0) [0|100] "%" AMP
 SG_ SeatFrntLeFAPosn : 239|7@0+ (1,0) [0|100] "%" AMP
 SG_ SeatFrntLeHdrPosn : 263|7@0+ (1,0) [0|100] "%" AMP
 SG_ SeatFrntLeRecPosn : 255|7@0+ (1,0) [0|100] "%" AMP
 SG_ SeatFrntLeUPPosn : 247|7@0+ (1,0) [0|100] "%" AMP
 SG_ SeatFrntRiFAPosn : 271|7@0+ (1,0) [0|100] "%" AMP
 SG_ SeatFrntRiHdrPosn : 295|7@0+ (1,0) [0|100] "%" AMP
 SG_ SeatFrntRiRecPosn : 287|7@0+ (1,0) [0|100] "%" AMP
 SG_ SeatFrntRiUPPosn : 279|7@0+ (1,0) [0|100] "%" AMP
 SG_ SeatOccpFrntLeSts : 223|2@0+ (1,0) [0|3] "" AMP
 SG_ SeatOccpFrntRiFail : 221|2@0+ (1,0) [0|3] "" AMP
 SG_ SeatOccpt2ndLeSts : 219|2@0+ (1,0) [0|3] "" AMP
 SG_ SeatOccpt2ndMidSts : 217|2@0+ (1,0) [0|3] "" AMP
 SG_ SeatOccpt2ndRiSts : 231|2@0+ (1,0) [0|3] "" AMP
 SG_ SeatOccpt3rdLeSts : 229|2@0+ (1,0) [0|3] "" AMP
 SG_ SeatOccpt3rdRiSts : 430|2@0+ (1,0) [0|3] "" AMP
 SG_ SunroofPosn : 151|7@0+ (1,0) [0|100] "%" AMP
 SG_ TailgateSts : 32|1@0+ (1,0) [0|1] "" AMP
 SG_ TpmsFrntLeWhlPress : 63|8@0+ (1.373,0) [0|350.115] "Kpa" AMP
 SG_ TpmsFrntRiWhlPress : 71|8@0+ (1.373,0) [0|350.115] "Kpa" AMP
 SG_ TpmsReLeWhlPress : 79|8@0+ (1.373,0) [0|350.115] "Kpa" AMP
 SG_ TpmsReRiWhlPress : 111|8@0+ (1.373,0) [0|350.115] "Kpa" AMP
 SG_ VC_SeatNumber : 226|3@0+ (1,0) [0|7] "" AMP
 SG_ VC_SuspensionType : 232|1@0+ (1,0) [0|1] "" AMP
 SG_ VehicleOnVoltage : 240|1@0+ (1,0) [0|1] "" AMP
 SG_ WinFrntLePosn : 87|7@0+ (1,0) [0|127] "%" AMP
 SG_ WinFrntRiPosn : 95|7@0+ (1,0) [0|127] "%" AMP
 SG_ WinReLePosn : 103|7@0+ (1,0) [0|127] "%" AMP
 SG_ WinReRiPosn : 159|7@0+ (1,0) [0|127] "" AMP
        
BO_ 781 Zone_AMP_01: 64 ZONE_FTM
 SG_ CDF_SetEPMode : 49|2@0+ (1,0) [0|3] "" AMP
 SG_ CDF_SetEPSwitch : 97|1@0+ (1,0) [0|1] "" AMP
 SG_ CDF_SetKROKMicType : 104|1@0+ (1,0) [0|1] "" AMP
 SG_ CDF_SetKROKSwitch : 25|1@0+ (1,0) [0|1] "" AMP
 SG_ CDF_SetKROKVol : 47|5@0+ (1,0) [0|30] "" AMP
 SG_ CDF_SetMediaFrontMute : 89|1@0+ (1,0) [0|1] "" AMP
 SG_ CDF_SetMediaPath : 81|2@0+ (1,0) [0|3] "" AMP
 SG_ CDF_SetMediaRearMute : 42|1@0+ (1,0) [0|1] "" AMP
 SG_ CDF_SetMediaSwitch : 88|1@0+ (1,0) [0|1] "" AMP
 SG_ CDF_SetMediaType : 73|2@0+ (1,0) [0|3] "" AMP
 SG_ CDF_SetMediaVol : 31|5@0+ (1,0) [0|30] "" AMP
 SG_ CDF_SetMute : 26|1@0+ (1,0) [0|1] "" AMP
 SG_ CDF_SetNomiSwitch : 41|1@0+ (1,0) [0|1] "" AMP
 SG_ CDF_SetNomiType : 65|2@0+ (1,0) [0|3] "" AMP
 SG_ CDF_SetNomiVol : 39|5@0+ (1,0) [0|30] "" AMP
 SG_ CDF_SetPEQFrqPoint_1 : 55|6@0+ (1,0) [0|63] "" AMP
 SG_ CDF_SetPEQFrqPoint_10 : 127|6@0+ (1,0) [0|63] "" AMP
 SG_ CDF_SetPEQFrqPoint_2 : 63|6@0+ (1,0) [0|63] "" AMP
 SG_ CDF_SetPEQFrqPoint_3 : 71|6@0+ (1,0) [0|63] "" AMP
 SG_ CDF_SetPEQFrqPoint_4 : 79|6@0+ (1,0) [0|63] "" AMP
 SG_ CDF_SetPEQFrqPoint_5 : 87|6@0+ (1,0) [0|63] "" AMP
 SG_ CDF_SetPEQFrqPoint_6 : 95|6@0+ (1,0) [0|63] "" AMP
 SG_ CDF_SetPEQFrqPoint_7 : 103|6@0+ (1,0) [0|63] "" AMP
 SG_ CDF_SetPEQFrqPoint_8 : 111|6@0+ (1,0) [0|63] "" AMP
 SG_ CDF_SetPEQFrqPoint_9 : 119|6@0+ (1,0) [0|63] "" AMP
 SG_ CDF_SetPEQGain_1 : 163|4@0+ (1,-6) [-6|6] "" AMP
 SG_ CDF_SetPEQGain_10 : 167|4@0+ (1,-6) [-6|6] "" AMP
 SG_ CDF_SetPEQGain_2 : 135|4@0+ (1,-6) [-6|6] "" AMP
 SG_ CDF_SetPEQGain_3 : 131|4@0+ (1,-6) [-6|6] "" AMP
 SG_ CDF_SetPEQGain_4 : 143|4@0+ (1,-6) [-6|6] "" AMP
 SG_ CDF_SetPEQGain_5 : 139|4@0+ (1,-6) [-6|6] "" AMP
 SG_ CDF_SetPEQGain_6 : 151|4@0+ (1,-6) [-6|6] "" AMP
 SG_ CDF_SetPEQGain_7 : 147|4@0+ (1,-6) [-6|6] "" AMP
 SG_ CDF_SetPEQGain_8 : 159|4@0+ (1,-6) [-6|6] "" AMP
 SG_ CDF_SetPEQGain_9 : 155|4@0+ (1,-6) [-6|6] "" AMP
 SG_ CDF_SetPEQSwitch : 40|1@0+ (1,0) [0|1] "" AMP
 SG_ CDF_SetPEQValue_1 : 172|5@0+ (0.2,0.2) [0.2|4] "" AMP
 SG_ CDF_SetPEQValue_10 : 244|5@0+ (0.2,0.2) [0.2|4] "" AMP
 SG_ CDF_SetPEQValue_2 : 180|5@0+ (0.2,0.2) [0.2|4] "" AMP
 SG_ CDF_SetPEQValue_3 : 188|5@0+ (0.2,0.2) [0.2|4] "" AMP
 SG_ CDF_SetPEQValue_4 : 196|5@0+ (0.2,0.2) [0.2|4] "" AMP
 SG_ CDF_SetPEQValue_5 : 204|5@0+ (0.2,0.2) [0.2|4] "" AMP
 SG_ CDF_SetPEQValue_6 : 212|5@0+ (0.2,0.2) [0.2|4] "" AMP
 SG_ CDF_SetPEQValue_7 : 220|5@0+ (0.2,0.2) [0.2|4] "" AMP
 SG_ CDF_SetPEQValue_8 : 228|5@0+ (0.2,0.2) [0.2|4] "" AMP
 SG_ CDF_SetPEQValue_9 : 236|5@0+ (0.2,0.2) [0.2|4] "" AMP
 SG_ CDF_SetPhonePos : 57|2@0+ (1,0) [0|3] "" AMP
 SG_ CDF_SetPhoneSwitch : 24|1@0+ (1,0) [0|1] "" AMP
 SG_ CDF_SetRNCSwitch : 96|1@0+ (1,0) [0|1] "" AMP
 SG_ CDF_SetSoundFiled : 34|3@0+ (1,0) [0|7] "" AMP
 SG_ CDF_SetSoundFiledSwitch : 105|1@0+ (1,0) [0|1] "" AMP
 SG_ CDF_SetWorkMode : 251|4@0+ (1,0) [0|7] "" AMP
        
BO_ 937 ZONE_3A9: 8 ZONE_FTM
 SG_ VehOdometer : 7|24@0+ (1,0) [0|16777215] "km" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
        
BO_ 946 ZONE_3B2: 8 ZONE_FTM
 SG_ Day : 20|5@0+ (1,0) [0|31] "Day" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ Hr : 28|5@0+ (1,0) [0|23] "Hour" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ MSec : 49|10@0+ (1,0) [0|1023] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ Min : 37|6@0+ (1,0) [0|59] "min" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ Mth : 11|4@0+ (1,0) [0|15] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ Sec : 45|6@0+ (1,0) [0|59] "Sec" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ Yr : 7|8@0+ (1,2000) [2000|2254] "Year" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
        
BO_ 1159 HALO_CMD_02: 64 ZONE_FTM
 SG_ HALO_P_01_B : 39|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_01_G : 31|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_01_R : 23|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_01_X : 7|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_01_Y : 15|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_02_B : 79|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_02_G : 71|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_02_R : 63|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_02_X : 47|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_02_Y : 55|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_03_B : 119|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_03_G : 111|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_03_R : 103|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_03_X : 87|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_03_Y : 95|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_04_B : 159|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_04_G : 151|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_04_R : 143|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_04_X : 127|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_04_Y : 135|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_05_B : 199|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_05_G : 191|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_05_R : 183|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_05_X : 167|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_05_Y : 175|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_06_B : 239|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_06_G : 231|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_06_R : 223|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_06_X : 207|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_06_Y : 215|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_07_B : 279|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_07_G : 271|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_07_R : 263|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_07_X : 247|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_07_Y : 255|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_08_B : 319|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_08_G : 311|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_08_R : 303|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_08_X : 287|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_08_Y : 295|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_09_B : 359|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_09_G : 351|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_09_R : 343|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_09_X : 327|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_09_Y : 335|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_10_B : 399|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_10_G : 391|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_10_R : 383|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_10_X : 367|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_10_Y : 375|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_11_B : 439|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_11_G : 431|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_11_R : 423|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_11_X : 407|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_11_Y : 415|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_12_B : 479|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_12_G : 471|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_12_R : 463|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_12_X : 447|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_P_12_Y : 455|8@0+ (1,0) [0|255] "null" NOMI
        
BO_ 1161 HALO_CMD_01: 64 ZONE_FTM
 SG_ HALO_01_01_B : 31|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_01_01_G : 23|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_01_01_R : 15|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_01_02_B : 55|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_01_02_G : 47|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_01_02_R : 39|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_01_03_B : 79|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_01_03_G : 71|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_01_03_R : 63|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_01_04_B : 103|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_01_04_G : 95|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_01_04_R : 87|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_01_05_B : 127|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_01_05_G : 119|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_01_05_R : 111|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_01_06_B : 151|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_01_06_G : 143|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_01_06_R : 135|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_02_01_B : 175|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_02_01_G : 167|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_02_01_R : 159|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_02_02_B : 199|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_02_02_G : 191|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_02_02_R : 183|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_02_03_B : 223|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_02_03_G : 215|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_02_03_R : 207|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_02_04_B : 247|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_02_04_G : 239|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_02_04_R : 231|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_02_05_B : 271|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_02_05_G : 263|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_02_05_R : 255|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_02_06_B : 295|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_02_06_G : 287|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_02_06_R : 279|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_03_01_B : 319|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_03_01_G : 311|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_03_01_R : 303|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_03_02_B : 343|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_03_02_G : 335|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_03_02_R : 327|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_03_03_B : 367|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_03_03_G : 359|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_03_03_R : 351|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_03_04_B : 391|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_03_04_G : 383|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_03_04_R : 375|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_03_05_B : 415|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_03_05_G : 407|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_03_05_R : 399|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_03_06_B : 439|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_03_06_G : 431|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_03_06_R : 423|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_COL_01 : 7|8@0+ (1,0) [0|255] "null" NOMI
 SG_ HALO_OTA_Count : 447|16@0+ (1,0) [0|65535] "null" NOMI
 SG_ HALO_OTA_ID : 463|16@0+ (1,0) [0|65535] "null" NOMI
        
BO_ 1162 MATE_CMD_01: 16 ZONE_FTM
 SG_ CDCCmdAction : 79|8@0+ (1,0) [0|255] "" NOMI
 SG_ CDCCmdP_Dur : 23|16@0+ (1,0) [0|65535] "ms" NOMI
 SG_ CDCCmdP_Ind_I : 63|8@0+ (1,0) [0|100] "" NOMI
 SG_ CDCCmdP_Ind_O : 55|8@0+ (1,0) [0|100] "" NOMI
 SG_ CDCCmdPitch : 71|8@0+ (1,0) [0|40] "Deg" NOMI
 SG_ CDCCmdY_Dur : 7|16@0+ (1,0) [0|65535] "ms" NOMI
 SG_ CDCCmdY_Ind_I : 47|8@0+ (1,0) [0|100] "" NOMI
 SG_ CDCCmdY_Ind_O : 39|8@0+ (1,0) [0|100] "" NOMI
 SG_ CDCCmdYaw : 87|16@0+ (1,-177) [-177|177] "Deg" NOMI
 SG_ CDCFramePCount : 103|8@0+ (1,0) [0|255] "" NOMI
 SG_ CDCFrameYCount : 111|8@0+ (1,0) [0|255] "" NOMI
        
BO_ 1201 AMP_DEVLP_02: 8 ZONE_FTM
 SG_ AMP_Rx_Sig01 : 7|8@0+ (1,0) [0|255] "" AMP
 SG_ AMP_Rx_Sig02 : 15|8@0+ (1,0) [0|255] "" AMP
 SG_ AMP_Rx_Sig03 : 23|8@0+ (1,0) [0|255] "" AMP
 SG_ AMP_Rx_Sig04 : 31|8@0+ (1,0) [0|255] "" AMP
 SG_ AMP_Rx_Sig05 : 39|8@0+ (1,0) [0|255] "" AMP
 SG_ AMP_Rx_Sig06 : 47|8@0+ (1,0) [0|255] "" AMP
 SG_ AMP_Rx_Sig07 : 55|8@0+ (1,0) [0|255] "" AMP
 SG_ AMP_Rx_Sig08 : 63|8@0+ (1,0) [0|255] "" AMP
        
BO_ 1285 NM_ZONE_FTM: 8 ZONE_FTM
 SG_ ActvWakeup_ZONE_FTM : 12|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ NetMngtCoorn_ZONE_FTM : 10|2@0+ (1,0) [0|3] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ NetMngtCoorrSleepRdy_ZONE_FTM : 11|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN10_ZONE_FTM : 25|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN11_ZONE_FTM : 26|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN12_ZONE_FTM : 27|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN13_ZONE_FTM : 28|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN14_ZONE_FTM : 29|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN15_ZONE_FTM : 30|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN16_ZONE_FTM : 31|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN17_ZONE_FTM : 32|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN18_ZONE_FTM : 33|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN19_ZONE_FTM : 34|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN1_ZONE_FTM : 16|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN20_ZONE_FTM : 35|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN21_ZONE_FTM : 36|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN22_ZONE_FTM : 37|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN23_ZONE_FTM : 38|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN24_ZONE_FTM : 39|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN25_ZONE_FTM : 40|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN26_ZONE_FTM : 41|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN27_ZONE_FTM : 42|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN28_ZONE_FTM : 43|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN29_ZONE_FTM : 44|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN2_ZONE_FTM : 17|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN30_ZONE_FTM : 45|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN31_ZONE_FTM : 46|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN32_ZONE_FTM : 47|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN33_ZONE_FTM : 48|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN34_ZONE_FTM : 49|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN35_ZONE_FTM : 50|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN36_ZONE_FTM : 51|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN37_ZONE_FTM : 52|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN38_ZONE_FTM : 53|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN39_ZONE_FTM : 54|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN3_ZONE_FTM : 18|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN40_ZONE_FTM : 55|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN41_ZONE_FTM : 56|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN42_ZONE_FTM : 57|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN43_ZONE_FTM : 58|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN44_ZONE_FTM : 59|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN45_ZONE_FTM : 60|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN46_ZONE_FTM : 61|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN47_ZONE_FTM : 62|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN48_ZONE_FTM : 63|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN4_ZONE_FTM : 19|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN5_ZONE_FTM : 20|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN6_ZONE_FTM : 21|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN7_ZONE_FTM : 22|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN8_ZONE_FTM : 23|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PN9_ZONE_FTM : 24|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ PtlNetInfo_ZONE_FTM : 14|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ RepMsgReq_ZONE_FTM : 8|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ ResdBit1_ZONE_FTM : 13|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ ResdBit2_ZONE_FTM : 15|1@0+ (1,0) [0|1] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ ScrNodId_ZONE_FTM : 7|8@0+ (1,0) [0|255] "" AMP,CNSL_I,FI_R_R,HUD,IC,ICS,NOMI,SWC
        
BO_ 1504 VC_Data_5E0: 8 ZONE_FTM
 SG_ VC_Data_5E0_CRC : 7|8@0+ (1,0) [0|255] "" Vector__XXX
 SG_ VC_Data_5E0_MsgCntr : 11|4@0+ (1,0) [0|14] "" Vector__XXX
 SG_ VC_Data_5E0_Payload : 23|48@0+ (1,0) [0|281474976710655] "" Vector__XXX
        
BO_ 1537 DIAG_REQ_AllECU: 8 ZONE_FTM
 SG_ DiagReqData0_AllECU : 7|8@0+ (1,0) [0|255] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ DiagReqData1_AllECU : 15|8@0+ (1,0) [0|255] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ DiagReqData2_AllECU : 23|8@0+ (1,0) [0|255] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ DiagReqData3_AllECU : 31|8@0+ (1,0) [0|255] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ DiagReqData4_AllECU : 39|8@0+ (1,0) [0|255] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ DiagReqData5_AllECU : 47|8@0+ (1,0) [0|255] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ DiagReqData6_AllECU : 55|8@0+ (1,0) [0|255] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
 SG_ DiagReqData7_AllECU : 63|8@0+ (1,0) [0|255] "" AMP,FI_R_R,HUD,IC,ICS,NOMI,SWC
        
BO_ 1578 DIAG_REQ_SWC: 64 ZONE_FTM
 SG_ DiagReqData0_SWC : 7|32@0+ (1,0) [0|4294967295] "" SWC
 SG_ DiagReqData10_SWC : 327|32@0+ (1,0) [0|4294967295] "" SWC
 SG_ DiagReqData11_SWC : 359|32@0+ (1,0) [0|4294967295] "" SWC
 SG_ DiagReqData12_SWC : 391|32@0+ (1,0) [0|4294967295] "" SWC
 SG_ DiagReqData13_SWC : 423|32@0+ (1,0) [0|4294967295] "" SWC
 SG_ DiagReqData14_SWC : 455|32@0+ (1,0) [0|4294967295] "" SWC
 SG_ DiagReqData15_SWC : 487|32@0+ (1,0) [0|4294967295] "" SWC
 SG_ DiagReqData1_SWC : 39|32@0+ (1,0) [0|4294967295] "" SWC
 SG_ DiagReqData2_SWC : 71|32@0+ (1,0) [0|4294967295] "" SWC
 SG_ DiagReqData3_SWC : 103|32@0+ (1,0) [0|4294967295] "" SWC
 SG_ DiagReqData4_SWC : 135|32@0+ (1,0) [0|4294967295] "" SWC
 SG_ DiagReqData5_SWC : 167|32@0+ (1,0) [0|4294967295] "" SWC
 SG_ DiagReqData6_SWC : 199|32@0+ (1,0) [0|4294967295] "" SWC
 SG_ DiagReqData7_SWC : 231|32@0+ (1,0) [0|4294967295] "" SWC
 SG_ DiagReqData8_SWC : 263|32@0+ (1,0) [0|4294967295] "" SWC
 SG_ DiagReqData9_SWC : 295|32@0+ (1,0) [0|4294967295] "" SWC
        
BO_ 1587 DIAG_REQ_IC: 64 ZONE_FTM
 SG_ DiagReqData0_IC : 7|32@0+ (1,0) [0|4294967295] "" IC
 SG_ DiagReqData10_IC : 327|32@0+ (1,0) [0|4294967295] "" IC
 SG_ DiagReqData11_IC : 359|32@0+ (1,0) [0|4294967295] "" IC
 SG_ DiagReqData12_IC : 391|32@0+ (1,0) [0|4294967295] "" IC
 SG_ DiagReqData13_IC : 423|32@0+ (1,0) [0|4294967295] "" IC
 SG_ DiagReqData14_IC : 455|32@0+ (1,0) [0|4294967295] "" IC
 SG_ DiagReqData15_IC : 487|32@0+ (1,0) [0|4294967295] "" IC
 SG_ DiagReqData1_IC : 39|32@0+ (1,0) [0|4294967295] "" IC
 SG_ DiagReqData2_IC : 71|32@0+ (1,0) [0|4294967295] "" IC
 SG_ DiagReqData3_IC : 103|32@0+ (1,0) [0|4294967295] "" IC
 SG_ DiagReqData4_IC : 135|32@0+ (1,0) [0|4294967295] "" IC
 SG_ DiagReqData5_IC : 167|32@0+ (1,0) [0|4294967295] "" IC
 SG_ DiagReqData6_IC : 199|32@0+ (1,0) [0|4294967295] "" IC
 SG_ DiagReqData7_IC : 231|32@0+ (1,0) [0|4294967295] "" IC
 SG_ DiagReqData8_IC : 263|32@0+ (1,0) [0|4294967295] "" IC
 SG_ DiagReqData9_IC : 295|32@0+ (1,0) [0|4294967295] "" IC
        
BO_ 1589 DIAG_REQ_HUD: 64 ZONE_FTM
 SG_ DiagReqData0_HUD : 7|32@0+ (1,0) [0|4294967295] "" HUD
 SG_ DiagReqData10_HUD : 327|32@0+ (1,0) [0|4294967295] "" HUD
 SG_ DiagReqData11_HUD : 359|32@0+ (1,0) [0|4294967295] "" HUD
 SG_ DiagReqData12_HUD : 391|32@0+ (1,0) [0|4294967295] "" HUD
 SG_ DiagReqData13_HUD : 423|32@0+ (1,0) [0|4294967295] "" HUD
 SG_ DiagReqData14_HUD : 455|32@0+ (1,0) [0|4294967295] "" HUD
 SG_ DiagReqData15_HUD : 487|32@0+ (1,0) [0|4294967295] "" HUD
 SG_ DiagReqData1_HUD : 39|32@0+ (1,0) [0|4294967295] "" HUD
 SG_ DiagReqData2_HUD : 71|32@0+ (1,0) [0|4294967295] "" HUD
 SG_ DiagReqData3_HUD : 103|32@0+ (1,0) [0|4294967295] "" HUD
 SG_ DiagReqData4_HUD : 135|32@0+ (1,0) [0|4294967295] "" HUD
 SG_ DiagReqData5_HUD : 167|32@0+ (1,0) [0|4294967295] "" HUD
 SG_ DiagReqData6_HUD : 199|32@0+ (1,0) [0|4294967295] "" HUD
 SG_ DiagReqData7_HUD : 231|32@0+ (1,0) [0|4294967295] "" HUD
 SG_ DiagReqData8_HUD : 263|32@0+ (1,0) [0|4294967295] "" HUD
 SG_ DiagReqData9_HUD : 295|32@0+ (1,0) [0|4294967295] "" HUD
        
BO_ 1590 DIAG_REQ_ICS: 64 ZONE_FTM
 SG_ DiagReqData0_ICS : 7|32@0+ (1,0) [0|4294967295] "" ICS
 SG_ DiagReqData10_ICS : 327|32@0+ (1,0) [0|4294967295] "" ICS
 SG_ DiagReqData11_ICS : 359|32@0+ (1,0) [0|4294967295] "" ICS
 SG_ DiagReqData12_ICS : 391|32@0+ (1,0) [0|4294967295] "" ICS
 SG_ DiagReqData13_ICS : 423|32@0+ (1,0) [0|4294967295] "" ICS
 SG_ DiagReqData14_ICS : 455|32@0+ (1,0) [0|4294967295] "" ICS
 SG_ DiagReqData15_ICS : 487|32@0+ (1,0) [0|4294967295] "" ICS
 SG_ DiagReqData1_ICS : 39|32@0+ (1,0) [0|4294967295] "" ICS
 SG_ DiagReqData2_ICS : 71|32@0+ (1,0) [0|4294967295] "" ICS
 SG_ DiagReqData3_ICS : 103|32@0+ (1,0) [0|4294967295] "" ICS
 SG_ DiagReqData4_ICS : 135|32@0+ (1,0) [0|4294967295] "" ICS
 SG_ DiagReqData5_ICS : 167|32@0+ (1,0) [0|4294967295] "" ICS
 SG_ DiagReqData6_ICS : 199|32@0+ (1,0) [0|4294967295] "" ICS
 SG_ DiagReqData7_ICS : 231|32@0+ (1,0) [0|4294967295] "" ICS
 SG_ DiagReqData8_ICS : 263|32@0+ (1,0) [0|4294967295] "" ICS
 SG_ DiagReqData9_ICS : 295|32@0+ (1,0) [0|4294967295] "" ICS
        
BO_ 1591 DIAG_REQ_AMP: 64 ZONE_FTM
 SG_ DiagReqData0_AMP : 7|32@0+ (1,0) [0|4294967295] "" AMP
 SG_ DiagReqData10_AMP : 327|32@0+ (1,0) [0|4294967295] "" AMP
 SG_ DiagReqData11_AMP : 359|32@0+ (1,0) [0|4294967295] "" AMP
 SG_ DiagReqData12_AMP : 391|32@0+ (1,0) [0|4294967295] "" AMP
 SG_ DiagReqData13_AMP : 423|32@0+ (1,0) [0|4294967295] "" AMP
 SG_ DiagReqData14_AMP : 455|32@0+ (1,0) [0|4294967295] "" AMP
 SG_ DiagReqData15_AMP : 487|32@0+ (1,0) [0|4294967295] "" AMP
 SG_ DiagReqData1_AMP : 39|32@0+ (1,0) [0|4294967295] "" AMP
 SG_ DiagReqData2_AMP : 71|32@0+ (1,0) [0|4294967295] "" AMP
 SG_ DiagReqData3_AMP : 103|32@0+ (1,0) [0|4294967295] "" AMP
 SG_ DiagReqData4_AMP : 135|32@0+ (1,0) [0|4294967295] "" AMP
 SG_ DiagReqData5_AMP : 167|32@0+ (1,0) [0|4294967295] "" AMP
 SG_ DiagReqData6_AMP : 199|32@0+ (1,0) [0|4294967295] "" AMP
 SG_ DiagReqData7_AMP : 231|32@0+ (1,0) [0|4294967295] "" AMP
 SG_ DiagReqData8_AMP : 263|32@0+ (1,0) [0|4294967295] "" AMP
 SG_ DiagReqData9_AMP : 295|32@0+ (1,0) [0|4294967295] "" AMP
        
BO_ 1595 DIAG_REQ_CNSL_I: 64 ZONE_FTM
 SG_ DiagReqData0_CNSL_I : 7|32@0+ (1,0) [0|4294967295] "" CNSL_I
 SG_ DiagReqData10_CNSL_I : 327|32@0+ (1,0) [0|4294967295] "" CNSL_I
 SG_ DiagReqData11_CNSL_I : 359|32@0+ (1,0) [0|4294967295] "" CNSL_I
 SG_ DiagReqData12_CNSL_I : 391|32@0+ (1,0) [0|4294967295] "" CNSL_I
 SG_ DiagReqData13_CNSL_I : 423|32@0+ (1,0) [0|4294967295] "" CNSL_I
 SG_ DiagReqData14_CNSL_I : 455|32@0+ (1,0) [0|4294967295] "" CNSL_I
 SG_ DiagReqData15_CNSL_I : 487|32@0+ (1,0) [0|4294967295] "" CNSL_I
 SG_ DiagReqData1_CNSL_I : 39|32@0+ (1,0) [0|4294967295] "" CNSL_I
 SG_ DiagReqData2_CNSL_I : 71|32@0+ (1,0) [0|4294967295] "" CNSL_I
 SG_ DiagReqData3_CNSL_I : 103|32@0+ (1,0) [0|4294967295] "" CNSL_I
 SG_ DiagReqData4_CNSL_I : 135|32@0+ (1,0) [0|4294967295] "" CNSL_I
 SG_ DiagReqData5_CNSL_I : 167|32@0+ (1,0) [0|4294967295] "" CNSL_I
 SG_ DiagReqData6_CNSL_I : 199|32@0+ (1,0) [0|4294967295] "" CNSL_I
 SG_ DiagReqData7_CNSL_I : 231|32@0+ (1,0) [0|4294967295] "" CNSL_I
 SG_ DiagReqData8_CNSL_I : 263|32@0+ (1,0) [0|4294967295] "" CNSL_I
 SG_ DiagReqData9_CNSL_I : 295|32@0+ (1,0) [0|4294967295] "" CNSL_I
        
BO_ 1597 DIAG_REQ_NOMI: 64 ZONE_FTM
 SG_ DiagReqData0_NOMI : 7|32@0+ (1,0) [0|4294967295] "" NOMI
 SG_ DiagReqData10_NOMI : 327|32@0+ (1,0) [0|4294967295] "" NOMI
 SG_ DiagReqData11_NOMI : 359|32@0+ (1,0) [0|4294967295] "" NOMI
 SG_ DiagReqData12_NOMI : 391|32@0+ (1,0) [0|4294967295] "" NOMI
 SG_ DiagReqData13_NOMI : 423|32@0+ (1,0) [0|4294967295] "" NOMI
 SG_ DiagReqData14_NOMI : 455|32@0+ (1,0) [0|4294967295] "" NOMI
 SG_ DiagReqData15_NOMI : 487|32@0+ (1,0) [0|4294967295] "" NOMI
 SG_ DiagReqData1_NOMI : 39|32@0+ (1,0) [0|4294967295] "" NOMI
 SG_ DiagReqData2_NOMI : 71|32@0+ (1,0) [0|4294967295] "" NOMI
 SG_ DiagReqData3_NOMI : 103|32@0+ (1,0) [0|4294967295] "" NOMI
 SG_ DiagReqData4_NOMI : 135|32@0+ (1,0) [0|4294967295] "" NOMI
 SG_ DiagReqData5_NOMI : 167|32@0+ (1,0) [0|4294967295] "" NOMI
 SG_ DiagReqData6_NOMI : 199|32@0+ (1,0) [0|4294967295] "" NOMI
 SG_ DiagReqData7_NOMI : 231|32@0+ (1,0) [0|4294967295] "" NOMI
 SG_ DiagReqData8_NOMI : 263|32@0+ (1,0) [0|4294967295] "" NOMI
 SG_ DiagReqData9_NOMI : 295|32@0+ (1,0) [0|4294967295] "" NOMI
        
BO_ 1600 DIAG_REQ_FI_R_R: 64 ZONE_FTM
 SG_ DiagReqData0_FI_R : 7|32@0+ (1,0) [0|4294967295] "" FI_R_R
 SG_ DiagReqData10_FI_R : 327|32@0+ (1,0) [0|4294967295] "" FI_R_R
 SG_ DiagReqData11_FI_R : 359|32@0+ (1,0) [0|4294967295] "" FI_R_R
 SG_ DiagReqData12_FI_R : 391|32@0+ (1,0) [0|4294967295] "" FI_R_R
 SG_ DiagReqData13_FI_R : 423|32@0+ (1,0) [0|4294967295] "" FI_R_R
 SG_ DiagReqData14_FI_R : 455|32@0+ (1,0) [0|4294967295] "" FI_R_R
 SG_ DiagReqData15_FI_R : 487|32@0+ (1,0) [0|4294967295] "" FI_R_R
 SG_ DiagReqData1_FI_R : 39|32@0+ (1,0) [0|4294967295] "" FI_R_R
 SG_ DiagReqData2_FI_R : 71|32@0+ (1,0) [0|4294967295] "" FI_R_R
 SG_ DiagReqData3_FI_R : 103|32@0+ (1,0) [0|4294967295] "" FI_R_R
 SG_ DiagReqData4_FI_R : 135|32@0+ (1,0) [0|4294967295] "" FI_R_R
 SG_ DiagReqData5_FI_R : 167|32@0+ (1,0) [0|4294967295] "" FI_R_R
 SG_ DiagReqData6_FI_R : 199|32@0+ (1,0) [0|4294967295] "" FI_R_R
 SG_ DiagReqData7_FI_R : 231|32@0+ (1,0) [0|4294967295] "" FI_R_R
 SG_ DiagReqData8_FI_R : 263|32@0+ (1,0) [0|4294967295] "" FI_R_R
 SG_ DiagReqData9_FI_R : 295|32@0+ (1,0) [0|4294967295] "" FI_R_R

CM_ SG_ 836 AMP_SetEPMode_Sts "EP Mode Response";
CM_ SG_ 836 AMP_SetEPSwitch_Sts "EP Mode Switch Response";
CM_ SG_ 836 AMP_SetKROKMicType_Sts "KaraOka Type Response";
CM_ SG_ 836 AMP_SetKROKSwitch_Sts "KaraOka Channel Algorithm Bypass Switch Response";
CM_ SG_ 836 AMP_SetKROKVol_Sts "KaraOka Volume Response";
CM_ SG_ 836 AMP_SetMediaFrontMute_Sts "Front Mute Response";
CM_ SG_ 836 AMP_SetMediaPath_Sts "Media Path Response";
CM_ SG_ 836 AMP_SetMediaRearMute_Sts "Rear Mute Response";
CM_ SG_ 836 AMP_SetMediaSwitch_Sts "Media Channel Algorithm Bypass Status Response";
CM_ SG_ 836 AMP_SetMediaType_Sts "Media Type Response";
CM_ SG_ 836 AMP_SetMediaVol_Sts "Media Volume Response";
CM_ SG_ 836 AMP_SetMute_Sts "Whole Vehicle Mute Status Response";
CM_ SG_ 836 AMP_SetNomiSwitch_Sts "Nomi Channel Algorithm Bypass Switch Response";
CM_ SG_ 836 AMP_SetNomiType_Sts "Nomi Type Response";
CM_ SG_ 836 AMP_SetNomiVol_Sts "Nomi Volume Response";
CM_ SG_ 836 AMP_SetPEQFrqPoint_10_Sts "PEQ_10 Frequency Point Response";
CM_ SG_ 836 AMP_SetPEQFrqPoint_1_Sts "PEQ_1 Frequency Point Response";
CM_ SG_ 836 AMP_SetPEQFrqPoint_2_Sts "PEQ_2 Frequency Point Response";
CM_ SG_ 836 AMP_SetPEQFrqPoint_3_Sts "PEQ_3 Frequency Point Response";
CM_ SG_ 836 AMP_SetPEQFrqPoint_4_Sts "PEQ_4 Frequency Point Response";
CM_ SG_ 836 AMP_SetPEQFrqPoint_5_Sts "PEQ_5 Frequency Point Response";
CM_ SG_ 836 AMP_SetPEQFrqPoint_6_Sts "PEQ_6 Frequency Point Response";
CM_ SG_ 836 AMP_SetPEQFrqPoint_7_Sts "PEQ_7 Frequency Point Response";
CM_ SG_ 836 AMP_SetPEQFrqPoint_8_Sts "PEQ_8 Frequency Point Response";
CM_ SG_ 836 AMP_SetPEQFrqPoint_9_Sts "PEQ_9 Frequency Point Response";
CM_ SG_ 836 AMP_SetPEQGain_10_Sts "PEQ_10 Gain Value Responsefactor:1;
offset:-6;
0~12actual value-6~6";
CM_ SG_ 836 AMP_SetPEQGain_1_Sts "PEQ_1 Gain Value Response
factor:1;
offset:-6;
0~12actual value-6~6";
CM_ SG_ 836 AMP_SetPEQGain_2_Sts "PEQ_2 Gain Value Response
factor:1;
offset:-6;
0~12actual value-6~6";
CM_ SG_ 836 AMP_SetPEQGain_3_Sts "PEQ_3 Gain Value Response
factor:1;
offset:-6;
0~12actual value-6~6";
CM_ SG_ 836 AMP_SetPEQGain_4_Sts "PEQ_4 Gain Value Responsefactor:1;
offset:-6;
0~12actual value-6~6";
CM_ SG_ 836 AMP_SetPEQGain_5_Sts "PEQ_5 Gain Value Responsefactor:1;
offset:-6;
0~12actual value-6~6";
CM_ SG_ 836 AMP_SetPEQGain_6_Sts "PEQ_6 Gain Value Response
factor:1;
offset:-6;
0~12actual value-6~6";
CM_ SG_ 836 AMP_SetPEQGain_7_Sts "PEQ_7 Gain Value Response
factor:1;
offset:-6;
0~12actual value-6~6";
CM_ SG_ 836 AMP_SetPEQGain_8_Sts "PEQ_8 Gain Value Response
factor:1;
offset:-6;
0~12actual value-6~6";
CM_ SG_ 836 AMP_SetPEQGain_9_Sts "PEQ_9 Gain Value Response
factor:1;
offset:-6;
0~12actual value-6~6";
CM_ SG_ 836 AMP_SetPEQSwitch_Sts "PEQ Switch Response";
CM_ SG_ 836 AMP_SetPEQValue_10_Sts "PEQ_10 Q Value Response
factor:0.2;
offset:0.2;
0~19actual value0.2~4";
CM_ SG_ 836 AMP_SetPEQValue_1_Sts "PEQ_1 Q Value Response
factor:0.2;
offset:0.2;
0~19actual value0.2~4";
CM_ SG_ 836 AMP_SetPEQValue_2_Sts "PEQ_2 Q Value Response
factor:0.2;
offset:0.2;
0~19actual value0.2~4";
CM_ SG_ 836 AMP_SetPEQValue_3_Sts "PEQ_3 Q Value Response
factor:0.2;
offset:0.2;
0~19actual value0.2~4";
CM_ SG_ 836 AMP_SetPEQValue_4_Sts "PEQ_4 Q Value Response
factor:0.2;
offset:0.2;
0~19actual value0.2~4";
CM_ SG_ 836 AMP_SetPEQValue_5_Sts "PEQ_5 Q Value Response
factor:0.2;
offset:0.2;
0~19actual value0.2~4";
CM_ SG_ 836 AMP_SetPEQValue_6_Sts "PEQ_6 Q Value Response
factor:0.2;
offset:0.2;
0~19actual value0.2~4";
CM_ SG_ 836 AMP_SetPEQValue_7_Sts "PEQ_7 Q Value Response
factor:0.2;
offset:0.2;
0~19actual value0.2~4";
CM_ SG_ 836 AMP_SetPEQValue_8_Sts "PEQ_8 Q Value Response
factor:0.2;
offset:0.2;
0~19actual value0.2~4";
CM_ SG_ 836 AMP_SetPEQValue_9_Sts "PEQ_9 Q Value Response;
factor:0.2;
offset:0.2;
0~19actual value0.2~4";
CM_ SG_ 836 AMP_SetPhonePos_Sts "Phone Position Response";
CM_ SG_ 836 AMP_SetPhoneSwitch_Sts "Phone Channel Algorithm Bypass Switch Response";
CM_ SG_ 836 AMP_SetRNCSwitch_Sts "RNC Switch Response";
CM_ SG_ 836 AMP_SetSoundFiledSwitch_Sts "Sound Filed Switch Response";
CM_ SG_ 836 AMP_SetSoundFiled_Sts "Set Sound Filed Response";
CM_ SG_ 836 AMP_SetWorkMode_Sts "AMP Work Mode Response";
CM_ SG_ 917 A2B1_TDM_Lost_Times "A2B1 TDM lost times";
CM_ SG_ 917 A2B1_TDM_State "A2B1 TDM Status";
CM_ SG_ 917 A2B2_TDM_Lost_Times "A2B2 TDM lost times";
CM_ SG_ 917 A2B2_TDM_State "A2B2 TDM Status";
CM_ SG_ 917 AMP_Dsp1HeartBeat "DSP1 heartbeat";
CM_ SG_ 917 AMP_Dsp2HeartBeat "DSP2 heartbeat";
CM_ SG_ 917 AMP_Dsp3HeartBeat "DSP3 heartbeat";
CM_ SG_ 917 Audio_status "Audio Amplifier Initialization Status";
CM_ SG_ 917 Chip_10_comm_status "Amplifier chip 10 Communication Status";
CM_ SG_ 917 Chip_10_status "Amplifier chip 10 Status";
CM_ SG_ 917 Chip_1_comm_status "Amplifier chip 1 Communication Status";
CM_ SG_ 917 Chip_1_status "Amplifier chip 1 Status";
CM_ SG_ 917 Chip_2_comm_status "Amplifier chip 2 Communication Status";
CM_ SG_ 917 Chip_2_status "Amplifier chip 2 Status";
CM_ SG_ 917 Chip_3_comm_status "Amplifier chip 3 Communication Status";
CM_ SG_ 917 Chip_3_status "Amplifier chip 3 Status";
CM_ SG_ 917 Chip_4_comm_status "Amplifier chip 4 Communication Status";
CM_ SG_ 917 Chip_4_status "Amplifier chip 4 Status";
CM_ SG_ 917 Chip_5_comm_status "Amplifier chip 5 Communication Status";
CM_ SG_ 917 Chip_5_status "Amplifier chip 5 Status";
CM_ SG_ 917 Chip_6_comm_status "Amplifier chip 6 Communication Status";
CM_ SG_ 917 Chip_6_status "Amplifier chip 6 Status";
CM_ SG_ 917 Chip_7_comm_status "Amplifier chip 7 Communication Status";
CM_ SG_ 917 Chip_7_status "Amplifier chip 7 Status";
CM_ SG_ 917 Chip_8_comm_status "Amplifier chip 8 Communication Status";
CM_ SG_ 917 Chip_8_status "Amplifier chip 8 Status";
CM_ SG_ 917 Chip_9_comm_status "Amplifier chip 9 Communication Status";
CM_ SG_ 917 Chip_9_status "Amplifier chip 9 Status";
CM_ SG_ 917 DAP_Ver "DAP Version";
CM_ SG_ 917 DSP1_Ver "DSP1 Version";
CM_ SG_ 917 DSP2_Ver "DSP2 Version";
CM_ SG_ 917 DSP3_Ver "DSP3 Version";
CM_ SG_ 917 MCU_App_Ver "MCUAPP Version";
CM_ SG_ 917 MCU_AutoSar_Ver "MCUAutoSar Version";
CM_ SG_ 917 MCU_Mcal_Ver "MCUMcal Version";
CM_ SG_ 918 DSP1_STS "DSP1 Internal Status";
CM_ SG_ 918 DSP1_TEMP "DSP1 Temperature Status";
CM_ SG_ 918 DSP2_STS "DSP2 Internal Status";
CM_ SG_ 918 DSP2_TEMP "DSP2 Temperature Status";
CM_ SG_ 918 DSP3_STS "DSP3 Internal Status";
CM_ SG_ 918 DSP3_TEMP "DSP3 Temperature Status";
CM_ SG_ 918 InputVolt "Input Voltage";
CM_ SG_ 918 NCU_TEMP "MCU Temperature Status";
CM_ SG_ 1200 AMP_Tx_Sig01 "AMP_TX_Sig01 for development and calibration  ";
CM_ SG_ 1200 AMP_Tx_Sig02 "AMP_TX_Sig02 for development and calibration  ";
CM_ SG_ 1200 AMP_Tx_Sig03 "AMP_TX_Sig03 for development and calibration  ";
CM_ SG_ 1200 AMP_Tx_Sig04 "AMP_TX_Sig04 for development and calibration  ";
CM_ SG_ 1200 AMP_Tx_Sig05 "AMP_TX_Sig05 for development and calibration  ";
CM_ SG_ 1200 AMP_Tx_Sig06 "AMP_TX_Sig06 for development and calibration  ";
CM_ SG_ 1200 AMP_Tx_Sig07 "AMP_TX_Sig07 for development and calibration  ";
CM_ SG_ 1200 AMP_Tx_Sig08 "AMP_TX_Sig08 for development and calibration  ";
CM_ SG_ 1335 ActvWakeup_AMP "Control bit vector AMP Active wakeup - Reserved";
CM_ SG_ 1335 NetMngtCoorn_AMP "Control bit vector AMP Network management coordination - Reserved";
CM_ SG_ 1335 NetMngtCoorrSleepRdy_AMP "Control bit vector AMP Network management coordinator sleep ready - Reserved";
CM_ SG_ 1335 PtlNetInfo_AMP "Control bit vector AMP Partial network information - Reserved";
CM_ SG_ 1335 RepMsgReq_AMP "Control bit vector AMP Repeat Message Request";
CM_ SG_ 1335 ResdBit1_AMP "Control bit vector AMP Reserved";
CM_ SG_ 1335 ResdBit2_AMP "Control bit vector AMP Reserved";
CM_ SG_ 1335 ScrNodId_AMP "Source node identifier AMP";
CM_ SG_ 1719 DiagRespData0_AMP "Diagnostic response data 0 AMP   ";
CM_ SG_ 1719 DiagRespData10_AMP "Diagnostic response data 10 AMP   ";
CM_ SG_ 1719 DiagRespData11_AMP "Diagnostic response data 11 AMP   ";
CM_ SG_ 1719 DiagRespData12_AMP "Diagnostic response data 12 AMP   ";
CM_ SG_ 1719 DiagRespData13_AMP "Diagnostic response data 13 AMP   ";
CM_ SG_ 1719 DiagRespData14_AMP "Diagnostic response data 14 AMP   ";
CM_ SG_ 1719 DiagRespData15_AMP "Diagnostic response data 15 AMP   ";
CM_ SG_ 1719 DiagRespData1_AMP "Diagnostic response data 1 AMP   ";
CM_ SG_ 1719 DiagRespData2_AMP "Diagnostic response data 2 AMP   ";
CM_ SG_ 1719 DiagRespData3_AMP "Diagnostic response data 3 AMP   ";
CM_ SG_ 1719 DiagRespData4_AMP "Diagnostic response data 4 AMP   ";
CM_ SG_ 1719 DiagRespData5_AMP "Diagnostic response data 5 AMP   ";
CM_ SG_ 1719 DiagRespData6_AMP "Diagnostic response data 6 AMP   ";
CM_ SG_ 1719 DiagRespData7_AMP "Diagnostic response data 7 AMP   ";
CM_ SG_ 1719 DiagRespData8_AMP "Diagnostic response data 8 AMP   ";
CM_ SG_ 1719 DiagRespData9_AMP "Diagnostic response data 9 AMP   ";
CM_ SG_ 456 CNSL_I_LeftBtn_LongPress "CNSL_I left button long press event";
CM_ SG_ 456 CNSL_I_MidTp_LongPress "CNSL_I middle tp long press event";
CM_ SG_ 456 CNSL_I_RightBtn_LongPress "CNSL_I left button long press event";
CM_ SG_ 659 CNSL_I_EnableSwSt "CNSL_I enable/disable status";
CM_ SG_ 659 CNSL_I_OpModeSt "CNSL_I current operate mode";
CM_ SG_ 659 CNSL_I_SensSt "CNSL_I current  touch sensetive level";
CM_ SG_ 659 CNSL_I_St "CNSL_I status";
CM_ SG_ 659 CNSL_I_VibDeltalSt "CNSL_I current  vibrate trigger delta level";
CM_ SG_ 659 CNSL_I_VibFbSt "CNSL_I vibrator switch status";
CM_ SG_ 1178 CNSL_I_LeftBtn_Press "CNSL_I left button press event";
CM_ SG_ 1178 CNSL_I_LeftBtn_SwipeDown "CNSL_I left button swipe down event";
CM_ SG_ 1178 CNSL_I_LeftBtn_SwipeUp "CNSL_I left button swipe up event";
CM_ SG_ 1178 CNSL_I_LeftBtn_TouchCordData "CNSL_I left button touch coordinate data
bit[0-15]: x-coordinate data
bit[16-31]: y-coordinate data";
CM_ SG_ 1178 CNSL_I_LeftBtn_TouchRawData "CNSL_I left button touch raw data
bit[0-15]: x-raw data
bit[16-31]: y-raw data";
CM_ SG_ 1178 CNSL_I_LeftBtn_TouchSt "CNSL_I left button touch status";
CM_ SG_ 1178 CNSL_I_MidTp_2Finger_SwipeDown "CNSL_I middle tp swipe down event with 2 findgers";
CM_ SG_ 1178 CNSL_I_MidTp_2Finger_SwipeLeft "CNSL_I middle tp swipe left event with 2 findgers";
CM_ SG_ 1178 CNSL_I_MidTp_2Finger_SwipeRight "CNSL_I middle tp swipe left event with 2 findgers";
CM_ SG_ 1178 CNSL_I_MidTp_2Finger_SwipeUp "CNSL_I middle tp swipe up event with 2 findgers";
CM_ SG_ 1178 CNSL_I_MidTp_3Finger_SwipeDown "CNSL_I middle tp swipe down event with 3 findgers";
CM_ SG_ 1178 CNSL_I_MidTp_3Finger_SwipeLeft "CNSL_I middle tp swipe left event with 3 findgers";
CM_ SG_ 1178 CNSL_I_MidTp_3Finger_SwipeRight "CNSL_I middle tp swipe left event with 3 findgers";
CM_ SG_ 1178 CNSL_I_MidTp_3Finger_SwipeUp "CNSL_I middle tp swipe up event with 3 findgers";
CM_ SG_ 1178 CNSL_I_MidTp_F1_TouchRawData_F1 "CNSL_I middle tp touch raw data with single finger
bit[0-15]: x-raw data
bit[16-31]: y-raw data";
CM_ SG_ 1178 CNSL_I_MidTp_Press "CNSL_I middle tp press event";
CM_ SG_ 1178 CNSL_I_MidTp_SwipeDown "CNSL_I middle tp swipe down event";
CM_ SG_ 1178 CNSL_I_MidTp_SwipeLeft "CNSL_I middle tp swipe left event";
CM_ SG_ 1178 CNSL_I_MidTp_SwipeRight "CNSL_I middle tp swipe left event";
CM_ SG_ 1178 CNSL_I_MidTp_SwipeUp "CNSL_I middle tp swipe up event";
CM_ SG_ 1178 CNSL_I_MidTp_TouchCordData_F1 "CNSL_I middle tp touch coordinate data with single finger
bit[0-15]: x-coordinate data
bit[16-31]: y-coordinate data";
CM_ SG_ 1178 CNSL_I_MidTp_TouchCordData_F2 "CNSL_I middle tp touch coordinate data with two fingers
bit[0-15]: x-coordinate data
bit[16-31]: y-coordinate data";
CM_ SG_ 1178 CNSL_I_MidTp_TouchRawData_F2 "CNSL_I middle tp touch raw data with two fingers
bit[0-15]: x-raw data
bit[16-31]: y-raw data";
CM_ SG_ 1178 CNSL_I_MidTp_TouchSt_F1 "CNSL_I middle tp touch status";
CM_ SG_ 1178 CNSL_I_MidTp_TouchSt_F2 "CNSL_I middle tp touch status with two fingers";
CM_ SG_ 1178 CNSL_I_RightBtn_Press "CNSL_I left button press event";
CM_ SG_ 1178 CNSL_I_RightBtn_SwipeDown "CNSL_I left button swipe down event";
CM_ SG_ 1178 CNSL_I_RightBtn_SwipeUp "CNSL_I left button swipe up event";
CM_ SG_ 1178 CNSL_I_RightBtn_TouchCordData "CNSL_I left button touch coordinate data
bit[0-15]: x-coordinate data
bit[16-31]: y-coordinate data";
CM_ SG_ 1178 CNSL_I_RightBtn_TouchRawData "CNSL_I left button touch raw data
bit[0-15]: x-raw data
bit[16-31]: y-raw data";
CM_ SG_ 1178 CNSL_I_RightBtn_TouchSt "CNSL_I left button touch status";
CM_ SG_ 1339 ActvWakeup_CNSL_I "Control bit vector CNSL_I Active wakeup - Reserved";
CM_ SG_ 1339 NetMngtCoorn_CNSL_I "Control bit vector CNSL_I Network management coordination - Reserved";
CM_ SG_ 1339 NetMngtCoorrSleepRdy_CNSL_I "Control bit vector CNSL_I Network management coordinator sleep ready - Reserved";
CM_ SG_ 1339 PtlNetInfo_CNSL_I "Control bit vector CNSL_I Partial network information - Reserved";
CM_ SG_ 1339 RepMsgReq_CNSL_I "Control bit vector CNSL_I Repeat Message Request";
CM_ SG_ 1339 ResdBit1_CNSL_I "Control bit vector CNSL_I Reserved";
CM_ SG_ 1339 ResdBit2_CNSL_I "Control bit vector CNSL_I Reserved";
CM_ SG_ 1339 ScrNodId_CNSL_I "Source node identifier CNSL_I";
CM_ SG_ 1723 DiagRespData0_CNSL_I "Diagnostic response data 0 CNSL_I";
CM_ SG_ 1723 DiagRespData10_CNSL_I "Diagnostic response data 10 CNSL_I";
CM_ SG_ 1723 DiagRespData11_CNSL_I "Diagnostic response data 11 CNSL_I";
CM_ SG_ 1723 DiagRespData12_CNSL_I "Diagnostic response data 12 CNSL_I";
CM_ SG_ 1723 DiagRespData13_CNSL_I "Diagnostic response data 13 CNSL_I";
CM_ SG_ 1723 DiagRespData14_CNSL_I "Diagnostic response data 14 CNSL_I";
CM_ SG_ 1723 DiagRespData15_CNSL_I "Diagnostic response data 15 CNSL_I";
CM_ SG_ 1723 DiagRespData1_CNSL_I "Diagnostic response data 1 CNSL_I";
CM_ SG_ 1723 DiagRespData2_CNSL_I "Diagnostic response data 2 CNSL_I";
CM_ SG_ 1723 DiagRespData3_CNSL_I "Diagnostic response data 3 CNSL_I";
CM_ SG_ 1723 DiagRespData4_CNSL_I "Diagnostic response data 4 CNSL_I";
CM_ SG_ 1723 DiagRespData5_CNSL_I "Diagnostic response data 5 CNSL_I";
CM_ SG_ 1723 DiagRespData6_CNSL_I "Diagnostic response data 6 CNSL_I";
CM_ SG_ 1723 DiagRespData7_CNSL_I "Diagnostic response data 7 CNSL_I";
CM_ SG_ 1723 DiagRespData8_CNSL_I "Diagnostic response data 8 CNSL_I";
CM_ SG_ 1723 DiagRespData9_CNSL_I "Diagnostic response data 9 CNSL_I";
CM_ SG_ 804 FI_R_WarningRsp_LockSts "FI_R warning/failure lvds lock status";
CM_ SG_ 1344 ActvWakeup_FI_RR "Control bit vector FI_RR Active wakeup - Reserved";
CM_ SG_ 1344 NetMngtCoorn_FI_RR "Control bit vector FI_RR Network management coordination - Reserved";
CM_ SG_ 1344 NetMngtCoorrSleepRdy_FI_RR "Control bit vector FI_RR Network management coordinator sleep ready - Reserved";
CM_ SG_ 1344 PtlNetInfo_FI_RR "Control bit vector FI_RR Partial network information - Reserved";
CM_ SG_ 1344 RepMsgReq_FI_RR "Control bit vector FI_RR Repeat Message Request";
CM_ SG_ 1344 ResdBit1_FI_RR "Control bit vector FI_RR Reserved";
CM_ SG_ 1344 ResdBit2_FI_RR "Control bit vector FI_RR Reserved";
CM_ SG_ 1344 ScrNodId_FI_RR "Source node identifier FI_RR";
CM_ SG_ 1728 DiagRespData0_FI_R "Diagnostic response data 0 FI_R   ";
CM_ SG_ 1728 DiagRespData10_FI_R "Diagnostic response data 10 FI_R   ";
CM_ SG_ 1728 DiagRespData11_FI_R "Diagnostic response data 11 FI_R   ";
CM_ SG_ 1728 DiagRespData12_FI_R "Diagnostic response data 12 FI_R   ";
CM_ SG_ 1728 DiagRespData13_FI_R "Diagnostic response data 13 FI_R   ";
CM_ SG_ 1728 DiagRespData14_FI_R "Diagnostic response data 14 FI_R   ";
CM_ SG_ 1728 DiagRespData15_FI_R "Diagnostic response data 15 FI_R   ";
CM_ SG_ 1728 DiagRespData1_FI_R "Diagnostic response data 1 FI_R   ";
CM_ SG_ 1728 DiagRespData2_FI_R "Diagnostic response data 2 FI_R   ";
CM_ SG_ 1728 DiagRespData3_FI_R "Diagnostic response data 3 FI_R   ";
CM_ SG_ 1728 DiagRespData4_FI_R "Diagnostic response data 4 FI_R   ";
CM_ SG_ 1728 DiagRespData5_FI_R "Diagnostic response data 5 FI_R   ";
CM_ SG_ 1728 DiagRespData6_FI_R "Diagnostic response data 6 FI_R   ";
CM_ SG_ 1728 DiagRespData7_FI_R "Diagnostic response data 7 FI_R   ";
CM_ SG_ 1728 DiagRespData8_FI_R "Diagnostic response data 8 FI_R   ";
CM_ SG_ 1728 DiagRespData9_FI_R "Diagnostic response data 9 FI_R   ";
CM_ SG_ 776 HUD_HeiSts "HUD height status";
CM_ SG_ 776 HUD_LCDbackLiSts "HUD display brigthness status";
CM_ SG_ 776 HUD_Sts "HUD status    ";
CM_ SG_ 776 HUD_WarningRsp_BriLimd "HUD warning/failure Brightness Limited";
CM_ SG_ 776 HUD_WarningRsp_LockSts "HUD  warning/failure lvds lock status";
CM_ SG_ 776 HUD_WarningRsp_MotFail "HUD warning/failure Motor Fail";
CM_ SG_ 776 HUD_WarningRsp_TFT_LEDFail "HUD warning/failure TFT/LED Fail";
CM_ SG_ 776 HUD_WarningRsp_Thi "HUD warning/failure TemperatureHigh";
CM_ SG_ 1333 ActvWakeup_HUD "Control bit vector HUD Active wakeup - Reserved";
CM_ SG_ 1333 NetMngtCoorn_HUD "Control bit vector HUD Network management coordination - Reserved";
CM_ SG_ 1333 NetMngtCoorrSleepRdy_HUD "Control bit vector HUD Network management coordinator sleep ready - Reserved";
CM_ SG_ 1333 PtlNetInfo_HUD "Control bit vector HUD Partial network information - Reserved";
CM_ SG_ 1333 RepMsgReq_HUD "Control bit vector HUD Repeat Message Request";
CM_ SG_ 1333 ResdBit1_HUD "Control bit vector HUD Reserved";
CM_ SG_ 1333 ResdBit2_HUD "Control bit vector HUD Reserved";
CM_ SG_ 1333 ScrNodId_HUD "Source node identifier HUD";
CM_ SG_ 1717 DiagRespData0_HUD "Diagnostic response data 0 HUD   ";
CM_ SG_ 1717 DiagRespData10_HUD "Diagnostic response data 10 HUD   ";
CM_ SG_ 1717 DiagRespData11_HUD "Diagnostic response data 11 HUD   ";
CM_ SG_ 1717 DiagRespData12_HUD "Diagnostic response data 12 HUD   ";
CM_ SG_ 1717 DiagRespData13_HUD "Diagnostic response data 13 HUD   ";
CM_ SG_ 1717 DiagRespData14_HUD "Diagnostic response data 14 HUD   ";
CM_ SG_ 1717 DiagRespData15_HUD "Diagnostic response data 15 HUD   ";
CM_ SG_ 1717 DiagRespData1_HUD "Diagnostic response data 1 HUD   ";
CM_ SG_ 1717 DiagRespData2_HUD "Diagnostic response data 2 HUD   ";
CM_ SG_ 1717 DiagRespData3_HUD "Diagnostic response data 3 HUD   ";
CM_ SG_ 1717 DiagRespData4_HUD "Diagnostic response data 4 HUD   ";
CM_ SG_ 1717 DiagRespData5_HUD "Diagnostic response data 5 HUD   ";
CM_ SG_ 1717 DiagRespData6_HUD "Diagnostic response data 6 HUD   ";
CM_ SG_ 1717 DiagRespData7_HUD "Diagnostic response data 7 HUD   ";
CM_ SG_ 1717 DiagRespData8_HUD "Diagnostic response data 8 HUD   ";
CM_ SG_ 1717 DiagRespData9_HUD "Diagnostic response data 9 HUD   ";
CM_ SG_ 741 IC_LCDbackLiSts_L "Instrument cluster LCD backlight status";
CM_ SG_ 741 IC_LCDbackLiSts_R "Instrument cluster LCD backlight status";
CM_ SG_ 741 IC_Sts "IC working status";
CM_ SG_ 741 IC_WarningRsp_BriLimt_L "IC warning/failure Brightness limited";
CM_ SG_ 741 IC_WarningRsp_BriLimt_R "IC warning/failure Brightness limited";
CM_ SG_ 741 IC_WarningRsp_LCDFail_L "IC warning/failure LCD_Fail Detect";
CM_ SG_ 741 IC_WarningRsp_LCDFail_R "IC warning/failure LCD_Fail Detect";
CM_ SG_ 741 IC_WarningRsp_LedDrvr_L "IC warning/failure LED Driver Fail";
CM_ SG_ 741 IC_WarningRsp_LedDrvr_R "IC warning/failure LED Driver Fail";
CM_ SG_ 741 IC_WarningRsp_LocalDimmingFail_L "IC warning/failure Local Dimming Fail";
CM_ SG_ 741 IC_WarningRsp_LocalDimmingFail_R "IC warning/failure Local Dimming Fail";
CM_ SG_ 741 IC_WarningRsp_LockSts "IC warning/failure lvds lock status";
CM_ SG_ 741 IC_WarningRsp_Thi_L "IC warning/failure  Temperature high";
CM_ SG_ 741 IC_WarningRsp_Thi_R "IC warning/failure  Temperature high";
CM_ SG_ 741 IC_WarningRsp_UHiLo_L "IC warning/failure Voltage High/Low";
CM_ SG_ 741 IC_WarningRsp_UHiLo_R "IC warning/failure Voltage High/Low";
CM_ SG_ 1331 ActvWakeup_IC "Control bit vector IC Active wakeup - Reserved";
CM_ SG_ 1331 NetMngtCoorn_IC "Control bit vector IC Network management coordination - Reserved";
CM_ SG_ 1331 NetMngtCoorrSleepRdy_IC "Control bit vector IC Network management coordinator sleep ready - Reserved";
CM_ SG_ 1331 PtlNetInfo_IC "Control bit vector IC Partial network information - Reserved";
CM_ SG_ 1331 RepMsgReq_IC "Control bit vector IC Repeat Message Request";
CM_ SG_ 1331 ResdBit1_IC "Control bit vector IC Reserved";
CM_ SG_ 1331 ResdBit2_IC "Control bit vector IC Reserved";
CM_ SG_ 1331 ScrNodId_IC "Source node identifier IC";
CM_ SG_ 1715 DiagRespData0_IC "Diagnostic response data 0 IC   ";
CM_ SG_ 1715 DiagRespData10_IC "Diagnostic response data 10 IC   ";
CM_ SG_ 1715 DiagRespData11_IC "Diagnostic response data 11 IC   ";
CM_ SG_ 1715 DiagRespData12_IC "Diagnostic response data 12 IC   ";
CM_ SG_ 1715 DiagRespData13_IC "Diagnostic response data 13 IC   ";
CM_ SG_ 1715 DiagRespData14_IC "Diagnostic response data 14 IC   ";
CM_ SG_ 1715 DiagRespData15_IC "Diagnostic response data 15 IC   ";
CM_ SG_ 1715 DiagRespData1_IC "Diagnostic response data 1 IC   ";
CM_ SG_ 1715 DiagRespData2_IC "Diagnostic response data 2 IC   ";
CM_ SG_ 1715 DiagRespData3_IC "Diagnostic response data 3 IC   ";
CM_ SG_ 1715 DiagRespData4_IC "Diagnostic response data 4 IC   ";
CM_ SG_ 1715 DiagRespData5_IC "Diagnostic response data 5 IC   ";
CM_ SG_ 1715 DiagRespData6_IC "Diagnostic response data 6 IC   ";
CM_ SG_ 1715 DiagRespData7_IC "Diagnostic response data 7 IC   ";
CM_ SG_ 1715 DiagRespData8_IC "Diagnostic response data 8 IC   ";
CM_ SG_ 1715 DiagRespData9_IC "Diagnostic response data 9 IC   ";
CM_ SG_ 755 ICS_BrightnessSts "ICS backlight status";
CM_ SG_ 755 ICS_Sts "ICS  working status";
CM_ SG_ 755 ICS_WarningRsp_BriLimt "ICS warning/failure Brightness limited";
CM_ SG_ 755 ICS_WarningRsp_Thi "ICS warning/failure Temperature high";
CM_ SG_ 755 ICS_WarningRsp_TouchErr "ICS warning/failure Touch Error";
CM_ SG_ 755 ICS_WarningRsp_UHiLo "ICS warning/failure Voltage High/Low";
CM_ SG_ 1334 ActvWakeup_ICS "Control bit vector ICS Active wakeup - Reserved";
CM_ SG_ 1334 NetMngtCoorn_ICS "Control bit vector ICS Network management coordination - Reserved";
CM_ SG_ 1334 NetMngtCoorrSleepRdy_ICS "Control bit vector ICS Network management coordinator sleep ready - Reserved";
CM_ SG_ 1334 PtlNetInfo_ICS "Control bit vector ICS Partial network information - Reserved";
CM_ SG_ 1334 RepMsgReq_ICS "Control bit vector ICS Repeat Message Request";
CM_ SG_ 1334 ResdBit1_ICS "Control bit vector ICS Reserved";
CM_ SG_ 1334 ResdBit2_ICS "Control bit vector ICS Reserved";
CM_ SG_ 1334 ScrNodId_ICS "Source node identifier ICS";
CM_ SG_ 1718 DiagRespData0_ICS "Diagnostic response data 0 ICS   ";
CM_ SG_ 1718 DiagRespData10_ICS "Diagnostic response data 10 ICS   ";
CM_ SG_ 1718 DiagRespData11_ICS "Diagnostic response data 11 ICS   ";
CM_ SG_ 1718 DiagRespData12_ICS "Diagnostic response data 12 ICS   ";
CM_ SG_ 1718 DiagRespData13_ICS "Diagnostic response data 13 ICS   ";
CM_ SG_ 1718 DiagRespData14_ICS "Diagnostic response data 14 ICS   ";
CM_ SG_ 1718 DiagRespData15_ICS "Diagnostic response data 15 ICS   ";
CM_ SG_ 1718 DiagRespData1_ICS "Diagnostic response data 1 ICS   ";
CM_ SG_ 1718 DiagRespData2_ICS "Diagnostic response data 2 ICS   ";
CM_ SG_ 1718 DiagRespData3_ICS "Diagnostic response data 3 ICS   ";
CM_ SG_ 1718 DiagRespData4_ICS "Diagnostic response data 4 ICS   ";
CM_ SG_ 1718 DiagRespData5_ICS "Diagnostic response data 5 ICS   ";
CM_ SG_ 1718 DiagRespData6_ICS "Diagnostic response data 6 ICS   ";
CM_ SG_ 1718 DiagRespData7_ICS "Diagnostic response data 7 ICS   ";
CM_ SG_ 1718 DiagRespData8_ICS "Diagnostic response data 8 ICS   ";
CM_ SG_ 1718 DiagRespData9_ICS "Diagnostic response data 9 ICS   ";
CM_ SG_ 453 HALO_WORK_STATUS "Halo Work Status";
CM_ SG_ 453 MATEPosPitch "Degree of MATE tilt real time";
CM_ SG_ 453 MATEPosYaw "Degree of MATE pan real time";
CM_ SG_ 453 NOMI_BackLiSts "Brightness level of NOMI";
CM_ SG_ 453 NOMI_Sts "NOMI Status";
CM_ SG_ 453 NOMI_Type "NOMI Type";
CM_ SG_ 453 NOMI_WarningRsp "NOMI Warning Response

bit0: Motor temperature warning
bit1: Motor over temperature
bit2: Display panel or LEDs over temperature
bit3: Base Motor error
bit4: Head motor error
bit5: Screen error
bit6: LED Driver error
bit7: Lock Status";
CM_ SG_ 576 MateRespNfcUid0 "32 bytes nfc uid 0";
CM_ SG_ 576 MateRespNfcUid1 "32 bytes nfc uid 1";
CM_ SG_ 576 MateRespNfcUid2 "32 bytes nfc uid 2";
CM_ SG_ 576 MateRespNfcUid3 "32 bytes nfc uid 3";
CM_ SG_ 576 MateRespNfcUid4 "32 bytes nfc uid 4";
CM_ SG_ 576 MateRespNfcUid5 "32 bytes nfc uid 5";
CM_ SG_ 576 MateRespNfcUid6 "32 bytes nfc uid 6";
CM_ SG_ 576 MateRespNfcUid7 "32 bytes nfc uid 7";
CM_ SG_ 576 MateRespNioUid0 "32 bytes nio uid 0";
CM_ SG_ 576 MateRespNioUid1 "32 bytes nio uid 1";
CM_ SG_ 576 MateRespNioUid2 "32 bytes nio uid 2";
CM_ SG_ 576 MateRespNioUid3 "32 bytes nio uid 3";
CM_ SG_ 576 MateRespNioUid4 "32 bytes nio uid 4";
CM_ SG_ 576 MateRespNioUid5 "32 bytes nio uid 5";
CM_ SG_ 576 MateRespNioUid6 "32 bytes nio uid 6";
CM_ SG_ 576 MateRespNioUid7 "32 bytes nio uid 7";
CM_ SG_ 1163 MATEFrameRPCount "A properly updated frame count generally indicates that the message is not simply a repeat of a previous message but has been processed by the transmitter software since transmission of the previous message.";
CM_ SG_ 1163 MATEFrameRYCount "A properly updated frame count generally indicates that the message is not simply a repeat of a previous message but has been processed by the transmitter software since transmission of the previous message.";
CM_ SG_ 1163 MATERespFramePAck "MATE Command Pitch Ack";
CM_ SG_ 1163 MATERespFrameYAck "MATE Command Yaw Ack";
CM_ SG_ 1163 MateRespCmdActionCount "A properly updated frame count generally indicates that the message is not simply a repeat of a previous message but has been processed by the transmitter software since transmission of the previous message. ";
CM_ SG_ 1169 HALO_RSP_CODE "HALO CMD 01 error code";
CM_ SG_ 1169 HALO_RSP_Count "A properly updated frame count generally indicates that the message is not simply a repeat of a previous message but has been processed by the transmitter software since transmission of the previous message.";
CM_ SG_ 1169 HALO_RSP_ID "HALO CMD 01 Response OTA ID";
CM_ SG_ 1341 ActvWakeup_NOMI "Control bit vector NOMI Active wakeup - Reserved";
CM_ SG_ 1341 NetMngtCoorn_NOMI "Control bit vector NOMI Network management coordination - Reserved";
CM_ SG_ 1341 NetMngtCoorrSleepRdy_NOMI "Control bit vector NOMI Network management coordinator sleep ready - Reserved";
CM_ SG_ 1341 PtlNetInfo_NOMI "Control bit vector NOMI Partial network information - Reserved";
CM_ SG_ 1341 RepMsgReq_NOMI "Control bit vector NOMI Repeat Message Request";
CM_ SG_ 1341 ResdBit1_NOMI "Control bit vector NOMI Reserved";
CM_ SG_ 1341 ResdBit2_NOMI "Control bit vector NOMI Reserved";
CM_ SG_ 1341 ScrNodId_NOMI "Source node identifier NOMI";
CM_ SG_ 1725 DiagRespData0_NOMI "Diagnostic response data 0 NOMI";
CM_ SG_ 1725 DiagRespData10_NOMI "Diagnostic response data 10 NOMI   ";
CM_ SG_ 1725 DiagRespData11_NOMI "Diagnostic response data 11 NOMI   ";
CM_ SG_ 1725 DiagRespData12_NOMI "Diagnostic response data 12 NOMI   ";
CM_ SG_ 1725 DiagRespData13_NOMI "Diagnostic response data 13 NOMI   ";
CM_ SG_ 1725 DiagRespData14_NOMI "Diagnostic response data 14 NOMI   ";
CM_ SG_ 1725 DiagRespData15_NOMI "Diagnostic response data 15 NOMI   ";
CM_ SG_ 1725 DiagRespData1_NOMI "Diagnostic response data 1 NOMI";
CM_ SG_ 1725 DiagRespData2_NOMI "Diagnostic response data 2 NOMI";
CM_ SG_ 1725 DiagRespData3_NOMI "Diagnostic response data 3 NOMI";
CM_ SG_ 1725 DiagRespData4_NOMI "Diagnostic response data 4 NOMI";
CM_ SG_ 1725 DiagRespData5_NOMI "Diagnostic response data 5 NOMI";
CM_ SG_ 1725 DiagRespData6_NOMI "Diagnostic response data 6 NOMI";
CM_ SG_ 1725 DiagRespData7_NOMI "Diagnostic response data 7 NOMI";
CM_ SG_ 1725 DiagRespData8_NOMI "Diagnostic response data 8 NOMI   ";
CM_ SG_ 1725 DiagRespData9_NOMI "Diagnostic response data 9 NOMI   ";
CM_ SG_ 671 MenuPushSwtSts "Menu push switch  ";
CM_ SG_ 671 NOMICtrlPushSwtSts "NOMI (Voice control) push switch status    ";
CM_ SG_ 671 Spare01PushSwtSts "Spare 01 push switch Status";
CM_ SG_ 77 SWCRi_BarPos "finger position on touch bar";
CM_ SG_ 1322 ActvWakeup_SWC "Control bit vector SWC Active wakeup - Reserved";
CM_ SG_ 1322 NetMngtCoorn_SWC "Control bit vector SWC Network management coordination - Reserved";
CM_ SG_ 1322 NetMngtCoorrSleepRdy_SWC "Control bit vector SWC Network management coordinator sleep ready - Reserved";
CM_ SG_ 1322 PtlNetInfo_SWC "Control bit vector SWC Partial network information - Reserved";
CM_ SG_ 1322 RepMsgReq_SWC "Control bit vector SWC Repeat Message Request";
CM_ SG_ 1322 ResdBit1_SWC "Control bit vector SWC Reserved";
CM_ SG_ 1322 ResdBit2_SWC "Control bit vector SWC Reserved";
CM_ SG_ 1322 ScrNodId_SWC "Source node identifier SWC";
CM_ SG_ 1706 DiagRespData0_SWC "Diagnostic response data 0 SWC   ";
CM_ SG_ 1706 DiagRespData10_SWC "Diagnostic response data 10 SWC   ";
CM_ SG_ 1706 DiagRespData11_SWC "Diagnostic response data 11 SWC   ";
CM_ SG_ 1706 DiagRespData12_SWC "Diagnostic response data 12 SWC   ";
CM_ SG_ 1706 DiagRespData13_SWC "Diagnostic response data 13 SWC   ";
CM_ SG_ 1706 DiagRespData14_SWC "Diagnostic response data 14 SWC   ";
CM_ SG_ 1706 DiagRespData15_SWC "Diagnostic response data 15 SWC   ";
CM_ SG_ 1706 DiagRespData1_SWC "Diagnostic response data 1 SWC   ";
CM_ SG_ 1706 DiagRespData2_SWC "Diagnostic response data 2 SWC   ";
CM_ SG_ 1706 DiagRespData3_SWC "Diagnostic response data 3 SWC   ";
CM_ SG_ 1706 DiagRespData4_SWC "Diagnostic response data 4 SWC   ";
CM_ SG_ 1706 DiagRespData5_SWC "Diagnostic response data 5 SWC   ";
CM_ SG_ 1706 DiagRespData6_SWC "Diagnostic response data 6 SWC   ";
CM_ SG_ 1706 DiagRespData7_SWC "Diagnostic response data 7 SWC   ";
CM_ SG_ 1706 DiagRespData8_SWC "Diagnostic response data 8 SWC   ";
CM_ SG_ 1706 DiagRespData9_SWC "Diagnostic response data 9 SWC   ";
CM_ SG_ 286 BCU_11E_CRC "AUTOSAR E2E profile1, CRC";
CM_ SG_ 286 BCU_11E_MsgCntr "AUTOSAR E2E profile1, Message Counter";
CM_ SG_ 286 VehSpdSts_Rdnt1 "Vehicle speed validity and initialization status";
CM_ SG_ 286 VehSpd_Rdnt1 "Vehicle speed. Invalid values 1901 - 1FFF";
CM_ SG_ 371 AccrPedlActPosn "Accelerator pedal actual position";
CM_ SG_ 371 LiSnsrData "Light sensor";
CM_ SG_ 371 LiSnsrFailSts "Light sensor fail status";
CM_ SG_ 371 PEURMotSpd "Motor Speed rear";
CM_ SG_ 371 VCUActGear "VCU actual gear";
CM_ BO_ 549 "This command is transmitted to interrupt NOMI current operation.";
CM_ SG_ 549 HALO_AIR_QUALITY "Indicate the air quality level. valid when HALO_NOMI_STATUS is Need Filt Air(20)";
CM_ SG_ 549 HALO_AMBIENT_LIGHT "Indicate the target direction of halo animation. valid when HALO_WORK_SELECTOR is Adjust ambient light";
CM_ SG_ 549 HALO_CHARGING_DIRECTION "Indicate the target direction of halo animation. valid when HALO_WORK_SELECTOR is Remind risk(XXX)
0-360 Degree";
CM_ SG_ 549 HALO_LISTENING_DIRECTION "Indicate the listening direction of halo animation. valid when the HALO_WORK_SELECTOR is Listening(3)";
CM_ SG_ 549 HALO_LISTENING_VOLUME "Indicate the user voice volume percent of halo animation. valid when the HALO_WORK_SELECTOR is Listening(3)
0- 100 : Volume Percent
101-127 : Reserved";
CM_ SG_ 549 HALO_MUSIC_BPM "Indicate the sync music bmp value of halo animation. valid when HALO_WORK_SELECTOR is Music sync (15)
0- 100 : BPM Value
101-127 : Reserved";
CM_ SG_ 549 HALO_MUSIC_BURST_VOLUME "Indicate the sync music burst volume value of nomi animation,valid when HALO_WORK_SELECTOR is Music sync (15)
0- 100 : Burst Value
101-127 : Reserved";
CM_ SG_ 549 HALO_MUSIC_GENRE "Indicate the sync music genre value of halo animation. valid when HALO_WORK_SELECTOR is Music sync (15)";
CM_ SG_ 549 HALO_OBSTACLE_DIRECTION "Indicate the obstacle direction. valid when the HALO_WORK_SELECTOR is Indicating distance(XXX)
0 - 360 : direction clockwise angle value";
CM_ SG_ 549 HALO_OBSTACLE_DISTANCE "Indicate the obstacle distance. valid when the HALO_WORK_SELECTOR is Indicating distance(XXX)";
CM_ SG_ 549 HALO_REMIND_RISK "Remind risk source";
CM_ SG_ 549 HALO_TALKING_DIRECTION "Indicate the talking direction of halo animation. valid when HALO_WORK_SELECTOR is Talking (5)";
CM_ SG_ 549 HALO_TALKING_VOLUME "Indicate the talking volume percent of Halo animation. valid when the HALO_WORK_SELECTOR is Talking (5)
0- 100 : Volume Percent
101-127 : Reserved";
CM_ SG_ 549 HALO_WORK_MODE "Halo Light Effect Work Mode";
CM_ SG_ 549 HALO_WORK_SELECTOR "CDF notifies the current work status to Halo NOMI";
CM_ SG_ 549 NOMI_BackLiReq "Brightness level request for MATE Screen/HALO Light";
CM_ SG_ 549 NOMI_OnOffCmd "NOMI On/Off Command on special case (Defender/PSAP Mode)";
CM_ SG_ 549 NOMI_VideoSrcReq "Brightness On/Off request for MATE Screen/HALO Light";
CM_ SG_ 648 CNSL_I_EnableSwReq "CNSL_I enable/disable switch";
CM_ SG_ 648 CNSL_I_OpModeReq "CNSL_I operate mode setting";
CM_ SG_ 648 CNSL_I_SensReq "CNSL_I touch sensetive level setting";
CM_ SG_ 648 CNSL_I_VibDeltaReq "CNSL_I vibrate trigger delta level setting";
CM_ SG_ 648 CNSL_I_VibFbReq "CNSL_I vibrator switch";
CM_ SG_ 648 ECOPlusModSts "ECO plus mode state";
CM_ SG_ 648 FI_R_ResetReq "FI_R reset request from CDC";
CM_ SG_ 648 HUD_Cmd "HUD on/off request";
CM_ SG_ 648 HUD_LCDbackLiReq "HUD display brigthness";
CM_ SG_ 648 HUD_ResetReq "HUD reset request from CDC";
CM_ SG_ 648 ICS_BrightnessReq "Brightness request";
CM_ SG_ 648 ICS_OnReq "ICS on request from CDC";
CM_ SG_ 648 IC_LCDbackLiReq_L "Instrument cluster LCD backlight request";
CM_ SG_ 648 IC_LCDbackLiReq_R "Instrument cluster LCD backlight request";
CM_ SG_ 648 IC_ResetReq_L "IC reset request from CDC";
CM_ SG_ 648 IC_ResetReq_R "IC reset request from CDC";
CM_ SG_ 648 IC_VideoSrcReq_L "CDC LVDS video source request";
CM_ SG_ 648 IC_VideoSrcReq_R "CDC LVDS video source request";
CM_ SG_ 648 PositionReq "Head Up Display (HUD) position setting";
CM_ SG_ 747 CDF_SetBrake "Set Brake Status";
CM_ SG_ 747 CDF_SetGear "Set Gear";
CM_ SG_ 747 CDF_SetPedal "Set Pedal Status";
CM_ SG_ 747 CDF_SetRotSpeed "Set Rot Speed";
CM_ SG_ 747 CDF_SetTorqueFront "Front Torque";
CM_ SG_ 747 CDF_SetTorqueRear "Rear Torque";
CM_ SG_ 747 CDF_SetVehSpeed "Whole Vehicle Speed";
CM_ SG_ 747 CompressorSpd "Compressor Speed";
CM_ SG_ 747 Envtemp "Temperature (Environment)";
CM_ SG_ 747 Hvac2ndAutoSts "The Second HVAC Auto Status";
CM_ SG_ 747 Hvac2ndBlwSpd "The Second HVAC Blower Speed";
CM_ SG_ 747 Hvac2ndBlwSpdAutoSts "The Second HVAC Blower Speed Auto Status";
CM_ SG_ 747 Hvac2ndFanDirAutoSts "The Second HVAC Dir Auto Status";
CM_ SG_ 747 Hvac2ndFanDirFaceSts "The Second HVAC Dir Face Status";
CM_ SG_ 747 Hvac2ndFanDirFloorSts "The Second HVAC Dir Floor  Status";
CM_ SG_ 747 Hvac2ndOnOffSts "The Second HVAC On/Off Status";
CM_ SG_ 747 Hvac3rdAutoSts "The Third HVAC Auto Status";
CM_ SG_ 747 Hvac3rdBlwSpd "The Third HVAC Blower Speed";
CM_ SG_ 747 Hvac3rdBlwSpdAutoSts "The Third HVAC Blower Speed Auto Status";
CM_ SG_ 747 Hvac3rdFanDirAutoSts "The Third HVAC Dir Auto Status";
CM_ SG_ 747 Hvac3rdFanDirFaceSts "The Third HVAC Dir Face Status";
CM_ SG_ 747 Hvac3rdFanDirFloorSts "The Third HVAC Dir Floor  Status";
CM_ SG_ 747 Hvac3rdOnOffSts "The Third HVAC On/Off Status";
CM_ SG_ 747 HvacAcSts "HVAC AC Status";
CM_ SG_ 747 HvacAutoSts "HVAC Main Auto Status";
CM_ SG_ 747 HvacBlwSpd "HVAC Blower Speed";
CM_ SG_ 747 HvacBlwSpdAutoSts "HVAC  Blower Speed Auto Status";
CM_ SG_ 747 HvacFanDirAutoSts "HVAC Dir Auto Status";
CM_ SG_ 747 HvacFanDirFaceSts "HVAC Dir Face Status";
CM_ SG_ 747 HvacFanDirFloorSts "HVAC Dir Floor Status";
CM_ SG_ 747 HvacFrntDefogSts "HVAC Front Defrog Status";
CM_ SG_ 747 HvacFrntRiAutoSts "Front Right HVAC Auto Status";
CM_ SG_ 747 HvacFrntRiBlwSpd "Front Right HVAC Blower Speed";
CM_ SG_ 747 HvacFrntRiBlwSpdAutoSts "Front Right HVAC Blower Speed Auto Status";
CM_ SG_ 747 HvacFrntRiFanDirAutoSts "Front Right HVAC Dir Auto Status";
CM_ SG_ 747 HvacFrntRiFanDirFaceSts "Front Right HVAC Dir Face Status";
CM_ SG_ 747 HvacFrntRiFanDirFloorSts "Front Right HVAC Dir Floor  Status";
CM_ SG_ 747 HvacFrntRiOnOffSts "Front Right HVAC On/Off Status";
CM_ SG_ 747 HvacMaxAcSts "HVAC MAX AC Status";
CM_ SG_ 747 HvacMaxDefogSts "HVAC Max Defrog Status";
CM_ SG_ 747 HvacMaxHtSts "HVAC MAX Heat Status";
CM_ SG_ 747 HvacOnOffSts "HVAC Main On/Off Status";
CM_ SG_ 747 HvacRearDefogSts "HVAC Rear Defrog Status";
CM_ SG_ 747 HvacRecSts "HVAC Cycle Status";
CM_ SG_ 747 IntrTemp "Temperature (In-Cabin)";
CM_ SG_ 747 LidarSpd "Lidar Speed";
CM_ SG_ 747 SeatFrntLeFAPosn "Seats Position (Fore-Aft)";
CM_ SG_ 747 SeatFrntLeHdrPosn "Seats Position (Headrest)";
CM_ SG_ 747 SeatFrntLeRecPosn "Seats Position (Recline)";
CM_ SG_ 747 SeatFrntLeUPPosn "Seats Position (Up-Down)";
CM_ SG_ 747 SunroofPosn "Sunroof Status";
CM_ SG_ 747 TailgateSts "Tailgate Status";
CM_ SG_ 747 TpmsFrntRiWhlPress "front right wheel pressure status";
CM_ SG_ 747 VehicleOnVoltage "Vehicle On Voltage/Off";
CM_ SG_ 747 WinFrntLePosn "0-100:0-100
101-126:reserved
127:unknow";
CM_ SG_ 747 WinFrntRiPosn "0-100:0-100
101-126:reserved
127:unknow";
CM_ SG_ 747 WinReLePosn "0-100:0-100
101-126:reserved
127:unknow";
CM_ SG_ 747 WinReRiPosn "0-100:0-100
101-126:reserved
127:unknow";
CM_ SG_ 781 CDF_SetEPMode "Set EP Mode";
CM_ SG_ 781 CDF_SetEPSwitch "Set EP Switch";
CM_ SG_ 781 CDF_SetKROKMicType "KaraOka Type";
CM_ SG_ 781 CDF_SetKROKSwitch "KaraOka Channel Algorithm Bypass Switch";
CM_ SG_ 781 CDF_SetKROKVol "Set KaraOka Volume";
CM_ SG_ 781 CDF_SetMediaFrontMute "Front Mute";
CM_ SG_ 781 CDF_SetMediaPath "Media Path Selection";
CM_ SG_ 781 CDF_SetMediaRearMute "Rear Mute";
CM_ SG_ 781 CDF_SetMediaSwitch "Media Channel Algorithm Bypass Switch";
CM_ SG_ 781 CDF_SetMediaType "Media Channe Type";
CM_ SG_ 781 CDF_SetMediaVol "Set Media Volume";
CM_ SG_ 781 CDF_SetMute "Whole Vehicle Mute";
CM_ SG_ 781 CDF_SetNomiSwitch "NOMI Channel Algorithm Bypass Switch";
CM_ SG_ 781 CDF_SetNomiType "Nomi ltype";
CM_ SG_ 781 CDF_SetNomiVol "Set Nomi Volume";
CM_ SG_ 781 CDF_SetPEQFrqPoint_1 "PEQ_1 Frequency Point";
CM_ SG_ 781 CDF_SetPEQFrqPoint_10 "PEQ_10 Frequency Point";
CM_ SG_ 781 CDF_SetPEQFrqPoint_2 "PEQ_2 Frequency Point";
CM_ SG_ 781 CDF_SetPEQFrqPoint_3 "PEQ_3 Frequency Point";
CM_ SG_ 781 CDF_SetPEQFrqPoint_4 "PEQ_4 Frequency Point";
CM_ SG_ 781 CDF_SetPEQFrqPoint_5 "PEQ_5 Frequency Point";
CM_ SG_ 781 CDF_SetPEQFrqPoint_6 "PEQ_6 Frequency Point";
CM_ SG_ 781 CDF_SetPEQFrqPoint_7 "PEQ_7 Frequency Point";
CM_ SG_ 781 CDF_SetPEQFrqPoint_8 "PEQ_8 Frequency Point";
CM_ SG_ 781 CDF_SetPEQFrqPoint_9 "PEQ_9 Frequency Point";
CM_ SG_ 781 CDF_SetPEQGain_1 "PEQ_1 Gain Value";
CM_ SG_ 781 CDF_SetPEQGain_10 "PEQ_10 Gain Value";
CM_ SG_ 781 CDF_SetPEQGain_2 "PEQ_2 Gain Value";
CM_ SG_ 781 CDF_SetPEQGain_3 "PEQ_3 Gain Value";
CM_ SG_ 781 CDF_SetPEQGain_4 "PEQ_4 Gain Value";
CM_ SG_ 781 CDF_SetPEQGain_5 "PEQ_5 Gain Value";
CM_ SG_ 781 CDF_SetPEQGain_6 "PEQ_6 Gain Value";
CM_ SG_ 781 CDF_SetPEQGain_7 "PEQ_7 Gain Value";
CM_ SG_ 781 CDF_SetPEQGain_8 "PEQ_8 Gain Value";
CM_ SG_ 781 CDF_SetPEQGain_9 "PEQ_9 Gain Value";
CM_ SG_ 781 CDF_SetPEQSwitch "PEQ Switch";
CM_ SG_ 781 CDF_SetPEQValue_1 "PEQ_1 Q Value";
CM_ SG_ 781 CDF_SetPEQValue_10 "PEQ_10 Q Value";
CM_ SG_ 781 CDF_SetPEQValue_2 "PEQ_2 Q Value";
CM_ SG_ 781 CDF_SetPEQValue_3 "PEQ_3 Q Value";
CM_ SG_ 781 CDF_SetPEQValue_4 "PEQ_4 Q Value";
CM_ SG_ 781 CDF_SetPEQValue_5 "PEQ_5 Q Value";
CM_ SG_ 781 CDF_SetPEQValue_6 "PEQ_6 Q Value";
CM_ SG_ 781 CDF_SetPEQValue_7 "PEQ_7 Q Value";
CM_ SG_ 781 CDF_SetPEQValue_8 "PEQ_8 Q Value";
CM_ SG_ 781 CDF_SetPEQValue_9 "PEQ_9 Q Value";
CM_ SG_ 781 CDF_SetPhonePos "Set Phone Position";
CM_ SG_ 781 CDF_SetPhoneSwitch "Phone Channel Algorithm Bypass Switch";
CM_ SG_ 781 CDF_SetRNCSwitch "RNC Switch";
CM_ SG_ 781 CDF_SetSoundFiled "Set Sound Filed";
CM_ SG_ 781 CDF_SetSoundFiledSwitch "Set Sound Filed Switch";
CM_ SG_ 781 CDF_SetWorkMode "Set AMP Work Mode";
CM_ SG_ 937 VehOdometer "Vehicle odometerInvalid value FFFFFF";
CM_ SG_ 1159 HALO_P_01_B "HALO LED Blue for 1st Point";
CM_ SG_ 1159 HALO_P_01_G "HALO LED Green for 1st Point";
CM_ SG_ 1159 HALO_P_01_R "HALO LED Red for 1st Point";
CM_ SG_ 1159 HALO_P_01_X "HALO LED Array 1st Point X
0-95";
CM_ SG_ 1159 HALO_P_01_Y "HALO LED Array 1st Point Y
0-5";
CM_ SG_ 1159 HALO_P_02_B "HALO LED Blue for 2nd Point";
CM_ SG_ 1159 HALO_P_02_G "HALO LED Green for 2nd Point";
CM_ SG_ 1159 HALO_P_02_R "HALO LED Red for 2nd Point";
CM_ SG_ 1159 HALO_P_02_X "HALO LED Array 2nt Point X
0-95";
CM_ SG_ 1159 HALO_P_02_Y "HALO LED Array 2nd Point Y
0-5";
CM_ SG_ 1159 HALO_P_03_X "0-95";
CM_ SG_ 1159 HALO_P_03_Y "HALO LED Array 3rd Point Y
0-5";
CM_ SG_ 1159 HALO_P_04_X "0-95";
CM_ SG_ 1159 HALO_P_04_Y "HALO LED Array 4th Point Y
0-5";
CM_ SG_ 1159 HALO_P_05_X "0-95";
CM_ SG_ 1159 HALO_P_05_Y "HALO LED Array 5th Point Y
0-5";
CM_ SG_ 1159 HALO_P_06_X "0-95";
CM_ SG_ 1159 HALO_P_06_Y "HALO LED Array 6th Point Y
0-5";
CM_ SG_ 1159 HALO_P_07_X "0-95";
CM_ SG_ 1159 HALO_P_07_Y "HALO LED Array 7th Point Y
0-5";
CM_ SG_ 1159 HALO_P_08_X "0-95";
CM_ SG_ 1159 HALO_P_08_Y "HALO LED Array 8th Point Y
0-5";
CM_ SG_ 1159 HALO_P_09_X "0-95";
CM_ SG_ 1159 HALO_P_09_Y "HALO LED Array 9th Point Y
0-5";
CM_ SG_ 1159 HALO_P_10_X "0-95";
CM_ SG_ 1159 HALO_P_10_Y "HALO LED Array 10th Point Y
0-5";
CM_ SG_ 1159 HALO_P_11_X "0-95";
CM_ SG_ 1159 HALO_P_11_Y "HALO LED Array 11th Point Y
0-5";
CM_ SG_ 1159 HALO_P_12_X "0-95";
CM_ SG_ 1159 HALO_P_12_Y "HALO LED Array 12th Point Y
0-5";
CM_ SG_ 1161 HALO_01_01_B "HALO LED Blue for point (0+n , 0)";
CM_ SG_ 1161 HALO_01_01_G "HALO LED Green for point (0+n, 0)";
CM_ SG_ 1161 HALO_01_01_R "HALO LED Red for point (0+n, 0)";
CM_ SG_ 1161 HALO_COL_01 "HALO LED Array Column 0+n
0,3,6,9,,93";
CM_ SG_ 1161 HALO_OTA_Count "A properly updated frame count generally indicates that the message is not simply a repeat of a previous message but has been processed by the transmitter software since transmission of the previous message.";
CM_ SG_ 1161 HALO_OTA_ID "HALO Light Effect ID";
CM_ SG_ 1162 CDCCmdAction "MATE Kinetics Command Action";
CM_ SG_ 1162 CDCCmdP_Dur "Total duration the tilt to accomplish movement, in milliseconds";
CM_ SG_ 1162 CDCCmdP_Ind_I "percentage of acceleration time";
CM_ SG_ 1162 CDCCmdP_Ind_O "percentage of deceleration time";
CM_ SG_ 1162 CDCCmdPitch "Absolute degree the tilt will move, negative values mean turning down, positive values mean turning up.";
CM_ SG_ 1162 CDCCmdY_Dur "Total duration the panto accomplish movement, in
milliseconds";
CM_ SG_ 1162 CDCCmdY_Ind_I "percentage of acceleration time";
CM_ SG_ 1162 CDCCmdY_Ind_O "percentage of deceleration time";
CM_ SG_ 1162 CDCCmdYaw "Absolute degree the pan will move, negative values mean turning
right, positive values mean turning left.";
CM_ SG_ 1162 CDCFramePCount "A properly updated frame count generally indicates that the message is not simply a repeat of a previous message but has been processed by the transmitter software since transmission of the previous message.";
CM_ SG_ 1162 CDCFrameYCount "A properly updated frame count generally indicates that the message is not simply a repeat of a previous message but has been processed by the transmitter software since transmission of the previous message.";
CM_ SG_ 1201 AMP_Rx_Sig01 "AMP_RX_Sig01 for development and calibration  ";
CM_ SG_ 1201 AMP_Rx_Sig02 "AMP_RX_Sig02 for development and calibration  ";
CM_ SG_ 1201 AMP_Rx_Sig03 "AMP_RX_Sig03 for development and calibration  ";
CM_ SG_ 1201 AMP_Rx_Sig04 "AMP_RX_Sig04 for development and calibration  ";
CM_ SG_ 1201 AMP_Rx_Sig05 "AMP_RX_Sig05 for development and calibration  ";
CM_ SG_ 1201 AMP_Rx_Sig06 "AMP_RX_Sig06 for development and calibration  ";
CM_ SG_ 1201 AMP_Rx_Sig07 "AMP_RX_Sig07 for development and calibration  ";
CM_ SG_ 1201 AMP_Rx_Sig08 "AMP_RX_Sig08 for development and calibration  ";
CM_ SG_ 1285 ActvWakeup_ZONE_FTM "Control bit vector ZONE_FTM Active wakeup - Reserved";
CM_ SG_ 1285 NetMngtCoorn_ZONE_FTM "Control bit vector ZONE_FTM Network management coordination - Reserved";
CM_ SG_ 1285 NetMngtCoorrSleepRdy_ZONE_FTM "Control bit vector ZONE_FTM Network management coordinator sleep ready - Reserved";
CM_ SG_ 1285 PtlNetInfo_ZONE_FTM "Control bit vector ZONE_FTM Partial network information - Reserved";
CM_ SG_ 1285 RepMsgReq_ZONE_FTM "Control bit vector ZONE_FTM Repeat Message Request";
CM_ SG_ 1285 ResdBit1_ZONE_FTM "Control bit vector ZONE_FTM Reserved";
CM_ SG_ 1285 ResdBit2_ZONE_FTM "Control bit vector ZONE_FTM Reserved";
CM_ SG_ 1285 ScrNodId_ZONE_FTM "Source node identifier ZONE_FTM";
CM_ SG_ 1578 DiagReqData0_SWC "Diagnostic request data 0 SWC   ";
CM_ SG_ 1578 DiagReqData10_SWC "Diagnostic request data 10 SWC   ";
CM_ SG_ 1578 DiagReqData11_SWC "Diagnostic request data 11 SWC   ";
CM_ SG_ 1578 DiagReqData12_SWC "Diagnostic request data 12 SWC   ";
CM_ SG_ 1578 DiagReqData13_SWC "Diagnostic request data 13 SWC   ";
CM_ SG_ 1578 DiagReqData14_SWC "Diagnostic request data 14 SWC   ";
CM_ SG_ 1578 DiagReqData15_SWC "Diagnostic request data 15 SWC   ";
CM_ SG_ 1578 DiagReqData1_SWC "Diagnostic request data 1 SWC   ";
CM_ SG_ 1578 DiagReqData2_SWC "Diagnostic request data 2 SWC   ";
CM_ SG_ 1578 DiagReqData3_SWC "Diagnostic request data 3 SWC   ";
CM_ SG_ 1578 DiagReqData4_SWC "Diagnostic request data 4 SWC   ";
CM_ SG_ 1578 DiagReqData5_SWC "Diagnostic request data 5 SWC   ";
CM_ SG_ 1578 DiagReqData6_SWC "Diagnostic request data 6 SWC   ";
CM_ SG_ 1578 DiagReqData7_SWC "Diagnostic request data 7 SWC   ";
CM_ SG_ 1578 DiagReqData8_SWC "Diagnostic request data 8 SWC   ";
CM_ SG_ 1578 DiagReqData9_SWC "Diagnostic request data 9 SWC   ";
CM_ SG_ 1587 DiagReqData0_IC "Diagnostic request data 0 IC";
CM_ SG_ 1587 DiagReqData10_IC "Diagnostic request data 10 IC";
CM_ SG_ 1587 DiagReqData11_IC "Diagnostic request data 11 IC";
CM_ SG_ 1587 DiagReqData12_IC "Diagnostic request data 12 IC";
CM_ SG_ 1587 DiagReqData13_IC "Diagnostic request data 13 IC";
CM_ SG_ 1587 DiagReqData14_IC "Diagnostic request data 14 IC";
CM_ SG_ 1587 DiagReqData15_IC "Diagnostic request data 15 IC";
CM_ SG_ 1587 DiagReqData1_IC "Diagnostic request data 1 IC";
CM_ SG_ 1587 DiagReqData2_IC "Diagnostic request data 2 IC";
CM_ SG_ 1587 DiagReqData3_IC "Diagnostic request data 3 IC";
CM_ SG_ 1587 DiagReqData4_IC "Diagnostic request data 4 IC";
CM_ SG_ 1587 DiagReqData5_IC "Diagnostic request data 5 IC";
CM_ SG_ 1587 DiagReqData6_IC "Diagnostic request data 6 IC";
CM_ SG_ 1587 DiagReqData7_IC "Diagnostic request data 7 IC";
CM_ SG_ 1587 DiagReqData8_IC "Diagnostic request data 8 IC";
CM_ SG_ 1587 DiagReqData9_IC "Diagnostic request data 9 IC";
CM_ SG_ 1589 DiagReqData0_HUD "Diagnostic request data 0 HUD   ";
CM_ SG_ 1589 DiagReqData10_HUD "Diagnostic request data 10 HUD";
CM_ SG_ 1589 DiagReqData11_HUD "Diagnostic request data 11 HUD";
CM_ SG_ 1589 DiagReqData12_HUD "Diagnostic request data 12 HUD";
CM_ SG_ 1589 DiagReqData13_HUD "Diagnostic request data 13 HUD";
CM_ SG_ 1589 DiagReqData14_HUD "Diagnostic request data 14 HUD";
CM_ SG_ 1589 DiagReqData15_HUD "Diagnostic request data 15 HUD";
CM_ SG_ 1589 DiagReqData1_HUD "Diagnostic request data 1 HUD   ";
CM_ SG_ 1589 DiagReqData2_HUD "Diagnostic request data 2 HUD   ";
CM_ SG_ 1589 DiagReqData3_HUD "Diagnostic request data 3 HUD   ";
CM_ SG_ 1589 DiagReqData4_HUD "Diagnostic request data 4 HUD   ";
CM_ SG_ 1589 DiagReqData5_HUD "Diagnostic request data 5 HUD   ";
CM_ SG_ 1589 DiagReqData6_HUD "Diagnostic request data 6 HUD   ";
CM_ SG_ 1589 DiagReqData7_HUD "Diagnostic request data 7 HUD   ";
CM_ SG_ 1589 DiagReqData8_HUD "Diagnostic request data 8 HUD";
CM_ SG_ 1589 DiagReqData9_HUD "Diagnostic request data 9 HUD";
CM_ SG_ 1590 DiagReqData0_ICS "Diagnostic request data 0 ICS";
CM_ SG_ 1590 DiagReqData10_ICS "Diagnostic request data 10 ICS   ";
CM_ SG_ 1590 DiagReqData11_ICS "Diagnostic request data 11 ICS   ";
CM_ SG_ 1590 DiagReqData12_ICS "Diagnostic request data 12 ICS   ";
CM_ SG_ 1590 DiagReqData13_ICS "Diagnostic request data 13 ICS   ";
CM_ SG_ 1590 DiagReqData14_ICS "Diagnostic request data 14 ICS   ";
CM_ SG_ 1590 DiagReqData15_ICS "Diagnostic request data 15 ICS   ";
CM_ SG_ 1590 DiagReqData1_ICS "Diagnostic request data 1 ICS";
CM_ SG_ 1590 DiagReqData2_ICS "Diagnostic request data 2 ICS";
CM_ SG_ 1590 DiagReqData3_ICS "Diagnostic request data 3 ICS";
CM_ SG_ 1590 DiagReqData4_ICS "Diagnostic request data 4 ICS";
CM_ SG_ 1590 DiagReqData5_ICS "Diagnostic request data 5 ICS";
CM_ SG_ 1590 DiagReqData6_ICS "Diagnostic request data 6 ICS";
CM_ SG_ 1590 DiagReqData7_ICS "Diagnostic request data 7 ICS";
CM_ SG_ 1590 DiagReqData8_ICS "Diagnostic request data 8 ICS   ";
CM_ SG_ 1590 DiagReqData9_ICS "Diagnostic request data 9 ICS   ";
CM_ SG_ 1591 DiagReqData0_AMP "Diagnostic request data 0 AMP   ";
CM_ SG_ 1591 DiagReqData10_AMP "Diagnostic request data 10 AMP   ";
CM_ SG_ 1591 DiagReqData11_AMP "Diagnostic request data 11 AMP   ";
CM_ SG_ 1591 DiagReqData12_AMP "Diagnostic request data 12 AMP   ";
CM_ SG_ 1591 DiagReqData13_AMP "Diagnostic request data 13 AMP   ";
CM_ SG_ 1591 DiagReqData14_AMP "Diagnostic request data 14 AMP   ";
CM_ SG_ 1591 DiagReqData15_AMP "Diagnostic request data 15 AMP   ";
CM_ SG_ 1591 DiagReqData1_AMP "Diagnostic request data 1 AMP   ";
CM_ SG_ 1591 DiagReqData2_AMP "Diagnostic request data 2 AMP   ";
CM_ SG_ 1591 DiagReqData3_AMP "Diagnostic request data 3 AMP   ";
CM_ SG_ 1591 DiagReqData4_AMP "Diagnostic request data 4 AMP   ";
CM_ SG_ 1591 DiagReqData5_AMP "Diagnostic request data 5 AMP   ";
CM_ SG_ 1591 DiagReqData6_AMP "Diagnostic request data 6 AMP   ";
CM_ SG_ 1591 DiagReqData7_AMP "Diagnostic request data 7 AMP   ";
CM_ SG_ 1591 DiagReqData8_AMP "Diagnostic request data 8 AMP   ";
CM_ SG_ 1591 DiagReqData9_AMP "Diagnostic request data 9 AMP   ";
CM_ SG_ 1595 DiagReqData0_CNSL_I "Diagnostic request data 0 CNSL_I";
CM_ SG_ 1595 DiagReqData10_CNSL_I "Diagnostic request data 10 CNSL_I";
CM_ SG_ 1595 DiagReqData11_CNSL_I "Diagnostic request data 11 CNSL_I";
CM_ SG_ 1595 DiagReqData12_CNSL_I "Diagnostic request data 12 CNSL_I";
CM_ SG_ 1595 DiagReqData13_CNSL_I "Diagnostic request data 13 CNSL_I";
CM_ SG_ 1595 DiagReqData14_CNSL_I "Diagnostic request data 14 CNSL_I";
CM_ SG_ 1595 DiagReqData15_CNSL_I "Diagnostic request data 15 CNSL_I";
CM_ SG_ 1595 DiagReqData1_CNSL_I "Diagnostic request data 1 CNSL_I";
CM_ SG_ 1595 DiagReqData2_CNSL_I "Diagnostic request data 2 CNSL_I";
CM_ SG_ 1595 DiagReqData3_CNSL_I "Diagnostic request data 3 CNSL_I";
CM_ SG_ 1595 DiagReqData4_CNSL_I "Diagnostic request data 4 CNSL_I";
CM_ SG_ 1595 DiagReqData5_CNSL_I "Diagnostic request data 5 CNSL_I";
CM_ SG_ 1595 DiagReqData6_CNSL_I "Diagnostic request data 6 CNSL_I";
CM_ SG_ 1595 DiagReqData7_CNSL_I "Diagnostic request data 7 CNSL_I";
CM_ SG_ 1595 DiagReqData8_CNSL_I "Diagnostic request data 8 CNSL_I";
CM_ SG_ 1595 DiagReqData9_CNSL_I "Diagnostic request data 9 CNSL_I";
CM_ SG_ 1597 DiagReqData0_NOMI "Diagnostic request data 0 NOMI";
CM_ SG_ 1597 DiagReqData10_NOMI "Diagnostic request data 10 NOMI   ";
CM_ SG_ 1597 DiagReqData11_NOMI "Diagnostic request data 11 NOMI   ";
CM_ SG_ 1597 DiagReqData12_NOMI "Diagnostic request data 12 NOMI   ";
CM_ SG_ 1597 DiagReqData13_NOMI "Diagnostic request data 13 NOMI   ";
CM_ SG_ 1597 DiagReqData14_NOMI "Diagnostic request data 14 NOMI   ";
CM_ SG_ 1597 DiagReqData15_NOMI "Diagnostic request data 15 NOMI   ";
CM_ SG_ 1597 DiagReqData1_NOMI "Diagnostic request data 1 NOMI";
CM_ SG_ 1597 DiagReqData2_NOMI "Diagnostic request data 2 NOMI";
CM_ SG_ 1597 DiagReqData3_NOMI "Diagnostic request data 3 NOMI";
CM_ SG_ 1597 DiagReqData4_NOMI "Diagnostic request data 4 NOMI";
CM_ SG_ 1597 DiagReqData5_NOMI "Diagnostic request data 5 NOMI";
CM_ SG_ 1597 DiagReqData6_NOMI "Diagnostic request data 6 NOMI";
CM_ SG_ 1597 DiagReqData7_NOMI "Diagnostic request data 7 NOMI";
CM_ SG_ 1597 DiagReqData8_NOMI "Diagnostic request data 8 NOMI   ";
CM_ SG_ 1597 DiagReqData9_NOMI "Diagnostic request data 9 NOMI   ";
CM_ SG_ 1600 DiagReqData0_FI_R "Diagnostic request data 0 FI_R   ";
CM_ SG_ 1600 DiagReqData10_FI_R "Diagnostic request data 10 FI_R   ";
CM_ SG_ 1600 DiagReqData11_FI_R "Diagnostic request data 11 FI_R   ";
CM_ SG_ 1600 DiagReqData12_FI_R "Diagnostic request data 12 FI_R   ";
CM_ SG_ 1600 DiagReqData13_FI_R "Diagnostic request data 13 FI_R   ";
CM_ SG_ 1600 DiagReqData14_FI_R "Diagnostic request data 14 FI_R   ";
CM_ SG_ 1600 DiagReqData15_FI_R "Diagnostic request data 15 FI_R   ";
CM_ SG_ 1600 DiagReqData1_FI_R "Diagnostic request data 1 FI_R   ";
CM_ SG_ 1600 DiagReqData2_FI_R "Diagnostic request data 2 FI_R   ";
CM_ SG_ 1600 DiagReqData3_FI_R "Diagnostic request data 3 FI_R   ";
CM_ SG_ 1600 DiagReqData4_FI_R "Diagnostic request data 4 FI_R   ";
CM_ SG_ 1600 DiagReqData5_FI_R "Diagnostic request data 5 FI_R   ";
CM_ SG_ 1600 DiagReqData6_FI_R "Diagnostic request data 6 FI_R   ";
CM_ SG_ 1600 DiagReqData7_FI_R "Diagnostic request data 7 FI_R   ";
CM_ SG_ 1600 DiagReqData8_FI_R "Diagnostic request data 8 FI_R   ";
CM_ SG_ 1600 DiagReqData9_FI_R "Diagnostic request data 9 FI_R   ";

BA_DEF_ "Baudrate" INT 0 5000000;
BA_DEF_ "BusType" STRING;
BA_DEF_ BO_ "CANFD_BRS" ENUM "0","1";
BA_DEF_ "DBName" STRING;
BA_DEF_ BO_ "DiagRequest" ENUM "no","yes";
BA_DEF_ BO_ "DiagResponse" ENUM "no","yes";
BA_DEF_ BO_ "DiagState" ENUM "no","yes";
BA_DEF_ BO_ "DiagUudResponse" ENUM "False","True";
BA_DEF_ BO_ "DiagUudtResponse" ENUM "false","true";
BA_DEF_ BO_ "GenMsgCycleTime" INT 0 0;
BA_DEF_ BO_ "GenMsgCycleTimeFast" INT 0 0;
BA_DEF_ BO_ "GenMsgDelayTime" INT 0 0;
BA_DEF_ BO_ "GenMsgFastOnStart" INT 0 65535;
BA_DEF_ BO_ "GenMsgILSupport" ENUM "no","yes";
BA_DEF_ BO_ "GenMsgNrOfRepetition" INT 0 0;
BA_DEF_ BO_ "GenMsgSendType" ENUM "Cyclic","NotUsed","NotUsed","NotUsed","NotUsed","NotUsed","NotUsed","IfActive","NoMsgSendType","NotUsed";
BA_DEF_ BO_ "GenMsgStartDelayTime" INT 0 0;
BA_DEF_ SG_ "GenSigInactiveValue" INT 0 0;
BA_DEF_ SG_ "GenSigSendType" ENUM "Cyclic","OnWrite","OnWriteWithRepetition","OnChange","OnChangeWithRepetition","IfActive","IfActiveWithRepetition","NoSigSendType","NotUsed","NotUsed","NotUsed","NotUsed","NotUsed";
BA_DEF_ SG_ "GenSigStartValue" INT 0 0;
BA_DEF_REL_ BU_SG_REL_ "GenSigTimeoutTime" INT 0 65535;
BA_DEF_REL_ BU_SG_REL_ "GenSigTimeoutValue" INT 0 65535;
BA_DEF_ "ILTxTimeout" INT 0 65535;
BA_DEF_ BU_ "ILUsed" ENUM "no","yes";
BA_DEF_ "Manufacturer" STRING;
BA_DEF_ "NmAsrBaseAddress" HEX 0 2047;
BA_DEF_ BU_ "NmAsrCanMsgCycleOffset" INT 0 65535;
BA_DEF_ "NmAsrCanMsgCycleTime" INT 1 65535;
BA_DEF_ BU_ "NmAsrCanMsgReducedTime" INT 0 65535;
BA_DEF_ BO_ "NmAsrMessage" ENUM "no","yes";
BA_DEF_ "NmAsrMessageCount" INT 1 256;
BA_DEF_ BU_ "NmAsrNode" ENUM "no","yes";
BA_DEF_ BU_ "NmAsrNodeIdentifier" HEX 0 255;
BA_DEF_ "NmAsrRepeatMessageTime" INT 0 65535;
BA_DEF_ "NmAsrTimeoutTime" INT 1 65535;
BA_DEF_ "NmAsrWaitBusSleepTime" INT 0 65535;
BA_DEF_ BO_ "NmMessage" ENUM "no","yes";
BA_DEF_ BU_ "NmNode" ENUM "no","yes";
BA_DEF_ BU_ "NmStationAddress" HEX 0 255;
BA_DEF_ "NmType" STRING;
BA_DEF_ BU_ "NodeLayerModules" STRING;
BA_DEF_ BO_ "Timestamp" ENUM "0","1";
BA_DEF_ BO_ "TpApplType" STRING;
BA_DEF_ BO_ "TpTxIndex" INT 0 255;
BA_DEF_ BO_ "VFrameFormat" ENUM "StandardCAN","ExtendedCAN","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","reserved","StandardCAN_FD","ExtendedCAN_FD";
BA_DEF_ "VersionDay" INT 1 31;
BA_DEF_ "VersionMonth" INT 1 12;
BA_DEF_ "VersionYear" INT 0 99;

BA_DEF_DEF_  "Baudrate" 500000;
BA_DEF_DEF_  "BusType" "";
BA_DEF_DEF_  "CANFD_BRS" "0";
BA_DEF_DEF_  "DBName" "";
BA_DEF_DEF_  "DiagRequest" "no";
BA_DEF_DEF_  "DiagResponse" "no";
BA_DEF_DEF_  "DiagState" "no";
BA_DEF_DEF_  "DiagUudResponse" "False";
BA_DEF_DEF_  "DiagUudtResponse" "false";
BA_DEF_DEF_  "GenMsgCycleTime" 0;
BA_DEF_DEF_  "GenMsgCycleTimeFast" 0;
BA_DEF_DEF_  "GenMsgDelayTime" 0;
BA_DEF_DEF_  "GenMsgFastOnStart" 0;
BA_DEF_DEF_  "GenMsgILSupport" "no";
BA_DEF_DEF_  "GenMsgNrOfRepetition" 0;
BA_DEF_DEF_  "GenMsgSendType" "Cyclic";
BA_DEF_DEF_  "GenMsgStartDelayTime" 0;
BA_DEF_DEF_  "GenSigInactiveValue" 0;
BA_DEF_DEF_  "GenSigSendType" "Cyclic";
BA_DEF_DEF_  "GenSigStartValue" 0;
BA_DEF_DEF_REL_  "GenSigTimeoutTime" 0;
BA_DEF_DEF_REL_  "GenSigTimeoutValue" 0;
BA_DEF_DEF_  "ILTxTimeout" 0;
BA_DEF_DEF_  "ILUsed" "no";
BA_DEF_DEF_  "Manufacturer" "NIO";
BA_DEF_DEF_  "NmAsrBaseAddress" 1280;
BA_DEF_DEF_  "NmAsrCanMsgCycleOffset" 0;
BA_DEF_DEF_  "NmAsrCanMsgCycleTime" 640;
BA_DEF_DEF_  "NmAsrCanMsgReducedTime" 0;
BA_DEF_DEF_  "NmAsrMessage" "no";
BA_DEF_DEF_  "NmAsrMessageCount" 128;
BA_DEF_DEF_  "NmAsrNode" "no";
BA_DEF_DEF_  "NmAsrNodeIdentifier" 50;
BA_DEF_DEF_  "NmAsrRepeatMessageTime" 3200;
BA_DEF_DEF_  "NmAsrTimeoutTime" 2000;
BA_DEF_DEF_  "NmAsrWaitBusSleepTime" 2000;
BA_DEF_DEF_  "NmMessage" "no";
BA_DEF_DEF_  "NmNode" "no";
BA_DEF_DEF_  "NmStationAddress" 0;
BA_DEF_DEF_  "NmType" "";
BA_DEF_DEF_  "NodeLayerModules" "";
BA_DEF_DEF_  "Timestamp" "0";
BA_DEF_DEF_  "TpApplType" "";
BA_DEF_DEF_  "TpTxIndex" 0;
BA_DEF_DEF_  "VFrameFormat" "StandardCAN";
BA_DEF_DEF_  "VersionDay" 22;
BA_DEF_DEF_  "VersionMonth" 2;
BA_DEF_DEF_  "VersionYear" 21;

BA_ "ILUsed" BU_ AMP 1;
BA_ "NmAsrNode" BU_ AMP 1;
BA_ "NmStationAddress" BU_ AMP 55;
BA_ "ILUsed" BU_ CNSL_I 1;
BA_ "NmAsrNode" BU_ CNSL_I 1;
BA_ "NmAsrNodeIdentifier" BU_ CNSL_I 59;
BA_ "NmStationAddress" BU_ CNSL_I 59;
BA_ "ILUsed" BU_ FI_R_R 1;
BA_ "NmAsrNode" BU_ FI_R_R 1;
BA_ "NmStationAddress" BU_ FI_R_R 64;
BA_ "ILUsed" BU_ HUD 1;
BA_ "NmAsrNode" BU_ HUD 1;
BA_ "NmStationAddress" BU_ HUD 53;
BA_ "ILUsed" BU_ IC 1;
BA_ "NmAsrNode" BU_ IC 1;
BA_ "NmStationAddress" BU_ IC 51;
BA_ "ILUsed" BU_ ICS 1;
BA_ "NmAsrNode" BU_ ICS 1;
BA_ "NmStationAddress" BU_ ICS 54;
BA_ "ILUsed" BU_ NOMI 1;
BA_ "NmAsrNode" BU_ NOMI 1;
BA_ "NmStationAddress" BU_ NOMI 61;
BA_ "ILUsed" BU_ SWC 1;
BA_ "NmAsrNode" BU_ SWC 1;
BA_ "NmStationAddress" BU_ SWC 42;
BA_ "ILUsed" BU_ ZONE_FTM 1;
BA_ "NmAsrNode" BU_ ZONE_FTM 1;
BA_ "NmStationAddress" BU_ ZONE_FTM 5;
BA_ "Baudrate" 500000;
BA_ "BusType" "CAN FD";
BA_ "DBName" "info_canfd";
BA_ "NmType" "AUTOSAR";

BA_ "CANFD_BRS" BO_ 836 1;
BA_ "GenMsgCycleTime" BO_ 836 200;
BA_ "GenMsgILSupport" BO_ 836 1;
BA_ "GenMsgSendType" BO_ 836 0;
BA_ "VFrameFormat" BO_ 836 14;
BA_ "CANFD_BRS" BO_ 917 1;
BA_ "GenMsgCycleTime" BO_ 917 1000;
BA_ "GenMsgILSupport" BO_ 917 1;
BA_ "GenMsgSendType" BO_ 917 0;
BA_ "VFrameFormat" BO_ 917 14;
BA_ "CANFD_BRS" BO_ 918 1;
BA_ "GenMsgCycleTime" BO_ 918 1000;
BA_ "GenMsgILSupport" BO_ 918 1;
BA_ "GenMsgSendType" BO_ 918 0;
BA_ "VFrameFormat" BO_ 918 14;
BA_ "CANFD_BRS" BO_ 1200 1;
BA_ "GenMsgCycleTime" BO_ 1200 0;
BA_ "GenMsgILSupport" BO_ 1200 1;
BA_ "GenMsgSendType" BO_ 1200 8;
BA_ "VFrameFormat" BO_ 1200 14;
BA_ "GenMsgCycleTime" BO_ 1335 0;
BA_ "GenMsgSendType" BO_ 1335 8;
BA_ "NmAsrMessage" BO_ 1335 1;
BA_ "VFrameFormat" BO_ 1335 0;
BA_ "CANFD_BRS" BO_ 1719 1;
BA_ "DiagResponse" BO_ 1719 1;
BA_ "GenMsgCycleTime" BO_ 1719 0;
BA_ "GenMsgSendType" BO_ 1719 8;
BA_ "VFrameFormat" BO_ 1719 14;
BA_ "CANFD_BRS" BO_ 456 1;
BA_ "GenMsgCycleTime" BO_ 456 50;
BA_ "GenMsgILSupport" BO_ 456 1;
BA_ "GenMsgSendType" BO_ 456 0;
BA_ "VFrameFormat" BO_ 456 14;
BA_ "CANFD_BRS" BO_ 659 1;
BA_ "GenMsgCycleTime" BO_ 659 100;
BA_ "GenMsgILSupport" BO_ 659 1;
BA_ "GenMsgSendType" BO_ 659 0;
BA_ "VFrameFormat" BO_ 659 14;
BA_ "CANFD_BRS" BO_ 1178 1;
BA_ "GenMsgCycleTime" BO_ 1178 0;
BA_ "GenMsgILSupport" BO_ 1178 1;
BA_ "GenMsgSendType" BO_ 1178 8;
BA_ "VFrameFormat" BO_ 1178 14;
BA_ "GenMsgCycleTime" BO_ 1339 0;
BA_ "GenMsgSendType" BO_ 1339 8;
BA_ "NmAsrMessage" BO_ 1339 1;
BA_ "VFrameFormat" BO_ 1339 0;
BA_ "CANFD_BRS" BO_ 1723 1;
BA_ "DiagResponse" BO_ 1723 1;
BA_ "GenMsgCycleTime" BO_ 1723 0;
BA_ "GenMsgSendType" BO_ 1723 8;
BA_ "VFrameFormat" BO_ 1723 14;
BA_ "CANFD_BRS" BO_ 804 1;
BA_ "GenMsgCycleTime" BO_ 804 100;
BA_ "GenMsgILSupport" BO_ 804 1;
BA_ "GenMsgSendType" BO_ 804 0;
BA_ "VFrameFormat" BO_ 804 14;
BA_ "GenMsgCycleTime" BO_ 1344 0;
BA_ "GenMsgSendType" BO_ 1344 8;
BA_ "NmAsrMessage" BO_ 1344 1;
BA_ "VFrameFormat" BO_ 1344 0;
BA_ "CANFD_BRS" BO_ 1728 1;
BA_ "DiagResponse" BO_ 1728 1;
BA_ "GenMsgCycleTime" BO_ 1728 0;
BA_ "GenMsgSendType" BO_ 1728 8;
BA_ "VFrameFormat" BO_ 1728 14;
BA_ "CANFD_BRS" BO_ 776 1;
BA_ "GenMsgCycleTime" BO_ 776 100;
BA_ "GenMsgILSupport" BO_ 776 1;
BA_ "GenMsgSendType" BO_ 776 0;
BA_ "VFrameFormat" BO_ 776 14;
BA_ "GenMsgCycleTime" BO_ 1333 0;
BA_ "GenMsgSendType" BO_ 1333 8;
BA_ "NmAsrMessage" BO_ 1333 1;
BA_ "VFrameFormat" BO_ 1333 0;
BA_ "CANFD_BRS" BO_ 1717 1;
BA_ "DiagResponse" BO_ 1717 1;
BA_ "GenMsgCycleTime" BO_ 1717 0;
BA_ "GenMsgSendType" BO_ 1717 8;
BA_ "VFrameFormat" BO_ 1717 14;
BA_ "CANFD_BRS" BO_ 741 1;
BA_ "GenMsgCycleTime" BO_ 741 100;
BA_ "GenMsgILSupport" BO_ 741 1;
BA_ "GenMsgSendType" BO_ 741 0;
BA_ "VFrameFormat" BO_ 741 14;
BA_ "GenMsgCycleTime" BO_ 1331 0;
BA_ "GenMsgSendType" BO_ 1331 8;
BA_ "NmAsrMessage" BO_ 1331 1;
BA_ "VFrameFormat" BO_ 1331 0;
BA_ "CANFD_BRS" BO_ 1715 1;
BA_ "DiagResponse" BO_ 1715 1;
BA_ "GenMsgCycleTime" BO_ 1715 0;
BA_ "GenMsgSendType" BO_ 1715 8;
BA_ "VFrameFormat" BO_ 1715 14;
BA_ "CANFD_BRS" BO_ 755 1;
BA_ "GenMsgCycleTime" BO_ 755 100;
BA_ "GenMsgILSupport" BO_ 755 1;
BA_ "GenMsgSendType" BO_ 755 0;
BA_ "VFrameFormat" BO_ 755 14;
BA_ "GenMsgCycleTime" BO_ 1334 0;
BA_ "GenMsgSendType" BO_ 1334 8;
BA_ "NmAsrMessage" BO_ 1334 1;
BA_ "VFrameFormat" BO_ 1334 0;
BA_ "CANFD_BRS" BO_ 1718 1;
BA_ "DiagResponse" BO_ 1718 1;
BA_ "GenMsgCycleTime" BO_ 1718 0;
BA_ "GenMsgSendType" BO_ 1718 8;
BA_ "VFrameFormat" BO_ 1718 14;
BA_ "CANFD_BRS" BO_ 453 1;
BA_ "GenMsgCycleTime" BO_ 453 50;
BA_ "GenMsgILSupport" BO_ 453 1;
BA_ "GenMsgSendType" BO_ 453 0;
BA_ "VFrameFormat" BO_ 453 14;
BA_ "CANFD_BRS" BO_ 576 1;
BA_ "GenMsgCycleTime" BO_ 576 100;
BA_ "GenMsgILSupport" BO_ 576 1;
BA_ "GenMsgSendType" BO_ 576 0;
BA_ "VFrameFormat" BO_ 576 14;
BA_ "CANFD_BRS" BO_ 1163 1;
BA_ "GenMsgCycleTime" BO_ 1163 0;
BA_ "GenMsgILSupport" BO_ 1163 1;
BA_ "GenMsgSendType" BO_ 1163 8;
BA_ "VFrameFormat" BO_ 1163 14;
BA_ "CANFD_BRS" BO_ 1169 1;
BA_ "GenMsgCycleTime" BO_ 1169 0;
BA_ "GenMsgILSupport" BO_ 1169 1;
BA_ "GenMsgSendType" BO_ 1169 8;
BA_ "VFrameFormat" BO_ 1169 14;
BA_ "GenMsgCycleTime" BO_ 1341 0;
BA_ "GenMsgSendType" BO_ 1341 8;
BA_ "NmAsrMessage" BO_ 1341 1;
BA_ "VFrameFormat" BO_ 1341 0;
BA_ "CANFD_BRS" BO_ 1725 1;
BA_ "DiagResponse" BO_ 1725 1;
BA_ "GenMsgCycleTime" BO_ 1725 0;
BA_ "GenMsgSendType" BO_ 1725 8;
BA_ "VFrameFormat" BO_ 1725 14;
BA_ "CANFD_BRS" BO_ 671 1;
BA_ "GenMsgCycleTime" BO_ 671 20;
BA_ "GenMsgILSupport" BO_ 671 1;
BA_ "GenMsgSendType" BO_ 671 0;
BA_ "VFrameFormat" BO_ 671 14;
BA_ "CANFD_BRS" BO_ 77 1;
BA_ "GenMsgCycleTime" BO_ 77 10;
BA_ "GenMsgILSupport" BO_ 77 1;
BA_ "GenMsgSendType" BO_ 77 0;
BA_ "VFrameFormat" BO_ 77 14;
BA_ "GenMsgCycleTime" BO_ 1322 0;
BA_ "GenMsgSendType" BO_ 1322 8;
BA_ "NmAsrMessage" BO_ 1322 1;
BA_ "VFrameFormat" BO_ 1322 0;
BA_ "CANFD_BRS" BO_ 1706 1;
BA_ "DiagResponse" BO_ 1706 1;
BA_ "GenMsgCycleTime" BO_ 1706 0;
BA_ "GenMsgSendType" BO_ 1706 8;
BA_ "VFrameFormat" BO_ 1706 14;
BA_ "CANFD_BRS" BO_ 286 1;
BA_ "GenMsgCycleTime" BO_ 286 20;
BA_ "GenMsgILSupport" BO_ 286 1;
BA_ "GenMsgSendType" BO_ 286 0;
BA_ "VFrameFormat" BO_ 286 14;
BA_ "CANFD_BRS" BO_ 316 1;
BA_ "GenMsgCycleTime" BO_ 316 20;
BA_ "GenMsgILSupport" BO_ 316 1;
BA_ "GenMsgSendType" BO_ 316 0;
BA_ "VFrameFormat" BO_ 316 14;
BA_ "CANFD_BRS" BO_ 371 1;
BA_ "GenMsgCycleTime" BO_ 371 20;
BA_ "GenMsgILSupport" BO_ 371 1;
BA_ "GenMsgSendType" BO_ 371 0;
BA_ "VFrameFormat" BO_ 371 14;
BA_ "CANFD_BRS" BO_ 549 1;
BA_ "GenMsgCycleTime" BO_ 549 100;
BA_ "GenMsgILSupport" BO_ 549 1;
BA_ "GenMsgSendType" BO_ 549 0;
BA_ "VFrameFormat" BO_ 549 14;
BA_ "CANFD_BRS" BO_ 648 1;
BA_ "GenMsgCycleTime" BO_ 648 100;
BA_ "GenMsgILSupport" BO_ 648 1;
BA_ "GenMsgSendType" BO_ 648 0;
BA_ "VFrameFormat" BO_ 648 14;
BA_ "CANFD_BRS" BO_ 747 1;
BA_ "GenMsgCycleTime" BO_ 747 100;
BA_ "GenMsgILSupport" BO_ 747 1;
BA_ "GenMsgSendType" BO_ 747 0;
BA_ "VFrameFormat" BO_ 747 14;
BA_ "CANFD_BRS" BO_ 781 1;
BA_ "GenMsgCycleTime" BO_ 781 100;
BA_ "GenMsgILSupport" BO_ 781 1;
BA_ "GenMsgSendType" BO_ 781 0;
BA_ "VFrameFormat" BO_ 781 14;
BA_ "CANFD_BRS" BO_ 937 1;
BA_ "GenMsgCycleTime" BO_ 937 1000;
BA_ "GenMsgILSupport" BO_ 937 1;
BA_ "GenMsgSendType" BO_ 937 0;
BA_ "VFrameFormat" BO_ 937 14;
BA_ "CANFD_BRS" BO_ 946 1;
BA_ "GenMsgCycleTime" BO_ 946 1000;
BA_ "GenMsgILSupport" BO_ 946 1;
BA_ "GenMsgSendType" BO_ 946 0;
BA_ "VFrameFormat" BO_ 946 14;
BA_ "CANFD_BRS" BO_ 1159 1;
BA_ "GenMsgCycleTime" BO_ 1159 0;
BA_ "GenMsgILSupport" BO_ 1159 1;
BA_ "GenMsgSendType" BO_ 1159 8;
BA_ "VFrameFormat" BO_ 1159 14;
BA_ "CANFD_BRS" BO_ 1161 1;
BA_ "GenMsgCycleTime" BO_ 1161 0;
BA_ "GenMsgILSupport" BO_ 1161 1;
BA_ "GenMsgSendType" BO_ 1161 8;
BA_ "VFrameFormat" BO_ 1161 14;
BA_ "CANFD_BRS" BO_ 1162 1;
BA_ "GenMsgCycleTime" BO_ 1162 0;
BA_ "GenMsgILSupport" BO_ 1162 1;
BA_ "GenMsgSendType" BO_ 1162 8;
BA_ "VFrameFormat" BO_ 1162 14;
BA_ "CANFD_BRS" BO_ 1201 1;
BA_ "GenMsgCycleTime" BO_ 1201 0;
BA_ "GenMsgILSupport" BO_ 1201 1;
BA_ "GenMsgSendType" BO_ 1201 8;
BA_ "VFrameFormat" BO_ 1201 14;
BA_ "GenMsgCycleTime" BO_ 1285 0;
BA_ "GenMsgSendType" BO_ 1285 8;
BA_ "NmAsrMessage" BO_ 1285 1;
BA_ "VFrameFormat" BO_ 1285 0;
BA_ "CANFD_BRS" BO_ 1504 1;
BA_ "GenMsgCycleTime" BO_ 1504 50;
BA_ "GenMsgILSupport" BO_ 1504 1;
BA_ "GenMsgSendType" BO_ 1504 0;
BA_ "VFrameFormat" BO_ 1504 14;
BA_ "CANFD_BRS" BO_ 1537 1;
BA_ "DiagState" BO_ 1537 1;
BA_ "GenMsgCycleTime" BO_ 1537 0;
BA_ "GenMsgSendType" BO_ 1537 8;
BA_ "VFrameFormat" BO_ 1537 14;
BA_ "CANFD_BRS" BO_ 1578 1;
BA_ "DiagRequest" BO_ 1578 1;
BA_ "GenMsgCycleTime" BO_ 1578 0;
BA_ "GenMsgSendType" BO_ 1578 8;
BA_ "VFrameFormat" BO_ 1578 14;
BA_ "CANFD_BRS" BO_ 1587 1;
BA_ "DiagRequest" BO_ 1587 1;
BA_ "GenMsgCycleTime" BO_ 1587 0;
BA_ "GenMsgSendType" BO_ 1587 8;
BA_ "VFrameFormat" BO_ 1587 14;
BA_ "CANFD_BRS" BO_ 1589 1;
BA_ "DiagRequest" BO_ 1589 1;
BA_ "GenMsgCycleTime" BO_ 1589 0;
BA_ "GenMsgSendType" BO_ 1589 8;
BA_ "VFrameFormat" BO_ 1589 14;
BA_ "CANFD_BRS" BO_ 1590 1;
BA_ "DiagRequest" BO_ 1590 1;
BA_ "GenMsgCycleTime" BO_ 1590 0;
BA_ "GenMsgSendType" BO_ 1590 8;
BA_ "VFrameFormat" BO_ 1590 14;
BA_ "CANFD_BRS" BO_ 1591 1;
BA_ "DiagRequest" BO_ 1591 1;
BA_ "GenMsgCycleTime" BO_ 1591 0;
BA_ "GenMsgSendType" BO_ 1591 8;
BA_ "VFrameFormat" BO_ 1591 14;
BA_ "CANFD_BRS" BO_ 1595 1;
BA_ "DiagRequest" BO_ 1595 1;
BA_ "GenMsgCycleTime" BO_ 1595 0;
BA_ "GenMsgSendType" BO_ 1595 8;
BA_ "VFrameFormat" BO_ 1595 14;
BA_ "CANFD_BRS" BO_ 1597 1;
BA_ "DiagRequest" BO_ 1597 1;
BA_ "GenMsgCycleTime" BO_ 1597 0;
BA_ "GenMsgSendType" BO_ 1597 8;
BA_ "VFrameFormat" BO_ 1597 14;
BA_ "CANFD_BRS" BO_ 1600 1;
BA_ "DiagRequest" BO_ 1600 1;
BA_ "GenMsgCycleTime" BO_ 1600 0;
BA_ "GenMsgSendType" BO_ 1600 8;
BA_ "VFrameFormat" BO_ 1600 14;

BA_ "GenSigSendType" SG_ 836 AMP_SetEPMode_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetEPMode_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetEPSwitch_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetEPSwitch_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetKROKMicType_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetKROKMicType_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetKROKSwitch_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetKROKSwitch_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetKROKVol_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetKROKVol_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetMediaFrontMute_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetMediaFrontMute_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetMediaPath_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetMediaPath_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetMediaRearMute_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetMediaRearMute_Sts 0;
BA_ "GenSigInactiveValue" SG_ 836 AMP_SetMediaSwitch_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetMediaSwitch_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetMediaSwitch_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetMediaType_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetMediaType_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetMediaVol_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetMediaVol_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetMute_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetMute_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetNomiSwitch_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetNomiSwitch_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetNomiType_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetNomiType_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetNomiVol_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetNomiVol_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQFrqPoint_10_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQFrqPoint_10_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQFrqPoint_1_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQFrqPoint_1_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQFrqPoint_2_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQFrqPoint_2_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQFrqPoint_3_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQFrqPoint_3_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQFrqPoint_4_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQFrqPoint_4_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQFrqPoint_5_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQFrqPoint_5_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQFrqPoint_6_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQFrqPoint_6_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQFrqPoint_7_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQFrqPoint_7_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQFrqPoint_8_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQFrqPoint_8_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQFrqPoint_9_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQFrqPoint_9_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQGain_10_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQGain_10_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQGain_1_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQGain_1_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQGain_2_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQGain_2_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQGain_3_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQGain_3_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQGain_4_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQGain_4_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQGain_5_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQGain_5_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQGain_6_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQGain_6_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQGain_7_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQGain_7_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQGain_8_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQGain_8_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQGain_9_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQGain_9_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQSwitch_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQSwitch_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQValue_10_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQValue_10_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQValue_1_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQValue_1_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQValue_2_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQValue_2_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQValue_3_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQValue_3_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQValue_4_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQValue_4_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQValue_5_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQValue_5_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQValue_6_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQValue_6_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQValue_7_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQValue_7_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQValue_8_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQValue_8_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPEQValue_9_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPEQValue_9_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPhonePos_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPhonePos_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetPhoneSwitch_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetPhoneSwitch_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetRNCSwitch_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetRNCSwitch_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetSoundFiledSwitch_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetSoundFiledSwitch_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetSoundFiled_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetSoundFiled_Sts 0;
BA_ "GenSigSendType" SG_ 836 AMP_SetWorkMode_Sts 0;
BA_ "GenSigStartValue" SG_ 836 AMP_SetWorkMode_Sts 0;
BA_ "GenSigSendType" SG_ 917 A2B1_TDM_Lost_Times 0;
BA_ "GenSigStartValue" SG_ 917 A2B1_TDM_Lost_Times 0;
BA_ "GenSigSendType" SG_ 917 A2B1_TDM_State 0;
BA_ "GenSigStartValue" SG_ 917 A2B1_TDM_State 0;
BA_ "GenSigSendType" SG_ 917 A2B2_TDM_Lost_Times 0;
BA_ "GenSigStartValue" SG_ 917 A2B2_TDM_Lost_Times 0;
BA_ "GenSigSendType" SG_ 917 A2B2_TDM_State 0;
BA_ "GenSigStartValue" SG_ 917 A2B2_TDM_State 0;
BA_ "GenSigSendType" SG_ 917 AMP_Dsp1HeartBeat 0;
BA_ "GenSigStartValue" SG_ 917 AMP_Dsp1HeartBeat 0;
BA_ "GenSigSendType" SG_ 917 AMP_Dsp2HeartBeat 0;
BA_ "GenSigStartValue" SG_ 917 AMP_Dsp2HeartBeat 0;
BA_ "GenSigSendType" SG_ 917 AMP_Dsp3HeartBeat 0;
BA_ "GenSigStartValue" SG_ 917 AMP_Dsp3HeartBeat 0;
BA_ "GenSigSendType" SG_ 917 Audio_status 0;
BA_ "GenSigStartValue" SG_ 917 Audio_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_10_comm_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_10_comm_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_10_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_10_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_1_comm_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_1_comm_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_1_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_1_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_2_comm_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_2_comm_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_2_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_2_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_3_comm_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_3_comm_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_3_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_3_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_4_comm_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_4_comm_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_4_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_4_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_5_comm_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_5_comm_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_5_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_5_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_6_comm_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_6_comm_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_6_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_6_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_7_comm_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_7_comm_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_7_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_7_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_8_comm_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_8_comm_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_8_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_8_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_9_comm_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_9_comm_status 0;
BA_ "GenSigSendType" SG_ 917 Chip_9_status 0;
BA_ "GenSigStartValue" SG_ 917 Chip_9_status 0;
BA_ "GenSigSendType" SG_ 917 DAP_Ver 0;
BA_ "GenSigStartValue" SG_ 917 DAP_Ver 0;
BA_ "GenSigSendType" SG_ 917 DSP1_Ver 0;
BA_ "GenSigStartValue" SG_ 917 DSP1_Ver 0;
BA_ "GenSigSendType" SG_ 917 DSP2_Ver 0;
BA_ "GenSigStartValue" SG_ 917 DSP2_Ver 0;
BA_ "GenSigSendType" SG_ 917 DSP3_Ver 0;
BA_ "GenSigStartValue" SG_ 917 DSP3_Ver 0;
BA_ "GenSigSendType" SG_ 917 MCU_App_Ver 0;
BA_ "GenSigStartValue" SG_ 917 MCU_App_Ver 0;
BA_ "GenSigSendType" SG_ 917 MCU_AutoSar_Ver 0;
BA_ "GenSigStartValue" SG_ 917 MCU_AutoSar_Ver 0;
BA_ "GenSigSendType" SG_ 917 MCU_Mcal_Ver 0;
BA_ "GenSigStartValue" SG_ 917 MCU_Mcal_Ver 0;
BA_ "GenSigSendType" SG_ 918 DSP1_STS 0;
BA_ "GenSigStartValue" SG_ 918 DSP1_STS 0;
BA_ "GenSigSendType" SG_ 918 DSP1_TEMP 0;
BA_ "GenSigStartValue" SG_ 918 DSP1_TEMP 0;
BA_ "GenSigSendType" SG_ 918 DSP2_STS 0;
BA_ "GenSigStartValue" SG_ 918 DSP2_STS 0;
BA_ "GenSigSendType" SG_ 918 DSP2_TEMP 0;
BA_ "GenSigStartValue" SG_ 918 DSP2_TEMP 0;
BA_ "GenSigSendType" SG_ 918 DSP3_STS 0;
BA_ "GenSigStartValue" SG_ 918 DSP3_STS 0;
BA_ "GenSigSendType" SG_ 918 DSP3_TEMP 0;
BA_ "GenSigStartValue" SG_ 918 DSP3_TEMP 0;
BA_ "GenSigSendType" SG_ 918 InputVolt 0;
BA_ "GenSigStartValue" SG_ 918 InputVolt 0;
BA_ "GenSigSendType" SG_ 918 NCU_TEMP 0;
BA_ "GenSigStartValue" SG_ 918 NCU_TEMP 0;
BA_ "GenSigInactiveValue" SG_ 1200 AMP_Tx_Sig01 0;
BA_ "GenSigSendType" SG_ 1200 AMP_Tx_Sig01 7;
BA_ "GenSigStartValue" SG_ 1200 AMP_Tx_Sig01 0;
BA_ "GenSigInactiveValue" SG_ 1200 AMP_Tx_Sig02 0;
BA_ "GenSigSendType" SG_ 1200 AMP_Tx_Sig02 7;
BA_ "GenSigStartValue" SG_ 1200 AMP_Tx_Sig02 0;
BA_ "GenSigInactiveValue" SG_ 1200 AMP_Tx_Sig03 0;
BA_ "GenSigSendType" SG_ 1200 AMP_Tx_Sig03 7;
BA_ "GenSigStartValue" SG_ 1200 AMP_Tx_Sig03 0;
BA_ "GenSigInactiveValue" SG_ 1200 AMP_Tx_Sig04 0;
BA_ "GenSigSendType" SG_ 1200 AMP_Tx_Sig04 7;
BA_ "GenSigStartValue" SG_ 1200 AMP_Tx_Sig04 0;
BA_ "GenSigInactiveValue" SG_ 1200 AMP_Tx_Sig05 0;
BA_ "GenSigSendType" SG_ 1200 AMP_Tx_Sig05 7;
BA_ "GenSigStartValue" SG_ 1200 AMP_Tx_Sig05 0;
BA_ "GenSigInactiveValue" SG_ 1200 AMP_Tx_Sig06 0;
BA_ "GenSigSendType" SG_ 1200 AMP_Tx_Sig06 7;
BA_ "GenSigStartValue" SG_ 1200 AMP_Tx_Sig06 0;
BA_ "GenSigInactiveValue" SG_ 1200 AMP_Tx_Sig07 0;
BA_ "GenSigSendType" SG_ 1200 AMP_Tx_Sig07 7;
BA_ "GenSigStartValue" SG_ 1200 AMP_Tx_Sig07 0;
BA_ "GenSigInactiveValue" SG_ 1200 AMP_Tx_Sig08 0;
BA_ "GenSigSendType" SG_ 1200 AMP_Tx_Sig08 7;
BA_ "GenSigStartValue" SG_ 1200 AMP_Tx_Sig08 0;
BA_ "GenSigInactiveValue" SG_ 1335 ActvWakeup_AMP 0;
BA_ "GenSigSendType" SG_ 1335 ActvWakeup_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 ActvWakeup_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 NetMngtCoorn_AMP 0;
BA_ "GenSigSendType" SG_ 1335 NetMngtCoorn_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 NetMngtCoorn_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 NetMngtCoorrSleepRdy_AMP 0;
BA_ "GenSigSendType" SG_ 1335 NetMngtCoorrSleepRdy_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 NetMngtCoorrSleepRdy_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN10_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN10_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN10_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN11_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN11_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN11_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN12_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN12_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN12_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN13_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN13_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN13_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN14_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN14_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN14_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN15_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN15_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN15_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN16_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN16_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN16_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN17_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN17_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN17_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN18_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN18_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN18_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN19_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN19_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN19_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN1_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN1_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN1_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN20_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN20_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN20_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN21_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN21_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN21_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN22_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN22_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN22_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN23_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN23_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN23_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN24_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN24_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN24_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN25_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN25_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN25_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN26_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN26_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN26_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN27_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN27_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN27_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN28_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN28_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN28_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN29_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN29_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN29_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN2_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN2_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN2_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN30_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN30_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN30_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN31_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN31_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN31_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN32_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN32_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN32_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN33_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN33_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN33_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN34_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN34_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN34_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN35_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN35_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN35_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN36_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN36_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN36_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN37_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN37_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN37_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN38_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN38_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN38_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN39_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN39_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN39_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN3_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN3_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN3_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN40_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN40_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN40_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN41_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN41_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN41_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN42_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN42_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN42_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN43_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN43_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN43_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN44_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN44_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN44_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN45_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN45_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN45_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN46_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN46_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN46_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN47_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN47_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN47_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN48_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN48_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN48_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN4_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN4_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN4_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN5_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN5_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN5_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN6_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN6_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN6_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN7_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN7_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN7_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN8_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN8_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN8_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PN9_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PN9_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PN9_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 PtlNetInfo_AMP 0;
BA_ "GenSigSendType" SG_ 1335 PtlNetInfo_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 PtlNetInfo_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 RepMsgReq_AMP 0;
BA_ "GenSigSendType" SG_ 1335 RepMsgReq_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 RepMsgReq_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 ResdBit1_AMP 0;
BA_ "GenSigSendType" SG_ 1335 ResdBit1_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 ResdBit1_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 ResdBit2_AMP 0;
BA_ "GenSigSendType" SG_ 1335 ResdBit2_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 ResdBit2_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1335 ScrNodId_AMP 0;
BA_ "GenSigSendType" SG_ 1335 ScrNodId_AMP 7;
BA_ "GenSigStartValue" SG_ 1335 ScrNodId_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1719 DiagRespData0_AMP 0;
BA_ "GenSigSendType" SG_ 1719 DiagRespData0_AMP 7;
BA_ "GenSigStartValue" SG_ 1719 DiagRespData0_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1719 DiagRespData10_AMP 0;
BA_ "GenSigSendType" SG_ 1719 DiagRespData10_AMP 7;
BA_ "GenSigStartValue" SG_ 1719 DiagRespData10_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1719 DiagRespData11_AMP 0;
BA_ "GenSigSendType" SG_ 1719 DiagRespData11_AMP 7;
BA_ "GenSigStartValue" SG_ 1719 DiagRespData11_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1719 DiagRespData12_AMP 0;
BA_ "GenSigSendType" SG_ 1719 DiagRespData12_AMP 7;
BA_ "GenSigStartValue" SG_ 1719 DiagRespData12_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1719 DiagRespData13_AMP 0;
BA_ "GenSigSendType" SG_ 1719 DiagRespData13_AMP 7;
BA_ "GenSigStartValue" SG_ 1719 DiagRespData13_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1719 DiagRespData14_AMP 0;
BA_ "GenSigSendType" SG_ 1719 DiagRespData14_AMP 7;
BA_ "GenSigStartValue" SG_ 1719 DiagRespData14_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1719 DiagRespData15_AMP 0;
BA_ "GenSigSendType" SG_ 1719 DiagRespData15_AMP 7;
BA_ "GenSigStartValue" SG_ 1719 DiagRespData15_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1719 DiagRespData1_AMP 0;
BA_ "GenSigSendType" SG_ 1719 DiagRespData1_AMP 7;
BA_ "GenSigStartValue" SG_ 1719 DiagRespData1_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1719 DiagRespData2_AMP 0;
BA_ "GenSigSendType" SG_ 1719 DiagRespData2_AMP 7;
BA_ "GenSigStartValue" SG_ 1719 DiagRespData2_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1719 DiagRespData3_AMP 0;
BA_ "GenSigSendType" SG_ 1719 DiagRespData3_AMP 7;
BA_ "GenSigStartValue" SG_ 1719 DiagRespData3_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1719 DiagRespData4_AMP 0;
BA_ "GenSigSendType" SG_ 1719 DiagRespData4_AMP 7;
BA_ "GenSigStartValue" SG_ 1719 DiagRespData4_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1719 DiagRespData5_AMP 0;
BA_ "GenSigSendType" SG_ 1719 DiagRespData5_AMP 7;
BA_ "GenSigStartValue" SG_ 1719 DiagRespData5_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1719 DiagRespData6_AMP 0;
BA_ "GenSigSendType" SG_ 1719 DiagRespData6_AMP 7;
BA_ "GenSigStartValue" SG_ 1719 DiagRespData6_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1719 DiagRespData7_AMP 0;
BA_ "GenSigSendType" SG_ 1719 DiagRespData7_AMP 7;
BA_ "GenSigStartValue" SG_ 1719 DiagRespData7_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1719 DiagRespData8_AMP 0;
BA_ "GenSigSendType" SG_ 1719 DiagRespData8_AMP 7;
BA_ "GenSigStartValue" SG_ 1719 DiagRespData8_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1719 DiagRespData9_AMP 0;
BA_ "GenSigSendType" SG_ 1719 DiagRespData9_AMP 7;
BA_ "GenSigStartValue" SG_ 1719 DiagRespData9_AMP 0;
BA_ "GenSigSendType" SG_ 456 CNSL_I_LeftBtn_LongPress 0;
BA_ "GenSigStartValue" SG_ 456 CNSL_I_LeftBtn_LongPress 0;
BA_ "GenSigSendType" SG_ 456 CNSL_I_MidTp_LongPress 0;
BA_ "GenSigStartValue" SG_ 456 CNSL_I_MidTp_LongPress 0;
BA_ "GenSigSendType" SG_ 456 CNSL_I_RightBtn_LongPress 0;
BA_ "GenSigStartValue" SG_ 456 CNSL_I_RightBtn_LongPress 0;
BA_ "GenSigSendType" SG_ 659 CNSL_I_EnableSwSt 0;
BA_ "GenSigStartValue" SG_ 659 CNSL_I_EnableSwSt 1;
BA_ "GenSigSendType" SG_ 659 CNSL_I_OpModeSt 0;
BA_ "GenSigStartValue" SG_ 659 CNSL_I_OpModeSt 0;
BA_ "GenSigSendType" SG_ 659 CNSL_I_SensSt 0;
BA_ "GenSigStartValue" SG_ 659 CNSL_I_SensSt 0;
BA_ "GenSigSendType" SG_ 659 CNSL_I_St 0;
BA_ "GenSigStartValue" SG_ 659 CNSL_I_St 0;
BA_ "GenSigSendType" SG_ 659 CNSL_I_VibDeltalSt 0;
BA_ "GenSigStartValue" SG_ 659 CNSL_I_VibDeltalSt 0;
BA_ "GenSigSendType" SG_ 659 CNSL_I_VibFbSt 0;
BA_ "GenSigStartValue" SG_ 659 CNSL_I_VibFbSt 1;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_LeftBtn_Press 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_LeftBtn_Press 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_LeftBtn_SwipeDown 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_LeftBtn_SwipeDown 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_LeftBtn_SwipeUp 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_LeftBtn_SwipeUp 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_LeftBtn_TouchCordData 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_LeftBtn_TouchCordData 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_LeftBtn_TouchRawData 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_LeftBtn_TouchRawData 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_LeftBtn_TouchSt 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_LeftBtn_TouchSt 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_2Finger_SwipeDown 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_2Finger_SwipeDown 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_2Finger_SwipeLeft 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_2Finger_SwipeLeft 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_2Finger_SwipeRight 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_2Finger_SwipeRight 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_2Finger_SwipeUp 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_2Finger_SwipeUp 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_3Finger_SwipeDown 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_3Finger_SwipeDown 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_3Finger_SwipeLeft 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_3Finger_SwipeLeft 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_3Finger_SwipeRight 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_3Finger_SwipeRight 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_3Finger_SwipeUp 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_3Finger_SwipeUp 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_F1_TouchRawData_F1 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_F1_TouchRawData_F1 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_Press 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_Press 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_SwipeDown 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_SwipeDown 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_SwipeLeft 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_SwipeLeft 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_SwipeRight 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_SwipeRight 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_SwipeUp 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_SwipeUp 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_TouchCordData_F1 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_TouchCordData_F1 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_TouchCordData_F2 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_TouchCordData_F2 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_TouchRawData_F2 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_TouchRawData_F2 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_TouchSt_F1 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_TouchSt_F1 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_MidTp_TouchSt_F2 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_MidTp_TouchSt_F2 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_RightBtn_Press 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_RightBtn_Press 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_RightBtn_SwipeDown 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_RightBtn_SwipeDown 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_RightBtn_SwipeUp 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_RightBtn_SwipeUp 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_RightBtn_TouchCordData 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_RightBtn_TouchCordData 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_RightBtn_TouchRawData 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_RightBtn_TouchRawData 0;
BA_ "GenSigSendType" SG_ 1178 CNSL_I_RightBtn_TouchSt 7;
BA_ "GenSigStartValue" SG_ 1178 CNSL_I_RightBtn_TouchSt 0;
BA_ "GenSigSendType" SG_ 1339 ActvWakeup_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 ActvWakeup_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 NetMngtCoorn_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 NetMngtCoorn_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 NetMngtCoorrSleepRdy_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 NetMngtCoorrSleepRdy_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC10_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC10_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC11_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC11_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC12_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC12_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC13_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC13_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC14_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC14_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC15_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC15_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC16_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC16_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC17_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC17_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC18_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC18_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC19_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC19_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC1_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC1_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC20_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC20_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC21_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC21_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC22_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC22_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC23_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC23_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC24_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC24_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC25_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC25_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC26_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC26_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC27_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC27_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC28_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC28_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC29_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC29_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC2_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC2_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC30_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC30_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC31_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC31_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC32_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC32_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC33_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC33_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC34_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC34_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC35_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC35_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC36_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC36_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC37_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC37_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC38_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC38_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC39_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC39_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC3_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC3_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC40_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC40_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC41_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC41_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC42_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC42_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC43_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC43_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC44_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC44_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC45_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC45_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC46_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC46_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC47_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC47_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC48_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC48_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC4_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC4_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC5_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC5_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC6_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC6_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC7_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC7_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC8_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC8_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PNC9_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PNC9_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 PtlNetInfo_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 PtlNetInfo_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 RepMsgReq_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 RepMsgReq_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 ResdBit1_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 ResdBit1_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 ResdBit2_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 ResdBit2_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1339 ScrNodId_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1339 ScrNodId_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1723 DiagRespData0_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1723 DiagRespData0_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1723 DiagRespData10_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1723 DiagRespData10_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1723 DiagRespData11_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1723 DiagRespData11_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1723 DiagRespData12_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1723 DiagRespData12_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1723 DiagRespData13_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1723 DiagRespData13_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1723 DiagRespData14_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1723 DiagRespData14_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1723 DiagRespData15_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1723 DiagRespData15_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1723 DiagRespData1_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1723 DiagRespData1_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1723 DiagRespData2_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1723 DiagRespData2_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1723 DiagRespData3_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1723 DiagRespData3_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1723 DiagRespData4_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1723 DiagRespData4_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1723 DiagRespData5_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1723 DiagRespData5_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1723 DiagRespData6_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1723 DiagRespData6_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1723 DiagRespData7_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1723 DiagRespData7_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1723 DiagRespData8_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1723 DiagRespData8_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1723 DiagRespData9_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1723 DiagRespData9_CNSL_I 0;
BA_ "GenSigInactiveValue" SG_ 804 FI_R_BrightnessSts 0;
BA_ "GenSigSendType" SG_ 804 FI_R_BrightnessSts 0;
BA_ "GenSigStartValue" SG_ 804 FI_R_BrightnessSts 0;
BA_ "GenSigInactiveValue" SG_ 804 FI_R_Sts 0;
BA_ "GenSigSendType" SG_ 804 FI_R_Sts 0;
BA_ "GenSigStartValue" SG_ 804 FI_R_Sts 1;
BA_ "GenSigInactiveValue" SG_ 804 FI_R_WarningRsp_BriLimt 0;
BA_ "GenSigSendType" SG_ 804 FI_R_WarningRsp_BriLimt 0;
BA_ "GenSigStartValue" SG_ 804 FI_R_WarningRsp_BriLimt 0;
BA_ "GenSigInactiveValue" SG_ 804 FI_R_WarningRsp_LCD 0;
BA_ "GenSigSendType" SG_ 804 FI_R_WarningRsp_LCD 0;
BA_ "GenSigStartValue" SG_ 804 FI_R_WarningRsp_LCD 0;
BA_ "GenSigInactiveValue" SG_ 804 FI_R_WarningRsp_LedDrvr 0;
BA_ "GenSigSendType" SG_ 804 FI_R_WarningRsp_LedDrvr 0;
BA_ "GenSigStartValue" SG_ 804 FI_R_WarningRsp_LedDrvr 0;
BA_ "GenSigInactiveValue" SG_ 804 FI_R_WarningRsp_LocalDimmingFail 0;
BA_ "GenSigSendType" SG_ 804 FI_R_WarningRsp_LocalDimmingFail 0;
BA_ "GenSigStartValue" SG_ 804 FI_R_WarningRsp_LocalDimmingFail 0;
BA_ "GenSigInactiveValue" SG_ 804 FI_R_WarningRsp_LockSts 0;
BA_ "GenSigSendType" SG_ 804 FI_R_WarningRsp_LockSts 0;
BA_ "GenSigStartValue" SG_ 804 FI_R_WarningRsp_LockSts 0;
BA_ "GenSigSendType" SG_ 804 FI_R_WarningRsp_PMICErr 0;
BA_ "GenSigSendType" SG_ 804 FI_R_WarningRsp_TconErr 0;
BA_ "GenSigInactiveValue" SG_ 804 FI_R_WarningRsp_Thi 0;
BA_ "GenSigSendType" SG_ 804 FI_R_WarningRsp_Thi 0;
BA_ "GenSigStartValue" SG_ 804 FI_R_WarningRsp_Thi 0;
BA_ "GenSigInactiveValue" SG_ 804 FI_R_WarningRsp_TouchErr 0;
BA_ "GenSigSendType" SG_ 804 FI_R_WarningRsp_TouchErr 0;
BA_ "GenSigStartValue" SG_ 804 FI_R_WarningRsp_TouchErr 0;
BA_ "GenSigInactiveValue" SG_ 804 FI_R_WarningRsp_UHiLo 0;
BA_ "GenSigSendType" SG_ 804 FI_R_WarningRsp_UHiLo 0;
BA_ "GenSigStartValue" SG_ 804 FI_R_WarningRsp_UHiLo 0;
BA_ "GenSigSendType" SG_ 1344 ActvWakeup_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 ActvWakeup_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 NetMngtCoorn_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 NetMngtCoorn_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 NetMngtCoorrSleepRdy_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 NetMngtCoorrSleepRdy_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN10_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN10_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN11_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN11_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN12_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN12_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN13_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN13_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN14_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN14_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN15_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN15_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN16_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN16_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN17_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN17_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN18_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN18_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN19_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN19_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN1_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN1_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN20_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN20_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN21_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN21_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN22_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN22_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN23_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN23_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN24_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN24_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN25_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN25_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN26_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN26_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN27_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN27_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN28_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN28_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN29_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN29_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN2_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN2_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN30_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN30_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN31_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN31_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN32_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN32_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN33_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN33_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN34_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN34_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN35_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN35_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN36_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN36_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN37_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN37_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN38_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN38_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN39_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN39_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN3_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN3_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN40_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN40_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN41_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN41_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN42_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN42_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN43_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN43_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN44_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN44_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN45_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN45_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN46_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN46_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN47_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN47_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN48_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN48_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN4_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN4_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN5_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN5_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN6_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN6_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN7_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN7_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN8_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN8_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PN9_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PN9_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 PtlNetInfo_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 PtlNetInfo_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 RepMsgReq_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 RepMsgReq_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 ResdBit1_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 ResdBit1_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 ResdBit2_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 ResdBit2_FI_RR 0;
BA_ "GenSigSendType" SG_ 1344 ScrNodId_FI_RR 7;
BA_ "GenSigStartValue" SG_ 1344 ScrNodId_FI_RR 0;
BA_ "GenSigInactiveValue" SG_ 1728 DiagRespData0_FI_R 0;
BA_ "GenSigSendType" SG_ 1728 DiagRespData0_FI_R 7;
BA_ "GenSigStartValue" SG_ 1728 DiagRespData0_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1728 DiagRespData10_FI_R 0;
BA_ "GenSigSendType" SG_ 1728 DiagRespData10_FI_R 7;
BA_ "GenSigStartValue" SG_ 1728 DiagRespData10_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1728 DiagRespData11_FI_R 0;
BA_ "GenSigSendType" SG_ 1728 DiagRespData11_FI_R 7;
BA_ "GenSigStartValue" SG_ 1728 DiagRespData11_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1728 DiagRespData12_FI_R 0;
BA_ "GenSigSendType" SG_ 1728 DiagRespData12_FI_R 7;
BA_ "GenSigStartValue" SG_ 1728 DiagRespData12_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1728 DiagRespData13_FI_R 0;
BA_ "GenSigSendType" SG_ 1728 DiagRespData13_FI_R 7;
BA_ "GenSigStartValue" SG_ 1728 DiagRespData13_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1728 DiagRespData14_FI_R 0;
BA_ "GenSigSendType" SG_ 1728 DiagRespData14_FI_R 7;
BA_ "GenSigStartValue" SG_ 1728 DiagRespData14_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1728 DiagRespData15_FI_R 0;
BA_ "GenSigSendType" SG_ 1728 DiagRespData15_FI_R 7;
BA_ "GenSigStartValue" SG_ 1728 DiagRespData15_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1728 DiagRespData1_FI_R 0;
BA_ "GenSigSendType" SG_ 1728 DiagRespData1_FI_R 7;
BA_ "GenSigStartValue" SG_ 1728 DiagRespData1_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1728 DiagRespData2_FI_R 0;
BA_ "GenSigSendType" SG_ 1728 DiagRespData2_FI_R 7;
BA_ "GenSigStartValue" SG_ 1728 DiagRespData2_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1728 DiagRespData3_FI_R 0;
BA_ "GenSigSendType" SG_ 1728 DiagRespData3_FI_R 7;
BA_ "GenSigStartValue" SG_ 1728 DiagRespData3_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1728 DiagRespData4_FI_R 0;
BA_ "GenSigSendType" SG_ 1728 DiagRespData4_FI_R 7;
BA_ "GenSigStartValue" SG_ 1728 DiagRespData4_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1728 DiagRespData5_FI_R 0;
BA_ "GenSigSendType" SG_ 1728 DiagRespData5_FI_R 7;
BA_ "GenSigStartValue" SG_ 1728 DiagRespData5_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1728 DiagRespData6_FI_R 0;
BA_ "GenSigSendType" SG_ 1728 DiagRespData6_FI_R 7;
BA_ "GenSigStartValue" SG_ 1728 DiagRespData6_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1728 DiagRespData7_FI_R 0;
BA_ "GenSigSendType" SG_ 1728 DiagRespData7_FI_R 7;
BA_ "GenSigStartValue" SG_ 1728 DiagRespData7_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1728 DiagRespData8_FI_R 0;
BA_ "GenSigSendType" SG_ 1728 DiagRespData8_FI_R 7;
BA_ "GenSigStartValue" SG_ 1728 DiagRespData8_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1728 DiagRespData9_FI_R 0;
BA_ "GenSigSendType" SG_ 1728 DiagRespData9_FI_R 7;
BA_ "GenSigStartValue" SG_ 1728 DiagRespData9_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 776 HUD_AgAdjmtSts 0;
BA_ "GenSigSendType" SG_ 776 HUD_AgAdjmtSts 0;
BA_ "GenSigStartValue" SG_ 776 HUD_AgAdjmtSts 10;
BA_ "GenSigInactiveValue" SG_ 776 HUD_HeiSts 0;
BA_ "GenSigSendType" SG_ 776 HUD_HeiSts 0;
BA_ "GenSigStartValue" SG_ 776 HUD_HeiSts 0;
BA_ "GenSigInactiveValue" SG_ 776 HUD_LCDbackLiSts 0;
BA_ "GenSigSendType" SG_ 776 HUD_LCDbackLiSts 0;
BA_ "GenSigStartValue" SG_ 776 HUD_LCDbackLiSts 0;
BA_ "GenSigInactiveValue" SG_ 776 HUD_Sts 0;
BA_ "GenSigSendType" SG_ 776 HUD_Sts 0;
BA_ "GenSigStartValue" SG_ 776 HUD_Sts 1;
BA_ "GenSigInactiveValue" SG_ 776 HUD_WarningRsp_BriLimd 0;
BA_ "GenSigSendType" SG_ 776 HUD_WarningRsp_BriLimd 0;
BA_ "GenSigStartValue" SG_ 776 HUD_WarningRsp_BriLimd 0;
BA_ "GenSigInactiveValue" SG_ 776 HUD_WarningRsp_LockSts 0;
BA_ "GenSigSendType" SG_ 776 HUD_WarningRsp_LockSts 0;
BA_ "GenSigStartValue" SG_ 776 HUD_WarningRsp_LockSts 0;
BA_ "GenSigInactiveValue" SG_ 776 HUD_WarningRsp_MotFail 0;
BA_ "GenSigSendType" SG_ 776 HUD_WarningRsp_MotFail 0;
BA_ "GenSigStartValue" SG_ 776 HUD_WarningRsp_MotFail 0;
BA_ "GenSigInactiveValue" SG_ 776 HUD_WarningRsp_TFT_LEDFail 0;
BA_ "GenSigSendType" SG_ 776 HUD_WarningRsp_TFT_LEDFail 0;
BA_ "GenSigStartValue" SG_ 776 HUD_WarningRsp_TFT_LEDFail 0;
BA_ "GenSigInactiveValue" SG_ 776 HUD_WarningRsp_Thi 0;
BA_ "GenSigSendType" SG_ 776 HUD_WarningRsp_Thi 0;
BA_ "GenSigStartValue" SG_ 776 HUD_WarningRsp_Thi 0;
BA_ "GenSigInactiveValue" SG_ 1333 ActvWakeup_HUD 0;
BA_ "GenSigSendType" SG_ 1333 ActvWakeup_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 ActvWakeup_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 NetMngtCoorn_HUD 0;
BA_ "GenSigSendType" SG_ 1333 NetMngtCoorn_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 NetMngtCoorn_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 NetMngtCoorrSleepRdy_HUD 0;
BA_ "GenSigSendType" SG_ 1333 NetMngtCoorrSleepRdy_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 NetMngtCoorrSleepRdy_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN10_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN10_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN10_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN11_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN11_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN11_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN12_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN12_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN12_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN13_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN13_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN13_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN14_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN14_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN14_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN15_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN15_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN15_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN16_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN16_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN16_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN17_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN17_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN17_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN18_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN18_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN18_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN19_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN19_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN19_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN1_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN1_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN1_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN20_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN20_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN20_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN21_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN21_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN21_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN22_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN22_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN22_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN23_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN23_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN23_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN24_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN24_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN24_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN25_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN25_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN25_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN26_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN26_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN26_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN27_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN27_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN27_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN28_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN28_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN28_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN29_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN29_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN29_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN2_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN2_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN2_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN30_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN30_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN30_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN31_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN31_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN31_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN32_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN32_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN32_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN33_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN33_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN33_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN34_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN34_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN34_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN35_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN35_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN35_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN36_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN36_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN36_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN37_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN37_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN37_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN38_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN38_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN38_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN39_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN39_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN39_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN3_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN3_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN3_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN40_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN40_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN40_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN41_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN41_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN41_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN42_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN42_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN42_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN43_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN43_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN43_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN44_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN44_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN44_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN45_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN45_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN45_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN46_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN46_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN46_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN47_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN47_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN47_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN48_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN48_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN48_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN4_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN4_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN4_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN5_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN5_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN5_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN6_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN6_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN6_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN7_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN7_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN7_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN8_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN8_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN8_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PN9_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PN9_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PN9_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 PtlNetInfo_HUD 0;
BA_ "GenSigSendType" SG_ 1333 PtlNetInfo_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 PtlNetInfo_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 RepMsgReq_HUD 0;
BA_ "GenSigSendType" SG_ 1333 RepMsgReq_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 RepMsgReq_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 ResdBit1_HUD 0;
BA_ "GenSigSendType" SG_ 1333 ResdBit1_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 ResdBit1_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 ResdBit2_HUD 0;
BA_ "GenSigSendType" SG_ 1333 ResdBit2_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 ResdBit2_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1333 ScrNodId_HUD 0;
BA_ "GenSigSendType" SG_ 1333 ScrNodId_HUD 7;
BA_ "GenSigStartValue" SG_ 1333 ScrNodId_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1717 DiagRespData0_HUD 0;
BA_ "GenSigSendType" SG_ 1717 DiagRespData0_HUD 7;
BA_ "GenSigStartValue" SG_ 1717 DiagRespData0_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1717 DiagRespData10_HUD 0;
BA_ "GenSigSendType" SG_ 1717 DiagRespData10_HUD 7;
BA_ "GenSigStartValue" SG_ 1717 DiagRespData10_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1717 DiagRespData11_HUD 0;
BA_ "GenSigSendType" SG_ 1717 DiagRespData11_HUD 7;
BA_ "GenSigStartValue" SG_ 1717 DiagRespData11_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1717 DiagRespData12_HUD 0;
BA_ "GenSigSendType" SG_ 1717 DiagRespData12_HUD 7;
BA_ "GenSigStartValue" SG_ 1717 DiagRespData12_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1717 DiagRespData13_HUD 0;
BA_ "GenSigSendType" SG_ 1717 DiagRespData13_HUD 7;
BA_ "GenSigStartValue" SG_ 1717 DiagRespData13_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1717 DiagRespData14_HUD 0;
BA_ "GenSigSendType" SG_ 1717 DiagRespData14_HUD 7;
BA_ "GenSigStartValue" SG_ 1717 DiagRespData14_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1717 DiagRespData15_HUD 0;
BA_ "GenSigSendType" SG_ 1717 DiagRespData15_HUD 7;
BA_ "GenSigStartValue" SG_ 1717 DiagRespData15_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1717 DiagRespData1_HUD 0;
BA_ "GenSigSendType" SG_ 1717 DiagRespData1_HUD 7;
BA_ "GenSigStartValue" SG_ 1717 DiagRespData1_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1717 DiagRespData2_HUD 0;
BA_ "GenSigSendType" SG_ 1717 DiagRespData2_HUD 7;
BA_ "GenSigStartValue" SG_ 1717 DiagRespData2_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1717 DiagRespData3_HUD 0;
BA_ "GenSigSendType" SG_ 1717 DiagRespData3_HUD 7;
BA_ "GenSigStartValue" SG_ 1717 DiagRespData3_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1717 DiagRespData4_HUD 0;
BA_ "GenSigSendType" SG_ 1717 DiagRespData4_HUD 7;
BA_ "GenSigStartValue" SG_ 1717 DiagRespData4_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1717 DiagRespData5_HUD 0;
BA_ "GenSigSendType" SG_ 1717 DiagRespData5_HUD 7;
BA_ "GenSigStartValue" SG_ 1717 DiagRespData5_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1717 DiagRespData6_HUD 0;
BA_ "GenSigSendType" SG_ 1717 DiagRespData6_HUD 7;
BA_ "GenSigStartValue" SG_ 1717 DiagRespData6_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1717 DiagRespData7_HUD 0;
BA_ "GenSigSendType" SG_ 1717 DiagRespData7_HUD 7;
BA_ "GenSigStartValue" SG_ 1717 DiagRespData7_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1717 DiagRespData8_HUD 0;
BA_ "GenSigSendType" SG_ 1717 DiagRespData8_HUD 7;
BA_ "GenSigStartValue" SG_ 1717 DiagRespData8_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1717 DiagRespData9_HUD 0;
BA_ "GenSigSendType" SG_ 1717 DiagRespData9_HUD 7;
BA_ "GenSigStartValue" SG_ 1717 DiagRespData9_HUD 0;
BA_ "GenSigSendType" SG_ 741 IC_LCDbackLiSts_L 0;
BA_ "GenSigStartValue" SG_ 741 IC_LCDbackLiSts_L 0;
BA_ "GenSigSendType" SG_ 741 IC_LCDbackLiSts_R 0;
BA_ "GenSigStartValue" SG_ 741 IC_LCDbackLiSts_R 0;
BA_ "GenSigInactiveValue" SG_ 741 IC_Sts 0;
BA_ "GenSigSendType" SG_ 741 IC_Sts 0;
BA_ "GenSigStartValue" SG_ 741 IC_Sts 1;
BA_ "GenSigSendType" SG_ 741 IC_WarningRsp_BriLimt_L 0;
BA_ "GenSigStartValue" SG_ 741 IC_WarningRsp_BriLimt_L 0;
BA_ "GenSigSendType" SG_ 741 IC_WarningRsp_BriLimt_R 0;
BA_ "GenSigStartValue" SG_ 741 IC_WarningRsp_BriLimt_R 0;
BA_ "GenSigSendType" SG_ 741 IC_WarningRsp_LCDFail_L 0;
BA_ "GenSigStartValue" SG_ 741 IC_WarningRsp_LCDFail_L 0;
BA_ "GenSigSendType" SG_ 741 IC_WarningRsp_LCDFail_R 0;
BA_ "GenSigStartValue" SG_ 741 IC_WarningRsp_LCDFail_R 0;
BA_ "GenSigSendType" SG_ 741 IC_WarningRsp_LedDrvr_L 0;
BA_ "GenSigStartValue" SG_ 741 IC_WarningRsp_LedDrvr_L 0;
BA_ "GenSigSendType" SG_ 741 IC_WarningRsp_LedDrvr_R 0;
BA_ "GenSigStartValue" SG_ 741 IC_WarningRsp_LedDrvr_R 0;
BA_ "GenSigSendType" SG_ 741 IC_WarningRsp_LocalDimmingFail_L 0;
BA_ "GenSigStartValue" SG_ 741 IC_WarningRsp_LocalDimmingFail_L 0;
BA_ "GenSigSendType" SG_ 741 IC_WarningRsp_LocalDimmingFail_R 0;
BA_ "GenSigStartValue" SG_ 741 IC_WarningRsp_LocalDimmingFail_R 0;
BA_ "GenSigInactiveValue" SG_ 741 IC_WarningRsp_LockSts 0;
BA_ "GenSigSendType" SG_ 741 IC_WarningRsp_LockSts 0;
BA_ "GenSigStartValue" SG_ 741 IC_WarningRsp_LockSts 0;
BA_ "GenSigSendType" SG_ 741 IC_WarningRsp_Thi_L 0;
BA_ "GenSigStartValue" SG_ 741 IC_WarningRsp_Thi_L 0;
BA_ "GenSigSendType" SG_ 741 IC_WarningRsp_Thi_R 0;
BA_ "GenSigStartValue" SG_ 741 IC_WarningRsp_Thi_R 0;
BA_ "GenSigSendType" SG_ 741 IC_WarningRsp_UHiLo_L 0;
BA_ "GenSigStartValue" SG_ 741 IC_WarningRsp_UHiLo_L 0;
BA_ "GenSigSendType" SG_ 741 IC_WarningRsp_UHiLo_R 0;
BA_ "GenSigStartValue" SG_ 741 IC_WarningRsp_UHiLo_R 0;
BA_ "GenSigInactiveValue" SG_ 1331 ActvWakeup_IC 0;
BA_ "GenSigSendType" SG_ 1331 ActvWakeup_IC 7;
BA_ "GenSigStartValue" SG_ 1331 ActvWakeup_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 NetMngtCoorn_IC 0;
BA_ "GenSigSendType" SG_ 1331 NetMngtCoorn_IC 7;
BA_ "GenSigStartValue" SG_ 1331 NetMngtCoorn_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 NetMngtCoorrSleepRdy_IC 0;
BA_ "GenSigSendType" SG_ 1331 NetMngtCoorrSleepRdy_IC 7;
BA_ "GenSigStartValue" SG_ 1331 NetMngtCoorrSleepRdy_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN10_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN10_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN10_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN11_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN11_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN11_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN12_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN12_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN12_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN13_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN13_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN13_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN14_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN14_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN14_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN15_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN15_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN15_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN16_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN16_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN16_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN17_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN17_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN17_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN18_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN18_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN18_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN19_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN19_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN19_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN1_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN1_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN1_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN20_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN20_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN20_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN21_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN21_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN21_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN22_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN22_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN22_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN23_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN23_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN23_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN24_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN24_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN24_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN25_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN25_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN25_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN26_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN26_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN26_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN27_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN27_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN27_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN28_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN28_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN28_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN29_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN29_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN29_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN2_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN2_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN2_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN30_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN30_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN30_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN31_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN31_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN31_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN32_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN32_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN32_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN33_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN33_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN33_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN34_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN34_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN34_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN35_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN35_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN35_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN36_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN36_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN36_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN37_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN37_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN37_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN38_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN38_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN38_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN39_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN39_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN39_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN3_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN3_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN3_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN40_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN40_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN40_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN41_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN41_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN41_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN42_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN42_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN42_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN43_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN43_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN43_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN44_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN44_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN44_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN45_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN45_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN45_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN46_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN46_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN46_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN47_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN47_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN47_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN48_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN48_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN48_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN4_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN4_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN4_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN5_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN5_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN5_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN6_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN6_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN6_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN7_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN7_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN7_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN8_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN8_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN8_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PN9_IC 0;
BA_ "GenSigSendType" SG_ 1331 PN9_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PN9_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 PtlNetInfo_IC 0;
BA_ "GenSigSendType" SG_ 1331 PtlNetInfo_IC 7;
BA_ "GenSigStartValue" SG_ 1331 PtlNetInfo_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 RepMsgReq_IC 0;
BA_ "GenSigSendType" SG_ 1331 RepMsgReq_IC 7;
BA_ "GenSigStartValue" SG_ 1331 RepMsgReq_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 ResdBit1_IC 0;
BA_ "GenSigSendType" SG_ 1331 ResdBit1_IC 7;
BA_ "GenSigStartValue" SG_ 1331 ResdBit1_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 ResdBit2_IC 0;
BA_ "GenSigSendType" SG_ 1331 ResdBit2_IC 7;
BA_ "GenSigStartValue" SG_ 1331 ResdBit2_IC 0;
BA_ "GenSigInactiveValue" SG_ 1331 ScrNodId_IC 0;
BA_ "GenSigSendType" SG_ 1331 ScrNodId_IC 7;
BA_ "GenSigStartValue" SG_ 1331 ScrNodId_IC 0;
BA_ "GenSigInactiveValue" SG_ 1715 DiagRespData0_IC 0;
BA_ "GenSigSendType" SG_ 1715 DiagRespData0_IC 7;
BA_ "GenSigStartValue" SG_ 1715 DiagRespData0_IC 0;
BA_ "GenSigInactiveValue" SG_ 1715 DiagRespData10_IC 0;
BA_ "GenSigSendType" SG_ 1715 DiagRespData10_IC 7;
BA_ "GenSigStartValue" SG_ 1715 DiagRespData10_IC 0;
BA_ "GenSigInactiveValue" SG_ 1715 DiagRespData11_IC 0;
BA_ "GenSigSendType" SG_ 1715 DiagRespData11_IC 7;
BA_ "GenSigStartValue" SG_ 1715 DiagRespData11_IC 0;
BA_ "GenSigInactiveValue" SG_ 1715 DiagRespData12_IC 0;
BA_ "GenSigSendType" SG_ 1715 DiagRespData12_IC 7;
BA_ "GenSigStartValue" SG_ 1715 DiagRespData12_IC 0;
BA_ "GenSigInactiveValue" SG_ 1715 DiagRespData13_IC 0;
BA_ "GenSigSendType" SG_ 1715 DiagRespData13_IC 7;
BA_ "GenSigStartValue" SG_ 1715 DiagRespData13_IC 0;
BA_ "GenSigInactiveValue" SG_ 1715 DiagRespData14_IC 0;
BA_ "GenSigSendType" SG_ 1715 DiagRespData14_IC 7;
BA_ "GenSigStartValue" SG_ 1715 DiagRespData14_IC 0;
BA_ "GenSigInactiveValue" SG_ 1715 DiagRespData15_IC 0;
BA_ "GenSigSendType" SG_ 1715 DiagRespData15_IC 7;
BA_ "GenSigStartValue" SG_ 1715 DiagRespData15_IC 0;
BA_ "GenSigInactiveValue" SG_ 1715 DiagRespData1_IC 0;
BA_ "GenSigSendType" SG_ 1715 DiagRespData1_IC 7;
BA_ "GenSigStartValue" SG_ 1715 DiagRespData1_IC 0;
BA_ "GenSigInactiveValue" SG_ 1715 DiagRespData2_IC 0;
BA_ "GenSigSendType" SG_ 1715 DiagRespData2_IC 7;
BA_ "GenSigStartValue" SG_ 1715 DiagRespData2_IC 0;
BA_ "GenSigInactiveValue" SG_ 1715 DiagRespData3_IC 0;
BA_ "GenSigSendType" SG_ 1715 DiagRespData3_IC 7;
BA_ "GenSigStartValue" SG_ 1715 DiagRespData3_IC 0;
BA_ "GenSigInactiveValue" SG_ 1715 DiagRespData4_IC 0;
BA_ "GenSigSendType" SG_ 1715 DiagRespData4_IC 7;
BA_ "GenSigStartValue" SG_ 1715 DiagRespData4_IC 0;
BA_ "GenSigInactiveValue" SG_ 1715 DiagRespData5_IC 0;
BA_ "GenSigSendType" SG_ 1715 DiagRespData5_IC 7;
BA_ "GenSigStartValue" SG_ 1715 DiagRespData5_IC 0;
BA_ "GenSigInactiveValue" SG_ 1715 DiagRespData6_IC 0;
BA_ "GenSigSendType" SG_ 1715 DiagRespData6_IC 7;
BA_ "GenSigStartValue" SG_ 1715 DiagRespData6_IC 0;
BA_ "GenSigInactiveValue" SG_ 1715 DiagRespData7_IC 0;
BA_ "GenSigSendType" SG_ 1715 DiagRespData7_IC 7;
BA_ "GenSigStartValue" SG_ 1715 DiagRespData7_IC 0;
BA_ "GenSigInactiveValue" SG_ 1715 DiagRespData8_IC 0;
BA_ "GenSigSendType" SG_ 1715 DiagRespData8_IC 7;
BA_ "GenSigStartValue" SG_ 1715 DiagRespData8_IC 0;
BA_ "GenSigInactiveValue" SG_ 1715 DiagRespData9_IC 0;
BA_ "GenSigSendType" SG_ 1715 DiagRespData9_IC 7;
BA_ "GenSigStartValue" SG_ 1715 DiagRespData9_IC 0;
BA_ "GenSigInactiveValue" SG_ 755 ICS_BrightnessSts 0;
BA_ "GenSigSendType" SG_ 755 ICS_BrightnessSts 0;
BA_ "GenSigStartValue" SG_ 755 ICS_BrightnessSts 0;
BA_ "GenSigInactiveValue" SG_ 755 ICS_Sts 0;
BA_ "GenSigSendType" SG_ 755 ICS_Sts 0;
BA_ "GenSigStartValue" SG_ 755 ICS_Sts 1;
BA_ "GenSigInactiveValue" SG_ 755 ICS_WarningRsp_BriLimt 0;
BA_ "GenSigSendType" SG_ 755 ICS_WarningRsp_BriLimt 0;
BA_ "GenSigStartValue" SG_ 755 ICS_WarningRsp_BriLimt 0;
BA_ "GenSigSendType" SG_ 755 ICS_WarningRsp_BridgeErr 0;
BA_ "GenSigInactiveValue" SG_ 755 ICS_WarningRsp_LockSts 0;
BA_ "GenSigSendType" SG_ 755 ICS_WarningRsp_LockSts 0;
BA_ "GenSigStartValue" SG_ 755 ICS_WarningRsp_LockSts 0;
BA_ "GenSigSendType" SG_ 755 ICS_WarningRsp_PMICErr 0;
BA_ "GenSigInactiveValue" SG_ 755 ICS_WarningRsp_TconErr 0;
BA_ "GenSigSendType" SG_ 755 ICS_WarningRsp_TconErr 0;
BA_ "GenSigStartValue" SG_ 755 ICS_WarningRsp_TconErr 0;
BA_ "GenSigInactiveValue" SG_ 755 ICS_WarningRsp_Thi 0;
BA_ "GenSigSendType" SG_ 755 ICS_WarningRsp_Thi 0;
BA_ "GenSigStartValue" SG_ 755 ICS_WarningRsp_Thi 0;
BA_ "GenSigInactiveValue" SG_ 755 ICS_WarningRsp_TouchErr 0;
BA_ "GenSigSendType" SG_ 755 ICS_WarningRsp_TouchErr 0;
BA_ "GenSigStartValue" SG_ 755 ICS_WarningRsp_TouchErr 0;
BA_ "GenSigInactiveValue" SG_ 755 ICS_WarningRsp_UHiLo 0;
BA_ "GenSigSendType" SG_ 755 ICS_WarningRsp_UHiLo 0;
BA_ "GenSigStartValue" SG_ 755 ICS_WarningRsp_UHiLo 0;
BA_ "GenSigInactiveValue" SG_ 1334 ActvWakeup_ICS 0;
BA_ "GenSigSendType" SG_ 1334 ActvWakeup_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 ActvWakeup_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 NetMngtCoorn_ICS 0;
BA_ "GenSigSendType" SG_ 1334 NetMngtCoorn_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 NetMngtCoorn_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 NetMngtCoorrSleepRdy_ICS 0;
BA_ "GenSigSendType" SG_ 1334 NetMngtCoorrSleepRdy_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 NetMngtCoorrSleepRdy_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN10_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN10_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN10_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN11_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN11_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN11_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN12_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN12_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN12_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN13_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN13_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN13_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN14_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN14_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN14_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN15_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN15_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN15_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN16_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN16_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN16_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN17_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN17_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN17_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN18_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN18_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN18_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN19_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN19_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN19_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN1_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN1_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN1_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN20_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN20_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN20_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN21_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN21_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN21_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN22_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN22_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN22_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN23_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN23_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN23_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN24_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN24_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN24_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN25_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN25_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN25_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN26_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN26_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN26_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN27_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN27_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN27_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN28_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN28_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN28_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN29_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN29_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN29_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN2_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN2_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN2_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN30_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN30_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN30_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN31_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN31_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN31_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN32_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN32_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN32_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN33_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN33_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN33_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN34_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN34_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN34_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN35_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN35_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN35_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN36_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN36_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN36_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN37_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN37_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN37_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN38_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN38_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN38_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN39_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN39_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN39_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN3_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN3_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN3_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN40_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN40_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN40_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN41_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN41_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN41_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN42_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN42_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN42_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN43_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN43_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN43_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN44_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN44_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN44_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN45_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN45_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN45_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN46_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN46_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN46_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN47_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN47_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN47_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN48_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN48_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN48_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN4_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN4_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN4_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN5_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN5_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN5_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN6_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN6_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN6_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN7_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN7_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN7_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN8_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN8_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN8_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PN9_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PN9_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PN9_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 PtlNetInfo_ICS 0;
BA_ "GenSigSendType" SG_ 1334 PtlNetInfo_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 PtlNetInfo_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 RepMsgReq_ICS 0;
BA_ "GenSigSendType" SG_ 1334 RepMsgReq_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 RepMsgReq_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 ResdBit1_ICS 0;
BA_ "GenSigSendType" SG_ 1334 ResdBit1_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 ResdBit1_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 ResdBit2_ICS 0;
BA_ "GenSigSendType" SG_ 1334 ResdBit2_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 ResdBit2_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1334 ScrNodId_ICS 0;
BA_ "GenSigSendType" SG_ 1334 ScrNodId_ICS 7;
BA_ "GenSigStartValue" SG_ 1334 ScrNodId_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1718 DiagRespData0_ICS 0;
BA_ "GenSigSendType" SG_ 1718 DiagRespData0_ICS 7;
BA_ "GenSigStartValue" SG_ 1718 DiagRespData0_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1718 DiagRespData10_ICS 0;
BA_ "GenSigSendType" SG_ 1718 DiagRespData10_ICS 7;
BA_ "GenSigStartValue" SG_ 1718 DiagRespData10_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1718 DiagRespData11_ICS 0;
BA_ "GenSigSendType" SG_ 1718 DiagRespData11_ICS 7;
BA_ "GenSigStartValue" SG_ 1718 DiagRespData11_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1718 DiagRespData12_ICS 0;
BA_ "GenSigSendType" SG_ 1718 DiagRespData12_ICS 7;
BA_ "GenSigStartValue" SG_ 1718 DiagRespData12_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1718 DiagRespData13_ICS 0;
BA_ "GenSigSendType" SG_ 1718 DiagRespData13_ICS 7;
BA_ "GenSigStartValue" SG_ 1718 DiagRespData13_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1718 DiagRespData14_ICS 0;
BA_ "GenSigSendType" SG_ 1718 DiagRespData14_ICS 7;
BA_ "GenSigStartValue" SG_ 1718 DiagRespData14_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1718 DiagRespData15_ICS 0;
BA_ "GenSigSendType" SG_ 1718 DiagRespData15_ICS 7;
BA_ "GenSigStartValue" SG_ 1718 DiagRespData15_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1718 DiagRespData1_ICS 0;
BA_ "GenSigSendType" SG_ 1718 DiagRespData1_ICS 7;
BA_ "GenSigStartValue" SG_ 1718 DiagRespData1_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1718 DiagRespData2_ICS 0;
BA_ "GenSigSendType" SG_ 1718 DiagRespData2_ICS 7;
BA_ "GenSigStartValue" SG_ 1718 DiagRespData2_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1718 DiagRespData3_ICS 0;
BA_ "GenSigSendType" SG_ 1718 DiagRespData3_ICS 7;
BA_ "GenSigStartValue" SG_ 1718 DiagRespData3_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1718 DiagRespData4_ICS 0;
BA_ "GenSigSendType" SG_ 1718 DiagRespData4_ICS 7;
BA_ "GenSigStartValue" SG_ 1718 DiagRespData4_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1718 DiagRespData5_ICS 0;
BA_ "GenSigSendType" SG_ 1718 DiagRespData5_ICS 7;
BA_ "GenSigStartValue" SG_ 1718 DiagRespData5_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1718 DiagRespData6_ICS 0;
BA_ "GenSigSendType" SG_ 1718 DiagRespData6_ICS 7;
BA_ "GenSigStartValue" SG_ 1718 DiagRespData6_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1718 DiagRespData7_ICS 0;
BA_ "GenSigSendType" SG_ 1718 DiagRespData7_ICS 7;
BA_ "GenSigStartValue" SG_ 1718 DiagRespData7_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1718 DiagRespData8_ICS 0;
BA_ "GenSigSendType" SG_ 1718 DiagRespData8_ICS 7;
BA_ "GenSigStartValue" SG_ 1718 DiagRespData8_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1718 DiagRespData9_ICS 0;
BA_ "GenSigSendType" SG_ 1718 DiagRespData9_ICS 7;
BA_ "GenSigStartValue" SG_ 1718 DiagRespData9_ICS 0;
BA_ "GenSigSendType" SG_ 453 HALO_WORK_STATUS 0;
BA_ "GenSigStartValue" SG_ 453 HALO_WORK_STATUS 0;
BA_ "GenSigSendType" SG_ 453 MATEPosPitch 0;
BA_ "GenSigStartValue" SG_ 453 MATEPosPitch 0;
BA_ "GenSigInactiveValue" SG_ 453 MATEPosYaw 0;
BA_ "GenSigSendType" SG_ 453 MATEPosYaw 0;
BA_ "GenSigStartValue" SG_ 453 MATEPosYaw 177;
BA_ "GenSigInactiveValue" SG_ 453 NOMI_BackLiSts 0;
BA_ "GenSigSendType" SG_ 453 NOMI_BackLiSts 0;
BA_ "GenSigStartValue" SG_ 453 NOMI_BackLiSts 0;
BA_ "GenSigInactiveValue" SG_ 453 NOMI_Sts 0;
BA_ "GenSigSendType" SG_ 453 NOMI_Sts 0;
BA_ "GenSigStartValue" SG_ 453 NOMI_Sts 0;
BA_ "GenSigInactiveValue" SG_ 453 NOMI_Type 0;
BA_ "GenSigSendType" SG_ 453 NOMI_Type 0;
BA_ "GenSigStartValue" SG_ 453 NOMI_Type 7;
BA_ "GenSigInactiveValue" SG_ 453 NOMI_WarningRsp 0;
BA_ "GenSigSendType" SG_ 453 NOMI_WarningRsp 0;
BA_ "GenSigStartValue" SG_ 453 NOMI_WarningRsp 0;
BA_ "GenSigSendType" SG_ 576 MateRespNfcUid0 0;
BA_ "GenSigSendType" SG_ 576 MateRespNfcUid1 0;
BA_ "GenSigSendType" SG_ 576 MateRespNfcUid2 0;
BA_ "GenSigSendType" SG_ 576 MateRespNfcUid3 0;
BA_ "GenSigSendType" SG_ 576 MateRespNfcUid4 0;
BA_ "GenSigSendType" SG_ 576 MateRespNfcUid5 0;
BA_ "GenSigSendType" SG_ 576 MateRespNfcUid6 0;
BA_ "GenSigSendType" SG_ 576 MateRespNfcUid7 0;
BA_ "GenSigSendType" SG_ 576 MateRespNioUid0 0;
BA_ "GenSigSendType" SG_ 576 MateRespNioUid1 0;
BA_ "GenSigSendType" SG_ 576 MateRespNioUid2 0;
BA_ "GenSigSendType" SG_ 576 MateRespNioUid3 0;
BA_ "GenSigSendType" SG_ 576 MateRespNioUid4 0;
BA_ "GenSigSendType" SG_ 576 MateRespNioUid5 0;
BA_ "GenSigSendType" SG_ 576 MateRespNioUid6 0;
BA_ "GenSigSendType" SG_ 576 MateRespNioUid7 0;
BA_ "GenSigInactiveValue" SG_ 1163 MATEFrameRPCount 0;
BA_ "GenSigSendType" SG_ 1163 MATEFrameRPCount 7;
BA_ "GenSigStartValue" SG_ 1163 MATEFrameRPCount 0;
BA_ "GenSigInactiveValue" SG_ 1163 MATEFrameRYCount 0;
BA_ "GenSigSendType" SG_ 1163 MATEFrameRYCount 7;
BA_ "GenSigStartValue" SG_ 1163 MATEFrameRYCount 0;
BA_ "GenSigInactiveValue" SG_ 1163 MATERespFramePAck 0;
BA_ "GenSigSendType" SG_ 1163 MATERespFramePAck 7;
BA_ "GenSigStartValue" SG_ 1163 MATERespFramePAck 7;
BA_ "GenSigInactiveValue" SG_ 1163 MATERespFrameYAck 0;
BA_ "GenSigSendType" SG_ 1163 MATERespFrameYAck 7;
BA_ "GenSigStartValue" SG_ 1163 MATERespFrameYAck 7;
BA_ "GenSigSendType" SG_ 1163 MateRespCmdAction 7;
BA_ "GenSigStartValue" SG_ 1163 MateRespCmdAction 7;
BA_ "GenSigSendType" SG_ 1163 MateRespCmdActionCount 7;
BA_ "GenSigSendType" SG_ 1169 HALO_RSP_CODE 7;
BA_ "GenSigStartValue" SG_ 1169 HALO_RSP_CODE 255;
BA_ "GenSigSendType" SG_ 1169 HALO_RSP_Count 7;
BA_ "GenSigStartValue" SG_ 1169 HALO_RSP_Count 0;
BA_ "GenSigSendType" SG_ 1169 HALO_RSP_ID 7;
BA_ "GenSigStartValue" SG_ 1169 HALO_RSP_ID 0;
BA_ "GenSigInactiveValue" SG_ 1341 ActvWakeup_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 ActvWakeup_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 ActvWakeup_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 NetMngtCoorn_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 NetMngtCoorn_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 NetMngtCoorn_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 NetMngtCoorrSleepRdy_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 NetMngtCoorrSleepRdy_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 NetMngtCoorrSleepRdy_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN10_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN10_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN10_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN11_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN11_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN11_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN12_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN12_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN12_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN13_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN13_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN13_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN14_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN14_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN14_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN15_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN15_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN15_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN16_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN16_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN16_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN17_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN17_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN17_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN18_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN18_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN18_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN19_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN19_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN19_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN1_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN1_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN1_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN20_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN20_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN20_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN21_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN21_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN21_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN22_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN22_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN22_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN23_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN23_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN23_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN24_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN24_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN24_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN25_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN25_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN25_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN26_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN26_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN26_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN27_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN27_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN27_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN28_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN28_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN28_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN29_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN29_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN29_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN2_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN2_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN2_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN30_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN30_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN30_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN31_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN31_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN31_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN32_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN32_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN32_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN33_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN33_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN33_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN34_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN34_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN34_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN35_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN35_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN35_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN36_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN36_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN36_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN37_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN37_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN37_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN38_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN38_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN38_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN39_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN39_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN39_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN3_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN3_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN3_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN40_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN40_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN40_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN41_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN41_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN41_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN42_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN42_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN42_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN43_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN43_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN43_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN44_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN44_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN44_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN45_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN45_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN45_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN46_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN46_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN46_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN47_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN47_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN47_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN48_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN48_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN48_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN4_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN4_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN4_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN5_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN5_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN5_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN6_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN6_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN6_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN7_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN7_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN7_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN8_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN8_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN8_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PN9_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PN9_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PN9_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 PtlNetInfo_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 PtlNetInfo_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 PtlNetInfo_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 RepMsgReq_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 RepMsgReq_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 RepMsgReq_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 ResdBit1_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 ResdBit1_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 ResdBit1_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 ResdBit2_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 ResdBit2_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 ResdBit2_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1341 ScrNodId_NOMI 0;
BA_ "GenSigSendType" SG_ 1341 ScrNodId_NOMI 7;
BA_ "GenSigStartValue" SG_ 1341 ScrNodId_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1725 DiagRespData0_NOMI 0;
BA_ "GenSigSendType" SG_ 1725 DiagRespData0_NOMI 7;
BA_ "GenSigStartValue" SG_ 1725 DiagRespData0_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1725 DiagRespData10_NOMI 0;
BA_ "GenSigSendType" SG_ 1725 DiagRespData10_NOMI 7;
BA_ "GenSigStartValue" SG_ 1725 DiagRespData10_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1725 DiagRespData11_NOMI 0;
BA_ "GenSigSendType" SG_ 1725 DiagRespData11_NOMI 7;
BA_ "GenSigStartValue" SG_ 1725 DiagRespData11_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1725 DiagRespData12_NOMI 0;
BA_ "GenSigSendType" SG_ 1725 DiagRespData12_NOMI 7;
BA_ "GenSigStartValue" SG_ 1725 DiagRespData12_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1725 DiagRespData13_NOMI 0;
BA_ "GenSigSendType" SG_ 1725 DiagRespData13_NOMI 7;
BA_ "GenSigStartValue" SG_ 1725 DiagRespData13_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1725 DiagRespData14_NOMI 0;
BA_ "GenSigSendType" SG_ 1725 DiagRespData14_NOMI 7;
BA_ "GenSigStartValue" SG_ 1725 DiagRespData14_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1725 DiagRespData15_NOMI 0;
BA_ "GenSigSendType" SG_ 1725 DiagRespData15_NOMI 7;
BA_ "GenSigStartValue" SG_ 1725 DiagRespData15_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1725 DiagRespData1_NOMI 0;
BA_ "GenSigSendType" SG_ 1725 DiagRespData1_NOMI 7;
BA_ "GenSigStartValue" SG_ 1725 DiagRespData1_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1725 DiagRespData2_NOMI 0;
BA_ "GenSigSendType" SG_ 1725 DiagRespData2_NOMI 7;
BA_ "GenSigStartValue" SG_ 1725 DiagRespData2_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1725 DiagRespData3_NOMI 0;
BA_ "GenSigSendType" SG_ 1725 DiagRespData3_NOMI 7;
BA_ "GenSigStartValue" SG_ 1725 DiagRespData3_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1725 DiagRespData4_NOMI 0;
BA_ "GenSigSendType" SG_ 1725 DiagRespData4_NOMI 7;
BA_ "GenSigStartValue" SG_ 1725 DiagRespData4_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1725 DiagRespData5_NOMI 0;
BA_ "GenSigSendType" SG_ 1725 DiagRespData5_NOMI 7;
BA_ "GenSigStartValue" SG_ 1725 DiagRespData5_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1725 DiagRespData6_NOMI 0;
BA_ "GenSigSendType" SG_ 1725 DiagRespData6_NOMI 7;
BA_ "GenSigStartValue" SG_ 1725 DiagRespData6_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1725 DiagRespData7_NOMI 0;
BA_ "GenSigSendType" SG_ 1725 DiagRespData7_NOMI 7;
BA_ "GenSigStartValue" SG_ 1725 DiagRespData7_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1725 DiagRespData8_NOMI 0;
BA_ "GenSigSendType" SG_ 1725 DiagRespData8_NOMI 7;
BA_ "GenSigStartValue" SG_ 1725 DiagRespData8_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1725 DiagRespData9_NOMI 0;
BA_ "GenSigSendType" SG_ 1725 DiagRespData9_NOMI 7;
BA_ "GenSigStartValue" SG_ 1725 DiagRespData9_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 671 MenuPushSwtSts 0;
BA_ "GenSigSendType" SG_ 671 MenuPushSwtSts 0;
BA_ "GenSigStartValue" SG_ 671 MenuPushSwtSts 0;
BA_ "GenSigInactiveValue" SG_ 671 NOMICtrlPushSwtSts 0;
BA_ "GenSigSendType" SG_ 671 NOMICtrlPushSwtSts 0;
BA_ "GenSigStartValue" SG_ 671 NOMICtrlPushSwtSts 0;
BA_ "GenSigInactiveValue" SG_ 671 SWCFailSts 0;
BA_ "GenSigSendType" SG_ 671 SWCFailSts 0;
BA_ "GenSigStartValue" SG_ 671 SWCFailSts 0;
BA_ "GenSigInactiveValue" SG_ 671 SWCLe_CentPushSwtSts 0;
BA_ "GenSigSendType" SG_ 671 SWCLe_CentPushSwtSts 0;
BA_ "GenSigStartValue" SG_ 671 SWCLe_CentPushSwtSts 0;
BA_ "GenSigInactiveValue" SG_ 671 SWCLe_DwnPushSwtSts 0;
BA_ "GenSigSendType" SG_ 671 SWCLe_DwnPushSwtSts 0;
BA_ "GenSigStartValue" SG_ 671 SWCLe_DwnPushSwtSts 0;
BA_ "GenSigInactiveValue" SG_ 671 SWCLe_LePushSwtSts 0;
BA_ "GenSigSendType" SG_ 671 SWCLe_LePushSwtSts 0;
BA_ "GenSigStartValue" SG_ 671 SWCLe_LePushSwtSts 0;
BA_ "GenSigInactiveValue" SG_ 671 SWCLe_RiPushSwtSts 0;
BA_ "GenSigSendType" SG_ 671 SWCLe_RiPushSwtSts 0;
BA_ "GenSigStartValue" SG_ 671 SWCLe_RiPushSwtSts 0;
BA_ "GenSigInactiveValue" SG_ 671 SWCLe_UpPushSwtSts 0;
BA_ "GenSigSendType" SG_ 671 SWCLe_UpPushSwtSts 0;
BA_ "GenSigStartValue" SG_ 671 SWCLe_UpPushSwtSts 0;
BA_ "GenSigInactiveValue" SG_ 671 SWCRi_CentPushSwtSts 0;
BA_ "GenSigSendType" SG_ 671 SWCRi_CentPushSwtSts 0;
BA_ "GenSigStartValue" SG_ 671 SWCRi_CentPushSwtSts 0;
BA_ "GenSigInactiveValue" SG_ 671 SWCRi_DwnPushSwtSts 0;
BA_ "GenSigSendType" SG_ 671 SWCRi_DwnPushSwtSts 0;
BA_ "GenSigStartValue" SG_ 671 SWCRi_DwnPushSwtSts 0;
BA_ "GenSigInactiveValue" SG_ 671 SWCRi_LePushSwtSts 0;
BA_ "GenSigSendType" SG_ 671 SWCRi_LePushSwtSts 0;
BA_ "GenSigStartValue" SG_ 671 SWCRi_LePushSwtSts 0;
BA_ "GenSigInactiveValue" SG_ 671 SWCRi_RiPushSwtSts 0;
BA_ "GenSigSendType" SG_ 671 SWCRi_RiPushSwtSts 0;
BA_ "GenSigStartValue" SG_ 671 SWCRi_RiPushSwtSts 0;
BA_ "GenSigInactiveValue" SG_ 671 SWCRi_UpPushSwtSts 0;
BA_ "GenSigSendType" SG_ 671 SWCRi_UpPushSwtSts 0;
BA_ "GenSigStartValue" SG_ 671 SWCRi_UpPushSwtSts 0;
BA_ "GenSigInactiveValue" SG_ 671 SWC_29F_CRC 0;
BA_ "GenSigSendType" SG_ 671 SWC_29F_CRC 0;
BA_ "GenSigStartValue" SG_ 671 SWC_29F_CRC 0;
BA_ "GenSigInactiveValue" SG_ 671 SWC_29F_MsgCntr 0;
BA_ "GenSigSendType" SG_ 671 SWC_29F_MsgCntr 0;
BA_ "GenSigStartValue" SG_ 671 SWC_29F_MsgCntr 0;
BA_ "GenSigInactiveValue" SG_ 671 Spare01PushSwtSts 0;
BA_ "GenSigSendType" SG_ 671 Spare01PushSwtSts 0;
BA_ "GenSigStartValue" SG_ 671 Spare01PushSwtSts 0;
BA_ "GenSigSendType" SG_ 77 SWCLeSts 0;
BA_ "GenSigStartValue" SG_ 77 SWCLeSts 0;
BA_ "GenSigSendType" SG_ 77 SWCRiSts 0;
BA_ "GenSigStartValue" SG_ 77 SWCRiSts 0;
BA_ "GenSigSendType" SG_ 77 SWCRi_BarBtmPressed 0;
BA_ "GenSigStartValue" SG_ 77 SWCRi_BarBtmPressed 0;
BA_ "GenSigSendType" SG_ 77 SWCRi_BarPos 0;
BA_ "GenSigStartValue" SG_ 77 SWCRi_BarPos 0;
BA_ "GenSigSendType" SG_ 77 SWCRi_BarTopPressed 0;
BA_ "GenSigStartValue" SG_ 77 SWCRi_BarTopPressed 0;
BA_ "GenSigSendType" SG_ 77 SWCRi_BarTouch 0;
BA_ "GenSigStartValue" SG_ 77 SWCRi_BarTouch 0;
BA_ "GenSigInactiveValue" SG_ 1322 ActvWakeup_SWC 0;
BA_ "GenSigSendType" SG_ 1322 ActvWakeup_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 ActvWakeup_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 NetMngtCoorn_SWC 0;
BA_ "GenSigSendType" SG_ 1322 NetMngtCoorn_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 NetMngtCoorn_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 NetMngtCoorrSleepRdy_SWC 0;
BA_ "GenSigSendType" SG_ 1322 NetMngtCoorrSleepRdy_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 NetMngtCoorrSleepRdy_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN10_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN10_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN10_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN11_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN11_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN11_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN12_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN12_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN12_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN13_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN13_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN13_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN14_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN14_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN14_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN15_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN15_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN15_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN16_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN16_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN16_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN17_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN17_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN17_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN18_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN18_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN18_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN19_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN19_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN19_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN1_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN1_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN1_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN20_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN20_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN20_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN21_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN21_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN21_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN22_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN22_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN22_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN23_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN23_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN23_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN24_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN24_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN24_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN25_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN25_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN25_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN26_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN26_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN26_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN27_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN27_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN27_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN28_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN28_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN28_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN29_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN29_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN29_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN2_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN2_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN2_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN30_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN30_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN30_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN31_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN31_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN31_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN32_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN32_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN32_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN33_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN33_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN33_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN34_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN34_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN34_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN35_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN35_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN35_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN36_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN36_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN36_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN37_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN37_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN37_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN38_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN38_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN38_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN39_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN39_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN39_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN3_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN3_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN3_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN40_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN40_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN40_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN41_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN41_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN41_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN42_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN42_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN42_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN43_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN43_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN43_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN44_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN44_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN44_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN45_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN45_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN45_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN46_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN46_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN46_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN47_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN47_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN47_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN48_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN48_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN48_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN4_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN4_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN4_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN5_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN5_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN5_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN6_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN6_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN6_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN7_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN7_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN7_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN8_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN8_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN8_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PN9_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PN9_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PN9_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 PtlNetInfo_SWC 0;
BA_ "GenSigSendType" SG_ 1322 PtlNetInfo_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 PtlNetInfo_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 RepMsgReq_SWC 0;
BA_ "GenSigSendType" SG_ 1322 RepMsgReq_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 RepMsgReq_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 ResdBit1_SWC 0;
BA_ "GenSigSendType" SG_ 1322 ResdBit1_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 ResdBit1_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 ResdBit2_SWC 0;
BA_ "GenSigSendType" SG_ 1322 ResdBit2_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 ResdBit2_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1322 ScrNodId_SWC 0;
BA_ "GenSigSendType" SG_ 1322 ScrNodId_SWC 7;
BA_ "GenSigStartValue" SG_ 1322 ScrNodId_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1706 DiagRespData0_SWC 0;
BA_ "GenSigSendType" SG_ 1706 DiagRespData0_SWC 7;
BA_ "GenSigStartValue" SG_ 1706 DiagRespData0_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1706 DiagRespData10_SWC 0;
BA_ "GenSigSendType" SG_ 1706 DiagRespData10_SWC 7;
BA_ "GenSigStartValue" SG_ 1706 DiagRespData10_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1706 DiagRespData11_SWC 0;
BA_ "GenSigSendType" SG_ 1706 DiagRespData11_SWC 7;
BA_ "GenSigStartValue" SG_ 1706 DiagRespData11_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1706 DiagRespData12_SWC 0;
BA_ "GenSigSendType" SG_ 1706 DiagRespData12_SWC 7;
BA_ "GenSigStartValue" SG_ 1706 DiagRespData12_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1706 DiagRespData13_SWC 0;
BA_ "GenSigSendType" SG_ 1706 DiagRespData13_SWC 7;
BA_ "GenSigStartValue" SG_ 1706 DiagRespData13_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1706 DiagRespData14_SWC 0;
BA_ "GenSigSendType" SG_ 1706 DiagRespData14_SWC 7;
BA_ "GenSigStartValue" SG_ 1706 DiagRespData14_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1706 DiagRespData15_SWC 0;
BA_ "GenSigSendType" SG_ 1706 DiagRespData15_SWC 7;
BA_ "GenSigStartValue" SG_ 1706 DiagRespData15_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1706 DiagRespData1_SWC 0;
BA_ "GenSigSendType" SG_ 1706 DiagRespData1_SWC 7;
BA_ "GenSigStartValue" SG_ 1706 DiagRespData1_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1706 DiagRespData2_SWC 0;
BA_ "GenSigSendType" SG_ 1706 DiagRespData2_SWC 7;
BA_ "GenSigStartValue" SG_ 1706 DiagRespData2_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1706 DiagRespData3_SWC 0;
BA_ "GenSigSendType" SG_ 1706 DiagRespData3_SWC 7;
BA_ "GenSigStartValue" SG_ 1706 DiagRespData3_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1706 DiagRespData4_SWC 0;
BA_ "GenSigSendType" SG_ 1706 DiagRespData4_SWC 7;
BA_ "GenSigStartValue" SG_ 1706 DiagRespData4_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1706 DiagRespData5_SWC 0;
BA_ "GenSigSendType" SG_ 1706 DiagRespData5_SWC 7;
BA_ "GenSigStartValue" SG_ 1706 DiagRespData5_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1706 DiagRespData6_SWC 0;
BA_ "GenSigSendType" SG_ 1706 DiagRespData6_SWC 7;
BA_ "GenSigStartValue" SG_ 1706 DiagRespData6_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1706 DiagRespData7_SWC 0;
BA_ "GenSigSendType" SG_ 1706 DiagRespData7_SWC 7;
BA_ "GenSigStartValue" SG_ 1706 DiagRespData7_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1706 DiagRespData8_SWC 0;
BA_ "GenSigSendType" SG_ 1706 DiagRespData8_SWC 7;
BA_ "GenSigStartValue" SG_ 1706 DiagRespData8_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1706 DiagRespData9_SWC 0;
BA_ "GenSigSendType" SG_ 1706 DiagRespData9_SWC 7;
BA_ "GenSigStartValue" SG_ 1706 DiagRespData9_SWC 0;
BA_ "GenSigSendType" SG_ 286 BCU_11E_CRC 0;
BA_ "GenSigSendType" SG_ 286 BCU_11E_MsgCntr 0;
BA_ "GenSigSendType" SG_ 286 VehSpdSts_Rdnt1 0;
BA_ "GenSigSendType" SG_ 286 VehSpd_Rdnt1 0;
BA_ "GenSigSendType" SG_ 316 DrvState 0;
BA_ "GenSigStartValue" SG_ 316 DrvState 0;
BA_ "GenSigSendType" SG_ 316 DrvState_SrvSts 0;
BA_ "GenSigStartValue" SG_ 316 DrvState_SrvSts 0;
BA_ "GenSigSendType" SG_ 316 OperatorState 0;
BA_ "GenSigStartValue" SG_ 316 OperatorState 0;
BA_ "GenSigSendType" SG_ 316 OperatorState_SrvSts 0;
BA_ "GenSigStartValue" SG_ 316 OperatorState_SrvSts 0;
BA_ "GenSigSendType" SG_ 316 VehStateDetailed 0;
BA_ "GenSigStartValue" SG_ 316 VehStateDetailed 0;
BA_ "GenSigSendType" SG_ 316 VehStateDetailed_SrvSts 0;
BA_ "GenSigStartValue" SG_ 316 VehStateDetailed_SrvSts 0;
BA_ "GenSigSendType" SG_ 316 ZONE_13C_CRC 0;
BA_ "GenSigStartValue" SG_ 316 ZONE_13C_CRC 0;
BA_ "GenSigSendType" SG_ 316 ZONE_13C_MsgCntr 0;
BA_ "GenSigStartValue" SG_ 316 ZONE_13C_MsgCntr 0;
BA_ "GenSigInactiveValue" SG_ 371 AccrPedlActPosn 0;
BA_ "GenSigSendType" SG_ 371 AccrPedlActPosn 0;
BA_ "GenSigStartValue" SG_ 371 AccrPedlActPosn 0;
BA_ "GenSigSendType" SG_ 371 BMSChrgState 0;
BA_ "GenSigStartValue" SG_ 371 BMSChrgState 0;
BA_ "GenSigInactiveValue" SG_ 371 BackLiDutyCycCmd 0;
BA_ "GenSigSendType" SG_ 371 BackLiDutyCycCmd 0;
BA_ "GenSigStartValue" SG_ 371 BackLiDutyCycCmd 0;
BA_ "GenSigInactiveValue" SG_ 371 ComfEna 0;
BA_ "GenSigSendType" SG_ 371 ComfEna 0;
BA_ "GenSigStartValue" SG_ 371 ComfEna 0;
BA_ "GenSigSendType" SG_ 371 HVBattSOCLowWarn 0;
BA_ "GenSigInactiveValue" SG_ 371 ImobEnaReq 0;
BA_ "GenSigSendType" SG_ 371 ImobEnaReq 0;
BA_ "GenSigStartValue" SG_ 371 ImobEnaReq 0;
BA_ "GenSigInactiveValue" SG_ 371 LiSnsrData 0;
BA_ "GenSigSendType" SG_ 371 LiSnsrData 0;
BA_ "GenSigStartValue" SG_ 371 LiSnsrData 0;
BA_ "GenSigInactiveValue" SG_ 371 LiSnsrFailSts 0;
BA_ "GenSigSendType" SG_ 371 LiSnsrFailSts 0;
BA_ "GenSigStartValue" SG_ 371 LiSnsrFailSts 0;
BA_ "GenSigInactiveValue" SG_ 371 PEUFMotSpd 0;
BA_ "GenSigSendType" SG_ 371 PEUFMotSpd 0;
BA_ "GenSigStartValue" SG_ 371 PEUFMotSpd 32768;
BA_ "GenSigInactiveValue" SG_ 371 PEURMotSpd 0;
BA_ "GenSigSendType" SG_ 371 PEURMotSpd 0;
BA_ "GenSigStartValue" SG_ 371 PEURMotSpd 0;
BA_ "GenSigInactiveValue" SG_ 371 VCUActGear 0;
BA_ "GenSigSendType" SG_ 371 VCUActGear 0;
BA_ "GenSigStartValue" SG_ 371 VCUActGear 0;
BA_ "GenSigInactiveValue" SG_ 371 VCUActGearValid 0;
BA_ "GenSigSendType" SG_ 371 VCUActGearValid 0;
BA_ "GenSigStartValue" SG_ 371 VCUActGearValid 0;
BA_ "GenSigInactiveValue" SG_ 371 VCUVehDispdSpd 0;
BA_ "GenSigSendType" SG_ 371 VCUVehDispdSpd 0;
BA_ "GenSigStartValue" SG_ 371 VCUVehDispdSpd 0;
BA_ "GenSigSendType" SG_ 549 HALO_AIR_QUALITY 0;
BA_ "GenSigStartValue" SG_ 549 HALO_AIR_QUALITY 0;
BA_ "GenSigSendType" SG_ 549 HALO_AMBIENT_LIGHT 0;
BA_ "GenSigStartValue" SG_ 549 HALO_AMBIENT_LIGHT 0;
BA_ "GenSigSendType" SG_ 549 HALO_CHARGING_DIRECTION 0;
BA_ "GenSigStartValue" SG_ 549 HALO_CHARGING_DIRECTION 511;
BA_ "GenSigSendType" SG_ 549 HALO_LISTENING_DIRECTION 0;
BA_ "GenSigStartValue" SG_ 549 HALO_LISTENING_DIRECTION 7;
BA_ "GenSigSendType" SG_ 549 HALO_LISTENING_VOLUME 0;
BA_ "GenSigStartValue" SG_ 549 HALO_LISTENING_VOLUME 127;
BA_ "GenSigSendType" SG_ 549 HALO_MUSIC_BPM 0;
BA_ "GenSigStartValue" SG_ 549 HALO_MUSIC_BPM 127;
BA_ "GenSigSendType" SG_ 549 HALO_MUSIC_BURST_VOLUME 0;
BA_ "GenSigStartValue" SG_ 549 HALO_MUSIC_BURST_VOLUME 127;
BA_ "GenSigSendType" SG_ 549 HALO_MUSIC_GENRE 0;
BA_ "GenSigStartValue" SG_ 549 HALO_MUSIC_GENRE 0;
BA_ "GenSigSendType" SG_ 549 HALO_OBSTACLE_DIRECTION 0;
BA_ "GenSigStartValue" SG_ 549 HALO_OBSTACLE_DIRECTION 0;
BA_ "GenSigSendType" SG_ 549 HALO_OBSTACLE_DISTANCE 0;
BA_ "GenSigStartValue" SG_ 549 HALO_OBSTACLE_DISTANCE 0;
BA_ "GenSigSendType" SG_ 549 HALO_REMIND_RISK 0;
BA_ "GenSigStartValue" SG_ 549 HALO_REMIND_RISK 0;
BA_ "GenSigSendType" SG_ 549 HALO_TALKING_DIRECTION 0;
BA_ "GenSigStartValue" SG_ 549 HALO_TALKING_DIRECTION 7;
BA_ "GenSigSendType" SG_ 549 HALO_TALKING_VOLUME 0;
BA_ "GenSigStartValue" SG_ 549 HALO_TALKING_VOLUME 127;
BA_ "GenSigSendType" SG_ 549 HALO_WORK_MODE 0;
BA_ "GenSigStartValue" SG_ 549 HALO_WORK_MODE 0;
BA_ "GenSigSendType" SG_ 549 HALO_WORK_SELECTOR 0;
BA_ "GenSigStartValue" SG_ 549 HALO_WORK_SELECTOR 0;
BA_ "GenSigInactiveValue" SG_ 549 NOMI_BackLiReq 0;
BA_ "GenSigSendType" SG_ 549 NOMI_BackLiReq 0;
BA_ "GenSigStartValue" SG_ 549 NOMI_BackLiReq 0;
BA_ "GenSigSendType" SG_ 549 NOMI_OnOffCmd 0;
BA_ "GenSigStartValue" SG_ 549 NOMI_OnOffCmd 3;
BA_ "GenSigInactiveValue" SG_ 549 NOMI_VideoSrcReq 0;
BA_ "GenSigSendType" SG_ 549 NOMI_VideoSrcReq 0;
BA_ "GenSigStartValue" SG_ 549 NOMI_VideoSrcReq 0;
BA_ "GenSigSendType" SG_ 648 CNSL_I_EnableSwReq 0;
BA_ "GenSigStartValue" SG_ 648 CNSL_I_EnableSwReq 3;
BA_ "GenSigSendType" SG_ 648 CNSL_I_OpModeReq 0;
BA_ "GenSigStartValue" SG_ 648 CNSL_I_OpModeReq 0;
BA_ "GenSigSendType" SG_ 648 CNSL_I_SensReq 0;
BA_ "GenSigStartValue" SG_ 648 CNSL_I_SensReq 0;
BA_ "GenSigSendType" SG_ 648 CNSL_I_VibDeltaReq 0;
BA_ "GenSigStartValue" SG_ 648 CNSL_I_VibDeltaReq 0;
BA_ "GenSigSendType" SG_ 648 CNSL_I_VibFbReq 0;
BA_ "GenSigStartValue" SG_ 648 CNSL_I_VibFbReq 0;
BA_ "GenSigInactiveValue" SG_ 648 CenLockUnlockSts 0;
BA_ "GenSigSendType" SG_ 648 CenLockUnlockSts 0;
BA_ "GenSigStartValue" SG_ 648 CenLockUnlockSts 0;
BA_ "GenSigInactiveValue" SG_ 648 DoorAjarFrntLeSts 0;
BA_ "GenSigSendType" SG_ 648 DoorAjarFrntLeSts 0;
BA_ "GenSigStartValue" SG_ 648 DoorAjarFrntLeSts 0;
BA_ "GenSigInactiveValue" SG_ 648 DoorAjarFrntRiSts 0;
BA_ "GenSigSendType" SG_ 648 DoorAjarFrntRiSts 0;
BA_ "GenSigStartValue" SG_ 648 DoorAjarFrntRiSts 0;
BA_ "GenSigInactiveValue" SG_ 648 DoorAjarReLeSts 0;
BA_ "GenSigSendType" SG_ 648 DoorAjarReLeSts 0;
BA_ "GenSigStartValue" SG_ 648 DoorAjarReLeSts 0;
BA_ "GenSigInactiveValue" SG_ 648 DoorAjarReRiSts 0;
BA_ "GenSigSendType" SG_ 648 DoorAjarReRiSts 0;
BA_ "GenSigStartValue" SG_ 648 DoorAjarReRiSts 0;
BA_ "GenSigInactiveValue" SG_ 648 ECOPlusModSts 0;
BA_ "GenSigSendType" SG_ 648 ECOPlusModSts 0;
BA_ "GenSigStartValue" SG_ 648 ECOPlusModSts 0;
BA_ "GenSigInactiveValue" SG_ 648 ETCPaymentCtrl 0;
BA_ "GenSigSendType" SG_ 648 ETCPaymentCtrl 0;
BA_ "GenSigStartValue" SG_ 648 ETCPaymentCtrl 0;
BA_ "GenSigInactiveValue" SG_ 648 FI_R_OnReq 0;
BA_ "GenSigSendType" SG_ 648 FI_R_OnReq 0;
BA_ "GenSigStartValue" SG_ 648 FI_R_OnReq 0;
BA_ "GenSigInactiveValue" SG_ 648 FI_R_ResetReq 0;
BA_ "GenSigSendType" SG_ 648 FI_R_ResetReq 0;
BA_ "GenSigStartValue" SG_ 648 FI_R_ResetReq 0;
BA_ "GenSigInactiveValue" SG_ 648 FI_R_SetBackLight 0;
BA_ "GenSigSendType" SG_ 648 FI_R_SetBackLight 0;
BA_ "GenSigStartValue" SG_ 648 FI_R_SetBackLight 0;
BA_ "GenSigInactiveValue" SG_ 648 FI_R_VideoSrcReq 0;
BA_ "GenSigSendType" SG_ 648 FI_R_VideoSrcReq 0;
BA_ "GenSigStartValue" SG_ 648 FI_R_VideoSrcReq 0;
BA_ "GenSigInactiveValue" SG_ 648 HUD_AgAdjmtReq 0;
BA_ "GenSigSendType" SG_ 648 HUD_AgAdjmtReq 0;
BA_ "GenSigStartValue" SG_ 648 HUD_AgAdjmtReq 10;
BA_ "GenSigInactiveValue" SG_ 648 HUD_Cmd 0;
BA_ "GenSigSendType" SG_ 648 HUD_Cmd 0;
BA_ "GenSigStartValue" SG_ 648 HUD_Cmd 0;
BA_ "GenSigInactiveValue" SG_ 648 HUD_LCDbackLiReq 0;
BA_ "GenSigSendType" SG_ 648 HUD_LCDbackLiReq 0;
BA_ "GenSigStartValue" SG_ 648 HUD_LCDbackLiReq 0;
BA_ "GenSigInactiveValue" SG_ 648 HUD_ResetReq 0;
BA_ "GenSigSendType" SG_ 648 HUD_ResetReq 0;
BA_ "GenSigStartValue" SG_ 648 HUD_ResetReq 0;
BA_ "GenSigInactiveValue" SG_ 648 HoodAjarSts 0;
BA_ "GenSigSendType" SG_ 648 HoodAjarSts 0;
BA_ "GenSigStartValue" SG_ 648 HoodAjarSts 0;
BA_ "GenSigInactiveValue" SG_ 648 ICS_BrightnessReq 0;
BA_ "GenSigSendType" SG_ 648 ICS_BrightnessReq 0;
BA_ "GenSigStartValue" SG_ 648 ICS_BrightnessReq 0;
BA_ "GenSigInactiveValue" SG_ 648 ICS_OnReq 0;
BA_ "GenSigSendType" SG_ 648 ICS_OnReq 0;
BA_ "GenSigStartValue" SG_ 648 ICS_OnReq 0;
BA_ "GenSigSendType" SG_ 648 ICS_ResetReq 0;
BA_ "GenSigStartValue" SG_ 648 ICS_ResetReq 0;
BA_ "GenSigInactiveValue" SG_ 648 ICS_VideoSrcReq 0;
BA_ "GenSigSendType" SG_ 648 ICS_VideoSrcReq 0;
BA_ "GenSigStartValue" SG_ 648 ICS_VideoSrcReq 0;
BA_ "GenSigSendType" SG_ 648 IC_LCDbackLiReq_L 0;
BA_ "GenSigStartValue" SG_ 648 IC_LCDbackLiReq_L 0;
BA_ "GenSigSendType" SG_ 648 IC_LCDbackLiReq_R 0;
BA_ "GenSigStartValue" SG_ 648 IC_LCDbackLiReq_R 0;
BA_ "GenSigInactiveValue" SG_ 648 IC_ResetReq_L 0;
BA_ "GenSigSendType" SG_ 648 IC_ResetReq_L 0;
BA_ "GenSigStartValue" SG_ 648 IC_ResetReq_L 0;
BA_ "GenSigInactiveValue" SG_ 648 IC_ResetReq_R 0;
BA_ "GenSigSendType" SG_ 648 IC_ResetReq_R 0;
BA_ "GenSigStartValue" SG_ 648 IC_ResetReq_R 0;
BA_ "GenSigSendType" SG_ 648 IC_VideoSrcReq_L 0;
BA_ "GenSigStartValue" SG_ 648 IC_VideoSrcReq_L 0;
BA_ "GenSigSendType" SG_ 648 IC_VideoSrcReq_R 0;
BA_ "GenSigStartValue" SG_ 648 IC_VideoSrcReq_R 0;
BA_ "GenSigInactiveValue" SG_ 648 LVBattUSts_LFP 0;
BA_ "GenSigSendType" SG_ 648 LVBattUSts_LFP 0;
BA_ "GenSigStartValue" SG_ 648 LVBattUSts_LFP 0;
BA_ "GenSigInactiveValue" SG_ 648 LVBattU_LFP 0;
BA_ "GenSigSendType" SG_ 648 LVBattU_LFP 0;
BA_ "GenSigStartValue" SG_ 648 LVBattU_LFP 0;
BA_ "GenSigInactiveValue" SG_ 648 PositionReq 0;
BA_ "GenSigSendType" SG_ 648 PositionReq 0;
BA_ "GenSigStartValue" SG_ 648 PositionReq 0;
BA_ "GenSigInactiveValue" SG_ 648 TrAjarSts 0;
BA_ "GenSigSendType" SG_ 648 TrAjarSts 0;
BA_ "GenSigStartValue" SG_ 648 TrAjarSts 0;
BA_ "GenSigInactiveValue" SG_ 648 VCUPwrDisp 0;
BA_ "GenSigSendType" SG_ 648 VCUPwrDisp 0;
BA_ "GenSigStartValue" SG_ 648 VCUPwrDisp 300;
BA_ "GenSigSendType" SG_ 747 CDF_SetBrake 0;
BA_ "GenSigStartValue" SG_ 747 CDF_SetBrake 0;
BA_ "GenSigSendType" SG_ 747 CDF_SetGear 0;
BA_ "GenSigStartValue" SG_ 747 CDF_SetGear 0;
BA_ "GenSigSendType" SG_ 747 CDF_SetPedal 0;
BA_ "GenSigStartValue" SG_ 747 CDF_SetPedal 0;
BA_ "GenSigSendType" SG_ 747 CDF_SetRotSpeed 0;
BA_ "GenSigStartValue" SG_ 747 CDF_SetRotSpeed 0;
BA_ "GenSigSendType" SG_ 747 CDF_SetTorqueFront 0;
BA_ "GenSigStartValue" SG_ 747 CDF_SetTorqueFront 0;
BA_ "GenSigSendType" SG_ 747 CDF_SetTorqueRear 0;
BA_ "GenSigStartValue" SG_ 747 CDF_SetTorqueRear 0;
BA_ "GenSigSendType" SG_ 747 CDF_SetVehSpeed 0;
BA_ "GenSigStartValue" SG_ 747 CDF_SetVehSpeed 0;
BA_ "GenSigSendType" SG_ 747 CompressorSpd 0;
BA_ "GenSigStartValue" SG_ 747 CompressorSpd 0;
BA_ "GenSigSendType" SG_ 747 DriveModType 0;
BA_ "GenSigStartValue" SG_ 747 DriveModType 0;
BA_ "GenSigSendType" SG_ 747 Envtemp 0;
BA_ "GenSigStartValue" SG_ 747 Envtemp 0;
BA_ "GenSigSendType" SG_ 747 Hvac2ndAutoSts 0;
BA_ "GenSigStartValue" SG_ 747 Hvac2ndAutoSts 0;
BA_ "GenSigSendType" SG_ 747 Hvac2ndBlwSpd 0;
BA_ "GenSigStartValue" SG_ 747 Hvac2ndBlwSpd 0;
BA_ "GenSigSendType" SG_ 747 Hvac2ndBlwSpdAutoSts 0;
BA_ "GenSigStartValue" SG_ 747 Hvac2ndBlwSpdAutoSts 0;
BA_ "GenSigSendType" SG_ 747 Hvac2ndFanDirAutoSts 0;
BA_ "GenSigStartValue" SG_ 747 Hvac2ndFanDirAutoSts 0;
BA_ "GenSigSendType" SG_ 747 Hvac2ndFanDirFaceSts 0;
BA_ "GenSigStartValue" SG_ 747 Hvac2ndFanDirFaceSts 0;
BA_ "GenSigSendType" SG_ 747 Hvac2ndFanDirFloorSts 0;
BA_ "GenSigStartValue" SG_ 747 Hvac2ndFanDirFloorSts 0;
BA_ "GenSigSendType" SG_ 747 Hvac2ndOnOffSts 0;
BA_ "GenSigStartValue" SG_ 747 Hvac2ndOnOffSts 0;
BA_ "GenSigSendType" SG_ 747 Hvac3rdAutoSts 0;
BA_ "GenSigStartValue" SG_ 747 Hvac3rdAutoSts 0;
BA_ "GenSigSendType" SG_ 747 Hvac3rdBlwSpd 0;
BA_ "GenSigStartValue" SG_ 747 Hvac3rdBlwSpd 0;
BA_ "GenSigSendType" SG_ 747 Hvac3rdBlwSpdAutoSts 0;
BA_ "GenSigStartValue" SG_ 747 Hvac3rdBlwSpdAutoSts 0;
BA_ "GenSigSendType" SG_ 747 Hvac3rdFanDirAutoSts 0;
BA_ "GenSigStartValue" SG_ 747 Hvac3rdFanDirAutoSts 0;
BA_ "GenSigSendType" SG_ 747 Hvac3rdFanDirFaceSts 0;
BA_ "GenSigStartValue" SG_ 747 Hvac3rdFanDirFaceSts 0;
BA_ "GenSigSendType" SG_ 747 Hvac3rdFanDirFloorSts 0;
BA_ "GenSigStartValue" SG_ 747 Hvac3rdFanDirFloorSts 0;
BA_ "GenSigSendType" SG_ 747 Hvac3rdOnOffSts 0;
BA_ "GenSigStartValue" SG_ 747 Hvac3rdOnOffSts 0;
BA_ "GenSigSendType" SG_ 747 HvacAcSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacAcSts 0;
BA_ "GenSigSendType" SG_ 747 HvacAutoSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacAutoSts 0;
BA_ "GenSigSendType" SG_ 747 HvacBlwSpd 0;
BA_ "GenSigStartValue" SG_ 747 HvacBlwSpd 0;
BA_ "GenSigSendType" SG_ 747 HvacBlwSpdAutoSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacBlwSpdAutoSts 0;
BA_ "GenSigSendType" SG_ 747 HvacFanDirAutoSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacFanDirAutoSts 0;
BA_ "GenSigSendType" SG_ 747 HvacFanDirFaceSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacFanDirFaceSts 0;
BA_ "GenSigSendType" SG_ 747 HvacFanDirFloorSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacFanDirFloorSts 0;
BA_ "GenSigSendType" SG_ 747 HvacFrntDefogSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacFrntDefogSts 0;
BA_ "GenSigSendType" SG_ 747 HvacFrntRiAutoSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacFrntRiAutoSts 0;
BA_ "GenSigSendType" SG_ 747 HvacFrntRiBlwSpd 0;
BA_ "GenSigStartValue" SG_ 747 HvacFrntRiBlwSpd 0;
BA_ "GenSigSendType" SG_ 747 HvacFrntRiBlwSpdAutoSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacFrntRiBlwSpdAutoSts 0;
BA_ "GenSigSendType" SG_ 747 HvacFrntRiFanDirAutoSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacFrntRiFanDirAutoSts 0;
BA_ "GenSigSendType" SG_ 747 HvacFrntRiFanDirFaceSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacFrntRiFanDirFaceSts 0;
BA_ "GenSigSendType" SG_ 747 HvacFrntRiFanDirFloorSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacFrntRiFanDirFloorSts 0;
BA_ "GenSigSendType" SG_ 747 HvacFrntRiOnOffSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacFrntRiOnOffSts 0;
BA_ "GenSigSendType" SG_ 747 HvacMaxAcSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacMaxAcSts 0;
BA_ "GenSigSendType" SG_ 747 HvacMaxDefogSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacMaxDefogSts 0;
BA_ "GenSigSendType" SG_ 747 HvacMaxHtSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacMaxHtSts 0;
BA_ "GenSigSendType" SG_ 747 HvacOnOffSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacOnOffSts 0;
BA_ "GenSigSendType" SG_ 747 HvacRearDefogSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacRearDefogSts 0;
BA_ "GenSigSendType" SG_ 747 HvacRecSts 0;
BA_ "GenSigStartValue" SG_ 747 HvacRecSts 0;
BA_ "GenSigSendType" SG_ 747 IntrTemp 0;
BA_ "GenSigStartValue" SG_ 747 IntrTemp 0;
BA_ "GenSigSendType" SG_ 747 LidarSpd 0;
BA_ "GenSigStartValue" SG_ 747 LidarSpd 0;
BA_ "GenSigSendType" SG_ 747 Seat2ndLeFAPosn 0;
BA_ "GenSigStartValue" SG_ 747 Seat2ndLeFAPosn 0;
BA_ "GenSigSendType" SG_ 747 Seat2ndLeHdrPosn 0;
BA_ "GenSigStartValue" SG_ 747 Seat2ndLeHdrPosn 0;
BA_ "GenSigSendType" SG_ 747 Seat2ndLeRecPosn 0;
BA_ "GenSigStartValue" SG_ 747 Seat2ndLeRecPosn 0;
BA_ "GenSigSendType" SG_ 747 Seat2ndLeUPPosn 0;
BA_ "GenSigStartValue" SG_ 747 Seat2ndLeUPPosn 0;
BA_ "GenSigSendType" SG_ 747 Seat2ndRiFAPosn 0;
BA_ "GenSigStartValue" SG_ 747 Seat2ndRiFAPosn 0;
BA_ "GenSigSendType" SG_ 747 Seat2ndRiHdrPosn 0;
BA_ "GenSigStartValue" SG_ 747 Seat2ndRiHdrPosn 0;
BA_ "GenSigSendType" SG_ 747 Seat2ndRiRecPosn 0;
BA_ "GenSigStartValue" SG_ 747 Seat2ndRiRecPosn 0;
BA_ "GenSigSendType" SG_ 747 Seat2ndRiUPPosn 0;
BA_ "GenSigStartValue" SG_ 747 Seat2ndRiUPPosn 0;
BA_ "GenSigSendType" SG_ 747 Seat3rdLeFAPosn 0;
BA_ "GenSigStartValue" SG_ 747 Seat3rdLeFAPosn 0;
BA_ "GenSigSendType" SG_ 747 Seat3rdLeHdrPosn 0;
BA_ "GenSigStartValue" SG_ 747 Seat3rdLeHdrPosn 0;
BA_ "GenSigSendType" SG_ 747 Seat3rdLeRecPosn 0;
BA_ "GenSigStartValue" SG_ 747 Seat3rdLeRecPosn 0;
BA_ "GenSigSendType" SG_ 747 Seat3rdLeUPPosn 0;
BA_ "GenSigStartValue" SG_ 747 Seat3rdLeUPPosn 0;
BA_ "GenSigSendType" SG_ 747 Seat3rdRiFAPosn 0;
BA_ "GenSigStartValue" SG_ 747 Seat3rdRiFAPosn 0;
BA_ "GenSigSendType" SG_ 747 Seat3rdRiHdrPosn 0;
BA_ "GenSigStartValue" SG_ 747 Seat3rdRiHdrPosn 0;
BA_ "GenSigSendType" SG_ 747 Seat3rdRiRecPosn 0;
BA_ "GenSigStartValue" SG_ 747 Seat3rdRiRecPosn 0;
BA_ "GenSigSendType" SG_ 747 Seat3rdRiUPPosn 0;
BA_ "GenSigStartValue" SG_ 747 Seat3rdRiUPPosn 0;
BA_ "GenSigSendType" SG_ 747 SeatFrntLeFAPosn 0;
BA_ "GenSigStartValue" SG_ 747 SeatFrntLeFAPosn 0;
BA_ "GenSigSendType" SG_ 747 SeatFrntLeHdrPosn 0;
BA_ "GenSigStartValue" SG_ 747 SeatFrntLeHdrPosn 0;
BA_ "GenSigSendType" SG_ 747 SeatFrntLeRecPosn 0;
BA_ "GenSigStartValue" SG_ 747 SeatFrntLeRecPosn 0;
BA_ "GenSigSendType" SG_ 747 SeatFrntLeUPPosn 0;
BA_ "GenSigStartValue" SG_ 747 SeatFrntLeUPPosn 0;
BA_ "GenSigSendType" SG_ 747 SeatFrntRiFAPosn 0;
BA_ "GenSigStartValue" SG_ 747 SeatFrntRiFAPosn 0;
BA_ "GenSigSendType" SG_ 747 SeatFrntRiHdrPosn 0;
BA_ "GenSigStartValue" SG_ 747 SeatFrntRiHdrPosn 0;
BA_ "GenSigSendType" SG_ 747 SeatFrntRiRecPosn 0;
BA_ "GenSigStartValue" SG_ 747 SeatFrntRiRecPosn 0;
BA_ "GenSigSendType" SG_ 747 SeatFrntRiUPPosn 0;
BA_ "GenSigStartValue" SG_ 747 SeatFrntRiUPPosn 0;
BA_ "GenSigInactiveValue" SG_ 747 SeatOccpFrntLeSts 0;
BA_ "GenSigSendType" SG_ 747 SeatOccpFrntLeSts 0;
BA_ "GenSigStartValue" SG_ 747 SeatOccpFrntLeSts 0;
BA_ "GenSigInactiveValue" SG_ 747 SeatOccpFrntRiFail 0;
BA_ "GenSigSendType" SG_ 747 SeatOccpFrntRiFail 0;
BA_ "GenSigStartValue" SG_ 747 SeatOccpFrntRiFail 0;
BA_ "GenSigInactiveValue" SG_ 747 SeatOccpt2ndLeSts 0;
BA_ "GenSigSendType" SG_ 747 SeatOccpt2ndLeSts 0;
BA_ "GenSigStartValue" SG_ 747 SeatOccpt2ndLeSts 0;
BA_ "GenSigInactiveValue" SG_ 747 SeatOccpt2ndMidSts 0;
BA_ "GenSigSendType" SG_ 747 SeatOccpt2ndMidSts 0;
BA_ "GenSigStartValue" SG_ 747 SeatOccpt2ndMidSts 0;
BA_ "GenSigInactiveValue" SG_ 747 SeatOccpt2ndRiSts 0;
BA_ "GenSigSendType" SG_ 747 SeatOccpt2ndRiSts 0;
BA_ "GenSigStartValue" SG_ 747 SeatOccpt2ndRiSts 0;
BA_ "GenSigInactiveValue" SG_ 747 SeatOccpt3rdLeSts 0;
BA_ "GenSigSendType" SG_ 747 SeatOccpt3rdLeSts 0;
BA_ "GenSigStartValue" SG_ 747 SeatOccpt3rdLeSts 0;
BA_ "GenSigInactiveValue" SG_ 747 SeatOccpt3rdRiSts 0;
BA_ "GenSigSendType" SG_ 747 SeatOccpt3rdRiSts 0;
BA_ "GenSigStartValue" SG_ 747 SeatOccpt3rdRiSts 0;
BA_ "GenSigSendType" SG_ 747 SunroofPosn 0;
BA_ "GenSigStartValue" SG_ 747 SunroofPosn 0;
BA_ "GenSigSendType" SG_ 747 TailgateSts 0;
BA_ "GenSigStartValue" SG_ 747 TailgateSts 0;
BA_ "GenSigSendType" SG_ 747 TpmsFrntLeWhlPress 0;
BA_ "GenSigStartValue" SG_ 747 TpmsFrntLeWhlPress 255;
BA_ "GenSigSendType" SG_ 747 TpmsFrntRiWhlPress 0;
BA_ "GenSigStartValue" SG_ 747 TpmsFrntRiWhlPress 255;
BA_ "GenSigSendType" SG_ 747 TpmsReLeWhlPress 0;
BA_ "GenSigStartValue" SG_ 747 TpmsReLeWhlPress 255;
BA_ "GenSigSendType" SG_ 747 TpmsReRiWhlPress 0;
BA_ "GenSigStartValue" SG_ 747 TpmsReRiWhlPress 255;
BA_ "GenSigInactiveValue" SG_ 747 VC_SeatNumber 0;
BA_ "GenSigSendType" SG_ 747 VC_SeatNumber 0;
BA_ "GenSigStartValue" SG_ 747 VC_SeatNumber 0;
BA_ "GenSigSendType" SG_ 747 VC_SuspensionType 0;
BA_ "GenSigStartValue" SG_ 747 VC_SuspensionType 0;
BA_ "GenSigSendType" SG_ 747 VehicleOnVoltage 0;
BA_ "GenSigStartValue" SG_ 747 VehicleOnVoltage 0;
BA_ "GenSigInactiveValue" SG_ 747 WinFrntLePosn 0;
BA_ "GenSigSendType" SG_ 747 WinFrntLePosn 0;
BA_ "GenSigStartValue" SG_ 747 WinFrntLePosn 0;
BA_ "GenSigInactiveValue" SG_ 747 WinFrntRiPosn 0;
BA_ "GenSigSendType" SG_ 747 WinFrntRiPosn 0;
BA_ "GenSigStartValue" SG_ 747 WinFrntRiPosn 0;
BA_ "GenSigInactiveValue" SG_ 747 WinReLePosn 0;
BA_ "GenSigSendType" SG_ 747 WinReLePosn 0;
BA_ "GenSigStartValue" SG_ 747 WinReLePosn 0;
BA_ "GenSigInactiveValue" SG_ 747 WinReRiPosn 0;
BA_ "GenSigSendType" SG_ 747 WinReRiPosn 0;
BA_ "GenSigStartValue" SG_ 747 WinReRiPosn 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetEPMode 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetEPMode 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetEPSwitch 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetEPSwitch 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetKROKMicType 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetKROKMicType 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetKROKSwitch 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetKROKSwitch 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetKROKVol 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetKROKVol 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetMediaFrontMute 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetMediaFrontMute 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetMediaPath 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetMediaPath 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetMediaRearMute 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetMediaRearMute 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetMediaSwitch 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetMediaSwitch 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetMediaType 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetMediaType 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetMediaVol 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetMediaVol 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetMute 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetMute 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetNomiSwitch 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetNomiSwitch 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetNomiType 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetNomiType 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetNomiVol 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetNomiVol 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQFrqPoint_1 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQFrqPoint_1 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQFrqPoint_10 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQFrqPoint_10 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQFrqPoint_2 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQFrqPoint_2 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQFrqPoint_3 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQFrqPoint_3 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQFrqPoint_4 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQFrqPoint_4 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQFrqPoint_5 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQFrqPoint_5 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQFrqPoint_6 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQFrqPoint_6 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQFrqPoint_7 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQFrqPoint_7 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQFrqPoint_8 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQFrqPoint_8 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQFrqPoint_9 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQFrqPoint_9 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQGain_1 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQGain_1 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQGain_10 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQGain_10 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQGain_2 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQGain_2 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQGain_3 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQGain_3 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQGain_4 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQGain_4 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQGain_5 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQGain_5 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQGain_6 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQGain_6 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQGain_7 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQGain_7 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQGain_8 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQGain_8 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQGain_9 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQGain_9 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQSwitch 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQSwitch 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQValue_1 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQValue_1 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQValue_10 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQValue_10 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQValue_2 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQValue_2 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQValue_3 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQValue_3 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQValue_4 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQValue_4 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQValue_5 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQValue_5 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQValue_6 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQValue_6 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQValue_7 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQValue_7 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQValue_8 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQValue_8 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPEQValue_9 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPEQValue_9 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPhonePos 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPhonePos 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetPhoneSwitch 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetPhoneSwitch 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetRNCSwitch 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetRNCSwitch 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetSoundFiled 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetSoundFiled 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetSoundFiledSwitch 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetSoundFiledSwitch 0;
BA_ "GenSigSendType" SG_ 781 CDF_SetWorkMode 0;
BA_ "GenSigStartValue" SG_ 781 CDF_SetWorkMode 0;
BA_ "GenSigSendType" SG_ 937 VehOdometer 0;
BA_ "GenSigStartValue" SG_ 937 VehOdometer 0;
BA_ "GenSigSendType" SG_ 946 Day 0;
BA_ "GenSigStartValue" SG_ 946 Day 0;
BA_ "GenSigSendType" SG_ 946 Hr 0;
BA_ "GenSigStartValue" SG_ 946 Hr 0;
BA_ "GenSigSendType" SG_ 946 MSec 0;
BA_ "GenSigStartValue" SG_ 946 MSec 0;
BA_ "GenSigSendType" SG_ 946 Min 0;
BA_ "GenSigStartValue" SG_ 946 Min 0;
BA_ "GenSigSendType" SG_ 946 Mth 0;
BA_ "GenSigStartValue" SG_ 946 Mth 0;
BA_ "GenSigSendType" SG_ 946 Sec 0;
BA_ "GenSigStartValue" SG_ 946 Sec 0;
BA_ "GenSigSendType" SG_ 946 Yr 0;
BA_ "GenSigStartValue" SG_ 946 Yr 0;
BA_ "GenSigSendType" SG_ 1159 HALO_P_01_B 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_01_B 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_01_G 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_01_G 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_01_R 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_01_R 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_01_X 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_01_X 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_01_Y 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_01_Y 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_02_B 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_02_B 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_02_G 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_02_G 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_02_R 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_02_R 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_02_X 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_02_X 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_02_Y 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_02_Y 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_03_B 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_03_B 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_03_G 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_03_G 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_03_R 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_03_R 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_03_X 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_03_X 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_03_Y 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_03_Y 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_04_B 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_04_B 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_04_G 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_04_G 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_04_R 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_04_R 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_04_X 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_04_X 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_04_Y 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_04_Y 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_05_B 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_05_B 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_05_G 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_05_G 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_05_R 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_05_R 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_05_X 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_05_X 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_05_Y 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_05_Y 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_06_B 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_06_B 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_06_G 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_06_G 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_06_R 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_06_R 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_06_X 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_06_X 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_06_Y 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_06_Y 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_07_B 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_07_B 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_07_G 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_07_G 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_07_R 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_07_R 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_07_X 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_07_X 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_07_Y 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_07_Y 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_08_B 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_08_B 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_08_G 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_08_G 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_08_R 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_08_R 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_08_X 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_08_X 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_08_Y 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_08_Y 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_09_B 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_09_B 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_09_G 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_09_G 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_09_R 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_09_R 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_09_X 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_09_X 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_09_Y 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_09_Y 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_10_B 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_10_B 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_10_G 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_10_G 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_10_R 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_10_R 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_10_X 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_10_X 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_10_Y 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_10_Y 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_11_B 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_11_B 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_11_G 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_11_G 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_11_R 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_11_R 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_11_X 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_11_X 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_11_Y 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_11_Y 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_12_B 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_12_B 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_12_G 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_12_G 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_12_R 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_12_R 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_12_X 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_12_X 255;
BA_ "GenSigSendType" SG_ 1159 HALO_P_12_Y 7;
BA_ "GenSigStartValue" SG_ 1159 HALO_P_12_Y 255;
BA_ "GenSigSendType" SG_ 1161 HALO_01_01_B 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_01_01_B 255;
BA_ "GenSigSendType" SG_ 1161 HALO_01_01_G 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_01_01_G 255;
BA_ "GenSigSendType" SG_ 1161 HALO_01_01_R 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_01_01_R 255;
BA_ "GenSigSendType" SG_ 1161 HALO_01_02_B 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_01_02_B 255;
BA_ "GenSigSendType" SG_ 1161 HALO_01_02_G 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_01_02_G 255;
BA_ "GenSigSendType" SG_ 1161 HALO_01_02_R 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_01_02_R 255;
BA_ "GenSigSendType" SG_ 1161 HALO_01_03_B 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_01_03_B 255;
BA_ "GenSigSendType" SG_ 1161 HALO_01_03_G 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_01_03_G 255;
BA_ "GenSigSendType" SG_ 1161 HALO_01_03_R 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_01_03_R 255;
BA_ "GenSigSendType" SG_ 1161 HALO_01_04_B 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_01_04_B 255;
BA_ "GenSigSendType" SG_ 1161 HALO_01_04_G 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_01_04_G 255;
BA_ "GenSigSendType" SG_ 1161 HALO_01_04_R 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_01_04_R 255;
BA_ "GenSigSendType" SG_ 1161 HALO_01_05_B 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_01_05_B 255;
BA_ "GenSigSendType" SG_ 1161 HALO_01_05_G 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_01_05_G 255;
BA_ "GenSigSendType" SG_ 1161 HALO_01_05_R 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_01_05_R 255;
BA_ "GenSigSendType" SG_ 1161 HALO_01_06_B 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_01_06_B 255;
BA_ "GenSigSendType" SG_ 1161 HALO_01_06_G 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_01_06_G 255;
BA_ "GenSigSendType" SG_ 1161 HALO_01_06_R 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_01_06_R 255;
BA_ "GenSigSendType" SG_ 1161 HALO_02_01_B 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_02_01_B 255;
BA_ "GenSigSendType" SG_ 1161 HALO_02_01_G 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_02_01_G 255;
BA_ "GenSigSendType" SG_ 1161 HALO_02_01_R 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_02_01_R 255;
BA_ "GenSigSendType" SG_ 1161 HALO_02_02_B 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_02_02_B 255;
BA_ "GenSigSendType" SG_ 1161 HALO_02_02_G 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_02_02_G 255;
BA_ "GenSigSendType" SG_ 1161 HALO_02_02_R 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_02_02_R 255;
BA_ "GenSigSendType" SG_ 1161 HALO_02_03_B 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_02_03_B 255;
BA_ "GenSigSendType" SG_ 1161 HALO_02_03_G 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_02_03_G 255;
BA_ "GenSigSendType" SG_ 1161 HALO_02_03_R 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_02_03_R 255;
BA_ "GenSigSendType" SG_ 1161 HALO_02_04_B 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_02_04_B 255;
BA_ "GenSigSendType" SG_ 1161 HALO_02_04_G 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_02_04_G 255;
BA_ "GenSigSendType" SG_ 1161 HALO_02_04_R 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_02_04_R 255;
BA_ "GenSigSendType" SG_ 1161 HALO_02_05_B 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_02_05_B 255;
BA_ "GenSigSendType" SG_ 1161 HALO_02_05_G 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_02_05_G 255;
BA_ "GenSigSendType" SG_ 1161 HALO_02_05_R 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_02_05_R 255;
BA_ "GenSigSendType" SG_ 1161 HALO_02_06_B 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_02_06_B 255;
BA_ "GenSigSendType" SG_ 1161 HALO_02_06_G 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_02_06_G 255;
BA_ "GenSigSendType" SG_ 1161 HALO_02_06_R 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_02_06_R 255;
BA_ "GenSigSendType" SG_ 1161 HALO_03_01_B 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_03_01_B 255;
BA_ "GenSigSendType" SG_ 1161 HALO_03_01_G 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_03_01_G 255;
BA_ "GenSigSendType" SG_ 1161 HALO_03_01_R 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_03_01_R 255;
BA_ "GenSigSendType" SG_ 1161 HALO_03_02_B 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_03_02_B 255;
BA_ "GenSigSendType" SG_ 1161 HALO_03_02_G 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_03_02_G 255;
BA_ "GenSigSendType" SG_ 1161 HALO_03_02_R 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_03_02_R 255;
BA_ "GenSigSendType" SG_ 1161 HALO_03_03_B 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_03_03_B 255;
BA_ "GenSigSendType" SG_ 1161 HALO_03_03_G 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_03_03_G 255;
BA_ "GenSigSendType" SG_ 1161 HALO_03_03_R 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_03_03_R 255;
BA_ "GenSigSendType" SG_ 1161 HALO_03_04_B 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_03_04_B 255;
BA_ "GenSigSendType" SG_ 1161 HALO_03_04_G 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_03_04_G 255;
BA_ "GenSigSendType" SG_ 1161 HALO_03_04_R 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_03_04_R 255;
BA_ "GenSigSendType" SG_ 1161 HALO_03_05_B 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_03_05_B 255;
BA_ "GenSigSendType" SG_ 1161 HALO_03_05_G 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_03_05_G 255;
BA_ "GenSigSendType" SG_ 1161 HALO_03_05_R 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_03_05_R 255;
BA_ "GenSigSendType" SG_ 1161 HALO_03_06_B 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_03_06_B 255;
BA_ "GenSigSendType" SG_ 1161 HALO_03_06_G 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_03_06_G 255;
BA_ "GenSigSendType" SG_ 1161 HALO_03_06_R 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_03_06_R 255;
BA_ "GenSigSendType" SG_ 1161 HALO_COL_01 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_COL_01 255;
BA_ "GenSigSendType" SG_ 1161 HALO_OTA_Count 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_OTA_Count 65535;
BA_ "GenSigSendType" SG_ 1161 HALO_OTA_ID 7;
BA_ "GenSigStartValue" SG_ 1161 HALO_OTA_ID 65535;
BA_ "GenSigInactiveValue" SG_ 1162 CDCCmdAction 0;
BA_ "GenSigSendType" SG_ 1162 CDCCmdAction 7;
BA_ "GenSigStartValue" SG_ 1162 CDCCmdAction 0;
BA_ "GenSigInactiveValue" SG_ 1162 CDCCmdP_Dur 0;
BA_ "GenSigSendType" SG_ 1162 CDCCmdP_Dur 7;
BA_ "GenSigStartValue" SG_ 1162 CDCCmdP_Dur 0;
BA_ "GenSigInactiveValue" SG_ 1162 CDCCmdP_Ind_I 0;
BA_ "GenSigSendType" SG_ 1162 CDCCmdP_Ind_I 7;
BA_ "GenSigStartValue" SG_ 1162 CDCCmdP_Ind_I 0;
BA_ "GenSigInactiveValue" SG_ 1162 CDCCmdP_Ind_O 0;
BA_ "GenSigSendType" SG_ 1162 CDCCmdP_Ind_O 7;
BA_ "GenSigStartValue" SG_ 1162 CDCCmdP_Ind_O 0;
BA_ "GenSigInactiveValue" SG_ 1162 CDCCmdPitch 0;
BA_ "GenSigSendType" SG_ 1162 CDCCmdPitch 7;
BA_ "GenSigStartValue" SG_ 1162 CDCCmdPitch 0;
BA_ "GenSigInactiveValue" SG_ 1162 CDCCmdY_Dur 0;
BA_ "GenSigSendType" SG_ 1162 CDCCmdY_Dur 7;
BA_ "GenSigStartValue" SG_ 1162 CDCCmdY_Dur 0;
BA_ "GenSigInactiveValue" SG_ 1162 CDCCmdY_Ind_I 0;
BA_ "GenSigSendType" SG_ 1162 CDCCmdY_Ind_I 7;
BA_ "GenSigStartValue" SG_ 1162 CDCCmdY_Ind_I 0;
BA_ "GenSigInactiveValue" SG_ 1162 CDCCmdY_Ind_O 0;
BA_ "GenSigSendType" SG_ 1162 CDCCmdY_Ind_O 7;
BA_ "GenSigStartValue" SG_ 1162 CDCCmdY_Ind_O 0;
BA_ "GenSigInactiveValue" SG_ 1162 CDCCmdYaw 0;
BA_ "GenSigSendType" SG_ 1162 CDCCmdYaw 7;
BA_ "GenSigStartValue" SG_ 1162 CDCCmdYaw 177;
BA_ "GenSigSendType" SG_ 1162 CDCFramePCount 7;
BA_ "GenSigStartValue" SG_ 1162 CDCFramePCount 0;
BA_ "GenSigSendType" SG_ 1162 CDCFrameYCount 7;
BA_ "GenSigStartValue" SG_ 1162 CDCFrameYCount 0;
BA_ "GenSigInactiveValue" SG_ 1201 AMP_Rx_Sig01 0;
BA_ "GenSigSendType" SG_ 1201 AMP_Rx_Sig01 7;
BA_ "GenSigStartValue" SG_ 1201 AMP_Rx_Sig01 0;
BA_ "GenSigInactiveValue" SG_ 1201 AMP_Rx_Sig02 0;
BA_ "GenSigSendType" SG_ 1201 AMP_Rx_Sig02 7;
BA_ "GenSigStartValue" SG_ 1201 AMP_Rx_Sig02 0;
BA_ "GenSigInactiveValue" SG_ 1201 AMP_Rx_Sig03 0;
BA_ "GenSigSendType" SG_ 1201 AMP_Rx_Sig03 7;
BA_ "GenSigStartValue" SG_ 1201 AMP_Rx_Sig03 0;
BA_ "GenSigInactiveValue" SG_ 1201 AMP_Rx_Sig04 0;
BA_ "GenSigSendType" SG_ 1201 AMP_Rx_Sig04 7;
BA_ "GenSigStartValue" SG_ 1201 AMP_Rx_Sig04 0;
BA_ "GenSigInactiveValue" SG_ 1201 AMP_Rx_Sig05 0;
BA_ "GenSigSendType" SG_ 1201 AMP_Rx_Sig05 7;
BA_ "GenSigStartValue" SG_ 1201 AMP_Rx_Sig05 0;
BA_ "GenSigInactiveValue" SG_ 1201 AMP_Rx_Sig06 0;
BA_ "GenSigSendType" SG_ 1201 AMP_Rx_Sig06 7;
BA_ "GenSigStartValue" SG_ 1201 AMP_Rx_Sig06 0;
BA_ "GenSigInactiveValue" SG_ 1201 AMP_Rx_Sig07 0;
BA_ "GenSigSendType" SG_ 1201 AMP_Rx_Sig07 7;
BA_ "GenSigStartValue" SG_ 1201 AMP_Rx_Sig07 0;
BA_ "GenSigInactiveValue" SG_ 1201 AMP_Rx_Sig08 0;
BA_ "GenSigSendType" SG_ 1201 AMP_Rx_Sig08 7;
BA_ "GenSigStartValue" SG_ 1201 AMP_Rx_Sig08 0;
BA_ "GenSigInactiveValue" SG_ 1285 ActvWakeup_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 ActvWakeup_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 ActvWakeup_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 NetMngtCoorn_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 NetMngtCoorn_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 NetMngtCoorn_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 NetMngtCoorrSleepRdy_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 NetMngtCoorrSleepRdy_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 NetMngtCoorrSleepRdy_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN10_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN10_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN10_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN11_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN11_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN11_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN12_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN12_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN12_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN13_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN13_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN13_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN14_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN14_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN14_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN15_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN15_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN15_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN16_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN16_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN16_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN17_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN17_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN17_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN18_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN18_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN18_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN19_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN19_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN19_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN1_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN1_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN1_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN20_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN20_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN20_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN21_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN21_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN21_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN22_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN22_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN22_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN23_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN23_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN23_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN24_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN24_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN24_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN25_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN25_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN25_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN26_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN26_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN26_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN27_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN27_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN27_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN28_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN28_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN28_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN29_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN29_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN29_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN2_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN2_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN2_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN30_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN30_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN30_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN31_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN31_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN31_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN32_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN32_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN32_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN33_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN33_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN33_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN34_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN34_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN34_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN35_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN35_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN35_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN36_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN36_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN36_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN37_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN37_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN37_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN38_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN38_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN38_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN39_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN39_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN39_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN3_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN3_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN3_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN40_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN40_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN40_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN41_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN41_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN41_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN42_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN42_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN42_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN43_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN43_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN43_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN44_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN44_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN44_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN45_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN45_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN45_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN46_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN46_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN46_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN47_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN47_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN47_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN48_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN48_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN48_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN4_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN4_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN4_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN5_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN5_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN5_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN6_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN6_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN6_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN7_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN7_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN7_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN8_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN8_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN8_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PN9_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PN9_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PN9_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 PtlNetInfo_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 PtlNetInfo_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 PtlNetInfo_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 RepMsgReq_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 RepMsgReq_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 RepMsgReq_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 ResdBit1_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 ResdBit1_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 ResdBit1_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 ResdBit2_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 ResdBit2_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 ResdBit2_ZONE_FTM 0;
BA_ "GenSigInactiveValue" SG_ 1285 ScrNodId_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1285 ScrNodId_ZONE_FTM 7;
BA_ "GenSigStartValue" SG_ 1285 ScrNodId_ZONE_FTM 0;
BA_ "GenSigSendType" SG_ 1504 VC_Data_5E0_CRC 0;
BA_ "GenSigStartValue" SG_ 1504 VC_Data_5E0_CRC 0;
BA_ "GenSigSendType" SG_ 1504 VC_Data_5E0_MsgCntr 0;
BA_ "GenSigSendType" SG_ 1504 VC_Data_5E0_Payload 0;
BA_ "GenSigSendType" SG_ 1537 DiagReqData0_AllECU 7;
BA_ "GenSigStartValue" SG_ 1537 DiagReqData0_AllECU 0;
BA_ "GenSigSendType" SG_ 1537 DiagReqData1_AllECU 7;
BA_ "GenSigStartValue" SG_ 1537 DiagReqData1_AllECU 0;
BA_ "GenSigSendType" SG_ 1537 DiagReqData2_AllECU 7;
BA_ "GenSigStartValue" SG_ 1537 DiagReqData2_AllECU 0;
BA_ "GenSigSendType" SG_ 1537 DiagReqData3_AllECU 7;
BA_ "GenSigStartValue" SG_ 1537 DiagReqData3_AllECU 0;
BA_ "GenSigSendType" SG_ 1537 DiagReqData4_AllECU 7;
BA_ "GenSigStartValue" SG_ 1537 DiagReqData4_AllECU 0;
BA_ "GenSigSendType" SG_ 1537 DiagReqData5_AllECU 7;
BA_ "GenSigStartValue" SG_ 1537 DiagReqData5_AllECU 0;
BA_ "GenSigSendType" SG_ 1537 DiagReqData6_AllECU 7;
BA_ "GenSigStartValue" SG_ 1537 DiagReqData6_AllECU 0;
BA_ "GenSigSendType" SG_ 1537 DiagReqData7_AllECU 7;
BA_ "GenSigStartValue" SG_ 1537 DiagReqData7_AllECU 0;
BA_ "GenSigInactiveValue" SG_ 1578 DiagReqData0_SWC 0;
BA_ "GenSigSendType" SG_ 1578 DiagReqData0_SWC 7;
BA_ "GenSigStartValue" SG_ 1578 DiagReqData0_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1578 DiagReqData10_SWC 0;
BA_ "GenSigSendType" SG_ 1578 DiagReqData10_SWC 7;
BA_ "GenSigStartValue" SG_ 1578 DiagReqData10_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1578 DiagReqData11_SWC 0;
BA_ "GenSigSendType" SG_ 1578 DiagReqData11_SWC 7;
BA_ "GenSigStartValue" SG_ 1578 DiagReqData11_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1578 DiagReqData12_SWC 0;
BA_ "GenSigSendType" SG_ 1578 DiagReqData12_SWC 7;
BA_ "GenSigStartValue" SG_ 1578 DiagReqData12_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1578 DiagReqData13_SWC 0;
BA_ "GenSigSendType" SG_ 1578 DiagReqData13_SWC 7;
BA_ "GenSigStartValue" SG_ 1578 DiagReqData13_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1578 DiagReqData14_SWC 0;
BA_ "GenSigSendType" SG_ 1578 DiagReqData14_SWC 7;
BA_ "GenSigStartValue" SG_ 1578 DiagReqData14_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1578 DiagReqData15_SWC 0;
BA_ "GenSigSendType" SG_ 1578 DiagReqData15_SWC 7;
BA_ "GenSigStartValue" SG_ 1578 DiagReqData15_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1578 DiagReqData1_SWC 0;
BA_ "GenSigSendType" SG_ 1578 DiagReqData1_SWC 7;
BA_ "GenSigStartValue" SG_ 1578 DiagReqData1_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1578 DiagReqData2_SWC 0;
BA_ "GenSigSendType" SG_ 1578 DiagReqData2_SWC 7;
BA_ "GenSigStartValue" SG_ 1578 DiagReqData2_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1578 DiagReqData3_SWC 0;
BA_ "GenSigSendType" SG_ 1578 DiagReqData3_SWC 7;
BA_ "GenSigStartValue" SG_ 1578 DiagReqData3_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1578 DiagReqData4_SWC 0;
BA_ "GenSigSendType" SG_ 1578 DiagReqData4_SWC 7;
BA_ "GenSigStartValue" SG_ 1578 DiagReqData4_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1578 DiagReqData5_SWC 0;
BA_ "GenSigSendType" SG_ 1578 DiagReqData5_SWC 7;
BA_ "GenSigStartValue" SG_ 1578 DiagReqData5_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1578 DiagReqData6_SWC 0;
BA_ "GenSigSendType" SG_ 1578 DiagReqData6_SWC 7;
BA_ "GenSigStartValue" SG_ 1578 DiagReqData6_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1578 DiagReqData7_SWC 0;
BA_ "GenSigSendType" SG_ 1578 DiagReqData7_SWC 7;
BA_ "GenSigStartValue" SG_ 1578 DiagReqData7_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1578 DiagReqData8_SWC 0;
BA_ "GenSigSendType" SG_ 1578 DiagReqData8_SWC 7;
BA_ "GenSigStartValue" SG_ 1578 DiagReqData8_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1578 DiagReqData9_SWC 0;
BA_ "GenSigSendType" SG_ 1578 DiagReqData9_SWC 7;
BA_ "GenSigStartValue" SG_ 1578 DiagReqData9_SWC 0;
BA_ "GenSigInactiveValue" SG_ 1587 DiagReqData0_IC 0;
BA_ "GenSigSendType" SG_ 1587 DiagReqData0_IC 7;
BA_ "GenSigStartValue" SG_ 1587 DiagReqData0_IC 0;
BA_ "GenSigInactiveValue" SG_ 1587 DiagReqData10_IC 0;
BA_ "GenSigSendType" SG_ 1587 DiagReqData10_IC 7;
BA_ "GenSigStartValue" SG_ 1587 DiagReqData10_IC 0;
BA_ "GenSigInactiveValue" SG_ 1587 DiagReqData11_IC 0;
BA_ "GenSigSendType" SG_ 1587 DiagReqData11_IC 7;
BA_ "GenSigStartValue" SG_ 1587 DiagReqData11_IC 0;
BA_ "GenSigInactiveValue" SG_ 1587 DiagReqData12_IC 0;
BA_ "GenSigSendType" SG_ 1587 DiagReqData12_IC 7;
BA_ "GenSigStartValue" SG_ 1587 DiagReqData12_IC 0;
BA_ "GenSigInactiveValue" SG_ 1587 DiagReqData13_IC 0;
BA_ "GenSigSendType" SG_ 1587 DiagReqData13_IC 7;
BA_ "GenSigStartValue" SG_ 1587 DiagReqData13_IC 0;
BA_ "GenSigInactiveValue" SG_ 1587 DiagReqData14_IC 0;
BA_ "GenSigSendType" SG_ 1587 DiagReqData14_IC 7;
BA_ "GenSigStartValue" SG_ 1587 DiagReqData14_IC 0;
BA_ "GenSigInactiveValue" SG_ 1587 DiagReqData15_IC 0;
BA_ "GenSigSendType" SG_ 1587 DiagReqData15_IC 7;
BA_ "GenSigStartValue" SG_ 1587 DiagReqData15_IC 0;
BA_ "GenSigInactiveValue" SG_ 1587 DiagReqData1_IC 0;
BA_ "GenSigSendType" SG_ 1587 DiagReqData1_IC 7;
BA_ "GenSigStartValue" SG_ 1587 DiagReqData1_IC 0;
BA_ "GenSigInactiveValue" SG_ 1587 DiagReqData2_IC 0;
BA_ "GenSigSendType" SG_ 1587 DiagReqData2_IC 7;
BA_ "GenSigStartValue" SG_ 1587 DiagReqData2_IC 0;
BA_ "GenSigInactiveValue" SG_ 1587 DiagReqData3_IC 0;
BA_ "GenSigSendType" SG_ 1587 DiagReqData3_IC 7;
BA_ "GenSigStartValue" SG_ 1587 DiagReqData3_IC 0;
BA_ "GenSigInactiveValue" SG_ 1587 DiagReqData4_IC 0;
BA_ "GenSigSendType" SG_ 1587 DiagReqData4_IC 7;
BA_ "GenSigStartValue" SG_ 1587 DiagReqData4_IC 0;
BA_ "GenSigInactiveValue" SG_ 1587 DiagReqData5_IC 0;
BA_ "GenSigSendType" SG_ 1587 DiagReqData5_IC 7;
BA_ "GenSigStartValue" SG_ 1587 DiagReqData5_IC 0;
BA_ "GenSigInactiveValue" SG_ 1587 DiagReqData6_IC 0;
BA_ "GenSigSendType" SG_ 1587 DiagReqData6_IC 7;
BA_ "GenSigStartValue" SG_ 1587 DiagReqData6_IC 0;
BA_ "GenSigInactiveValue" SG_ 1587 DiagReqData7_IC 0;
BA_ "GenSigSendType" SG_ 1587 DiagReqData7_IC 7;
BA_ "GenSigStartValue" SG_ 1587 DiagReqData7_IC 0;
BA_ "GenSigInactiveValue" SG_ 1587 DiagReqData8_IC 0;
BA_ "GenSigSendType" SG_ 1587 DiagReqData8_IC 7;
BA_ "GenSigStartValue" SG_ 1587 DiagReqData8_IC 0;
BA_ "GenSigInactiveValue" SG_ 1587 DiagReqData9_IC 0;
BA_ "GenSigSendType" SG_ 1587 DiagReqData9_IC 7;
BA_ "GenSigStartValue" SG_ 1587 DiagReqData9_IC 0;
BA_ "GenSigInactiveValue" SG_ 1589 DiagReqData0_HUD 0;
BA_ "GenSigSendType" SG_ 1589 DiagReqData0_HUD 7;
BA_ "GenSigStartValue" SG_ 1589 DiagReqData0_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1589 DiagReqData10_HUD 0;
BA_ "GenSigSendType" SG_ 1589 DiagReqData10_HUD 7;
BA_ "GenSigStartValue" SG_ 1589 DiagReqData10_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1589 DiagReqData11_HUD 0;
BA_ "GenSigSendType" SG_ 1589 DiagReqData11_HUD 7;
BA_ "GenSigStartValue" SG_ 1589 DiagReqData11_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1589 DiagReqData12_HUD 0;
BA_ "GenSigSendType" SG_ 1589 DiagReqData12_HUD 7;
BA_ "GenSigStartValue" SG_ 1589 DiagReqData12_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1589 DiagReqData13_HUD 0;
BA_ "GenSigSendType" SG_ 1589 DiagReqData13_HUD 7;
BA_ "GenSigStartValue" SG_ 1589 DiagReqData13_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1589 DiagReqData14_HUD 0;
BA_ "GenSigSendType" SG_ 1589 DiagReqData14_HUD 7;
BA_ "GenSigStartValue" SG_ 1589 DiagReqData14_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1589 DiagReqData15_HUD 0;
BA_ "GenSigSendType" SG_ 1589 DiagReqData15_HUD 7;
BA_ "GenSigStartValue" SG_ 1589 DiagReqData15_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1589 DiagReqData1_HUD 0;
BA_ "GenSigSendType" SG_ 1589 DiagReqData1_HUD 7;
BA_ "GenSigStartValue" SG_ 1589 DiagReqData1_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1589 DiagReqData2_HUD 0;
BA_ "GenSigSendType" SG_ 1589 DiagReqData2_HUD 7;
BA_ "GenSigStartValue" SG_ 1589 DiagReqData2_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1589 DiagReqData3_HUD 0;
BA_ "GenSigSendType" SG_ 1589 DiagReqData3_HUD 7;
BA_ "GenSigStartValue" SG_ 1589 DiagReqData3_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1589 DiagReqData4_HUD 0;
BA_ "GenSigSendType" SG_ 1589 DiagReqData4_HUD 7;
BA_ "GenSigStartValue" SG_ 1589 DiagReqData4_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1589 DiagReqData5_HUD 0;
BA_ "GenSigSendType" SG_ 1589 DiagReqData5_HUD 7;
BA_ "GenSigStartValue" SG_ 1589 DiagReqData5_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1589 DiagReqData6_HUD 0;
BA_ "GenSigSendType" SG_ 1589 DiagReqData6_HUD 7;
BA_ "GenSigStartValue" SG_ 1589 DiagReqData6_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1589 DiagReqData7_HUD 0;
BA_ "GenSigSendType" SG_ 1589 DiagReqData7_HUD 7;
BA_ "GenSigStartValue" SG_ 1589 DiagReqData7_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1589 DiagReqData8_HUD 0;
BA_ "GenSigSendType" SG_ 1589 DiagReqData8_HUD 7;
BA_ "GenSigStartValue" SG_ 1589 DiagReqData8_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1589 DiagReqData9_HUD 0;
BA_ "GenSigSendType" SG_ 1589 DiagReqData9_HUD 7;
BA_ "GenSigStartValue" SG_ 1589 DiagReqData9_HUD 0;
BA_ "GenSigInactiveValue" SG_ 1590 DiagReqData0_ICS 0;
BA_ "GenSigSendType" SG_ 1590 DiagReqData0_ICS 7;
BA_ "GenSigStartValue" SG_ 1590 DiagReqData0_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1590 DiagReqData10_ICS 0;
BA_ "GenSigSendType" SG_ 1590 DiagReqData10_ICS 7;
BA_ "GenSigStartValue" SG_ 1590 DiagReqData10_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1590 DiagReqData11_ICS 0;
BA_ "GenSigSendType" SG_ 1590 DiagReqData11_ICS 7;
BA_ "GenSigStartValue" SG_ 1590 DiagReqData11_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1590 DiagReqData12_ICS 0;
BA_ "GenSigSendType" SG_ 1590 DiagReqData12_ICS 7;
BA_ "GenSigStartValue" SG_ 1590 DiagReqData12_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1590 DiagReqData13_ICS 0;
BA_ "GenSigSendType" SG_ 1590 DiagReqData13_ICS 7;
BA_ "GenSigStartValue" SG_ 1590 DiagReqData13_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1590 DiagReqData14_ICS 0;
BA_ "GenSigSendType" SG_ 1590 DiagReqData14_ICS 7;
BA_ "GenSigStartValue" SG_ 1590 DiagReqData14_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1590 DiagReqData15_ICS 0;
BA_ "GenSigSendType" SG_ 1590 DiagReqData15_ICS 7;
BA_ "GenSigStartValue" SG_ 1590 DiagReqData15_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1590 DiagReqData1_ICS 0;
BA_ "GenSigSendType" SG_ 1590 DiagReqData1_ICS 7;
BA_ "GenSigStartValue" SG_ 1590 DiagReqData1_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1590 DiagReqData2_ICS 0;
BA_ "GenSigSendType" SG_ 1590 DiagReqData2_ICS 7;
BA_ "GenSigStartValue" SG_ 1590 DiagReqData2_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1590 DiagReqData3_ICS 0;
BA_ "GenSigSendType" SG_ 1590 DiagReqData3_ICS 7;
BA_ "GenSigStartValue" SG_ 1590 DiagReqData3_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1590 DiagReqData4_ICS 0;
BA_ "GenSigSendType" SG_ 1590 DiagReqData4_ICS 7;
BA_ "GenSigStartValue" SG_ 1590 DiagReqData4_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1590 DiagReqData5_ICS 0;
BA_ "GenSigSendType" SG_ 1590 DiagReqData5_ICS 7;
BA_ "GenSigStartValue" SG_ 1590 DiagReqData5_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1590 DiagReqData6_ICS 0;
BA_ "GenSigSendType" SG_ 1590 DiagReqData6_ICS 7;
BA_ "GenSigStartValue" SG_ 1590 DiagReqData6_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1590 DiagReqData7_ICS 0;
BA_ "GenSigSendType" SG_ 1590 DiagReqData7_ICS 7;
BA_ "GenSigStartValue" SG_ 1590 DiagReqData7_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1590 DiagReqData8_ICS 0;
BA_ "GenSigSendType" SG_ 1590 DiagReqData8_ICS 7;
BA_ "GenSigStartValue" SG_ 1590 DiagReqData8_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1590 DiagReqData9_ICS 0;
BA_ "GenSigSendType" SG_ 1590 DiagReqData9_ICS 7;
BA_ "GenSigStartValue" SG_ 1590 DiagReqData9_ICS 0;
BA_ "GenSigInactiveValue" SG_ 1591 DiagReqData0_AMP 0;
BA_ "GenSigSendType" SG_ 1591 DiagReqData0_AMP 7;
BA_ "GenSigStartValue" SG_ 1591 DiagReqData0_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1591 DiagReqData10_AMP 0;
BA_ "GenSigSendType" SG_ 1591 DiagReqData10_AMP 7;
BA_ "GenSigStartValue" SG_ 1591 DiagReqData10_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1591 DiagReqData11_AMP 0;
BA_ "GenSigSendType" SG_ 1591 DiagReqData11_AMP 7;
BA_ "GenSigStartValue" SG_ 1591 DiagReqData11_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1591 DiagReqData12_AMP 0;
BA_ "GenSigSendType" SG_ 1591 DiagReqData12_AMP 7;
BA_ "GenSigStartValue" SG_ 1591 DiagReqData12_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1591 DiagReqData13_AMP 0;
BA_ "GenSigSendType" SG_ 1591 DiagReqData13_AMP 7;
BA_ "GenSigStartValue" SG_ 1591 DiagReqData13_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1591 DiagReqData14_AMP 0;
BA_ "GenSigSendType" SG_ 1591 DiagReqData14_AMP 7;
BA_ "GenSigStartValue" SG_ 1591 DiagReqData14_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1591 DiagReqData15_AMP 0;
BA_ "GenSigSendType" SG_ 1591 DiagReqData15_AMP 7;
BA_ "GenSigStartValue" SG_ 1591 DiagReqData15_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1591 DiagReqData1_AMP 0;
BA_ "GenSigSendType" SG_ 1591 DiagReqData1_AMP 7;
BA_ "GenSigStartValue" SG_ 1591 DiagReqData1_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1591 DiagReqData2_AMP 0;
BA_ "GenSigSendType" SG_ 1591 DiagReqData2_AMP 7;
BA_ "GenSigStartValue" SG_ 1591 DiagReqData2_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1591 DiagReqData3_AMP 0;
BA_ "GenSigSendType" SG_ 1591 DiagReqData3_AMP 7;
BA_ "GenSigStartValue" SG_ 1591 DiagReqData3_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1591 DiagReqData4_AMP 0;
BA_ "GenSigSendType" SG_ 1591 DiagReqData4_AMP 7;
BA_ "GenSigStartValue" SG_ 1591 DiagReqData4_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1591 DiagReqData5_AMP 0;
BA_ "GenSigSendType" SG_ 1591 DiagReqData5_AMP 7;
BA_ "GenSigStartValue" SG_ 1591 DiagReqData5_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1591 DiagReqData6_AMP 0;
BA_ "GenSigSendType" SG_ 1591 DiagReqData6_AMP 7;
BA_ "GenSigStartValue" SG_ 1591 DiagReqData6_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1591 DiagReqData7_AMP 0;
BA_ "GenSigSendType" SG_ 1591 DiagReqData7_AMP 7;
BA_ "GenSigStartValue" SG_ 1591 DiagReqData7_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1591 DiagReqData8_AMP 0;
BA_ "GenSigSendType" SG_ 1591 DiagReqData8_AMP 7;
BA_ "GenSigStartValue" SG_ 1591 DiagReqData8_AMP 0;
BA_ "GenSigInactiveValue" SG_ 1591 DiagReqData9_AMP 0;
BA_ "GenSigSendType" SG_ 1591 DiagReqData9_AMP 7;
BA_ "GenSigStartValue" SG_ 1591 DiagReqData9_AMP 0;
BA_ "GenSigSendType" SG_ 1595 DiagReqData0_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1595 DiagReqData0_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1595 DiagReqData10_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1595 DiagReqData10_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1595 DiagReqData11_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1595 DiagReqData11_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1595 DiagReqData12_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1595 DiagReqData12_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1595 DiagReqData13_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1595 DiagReqData13_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1595 DiagReqData14_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1595 DiagReqData14_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1595 DiagReqData15_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1595 DiagReqData15_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1595 DiagReqData1_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1595 DiagReqData1_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1595 DiagReqData2_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1595 DiagReqData2_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1595 DiagReqData3_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1595 DiagReqData3_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1595 DiagReqData4_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1595 DiagReqData4_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1595 DiagReqData5_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1595 DiagReqData5_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1595 DiagReqData6_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1595 DiagReqData6_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1595 DiagReqData7_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1595 DiagReqData7_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1595 DiagReqData8_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1595 DiagReqData8_CNSL_I 0;
BA_ "GenSigSendType" SG_ 1595 DiagReqData9_CNSL_I 7;
BA_ "GenSigStartValue" SG_ 1595 DiagReqData9_CNSL_I 0;
BA_ "GenSigInactiveValue" SG_ 1597 DiagReqData0_NOMI 0;
BA_ "GenSigSendType" SG_ 1597 DiagReqData0_NOMI 7;
BA_ "GenSigStartValue" SG_ 1597 DiagReqData0_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1597 DiagReqData10_NOMI 0;
BA_ "GenSigSendType" SG_ 1597 DiagReqData10_NOMI 7;
BA_ "GenSigStartValue" SG_ 1597 DiagReqData10_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1597 DiagReqData11_NOMI 0;
BA_ "GenSigSendType" SG_ 1597 DiagReqData11_NOMI 7;
BA_ "GenSigStartValue" SG_ 1597 DiagReqData11_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1597 DiagReqData12_NOMI 0;
BA_ "GenSigSendType" SG_ 1597 DiagReqData12_NOMI 7;
BA_ "GenSigStartValue" SG_ 1597 DiagReqData12_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1597 DiagReqData13_NOMI 0;
BA_ "GenSigSendType" SG_ 1597 DiagReqData13_NOMI 7;
BA_ "GenSigStartValue" SG_ 1597 DiagReqData13_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1597 DiagReqData14_NOMI 0;
BA_ "GenSigSendType" SG_ 1597 DiagReqData14_NOMI 7;
BA_ "GenSigStartValue" SG_ 1597 DiagReqData14_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1597 DiagReqData15_NOMI 0;
BA_ "GenSigSendType" SG_ 1597 DiagReqData15_NOMI 7;
BA_ "GenSigStartValue" SG_ 1597 DiagReqData15_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1597 DiagReqData1_NOMI 0;
BA_ "GenSigSendType" SG_ 1597 DiagReqData1_NOMI 7;
BA_ "GenSigStartValue" SG_ 1597 DiagReqData1_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1597 DiagReqData2_NOMI 0;
BA_ "GenSigSendType" SG_ 1597 DiagReqData2_NOMI 7;
BA_ "GenSigStartValue" SG_ 1597 DiagReqData2_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1597 DiagReqData3_NOMI 0;
BA_ "GenSigSendType" SG_ 1597 DiagReqData3_NOMI 7;
BA_ "GenSigStartValue" SG_ 1597 DiagReqData3_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1597 DiagReqData4_NOMI 0;
BA_ "GenSigSendType" SG_ 1597 DiagReqData4_NOMI 7;
BA_ "GenSigStartValue" SG_ 1597 DiagReqData4_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1597 DiagReqData5_NOMI 0;
BA_ "GenSigSendType" SG_ 1597 DiagReqData5_NOMI 7;
BA_ "GenSigStartValue" SG_ 1597 DiagReqData5_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1597 DiagReqData6_NOMI 0;
BA_ "GenSigSendType" SG_ 1597 DiagReqData6_NOMI 7;
BA_ "GenSigStartValue" SG_ 1597 DiagReqData6_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1597 DiagReqData7_NOMI 0;
BA_ "GenSigSendType" SG_ 1597 DiagReqData7_NOMI 7;
BA_ "GenSigStartValue" SG_ 1597 DiagReqData7_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1597 DiagReqData8_NOMI 0;
BA_ "GenSigSendType" SG_ 1597 DiagReqData8_NOMI 7;
BA_ "GenSigStartValue" SG_ 1597 DiagReqData8_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1597 DiagReqData9_NOMI 0;
BA_ "GenSigSendType" SG_ 1597 DiagReqData9_NOMI 7;
BA_ "GenSigStartValue" SG_ 1597 DiagReqData9_NOMI 0;
BA_ "GenSigInactiveValue" SG_ 1600 DiagReqData0_FI_R 0;
BA_ "GenSigSendType" SG_ 1600 DiagReqData0_FI_R 7;
BA_ "GenSigStartValue" SG_ 1600 DiagReqData0_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1600 DiagReqData10_FI_R 0;
BA_ "GenSigSendType" SG_ 1600 DiagReqData10_FI_R 7;
BA_ "GenSigStartValue" SG_ 1600 DiagReqData10_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1600 DiagReqData11_FI_R 0;
BA_ "GenSigSendType" SG_ 1600 DiagReqData11_FI_R 7;
BA_ "GenSigStartValue" SG_ 1600 DiagReqData11_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1600 DiagReqData12_FI_R 0;
BA_ "GenSigSendType" SG_ 1600 DiagReqData12_FI_R 7;
BA_ "GenSigStartValue" SG_ 1600 DiagReqData12_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1600 DiagReqData13_FI_R 0;
BA_ "GenSigSendType" SG_ 1600 DiagReqData13_FI_R 7;
BA_ "GenSigStartValue" SG_ 1600 DiagReqData13_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1600 DiagReqData14_FI_R 0;
BA_ "GenSigSendType" SG_ 1600 DiagReqData14_FI_R 7;
BA_ "GenSigStartValue" SG_ 1600 DiagReqData14_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1600 DiagReqData15_FI_R 0;
BA_ "GenSigSendType" SG_ 1600 DiagReqData15_FI_R 7;
BA_ "GenSigStartValue" SG_ 1600 DiagReqData15_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1600 DiagReqData1_FI_R 0;
BA_ "GenSigSendType" SG_ 1600 DiagReqData1_FI_R 7;
BA_ "GenSigStartValue" SG_ 1600 DiagReqData1_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1600 DiagReqData2_FI_R 0;
BA_ "GenSigSendType" SG_ 1600 DiagReqData2_FI_R 7;
BA_ "GenSigStartValue" SG_ 1600 DiagReqData2_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1600 DiagReqData3_FI_R 0;
BA_ "GenSigSendType" SG_ 1600 DiagReqData3_FI_R 7;
BA_ "GenSigStartValue" SG_ 1600 DiagReqData3_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1600 DiagReqData4_FI_R 0;
BA_ "GenSigSendType" SG_ 1600 DiagReqData4_FI_R 7;
BA_ "GenSigStartValue" SG_ 1600 DiagReqData4_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1600 DiagReqData5_FI_R 0;
BA_ "GenSigSendType" SG_ 1600 DiagReqData5_FI_R 7;
BA_ "GenSigStartValue" SG_ 1600 DiagReqData5_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1600 DiagReqData6_FI_R 0;
BA_ "GenSigSendType" SG_ 1600 DiagReqData6_FI_R 7;
BA_ "GenSigStartValue" SG_ 1600 DiagReqData6_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1600 DiagReqData7_FI_R 0;
BA_ "GenSigSendType" SG_ 1600 DiagReqData7_FI_R 7;
BA_ "GenSigStartValue" SG_ 1600 DiagReqData7_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1600 DiagReqData8_FI_R 0;
BA_ "GenSigSendType" SG_ 1600 DiagReqData8_FI_R 7;
BA_ "GenSigStartValue" SG_ 1600 DiagReqData8_FI_R 0;
BA_ "GenSigInactiveValue" SG_ 1600 DiagReqData9_FI_R 0;
BA_ "GenSigSendType" SG_ 1600 DiagReqData9_FI_R 7;
BA_ "GenSigStartValue" SG_ 1600 DiagReqData9_FI_R 0;


VAL_ 836 AMP_SetEPMode_Sts 3 "Reserved;" 2 "C;" 1 "B;" 0 "A;" ;
VAL_ 836 AMP_SetEPSwitch_Sts 1 "Open" 0 "Close" ;
VAL_ 836 AMP_SetKROKMicType_Sts 1 "Handy Mic;" 0 "OnCar Mic;" ;
VAL_ 836 AMP_SetKROKSwitch_Sts 1 "Open" 0 "Close" ;
VAL_ 836 AMP_SetMediaFrontMute_Sts 1 "Unmute" 0 "Mute" ;
VAL_ 836 AMP_SetMediaPath_Sts 3 "7.1.4,48Khz" 2 "5.1,48Khz" 1 "2.0,96Khz" 0 "2.0,48Khz" ;
VAL_ 836 AMP_SetMediaRearMute_Sts 1 "Unmute" 0 "Mute" ;
VAL_ 836 AMP_SetMediaSwitch_Sts 1 "Open" 0 "Close" ;
VAL_ 836 AMP_SetMediaType_Sts 3 "Reserved;" 2 "Game;" 1 "Movie;" 0 "Music;" ;
VAL_ 836 AMP_SetMute_Sts 1 "Open" 0 "Close" ;
VAL_ 836 AMP_SetNomiSwitch_Sts 1 "Open" 0 "Close" ;
VAL_ 836 AMP_SetNomiType_Sts 3 "Reserved;" 2 "C;" 1 "B;" 0 "A;" ;
VAL_ 836 AMP_SetPEQSwitch_Sts 1 "Open" 0 "Close" ;
VAL_ 836 AMP_SetPhonePos_Sts 3 "Reserved;" 2 "C;" 1 "B;" 0 "A;" ;
VAL_ 836 AMP_SetPhoneSwitch_Sts 1 "Open" 0 "Close" ;
VAL_ 836 AMP_SetRNCSwitch_Sts 1 "Open" 0 "Close" ;
VAL_ 836 AMP_SetSoundFiledSwitch_Sts 1 "Open" 0 "Close" ;
VAL_ 836 AMP_SetSoundFiled_Sts 7 "Reserved;" 6 "Reserved;" 5 "Reserved;" 4 "Surround;" 3 "Theatre;" 2 "Rear Seat;" 1 "Driver;" 0 "All Seat;" ;
VAL_ 836 AMP_SetWorkMode_Sts 7 "Invalid" 6 "Reserved" 5 "Reserved" 4 "Reserved" 3 "Sleep" 2 "Normal" 1 "Standby" 0 "Ready" ;
VAL_ 917 A2B1_TDM_State 1 "Abnormal" 0 "Normal" ;
VAL_ 917 A2B2_TDM_State 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Audio_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_10_comm_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_10_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_1_comm_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_1_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_2_comm_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_2_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_3_comm_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_3_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_4_comm_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_4_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_5_comm_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_5_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_6_comm_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_6_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_7_comm_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_7_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_8_comm_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_8_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_9_comm_status 1 "Abnormal" 0 "Normal" ;
VAL_ 917 Chip_9_status 1 "Abnormal" 0 "Normal" ;
VAL_ 918 DSP1_STS 1 "Abnormal" 0 "Normal" ;
VAL_ 918 DSP2_STS 1 "Abnormal" 0 "Normal" ;
VAL_ 918 DSP3_STS 1 "Abnormal" 0 "Normal" ;
VAL_ 1335 PN10_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN11_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN12_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN13_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN14_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN15_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN16_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN17_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN18_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN19_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN1_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN20_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN21_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN22_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN23_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN24_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN25_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN26_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN27_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN28_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN29_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN2_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN30_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN31_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN32_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN33_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN34_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN35_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN36_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN37_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN38_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN39_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN3_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN40_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN41_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN42_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN43_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN44_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN45_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN46_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN47_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN48_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN4_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN5_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN6_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN7_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN8_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 PN9_AMP 1 "Awake" 0 "Not awake" ;
VAL_ 1335 RepMsgReq_AMP 1 "Repeat message request" 0 "No repeat message request" ;
VAL_ 456 CNSL_I_LeftBtn_LongPress 1 "Pressed" 0 "Not press" ;
VAL_ 456 CNSL_I_MidTp_LongPress 1 "Pressed" 0 "Not press" ;
VAL_ 456 CNSL_I_RightBtn_LongPress 1 "Pressed" 0 "Not press" ;
VAL_ 659 CNSL_I_EnableSwSt 1 "Enabled" 0 "Disabled" ;
VAL_ 659 CNSL_I_OpModeSt 7 "Reserved" 6 "Reserved" 5 "Reserved" 4 "HMI control" 3 "Call" 2 "MultiMedia" 1 "Global" 0 "Invalid" ;
VAL_ 659 CNSL_I_SensSt 3 "Reserved" 2 "High" 1 "Low" 0 "Invalid" ;
VAL_ 659 CNSL_I_St 15 "Reserved" 14 "Reserved" 13 "Reserved" 12 "Reserved" 11 "Reserved" 10 "Reserved" 9 "Reserved" 8 "Reserved" 7 "Reserved" 6 "Reserved" 5 "Reserved" 4 "Reserved" 3 "Reserved" 2 "Reserved" 1 "Reserved" 0 "No Error" ;
VAL_ 659 CNSL_I_VibDeltalSt 15 "15 levels" 14 "14 levels" 13 "13 levels" 12 "12 levels" 11 "11 levels" 10 "10 levels" 9 "9 levels" 8 "8 levels" 7 "7 levels" 6 "6 levels" 5 "5 levels" 4 "4 levels" 3 "3 levels" 2 "2 levels" 1 "default level" 0 "Invalid" ;
VAL_ 659 CNSL_I_VibFbSt 1 "On" 0 "Off" ;
VAL_ 1178 CNSL_I_LeftBtn_Press 1 "Pressed" 0 "Not press" ;
VAL_ 1178 CNSL_I_LeftBtn_SwipeDown 1 "Triggered" 0 "Not Trigger" ;
VAL_ 1178 CNSL_I_LeftBtn_SwipeUp 1 "Triggered" 0 "Not Trigger" ;
VAL_ 1178 CNSL_I_LeftBtn_TouchSt 1 "Touching" 0 "No touch" ;
VAL_ 1178 CNSL_I_MidTp_2Finger_SwipeDown 1 "Triggered" 0 "Not Trigger" ;
VAL_ 1178 CNSL_I_MidTp_2Finger_SwipeLeft 1 "Triggered" 0 "Not Trigger" ;
VAL_ 1178 CNSL_I_MidTp_2Finger_SwipeRight 1 "Triggered" 0 "Not Trigger" ;
VAL_ 1178 CNSL_I_MidTp_2Finger_SwipeUp 1 "Triggered" 0 "Not Trigger" ;
VAL_ 1178 CNSL_I_MidTp_3Finger_SwipeDown 1 "Triggered" 0 "Not Trigger" ;
VAL_ 1178 CNSL_I_MidTp_3Finger_SwipeLeft 1 "Triggered" 0 "Not Trigger" ;
VAL_ 1178 CNSL_I_MidTp_3Finger_SwipeRight 1 "Triggered" 0 "Not Trigger" ;
VAL_ 1178 CNSL_I_MidTp_3Finger_SwipeUp 1 "Triggered" 0 "Not Trigger" ;
VAL_ 1178 CNSL_I_MidTp_Press 1 "Pressed" 0 "Not press" ;
VAL_ 1178 CNSL_I_MidTp_SwipeDown 1 "Triggered" 0 "Not Trigger" ;
VAL_ 1178 CNSL_I_MidTp_SwipeLeft 1 "Triggered" 0 "Not Trigger" ;
VAL_ 1178 CNSL_I_MidTp_SwipeRight 1 "Triggered" 0 "Not Trigger" ;
VAL_ 1178 CNSL_I_MidTp_SwipeUp 1 "Triggered" 0 "Not Trigger" ;
VAL_ 1178 CNSL_I_MidTp_TouchSt_F1 1 "Touching" 0 "No touch" ;
VAL_ 1178 CNSL_I_MidTp_TouchSt_F2 1 "Touching" 0 "No touch" ;
VAL_ 1178 CNSL_I_RightBtn_Press 1 "Pressed" 0 "Not press" ;
VAL_ 1178 CNSL_I_RightBtn_SwipeDown 1 "Triggered" 0 "Not Trigger" ;
VAL_ 1178 CNSL_I_RightBtn_SwipeUp 1 "Triggered" 0 "Not Trigger" ;
VAL_ 1178 CNSL_I_RightBtn_TouchSt 1 "Touching" 0 "No touch" ;
VAL_ 1339 PNC10_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC11_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC12_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC13_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC14_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC15_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC16_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC17_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC18_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC19_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC1_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC20_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC21_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC22_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC23_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC24_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC25_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC26_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC27_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC28_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC29_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC2_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC30_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC31_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC32_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC33_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC34_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC35_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC36_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC37_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC38_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC39_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC3_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC40_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC41_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC42_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC43_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC44_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC45_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC46_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC47_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC48_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC4_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC5_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC6_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC7_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC8_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 PNC9_CNSL_I 1 "Awake" 0 "Not awake" ;
VAL_ 1339 RepMsgReq_CNSL_I 1 "Repeat message request" 0 "No repeat message request" ;
VAL_ 804 FI_R_Sts 7 "Invalid" 6 "Reserved" 5 "Reserved" 4 "Standby" 3 "Normal mode" 2 "Ready mode" 1 "Not Ready Mode" 0 "Sleep mode" ;
VAL_ 804 FI_R_WarningRsp_BriLimt 1 "Error" 0 "Not Error" ;
VAL_ 804 FI_R_WarningRsp_LCD 1 "Error" 0 "Not Error" ;
VAL_ 804 FI_R_WarningRsp_LedDrvr 1 "Error" 0 "Not Error" ;
VAL_ 804 FI_R_WarningRsp_LocalDimmingFail 1 "Error" 0 "Not Error" ;
VAL_ 804 FI_R_WarningRsp_LockSts 1 "Unlock" 0 "Lock" ;
VAL_ 804 FI_R_WarningRsp_PMICErr 1 "Error" 0 "Not Error" ;
VAL_ 804 FI_R_WarningRsp_TconErr 1 "Error" 0 "Not Error" ;
VAL_ 804 FI_R_WarningRsp_Thi 1 "Error" 0 "Not Error" ;
VAL_ 804 FI_R_WarningRsp_TouchErr 1 "Error" 0 "Not Error" ;
VAL_ 804 FI_R_WarningRsp_UHiLo 1 "Error" 0 "Not Error" ;
VAL_ 1344 PN10_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN11_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN12_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN13_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN14_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN15_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN16_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN17_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN18_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN19_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN1_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN20_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN21_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN22_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN23_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN24_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN25_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN26_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN27_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN28_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN29_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN2_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN30_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN31_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN32_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN33_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN34_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN35_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN36_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN37_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN38_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN39_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN3_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN40_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN41_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN42_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN43_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN44_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN45_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN46_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN47_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN48_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN4_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN5_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN6_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN7_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN8_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 PN9_FI_RR 1 "Awake" 0 "Not awake" ;
VAL_ 1344 RepMsgReq_FI_RR 1 "Repeat message request" 0 "No repeat message request" ;
VAL_ 776 HUD_Sts 7 " Invalid" 6 " Reserved" 5 " Reserved" 4 " Reserved" 3 " Normal mode" 2 "Ready mode" 1 "Not Ready mode" 0 " sleep mode" ;
VAL_ 776 HUD_WarningRsp_BriLimd 1 "Error" 0 "No Error" ;
VAL_ 776 HUD_WarningRsp_LockSts 1 "Unlock" 0 "Lock" ;
VAL_ 776 HUD_WarningRsp_MotFail 1 "Error" 0 "No Error" ;
VAL_ 776 HUD_WarningRsp_TFT_LEDFail 1 "Error" 0 "No Error" ;
VAL_ 776 HUD_WarningRsp_Thi 1 "Error" 0 "No Error" ;
VAL_ 1333 PN10_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN11_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN12_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN13_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN14_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN15_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN16_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN17_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN18_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN19_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN1_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN20_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN21_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN22_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN23_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN24_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN25_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN26_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN27_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN28_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN29_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN2_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN30_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN31_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN32_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN33_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN34_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN35_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN36_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN37_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN38_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN39_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN3_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN40_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN41_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN42_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN43_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN44_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN45_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN46_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN47_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN48_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN4_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN5_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN6_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN7_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN8_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 PN9_HUD 1 "Awake" 0 "Not awake" ;
VAL_ 1333 RepMsgReq_HUD 1 "Repeat message request" 0 "No repeat message request" ;
VAL_ 741 IC_Sts 7 "Invalid" 6 "Reserved" 5 "Reserved" 4 "Reserved" 3 "Normal mode" 2 "Ready mode" 1 "Not Ready mode" 0 "Sleep mode" ;
VAL_ 741 IC_WarningRsp_BriLimt_L 1 "Error" 0 "No Error" ;
VAL_ 741 IC_WarningRsp_BriLimt_R 1 "Error" 0 "No Error" ;
VAL_ 741 IC_WarningRsp_LCDFail_L 1 "Error" 0 "No Error" ;
VAL_ 741 IC_WarningRsp_LCDFail_R 1 "Error" 0 "No Error" ;
VAL_ 741 IC_WarningRsp_LedDrvr_L 1 "Error" 0 "No Error" ;
VAL_ 741 IC_WarningRsp_LedDrvr_R 1 "Error" 0 "No Error" ;
VAL_ 741 IC_WarningRsp_LocalDimmingFail_L 1 "Error" 0 "No Error" ;
VAL_ 741 IC_WarningRsp_LocalDimmingFail_R 1 "Error" 0 "No Error" ;
VAL_ 741 IC_WarningRsp_LockSts 1 "Unlock" 0 "Lock" ;
VAL_ 741 IC_WarningRsp_Thi_L 1 "Error" 0 "No Error" ;
VAL_ 741 IC_WarningRsp_Thi_R 1 "Error" 0 "No Error" ;
VAL_ 741 IC_WarningRsp_UHiLo_L 1 "Error" 0 "No Error" ;
VAL_ 741 IC_WarningRsp_UHiLo_R 1 "Error" 0 "No Error" ;
VAL_ 1331 PN10_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN11_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN12_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN13_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN14_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN15_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN16_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN17_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN18_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN19_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN1_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN20_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN21_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN22_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN23_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN24_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN25_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN26_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN27_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN28_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN29_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN2_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN30_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN31_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN32_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN33_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN34_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN35_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN36_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN37_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN38_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN39_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN3_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN40_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN41_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN42_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN43_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN44_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN45_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN46_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN47_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN48_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN4_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN5_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN6_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN7_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN8_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 PN9_IC 1 "Awake" 0 "Not awake" ;
VAL_ 1331 RepMsgReq_IC 1 "Repeat message request" 0 "No repeat message request" ;
VAL_ 755 ICS_Sts 7 "Invalid" 6 "Reserved" 5 "Reserved" 4 "Reserved" 3 "Normal mode" 2 "Ready mode" 1 "Not Ready mode" 0 "Sleep mode" ;
VAL_ 755 ICS_WarningRsp_BriLimt 1 "Error" 0 "No Error" ;
VAL_ 755 ICS_WarningRsp_BridgeErr 1 "Error" 0 "No Error" ;
VAL_ 755 ICS_WarningRsp_LockSts 1 "Unlock" 0 "Lock" ;
VAL_ 755 ICS_WarningRsp_PMICErr 1 "Error" 0 "No Error" ;
VAL_ 755 ICS_WarningRsp_TconErr 1 "Error" 0 "No Error" ;
VAL_ 755 ICS_WarningRsp_Thi 1 "Error" 0 "No Error" ;
VAL_ 755 ICS_WarningRsp_TouchErr 1 "Error" 0 "No Error" ;
VAL_ 755 ICS_WarningRsp_UHiLo 1 "Error" 0 "No Error" ;
VAL_ 1334 PN10_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN11_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN12_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN13_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN14_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN15_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN16_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN17_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN18_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN19_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN1_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN20_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN21_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN22_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN23_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN24_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN25_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN26_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN27_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN28_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN29_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN2_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN30_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN31_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN32_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN33_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN34_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN35_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN36_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN37_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN38_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN39_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN3_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN40_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN41_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN42_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN43_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN44_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN45_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN46_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN47_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN48_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN4_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN5_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN6_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN7_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN8_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 PN9_ICS 1 "Awake" 0 "Not awake" ;
VAL_ 1334 RepMsgReq_ICS 1 "Repeat message request" 0 "No repeat message request" ;
VAL_ 453 HALO_WORK_STATUS 255 "Invalid" 254 "Dynamic" 105 "Sleep" 104 "Shutdown" 103 "Charging" 102 "SideDoorOpen" 101 "WakeUp" 26 "Converation" 25 "Fatigue warning" 24 "Remind update" 23 "Remind vehicle warning and status report" 22 "New device connected" 21 "Introduce function" 20 "Need filt air" 19 "Self Introduction" 18 "Greet User" 17 "Function not implement" 16 "Silent" 15 "Music sync" 14 "Celebration" 13 "Self timer photo" 12 "Command not understood" 11 "Point center screen" 10 "Notification" 9 "Ongoing call" 8 "Incoming call" 7 "Complete mission" 6 "Processing/Thinking" 5 "Talking" 4 "Acknowledge" 3 "Listening" 2 "Activate voice interaction" 1 "Idle" ;
VAL_ 453 NOMI_Sts 7 "Invalid" 6 "Reserved" 5 "Reserved" 4 "Reserved" 3 "Normal" 2 "Ready" 1 "Standby" 0 "Sleep" ;
VAL_ 453 NOMI_Type 7 "Invalid" 6 "Reserved" 5 "Halo 3.0" 4 "Mate 3.0" 3 "Mate 2.0" 2 "Halo 2.0" 1 "Mate 1.0" 0 "Halo 1.0" ;
VAL_ 1163 MATERespFramePAck 7 "invalid" 6 "reserved" 5 "Blocked" 4 "free" 3 "out of range" 2 "ERROR" 1 "Rotate into place" 0 "CDC Cmd received" ;
VAL_ 1163 MATERespFrameYAck 7 "invalid" 6 "reserved" 5 "Blocked" 4 "free" 3 "out of range" 2 "ERROR" 1 "Rotate into place" 0 "CDC Cmd received" ;
VAL_ 1163 MateRespCmdAction 7 "invalid" 6 "reserved" 5 "Blocked" 4 "free" 3 "out of range" 2 "ERROR" 1 "Rotate into place" 0 "CDC Cmd received" ;
VAL_ 1169 HALO_RSP_CODE 255 "Invalid" 1 "Fail" 0 "OK" ;
VAL_ 1341 PN10_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN11_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN12_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN13_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN14_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN15_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN16_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN17_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN18_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN19_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN1_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN20_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN21_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN22_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN23_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN24_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN25_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN26_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN27_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN28_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN29_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN2_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN30_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN31_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN32_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN33_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN34_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN35_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN36_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN37_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN38_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN39_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN3_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN40_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN41_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN42_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN43_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN44_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN45_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN46_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN47_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN48_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN4_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN5_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN6_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN7_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN8_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 PN9_NOMI 1 "Awake" 0 "Not awake" ;
VAL_ 1341 RepMsgReq_NOMI 1 "Repeat message request" 0 "No repeat message request" ;
VAL_ 671 MenuPushSwtSts 3 " Invalid" 2 " Reserved" 1 " Pressed" 0 " Not pressed" ;
VAL_ 671 NOMICtrlPushSwtSts 3 " Invalid" 2 " Reserved" 1 " Pressed" 0 " Not pressed" ;
VAL_ 671 SWCFailSts 3 "Invalid" 2 "Reserved" 1 "Fail" 0 "Normal" ;
VAL_ 671 SWCLe_CentPushSwtSts 3 "Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 671 SWCLe_DwnPushSwtSts 3 "Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 671 SWCLe_LePushSwtSts 3 "Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 671 SWCLe_RiPushSwtSts 3 "Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 671 SWCLe_UpPushSwtSts 3 "Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 671 SWCRi_CentPushSwtSts 3 "Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 671 SWCRi_DwnPushSwtSts 3 "Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 671 SWCRi_LePushSwtSts 3 "Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 671 SWCRi_RiPushSwtSts 3 "Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 671 SWCRi_UpPushSwtSts 3 "Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 671 Spare01PushSwtSts 3 "Invalid" 2 "Reserved" 1 "Pressed" 0 "Not pressed" ;
VAL_ 77 SWCLeSts 1 "invalid" 0 "valid" ;
VAL_ 77 SWCRiSts 1 "invalid" 0 "valid" ;
VAL_ 77 SWCRi_BarBtmPressed 1 "Pressed" 0 "Not Pressed" ;
VAL_ 77 SWCRi_BarTopPressed 1 "Pressed" 0 "Not Pressed" ;
VAL_ 77 SWCRi_BarTouch 3 "Reserved" 2 "Reserved" 1 "finger on touch bar" 0 "NO TOUCH" ;
VAL_ 1322 PN10_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN11_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN12_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN13_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN14_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN15_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN16_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN17_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN18_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN19_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN1_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN20_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN21_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN22_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN23_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN24_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN25_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN26_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN27_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN28_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN29_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN2_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN30_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN31_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN32_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN33_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN34_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN35_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN36_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN37_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN38_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN39_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN3_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN40_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN41_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN42_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN43_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN44_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN45_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN46_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN47_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN48_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN4_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN5_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN6_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN7_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN8_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 PN9_SWC 1 "Awake" 0 "Not awake" ;
VAL_ 1322 RepMsgReq_SWC 1 "Repeat message request" 0 "No repeat message request" ;
VAL_ 286 VehSpdSts_Rdnt1 1 "Valid" 0 "Inalid" ;
VAL_ 316 DrvState 7 "DRV_ST_INVALID" 2 "DRV_ST_AD" 1 "DRV_ST_MD" 0 "DRV_ST_NONE" ;
VAL_ 316 DrvState_SrvSts 1 "valid" 0 "invalid" ;
VAL_ 316 OperatorState 7 "OPERATOR_ST_INVALID" 3 "OPERATOR_ST_INSIDE" 2 "OPERATOR_ST_AVAILABLE" 1 "OPERATOR_ST_OUTSIDE" 0 "OPERATOR_ST_NONE" ;
VAL_ 316 OperatorState_SrvSts 1 "valid" 0 "invalid" ;
VAL_ 316 VehStateDetailed 15 "VEH_ST_INVALID" 5 "VEH_ST_PWRSWAP" 4 "VEH_ST_CHARGING" 3 "VEH_ST_SWUPDATE" 2 "VEH_ST_DRIVING" 1 "VEH_ST_DRVRDY" 0 "VEH_ST_PARKED" ;
VAL_ 316 VehStateDetailed_SrvSts 1 "valid" 0 "invalid" ;
VAL_ 371 BMSChrgState 7 "Invalid" 6 "Reserved" 5 "Reserved" 4 "Charge processing for thermal keeping" 3 "Charge fault" 2 "Charge complete" 1 "Charge processing" 0 "No charging" ;
VAL_ 371 ComfEna 3 "Invalid" 2 "Reserved" 1 "Comfort enabled" 0 "Comfort not enabled" ;
VAL_ 371 HVBattSOCLowWarn 1 "lamp on" 0 "lamp off" ;
VAL_ 371 ImobEnaReq 3 "Invalid" 2 "Enable AID2" 1 "Enable AID1" 0 "Disable" ;
VAL_ 371 LiSnsrFailSts 1 "Failure" 0 "Normal" ;
VAL_ 371 VCUActGear 7 "Invalid" 6 "Reserved" 5 "Reserved" 4 "Reserved" 3 "Parking" 2 "Reverse" 1 "Drive" 0 "Neutral" ;
VAL_ 371 VCUActGearValid 1 "Invalid" 0 "Vaild" ;
VAL_ 549 HALO_AIR_QUALITY 3 "Reserved" 2 "Bad quality" 1 "Medium quality" 0 "No action" ;
VAL_ 549 HALO_AMBIENT_LIGHT 15 "Ambient light color 15" 14 "Ambient light color 14" 13 "Ambient light color 13" 12 "Ambient light color 12" 11 "Ambient light color 11" 10 "Ambient light color 10" 9 "Ambient light color 9" 8 "Ambient light color 8" 7 "Ambient light color 7" 6 "Ambient light color 6" 5 "Ambient light color 5" 4 "Ambient light color 4" 3 "Ambient light color 3" 2 "Ambient light color 2" 1 "Ambient light color 1" 0 "No action" ;
VAL_ 549 HALO_LISTENING_DIRECTION 7 "Invalid" 6 "Reserved" 5 "Rear Right" 4 "Rear Middle" 3 "Rear Left" 2 "Front Right" 1 "Front Left" 0 "No Action" ;
VAL_ 549 HALO_OBSTACLE_DISTANCE 15 "Distance Level 15" 14 "Distance Level 14" 13 "Distance Level 13" 12 "Distance Level 12" 11 "Distance Level 11" 10 "Distance Level 10" 9 "Distance Level 9" 8 "Distance Level 8" 7 "Distance Level 7" 6 "Distance Level 6" 5 "Distance Level 5" 4 "Distance Level 4" 3 "Distance Level 3" 2 "Distance Level 2" 1 "Distance Level 1" 0 "No action" ;
VAL_ 549 HALO_REMIND_RISK 3 "Both side" 2 "Only right side" 1 "Only left side" 0 "No action" ;
VAL_ 549 HALO_TALKING_DIRECTION 7 "Invalid" 6 "Reserved" 5 "Rear Right" 4 "Rear Middle" 3 "Rear Left" 2 "Front Right" 1 "Front Left" 0 "No Action" ;
VAL_ 549 HALO_WORK_MODE 1 "Dynamic Light" 0 "Static Light" ;
VAL_ 549 HALO_WORK_SELECTOR 255 "Invalid" 32 "Charging" 31 "SideDoorOpenTrunk" 30 "SideDoorOpenRR" 29 "SideDoorOpenRL" 28 "SideDoorOpenFR" 27 "SideDoorOpenFL" 26 "Conversation" 25 "Fatigue warning" 24 "Remind update" 23 "Remind vehicle warning and status report" 22 "New device connected" 21 "Introduce function" 20 "Need filter air" 19 "Self Introduction" 18 "Greet User" 17 "Function not implement" 16 "Silent" 15 "Music sync" 14 "Celebration" 13 "Self timer photo" 12 "Command not understood" 11 "Point center screen" 10 "Notification" 9 "Ongoing call" 8 "Incoming call" 7 "Complete mission" 6 "Processing/Thinking" 5 "Talking" 4 "Acknowledge" 3 "Listening" 2 "Activate voice interaction" 1 "Idle" 0 "No action" ;
VAL_ 549 NOMI_OnOffCmd 3 "Invalid" 2 "Reserved" 1 "Active" 0 "Not active" ;
VAL_ 549 NOMI_VideoSrcReq 3 "Invalid" 2 "Reserved" 1 "Active" 0 "Not active" ;
VAL_ 648 CNSL_I_EnableSwReq 3 "Invalid" 2 "Reserved" 1 "Enable" 0 "Disable" ;
VAL_ 648 CNSL_I_OpModeReq 7 "Reserved" 6 "Reserved" 5 "Reserved" 4 "HMI control" 3 "Call" 2 "MultiMedia" 1 "Global" 0 "No Request" ;
VAL_ 648 CNSL_I_SensReq 3 "Reserved" 2 "High" 1 "Low" 0 "No Request" ;
VAL_ 648 CNSL_I_VibDeltaReq 15 "15 levels" 14 "14 levels" 13 "13 levels" 12 "12 levels" 11 "11 levels" 10 "10 levels" 9 "9 levels" 8 "8 levels" 7 "7 levels" 6 "6 levels" 5 "5 levels" 4 "4 levels" 3 "3 levels" 2 "2 levels" 1 "default level" 0 "No Request" ;
VAL_ 648 CNSL_I_VibFbReq 3 "Reserved" 2 "High" 1 "Low" 0 "No Request" ;
VAL_ 648 CenLockUnlockSts 7 "Invalid" 6 "Reserved" 5 "Reserved" 4 "Reserved" 3 "Reserved" 2 "Driver door unlock only" 1 "Vehicle fully locked" 0 "Vehicle unlocked" ;
VAL_ 648 DoorAjarFrntLeSts 3 "Reserved" 2 "opened" 1 "Closed" 0 "Invalid" ;
VAL_ 648 DoorAjarFrntRiSts 3 "Reserved" 2 "opened" 1 "Closed" 0 "Invalid" ;
VAL_ 648 DoorAjarReLeSts 3 "Reserved" 2 "opened" 1 "Closed" 0 "Invalid" ;
VAL_ 648 DoorAjarReRiSts 3 "Reserved" 2 "opened" 1 "Closed" 0 "Invalid" ;
VAL_ 648 ECOPlusModSts 1 "on" 0 "off" ;
VAL_ 648 ETCPaymentCtrl 3 "reserved" 2 "disable" 1 "enable" 0 "No request" ;
VAL_ 648 FI_R_OnReq 3 "Invalid" 2 "Reserved" 1 "on" 0 "off" ;
VAL_ 648 FI_R_ResetReq 3 "Invalid" 2 "Reserved" 1 "Request" 0 "No request" ;
VAL_ 648 FI_R_VideoSrcReq 3 "Invalid" 2 "Reserved" 1 "Active" 0 "Not active" ;
VAL_ 648 HUD_Cmd 3 "Invalid" 2 "Reserved" 1 "Off" 0 "On" ;
VAL_ 648 HUD_ResetReq 3 "Invalid" 2 "Reserved" 1 "Request" 0 "No request" ;
VAL_ 648 HoodAjarSts 3 "Invalid" 2 "Reserved" 1 "Closed" 0 "Opened" ;
VAL_ 648 ICS_OnReq 3 "Invalid" 2 "Reserved" 1 "Request" 0 "Not Request" ;
VAL_ 648 ICS_ResetReq 3 "Invalid" 2 "Reserved" 1 "Request" 0 "No request" ;
VAL_ 648 ICS_VideoSrcReq 3 "Invalid" 2 "Reserved" 1 "Active" 0 "Not active" ;
VAL_ 648 IC_ResetReq_L 3 "Invalid" 2 "Reserved" 1 "Request" 0 "No request" ;
VAL_ 648 IC_ResetReq_R 3 "Invalid" 2 "Reserved" 1 "Request" 0 "No request" ;
VAL_ 648 IC_VideoSrcReq_L 3 "Invalid" 2 "Reserved" 1 "Active" 0 "Not active" ;
VAL_ 648 IC_VideoSrcReq_R 3 "Invalid" 2 "Reserved" 1 "Active" 0 "Not active" ;
VAL_ 648 LVBattUSts_LFP 3 "Invalid" 2 "Failure" 1 "Degraded mode" 0 "Normal" ;
VAL_ 648 TrAjarSts 3 "Invalid" 2 "Reserved" 1 "Closed" 0 "Opened" ;
VAL_ 747 DriveModType 5 "DM_INDIVIDUAL" 4 "DM_SPORT_PLUS" 3 "DM_SPORT" 2 "DM_COMFORT" 1 "DM_ECO_PLUS" 0 "DM_ECO" ;
VAL_ 747 Hvac2ndAutoSts 1 "Open" 0 "Close" ;
VAL_ 747 Hvac2ndFanDirAutoSts 1 "Open" 0 "Close" ;
VAL_ 747 Hvac2ndFanDirFaceSts 1 "Open" 0 "Close" ;
VAL_ 747 Hvac2ndFanDirFloorSts 1 "Open" 0 "Close" ;
VAL_ 747 Hvac2ndOnOffSts 1 "Open" 0 "Close" ;
VAL_ 747 Hvac3rdAutoSts 1 "Open" 0 "Close" ;
VAL_ 747 Hvac3rdFanDirAutoSts 1 "Open" 0 "Close" ;
VAL_ 747 Hvac3rdFanDirFaceSts 1 "Open" 0 "Close" ;
VAL_ 747 Hvac3rdFanDirFloorSts 1 "Open" 0 "Close" ;
VAL_ 747 Hvac3rdOnOffSts 1 "Open" 0 "Close" ;
VAL_ 747 HvacAcSts 1 "Open" 0 "Close" ;
VAL_ 747 HvacAutoSts 1 "Open" 0 "Close" ;
VAL_ 747 HvacFanDirAutoSts 1 "Open" 0 "Close" ;
VAL_ 747 HvacFanDirFaceSts 1 "Open" 0 "Close" ;
VAL_ 747 HvacFanDirFloorSts 1 "Open" 0 "Close" ;
VAL_ 747 HvacFrntDefogSts 1 "Open" 0 "Close" ;
VAL_ 747 HvacFrntRiAutoSts 1 "Open" 0 "Close" ;
VAL_ 747 HvacFrntRiFanDirAutoSts 1 "Open" 0 "Close" ;
VAL_ 747 HvacFrntRiFanDirFaceSts 1 "Open" 0 "Close" ;
VAL_ 747 HvacFrntRiFanDirFloorSts 1 "Open" 0 "Close" ;
VAL_ 747 HvacFrntRiOnOffSts 1 "Open" 0 "Close" ;
VAL_ 747 HvacMaxAcSts 1 "Open" 0 "Close" ;
VAL_ 747 HvacMaxDefogSts 1 "Open" 0 "Close" ;
VAL_ 747 HvacMaxHtSts 1 "Open" 0 "Close" ;
VAL_ 747 HvacOnOffSts 1 "Open" 0 "Close" ;
VAL_ 747 HvacRearDefogSts 1 "Open" 0 "Close" ;
VAL_ 747 HvacRecSts 2 "OSA" 1 "REC" 0 "Auto" ;
VAL_ 747 SeatOccpFrntLeSts 3 "Invalid" 2 "Reserved" 1 "Occupant" 0 "No occupant" ;
VAL_ 747 SeatOccpFrntRiFail 3 "Invalid" 2 "Reserved" 1 "Failure" 0 "No Failiure" ;
VAL_ 747 SeatOccpt2ndLeSts 3 "Invalid" 2 "Reserved" 1 "Occupant" 0 "No occupant" ;
VAL_ 747 SeatOccpt2ndMidSts 3 "Invalid" 2 "Reserved" 1 "Occupant" 0 "No occupant" ;
VAL_ 747 SeatOccpt2ndRiSts 3 "Invalid" 2 "Reserved" 1 "Occupant" 0 "No occupant" ;
VAL_ 747 SeatOccpt3rdLeSts 3 "Invalid" 2 "Reserved" 1 "Occupant" 0 "No occupant" ;
VAL_ 747 SeatOccpt3rdRiSts 3 "Invalid" 2 "Reserved" 1 "Occupant" 0 "No occupant" ;
VAL_ 747 TailgateSts 1 "Open" 0 "Close" ;
VAL_ 747 VC_SeatNumber 2 " 7 seats" 1 " 6 seats" 0 " 5 seats" ;
VAL_ 747 VC_SuspensionType 1 "coil spring" 0 "air suspension" ;
VAL_ 747 VehicleOnVoltage 1 "Open" 0 "Close" ;
VAL_ 747 WinFrntLePosn 127 "unknow" 126 "reserved" 101 "reserved" 100 "100%" 0 "0%" ;
VAL_ 747 WinFrntRiPosn 127 "unknow" 126 "reserved" 101 "reserved" 100 "100%" 0 "0%" ;
VAL_ 747 WinReLePosn 127 "unknow" 126 "reserved" 101 "reserved" 100 "100%" 0 "0%" ;
VAL_ 747 WinReRiPosn 127 "unknow" 126 "reserved" 101 "reserved" 100 "100%" 0 "0%" ;
VAL_ 781 CDF_SetEPMode 3 "Reserved;" 2 "C;" 1 "B;" 0 "A;" ;
VAL_ 781 CDF_SetEPSwitch 1 "Open" 0 "Close" ;
VAL_ 781 CDF_SetKROKMicType 1 "Handy Mic;" 0 "OnCar Mic;" ;
VAL_ 781 CDF_SetKROKSwitch 1 "Open" 0 "Close" ;
VAL_ 781 CDF_SetMediaFrontMute 1 "Unmute" 0 "Mute" ;
VAL_ 781 CDF_SetMediaPath 3 "7.1.4,48Khz" 2 "5.1,48Khz" 1 "2.0,96Khz" 0 "2.0,48Khz" ;
VAL_ 781 CDF_SetMediaRearMute 1 "Unmute" 0 "Mute" ;
VAL_ 781 CDF_SetMediaSwitch 1 "Open" 0 "Close" ;
VAL_ 781 CDF_SetMediaType 3 "Reserved;" 2 "Game;" 1 "Movie;" 0 "Music;" ;
VAL_ 781 CDF_SetMute 1 "Open" 0 "Close" ;
VAL_ 781 CDF_SetNomiSwitch 1 "Open" 0 "Close" ;
VAL_ 781 CDF_SetNomiType 3 "Reserved;" 2 "C;" 1 "B;" 0 "A;" ;
VAL_ 781 CDF_SetPEQSwitch 1 "Open" 0 "Close" ;
VAL_ 781 CDF_SetPhonePos 3 "Reserved;" 2 "C;" 1 "B;" 0 "A;" ;
VAL_ 781 CDF_SetPhoneSwitch 1 "Open" 0 "Close" ;
VAL_ 781 CDF_SetRNCSwitch 1 "Open" 0 "Close" ;
VAL_ 781 CDF_SetSoundFiled 7 "Reserved;" 6 "Reserved;" 5 "Reserved;" 4 "Surround;" 3 "Theatre;" 2 "Rear Seat;" 1 "Driver;" 0 "All Seat;" ;
VAL_ 781 CDF_SetSoundFiledSwitch 1 "Open" 0 "Close" ;
VAL_ 781 CDF_SetWorkMode 7 "Invalid" 6 "Reserved" 5 "Reserved" 4 "Reserved" 3 "Sleep" 2 "Normal" 1 "Standby" 0 "Ready" ;
VAL_ 946 Mth 15 "Invalid" 14 "Reserved" 13 "Reserved" 12 "December" 11 "November" 10 "October" 9 "September" 8 "August" 7 "July" 6 "June" 5 "May" 4 "April" 3 "March" 2 "February" 1 "January" 0 "Unknown" ;
VAL_ 1162 CDCCmdAction 255 "Invalid" 1 "Do Action" 0 "Action Interruption" ;
VAL_ 1285 PN10_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN11_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN12_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN13_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN14_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN15_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN16_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN17_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN18_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN19_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN1_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN20_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN21_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN22_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN23_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN24_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN25_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN26_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN27_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN28_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN29_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN2_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN30_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN31_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN32_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN33_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN34_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN35_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN36_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN37_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN38_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN39_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN3_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN40_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN41_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN42_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN43_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN44_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN45_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN46_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN47_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN48_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN4_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN5_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN6_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN7_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN8_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 PN9_ZONE_FTM 1 "Awake" 0 "Not awake" ;
VAL_ 1285 RepMsgReq_ZONE_FTM 1 "Repeat message request" 0 "No repeat message request" ;
