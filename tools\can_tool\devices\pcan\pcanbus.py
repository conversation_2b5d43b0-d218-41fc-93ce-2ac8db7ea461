from can import BusABC

from common.LogUtils import logger

# CANFD的消息DLC和消息数据长度对应关系
CANFD_MSG_DLC = {8: 8, 12: 9, 16: 10, 20: 11, 24: 12, 32: 13, 48: 14, 64: 15}
CANFD_MSG_LEN = {8: 8, 9: 12, 10: 16, 11: 20, 12: 24, 13: 32, 14: 48, 15: 64}


class PcanBus(BusABC):
    def __init__(self, channel, ratio, can_type):
        self.channel = channel
        self.ratio = ratio
        self.can_type = can_type
        self.pcan_channel = PCAN_USBBUS1
        self.ratio_dict = {10000: PCAN_BAUD_10K, 20000: PCAN_BAUD_20K, 50000: PCAN_BAUD_50K, 100000: PCAN_BAUD_100K,
                           125000: PCAN_BAUD_125K, 250000: PCAN_BAUD_250K, 500000: PCAN_BAUD_500K,
                           800000: PCAN_BAUD_800K, 1000000: PCAN_BAUD_1M
                           }
        self.status = False
        self.pcan_basic = PCANBasic()
        self.init_device()
        self.is_send_success = True

    def init_device(self):
        result = None
        if self.channel == 0:
            self.pcan_channel = PCAN_USBBUS1
        elif self.channel == 1:
            self.pcan_channel = PCAN_USBBUS2
        if self.can_type == "CAN":
            # 总线初始化
            result = self.pcan_basic.Initialize(self.pcan_channel, self.ratio_dict[self.ratio])
            logger.info("init_device PCAN-USB 总线CAN初始化")
        elif self.can_type == "CANFD":
            # 总线初始化
            bitrate = TPCANBitrateFD(
                b"f_clock_mhz=80, nom_brp=2, nom_tseg1=63, nom_tseg2=16, nom_sjw=16, data_brp=2, data_tseg1=15, data_tseg2=4, data_sjw=4"
            )
            result = self.pcan_basic.InitializeFD(self.pcan_channel, bitrate)
            logger.info("init_device PCAN-USB 总线CANFD初始化")
        self.pcan_basic.SetValue(self.pcan_channel, PCAN_BUSOFF_AUTORESET, 1000000)
        if result != PCAN_ERROR_OK:
            logger.info("init_device PCAN-USB 初始化失败", self.pcan_basic.GetErrorText(result))
            self.status = False
        else:
            logger.info("init_device PCAN-USB 已初始化")
            self.status = True

    def send(self, msg, timeout=None):
        try:
            result = None
            if self.can_type == "CAN":
                # 构建CAN消息
                msgs = TPCANMsg()
                msgs.ID = msg.arbitration_id
                msgs.MSGTYPE = PCAN_MESSAGE_STANDARD
                msgs.LEN = msg.dlc
                for j in range(msg.dlc):
                    msgs.DATA[j] = msg.data[j]
                # 写入CAN消息
                result = self.pcan_basic.Write(self.pcan_channel, msgs)
            elif self.can_type == "CANFD":
                # 构建CANFD消息
                msgs = TPCANMsgFD()
                msgs.ID = msg.arbitration_id
                msgs.MSGTYPE = PCAN_MESSAGE_FD
                if msg.dlc > 8:
                    msgs.DLC = CANFD_MSG_DLC[msg.dlc]
                else:
                    msgs.DLC = msg.dlc
                for j in range(msg.dlc):
                    msgs.DATA[j] = msg.data[j]
                # 写入CANFD消息
                result = self.pcan_basic.WriteFD(self.pcan_channel, msgs)

            if result is not None and result == PCAN_ERROR_OK:
                if self.is_send_success:
                    self.is_send_success = False
                logger.info("消息发送成功 msg: {}".format(msg))
            else:
                error = self.pcan_basic.GetErrorText(result)
                logger.info("send error={}".format(error))
                if self.is_send_success:
                    self.is_send_success = False
        except Exception as e:
            logger.error("send exception: {}".format(str(e.args)))

    def _recv_internal(self, timeout):
        read_result = None
        data = None
        if self.can_type == "CAN":
            read_result = self.pcan_basic.Read(self.pcan_channel)
        elif self.can_type == "CANFD":
            read_result = self.pcan_basic.ReadFD(self.pcan_channel)
        if read_result is not None and read_result[1].ID:
            logger.debug("_recv_internal type: CANFD, id: %s" % (hex(read_result[1].ID)))
            if self.can_type == "CAN":
                data = read_result[1].DATA[:read_result[1].LEN]
            elif self.can_type == "CANFD":
                if read_result[1].DLC >= 8:
                    data = read_result[1].DATA[:CANFD_MSG_LEN[read_result[1].DLC]]

            msg = HW_Message(
                timestamp=read_result[2].value / 1000,
                arbitration_id=read_result[1].ID,
                is_extended_id=False,
                is_remote_frame=False,
                is_error_frame=False,
                channel=1,
                dlc=read_result[1].LEN if self.can_type == "CAN" else read_result[1].DLC,
                data=data,
                is_fd=False if self.can_type == "CAN" else True,
                count=0
            )
            return msg, True
        return None, True

    def close_pcan_channel(self):
        logger.info("close_pcan_channel")
        try:
            status = self.pcan_basic.Uninitialize(self.pcan_channel)
            logger.info("close_pcan_channel status={}".format(status))
        except Exception as e:
            logger.error("close_pcan_channel exception: {}".format(str(e.args)))


if __name__ == '__main__':
    from hw_message import HW_Message
    from PCANBasic import *
    import time

    # 1. 创建 PcanBus 实例
    channel = 1  # 这需要根据你的具体PCAN USB 通道来设置
    ratio = 500000  # 波特率，假设为 500K
    can_type = "CANFD"  # 设置为CAN模式
    # can_type = "CAN"  # 设置为CAN模式
    pcan_bus = PcanBus(channel, ratio, can_type)


    # 2. 构建并发送CAN报文
    # send_msg = Message(arbitration_id=1794, data=[0x02, 0x11, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00],
    #                    is_extended_id=False)
    # pcan_bus.send(send_msg)

    # 3. 监控并打印返回的报文
    def monitor_response(pcan_bus, expected_id, timeout=50):
        start_time = time.time()
        while time.time() - start_time < timeout:
            msg, success = pcan_bus._recv_internal(timeout)
            # print(msg,success)
            if msg:
                print("msg.arbitration_id", msg.arbitration_id)
            if msg and msg.arbitration_id == expected_id:
                print(f"收到期望回复报文： ID={hex(msg.arbitration_id)}, Data={msg.data}")
                return
            # time.sleep(0.01)
        print("超时未收到期望回复")


    # 调用监听函数等待响应
    monitor_response(pcan_bus, 371)

    # 完成后关闭PCAN通道
    pcan_bus.close_pcan_channel()
