<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Dialog</class>
 <widget class="QDialog" name="Dialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1600</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1600</width>
    <height>900</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3" stretch="1,0">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1,1">
     <item>
      <widget class="QGroupBox" name="groupBox">
       <property name="title">
        <string>配置</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <layout class="QFormLayout" name="formLayout">
          <item row="0" column="0">
           <widget class="QLabel" name="label">
            <property name="text">
             <string>电机运动位置：</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_2">
            <property name="text">
             <string>电机按下时间：</string>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="label_3">
            <property name="text">
             <string>循环次数：</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QSpinBox" name="spinBox_repeat">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="maximum">
             <number>999999999</number>
            </property>
            <property name="value">
             <number>10000</number>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QDoubleSpinBox" name="doubleSpinBox_position">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="decimals">
             <number>5</number>
            </property>
            <property name="minimum">
             <double>-9.000000000000000</double>
            </property>
            <property name="maximum">
             <double>0.000000000000000</double>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QDoubleSpinBox" name="doubleSpinBox_press">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="maximum">
             <double>9999999999999.990234375000000</double>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="label_6">
            <property name="text">
             <string>电机抬起时间：</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QDoubleSpinBox" name="doubleSpinBox_up">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="groupBox_2">
       <property name="title">
        <string>控制</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <widget class="QPushButton" name="pushButton_start">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="text">
           <string>开始</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton_pause">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="text">
           <string>暂停</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton_end">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Minimum" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="text">
           <string>结束</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_3">
     <property name="title">
      <string>进度</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <layout class="QFormLayout" name="formLayout_2">
        <item row="0" column="0">
         <widget class="QLabel" name="label_4">
          <property name="text">
           <string>工作状态：</string>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="label_5">
          <property name="text">
           <string>按压次数：</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLineEdit" name="lineEdit_status">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>45</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QLineEdit" name="lineEdit_press">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>45</height>
           </size>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QProgressBar" name="progressBar">
        <property name="value">
         <number>24</number>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
