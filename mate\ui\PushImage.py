# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'PushImage.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_PushImgForm(object):
    def setupUi(self, PushImgForm):
        PushImgForm.setObjectName("PushImgForm")
        PushImgForm.resize(1600, 900)
        PushImgForm.setMinimumSize(QtCore.QSize(1600, 900))
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(PushImgForm)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout()
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.toolButtonOpen = QtWidgets.QToolButton(PushImgForm)
        self.toolButtonOpen.setObjectName("toolButtonOpen")
        self.horizontalLayout.addWidget(self.toolButtonOpen)
        self.lineEdit = QtWidgets.QLineEdit(PushImgForm)
        self.lineEdit.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit.setObjectName("lineEdit")
        self.horizontalLayout.addWidget(self.lineEdit)
        self.pushButtonPushImg = QtWidgets.QPushButton(PushImgForm)
        self.pushButtonPushImg.setObjectName("pushButtonPushImg")
        self.horizontalLayout.addWidget(self.pushButtonPushImg)
        spacerItem = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem)
        self.pushButtonPushVideo = QtWidgets.QPushButton(PushImgForm)
        self.pushButtonPushVideo.setObjectName("pushButtonPushVideo")
        self.horizontalLayout.addWidget(self.pushButtonPushVideo)
        spacerItem1 = QtWidgets.QSpacerItem(20, 20, QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Minimum)
        self.horizontalLayout.addItem(spacerItem1)
        self.pushButtonWB = QtWidgets.QPushButton(PushImgForm)
        self.pushButtonWB.setObjectName("pushButtonWB")
        self.horizontalLayout.addWidget(self.pushButtonWB)
        self.verticalLayout_4.addLayout(self.horizontalLayout)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.labelImg = QtWidgets.QLabel(PushImgForm)
        font = QtGui.QFont()
        font.setPointSize(40)
        self.labelImg.setFont(font)
        self.labelImg.setAlignment(QtCore.Qt.AlignCenter)
        self.labelImg.setObjectName("labelImg")
        self.horizontalLayout_3.addWidget(self.labelImg)
        self.verticalLayout_3 = QtWidgets.QVBoxLayout()
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.groupBox = QtWidgets.QGroupBox(PushImgForm)
        self.groupBox.setObjectName("groupBox")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.groupBox)
        self.verticalLayout.setObjectName("verticalLayout")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.spinBox = QtWidgets.QSpinBox(self.groupBox)
        self.spinBox.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox.setMinimum(-110)
        self.spinBox.setMaximum(360)
        self.spinBox.setProperty("value", 40)
        self.spinBox.setObjectName("spinBox")
        self.horizontalLayout_2.addWidget(self.spinBox)
        self.pushButtonRotate = QtWidgets.QPushButton(self.groupBox)
        self.pushButtonRotate.setObjectName("pushButtonRotate")
        self.horizontalLayout_2.addWidget(self.pushButtonRotate)
        self.verticalLayout.addLayout(self.horizontalLayout_2)
        self.verticalLayout_3.addWidget(self.groupBox)
        self.groupBox_2 = QtWidgets.QGroupBox(PushImgForm)
        self.groupBox_2.setObjectName("groupBox_2")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.groupBox_2)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setObjectName("gridLayout")
        self.label_2 = QtWidgets.QLabel(self.groupBox_2)
        self.label_2.setObjectName("label_2")
        self.gridLayout.addWidget(self.label_2, 0, 0, 1, 1)
        self.spinBoxYaw = QtWidgets.QSpinBox(self.groupBox_2)
        self.spinBoxYaw.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBoxYaw.setMinimum(-177)
        self.spinBoxYaw.setMaximum(177)
        self.spinBoxYaw.setObjectName("spinBoxYaw")
        self.gridLayout.addWidget(self.spinBoxYaw, 0, 1, 1, 1)
        self.label_3 = QtWidgets.QLabel(self.groupBox_2)
        self.label_3.setObjectName("label_3")
        self.gridLayout.addWidget(self.label_3, 1, 0, 1, 1)
        self.spinBoxPitch = QtWidgets.QSpinBox(self.groupBox_2)
        self.spinBoxPitch.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBoxPitch.setMinimum(0)
        self.spinBoxPitch.setMaximum(360)
        self.spinBoxPitch.setObjectName("spinBoxPitch")
        self.gridLayout.addWidget(self.spinBoxPitch, 1, 1, 1, 1)
        self.gridLayout.setRowMinimumHeight(0, 45)
        self.gridLayout.setRowMinimumHeight(1, 45)
        self.verticalLayout_2.addLayout(self.gridLayout)
        self.pushButtonMate3Rotate = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButtonMate3Rotate.setObjectName("pushButtonMate3Rotate")
        self.verticalLayout_2.addWidget(self.pushButtonMate3Rotate)
        self.verticalLayout_3.addWidget(self.groupBox_2)
        spacerItem2 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_3.addItem(spacerItem2)
        self.horizontalLayout_3.addLayout(self.verticalLayout_3)
        self.verticalLayout_4.addLayout(self.horizontalLayout_3)
        self.verticalLayout_5.addLayout(self.verticalLayout_4)

        self.retranslateUi(PushImgForm)
        QtCore.QMetaObject.connectSlotsByName(PushImgForm)

    def retranslateUi(self, PushImgForm):
        _translate = QtCore.QCoreApplication.translate
        PushImgForm.setWindowTitle(_translate("PushImgForm", "Form"))
        self.toolButtonOpen.setText(_translate("PushImgForm", "..."))
        self.pushButtonPushImg.setText(_translate("PushImgForm", "推图"))
        self.pushButtonPushVideo.setText(_translate("PushImgForm", "推视频"))
        self.pushButtonWB.setText(_translate("PushImgForm", "黑白切换"))
        self.labelImg.setText(_translate("PushImgForm", "拖拽图片到此处"))
        self.groupBox.setTitle(_translate("PushImgForm", "mate2转动控制"))
        self.pushButtonRotate.setText(_translate("PushImgForm", "执行"))
        self.groupBox_2.setTitle(_translate("PushImgForm", "mate3转动控制"))
        self.label_2.setText(_translate("PushImgForm", "Yaw位置"))
        self.label_3.setText(_translate("PushImgForm", "Pitch位置"))
        self.pushButtonMate3Rotate.setText(_translate("PushImgForm", "执行"))
