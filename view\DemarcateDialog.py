import cv2
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QImage, QPixmap
from PyQt5.QtWidgets import QDialog

from common.LogUtils import logger
from utils.SignalsManager import signals_manager
from ui.ROI import Ui_Dialog
from vision.CameraManager import camera_manager


class DemarcateDialog(QDialog, Ui_Dialog):

    def __init__(self, video_list_calibrate):
        super().__init__()
        self.setupUi(self)
        # 设置对 话框标题
        self.setWindowTitle("请选择视觉测试区域")
        self.resize(800, 600)
        self.rect = None
        self.gray_frame = None
        self.brightness_origin = 0
        signals_manager.update_calibrate_frame.connect(self.update_calibrate_frame)
        self.refresh_button.clicked.connect(self.refresh)
        self.enter_button.clicked.connect(self.enter_submit)
        self.close_button.clicked.connect(self.enter_cancel)
        self.video_list_calibrate = video_list_calibrate
        self.setWindowFlags(Qt.Window | Qt.CustomizeWindowHint | Qt.WindowMaximizeButtonHint)
        self.refresh_button.setFixedSize(150, 60)
        self.enter_button.setFixedSize(150, 60)
        self.close_button.setFixedSize(150, 60)
        self.refresh()

    def update_calibrate_frame(self, pixmap):
        self.labelRoi.setPixmap(pixmap)

    def refresh(self):
        self.video_list_calibrate.queue.clear()
        frame = self.video_list_calibrate.get()
        self.gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        image = QImage(frame, frame.shape[1], frame.shape[0], QImage.Format_BGR888)
        pixmap = QPixmap.fromImage(image)
        signals_manager.update_calibrate_frame.emit(pixmap)

    def enter_submit(self):
        from case.CaseManager import case_manager
        logger.info(f"enter_submit rect={self.labelRoi.rect}")
        try:
            self.rect = self.labelRoi.rect
            rects = (self.rect.x(), self.rect.y(), self.rect.width(), self.rect.height())
            frame_cropped = self.gray_frame[rects[1]:rects[1] + rects[3], rects[0]:rects[0] + rects[2]]
            self.brightness_origin = int(frame_cropped.mean())
            logger.error(f"enter_submit brightness_origin={self.brightness_origin}")
            self.close()
            camera_manager.calibrate_flag = False
            case_manager.demarcated = True
        except Exception as e:
            logger.error(f"enter_submit exception: {str(e.args)}")
            camera_manager.calibrate_flag = False
            case_manager.demarcated = False

    def enter_cancel(self):
        logger.info(f"enter_cancel")
        camera_manager.calibrate_flag = False
        self.close()
