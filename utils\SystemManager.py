# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/9/14 16:59
@Desc   : 系统管理模块
"""
import os
import threading

import psutil

from case.CaseManager import case_manager, CaseStatus
from common.AppConfig import app_config
from common.LogUtils import logger
from fs_manager.FSManager import fs_manager
from utils import get_workspace_disk_partition
from utils.ProjectManager import project_manager
from utils.SignalsManager import signals_manager


class SystemManager:

    def __init__(self):
        super().__init__()
        self.detect_disk_timer = None

    def check_disk_free_usage(self, detect_interval=3600, lowest_storage=10):
        self.detect_disk_timer = threading.Timer(interval=detect_interval,
                                                 function=self.check_disk_free_usage,
                                                 args=(detect_interval, lowest_storage))
        self.detect_disk_timer.start()
        work_disk = get_workspace_disk_partition()
        if work_disk is None:
            work_disk = os.path.dirname(app_config.work_folder)
        disk = psutil.disk_usage(work_disk)
        disk_free = round(disk.free / (1024 * 1024 * 1024), 2)
        if disk_free < lowest_storage:
            logger.warning(f"check_disk_free_usage disk free is {disk_free}GB lower than {lowest_storage}GB")
            tester = project_manager.get_test_user()
            project_number = project_manager.get_test_plan_project_number()
            machine_number = project_manager.get_machine_number()
            tip = f"工作目录磁盘剩余空间小于{lowest_storage}GB，请及时清理磁盘无用资源，保证测试正常进行"
            if not project_number.__eq__(""):
                fs_manager.post_machine_storage_alarm_msg(project_number=project_number,
                                                          machine=machine_number,
                                                          tester=tester,
                                                          cwd=app_config.work_folder,
                                                          remain_capacity=f"{disk_free}GB",
                                                          tip=tip)

        if disk_free == 0:
            logger.warning(f"check_disk_free_usage disk free is {disk_free}GB")
            tester = project_manager.get_test_user()
            project_number = project_manager.get_test_plan_project_number()
            machine_number = project_manager.get_machine_number()
            tip = f"工作目录磁盘剩余空间为0GB，自动化测试程序已被主动停止，请清理磁盘后再次测试"
            if not project_number.__eq__("") and case_manager.status == CaseStatus.TESTING:
                fs_manager.post_machine_storage_alarm_msg(project_number=project_number,
                                                          machine=machine_number,
                                                          tester=tester,
                                                          cwd=app_config.work_folder,
                                                          remain_capacity=f"{disk_free}GB",
                                                          tip=tip)
            # 本地工作目录磁盘为0主动停止测试
            signals_manager.operate_test_status.emit(False)

    def stop_check_disk_disk_usage(self):
        logger.info("stop_check_disk_disk_usage")
        if self.detect_disk_timer is not None:
            self.detect_disk_timer.cancel()
            self.detect_disk_timer = None


system_manager: SystemManager = SystemManager()
