# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'ElevationAngle.ui'
#
# Created by: PyQt5 UI code generator 5.15.0
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_elevationForm(object):
    def setupUi(self, elevationForm):
        elevationForm.setObjectName("elevationForm")
        elevationForm.resize(1600, 900)
        elevationForm.setMinimumSize(QtCore.QSize(1600, 900))
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(elevationForm)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.horizontalLayout_2 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_2.setObjectName("horizontalLayout_2")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout()
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.verticalLayout = QtWidgets.QVBoxLayout()
        self.verticalLayout.setObjectName("verticalLayout")
        self.lcdNumber = QtWidgets.QLCDNumber(elevationForm)
        self.lcdNumber.setMinimumSize(QtCore.QSize(0, 200))
        font = QtGui.QFont()
        font.setPointSize(9)
        self.lcdNumber.setFont(font)
        self.lcdNumber.setObjectName("lcdNumber")
        self.verticalLayout.addWidget(self.lcdNumber)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.pushButtonStart = QtWidgets.QPushButton(elevationForm)
        self.pushButtonStart.setObjectName("pushButtonStart")
        self.horizontalLayout.addWidget(self.pushButtonStart)
        self.pushButtonStop = QtWidgets.QPushButton(elevationForm)
        self.pushButtonStop.setObjectName("pushButtonStop")
        self.horizontalLayout.addWidget(self.pushButtonStop)
        self.verticalLayout.addLayout(self.horizontalLayout)
        self.verticalLayout_2.addLayout(self.verticalLayout)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_2.addItem(spacerItem)
        self.horizontalLayout_2.addLayout(self.verticalLayout_2)
        self.widget_display = CustomPlotWidget(elevationForm)
        self.widget_display.setStyleSheet("background-color: rgb(168, 168, 168);")
        self.widget_display.setObjectName("widget_display")
        self.horizontalLayout_2.addWidget(self.widget_display)
        self.horizontalLayout_2.setStretch(1, 1)
        self.verticalLayout_3.addLayout(self.horizontalLayout_2)

        self.retranslateUi(elevationForm)
        QtCore.QMetaObject.connectSlotsByName(elevationForm)

    def retranslateUi(self, elevationForm):
        _translate = QtCore.QCoreApplication.translate
        elevationForm.setWindowTitle(_translate("elevationForm", "Form"))
        self.pushButtonStart.setText(_translate("elevationForm", "测量"))
        self.pushButtonStop.setText(_translate("elevationForm", "停止"))
from ui.canvas import CustomPlotWidget
