class CrcCheck(object):
    __crcTable = {}
    __POLYNOMIAL = 0x1021
    __INITIAL_REMAINDER = 0xFFFF
    __FINAL_XOR_VALUE = 0x0000
    __WIDTH = 16
    __TOP_BIT = (1 << (__WIDTH - 1))

    def __init__(self):
        self.__crc_init()

    def __crc_init(self):
        shift = self.__WIDTH - 8
        for step in range(0, 256):
            remainder = step << shift
            for bit in range(8, 0, -1):
                if remainder & self.__TOP_BIT:
                    remainder = ((remainder << 1) & self.__INITIAL_REMAINDER) ^ self.__POLYNOMIAL
                else:
                    remainder = remainder << 1
            self.__crcTable[step] = remainder

    def print_tab(self):
        print("unsigned short crc16_tab = {")
        cnt = 0
        for step in range(0, 256):
            if 255 == step:
                print(" 0x%04x" % self.__crcTable[step])
                break
            print(" 0x%04x" % self.__crcTable[step], end=',')
            cnt += 1
            if 10 == cnt:
                cnt = 0
                print("\r\n", end='')

    def calc(self, message, n_bytes):
        remainder = 0xFFFF
        byte = 0
        while byte < n_bytes:
            data = message[byte] ^ (remainder >> (self.__WIDTH - 8))
            remainder = self.__crcTable[data] ^ ((remainder << 8) & 0xFFFF)
            byte = byte + 1

        return remainder


if __name__ == '__main__':
    crc16_obj = CrcCheck()
    crc16_obj.print_tab()

    msg = [10, 20, 30, 40, 50]
    crc_val = crc16_obj.calc(msg, 5)
    print("crc_val = 0x%x" % crc_val)
