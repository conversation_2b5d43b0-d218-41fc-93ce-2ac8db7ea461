<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1600</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1600</width>
    <height>900</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3" stretch="1,20">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="1,1">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,5,1,2">
       <item>
        <widget class="QPushButton" name="dbc_choice_button">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>45</height>
          </size>
         </property>
         <property name="text">
          <string>选择DBC</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="dbc_path">
         <property name="text">
          <string>请加载DBC文件...</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="dbc_load_button">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>45</height>
          </size>
         </property>
         <property name="text">
          <string>加载</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QComboBox" name="connect_type">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>45</height>
          </size>
         </property>
         <item>
          <property name="text">
           <string>请选择通讯方式</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>PCAN</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>周立功</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>CANOE</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>LIN</string>
          </property>
         </item>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1,1,1,1,1,1,1">
       <item>
        <widget class="QPushButton" name="can_start_button">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>45</height>
          </size>
         </property>
         <property name="text">
          <string>开始</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="can_stop_button">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>45</height>
          </size>
         </property>
         <property name="text">
          <string>暂停</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="can_realsave_button">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>45</height>
          </size>
         </property>
         <property name="text">
          <string>实时保存开始</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="can_realsaveSave_stop_button">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>45</height>
          </size>
         </property>
         <property name="text">
          <string>实时保存停止</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="can_clear_button">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>45</height>
          </size>
         </property>
         <property name="text">
          <string>清空</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="1,1">
     <item>
      <widget class="QGroupBox" name="groupBox">
       <property name="title">
        <string>DBC视图</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <widget class="QTreeWidget" name="dbc_treeWidget">
          <column>
           <property name="text">
            <string>次数</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>时间</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>间隔时间</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>消息名</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>帧ID</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>帧类型</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>方向</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>长度</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>数据</string>
           </property>
          </column>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="groupBox_2">
       <property name="title">
        <string>CAN视图</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="3,1,2,0,0,1,4">
          <item>
           <widget class="QLineEdit" name="can_time_select">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="can_origin_select">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="can_ID_select">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="can_type_select">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <item>
             <property name="text">
              <string>全部</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>CAN</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>CANFD</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>CANFD加速</string>
             </property>
            </item>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="can_direction_select">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <item>
             <property name="text">
              <string>全部</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>Tx</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>Rx</string>
             </property>
            </item>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="can_lenght_select">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="can_data_select">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>45</height>
             </size>
            </property>
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QTableWidget" name="can_tableWidget">
          <column>
           <property name="text">
            <string>次数</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>时间标识</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>数据时间差</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>源通道</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>帧ID</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>CAN类型</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>方向</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>长度</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>数据</string>
           </property>
          </column>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
