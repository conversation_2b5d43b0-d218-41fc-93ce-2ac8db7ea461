"""
user: Created by jid on 2019/6/12.
email: <EMAIL>
description:通信联调模块
"""
from PyQt5.QtCore import QObject, pyqtSignal

from simbox_tools.ConfigManger import NomiConfig



class CommunicationManager(QObject):
    can_state_signal = pyqtSignal(object)
    calibrate_error_state_signal = pyqtSignal()
    calibrate_state_signal = pyqtSignal()
    animation_frame_signal = pyqtSignal(object)
    animation_state_signal = pyqtSignal(object)
    simbox_rotate_signal = pyqtSignal(object)
    recv_diag_msg_signal = pyqtSignal(object)
    timeout_signal = pyqtSignal()

    def __init__(self):
        super().__init__()
        self._log_tag = "CommunicationManager"
        self._simbox_connect_state = False
        self._CAN_connect_state = NomiConfig.CANDisconnected
        self._connected_port = ""
        self._simbox_version = ""
        self._hardware_version = ""
        self._software_version = ""
        self._ecu_serial = ""
        self._request_brightness_percent = 100
        self._response_brightness_percent = 0
        self._response_calibrate_error_state = NomiConfig.CalibrateNormal
        # calibrate_state : 3 "Invalid" 2 "Reserved" 1 "Active" 0 "Not active"
        self._request_calibrate_state = NomiConfig.CalibrateNotActive
        self._response_calibrate_state = NomiConfig.CalibrateNotActivated
        self._animation_state = NomiConfig.AnimationInit
        self._animation_frame = 0
        self._yaw_real_position = 0
        self._pitch_real_position = 0
        self._current_brightness = 0
        self._pitch_rotate_state = NomiConfig.KineticRotateStateActivated
        self._yaw_rotate_state = NomiConfig.KineticRotateStateActivated
        self._combine_video_state = True
        self._drop_complement_frame_state = False

    def get_simbox_connect_state(self):
        return self._simbox_connect_state

    def set_simbox_connect_state(self, state):
        self._simbox_connect_state = state

    def get_CAN_connect_state(self):
        return self._CAN_connect_state

    def set_CAN_connect_state(self, state):
        self._CAN_connect_state = state

    def get_connected_port(self):
        return self._connected_port

    def set_connected_port(self, port):
        self._connected_port = port

    def get_simbox_version(self):
        return self._simbox_version

    def set_simbox_version(self, version):
        self._simbox_version = version

    def get_hardware_version(self):
        return self._hardware_version

    def set_hardware_version(self, version):
        self._hardware_version = version

    def get_software_version(self):
        return self._software_version

    def set_software_version(self, version):
        self._software_version = version

    def get_ecu_serial(self):
        return self._ecu_serial

    def set_ecu_serial(self, serial):
        self._ecu_serial = serial

    def get_request_brightness_percent(self):
        return self._request_brightness_percent

    def set_request_brightness_percent(self, percent):
        self._request_brightness_percent = percent

    def get_response_brightness_percent(self):
        return self._response_brightness_percent

    def set_response_brightness_percent(self, percent):
        self._response_brightness_percent = percent

    def get_response_calibrate_error_state(self):
        return self._response_calibrate_error_state

    def set_response_calibrate_error_state(self, state):
        self._response_calibrate_error_state = state

    def get_request_calibrate_state(self):
        return self._request_calibrate_state

    def set_request_calibrate_state(self, state):
        self._request_calibrate_state = state

    def get_response_calibrate_state(self):
        return self._response_calibrate_state

    def set_response_calibrate_state(self, state):
        self._response_calibrate_state = state

    def get_animation_state(self):
        return self._animation_state

    def set_animation_state(self, state):
        self._animation_state = state
        self.animation_state_signal.emit(state)

    def get_animation_frame(self):
        return self._animation_frame

    def set_animation_frame(self, frame):
        self._animation_frame = frame
        self.animation_frame_signal.emit(frame)

    def get_yaw_real_position(self):
        return self._yaw_real_position

    def set_yaw_real_position(self, position):
        self._yaw_real_position = position

    def get_pitch_real_position(self):
        return self._pitch_real_position

    def set_pitch_real_position(self, position):
        self._pitch_real_position = position

    def get_current_brightness(self):
        return self._current_brightness

    def set_current_brightness(self, brightness):
        self._current_brightness = brightness

    @property
    def pitch_rotate_state(self):
        return self._pitch_rotate_state

    @pitch_rotate_state.setter
    def pitch_rotate_state(self, state):
        self._pitch_rotate_state = state

    @property
    def yaw_rotate_state(self):
        return self._yaw_rotate_state

    @yaw_rotate_state.setter
    def yaw_rotate_state(self, state):
        self._yaw_rotate_state = state

    def get_combine_video_state(self):
        return self._combine_video_state

    def set_combine_video_state(self, state):
        self._combine_video_state = state

    def get_drop_complement_frame_state(self):
        return self._drop_complement_frame_state

    def set_drop_complement_frame_state(self, state):
        self._drop_complement_frame_state = state


communication_manager = CommunicationManager()
