import threading
import time

import serial

from common.LogUtils import logger
from utils.SignalsManager import signals_manager


class AutoKeyPress:

    def __init__(self):
        super().__init__()
        self.serial = None
        self.custom_cycle_timer = None
        self.stop_custom_cycle = False
        self.repeat = 0
        self.press_counter = 0
        self.position_offset = 0

    def open_device(self, port, baudrate=115200):
        logger.info("open_device port={}, baudrate={}".format(port, baudrate))
        state = False
        try:
            self.serial = serial.Serial()
            self.serial.port = port
            self.serial.baudrate = baudrate
            self.serial.open()
            state = self.serial.is_open
        except Exception as e:
            logger.error("open_device exception: {}".format(str(e.args)))
        logger.info("open_device state={}".format(state))
        return state

    def switch_down(self):
        logger.info("switch_down")
        data = [0x55, 0x01, 0x00, 0x00, 0x01]
        try:
            self.serial.write(bytes(data))
        except Exception as e:
            logger.error("switch_down exception: {}".format(str(e.args)))

    def switch_up(self):
        logger.info("switch_up")
        data = [0x55, 0x00, 0x00, 0x00, 0x00]
        try:
            self.serial.write(bytes(data))
        except Exception as e:
            logger.error("switch_down exception: {}".format(str(e.args)))

    def close_device(self):
        logger.info("close_device")
        if self.serial is not None:
            self.serial.close()
        return True

    def start_en(self):
        logger.info("start_en")
        if self.serial is None:
            return
        try:
            self.serial.write("en \r\n".encode())
        except Exception as e:
            logger.error("start_en exception: {}".format(str(e.args)))

    def set_mode(self, mode=8):
        logger.info("set_mode mode={}".format(mode))
        if self.serial is None:
            return
        try:
            self.serial.write("OPMODE {} \r\n".format(mode).encode())
        except Exception as e:
            logger.error("set_mode exception: {}".format(str(e.args)))

    def move_inc_dist1(self, dist1="20000"):
        logger.info("move_inc_dist1")
        if self.serial is None:
            return
        try:
            self.serial.write("moveincdist1 {} \r\n".format(dist1).encode())
        except Exception as e:
            logger.error("move_inc_dist1 exception: {}".format(str(e.args)))

    def move_inc_speed1(self):
        logger.info("move_inc_speed1")
        if self.serial is None:
            return
        try:
            self.serial.write("moveincspeed1 5000 \r\n".encode())
        except Exception as e:
            logger.error("move_inc_speed1 exception: {}".format(str(e.args)))

    def move_inc_dist2(self):
        logger.info("move_inc_dist2")
        if self.serial is None:
            return
        try:
            self.serial.write("moveincdist2 -20000 \r\n".encode())
        except Exception as e:
            logger.error("move_inc_dist2 exception: {}".format(str(e.args)))

    def move_inc_speed2(self):
        logger.info("move_inc_speed2")
        if self.serial is None:
            return
        try:
            self.serial.write("moveincspeed2 5000 \r\n".encode())
        except Exception as e:
            logger.error("move_inc_speed2 exception: {}".format(str(e.args)))

    def move_inc_delay(self, delay="500"):
        logger.info("move_inc_delay")
        if self.serial is None:
            return
        try:
            self.serial.write("moveincdelay {} \r\n".format(delay).encode())
        except Exception as e:
            logger.error("move_inc_delay exception: {}".format(str(e.args)))

    def move_inc_counter(self, counter="20000"):
        logger.info("move_inc_counter")
        if self.serial is None:
            return
        try:
            self.serial.write("moveinccounter {} \r\n".format(counter).encode())
        except Exception as e:
            logger.error("move_inc_counter exception: {}".format(str(e.args)))

    def move_abs_position(self, position="20000", speed="2000"):
        logger.info("move_abs_position position={}, speed={}".format(position, speed))
        if self.serial is None:
            return
        try:
            self.serial.write("MOVEABS {} {} \r\n".format(position, speed).encode())
        except Exception as e:
            logger.error("move_abs_position exception: {}".format(str(e.args)))

    def move_inc_position(self, position="20000", speed="2000"):
        logger.info("move_inc_position position={}, speed={}".format(position, speed))
        if self.serial is None:
            return
        try:
            self.serial.write("MOVEINC {} {} \r\n".format(position, speed).encode())
        except Exception as e:
            logger.error("move_inc_position exception: {}".format(str(e.args)))

    def set_acc(self, acc="5000"):
        logger.info("set_acc acc={}".format(acc))
        if self.serial is None:
            return
        try:
            self.serial.write("acc {} \r\n".format(acc).encode())
        except Exception as e:
            logger.error("set_acc exception: {}".format(str(e.args)))

    def set_dec(self, dec="5000"):
        logger.info("set_dec dec={}".format(dec))
        if self.serial is None:
            return
        try:
            self.serial.write("dec {} \r\n".format(dec).encode())
        except Exception as e:
            logger.error("set_dec exception: {}".format(str(e.args)))

    def start_mb(self):
        logger.info("start_mb")
        if self.serial is None:
            return
        try:
            self.serial.write("mb \r\n".encode())
        except Exception as e:
            logger.error("start_mb exception: {}".format(str(e.args)))

    def stop_mb(self):
        logger.info("stop_mb")
        if self.serial is None:
            return
        try:
            self.serial.write("k \r\n".encode())
        except Exception as e:
            logger.error("stop_mb exception: {}".format(str(e.args)))

    def start_auto_cycle_press(self, delay="500", counter="20000"):
        logger.info("start_auto_cycle_press")
        self.stop_mb()
        time.sleep(0.1)
        self.start_en()
        time.sleep(0.1)
        self.move_inc_dist1()
        time.sleep(0.1)
        self.move_inc_speed1()
        time.sleep(0.1)
        self.move_inc_dist2()
        time.sleep(0.1)
        self.move_inc_speed2()
        time.sleep(0.1)
        self.move_inc_delay(delay)
        time.sleep(0.1)
        self.move_inc_counter(counter)
        time.sleep(0.1)
        self.start_mb()

    def start_custom_cycle_press(self, repeat=20000, position="20000", press_time="0.2", up_time="0.2", speed="2000",
                                 acc="5000"):
        logger.info("start_custom_cycle_press repeat={}, position={}, press_time={}, up_time={}, speed={}, acc={}"
                     .format(repeat, position, press_time, up_time, speed, acc))
        if self.stop_custom_cycle:
            return
        self.repeat = repeat

        # 电机按下
        self.start_en()
        self.set_mode(8)
        self.move_inc_position(str(int(position) - self.position_offset), speed)
        time.sleep(0.1)
        self.set_acc(acc)

        # 设置按下持续时间
        time.sleep(float(press_time))
        # 设置缓冲距离防止每次回零位撞击(回到距离零位2000的地方)
        self.position_offset = 2000

        # 电机抬起
        self.start_en()
        self.set_mode(8)
        self.move_inc_position(str(int(position) * -1 + self.position_offset), speed)
        time.sleep(0.1)
        self.set_acc(acc)

        # 设置抬起持续时间
        time.sleep(float(up_time))

        self.press_counter += 1
        signals_manager.press_counter_signal.emit(self.press_counter)

        if self.press_counter == self.repeat:
            self.stop_custom_cycle_press()

        threading.Timer(interval=0.5,
                        function=self.start_custom_cycle_press,
                        args=(repeat, position, press_time, up_time, speed, acc)).start()

    def reset_custom_params(self):
        self.stop_mb()
        self.stop_custom_cycle = False
        self.press_counter = 0
        self.position_offset = 0

    def stop_custom_cycle_press(self):
        logger.info("stop_custom_cycle_press")
        self.stop_mb()
        self.stop_custom_cycle = True


auto_key_press: AutoKeyPress = AutoKeyPress()
