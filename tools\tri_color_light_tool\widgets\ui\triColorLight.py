# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'triColorLight.ui'
#
# Created by: PyQt5 UI code generator 5.15.6
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(1600, 900)
        Form.setMinimumSize(QtCore.QSize(1600, 900))
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(Form)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.groupBox = QtWidgets.QGroupBox(Form)
        self.groupBox.setTitle("")
        self.groupBox.setObjectName("groupBox")
        self.horizontalLayout = QtWidgets.QHBoxLayout(self.groupBox)
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.groupBox_2 = QtWidgets.QGroupBox(self.groupBox)
        self.groupBox_2.setObjectName("groupBox_2")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.groupBox_2)
        self.verticalLayout.setObjectName("verticalLayout")
        self.pushButton_red_open = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_red_open.setObjectName("pushButton_red_open")
        self.verticalLayout.addWidget(self.pushButton_red_open)
        self.pushButton_red_close = QtWidgets.QPushButton(self.groupBox_2)
        self.pushButton_red_close.setObjectName("pushButton_red_close")
        self.verticalLayout.addWidget(self.pushButton_red_close)
        spacerItem = QtWidgets.QSpacerItem(20, 21, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout.addItem(spacerItem)
        self.horizontalLayout.addWidget(self.groupBox_2)
        self.groupBox_3 = QtWidgets.QGroupBox(self.groupBox)
        self.groupBox_3.setObjectName("groupBox_3")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.groupBox_3)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.pushButton_green_open = QtWidgets.QPushButton(self.groupBox_3)
        self.pushButton_green_open.setObjectName("pushButton_green_open")
        self.verticalLayout_2.addWidget(self.pushButton_green_open)
        self.pushButton_green_close = QtWidgets.QPushButton(self.groupBox_3)
        self.pushButton_green_close.setObjectName("pushButton_green_close")
        self.verticalLayout_2.addWidget(self.pushButton_green_close)
        spacerItem1 = QtWidgets.QSpacerItem(20, 21, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_2.addItem(spacerItem1)
        self.horizontalLayout.addWidget(self.groupBox_3)
        self.groupBox_4 = QtWidgets.QGroupBox(self.groupBox)
        self.groupBox_4.setObjectName("groupBox_4")
        self.verticalLayout_3 = QtWidgets.QVBoxLayout(self.groupBox_4)
        self.verticalLayout_3.setObjectName("verticalLayout_3")
        self.pushButton_yellow_open = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_yellow_open.setObjectName("pushButton_yellow_open")
        self.verticalLayout_3.addWidget(self.pushButton_yellow_open)
        self.pushButton_yellow_close = QtWidgets.QPushButton(self.groupBox_4)
        self.pushButton_yellow_close.setObjectName("pushButton_yellow_close")
        self.verticalLayout_3.addWidget(self.pushButton_yellow_close)
        spacerItem2 = QtWidgets.QSpacerItem(20, 21, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_3.addItem(spacerItem2)
        self.horizontalLayout.addWidget(self.groupBox_4)
        self.groupBox_5 = QtWidgets.QGroupBox(self.groupBox)
        self.groupBox_5.setObjectName("groupBox_5")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.groupBox_5)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.pushButton_buzzer_open = QtWidgets.QPushButton(self.groupBox_5)
        self.pushButton_buzzer_open.setObjectName("pushButton_buzzer_open")
        self.verticalLayout_4.addWidget(self.pushButton_buzzer_open)
        self.pushButton_buzzer_close = QtWidgets.QPushButton(self.groupBox_5)
        self.pushButton_buzzer_close.setObjectName("pushButton_buzzer_close")
        self.verticalLayout_4.addWidget(self.pushButton_buzzer_close)
        spacerItem3 = QtWidgets.QSpacerItem(20, 21, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.verticalLayout_4.addItem(spacerItem3)
        self.horizontalLayout.addWidget(self.groupBox_5)
        self.verticalLayout_5.addWidget(self.groupBox)
        self.groupBox_6 = QtWidgets.QGroupBox(Form)
        self.groupBox_6.setTitle("")
        self.groupBox_6.setObjectName("groupBox_6")
        self.gridLayout = QtWidgets.QGridLayout(self.groupBox_6)
        self.gridLayout.setObjectName("gridLayout")
        self.pushButton_all_close = QtWidgets.QPushButton(self.groupBox_6)
        self.pushButton_all_close.setObjectName("pushButton_all_close")
        self.gridLayout.addWidget(self.pushButton_all_close, 1, 1, 1, 1)
        self.label_5 = QtWidgets.QLabel(self.groupBox_6)
        self.label_5.setObjectName("label_5")
        self.gridLayout.addWidget(self.label_5, 0, 0, 1, 1)
        self.lineEdit_conn_status = QtWidgets.QLineEdit(self.groupBox_6)
        self.lineEdit_conn_status.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_conn_status.setReadOnly(True)
        self.lineEdit_conn_status.setObjectName("lineEdit_conn_status")
        self.gridLayout.addWidget(self.lineEdit_conn_status, 1, 0, 1, 1)
        self.verticalLayout_5.addWidget(self.groupBox_6)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.groupBox_2.setTitle(_translate("Form", "红灯"))
        self.pushButton_red_open.setText(_translate("Form", "开"))
        self.pushButton_red_close.setText(_translate("Form", "关"))
        self.groupBox_3.setTitle(_translate("Form", "绿灯"))
        self.pushButton_green_open.setText(_translate("Form", "开"))
        self.pushButton_green_close.setText(_translate("Form", "关"))
        self.groupBox_4.setTitle(_translate("Form", "黄灯"))
        self.pushButton_yellow_open.setText(_translate("Form", "开"))
        self.pushButton_yellow_close.setText(_translate("Form", "关"))
        self.groupBox_5.setTitle(_translate("Form", "蜂鸣器"))
        self.pushButton_buzzer_open.setText(_translate("Form", "开"))
        self.pushButton_buzzer_close.setText(_translate("Form", "关"))
        self.pushButton_all_close.setText(_translate("Form", "全关"))
        self.label_5.setText(_translate("Form", "连接状态"))
