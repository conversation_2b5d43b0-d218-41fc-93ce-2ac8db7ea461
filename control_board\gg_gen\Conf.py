import ctypes

from .Base import gts

if gts is not None:
    gts.GTN_LoadConfig.argtypes = [ctypes.c_int16, ctypes.c_char_p]
    gts.GTN_LoadConfig.restype = ctypes.c_int16


def GTN_LoadConfig(core, cfgFilePath):
    return gts.GTN_LoadConfig(core, cfgFilePath.encode("utf-8"))


if gts is not None:
    gts.GTN_SetSense.argtypes = [ctypes.c_int16, ctypes.c_int16, ctypes.c_int16, ctypes.c_int16]
    gts.GTN_SetSense.restype = ctypes.c_int16


def GTN_SetSense(core, dataType, dataIndex, value):
    return gts.GTN_SetSense(core, dataType, dataIndex, value)


if gts is not None:
    gts.GTN_AlarmOn.argtypes = [ctypes.c_int16, ctypes.c_int16]
    gts.GTN_AlarmOn.restype = ctypes.c_int16


def GTN_AlarmOn(core, axis):
    return gts.GTN_AlarmOn(core, axis)


if gts is not None:
    gts.GTN_AlarmOff.argtypes = [ctypes.c_int16, ctypes.c_int16]
    gts.GTN_AlarmOff.restype = ctypes.c_int16


def GTN_AlarmOff(core, axis):
    return gts.GTN_AlarmOff(core, axis)


if gts is not None:
    gts.GTN_LmtsOnEx.argtypes = [ctypes.c_int16, ctypes.c_int16, ctypes.c_int16, ctypes.c_int16]
    gts.GTN_LmtsOnEx.restype = ctypes.c_int16


def GTN_LmtsOnEx(core, axis, limitType=-1, limitMode=-1):
    return gts.GTN_LmtsOnEx(core, axis)


if gts is not None:
    gts.GTN_LmtsOffEx.argtypes = [ctypes.c_int16, ctypes.c_int16, ctypes.c_int16, ctypes.c_int16]
    gts.GTN_LmtsOffEx.restype = ctypes.c_int16


def GTN_LmtsOffEx(core, axis, limitType=-1, limitMode=-1):
    return gts.GTN_LmtsOnEx(core, axis)
