import traceback

import serial
from modbus_tk import defines
from modbus_tk.modbus_rtu import RtuMaster

from common.LogUtils import logger


class ColorTempLightIntensityClient:
    def __init__(self):
        self.port = None
        self.baudrate = None
        self.bytesize = None
        self.parity = None
        self.stopbits = None
        self.slave_id = None
        self.timeout = None
        self._is_open = False
        self._master = None

    def is_open(self):
        return self._is_open

    def open(self, port, baudrate=9600, bytesize=8, parity="N", stopbits=1, slave_id=1, timeout=5.0):
        self.port = port
        self.baudrate = baudrate
        self.bytesize = bytesize
        self.parity = parity
        self.stopbits = stopbits
        self.slave_id = slave_id
        self.timeout = timeout

        if not self._is_open:
            try:
                if self._master:
                    self._master.close()
                self._master = RtuMaster(
                    serial.Serial(port=self.port, baudrate=self.baudrate, bytesize=self.bytesize,
                                  parity=self.parity, stopbits=self.stopbits)
                )
                self._master.set_timeout(self.timeout)
                self._master.open()
                self._is_open = True
            except Exception as e:
                logger.error(f"open exception: {str(e.args)}")
                print("open exception.\n", traceback.format_exc())
                self._is_open = False
        return self._is_open

    def close(self):
        if self._master:
            try:
                self._master.close()
            except Exception as e:
                logger.error(f"close exception: {str(e.args)}")

        self._master = None
        self._is_open = False

        return True

    def execute(self, function_code, starting_address, quantity_of_x=0, output_value=0,
                data_format="", expected_length=-1, write_starting_address_fc23=0):
        if not self._master:
            return None

        try:
            r = self._master.execute(
                self.slave_id, function_code, starting_address, quantity_of_x,
                output_value, data_format, expected_length, write_starting_address_fc23
            )
            return r
        except Exception as e:
            logger.error(f"execute exception: {str(e.args)}")
            print("execute exception.\n", traceback.format_exc())
            return None

    def read_hr_one(self, starting_address):
        """保持寄存器单个读取"""
        r = self.execute(defines.READ_HOLDING_REGISTERS, starting_address, 1)
        if r is None:
            return None
        return r[0]

    def read_hr_many(self, starting_address, quantity_of_x):
        """保持寄存器多个读取"""
        r = self.execute(defines.READ_HOLDING_REGISTERS, starting_address, quantity_of_x)
        return r

    def write_hr_one(self, starting_address, value):
        """保持寄存器单个写入"""
        r = self.execute(defines.WRITE_SINGLE_REGISTER, starting_address, 1, value)
        return r

    def write_hr_many(self, starting_address, data, data_format=""):
        """保持寄存器多个写入"""
        r = self.execute(defines.WRITE_MULTIPLE_REGISTERS, starting_address=starting_address,
                         output_value=data, data_format=data_format)
        return r

    def get_light_intensity(self):
        r = self.read_hr_one(1)
        return r

    def get_color_temp(self):
        r = self.read_hr_one(0)
        return r


color_temp_light_intensity_client: ColorTempLightIntensityClient = ColorTempLightIntensityClient()

if __name__ == '__main__':
    color_temp_light_intensity_client.open("COM19")
    print(color_temp_light_intensity_client.get_light_intensity())
    print(color_temp_light_intensity_client.get_color_temp())
