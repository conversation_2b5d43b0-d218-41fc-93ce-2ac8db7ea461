<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>824</width>
    <height>537</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,1">
    <item>
     <widget class="QFrame" name="frame">
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QGridLayout" name="gridLayout_2">
       <item row="0" column="0">
        <widget class="QPushButton" name="pushButton">
         <property name="text">
          <string>开始测试</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0" colspan="7">
        <widget class="QTableWidget" name="tableWidget">
         <column>
          <property name="text">
           <string>序号</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>画面</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>色坐标X</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>色坐标Y</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>亮度</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>对比度</string>
          </property>
         </column>
        </widget>
       </item>
       <item row="0" column="6" alignment="Qt::AlignRight">
        <widget class="QPushButton" name="pushButton_2">
         <property name="text">
          <string>导出报告</string>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="label">
         <property name="text">
          <string>对比度</string>
         </property>
        </widget>
       </item>
       <item row="0" column="5" alignment="Qt::AlignHCenter">
        <widget class="QLabel" name="label_2">
         <property name="text">
          <string>对比度测试</string>
         </property>
        </widget>
       </item>
       <item row="2" column="5" colspan="2">
        <widget class="QLineEdit" name="lineEdit">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>50</height>
          </size>
         </property>
         <property name="inputMask">
          <string notr="true"/>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="readOnly">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QFrame" name="frame_2">
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QGridLayout" name="gridLayout">
       <item row="0" column="0" colspan="2">
        <widget class="QPushButton" name="pushButton_3">
         <property name="text">
          <string>开始测试</string>
         </property>
        </widget>
       </item>
       <item row="0" column="2">
        <widget class="QComboBox" name="comboBox">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>50</height>
          </size>
         </property>
         <item>
          <property name="text">
           <string>DCI-P3 D65</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>NTSC</string>
          </property>
         </item>
        </widget>
       </item>
       <item row="0" column="3">
        <widget class="QLabel" name="label_3">
         <property name="text">
          <string>色域测试</string>
         </property>
        </widget>
       </item>
       <item row="0" column="4">
        <widget class="QPushButton" name="pushButton_4">
         <property name="text">
          <string>导出报告</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0" colspan="5">
        <widget class="QTableWidget" name="tableWidget_2">
         <column>
          <property name="text">
           <string>序号</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>画面</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>色坐标X</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>色坐标Y</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>亮度</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>色域值</string>
          </property>
         </column>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="QLabel" name="label_4">
         <property name="text">
          <string>色域值</string>
         </property>
        </widget>
       </item>
       <item row="2" column="1" colspan="4">
        <widget class="QLineEdit" name="lineEdit_2">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>50</height>
          </size>
         </property>
         <property name="inputMask">
          <string notr="true"/>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="readOnly">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
