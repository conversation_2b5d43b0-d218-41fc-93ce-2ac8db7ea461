# -*- coding: utf-8 -*-
"""
user:Created by jid on 2021/1/29
email:<EMAIL>
description:
"""

from PyQt5.QtCore import QObject

from common.LogUtils import logger
from photics import photics_manager
from photics.color_analyzer_tools.manager import MeasureType
from photics.color_analyzer_tools.manager.SignalCenter import signal_center
from photics.color_analyzer_tools.manager.WhiteBalanceClient import white_balance_client


class WhiteBalanceCollect(QObject):

    def __init__(self):
        super(WhiteBalanceCollect, self).__init__()
        self.gray_index = []
        self.brightness_index = 0
        self.brightness = []
        self.input_R = []
        self.input_G = []
        self.input_B = []
        self.input_grey_x = []
        self.input_grey_y = []
        self.input_grey_Y = []
        signal_center.white_balance_measure_data_signal.connect(self.measure_data)
        self.gray_level = [255, 254, 252, 250, 248, 240, 224, 208, 192, 176, 160, 144, 136, 128, 120, 112, 104, 96, 88,
                           80, 72, 64, 56, 48, 40, 32, 24, 16, 8, 0]
        self.create_wb_brightness()
        white_balance_client.start_ws()

    def create_wb_brightness(self):
        logger.info('create_wb_brightness')
        self.brightness.append({'R': 0, 'G': 0, 'B': 255})
        self.brightness.append({'R': 0, 'G': 255, 'B': 0})
        self.brightness.append({'R': 255, 'G': 0, 'B': 0})
        for level in self.gray_level:
            pattern = {'R': level, 'G': level, 'B': level}
            self.brightness.append(pattern)
        logger.info('create_wb_brightness gray_level=%s', self.gray_level)
        logger.info('create_wb_brightness brightness=%s', self.brightness)

    def reset_params(self):
        self.input_B.clear()
        self.input_G.clear()
        self.input_R.clear()
        self.input_grey_x.clear()
        self.input_grey_y.clear()
        self.input_grey_Y.clear()
        self.gray_index.clear()
        self.brightness_index = 0

    def measure_data(self, measure_data):
        logger.info('measure_data measure_data=%s', measure_data)
        if self.brightness_index == 0:
            for i in measure_data:
                self.input_B.append(i)
        elif self.brightness_index == 1:
            for i in measure_data:
                self.input_G.append(i)
        elif self.brightness_index == 2:
            for i in measure_data:
                self.input_R.append(i)
        elif self.brightness_index == 3:
            self.input_grey_x.append(measure_data[0])
            self.input_grey_y.append(measure_data[1])
            self.input_grey_Y.append(measure_data[2])
        elif self.brightness_index == len(self.brightness) - 1:
            # 灰阶0测试值可能为0 需要手动+0.0001
            self.input_grey_x.append(measure_data[0] + 0.0001)
            self.input_grey_y.append(measure_data[1] + 0.0001)
            self.input_grey_Y.append(measure_data[2] + 0.0001)
        else:
            self.input_grey_x.append(measure_data[0])
            self.input_grey_y.append(measure_data[1])
            self.input_grey_Y.append(measure_data[2])

        self.gray_index.append(self.brightness_index)
        self.brightness_index += 1
        if self.brightness_index == len(self.brightness):
            signal_center.white_balance_measure_event_signal.emit(MeasureType.MEASURE_COMPLETED)
        else:
            signal_center.white_balance_measure_event_signal.emit(MeasureType.MEASURE_NEXT)

    def set_pattern_background(self):
        logger.info('set_pattern_background brightness_index=%s, %s', self.brightness_index, len(self.brightness))
        if self.brightness_index > len(self.brightness):
            return
        brightness = self.brightness[self.brightness_index]
        photics_manager.set_background_color(r=brightness['R'], g=brightness['G'], b=brightness['B'])
