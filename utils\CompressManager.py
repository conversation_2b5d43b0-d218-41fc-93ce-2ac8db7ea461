import os
import signal
import subprocess
import time

from common.LogUtils import logger
from utils import get_process_pid


class CompressManager:

    def compress_video(self, input_file):
        if input_file is None:
            return
        output_file = input_file.replace(".mp4", "_compress.mp4")
        logger.info(f"compress_video input_file={input_file}, output_file={output_file}")
        """
        这个函数用于压缩指定的视频文件
    
        参数：
        input_file (str)：要压缩的输入视频文件路径
        output_file (str)：压缩后的输出视频文件路径
        """
        # 记录开始时间
        start_time = time.time()
        # 定义执行的命令，使用 ffmpeg 进行视频压缩
        command = f"ffmpeg\\bin\\ffmpeg.exe -y -i {input_file} -vcodec h264 -preset fast -b:v 2000k {output_file}"
        logger.info(f"compress_video command={command}")
        # 执行命令
        subprocess.run(command, shell=True)
        # 记录结束时间
        end_time = time.time()
        # 计算耗时并转换为分钟
        elapsed_time = (end_time - start_time)
        # 打印压缩完成的信息及耗时
        logger.info(f"compress_video output_file={output_file}视频压缩完成，耗时：{elapsed_time}秒")
        self.kill_process_pid()

    # def compress_video(self, input_video_path, crf=1):
    #     logger.info(f"compress_video input_video_path={input_video_path}, crf={crf}")
    #     # 记录开始时间
    #     start_time = time.time()
    #     output_video_path = input_video_path.replace('.mp4', '_compress.mp4')
    #     ffmpeg_pah = os.path.join(os.getcwd(), "ffmpeg", "bin", "ffmpeg.exe")
    #     ffmpeg_cmd = [
    #         ffmpeg_pah,
    #         '-i', input_video_path,
    #         '-c:v', 'libxvid',  # 使用 libxvid 编码器来处理 MP4V 格式
    #         '-q:v', str(crf),  # 设置视频质量
    #         '-c:a', 'aac',
    #         '-strict', 'experimental',
    #         '-b:a', '1000k',
    #         output_video_path
    #     ]

    #     try:
    #         subprocess.run(ffmpeg_cmd, check=True)
    #         os.remove(input_video_path)
    #         logger.info('compress_video 原视频删除完成')
    #         # 记录结束时间
    #         end_time = time.time()
    #         # 计算耗时
    #         elapsed_time = (end_time - start_time)
    #         logger.info(f"compress_video output_video_path={output_video_path}视频压缩完成，耗时：{elapsed_time}秒")
    #     except Exception as e:
    #         logger.error(f"compress_video 视频压缩失败: {str(e.args)}")

    #     self.kill_process_pid()

    @staticmethod
    def kill_process_pid():
        pid = get_process_pid("ffmpeg.exe")
        logger.info(f"kill_process_pid pid={pid}")
        if pid is not None:
            os.kill(pid, signal.SIGTERM)
        logger.info(f"kill_process_pid kill success")


compress_manager: CompressManager = CompressManager()
