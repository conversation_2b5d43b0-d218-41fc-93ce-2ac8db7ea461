import pyqtgraph as pg
import numpy as np

class RealTimePlot(pg.GraphicsLayoutWidget):
    def __init__(self,parent=None):
        super(RealTimePlot, self).__init__(parent=parent)
        self.plot = self.addPlot(title="实时数据")
        self.curve = self.plot.plot(pen='')
        self.data = np.zeros(1000)  # 初始化数据数组

    def update_plot(self, new_data):
        self.data = np.roll(self.data, -len(new_data))
        self.data[-len(new_data):] = new_data
        self.curve.setData(self.data)