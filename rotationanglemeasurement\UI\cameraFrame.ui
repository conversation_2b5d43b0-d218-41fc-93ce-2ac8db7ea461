<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>cameraForm</class>
 <widget class="QWidget" name="cameraForm">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>471</width>
    <height>486</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="spacing">
    <number>6</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout" stretch="99,99,0,0,0">
     <property name="spacing">
      <number>6</number>
     </property>
     <property name="leftMargin">
      <number>15</number>
     </property>
     <property name="topMargin">
      <number>5</number>
     </property>
     <property name="rightMargin">
      <number>15</number>
     </property>
     <property name="bottomMargin">
      <number>5</number>
     </property>
     <item>
      <widget class="QLabel" name="labelLeft">
       <property name="styleSheet">
        <string notr="true">background:#e0e5f5; padding:10; border-radius:5;</string>
       </property>
       <property name="text">
        <string>Left</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="labelRight">
       <property name="styleSheet">
        <string notr="true">background:#e0e5f5; padding:10; margin-top:10; border-radius:5;</string>
       </property>
       <property name="text">
        <string>Right</string>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="0,1">
       <item>
        <widget class="QLabel" name="label">
         <property name="text">
          <string>左图坐标：</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_coordinate_left">
         <property name="styleSheet">
          <string notr="true">background:#e0e5f5; padding:10; border-radius:5;</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="0,1">
       <item>
        <widget class="QLabel" name="label_2">
         <property name="text">
          <string>右图坐标：</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_coordinate_right">
         <property name="styleSheet">
          <string notr="true">background:#e0e5f5; padding:10; border-radius:5;</string>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QPushButton" name="pushButtonloadIMGLeft">
         <property name="styleSheet">
          <string notr="true">background:#e0e5f5; padding:10; border-radius:5;</string>
         </property>
         <property name="text">
          <string>左相机</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_6">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Fixed</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pushButtonloadIMGRight">
         <property name="styleSheet">
          <string notr="true">background:#e0e5f5; padding:10; border-radius:5;</string>
         </property>
         <property name="text">
          <string>右相机</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Fixed</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QComboBox" name="comboBox">
         <property name="minimumSize">
          <size>
           <width>80</width>
           <height>0</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background:#e0e5f5; padding:10; border-radius:5;</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
