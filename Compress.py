import os
import sys
import zipfile

if __name__ == '__main__':
    dir_path = sys.argv[1]   # 需要压缩的文件夹
    save_path = sys.argv[2]  # zip的保存路径
    zip_name = sys.argv[3]   # zip的文件名
    zip_file_path = os.path.join(save_path, zip_name)
    print('compress', dir_path, zip_file_path)

    if not os.path.exists(dir_path):
        print('compress path is not exist')
    else:
        print('compress start')
        if not os.path.exists(save_path):
            os.makedirs(save_path)

        zip_file = zipfile.ZipFile(zip_file_path, "w", zipfile.ZIP_DEFLATED)
        for path, dir_names, file_names in os.walk(dir_path):
            # 去掉目标跟路径，只对目标文件夹下边的文件及文件夹进行压缩
            file_path = path.replace(dir_path, '')

            for filename in file_names:
                print('compress => ', filename)
                zip_file.write(os.path.join(path, filename), os.path.join(file_path, filename))
        zip_file.close()
        print('compress complete')
