# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'serviceWidget.ui'
#
# Created by: PyQt5 UI code generator 5.12.3
#
# WARNING! All changes made in this file will be lost!


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_Form(object):
    def setupUi(self, Form):
        Form.setObjectName("Form")
        Form.resize(230, 370)
        self.verticalLayout = QtWidgets.QVBoxLayout(Form)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setSpacing(6)
        self.verticalLayout.setObjectName("verticalLayout")
        self.line_2 = QtWidgets.QFrame(Form)
        self.line_2.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_2.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_2.setObjectName("line_2")
        self.verticalLayout.addWidget(self.line_2)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.comboBox_delay = QtWidgets.QComboBox(Form)
        self.comboBox_delay.setMinimumSize(QtCore.QSize(0, 45))
        self.comboBox_delay.setObjectName("comboBox_delay")
        self.horizontalLayout.addWidget(self.comboBox_delay)
        self.pushButton_close = QtWidgets.QPushButton(Form)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Fixed, QtWidgets.QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton_close.sizePolicy().hasHeightForWidth())
        self.pushButton_close.setSizePolicy(sizePolicy)
        self.pushButton_close.setObjectName("pushButton_close")
        self.horizontalLayout.addWidget(self.pushButton_close)
        self.verticalLayout.addLayout(self.horizontalLayout)
        self.formLayout_2 = QtWidgets.QFormLayout()
        self.formLayout_2.setObjectName("formLayout_2")
        self.label = QtWidgets.QLabel(Form)
        self.label.setObjectName("label")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label)
        self.label_25 = QtWidgets.QLabel(Form)
        self.label_25.setObjectName("label_25")
        self.formLayout_2.setWidget(1, QtWidgets.QFormLayout.LabelRole, self.label_25)
        self.label_26 = QtWidgets.QLabel(Form)
        self.label_26.setObjectName("label_26")
        self.formLayout_2.setWidget(2, QtWidgets.QFormLayout.LabelRole, self.label_26)
        self.spinBox_on_time = QtWidgets.QSpinBox(Form)
        self.spinBox_on_time.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_on_time.setMinimum(1)
        self.spinBox_on_time.setMaximum(1000000)
        self.spinBox_on_time.setObjectName("spinBox_on_time")
        self.formLayout_2.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.spinBox_on_time)
        self.spinBox_off_time = QtWidgets.QSpinBox(Form)
        self.spinBox_off_time.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_off_time.setMinimum(1)
        self.spinBox_off_time.setMaximum(1000000)
        self.spinBox_off_time.setObjectName("spinBox_off_time")
        self.formLayout_2.setWidget(1, QtWidgets.QFormLayout.FieldRole, self.spinBox_off_time)
        self.spinBox_count = QtWidgets.QSpinBox(Form)
        self.spinBox_count.setMinimumSize(QtCore.QSize(0, 45))
        self.spinBox_count.setMaximum(1000000)
        self.spinBox_count.setObjectName("spinBox_count")
        self.formLayout_2.setWidget(2, QtWidgets.QFormLayout.FieldRole, self.spinBox_count)
        self.verticalLayout.addLayout(self.formLayout_2)
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.pushButton_update = QtWidgets.QPushButton(Form)
        self.pushButton_update.setObjectName("pushButton_update")
        self.horizontalLayout_5.addWidget(self.pushButton_update)
        self.pushButton_save = QtWidgets.QPushButton(Form)
        self.pushButton_save.setObjectName("pushButton_save")
        self.horizontalLayout_5.addWidget(self.pushButton_save)
        self.verticalLayout.addLayout(self.horizontalLayout_5)
        self.formLayout_4 = QtWidgets.QFormLayout()
        self.formLayout_4.setObjectName("formLayout_4")
        self.label_10 = QtWidgets.QLabel(Form)
        self.label_10.setObjectName("label_10")
        self.formLayout_4.setWidget(0, QtWidgets.QFormLayout.LabelRole, self.label_10)
        self.label_11 = QtWidgets.QLabel(Form)
        self.label_11.setObjectName("label_11")
        self.formLayout_4.setWidget(1, QtWidgets.QFormLayout.LabelRole, self.label_11)
        self.label_13 = QtWidgets.QLabel(Form)
        self.label_13.setObjectName("label_13")
        self.formLayout_4.setWidget(2, QtWidgets.QFormLayout.LabelRole, self.label_13)
        self.lineEdit_remain_count = QtWidgets.QLineEdit(Form)
        self.lineEdit_remain_count.setMinimumSize(QtCore.QSize(0, 45))
        self.lineEdit_remain_count.setReadOnly(True)
        self.lineEdit_remain_count.setObjectName("lineEdit_remain_count")
        self.formLayout_4.setWidget(0, QtWidgets.QFormLayout.FieldRole, self.lineEdit_remain_count)
        self.progressBar_on = QtWidgets.QProgressBar(Form)
        self.progressBar_on.setProperty("value", 24)
        self.progressBar_on.setObjectName("progressBar_on")
        self.formLayout_4.setWidget(1, QtWidgets.QFormLayout.FieldRole, self.progressBar_on)
        self.progressBar_off = QtWidgets.QProgressBar(Form)
        self.progressBar_off.setProperty("value", 24)
        self.progressBar_off.setObjectName("progressBar_off")
        self.formLayout_4.setWidget(2, QtWidgets.QFormLayout.FieldRole, self.progressBar_off)
        self.verticalLayout.addLayout(self.formLayout_4)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.pushButton_start = QtWidgets.QPushButton(Form)
        self.pushButton_start.setObjectName("pushButton_start")
        self.horizontalLayout_3.addWidget(self.pushButton_start)
        self.pushButton_stop = QtWidgets.QPushButton(Form)
        self.pushButton_stop.setObjectName("pushButton_stop")
        self.horizontalLayout_3.addWidget(self.pushButton_stop)
        self.verticalLayout.addLayout(self.horizontalLayout_3)
        self.line = QtWidgets.QFrame(Form)
        self.line.setFrameShape(QtWidgets.QFrame.HLine)
        self.line.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line.setObjectName("line")
        self.verticalLayout.addWidget(self.line)

        self.retranslateUi(Form)
        QtCore.QMetaObject.connectSlotsByName(Form)

    def retranslateUi(self, Form):
        _translate = QtCore.QCoreApplication.translate
        Form.setWindowTitle(_translate("Form", "Form"))
        self.pushButton_close.setText(_translate("Form", "关闭"))
        self.label.setText(_translate("Form", "唤醒时长："))
        self.label_25.setText(_translate("Form", "休眠时长："))
        self.label_26.setText(_translate("Form", "循环次数："))
        self.pushButton_update.setText(_translate("Form", "刷新"))
        self.pushButton_save.setText(_translate("Form", "保存"))
        self.label_10.setText(_translate("Form", "剩余次数："))
        self.label_11.setText(_translate("Form", "唤醒："))
        self.label_13.setText(_translate("Form", "休眠："))
        self.pushButton_start.setText(_translate("Form", "启动"))
        self.pushButton_stop.setText(_translate("Form", "停止"))
