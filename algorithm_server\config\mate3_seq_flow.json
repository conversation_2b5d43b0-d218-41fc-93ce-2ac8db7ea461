{
    "sequence":
    [
        //流程控制
        {
            "name":"TCP调试流程",
            "command":
            [
                {
                    "name":"原路返回TCP",
                    "type":"send_tcp_server",
                    "in":
                    {
                        "server":"调试tcp",
                        "content":"@TRIGGER_STRING"
                    },
                    "out":
                    {
                        "result":"原路返回TCP结果"
                    }
                },
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":","
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取序号",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"0"
                    },
                    "out":
                    {
                        "result":"获取序号结果",
                        "string":"字符序号"
                    }
                },
                {
                    "name":"获取参数",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"1"
                    },
                    "out":
                    {
                        "result":"获取参数结果",
                        "string":"字符参数"
                    }
                },
                {
                    "name":"推送序号是否等于1",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@字符序号\",\"=1\"]"
                    },
                    "out":
                    {
                        "result":"推送序号是否等于1结果",
                        "combine":"推送序号是否等于1"
                    }
                },
                {
                    "name":"计算推送序号是否等于1",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送序号是否等于1" 
                    },
                    "out":
                    {
                        "result":"计算推送序号是否等于1结果",
                        "value":"序号是否等于1"
                    }
                },
                {
                    "name":"序号是否等于1整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@序号是否等于1"                
                    },
                    "out":
                    {
                        "result":"计算序号是否等于1整型结果",
                        "integer":"序号是否等于1整型"
                    }
                },
                {
                    "name":"判断序号是否等于1",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"启动右开始流程",
                        "condition":"@序号是否等于1整型"
                    }
                },
                {
                    "name":"推送序号是否等于2",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@字符序号\",\"=2\"]"
                    },
                    "out":
                    {
                        "result":"推送序号是否等于2结果",
                        "combine":"推送序号是否等于2"
                    }
                },
                {
                    "name":"计算推送序号是否等于2",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送序号是否等于2" 
                    },
                    "out":
                    {
                        "result":"计算推送序号是否等于2结果",
                        "value":"序号是否等于2"
                    }
                },
                {
                    "name":"序号是否等于2整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@序号是否等于2"                
                    },
                    "out":
                    {
                        "result":"计算序号是否等于2整型结果",
                        "integer":"序号是否等于2整型"
                    }
                },
                {
                    "name":"判断序号是否等于2",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"启动右拍屏幕流程",
                        "condition":"@序号是否等于2整型"
                    }
                },
                {
                    "name":"推送序号是否等于3",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@字符序号\",\"=3\"]"
                    },
                    "out":
                    {
                        "result":"推送序号是否等于3结果",
                        "combine":"推送序号是否等于3"
                    }
                },
                {
                    "name":"计算推送序号是否等于3",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送序号是否等于3" 
                    },
                    "out":
                    {
                        "result":"计算推送序号是否等于3结果",
                        "value":"序号是否等于3"
                    }
                },
                {
                    "name":"序号是否等于3整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@序号是否等于3"                
                    },
                    "out":
                    {
                        "result":"计算序号是否等于3整型结果",
                        "integer":"序号是否等于3整型"
                    }
                },
                {
                    "name":"判断序号是否等于3",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"启动右对位流程",
                        "condition":"@序号是否等于3整型"
                    }
                },
                {
                    "name":"推送序号是否等于4",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@字符序号\",\"=4\"]"
                    },
                    "out":
                    {
                        "result":"推送序号是否等于4结果",
                        "combine":"推送序号是否等于4"
                    }
                },
                {
                    "name":"计算推送序号是否等于4",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送序号是否等于4" 
                    },
                    "out":
                    {
                        "result":"计算推送序号是否等于4结果",
                        "value":"序号是否等于4"
                    }
                },
                {
                    "name":"序号是否等于4整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@序号是否等于4"                
                    },
                    "out":
                    {
                        "result":"计算序号是否等于4整型结果",
                        "integer":"序号是否等于4整型"
                    }
                },
                {
                    "name":"判断序号是否等于4",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"启动左开始流程",
                        "condition":"@序号是否等于4整型"
                    }
                },
                {
                    "name":"推送序号是否等于5",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@字符序号\",\"=5\"]"
                    },
                    "out":
                    {
                        "result":"推送序号是否等于5结果",
                        "combine":"推送序号是否等于5"
                    }
                },
                {
                    "name":"计算推送序号是否等于5",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送序号是否等于5" 
                    },
                    "out":
                    {
                        "result":"计算推送序号是否等于5结果",
                        "value":"序号是否等于5"
                    }
                },
                {
                    "name":"序号是否等于5整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@序号是否等于5"                
                    },
                    "out":
                    {
                        "result":"计算序号是否等于5整型结果",
                        "integer":"序号是否等于5整型"
                    }
                },
                {
                    "name":"判断序号是否等于5",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"启动左拍屏幕流程",
                        "condition":"@序号是否等于5整型"
                    }
                },
                {
                    "name":"推送序号是否等于6",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@字符序号\",\"=6\"]"
                    },
                    "out":
                    {
                        "result":"推送序号是否等于6结果",
                        "combine":"推送序号是否等于6"
                    }
                },
                {
                    "name":"计算推送序号是否等于6",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送序号是否等于6" 
                    },
                    "out":
                    {
                        "result":"计算推送序号是否等于6结果",
                        "value":"序号是否等于6"
                    }
                },
                {
                    "name":"序号是否等于6整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@序号是否等于6"                
                    },
                    "out":
                    {
                        "result":"计算序号是否等于6整型结果",
                        "integer":"序号是否等于6整型"
                    }
                },
                {
                    "name":"判断序号是否等于6",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"启动左对位流程",
                        "condition":"@序号是否等于6整型"
                    }
                },
                {
                    "name":"推送序号是否等于7",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@字符序号\",\"=7\"]"
                    },
                    "out":
                    {
                        "result":"推送序号是否等于7结果",
                        "combine":"推送序号是否等于7"
                    }
                },
                {
                    "name":"计算推送序号是否等于7",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送序号是否等于7" 
                    },
                    "out":
                    {
                        "result":"计算推送序号是否等于7结果",
                        "value":"序号是否等于7"
                    }
                },
                {
                    "name":"序号是否等于7整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@序号是否等于7"                
                    },
                    "out":
                    {
                        "result":"计算序号是否等于7整型结果",
                        "integer":"序号是否等于7整型"
                    }
                },
                {
                    "name":"判断序号是否等于7",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"启动点胶标定流程",
                        "condition":"@序号是否等于7整型"
                    }
                },
                {
                    "name":"推送序号是否等于8",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@字符序号\",\"=8\"]"
                    },
                    "out":
                    {
                        "result":"推送序号是否等于8结果",
                        "combine":"推送序号是否等于8"
                    }
                },
                {
                    "name":"计算推送序号是否等于8",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送序号是否等于8" 
                    },
                    "out":
                    {
                        "result":"计算推送序号是否等于8结果",
                        "value":"序号是否等于8"
                    }
                },
                {
                    "name":"序号是否等于8整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@序号是否等于8"                
                    },
                    "out":
                    {
                        "result":"计算序号是否等于8整型结果",
                        "integer":"序号是否等于8整型"
                    }
                },
                {
                    "name":"判断序号是否等于8",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"启动右点胶流程",
                        "condition":"@序号是否等于8整型"
                    }
                },
                {
                    "name":"推送序号是否等于9",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@字符序号\",\"=9\"]"
                    },
                    "out":
                    {
                        "result":"推送序号是否等于9结果",
                        "combine":"推送序号是否等于9"
                    }
                },
                {
                    "name":"计算推送序号是否等于9",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送序号是否等于9" 
                    },
                    "out":
                    {
                        "result":"计算推送序号是否等于9结果",
                        "value":"序号是否等于9"
                    }
                },
                {
                    "name":"序号是否等于9整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@序号是否等于9"                
                    },
                    "out":
                    {
                        "result":"计算序号是否等于9整型结果",
                        "integer":"序号是否等于9整型"
                    }
                },
                {
                    "name":"判断序号是否等于9",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"启动左点胶流程",
                        "condition":"@序号是否等于9整型"
                    }
                },
                {
                    "name":"推送序号是否等于100",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@字符序号\",\"=100\"]"
                    },
                    "out":
                    {
                        "result":"推送序号是否等于100结果",
                        "combine":"推送序号是否等于100"
                    }
                },
                {
                    "name":"计算推送序号是否等于100",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送序号是否等于100" 
                    },
                    "out":
                    {
                        "result":"计算推送序号是否等于100结果",
                        "value":"序号是否等于100"
                    }
                },
                {
                    "name":"序号是否等于100整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@序号是否等于100"                
                    },
                    "out":
                    {
                        "result":"计算序号是否等于100整型结果",
                        "integer":"序号是否等于100整型"
                    }
                },
                {
                    "name":"判断序号是否等于100",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"启动左测试流程",
                        "condition":"@序号是否等于100整型"
                    }
                },
                {
                    "name":"结束TCP调试流程",
                    "type":"end"
                },
                {
                    "name":"启动左开始流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "name":"左开始流程",
                        "para":""
                    }
                },
                {
                    "name":"结束启动左开始流程",
                    "type":"end"
                },
                {
                    "name":"启动右开始流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "name":"右开始流程",
                        "para":""
                    }
                },
                {
                    "name":"结束启动右开始流程",
                    "type":"end"
                },
                {
                    "name":"启动右拍屏幕流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "name":"右拍屏幕流程",
                        "para":"@字符参数"
                    }
                },
                {
                    "name":"结束启动右拍屏幕流程",
                    "type":"end"
                },
                {
                    "name":"启动左拍屏幕流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "name":"左拍屏幕流程",
                        "para":"@字符参数"
                    }
                },
                {
                    "name":"结束启动左拍屏幕流程",
                    "type":"end"
                },
                {
                    "name":"启动右对位流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "name":"右对位流程",
                        "para":"@字符参数"
                    }
                },
                {
                    "name":"结束启动右对位流程",
                    "type":"end"
                },
                {
                    "name":"启动左对位流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "name":"左对位流程",
                        "para":"@字符参数"
                    }
                },
                {
                    "name":"结束启动左对位流程",
                    "type":"end"
                },
                {
                    "name":"启动点胶标定流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "name":"点胶标定流程",
                        "para":"@字符参数"
                    }
                },
                {
                    "name":"结束启动点胶标定流程",
                    "type":"end"
                },
                {
                    "name":"启动右点胶流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "name":"右点胶流程",
                        "para":"@字符参数"
                    }
                },
                {
                    "name":"结束启动右点胶流程",
                    "type":"end"
                },
                {
                    "name":"启动左点胶流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "name":"左点胶流程",
                        "para":"@字符参数"
                    }
                },
                {
                    "name":"结束启动左点胶流程",
                    "type":"end"
                },
                {
                    "name":"启动左测试流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "name":"test_align_L",
                        "para":"@字符参数"
                    }
                },
                {
                    "name":"结束启动左测试流程",
                    "type":"end"
                }
            ]
        },
        //MODBUS
        {
            "name":"开始点胶标定上升沿流程",
            "command":
            [
                {
                    "name":"回复收到点胶标定",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "master":"点胶控制卡",
                        "offset":"21",
                        "type":"short",
                        "content":"1"
                    }
                },
                {
                    "name":"启动点胶标定流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "name":"点胶标定流程",
                        "para":""
                    }
                }
            ]
        },
        {
            "name":"开始点胶上升沿流程",
            "command":
            [
                {
                    "name":"回复收到点胶标定",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "master":"点胶控制卡",
                        "offset":"36",
                        "type":"short",
                        "content":"1"
                    }
                },
                {
                    "name":"读取点胶方向",
                    "type":"read_modbus_master",
                    "in":
                    {
                        "master":"点胶控制卡",
                        "offset":"34",
                        "type":"short"
                    },
                    "out":
                    {
                        "result":"读取点胶方向结果",
                        "content":"点胶方向"
                    }
                },
                {
                    "name":"判断读取点胶方向结果",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"推送判断点胶方向左",
                        "condition":"@读取点胶方向结果"
                    }
                },
                {
                    "name":"跳转点胶失败",
                    "type":"jump",
                    "in":
                    {
                        "tag":"发送点胶NG结果"
                    }
                },
                {
                    "name":"推送判断点胶方向左",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@点胶方向\",\"=1\"]"
                    },
                    "out":
                    {
                        "result":"推送判断点胶方向左结果",
                        "combine":"推送判断点胶方向左"
                    }
                },
                {
                    "name":"计算判断点胶方向左",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送判断点胶方向左" 
                    },
                    "out":
                    {
                        "result":"计算判断点胶方向左结果",
                        "value":"判断点胶方向左"
                    }
                },
                {
                    "name":"判断点胶方向左整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@判断点胶方向左"                
                    },
                    "out":
                    {
                        "result":"计算判断点胶方向左整型结果",
                        "integer":"判断点胶方向左整型"
                    }
                },
                {
                    "name":"判断点胶方向左跳转",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"启动左点胶流程",
                        "condition":"@判断点胶方向左整型"
                    }
                },
                {
                    "name":"推送判断点胶方向右",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"@点胶方向\",\"=2\"]"
                    },
                    "out":
                    {
                        "result":"推送判断点胶方向右结果",
                        "combine":"推送判断点胶方向右"
                    }
                },
                {
                    "name":"计算判断点胶方向右",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送判断点胶方向右" 
                    },
                    "out":
                    {
                        "result":"计算判断点胶方向右结果",
                        "value":"判断点胶方向右"
                    }
                },
                {
                    "name":"判断点胶方向右整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@判断点胶方向右"                
                    },
                    "out":
                    {
                        "result":"计算判断点胶方向右整型结果",
                        "integer":"判断点胶方向右整型"
                    }
                },
                {
                    "name":"判断点胶方向右跳转",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"启动右点胶流程",
                        "condition":"@判断点胶方向右整型"
                    }
                },
                {
                    "name":"发送点胶NG结果",
                    "type":"send_modbus_master",
                    "in":
                    {
                        "master":"点胶控制卡",
                        "offset":"37",
                        "type":"short",
                        "content":"2"
                    }
                },
                {
                    "name":"结束开始点胶上升沿流程NG",
                    "type":"end"
                },
                {
                    "name":"启动左点胶流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "name":"左点胶流程",
                        "para":""
                    }
                },
                {
                    "name":"结束开始点胶上升沿流程左",
                    "type":"end"
                },
                {
                    "name":"启动右点胶流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "name":"右点胶流程",
                        "para":""
                    }
                },
                {
                    "name":"结束开始点胶上升沿流程右",
                    "type":"end"
                }
            ]
        },
        //左PLC
        {
            "name":"左请求复位上升沿流程",
            "command":
            [
                {
                    "name":"激活下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左相机复位下降沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送接收到复位请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"156",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"左请求复位下降沿流程",
            "command":
            [
                {
                    "name":"关闭下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左相机复位下降沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"激活上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左收到相机复位上升沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送清除接收到复位请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"156",
                        "type":"short",
                        "content":"0"
                    },
                    "out":
                    {
                        "result":"R"
                    }
                },
                {
                    "name":"启动左复位流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@R",
                        "name":"左复位流程",
                        "para":""
                    }
                }
            ] 
        },
        {
            "name":"左收到复位上升沿流程",
            "command":
            [
                {
                    "name":"关闭上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左收到相机复位上升沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"清除复位结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"158",
                        "type":"short",
                        "content":"0"
                    }
                }
            ] 
        },
        {
            "name":"左请求开始上升沿流程",
            "command":
            [
                {
                    "name":"激活下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左请求开始下降沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送接收到开始请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"160",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"左请求开始下降沿流程",
            "command":
            [
                {
                    "name":"关闭下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左请求开始下降沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"激活上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左收到开始上升沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送清除接收到开始请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"160",
                        "type":"short",
                        "content":"0"
                    },
                    "out":
                    {
                        "result":"R"
                    }    
                },
                {
                    "name":"启动左开始流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@R",
                        "name":"左开始流程",
                        "para":""
                    }
                }
            ] 
        }, 
        {
            "name":"左收到开始上升沿流程",
            "command":
            [
                {
                    "name":"关闭上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左收到开始上升沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"清除开始结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"162",
                        "type":"short",
                        "content":"0"
                    }
                }
            ] 
        },
        {
            "name":"左请求排线检测上升沿流程",
            "command":
            [
                {
                    "name":"激活下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左请求排线检测下降沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送接收到排线检测请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"164",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"左请求排线检测下降沿流程",
            "command":
            [
                {
                    "name":"关闭下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左请求排线检测下降沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"激活上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左收到排线检测上升沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送清除接收到排线检测请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"164",
                        "type":"short",
                        "content":"0"
                    },
                    "out":
                    {
                        "result":"R"
                    }
                },
                {
                    "name":"启动左排线检测流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@R",
                        "name":"左排线检测流程",
                        "para":""
                    }
                }
            ] 
        },
        {
            "name":"左收到排线检测上升沿流程",
            "command":
            [
                {
                    "name":"关闭上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左收到排线检测上升沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"清除排线检测结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"166",
                        "type":"short",
                        "content":"0"
                    }
                }
            ] 
        },
        {
            "name":"左请求防水胶带检测上升沿流程",
            "command":
            [
                {
                    "name":"激活下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左请求防水胶带检测下降沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送接收到防水胶带检测请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"184",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"左请求防水胶带检测下降沿流程",
            "command":
            [
                {
                    "name":"关闭下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左请求防水胶带检测下降沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"激活上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左收到防水胶带检测上升沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送清除接收到防水胶带检测请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"184",
                        "type":"short",
                        "content":"0"
                    },
                    "out":
                    {
                        "result":"R"
                    }
                },
                {
                    "name":"启动左防水胶带检测流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@R",
                        "name":"左防水胶带检测流程",
                        "para":""
                    }
                }
            ] 
        },
        {
            "name":"左收到防水胶带检测上升沿流程",
            "command":
            [
                {
                    "name":"关闭上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左收到防水胶带检测上升沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"清除防水胶带检测结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"186",
                        "type":"short",
                        "content":"0"
                    }
                }
            ] 
        },
        {
            "name":"左请求拍屏幕上升沿流程",
            "command":
            [
                {
                    "name":"激活下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左请求拍屏幕下降沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送接收到拍屏幕请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"168",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"左请求拍屏幕下降沿流程",
            "command":
            [
                {
                    "name":"关闭下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左请求拍屏幕下降沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"激活上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左收到拍屏幕上升沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送清除接收到拍屏幕请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"168",
                        "type":"short",
                        "content":"0"
                    },
                    "out":
                    {
                        "result":"L"
                    }
                },
                {
                    "name":"推送标志位是否等于1",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"$左是否为盖板对位\",\"=1\"]"
                    },
                    "out":
                    {
                        "result":"推送标志位是否等于1结果",
                        "combine":"推送标志位是否等于1"
                    }
                },
                {
                    "name":"计算推送标志位是否等于1",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送标志位是否等于1" 
                    },
                    "out":
                    {
                        "result":"计算推送标志位是否等于1结果",
                        "value":"标志位是否等于1"
                    }
                },
                {
                    "name":"标志位是否等于1整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@标志位是否等于1"                
                    },
                    "out":
                    {
                        "result":"计算标志位是否等于1整型结果",
                        "integer":"标志位是否等于1整型"
                    }
                },
                {
                    "name":"判断标志位是否等于1",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"启动左拍屏幕盖板流程",
                        "condition":"@标志位是否等于1整型"
                    }
                },
                {
                    "name":"启动左拍屏幕流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@L",
                        "name":"左拍屏幕流程",
                        "para":""
                    }
                },
                {
                    "name":"结束跳转",
                    "type":"end"
                },
                {
                    "name":"启动左拍屏幕盖板流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@L",
                        "name":"左拍屏幕盖板流程",
                        "para":""
                    }
                }
            ] 
        },
        {
            "name":"左收到拍屏幕上升沿流程",
            "command":
            [
                {
                    "name":"关闭上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左收到拍屏幕上升沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"清除拍屏幕结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"170",
                        "type":"short",
                        "content":"0"
                    }
                }
            ] 
        },
        {
            "name":"左请求对位上升沿流程",
            "command":
            [
                {
                    "name":"激活下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左请求对位下降沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送接收到对位请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"172",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"左请求对位下降沿流程",
            "command":
            [
                {
                    "name":"关闭下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左请求对位下降沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"激活上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左收到对位上升沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送清除接收到对位请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"172",
                        "type":"short",
                        "content":"0"
                    },
                    "out":
                    {
                        "result":"L"
                    }
                },
                {
                    "name":"推送标志位是否等于1",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"$左是否为盖板对位\",\"=1\"]"
                    },
                    "out":
                    {
                        "result":"推送标志位是否等于1结果",
                        "combine":"推送标志位是否等于1"
                    }
                },
                {
                    "name":"计算推送标志位是否等于1",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送标志位是否等于1" 
                    },
                    "out":
                    {
                        "result":"计算推送标志位是否等于1结果",
                        "value":"标志位是否等于1"
                    }
                },
                {
                    "name":"标志位是否等于1整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@标志位是否等于1"                
                    },
                    "out":
                    {
                        "result":"计算标志位是否等于1整型结果",
                        "integer":"标志位是否等于1整型"
                    }
                },
                {
                    "name":"判断标志位是否等于1",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"启动左对位盖板流程",
                        "condition":"@标志位是否等于1整型"
                    }
                },
                {
                    "name":"启动右对位流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@L",
                        "name":"左对位流程",
                        "para":""
                    }
                },
                {
                    "name":"结束跳转",
                    "type":"end"
                },
                {
                    "name":"启动左对位盖板流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@L",
                        "name":"左对位盖板流程",
                        "para":""
                    }
                }
            ] 
        },
        {
            "name":"左收到对位上升沿流程",
            "command":
            [
                {
                    "name":"关闭上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左收到对位上升沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"清除对位结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"174",
                        "type":"short",
                        "content":"0"
                    }
                }
            ] 
        },
        {
            "name":"左请求间隙拍照上升沿流程",
            "command":
            [
                {
                    "name":"激活下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左请求间隙拍照下降沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送接收到间隙拍照请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"176",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"左请求间隙拍照下降沿流程",
            "command":
            [
                {
                    "name":"关闭下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左请求间隙拍照下降沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"激活上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左收到间隙拍照上升沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送清除接收到间隙拍照请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"176",
                        "type":"short",
                        "content":"0"
                    },
                    "out":
                    {
                        "result":"R"
                    }
                },
                {
                    "name":"启动左间隙拍照流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@R",
                        "name":"左间隙拍照流程",
                        "para":""
                    }
                }
            ] 
        },
        {
            "name":"左收到间隙拍照上升沿流程",
            "command":
            [
                {
                    "name":"关闭上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左收到间隙拍照上升沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"清除间隙拍照结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"178",
                        "type":"short",
                        "content":"0"
                    }
                }
            ] 
        },
        {
            "name":"左请求角度检测上升沿流程",
            "command":
            [
                {
                    "name":"激活下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左请求角度检测下降沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送接收到角度检测请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"180",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"左请求角度检测下降沿流程",
            "command":
            [
                {
                    "name":"关闭下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左请求角度检测下降沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"激活上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左收到角度检测上升沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送清除接收到角度检测请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"180",
                        "type":"short",
                        "content":"0"
                    },
                    "out":
                    {
                        "result":"R"
                    }
                },
                {
                    "name":"启动左角度检测流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@R",
                        "name":"左角度检测流程",
                        "para":""
                    }
                }
            ] 
        },
        {
            "name":"左收到角度检测上升沿流程",
            "command":
            [
                {
                    "name":"关闭上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"左收到角度检测上升沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"清除角度检测结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"182",
                        "type":"short",
                        "content":"0"
                    }
                }
            ] 
        },
        //右PLC
        {
            "name":"右请求复位上升沿流程",
            "command":
            [
                {
                    "name":"激活下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右相机复位下降沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送接收到复位请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"156",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"右请求复位下降沿流程",
            "command":
            [
                {
                    "name":"关闭下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右相机复位下降沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"激活上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右收到相机复位上升沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送清除接收到复位请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"156",
                        "type":"short",
                        "content":"0"
                    },
                    "out":
                    {
                        "result":"R"
                    }
                },
                {
                    "name":"启动右复位流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@R",
                        "name":"右复位流程",
                        "para":""
                    }
                }
            ] 
        },
        {
            "name":"右收到复位上升沿流程",
            "command":
            [
                {
                    "name":"关闭上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右收到相机复位上升沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"清除复位结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"158",
                        "type":"short",
                        "content":"0"
                    }
                }
            ] 
        },
        {
            "name":"右请求开始上升沿流程",
            "command":
            [
                {
                    "name":"激活下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右请求开始下降沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送接收到开始请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"160",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"右请求开始下降沿流程",
            "command":
            [
                {
                    "name":"关闭下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右请求开始下降沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"激活上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右收到开始上升沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送清除接收到开始请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"160",
                        "type":"short",
                        "content":"0"
                    },
                    "out":
                    {
                        "result":"R"
                    }    
                },
                {
                    "name":"启动右开始流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@R",
                        "name":"右开始流程",
                        "para":""
                    }
                }
            ] 
        }, 
        {
            "name":"右收到开始上升沿流程",
            "command":
            [
                {
                    "name":"关闭上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右收到开始上升沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"清除开始结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"162",
                        "type":"short",
                        "content":"0"
                    }
                }
            ] 
        },
        {
            "name":"右请求排线检测上升沿流程",
            "command":
            [
                {
                    "name":"激活下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右请求排线检测下降沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送接收到排线检测请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"164",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"右请求排线检测下降沿流程",
            "command":
            [
                {
                    "name":"关闭下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右请求排线检测下降沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"激活上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右收到排线检测上升沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送清除接收到排线检测请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"164",
                        "type":"short",
                        "content":"0"
                    },
                    "out":
                    {
                        "result":"R"
                    }
                },
                {
                    "name":"启动右排线检测流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@R",
                        "name":"右排线检测流程",
                        "para":""
                    }
                }
            ] 
        },
        {
            "name":"右收到排线检测上升沿流程",
            "command":
            [
                {
                    "name":"关闭上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右收到排线检测上升沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"清除排线检测结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"166",
                        "type":"short",
                        "content":"0"
                    }
                }
            ] 
        },
        {
            "name":"右请求防水胶带检测上升沿流程",
            "command":
            [
                {
                    "name":"激活下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右请求防水胶带检测下降沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送接收到防水胶带检测请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"184",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"右请求防水胶带检测下降沿流程",
            "command":
            [
                {
                    "name":"关闭下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右请求防水胶带检测下降沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"激活上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右收到防水胶带检测上升沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送清除接收到防水胶带检测请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"184",
                        "type":"short",
                        "content":"0"
                    },
                    "out":
                    {
                        "result":"R"
                    }
                },
                {
                    "name":"启动右防水胶带检测流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@R",
                        "name":"右防水胶带检测流程",
                        "para":""
                    }
                }
            ] 
        },
        {
            "name":"右收到防水胶带检测上升沿流程",
            "command":
            [
                {
                    "name":"关闭上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右收到防水胶带检测上升沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"清除防水胶带检测结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"186",
                        "type":"short",
                        "content":"0"
                    }
                }
            ] 
        },
        {
            "name":"右请求拍屏幕上升沿流程",
            "command":
            [
                {
                    "name":"激活下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右请求拍屏幕下降沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送接收到拍屏幕请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"168",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"右请求拍屏幕下降沿流程",
            "command":
            [
                {
                    "name":"关闭下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右请求拍屏幕下降沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"激活上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右收到拍屏幕上升沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送清除接收到拍屏幕请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"168",
                        "type":"short",
                        "content":"0"
                    },
                    "out":
                    {
                        "result":"R"
                    }
                },
                {
                    "name":"推送标志位是否等于1",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"$右是否为盖板对位\",\"=1\"]"
                    },
                    "out":
                    {
                        "result":"推送标志位是否等于1结果",
                        "combine":"推送标志位是否等于1"
                    }
                },
                {
                    "name":"计算推送标志位是否等于1",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送标志位是否等于1" 
                    },
                    "out":
                    {
                        "result":"计算推送标志位是否等于1结果",
                        "value":"标志位是否等于1"
                    }
                },
                {
                    "name":"标志位是否等于1整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@标志位是否等于1"                
                    },
                    "out":
                    {
                        "result":"计算标志位是否等于1整型结果",
                        "integer":"标志位是否等于1整型"
                    }
                },
                {
                    "name":"判断标志位是否等于1",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"启动右拍屏幕盖板流程",
                        "condition":"@标志位是否等于1整型"
                    }
                },
                {
                    "name":"启动右拍屏幕流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@R",
                        "name":"右拍屏幕流程",
                        "para":""
                    }
                },
                {
                    "name":"结束跳转",
                    "type":"end"
                },
                {
                    "name":"启动右拍屏幕盖板流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@R",
                        "name":"右拍屏幕盖板流程",
                        "para":""
                    }
                }
            ] 
        },
        {
            "name":"右收到拍屏幕上升沿流程",
            "command":
            [
                {
                    "name":"关闭上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右收到拍屏幕上升沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"清除拍屏幕结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"170",
                        "type":"short",
                        "content":"0"
                    }
                }
            ] 
        },
        {
            "name":"右请求对位上升沿流程",
            "command":
            [
                {
                    "name":"激活下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右请求对位下降沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送接收到对位请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"172",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"右请求对位下降沿流程",
            "command":
            [
                {
                    "name":"关闭下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右请求对位下降沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"激活上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右收到对位上升沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送清除接收到对位请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"172",
                        "type":"short",
                        "content":"0"
                    },
                    "out":
                    {
                        "result":"R"
                    }
                },
                {
                    "name":"推送标志位是否等于1",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"$右是否为盖板对位\",\"=1\"]"
                    },
                    "out":
                    {
                        "result":"推送标志位是否等于1结果",
                        "combine":"推送标志位是否等于1"
                    }
                },
                {
                    "name":"计算推送标志位是否等于1",
                    "type":"calc_formula",
                    "in":
                    {
                        "formula":"@推送标志位是否等于1" 
                    },
                    "out":
                    {
                        "result":"计算推送标志位是否等于1结果",
                        "value":"标志位是否等于1"
                    }
                },
                {
                    "name":"标志位是否等于1整型",
                    "type":"float_to_integer",
                    "in":
                    {
                        "float":"@标志位是否等于1"                
                    },
                    "out":
                    {
                        "result":"计算标志位是否等于1整型结果",
                        "integer":"标志位是否等于1整型"
                    }
                },
                {
                    "name":"判断标志位是否等于1",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"启动右对位盖板流程",
                        "condition":"@标志位是否等于1整型"
                    }
                },
                {
                    "name":"启动右对位流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@R",
                        "name":"右对位流程",
                        "para":""
                    }
                },
                {
                    "name":"结束跳转",
                    "type":"end"
                },
                {
                    "name":"启动右对位盖板流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@R",
                        "name":"右对位盖板流程",
                        "para":""
                    }
                } 
            ] 
        },
        {
            "name":"右收到对位上升沿流程",
            "command":
            [
                {
                    "name":"关闭上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右收到对位上升沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"清除对位结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"174",
                        "type":"short",
                        "content":"0"
                    }
                }
            ] 
        },
        {
            "name":"右请求间隙拍照上升沿流程",
            "command":
            [
                {
                    "name":"激活下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右请求间隙拍照下降沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送接收到间隙拍照请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"176",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"右请求间隙拍照下降沿流程",
            "command":
            [
                {
                    "name":"关闭下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右请求间隙拍照下降沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"激活上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右收到间隙拍照上升沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送清除接收到间隙拍照请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"176",
                        "type":"short",
                        "content":"0"
                    },
                    "out":
                    {
                        "result":"R"
                    }
                },
                {
                    "name":"启动右间隙拍照流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@R",
                        "name":"右间隙拍照流程",
                        "para":""
                    }
                }
            ] 
        },
        {
            "name":"右收到间隙拍照上升沿流程",
            "command":
            [
                {
                    "name":"关闭上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右收到间隙拍照上升沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"清除间隙拍照结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"178",
                        "type":"short",
                        "content":"0"
                    }
                }
            ] 
        },
        {
            "name":"右请求角度检测上升沿流程",
            "command":
            [
                {
                    "name":"激活下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右请求角度检测下降沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送接收到角度检测请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"180",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"右请求角度检测下降沿流程",
            "command":
            [
                {
                    "name":"关闭下降沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右请求角度检测下降沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"激活上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右收到角度检测上升沿",
                        "enabled":"1"
                    }
                },
                {
                    "name":"发送清除接收到角度检测请求",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"180",
                        "type":"short",
                        "content":"0"
                    },
                    "out":
                    {
                        "result":"R"
                    }
                },
                {
                    "name":"启动右角度检测流程",
                    "type":"run_sequence",
                    "in":
                    {
                        "wait":"@R",
                        "name":"右角度检测流程",
                        "para":""
                    }
                }
            ] 
        },
        {
            "name":"右收到角度检测上升沿流程",
            "command":
            [
                {
                    "name":"关闭上升沿",
                    "type":"set_trigger_enabled",
                    "in":
                    {
                        "type":"s7client",
                        "name":"右收到角度检测上升沿",
                        "enabled":"0"
                    }
                },
                {
                    "name":"清除角度检测结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"182",
                        "type":"short",
                        "content":"0"
                    }
                }
            ] 
        },
         //算法
        //复位
        {
            "name":"左复位流程",
            "command":
            [
                {
                    "name":"发送复位结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"158",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"右复位流程",
            "command":
            [
                {
                    "name":"发送复位结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"158",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        //开始
        {
            "name":"左开始流程",
            "command":
            [
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"\\\\|/"
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取字符串长度",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"0"
                    },
                    "out":
                    {
                        "result":"获取字符串长度结果",
                        "string":"获取字符串长度"
                    }
                },
                {
                    "name":"清除对位次数",
                    "type":"set_global_integer",
                    "in":
                    {
                        "input":"0",
                        "name":"左对位次数"
                    },
                    "out":
                    {
                        "result":"清除对位次数结果"
                    }
                },
                {
                    "name":"生成调试文件夹",
                    "type":"creat_folder",
                    "in":
                    {
                        "path":"$左调试文件夹"
                    },
                    "out":
                    {
                        "result":"生成调试文件夹",
                        "full_path":"调试文件夹"
                    }
                },
                {
                    "name":"离线模式文件夹名称有变化",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"生成文件夹名称",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"生成文件夹",
                    "type":"creat_unique_folder",
                    "in":
                    {
                        "path":"inspection/L",
                        "length":"3"
                    },
                    "out":
                    {
                        "result":"生成文件夹结果",
                        "full_path":"文件夹"
                    }
                },
                {
                    "name":"跳转保存文件夹路径",
                    "type":"jump",
                    "in":
                    {
                        "tag":"保存文件夹路径"
                    }
                },
                {
                    "name":"生成文件夹名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"inspection/L/\",\"@获取字符串长度\"]"
                    },
                    "out":
                    {
                        "result":"生成文件夹名称结果",
                        "combine":"生成文件夹名称"
                    }
                },
                {
                    "name":"生成文件夹2",
                    "type":"creat_folder",
                    "in":
                    {
                        "path":"@生成文件夹名称"
                    },
                    "out":
                    {
                        "result":"生成文件夹2结果",
                        "full_path":"文件夹"
                    }
                },
                {
                    "name":"保存文件夹路径",
                    "type":"set_global_string",
                    "in":
                    {
                        "input":"@文件夹",
                        "name":"左图片文件夹"
                    },
                    "out":
                    {
                        "result":"保存文件夹路径结果"
                    }
                },
                {
                    "name":"读取壳体SN",
                    "type":"read_s7_client",
                    "in":
                    {
                        "client":"左PLC",
                        "db":"520",
                        "offset":"108",
                        "type":"string"
                    },
                    "out":
                    {
                        "result":"读取壳体SN结果",
                        "content":"壳体SN码"
                    }
                },
                {
                    "name":"读取屏幕SN",
                    "type":"read_s7_client",
                    "in":
                    {
                        "wait":"@读取壳体SN结果",
                        "client":"左PLC",
                        "db":"520",
                        "offset":"86",
                        "type":"string"
                    },
                    "out":
                    {
                        "result":"读取屏幕SN结果",
                        "content":"屏幕SN码"
                    }
                },
                {
                    "name":"生成显示信息",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"左读取壳体SN码:\",\"@壳体SN码\",\",屏幕SN码:\",\"@屏幕SN码\"]"
                    },
                    "out":
                    {
                        "result":"生成显示信息结果",
                        "combine":"显示信息"
                    }
                },
                {
                    "name":"显示码信息",
                    "type":"log_output",
                    "in":
                    {
                        "input":"@显示信息",
                        "level":"info"
                    }
                },
                {
                    "name":"保存SN码",
                    "type":"set_global_string",
                    "in":
                    {
                        "input":"@屏幕SN码",
                        "name":"左SN码"
                    },
                    "out":
                    {
                        "result":"保存SN码结果"
                    }
                },
                {
                    "name":"发送开始结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "wait":"@保存SN码结果",
                        "client":"左PLC",
                        "db":"520",
                        "offset":"162",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        },
        {
            "name":"右开始流程",
            "command":
            [
                {
                    "name":"分割触发字符",
                    "type":"split_string",
                    "in":
                    {
                        "string":"@TRIGGER_STRING",
                        "splitter":"\\\\|/"
                    },
                    "out":
                    {
                        "result":"分割触发字符结果",
                        "split":"字符分割"
                    }
                },
                {
                    "name":"获取字符串长度",
                    "type":"get_string",
                    "in":
                    {
                        "vstring":"@字符分割",
                        "index":"0"
                    },
                    "out":
                    {
                        "result":"获取字符串长度结果",
                        "string":"获取字符串长度"
                    }
                },
                {
                    "name":"清除对位次数",
                    "type":"set_global_integer",
                    "in":
                    {
                        "input":"0",
                        "name":"右对位次数"
                    },
                    "out":
                    {
                        "result":"清除对位次数结果"
                    }
                },
                {
                    "name":"生成调试文件夹",
                    "type":"creat_folder",
                    "in":
                    {
                        "path":"$右调试文件夹"
                    },
                    "out":
                    {
                        "result":"生成调试文件夹",
                        "full_path":"调试文件夹"
                    }
                },
                {
                    "name":"离线模式文件夹名称有变化",
                    "type":"jump_if",
                    "in":
                    {
                        "tag":"生成文件夹名称",
                        "condition":"@获取字符串长度结果"
                    }
                },
                {
                    "name":"生成文件夹",
                    "type":"creat_unique_folder",
                    "in":
                    {
                        "path":"inspection/R",
                        "length":"3"
                    },
                    "out":
                    {
                        "result":"生成文件夹结果",
                        "full_path":"文件夹"
                    }
                },
                {
                    "name":"跳转保存文件夹路径",
                    "type":"jump",
                    "in":
                    {
                        "tag":"保存文件夹路径"
                    }
                },
                {
                    "name":"生成文件夹名称",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"inspection/R/\",\"@获取字符串长度\"]"
                    },
                    "out":
                    {
                        "result":"生成文件夹名称结果",
                        "combine":"生成文件夹名称"
                    }
                },
                {
                    "name":"生成文件夹2",
                    "type":"creat_folder",
                    "in":
                    {
                        "path":"@生成文件夹名称"
                    },
                    "out":
                    {
                        "result":"生成文件夹2结果",
                        "full_path":"文件夹"
                    }
                },
                {
                    "name":"保存文件夹路径",
                    "type":"set_global_string",
                    "in":
                    {
                        "input":"@文件夹",
                        "name":"右图片文件夹"
                    },
                    "out":
                    {
                        "result":"保存文件夹路径结果"
                    }
                },
                {
                    "name":"读取壳体SN",
                    "type":"read_s7_client",
                    "in":
                    {
                        "client":"右PLC",
                        "db":"520",
                        "offset":"108",
                        "type":"string"
                    },
                    "out":
                    {
                        "result":"读取壳体SN结果",
                        "content":"壳体SN码"
                    }
                },
                {
                    "name":"读取屏幕SN",
                    "type":"read_s7_client",
                    "in":
                    {
                        "wait":"@读取壳体SN结果",
                        "client":"右PLC",
                        "db":"520",
                        "offset":"86",
                        "type":"string"
                    },
                    "out":
                    {
                        "result":"读取屏幕SN结果",
                        "content":"屏幕SN码"
                    }
                },
                {
                    "name":"生成显示信息",
                    "type":"combine_string",
                    "in":
                    {
                        "string":"[\"右读取壳体SN码:\",\"@壳体SN码\",\",屏幕SN码:\",\"@屏幕SN码\"]"
                    },
                    "out":
                    {
                        "result":"生成显示信息结果",
                        "combine":"显示信息"
                    }
                },
                {
                    "name":"显示码信息",
                    "type":"log_output",
                    "in":
                    {
                        "input":"@显示信息",
                        "level":"info"
                    }
                },
                {
                    "name":"保存SN码",
                    "type":"set_global_string",
                    "in":
                    {
                        "input":"@屏幕SN码",
                        "name":"右SN码"
                    },
                    "out":
                    {
                        "result":"保存SN码结果"
                    }
                },
                {
                    "name":"发送开始结果",
                    "type":"send_s7_client",
                    "in":
                    {
                        "wait":"@保存SN码结果",
                        "client":"右PLC",
                        "db":"520",
                        "offset":"162",
                        "type":"short",
                        "content":"1"
                    }
                }
            ] 
        }
    ]
}
