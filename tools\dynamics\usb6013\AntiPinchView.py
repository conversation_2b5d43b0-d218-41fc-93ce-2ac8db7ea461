import ctypes
import os
import threading
import time
from queue import Queue

import numpy as np
import pyqtgraph as pg
from PyQt5.QtCore import QTimer, Qt, pyqtSignal
from PyQt5.QtGui import QPixmap, QPainter
from PyQt5.QtWidgets import QWidget, QApplication, QVBoxLayout, QLCDNumber

from common import is_windows_platform
from common.LogUtils import logger
from common.view.MessageDialog import MessageDialog
from tools.dynamics.usb6013.ui.usb6103 import Ui_FormUSB6103


class AntiPinchWindow(QWidget, Ui_FormUSB6103):
    update_peeling = pyqtSignal(float)
    update_power = pyqtSignal(float)

    def __init__(self):
        super(AntiPinchWindow, self).__init__()
        self.setupUi(self)
        usb_dll_path = os.path.join(os.getcwd(), "adb/dll", "USB6103.dll")
        # usb_dll_path = r'D:\work\HWTreeATE\adb\dll\USB6103.dll'

        if is_windows_platform():
            # usb_dll_path = r'D:\work\HWTreeATE\adb\dll\USB6103.dll'
            self.DAQdll = ctypes.cdll.LoadLibrary(usb_dll_path)
        else:
            self.DAQdll = None
        self.stop_collect = True
        self.pushButtonConnect.clicked.connect(self.connect_usb)
        self.pushButtonStart.clicked.connect(self.start_collect_data)
        self.pushButtonEnd.clicked.connect(self.stop_collect_data)
        self.pushButtonSaveImage.clicked.connect(self.plot_widget_to_image)
        self.connected = False
        self.peeling = False
        self.data_q = Queue()
        # 初始化图表
        self.init_plot()
        self.data = []
        # 设置定时器来更新图表
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_plot)
        self.timer.start(50)  # 每100ms更新一次
        self.pushButtonPeeling.clicked.connect(self.peeling_start)
        self.update_peeling.connect(self.update_peeling_value)
        self.update_power.connect(self.update_power_value)
        self.doubleSpinBoxPeeling.setEnabled(False)
        self.doubleSpinBoxProportion.setEnabled(False)
        self.lcdNumber.setDigitCount(5)
        self.lcdNumber.setMode(QLCDNumber.Dec)
        self.lcdNumber.setSegmentStyle(QLCDNumber.Flat)

    def update_power_value(self, value):
        value = round(value, 1)
        self.lcdNumber.display(f"{value:.1f}")

    def update_peeling_value(self, value):
        self.doubleSpinBoxPeeling.setValue(value)

    def peeling_end(self):
        self.peeling = False
        self.stop_collect_data()
        MessageDialog.show_message("提示", "剥皮完成！可以开始测试")

    def peeling_start(self):
        self.peeling = True
        self.pushButtonStart.click()
        QTimer.singleShot(2000, self.peeling_end)

    def init_plot(self):
        # 创建 PyQtGraph 绘图窗口
        self.plot_widget = pg.PlotWidget()
        # 将口添加到 widget_display
        layout = QVBoxLayout()
        layout.addWidget(self.plot_widget)
        self.widget_display.setLayout(layout)
        # 设置绘图项
        self.curve = self.plot_widget.plot(pen='r', symbolBrush='r', symbolPen='r')
        # 存储最近的数据点
        self.data_buffer = np.zeros(10000)

    def plot_widget_to_image(self):
        # 获取 PlotWidget 的尺寸
        width = self.plot_widget.width()
        height = self.plot_widget.height()

        # 创建一个 QPixmap 来渲染 PlotWidget
        pixmap = QPixmap(width, height)
        pixmap.fill(Qt.white)  # 填充白色背景

        # 创建 QPainter 来绘制 PlotWidget 到 QPixmap
        painter = QPainter(pixmap)
        self.plot_widget.render(painter)
        painter.end()
        # 将 QPixmap 转换为 QImage
        image = pixmap.toImage()

        # 获取当前时间
        T = time.strftime("%Y_%m_%d_%H_%M_%S", time.localtime())
        # 点击弹框保存
        # 保存图像
        if not os.path.exists(os.path.join(os.getcwd(), "img")):
            os.mkdir(os.path.join(os.getcwd(), "img"))
        path = os.path.join(os.getcwd(), "img", f"plot_image_{T}.png")
        image.save(path)
        MessageDialog.show_message("提示", f"图像保存成功！\n{path}")
        return image

    def update_plot(self):
        # 从队列中获取数据
        while not self.data_q.empty():
            new_data = self.data_q.get()
            self.data.extend(new_data)
            # 将新数据添加到缓冲区
            self.data_buffer = np.roll(self.data_buffer, -len(new_data))
            # print(new_data)
            self.data_buffer[-len(new_data):] = new_data
            self.update_power.emit(np.mean(new_data))
        # 如果 peeling 模式激活, 计算平均值并更新到界面
        if self.peeling:
            if len(self.data) > 0:
                average_value = np.mean(self.data)
                # 保留一位小数
                average_value = round(average_value, 1)
                # 使用信号更新UI
                self.update_peeling.emit(average_value)

        # 更新图表
        if not self.stop_collect:
            self.curve.setData(self.data_buffer)
            self.plot_widget.setXRange(0, self.data_buffer.size)

    def stop_collect_data(self):
        self.pushButtonStart.setEnabled(True)
        self.stop_collect = True
        # 将self.data更新到窗口上
        self.update_plot_with_all_data(peeling=True)
        # 显示出某一段力值区间的时间
        # self.stop_collect = False

    def start_collect_data(self):
        self.pushButtonStart.setEnabled(False)
        self.stop_collect = False
        self.data.clear()
        self.data_buffer = np.zeros(10000)
        if not self.connected:
            # 弹窗提示
            MessageDialog.show_message("提示", "未连接设备！")
            return
        threading.Thread(target=self.collect_data).start()

    def collect_data(self):
        Proportion = self.doubleSpinBoxProportion.value()
        Peeling = self.doubleSpinBoxPeeling.value()

        # 配置采集参数
        dev = 0  # 设备号
        chan = self.spinBoxChannel.value()  # 通道号
        gain = 2  # 量程 (±10.24V)
        rate = self.spinBoxFrequency.value()  # 采样频率 (1kHz)
        # 启动连续采集
        erro = self.DAQdll.ADContinuConfigV6103(dev, chan, gain, rate)
        if erro != 0:
            # print("配置连续采集失败")
            self.DAQdll.CloseUsbV6103()
            return
        try:
            while not self.stop_collect:
                # 查询缓冲区数据量
                buffer_size = self.DAQdll.GetAdBuffSizeV6103(dev)
                if buffer_size > 0:
                    # 准备读取数据的缓冲区
                    data_buffer = (ctypes.c_float * buffer_size)()
                    # 读取数据
                    read_count = self.DAQdll.ReadAdBuffV6103(dev, data_buffer, buffer_size)
                    if read_count > 0:
                        # 用 numpy 对数据进行处理
                        np_data_buffer = np.frombuffer(data_buffer, dtype=np.float32)[:read_count]
                        processed_data = np_data_buffer * Proportion - Peeling
                        # 将处理后的数据放入队列
                        # self.data_q.put(processed_data.tolist())
                        # 处理数据 (这里只是简单打印，您可以根据需要进行数据处理)

                        self.data_q.put(processed_data)

                # 短暂休眠，避免过度占用CPU
                # time.sleep(0.005)

        except KeyboardInterrupt:
            logger.info("用户中断，停止采集")

        finally:
            # 停止采集
            self.DAQdll.ADContinuStopV6103(dev)
            logger.info("采集已停止")
        #     # 关闭设备
        #     self.DAQdll.CloseUsbV6103()

        logger.info("程序结束")

    def closeEvent(self, a0):
        # 关闭设备
        self.DAQdll.CloseUsbV6103()
        super(AntiPinchWindow, self).closeEvent(a0)

    def set_val(self):
        DoSetV6103 = self.DAQdll.DoSetV6103
        DoSetV6103.argtypes = [ctypes.c_int, ctypes.c_ubyte, ctypes.c_ubyte]
        DoSetV6103.restype = ctypes.c_int
        for i in range(8):
            result = DoSetV6103(0, i, 1)
            if result == 0:
                self.labelStatus.setText("连接成功")
            else:
                logger.info(f"设置失败，错误代码：{result}")

    def update_plot_with_all_data(self,peeling=False):
        # 确保所有数据都从队列中取出
        while not self.data_q.empty():
            new_data = self.data_q.get()
            self.data.extend(new_data)
        if len(self.data) > 10000:
            self.data = self.data[-10000:]
        if peeling: # 去皮使用以后2s采集的数据
            self.data = self.data[-1500:]

        all_data = np.array(self.data)
        # 更新图表
        self.plot_widget.clear()  # 清除现有的图表
        self.curve = self.plot_widget.plot(all_data, pen='r', symbolBrush='r', symbolPen='r')
        self.plot_widget.setXRange(0, len(all_data))
        y_min, y_max = all_data.min(), all_data.max()
        y_range = y_max - y_min
        self.plot_widget.setYRange(y_min - 0.1 * y_range, y_max + 0.1 * y_range)

        # 更新图表标题
        mask = all_data > 20
        regions = self.find_contiguous_regions(mask)
        if len(regions) == 0:
            return
        start = min([x[0] for x in regions])
        end = max([x[1] for x in regions])
        region_item = pg.LinearRegionItem(values=[start, end], movable=False, brush=pg.mkBrush(0, 255, 0, 50))
        self.plot_widget.addItem(region_item)
        rate = self.spinBoxFrequency.value()
        self.plot_widget.setTitle(
            f"Max: {y_max:.2f}N, Length: {len(all_data)}, PowerTime: {1000 * (end - start) / rate:.2f}ms",
            size="16pt", )
        # title = self.plot_widget.titleLabel
        # title.setHtml(f'<span style="color: red; font-size: 16pt;">Max: {y_max:.2f}N, Length: {len(all_data)}, PowerTime: {1000 * (end - start) / rate:.2f}ms</span>')

    def find_contiguous_regions(self, mask):
        # 找到连续的True区域
        region_edges = np.diff(mask.astype(int))
        region_starts = np.where(region_edges == 1)[0] + 1
        region_ends = np.where(region_edges == -1)[0] + 1

        if mask[0]:
            region_starts = np.insert(region_starts, 0, 0)
        if mask[-1]:
            region_ends = np.append(region_ends, len(mask))

        return list(zip(region_starts, region_ends))

    def connect_usb(self):
        if not self.DAQdll:
            # 弹窗提示
            MessageDialog.show_message("提示", "USB6103.dll加载错误!")
            return
        erro = self.DAQdll.OpenUsbV6103()
        if erro != 0:
            MessageDialog.show_message("提示", "连接设备错误!请检查设备连接")
            return
        self.connected = True
        threading.Thread(target=self.set_val).start()


if __name__ == '__main__':
    import sys

    app = QApplication(sys.argv)
    window = AntiPinchWindow()
    window.show()
    sys.exit(app.exec_())
