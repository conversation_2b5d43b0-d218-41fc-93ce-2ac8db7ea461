# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'Image.ui'
#
# Created by: PyQt5 UI code generator 5.15.0
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import Qt<PERSON><PERSON>, QtGui, QtWidgets


class Ui_FormImage(object):
    def setupUi(self, FormImage):
        FormImage.setObjectName("FormImage")
        FormImage.resize(400, 300)
        self.verticalLayout = QtWidgets.QVBoxLayout(FormImage)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName("verticalLayout")

        self.retranslateUi(FormImage)
        QtCore.QMetaObject.connectSlotsByName(FormImage)

    def retranslateUi(self, FormImage):
        _translate = QtCore.QCoreApplication.translate
        FormImage.setWindowTitle(_translate("FormImage", "ImageView"))
