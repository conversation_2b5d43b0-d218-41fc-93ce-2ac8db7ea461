%YAML:1.0
---
size: !!opencv-matrix
   rows: 2
   cols: 1
   dt: d
   data: [ 1920., 1080. ]
K1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.7316850748608681e+02, 0., 1.0153992472247509e+03, 0.,
       9.7270009904360234e+02, 5.4394621173755422e+02, 0., 0., 1. ]
D1: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ -8.8979778830713362e-03, 5.0989751329226445e-02,
       1.0907337924711795e-04, -3.5237902664253980e-05,
       -4.4590513266274792e-02 ]
K2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.7092910073954715e+02, 0., 9.8307135262577754e+02, 0.,
       9.7049279111363387e+02, 5.9207513094092030e+02, 0., 0., 1. ]
D2: !!opencv-matrix
   rows: 1
   cols: 5
   dt: d
   data: [ -6.9037446971241187e-03, 3.7043864434653039e-02,
       5.0854774328073951e-04, 2.3096951321992072e-04,
       -2.9308799942011788e-02 ]
R: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.9907709407367673e-01, 4.2951361207383021e-02,
       -3.7505696972983107e-04, -4.2951491620809704e-02,
       9.9907709865048444e-01, -3.4687126952613883e-04,
       3.5981223595644074e-04, 3.6266039626849968e-04,
       9.9999986950628739e-01 ]
T: !!opencv-matrix
   rows: 3
   cols: 1
   dt: d
   data: [ -1.4286935459564265e+02, -1.8534373337218097e+00,
       -9.5587852479683633e-01 ]
E: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ -4.1723297879574843e-02, 9.5432417489842203e-01,
       -1.8537686586573001e+00, -9.0359019691474440e-01,
       1.0756772963351216e-02, 1.4286969446099309e+02,
       7.9881786722076846e+00, -1.4265789261908600e+02,
       4.8862129815000821e-02 ]
F: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 3.1837411857770715e-09, -7.2855801266148823e-08,
       1.7405526597434262e-04, 6.8980421212688376e-08,
       -8.2157160333251276e-10, -1.0683679126053060e-02,
       -6.3579794734193690e-04, 1.0646406646717919e-02, 1. ]
R1: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.9841593752396518e-01, 5.5908811554143942e-02,
       6.3103477514877748e-03, -5.5908152230470590e-02,
       9.9843587654268873e-01, -2.8097438900034503e-04,
       -6.3161865327120426e-03, -7.2270574703383662e-05,
       9.9998005008332447e-01 ]
R2: !!opencv-matrix
   rows: 3
   cols: 3
   dt: d
   data: [ 9.9989348635812070e-01, 1.2971570583538398e-02,
       6.6898651106751814e-03, -1.2972268908256037e-02,
       9.9991585471882771e-01, 6.1002361038410414e-05,
       -6.6885108936624678e-03, -1.4777859263041164e-04,
       9.9997762074134089e-01 ]
P1: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ 8.2682840085766622e+02, 0., 1.0029198913574219e+03, 0., 0.,
       8.2682840085766622e+02, 5.6870051574707031e+02, 0., 0., 0., 1.,
       0. ]
P2: !!opencv-matrix
   rows: 3
   cols: 4
   dt: d
   data: [ 8.2682840085766622e+02, 0., 1.0029198913574219e+03,
       -1.1814102362256346e+05, 0., 8.2682840085766622e+02,
       5.6870051574707031e+02, 0., 0., 0., 1., 0. ]
Q: !!opencv-matrix
   rows: 4
   cols: 4
   dt: d
   data: [ 1., 0., 0., -1.0029198913574219e+03, 0., 1., 0.,
       -5.6870051574707031e+02, 0., 0., 0., 8.2682840085766622e+02, 0.,
       0., 6.9986561441960646e-03, 0. ]
