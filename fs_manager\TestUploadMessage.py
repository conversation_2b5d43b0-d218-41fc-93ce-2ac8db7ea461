class TestUploadMessage:

    def __init__(self, msg_id: str):
        super(TestUploadMessage, self).__init__()
        self._msg_id = msg_id

    @property
    def msg_id(self):
        return self._msg_id


class TestRecordMessage(TestUploadMessage):
    MSG_ID = 'TestRecordMessage'

    def __init__(self, test_record_id, order, project_number, project_name, test_plan_name, test_plan_id,
                 case_number, case_name, case_id, case_version, start_time, end_time, machine_number, result,
                 steps, file_date, can_msgs, mcu_msgs, soc_msgs, os_msgs, vds_app_msgs, function_test_result):
        super(TestRecordMessage, self).__init__(msg_id=TestRecordMessage.MSG_ID)
        self.test_record_id = test_record_id
        self.project_number = project_number
        self.project_name = project_name
        self.test_plan_name = test_plan_name
        self.test_plan_id = test_plan_id
        self.case_number = case_number
        self.case_name = case_name
        self.case_id = case_id
        self.case_version = case_version
        self.start_time = start_time
        self.end_time = end_time
        self.machine_number = machine_number
        self.result = result
        self.steps = steps
        self.order = order
        self.file_date = file_date
        self.can_msgs = can_msgs
        self.mcu_msgs = mcu_msgs
        self.soc_msgs = soc_msgs
        self.os_msgs =  os_msgs
        self.vds_app_msgs = vds_app_msgs
        self.function_test_result = function_test_result


class TestRecordResourceMessage(TestUploadMessage):
    MSG_ID = 'TestRecordResourceMessage'

    def __init__(self, category: int, device: str, action: str, status: int):
        super(TestRecordResourceMessage, self).__init__(msg_id=TestRecordResourceMessage.MSG_ID)
        self._category = category
        self._device = device
        self._action = action
        self._status = status

    @property
    def category(self):
        return self._category

    @property
    def device(self):
        return self._device

    @property
    def action(self):
        return self._action

    @property
    def status(self):
        return self._status
