import binascii

import serial
import serial.tools.list_ports

from common.LogUtils import logger

S_OPEN = 0
S_CLOSE = 1
S_SLOW_FLASH = 2
S_FAST_FLASH = 3

STATUS_MAP = {
    S_OPEN: "开",
    S_CLOSE: "关",
    S_SLOW_FLASH: "慢闪",
    S_FAST_FLASH: "快闪",
}


class TriColorLight:
    def __init__(self):
        self.ser = None
        self.red_light_status = None
        self.green_light_status = None
        self.yellow_light_status = None
        self.buzzer_status = None

        self._is_open = False

    def open(self, port):
        if self._is_open:
            return True
        try:
            self.ser = serial.Serial(
                port=port, baudrate=9600, parity='E', timeout=3
            )
            self._is_open = True
        except Exception as e:
            logger.error(f"open exception: {str(e.args)}")
            return False
        return True

    def is_open(self):
        return self._is_open

    def close(self):
        self._is_open = False
        if self.ser:
            try:
                self.ser.close()
            except Exception as e:
                logger.error(f"close exception: {str(e.args)}")
        self.ser = None
        return True

    def send(self, cmd):
        cmd = binascii.unhexlify(cmd)
        if self.ser is not None:
            self.ser.write(cmd)

    def recv(self):
        tmp = self.ser.read(8)
        tmp = binascii.hexlify(tmp)
        return tmp.upper()

    def query(self, cmd):
        self.ser.reset_input_buffer()
        self.send(cmd)
        return self.recv()

    def red_open(self):
        self.send(b"01050001FF00DDFA")

    def red_close(self):
        self.send(b"0105000100009C0A")

    def yellow_open(self):
        self.send(b"01050002FF002DFA")

    def yellow_close(self):
        self.send(b"0105000200006C0A")

    def green_open(self):
        self.send(b"01050003FF007C3A")

    def green_close(self):
        self.send(b"0105000300003DCA")

    def buzzer_open(self):
        self.send(b"01050004FF00CDFB")

    def buzzer_close(self):
        self.send(b"0105000400008C0B")

    def all_close(self):
        self.send(b"010500000000CDCA")

    def red_slow_flash(self):
        self.send(b"01050016FF006DFE")

    def red_fast_flash(self):
        self.send(b"01050016FF006DFE")

    def get_red_status(self):
        r = self.query(b"010100010001AC0A")
        if r == b"0101000100FF2D8A":
            s = S_OPEN
        elif r == b"0101000100006DCA":
            s = S_CLOSE
        elif r == b"010100010011FC06":
            s = S_SLOW_FLASH
        elif r == b"010100010022BC13":
            s = S_FAST_FLASH
        else:
            s = None

        return STATUS_MAP.get(s, "未知")

    def get_green_status(self):
        r = self.query(b"0101000200015C0A")
        if r == b"0101000200FFDD8A":
            s = S_OPEN
        elif r == b"0101000200009DCA":
            s = S_CLOSE
        elif r == b"0101000200115DC6":
            s = S_SLOW_FLASH
        elif r == b"0101000200221DD3":
            s = S_FAST_FLASH
        else:
            s = None

        return STATUS_MAP.get(s, "未知")

    def get_yellow_status(self):
        r = self.query(b"010100040001BC0B")
        if r == b"0101000400FF3D8B":
            s = S_OPEN
        elif r == b"0101000400007DCB":
            s = S_CLOSE
        elif r == b"010100040011BDC7":
            s = S_SLOW_FLASH
        elif r == b"010100040022FDD2":
            s = S_FAST_FLASH
        else:
            s = None

        return STATUS_MAP.get(s, "未知")

    def get_buzzer_status(self):
        r = self.query(b"0101000600011DCB")
        if r == b"0101000600FF9C4B":
            s = S_OPEN
        elif r == b"010100060000DC0B":
            s = S_CLOSE
        elif r == b"0101000600111C07":
            s = S_SLOW_FLASH
        elif r == b"0101000600225C12":
            s = S_FAST_FLASH
        else:
            s = None

        return STATUS_MAP.get(s, "未知")

    def status_monitor(self):
        if not self._is_open:
            return

        r = self.query(b"010100010001AC0A")
        if r == b"0101000100FF2D8A":
            self.red_light_status = S_OPEN
        elif r == b"0101000100006DCA":
            self.red_light_status = S_CLOSE
        elif r == b"010100010011FC06":
            self.red_light_status = S_SLOW_FLASH
        elif r == b"010100010022BC13":
            self.red_light_status = S_FAST_FLASH

        r = self.query(b"0101000200015C0A")
        if r == b"0101000200FFDD8A":
            self.green_light_status = S_OPEN
        elif r == b"0101000200009DCA":
            self.green_light_status = S_CLOSE
        elif r == b"0101000200115DC6":
            self.green_light_status = S_SLOW_FLASH
        elif r == b"0101000200221DD3":
            self.green_light_status = S_FAST_FLASH

        r = self.query(b"010100040001BC0B")
        if r == b"0101000400FF3D8B":
            self.yellow_light_status = S_OPEN
        elif r == b"0101000400007DCB":
            self.yellow_light_status = S_CLOSE
        elif r == b"010100040011BDC7":
            self.yellow_light_status = S_SLOW_FLASH
        elif r == b"010100040022FDD2":
            self.yellow_light_status = S_FAST_FLASH

        r = self.query(b"0101000600011DCB")
        if r == b"0101000600FF9C4B":
            self.buzzer_status = S_OPEN
        elif r == b"010100060000DC0B":
            self.buzzer_status = S_CLOSE
        elif r == b"0101000600111C07":
            self.buzzer_status = S_SLOW_FLASH
        elif r == b"0101000600225C12":
            self.buzzer_status = S_FAST_FLASH

    def open_color_yellow(self):
        self.red_close()
        self.green_close()
        self.yellow_open()

    def open_color_green(self):
        self.red_close()
        self.yellow_close()
        self.green_open()

    def open_color_red(self):
        self.yellow_close()
        self.green_close()
        self.red_open()


tri_color_light: TriColorLight = TriColorLight()
