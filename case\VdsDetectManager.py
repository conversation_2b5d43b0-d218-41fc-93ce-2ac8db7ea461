# -*- coding: utf-8 -*-
"""
<AUTHOR> jid
@Date   : 2024/3/22 16:38
@Desc   : 基于VDS的adb forward交互模块
"""
import time

import operator

from common.LogUtils import logger
from control_board.touchPointTest import touch_card
from touch.TouchManager import touch_manager
from utils.SignalsManager import signals_manager


class VdsDetectManager:

    def __init__(self):
        super().__init__()
        self.expect_software_version = {}
        self.expect_hardware_version = {}
        self.expect_inner_software_version = {}
        self.expect_inner_hardware_version = {}
        self.expect_part_number = {}
        self.expect_ld_version = {}
        self.expect_tddi_version = {}
        self.expect_tcon_version = {}
        self.expect_boot_version = {}
        self.expect_tp_version = {}
        self.expect_assembly_version = {}
        self.expect_hwsn = {}
        self.expect_psn = {}
        self.expect_screen_temp = {}
        self.expect_pcb_temp = {}
        self.expect_brightness = {}
        self.expect_work_voltage = {}
        self.expect_test_tpcm = {}
        self.expect_touch_resp_times = {}
        self.expect_test_touch_resp_time = {}
        self.expect_back_light_status = {}
        self.expect_bist_pattern_status = {}
        self.expect_serial_protocol_data = {}
        self.expect_end_touch_points_count = 0
        self.resp_screen_temp = None
        self.resp_color_temp = None
        self.resp_light_sensor = -1
        self.resp_light_r = 0
        self.resp_light_g = 0
        self.resp_light_b = 0
        self.case_number  = 0
        self.command  = None

    def set_expect_back_light_status(self, case_number, command, info):
        logger.info(f"set_expect_back_light_status case_number={case_number}, command={command}, info={info}")
        self.expect_back_light_status.update({case_number: [command, info]})

    def set_expect_bist_pattern_status(self, case_number, command, info):
        logger.info(f"set_expect_bist_pattern_status case_number={case_number}, command={command}, info={info}")
        self.expect_bist_pattern_status.update({case_number: [command, info]})

    def set_expect_test_touch_resp_time(self, case_number, command, info, time__):
        logger.info(f"set_expect_test_touch_resp_time case_number={case_number}, command={command}, info={info}")
        self.expect_test_touch_resp_time.update({case_number: [command, info, time__]})

    def set_expect_touch_resp_times(self, case_number, command, info):
        logger.info(f"set_expect_touch_resp_times case_number={case_number}, command={command}, info={info}")
        self.expect_touch_resp_times.update({case_number: [command, info]})

    def set_expect_software_version(self, case_number, command, info):
        logger.info(f"set_expect_software_version case_number={case_number}, command={command}, info={info}")
        self.expect_software_version.update({case_number: [command, info]})

    def set_expect_hardware_version(self, case_number, command, info):
        self.expect_hardware_version.update({case_number: [command, info]})

    def set_expect_inner_software_version(self, case_number, command, info):
        self.expect_inner_software_version.update({case_number: [command, info]})

    def set_expect_inner_hardware_version(self, case_number, command, info):
        self.expect_inner_hardware_version.update({case_number: [command, info]})

    def set_expect_part_number(self, case_number, command, info):
        self.expect_part_number.update({case_number: [command, info]})

    def set_expect_ld_version(self, case_number, command, info):
        self.expect_ld_version.update({case_number: [command, info]})

    def set_expect_tddi_version(self, case_number, command, info):
        self.expect_tddi_version.update({case_number: [command, info]})

    def set_expect_tcon_version(self, case_number, command, info):
        self.expect_tcon_version.update({case_number: [command, info]})

    def set_expect_boot_version(self, case_number, command, info):
        self.expect_boot_version.update({case_number: [command, info]})

    def set_expect_tp_version(self, case_number, command, info):
        self.expect_tp_version.update({case_number: [command, info]})

    def set_expect_assembly_version(self, case_number, command, info):
        self.expect_assembly_version.update({case_number: [command, info]})

    def set_expect_hwsn(self, case_number, command, info):
        self.expect_hwsn.update({case_number: [command, info]})

    def set_expect_psn(self, case_number, command, info):
        self.expect_psn.update({case_number: [command, info]})

    def set_expect_screen_temp(self, case_number, command, info):
        self.expect_screen_temp.update({case_number: [command, info]})

    def set_expect_pcb_temp(self, case_number, command, info):
        self.expect_pcb_temp.update({case_number: [command, info]})

    def set_expect_brightness(self, case_number, command, info):
        self.expect_brightness.update({case_number: [command, info]})

    def set_expect_work_voltage(self, case_number, command, min_voltage, max_voltage):
        self.expect_work_voltage.update({case_number: [command, min_voltage, max_voltage]})

    def set_expect_test_tpcm(self, case_number, command):
        self.expect_test_tpcm.update({case_number: [command]})

    def set_expect_serial_protocol_data(self, case_number, command, info):
        logger.info(f"set_expect_serial_protocol_data case_number={case_number}, command={command}, info={info}")
        self.expect_serial_protocol_data.update({case_number: [command, info]})

    def detect_action(self, action="", info=""):
        if not operator.eq("HeartBreak", action):
            logger.info(f"detect_action action={action}, info={info}")

        if action == "readSoftwareVersion":
            if len(self.expect_software_version) == 0:
                return
            case_number = list(self.expect_software_version.keys())[0]
            command = list(self.expect_software_version.values())[0][0]
            expect_info = list(self.expect_software_version.values())[0][1]
            logger.info("detect_action detect_info={}, expect_info={}".format(info, expect_info))
            if operator.eq(expect_info, info):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_software_version.clear()
        elif action == "readHardwareVersion":
            if len(self.expect_hardware_version) == 0:
                return
            case_number = list(self.expect_hardware_version.keys())[0]
            command = list(self.expect_hardware_version.values())[0][0]
            expect_info = list(self.expect_hardware_version.values())[0][1]
            logger.info("detect_action detect_info={}, expect_info={}".format(info, expect_info))
            if operator.eq(expect_info, info):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_hardware_version.clear()
        elif action == "readInnerSoftwareVersion":
            if len(self.expect_inner_software_version) == 0:
                return
            case_number = list(self.expect_inner_software_version.keys())[0]
            command = list(self.expect_inner_software_version.values())[0][0]
            expect_info = list(self.expect_inner_software_version.values())[0][1]
            logger.info("detect_action detect_info={}, expect_info={}".format(info, expect_info))
            if operator.eq(expect_info, info):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_inner_software_version.clear()
        elif action == "readInnerHardwareVersion":
            if len(self.expect_inner_hardware_version) == 0:
                return
            case_number = list(self.expect_inner_hardware_version.keys())[0]
            command = list(self.expect_inner_hardware_version.values())[0][0]
            expect_info = list(self.expect_inner_hardware_version.values())[0][1]
            logger.info("detect_action detect_info={}, expect_info={}".format(info, expect_info))
            if operator.eq(expect_info, info):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_inner_hardware_version.clear()
        elif action == "readPartNumber":
            if len(self.expect_part_number) == 0:
                return
            case_number = list(self.expect_part_number.keys())[0]
            command = list(self.expect_part_number.values())[0][0]
            expect_info = list(self.expect_part_number.values())[0][1]
            logger.info("detect_action detect_info={}, expect_info={}".format(info, expect_info))
            if operator.eq(expect_info, info):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_part_number.clear()
        elif action == "readLDVersion":
            if len(self.expect_ld_version) == 0:
                return
            case_number = list(self.expect_ld_version.keys())[0]
            command = list(self.expect_ld_version.values())[0][0]
            expect_info = list(self.expect_ld_version.values())[0][1]
            logger.info("detect_action detect_info={}, expect_info={}".format(info, expect_info))
            if operator.eq(expect_info, info):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_ld_version.clear()
        elif action == "readTDDIVersion":
            if len(self.expect_tddi_version) == 0:
                return
            case_number = list(self.expect_tddi_version.keys())[0]
            command = list(self.expect_tddi_version.values())[0][0]
            expect_info = list(self.expect_tddi_version.values())[0][1]
            logger.info("detect_action detect_info={}, expect_info={}".format(info, expect_info))
            if operator.eq(expect_info, info):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_tddi_version.clear()
        elif action == "readTCONVersion":
            if len(self.expect_tcon_version) == 0:
                return
            case_number = list(self.expect_tcon_version.keys())[0]
            command = list(self.expect_tcon_version.values())[0][0]
            expect_info = list(self.expect_tcon_version.values())[0][1]
            logger.info("detect_action detect_info={}, expect_info={}".format(info, expect_info))
            if operator.eq(expect_info, info):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_tcon_version.clear()
        elif action == "readBootVersion":
            if len(self.expect_boot_version) == 0:
                return
            case_number = list(self.expect_boot_version.keys())[0]
            command = list(self.expect_boot_version.values())[0][0]
            expect_info = list(self.expect_boot_version.values())[0][1]
            logger.info("detect_action detect_info={}, expect_info={}".format(info, expect_info))
            if operator.eq(expect_info, info):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_boot_version.clear()
        elif action == "readTpVersion":
            if len(self.expect_tp_version) == 0:
                return
            case_number = list(self.expect_tp_version.keys())[0]
            command = list(self.expect_tp_version.values())[0][0]
            expect_info = list(self.expect_tp_version.values())[0][1]
            logger.info("detect_action detect_info={}, expect_info={}".format(info, expect_info))
            if operator.eq(expect_info, info):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_tp_version.clear()
        elif action == "readAssemblyVersion":
            if len(self.expect_assembly_version) == 0:
                return
            case_number = list(self.expect_assembly_version.keys())[0]
            command = list(self.expect_assembly_version.values())[0][0]
            expect_info = list(self.expect_assembly_version.values())[0][1]
            logger.info("detect_action detect_info={}, expect_info={}".format(info, expect_info))
            if operator.eq(expect_info, info):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_assembly_version.clear()
        elif action == "readHWSN":
            if len(self.expect_hwsn) == 0:
                return
            case_number = list(self.expect_hwsn.keys())[0]
            command = list(self.expect_hwsn.values())[0][0]
            expect_info = list(self.expect_hwsn.values())[0][1]
            logger.info("detect_action detect_info={}, expect_info={}".format(info, expect_info))
            if operator.eq(expect_info, info):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_hwsn.clear()
        elif action == "readPSN":
            if len(self.expect_psn) == 0:
                return
            case_number = list(self.expect_psn.keys())[0]
            command = list(self.expect_psn.values())[0][0]
            expect_info = list(self.expect_psn.values())[0][1]
            logger.info("detect_action detect_info={}, expect_info={}".format(info, expect_info))
            if operator.eq(expect_info, info):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_psn.clear()
        elif action == "readScreenTemp":
            self.resp_screen_temp = info
            if len(self.expect_screen_temp) == 0:
                logger.info(f"expect_screen_temp is empty vds_detect_manager.resp_screen_temp is:{info}")
                vds_detect_manager.resp_screen_temp = info
                return
            case_number = list(self.expect_screen_temp.keys())[0]
            command = list(self.expect_screen_temp.values())[0][0]
            expect_info = list(self.expect_screen_temp.values())[0][1]
            logger.info("detect_action detect_info={}, expect_info={}".format(info, expect_info))
            if operator.eq(expect_info, info):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_screen_temp.clear()
        elif action == "readPCBTemp":
            if len(self.expect_pcb_temp) == 0:
                return
            case_number = list(self.expect_pcb_temp.keys())[0]
            command = list(self.expect_pcb_temp.values())[0][0]
            expect_info = list(self.expect_pcb_temp.values())[0][1]
            logger.info("detect_action detect_info={}, expect_info={}".format(info, expect_info))
            if operator.eq(expect_info, info):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_pcb_temp.clear()
        elif action == "readBrightness":
            if len(self.expect_brightness) == 0:
                return
            case_number = list(self.expect_brightness.keys())[0]
            command = list(self.expect_brightness.values())[0][0]
            expect_info = list(self.expect_brightness.values())[0][1]
            logger.info("detect_action detect_info={}, expect_info={}".format(info, expect_info))
            if operator.eq(expect_info, info):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_brightness.clear()
        elif action == "readWorkVoltage":
            if len(self.expect_work_voltage) == 0:
                return
            case_number = list(self.expect_work_voltage.keys())[0]
            command = list(self.expect_work_voltage.values())[0][0]
            expect_min = list(self.expect_work_voltage.values())[0][1]
            expect_max = list(self.expect_work_voltage.values())[0][2]
            logger.info(f"detect_action detect_info={info}, expect_min={expect_min}, expect_max={expect_max}")
            if float(expect_min) <= float(info) <= float(expect_max):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_work_voltage.clear()
        elif action == "testTPCM":
            if len(self.expect_test_tpcm) == 0:
                return
            case_number = list(self.expect_test_tpcm.keys())[0]
            command = list(self.expect_test_tpcm.values())[0][0]
            if info.__contains__("PASS"):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_test_tpcm.clear()
        elif action == "testTouchStill":
            if info.__contains__(":"):
                test_mode = info.split(":")[0].strip()
                coordinate = info.split(":")[1].strip()
                if operator.eq("1", test_mode):
                    # 静止模式
                    touch_manager.append_error_coordinates(f"[{coordinate}]")
        elif action == "readColorTemp":
            self.resp_color_temp = float(info)
        elif action == "readLightSensor":
            self.resp_light_sensor = float(info) / 1000
        elif action == "forwardLightSensor":
            if info.__contains__(" "):
                info_list = info.split(" ")
                self.resp_light_sensor = float(info_list[0])
                self.resp_light_r = float(info_list[1])
                self.resp_light_g = float(info_list[2])
                self.resp_light_b = float(info_list[3])
        elif action == "testTouchRespCountEnd":
            case_number = list(self.expect_touch_resp_times.keys())[0]
            data = list(self.expect_touch_resp_times.values())[0][-1]
            command = list(self.expect_touch_resp_times.values())[0][0]
            calibrate_resp_times = int(data.split(",")[1])
            count = int(info.split(":")[-1])
            result = calibrate_resp_times == count
            if result:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG",
                                                         "expected count is %d\n actual count is %d" % (
                                                             calibrate_resp_times, count))
        elif action == "testTouchRespTime":
            case_number = list(self.expect_test_touch_resp_time.keys())[0]
            T = list(self.expect_test_touch_resp_time.values())[0][1]
            time__ = list(self.expect_test_touch_resp_time.values())[0][-1]
            command = list(self.expect_test_touch_resp_time.values())[0][0]
            t = int(time.time() * 1000)
            result = t - T <= time__
            self.expect_test_touch_resp_time.update({case_number: [command, T, time__]})
            if result:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", f"{t - T}")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG",
                                                         "expected time is %d\n actual time is %d" % (time__, t - T))
        elif action == "readBackLightStatus":
            if len(self.expect_back_light_status) == 0:
                return
            case_number = list(self.expect_back_light_status.keys())[0]
            command = list(self.expect_back_light_status.values())[0][0]
            expect_info = list(self.expect_back_light_status.values())[0][1]
            logger.info(f"detect_action action={action}, detect_info={info}, expect_info={expect_info}")
            if operator.eq(expect_info, info):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_back_light_status.clear()
        elif action == "readBistPatternStatus":
            if len(self.expect_bist_pattern_status) == 0:
                return
            case_number = list(self.expect_bist_pattern_status.keys())[0]
            command = list(self.expect_bist_pattern_status.values())[0][0]
            expect_info = list(self.expect_bist_pattern_status.values())[0][1]
            logger.info(f"detect_action action={action}, detect_info={info}, expect_info={expect_info}")
            if operator.eq(expect_info, info):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_bist_pattern_status.clear()
        elif action == "end_draw_lines":
            if int(info) == 1:
                # pass
                logger.info(f"end_draw_lines result NG:{info} NG" )
                touch_card.finger_move_line_result = False


        elif action == "end_touch_points":
            info = int(info)
            case_number = touch_card.case_number
            command = touch_card.command
            if info == self.expect_end_touch_points_count:
                signals_manager.step_execute_finish.emit(case_number, command, "PASS", "PASS")
            else:
                signals_manager.step_execute_finish.emit(case_number, command, "NG", f"get points count is {info}")
        elif action == "serialProtocolTest":
            if len(self.expect_serial_protocol_data) == 0:
                return
            case_number = list(self.expect_serial_protocol_data.keys())[0]
            command = list(self.expect_serial_protocol_data.values())[0][0]
            expect_info = list(self.expect_serial_protocol_data.values())[0][1]
            logger.info(f"detect_action action={action}, detect_info={info}, expect_info={expect_info}")
            if info.strip().__contains__(expect_info.strip()):
                result = "PASS"
            else:
                result = "NG"
            signals_manager.step_execute_finish.emit(case_number, command, result, info)
            self.expect_serial_protocol_data.clear()
        elif action == "showCheckerboard":
            result = "pass"
            signals_manager.step_execute_finish.emit(self.case_number, self.command, result, info)


vds_detect_manager: VdsDetectManager = VdsDetectManager()
