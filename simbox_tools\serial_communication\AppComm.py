import struct

import time
from queue import Queue, Empty, Full
from threading import Lock, Thread

from common.LogUtils import logger
from simbox_tools.Logger import Logger
from simbox_tools.AppExcept import AppExcept

from simbox_tools.BytesTool import BytesTool
from simbox_tools.CommunicationManager import communication_manager
from simbox_tools.ConfigManger import NomiConfig
from simbox_tools.UsbCdc import UsbCdc
from simbox_tools.serial_communication.Transmit import Transmit


EN_READ_REQ = 0x01
EN_WRITE_REQ = 0x02
EN_READ_RESP = 0x81
EN_WRITE_RESP = 0x82
EN_ERR_RESP = 0xC0

SIMBOX_TIMEOUT = 3


# 创建应用通信的类
class AppComm(object):
    _log_tag = "AppComm"
    __usb = None  # 定义USB端口的对象
    __trans = None  # 定义传输层的对象
    __can_port = None  # 定义CAN传输层的端口对象
    __can2_port = None  # 定义CAN传输层的端口对象
    __diag1_port = None  # 定义CAN传输层的端口对象
    __diag2_port = None  # 定义CAN传输层的端口对象
    __nomi_data = None  # 定义指向Nomi的通讯数据的对象
    __sim_port = None  # 定义SimBox传输层的端口对象
    __sim_data = None  # 定义指向SimBox的通讯数据的对象
    __cur_port = None
    __mutex = None
    __msg_list = None
    __connect_flag = False  # 连接状态标志
    __comm_flag = False  # usb写入标志
    __comm_queue = None
    simbox_recv_time = 0

    def __init__(self, nomi_data=None, sim_data=None):
        self.__usb = UsbCdc()
        self.__trans = Transmit(self.comm_send)  # 对传输层对象的实例初始化
        self.__can_port = None
        self.__nomi_data = nomi_data
        self.__sim_port = None
        self.__sim_data = sim_data
        self.__mutex = Lock()
        self.__msg_list = None

    # 得到Port的列表
    def get_port_list(self):
        self.__usb.update_list()
        port_list = []
        for port in self.__usb.get_list():
            port_name = list(port)
            port_list.append(port_name[0])
        Logger.console(self._log_tag, "get_port_list -> %s" % port_list)
        return port_list

    def get_default_port(self, default_name):
        self.__usb.update_list()
        for port in self.__usb.get_list():
            port_name = list(port)
            if port_name[1].find(default_name) >= 0:
                return port_name[0]

    # 打开应用程序的连接，开始传输数据, port为端口名，例如"COM1"
    def connect(self, port=None, diag_recv=None, diag2_recv=None):
        if port is None:
            Logger.console(self._log_tag, "Please select a device")
            return False

        try:
            if self.__usb.is_open():
                self.__usb.close()

            self.__usb.open(port, receiver=self.__trans.get_recvbytes())
            self.__cur_port = port
            if self.__usb.is_open():
                Logger.console(self._log_tag, "Connect to %s" % self.__cur_port)
                self.__sim_port = self.__trans.register(1, self.simbox_recv)  # simbox消息通道
                self.__can_port = self.__trans.register(100, self.can_recv)  # can1消息通道,标准的以8个字节作为data
                self.__can2_port = self.__trans.register(101, self.can2_recv)  # can2消息通道,标准的以8个字节作为data
                if diag_recv is None:
                    self.__diag1_port = self.__trans.register(102, self.recv_dummy)
                else:
                    self.__diag1_port = self.__trans.register(102, diag_recv)  # diag_port消息通道,以任意个字节作为data,内置分包发送机制
                if diag2_recv is None:
                    self.__diag2_port = self.__trans.register(103, self.recv_dummy)
                else:
                    self.__diag2_port = self.__trans.register(103, diag2_recv)  # diag2_port消息通道,以任意个字节作为data,内置分包发送机制
                self.__connect_flag = True
                thread = Thread(target=self.run, args=())
                thread.start()
                self.__comm_flag = True
                self.__comm_queue = Queue()

                thread = Thread(target=self.comm_thread, args=())
                thread.start()

                thread = Thread(target=self.host_computer_thread, args=())
                thread.start()

                return True
            else:
                return False
        except Exception as e:
            logger.error("connect exception: %s", str(e.args))
            return False

    def reconnect(self):
        self.__usb.close()
        self.connect(self.__cur_port)

    # 断开应用程序的通信连接
    def disconnect(self):
        Logger.console(self._log_tag, "disconnect")
        self.__trans.close()
        self.__comm_flag = False
        self.__connect_flag = False
        self.__usb.close()

    def run(self):
        while self.__connect_flag:
            if (self.__msg_list is not None) and (len(self.__msg_list) > 0):
                remove_list = []
                for msg in self.__msg_list:
                    if msg.is_cycle():
                        msg.run()
                        if msg.check():
                            self.can_send(msg.get_id(), msg.get_data(), msg.get_ch(), msg.get_console())
                    else:
                        self.can_send(msg.get_id(), msg.get_data(), msg.get_ch(), msg.get_console())
                        remove_list.append(msg)
                    time.sleep(0.01)
                for msg in remove_list:
                    self.delete(msg)
                time.sleep(0.1)
            else:
                time.sleep(0.1)
        pass

    def append(self, msg):
        self.__mutex.acquire()
        if self.__msg_list is None:
            self.__msg_list = []
            self.__msg_list.append(msg)
        else:
            self.__msg_list.append(msg)
        self.__mutex.release()
        pass

    def delete(self, msg):
        self.__mutex.acquire()
        self.__msg_list.remove(msg)
        self.__mutex.release()
        pass

    def simbox_recv(self, data):
        # Logger.console(self._log_tag, "simbox_recv -> %s" % data)
        if data is None:
            return Logger.console(self._log_tag, "simbox_recv -> data is None")

        elif len(data) < 5:
            return Logger.console(self._log_tag, "simbox_recv -> len(data) < 5")

        if (self.__sim_data is None) or (self.__sim_data.set_data is None):
            raise AppExcept("Simbox data数据对象异常")

        bt = BytesTool("simbox_recv")
        bt.append_byte_list(data)
        operate = bt.get_int(1)
        id = bt.get_int(4)
        if EN_ERR_RESP == (operate & 0xF0):
            raise AppExcept("Receive error response")
        elif EN_READ_RESP == operate:
            value = bt.get_byte_list(len(data) - 5)
            self.__sim_data.set_data(id, value)

            if id == self.__sim_data.play_state.get_id():
                play_state = self.__sim_data.play_state.get_value()
                communication_manager.set_animation_state(play_state)
            elif id == self.__sim_data.play_frame.get_id():
                play_frame = self.__sim_data.play_frame.get_value()
                communication_manager.set_animation_frame(play_frame + 1)
            elif id == self.__sim_data.simbox_rotate.get_id():
                rotate_ack = self.__sim_data.simbox_rotate.get_value()
                communication_manager.simbox_rotate_signal.emit(rotate_ack)
        elif EN_WRITE_RESP == operate:
            self.__sim_data.set_data(id, None)

        if id == self.__sim_data.status.get_id():
            self.simbox_recv_time = int(time.time())

    def can_recv(self, data):
        # Logger.console(self._log_tag, "can_recv -> %s" % data)
        msg_id = data[0] << 8 | data[1]
        msg_len = data[2]
        msg_data = data[3:]

        if msg_id == 0x14:
            # Logger.console(self._log_tag, "can_recv 0x14 -> %s" % data)
            frame_id = msg_data[1]
            frame_ack = msg_data[2]
            if frame_id == NomiConfig.KineticYawId:
                communication_manager.yaw_rotate_state = frame_ack
            elif frame_id == NomiConfig.KineticPitchId:
                communication_manager.pitch_rotate_state = frame_ack
        elif msg_id == 0x15:
            if msg_len == 2:
                yaw_real_position = msg_data[0]
                pitch_real_position = msg_data[1]
                communication_manager.set_yaw_real_position(yaw_real_position - 55)
                communication_manager.set_pitch_real_position(pitch_real_position)

    def can2_recv(self, data):
        if len(data) == 11:
            msg_id = data[0] << 8 | data[1]
            if msg_id == 0x2E3:
                if communication_manager.get_CAN_connect_state() != NomiConfig.CANConnected:
                    communication_manager.set_CAN_connect_state(NomiConfig.CANConnected)
                    communication_manager.can_state_signal.emit(NomiConfig.CANConnected)

                response_brightness_percent = data[4]
                response_calibrate_error_state = data[5] & 0x80
                response_calibrate_state = data[6]

                if response_calibrate_error_state == NomiConfig.CalibrateError:
                    communication_manager.calibrate_error_state_signal.emit()

                if response_calibrate_state != communication_manager.get_response_calibrate_state():
                    communication_manager.set_response_calibrate_state(response_calibrate_state)
                    if response_calibrate_state == NomiConfig.CalibrateActivated:
                        communication_manager.calibrate_state_signal.emit()

                communication_manager.set_response_brightness_percent(response_brightness_percent)

    def recv_dummy(self, data):
        Logger.console(self._log_tag, "recv_dummy -> %s" % data)

    def comm_send(self, data):
        ret = True
        try:
            self.__comm_queue.put(data, block=True, timeout=1)
        except Full as e:
            Logger.console(self._log_tag, "comm_send -> self.__comm_queue is full! \n%s" % e.args)
            ret = False
        return ret

    def comm_thread(self):
        while self.__comm_flag:
            try:
                data = self.__comm_queue.get(block=True, timeout=1)
                self.__usb.write(data)
            except Empty:
                Logger.console(self._log_tag, "comm_thread -> self.__comm_queue is empty")

    def host_computer_thread(self):
        while self.__connect_flag:
            self.append(CanMessage(id=0x505, data=[0x05, 0x00, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA], ch=2))
            self.simbox_obj(EN_READ_REQ, self.__sim_data.status)
            request_brightness_percent = communication_manager.get_request_brightness_percent()
            request_calibrate_state = communication_manager.get_request_calibrate_state()
            self.append(CanMessage(id=0x2C3, data=[0x14, 0x00], ch=2))
            if request_calibrate_state == NomiConfig.CalibrateNotActive:
                data = [request_brightness_percent, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]
                self.append(CanMessage(id=0x2E2, data=data, ch=2))
            elif request_calibrate_state == NomiConfig.CalibrateActive:
                data = [request_brightness_percent, 0x51, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]
                self.append(CanMessage(id=0x2E2, data=data, ch=2))

            time.sleep(1)

    def can_send(self, can_id, data, ch=0, console=False):
        if console:
            Logger.console(self._log_tag, "can_send -> can_id :%s, data :%s, ch :%s" % (can_id, data, ch))

        if len(data) > 8:
            raise AppExcept("The size of CAN message can not greater than 8!")
        buffer = struct.pack("BBB", (can_id & 0xff00) >> 8, can_id & 0xff, len(data))

        for index in data:
            if index > 255:
                raise AppExcept("Can data error!")
            buffer = struct.pack("%dsB" % len(buffer), buffer, index)

        if (0 == ch) or (1 == ch):
            if self.__can_port is None:
                raise AppExcept("Please connect to a device1")
            self.__can_port.write(buffer)
        elif 2 == ch:
            if self.__can2_port is None:
                raise AppExcept("Please connect to a device2")
            self.__can2_port.write(buffer)

    def simbox_obj(self, operate, data):
        if data is None:
            return

        if self.__sim_port is None:
            return

        data.set_flag(False)
        if EN_READ_REQ == operate:
            self.simbox_send(operate, data.get_id(), None)
        elif EN_WRITE_REQ == operate:
            self.simbox_send(operate, data.get_id(), data.get_data())

    # SimBox应用层的数据发送函数
    def simbox_send(self, operate, data_id, data):
        # Logger.console(self._log_tag, "simbox_send -> %s, %s, %s" % (operate, data_id, data))
        temp = struct.pack("I", data_id)
        buffer = struct.pack("B4s", operate, temp)

        if data is not None:
            for index in data:
                buffer = struct.pack("%dsB" % len(buffer), buffer, index)

        if self.__sim_port is not None:
            self.__sim_port.write(buffer)

    def diag1_send(self, msg):
        if (msg is not None) and len(msg) > 0:
            bt = BytesTool("diag1_send")
            bt.append_byte_list(msg)
            bt.print()
            if self.__diag1_port is not None:
                self.__diag1_port.write(bt.get())

    def diag2_send(self, msg):
        if (msg is not None) and (len(msg) > 0):
            bt = BytesTool("%s diag2_send" % self._log_tag)
            bt.append_byte_list(msg)
            bt.print()
            if self.__diag2_port is not None:
                self.__diag2_port.write(bt.get())


class BaseCanMessage(object):
    __id = 0
    __data = None

    def __init__(self, id=0, data=None, bytes=None):
        if bytes is None:
            self.__id = id
            self.__data = data
        else:
            if len(bytes) >= 4:
                self.__id = bytes[0] * 256 + bytes[1]
                # print("byte[0] = %d, byte[1] = %d, %d" % (bytes[0], bytes[1], self.__id))
                size = bytes[2]
                self.__data = []
                for index in range(size):
                    self.__data.append(bytes[index + 3])

    def get_id(self):
        return self.__id

    def get_data(self):
        return self.__data

    def set_data(self, index, value):
        size = len(self.__data)
        if index < size:
            self.__data[index] = value

    def print(self, func=None):
        if func is not None:
            func("ID = 0x%x, data = ")
            for index in self.__data:
                func(" 0x%x" % index)
        else:
            print("ID = 0x%x, data = " % self.__id, end="")
            for index in self.__data:
                print(" 0x%x" % index, end="")
            print("\n", end="")


class CanMessage(object):
    __type = 0
    __id = 0
    __data = None
    __cycle = 0
    __current_cnt = 0
    __channel = 0
    __console = False

    def __init__(self, id, data, type=0, cycle=0, ch=0, console=False):
        self.__id = id
        self.__data = data
        self.__type = type
        self.__cycle = cycle
        self.__channel = ch
        self.__console = console

    def print(self):
        if self.__type == 1:
            print("\nid = 0x%02x, data = %s, %s=%d" % (self.__id, self.__data, "Cycle Type", self.__cycle))
        else:
            print("\nid = 0x%02x, data = %s, %s" % (self.__id, self.__data, "Single Type",))

    def is_cycle(self):
        if self.__type == 1:
            return True
        return False

    def get_ch(self):
        return self.__channel

    def get_console(self):
        return self.__console

    def get_type(self):
        return self.__type

    def get_id(self):
        return self.__id

    def get_data(self, index=None):
        if index is not None:
            if index >= len(self.__data):
                raise AppExcept("out of list range")
            return self.__data[index]
        return self.__data

    def set_data(self, data, index=None):
        if index is not None:
            if index >= len(self.__data):
                raise AppExcept("out of list range")
            self.__data[index] = data
        else:
            self.__data = data

    def run(self, cnt=100):
        self.__current_cnt += cnt

    def check(self):
        if self.__current_cnt >= self.__cycle:
            self.__current_cnt -= self.__cycle
            return True
        return False

    def get_bytes(self):
        if len(self.__data) > 8:
            raise AppExcept("The size of can message can not greater than 8!")
        buffer = struct.pack("BBB", (self.__id & 0xff00) >> 8, self.__id & 0xff, len(self.__data))
        for index in self.__data:
            buffer = struct.pack("%dsB" % len(buffer), buffer, index)

        print("\r\nCAN Send[id = 0x%03x] : " % self.__id, end="")
        for i in range(len(self.__data)):
            print(" 0x%02x" % self.__data[i], end="")
        print("\n", end="")
        return buffer

if __name__ == '__main__':
    app = AppComm()
    # self.__comm.connect(self._port, self.__diag1_server.msg_protocol, self.__diag2_server.msg_protocol)
    port = 0

    app.connect()
