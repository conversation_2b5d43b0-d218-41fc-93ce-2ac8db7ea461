<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="0">
     <widget class="QFrame" name="frame">
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <layout class="QGridLayout" name="gridLayout_2">
       <item row="0" column="0">
        <widget class="QPushButton" name="pushButton">
         <property name="text">
          <string>开始测试</string>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="QPushButton" name="pushButton_2">
         <property name="text">
          <string>导出报告</string>
         </property>
        </widget>
       </item>
       <item row="1" column="0" colspan="2">
        <widget class="QTableWidget" name="tableWidget">
         <row>
          <property name="text">
           <string>白画面</string>
          </property>
         </row>
         <row>
          <property name="text">
           <string>黑画面</string>
          </property>
         </row>
         <row>
          <property name="text">
           <string>红画面</string>
          </property>
         </row>
         <row>
          <property name="text">
           <string>绿画面</string>
          </property>
         </row>
         <row>
          <property name="text">
           <string>蓝画面</string>
          </property>
         </row>
         <column>
          <property name="text">
           <string>X坐标</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>Y坐标</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>亮度</string>
          </property>
         </column>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
