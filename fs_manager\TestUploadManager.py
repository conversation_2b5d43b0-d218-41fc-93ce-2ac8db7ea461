from abc import abstractmethod
from queue import Queue
from threading import Timer

from common.LogUtils import logger
from fs_manager.TestUploadMessage import TestUploadMessage


class Receiver:

    def __init__(self):
        super(Receiver, self).__init__()
        self._msg_ids = []

    @property
    def msg_ids(self):
        return self._msg_ids

    def register_msg_id(self, msg_id: str):
        self._msg_ids.append(msg_id)

    @abstractmethod
    def receive_message(self, msg: TestUploadMessage):
        pass


class TestUploadManager:
    DISPATCH_MESSAGE_INTERVAL = 0.02

    def __init__(self):
        super(TestUploadManager, self).__init__()
        self._receivers = []
        self._timer = Timer(0, self.dispatch_message)
        self._message_queue = Queue()

    def start_message_center(self):
        self._timer.start()

    def register_receiver(self, receiver: Receiver):
        self._receivers.append(receiver)

    def unregister_receiver(self, receiver: Receiver):
        if receiver in self._receivers:
            self._receivers.remove(receiver)

    def send_message(self, message: TestUploadMessage):
        self._message_queue.put(message)

    def dispatch_message(self):
        logger.debug("dispatch_message")
        self._timer = Timer(interval=2, function=self.dispatch_message)
        self._timer.start()
        if self._message_queue.empty():
            return
        message: TestUploadMessage = self._message_queue.get()
        if message is None:
            return

        for receiver in self._receivers:
            if message.msg_id in receiver.msg_ids:
                receiver.receive_message(message)


test_upload_manager: TestUploadManager = TestUploadManager()
