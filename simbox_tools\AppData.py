import struct
import time
from simbox_tools.AppExcept import AppExcept

from simbox_tools.BytesTool import BytesTool



class UnitOjb(object):
    __id = 0
    __data = []
    __size = 0
    __flag = False
    __access_flag = False

    def __init__(self, id, data=None, size=4):
        self.__id = id
        self.__data = []
        for index in range(size):
            self.__data.append(0)
        if data is not None:
            for index in range(size):
                self.__data[index] = data[index]
        self.__size = size
        self.__flag = False

    def get_id(self):
        return self.__id

    def get_size(self):
        return self.__size

    def set_data(self, data, index=None):
        if index is None:
            length = len(data)
            if length < self.__size:
                for index in range(length):
                    self.__data[index] = data[index]
                for index in range(length, self.__size, 1):
                    self.__data[index] = 0
            else:
                for index in range(0, self.__size, 1):
                    self.__data[index] = data[index]
        else:
            if index < self.__size:
                self.__data[index] = data

    def get_data(self, index=None):
        if index is None:
            return self.__data
        return self.__data[index]

    def set_flag(self, flag):
        self.__flag = flag
        if flag:
            self.__access_flag = False
        else:
            self.__access_flag = True

    def get_flag(self):
        return self.__flag

    def is_access(self):
        return self.__access_flag

    def check(self, timeout=0):
        if 0 == timeout:
            return self.__flag
        while True:
            if self.__flag is True:
                break
            time.sleep(0.001)
            timeout -= 1
            if timeout <= 0:
                raise AppExcept("ID = %d Receive timeout" % self.__id)
                break
        return self.__flag


class BytesObj(UnitOjb):
    def __init__(self, id, size):
        UnitOjb.__init__(self, id, None, size)

    def get_buffer_list(self, offset, size):
        list = []
        for index in range(self.get_size()):
            list.append(self.get_data(index))
        if (offset + size) <= self.get_size():
            return list[offset:(offset + size)]
        return None


class StringObj(BytesObj):

    def __int__(self, id, size):
        BytesObj.__init__(self, id, size)

    def get_string(self):
        char_list = []
        for d in self.get_data():
            char_list.append(chr(d))
        string = "".join(char_list)
        return string


class Int32Ojb(UnitOjb):

    def __init__(self, id, data):
        bytes = struct.pack("I", data)
        UnitOjb.__init__(self, id, bytes)

    def get_value(self):
        list = self.get_data()
        data = 0
        for index in range(4):
            data *= 256
            data += list[3 - index]
        return data

    def set_value(self, value):
        bytes = struct.pack("I", value)
        self.set_data(bytes)


class AppData(object):
    empty = Int32Ojb(0, 0)
    status = Int32Ojb(0, 0)
    command = Int32Ojb(1, 0)
    name = StringObj(2, 10)
    version = StringObj(3, 20)
    simbox_version = Int32Ojb(3, 0)
    upgrade_mode = Int32Ojb(10, 0)
    upgrade_status = Int32Ojb(11, 0)
    app_type = Int32Ojb(20, 0)
    code_addr = Int32Ojb(21, 0)
    code_buffer = BytesObj(22, 256)
    code_crc = Int32Ojb(23, 0)
    op_code = Int32Ojb(24, 0)
    diag_cmd = Int32Ojb(110, 0)
    diag_client_id = Int32Ojb(111, 0)
    diag_server_id = Int32Ojb(112, 0)
    replay_animation = Int32Ojb(200, 1)     # 开始播放指令
    play_animation = Int32Ojb(200, 2)       # 继续播放指令
    pause_animation = Int32Ojb(200, 3)      # 暂停指令
    play_state = Int32Ojb(200, 0)           # 播放状态
    play_frame = Int32Ojb(201, 0)           # 播放帧数
    simbox_rotate = Int32Ojb(202, 0)       # simbox旋转指令回执
    list = []
    list_len = 240

    def __init__(self):
        for i in range(self.list_len):
            self.list.append(self.empty)
        self.list[0] = self.status
        self.list[1] = self.command
        self.list[2] = self.name
        self.list[3] = self.version
        self.list[10] = self.upgrade_mode
        self.list[11] = self.upgrade_status
        self.list[20] = self.app_type
        self.list[21] = self.code_addr
        self.list[22] = self.code_buffer
        self.list[23] = self.code_crc
        self.list[24] = self.op_code
        self.list[110] = self.diag_cmd
        self.list[111] = self.diag_client_id
        self.list[112] = self.diag_server_id
        self.list[200] = self.play_state
        self.list[201] = self.play_frame
        self.list[202] = self.simbox_rotate

    def check_id(self, id):
        if id < len(self.list):
            return True
        return False

    def set_data(self, id, data=None):
        if id < len(self.list):
            self.list[id].set_flag(True)
            if data is not None:
                self.list[id].set_data(data)


if __name__ == '__main__':
    data = AppData()
    bt = BytesTool()
    bt.append_int(0x10)
    data.set_data(1, bt.get())
    print("%d" % data.command.get_value())
